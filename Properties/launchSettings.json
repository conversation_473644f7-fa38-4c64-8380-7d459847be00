{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:25838", "sslPort": 44338}}, "profiles": {"RaceSimulateAPI": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:7115;http://localhost:5115", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}
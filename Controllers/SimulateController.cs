using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using StandaloneSimulator;

namespace RaceSimulateAPI.Controllers;

[ApiController]
[Route("[controller]")]
public class SimulateController : ControllerBase
{
    /// <summary>
    /// メモリキャッシュ。使用しない場合はnull
    /// </summary>
    private readonly IMemoryCache? _memoryCache;

    /// <summary>
    /// ログを保存するディレクトリ
    /// </summary>
    private readonly string _logDir;
    
    public SimulateController(IMemoryCache memoryCache, IOptions<RaceSimulateAPIOptions> optionsAccessor)
    {
        var raceSimulateApiOptions = optionsAccessor.Value;
        _memoryCache = raceSimulateApiOptions.UseMemoryCache ? memoryCache : null;
        _logDir = raceSimulateApiOptions.LogDir;
    }

    [HttpPost]
    public IActionResult Simulate(RaceSimulateRequest request)
    {
        // TODO: validate request

        var simulateResult = EntryPoint.Execute(request, memoryCache: _memoryCache, logDir: _logDir);

        return Ok(new RaceSimulateResponseData { simulate_result = simulateResult });
    }
}

public class RaceSimulateResponseData
{
    public RaceSimulateResponse? simulate_result { get; set; }
}
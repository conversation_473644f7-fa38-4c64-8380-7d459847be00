using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using RaceSimulateAPI.Logging;

namespace RaceSimulateAPI.Controllers;

public class ErrorController : ControllerBase
{
    /// <summary>
    /// ログを保存するディレクトリ
    /// </summary>
    private readonly string _logDir;

    public ErrorController(IOptions<RaceSimulateAPIOptions> optionsAccessor)
    {
        _logDir = optionsAccessor.Value.LogDir;
    }

    [Route("/error")]
    [Produces("application/x-msgpack")]
    public IActionResult HandleError()
    {
        var context = HttpContext.Features.Get<IExceptionHandlerFeature>();
        var errorLogInfo = new ErrorLogInfo(
            dateTime: DateTime.Now,
            errorMessage: context?.Error.Message ?? "no error context",
            errorDetail: context?.Error.ToString() ?? "no error context"
        );

        // エラーログ出力
        ErrorLogger.Write(_logDir, errorLogInfo, info => info.FormatTsv());

        // TODO: Problemを整形
        return Problem(
            detail: errorLogInfo.ErrorDetail,
            title: errorLogInfo.ErrorMessage
        );
    }
}
using MessagePack;
using Microsoft.AspNetCore.Mvc;

namespace RaceSimulateAPI.Controllers;

[ApiController]
[Route("[controller]")]
public class StatusController : ControllerBase
{
    [HttpGet]
    public IActionResult Get()
    {
        ThreadPool.GetAvailableThreads(out var availableWorkerThreadNum, out var availableCompletionPortThreadNum);
        ThreadPool.GetMinThreads(out var minWorkerThreadNum, out var minCompletionPortThreadNum);
        ThreadPool.GetMaxThreads(out var maxWorkerThreadNum, out var maxPortThreadNum);

        var result = new StatusResult(
            WorkingWorkerThreadNum: maxWorkerThreadNum - availableWorkerThreadNum,
            WorkingCompletionPortThreadNum: maxPortThreadNum - availableCompletionPortThreadNum,
            ThreadPoolThreadNum: ThreadPool.ThreadCount,
            AvailableWorkerThreadNum: availableWorkerThreadNum,
            AvailableCompletionPortThreadNum: availableCompletionPortThreadNum,
            MinWorkerThreadNum: minWorkerThreadNum,
            MinCompletionPortThreadNum: minCompletionPortThreadNum,
            MaxWorkerThreadNum: maxWorkerThreadNum,
            MaxCompletionPortThreadNum: maxPortThreadNum
        );

        return Ok(result);
    }
}

[MessagePackObject(true)]
public record StatusResult(
    int WorkingWorkerThreadNum,
    int WorkingCompletionPortThreadNum,
    int ThreadPoolThreadNum,
    int AvailableWorkerThreadNum,
    int AvailableCompletionPortThreadNum,
    int MinWorkerThreadNum,
    int MinCompletionPortThreadNum,
    int MaxWorkerThreadNum,
    int MaxCompletionPortThreadNum
);
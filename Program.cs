using MessagePack.AspNetCoreMvcFormatter;
using MessagePack.Resolvers;
using RaceSimulateAPI;
using RaceSimulateAPI.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers(options =>
{
    // 通信形式にMessagePackを追加
    options.OutputFormatters.Insert(0, new MessagePackOutputFormatter(ContractlessStandardResolver.Options));
    options.InputFormatters.Insert(0, new MessagePackInputFormatter(ContractlessStandardResolver.Options));
});

var options = builder.Configuration.GetSection(RaceSimulateAPIOptions.SectionName);
// オプションを登録, DataAnnotationsに従って設定値検証
builder.Services.AddOptions<RaceSimulateAPIOptions>()
    .Bind(options)
    .ValidateDataAnnotations()
    .ValidateOnStart();

builder.Services.Configure<RaceSimulateAPIOptions>(options);
// Unix domain socketで受ける
builder.WebHost.UseUnixSocket(options.Get<RaceSimulateAPIOptions>().UnixSocket);
// メモリキャッシュを追加
builder.Services.AddMemoryCache();

var app = builder.Build();

// Configure the HTTP request pipeline.
// グローバルの例外処理ハンドラを登録
app.UseExceptionHandler("/error");

app.MapControllers();

app.Run();
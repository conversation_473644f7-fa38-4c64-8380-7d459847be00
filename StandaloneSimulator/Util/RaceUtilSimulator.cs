using System;
using System.Collections;
using System.IO;
using System.IO.Compression;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース関連ユーティリティ：シミュレーター関連処理。
    /// </summary>
    //-------------------------------------------------------------------
    public static partial class RaceUtil
    {
    #region <CYG_DEBUG用>
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        /// <summary>
        /// RaceHorseData配列を馬番で昇順にソート。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil"/>
        public static void SortRaceHorseDataAsc(RaceHorseSimulateData[] raceHorseDataArray)
        {
#if CYG_DEBUG
            Debug.Assert(!raceHorseDataArray.Any(r => r.frame_order == 0), "frame_orderが0のキャラが存在します。");
#endif
            Array.Sort(raceHorseDataArray, (a, b) => a.frame_order - b.frame_order);
        }
#endif
        
        /// <summary>
        /// サーバーから渡された値をクライアントで取り扱う値に変換する
        /// </summary>
        /// <remarks> .NETからサーバーからのデバッグ馬身差がfloatで渡されるようになったのでそのために定義 </remarks>
        /// <param name="bashin"></param>
        /// <returns></returns>
        public static float BashinServerVal2ClientVal(float bashin)
        {
            return bashin / BASHIN_INT2FLOAT_ACCURACY;
        }
        
    #endregion
        
    #region <BitConverterユーティリティ>
        /// <summary>
        /// longからバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="val">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void Int64ToBytesAddOffset(long val, ref byte[] retBytes, ref int retOffset)
        {
            Array.Copy(BitConverter.GetBytes(val), 0, retBytes, retOffset, sizeof(long));
            retOffset += sizeof(long);
        }
        
        /// <summary>
        /// バイト配列からshort値取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToInt16AddOffset(byte[] bytes, ref short retVal, ref int retOffset)
        {
            retVal = BitConverter.ToInt16(bytes, retOffset);
            retOffset += sizeof(short);
        }
        public static short BytesToInt16AddOffset(byte[] bytes, ref int retOffset)
        {
            short retVal = BitConverter.ToInt16(bytes, retOffset);
            retOffset += sizeof(short);
            return retVal;
        }
        
        public static void BytesToUInt16AddOffset(byte[] bytes, ref ushort retVal, ref int retOffset)
        {
            retVal = BitConverter.ToUInt16(bytes, retOffset);
            retOffset += sizeof(ushort);
        }
        public static ushort BytesToUInt16AddOffset(byte[] bytes, ref int retOffset)
        {
            ushort retVal = BitConverter.ToUInt16(bytes, retOffset);
            retOffset += sizeof(ushort);
            return retVal;
        }

        /// <summary>
        /// バイト配列からint値取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToInt32AddOffset(byte[] bytes, ref int retVal, ref int retOffset)
        {
            retVal = BitConverter.ToInt32(bytes, retOffset);
            retOffset += sizeof(int);
        }
        public static int BytesToInt32AddOffset(byte[] bytes, ref int retOffset)
        {
            int retVal = BitConverter.ToInt32(bytes, retOffset);
            retOffset += sizeof(int);
            return retVal;
        }
        
        /// <summary>
        /// byte配列から64bit分読み出してint32bitにキャストして返却。
        /// </summary>
        public static int BytesToInt64AddOffsetCastInt32(byte[] bytes, ref int retOffset)
        {
            long rawVal = BitConverter.ToInt64(bytes, retOffset);
            if (rawVal < int.MinValue || rawVal > int.MaxValue)
            {
                throw new Exception($"BytesToInt64AddOffsetCastInt32でintの値域を超えた値を受け取りました。 rawVal={rawVal}");
            }
            int retVal = (int)rawVal;
            retOffset += sizeof(long);
            return retVal;
        }

        /// <summary>
        /// バイト配列からuint値取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToUInt32AddOffset(byte[] bytes, ref uint retVal, ref int retOffset)
        {
            retVal = BitConverter.ToUInt32(bytes, retOffset);
            retOffset += sizeof(uint);
        }

        /// <summary>
        /// バイト配列から64bit分読み出してint32bitにキャストして返却。オフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToInt64AddOffsetCastInt32(byte[] bytes, ref int retVal, ref int retOffset)
        {
            long rawVal = BitConverter.ToInt64(bytes, retOffset);
            if (rawVal < int.MinValue || rawVal > int.MaxValue)
            {
                throw new Exception($"BytesToInt64AddOffsetCastInt32でintの値域を超えた値を受け取りました。 rawVal={rawVal}");
            }
            retVal = (int)rawVal;
            retOffset += sizeof(long);
        }

        /// <summary>
        /// バイト配列から64bit分読み出してuint32bitにキャストして返却。オフセット加算。
        /// </summary>
        public static void BytesToUInt64AddOffsetCastInt32(byte[] bytes, ref uint retVal, ref int retOffset)
        {
            ulong rawVal = BitConverter.ToUInt64(bytes, retOffset);
            if (rawVal < uint.MinValue || rawVal > uint.MaxValue)
            {
                throw new Exception($"BytesToUInt64AddOffsetCastInt32でuintの値域を超えた値を受け取りました。 rawVal={rawVal}");
            }
            retVal = (uint)rawVal;
            retOffset += sizeof(ulong);
        }

        /// <summary>
        /// バイト配列からlong取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToInt64AddOffset(byte[] bytes, ref long retVal, ref int retOffset)
        {
            retVal = BitConverter.ToInt64(bytes, retOffset);
            retOffset += sizeof(long);
        }

        /// <summary>
        /// バイト配列からfloat値取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToFloatAddOffset(byte[] bytes, ref float retVal, ref int retOffset)
        {
            retVal = BitConverter.ToSingle(bytes, retOffset);
            retOffset += sizeof(float);
        }
        public static float BytesToFloatAddOffset(byte[] bytes, ref int retOffset)
        {
            float retVal = BitConverter.ToSingle(bytes, retOffset);
            retOffset += sizeof(float);
            return retVal;
        }

        /// <summary>
        /// バイト配列からbyte値取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToByteAddOffset(byte[] bytes, ref byte retVal, ref int retOffset)
        {
            retVal = bytes[ retOffset ];
            retOffset += sizeof(byte);
        }
        public static byte BytesToByteAddOffset(byte[] bytes, ref int retOffset)
        {
            byte retVal = bytes[ retOffset ];
            retOffset += sizeof(byte);
            return retVal;
        }
        public static void BytesToSByteAddOffset(byte[] bytes, ref sbyte retVal, ref int retOffset)
        {
            retVal = (sbyte)bytes[ retOffset ];
            retOffset += sizeof(sbyte);
        }
        public static sbyte BytesToSByteAddOffset(byte[] bytes, ref int retOffset)
        {
            sbyte retVal = (sbyte)bytes[ retOffset ];
            retOffset += sizeof(sbyte);
            return retVal;
        }

        /// <summary>
        /// バイト配列からint配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。※「配列サイズ(int)」の後に「配列中身バイト」が続くバイト配列。</param>
        /// <param name="retVals">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        public static void BytesToInt32ArrayAddOffset(byte[] bytes, ref int[] retVals, ref int retOffset)
        {
            // 配列のサイズを取得。
            int arraySize = 0;
            RaceUtil.BytesToInt32AddOffset(bytes, ref arraySize, ref retOffset);

            // 配列のサイズが0ならサイズ0の配列を返却して処理抜ける。
            if (arraySize <= 0)
            {
                retVals = new int[0];
            }

            retVals = new int[arraySize];
            for (int i = 0; i < arraySize; ++i)
            {
                RaceUtil.BytesToInt32AddOffset(bytes, ref retVals[i], ref retOffset);
            }
        }

        /// <summary>
        /// バイト配列からstring取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="bytes">値を読み出すバイト配列。※「文字列バイト長(int)」の後に「文字列バイト」が続くバイト配列。</param>
        /// <param name="retVal">読み出した値を返却。</param>
        /// <param name="retOffset">読み出しオフセット(in & out)。</param>
        /// <param name="encode">テキストの文字コード指定。</param>
        public static void BytesToStringAddOffset(byte[] bytes, ref string retVal, ref int retOffset, System.Text.Encoding encode)
        {
            // 文字列のバイト長を取得。
            int strByteLen = 0;
            RaceUtil.BytesToInt32AddOffset(bytes, ref strByteLen, ref retOffset);

            // バイト長が0なら空文字列返却して処理抜ける。
            if (strByteLen <= 0)
            {
                retVal = string.Empty;
                return;
            }

            // バイト配列を取り出し、文字列へエンコード。
            var strBytes = new byte[strByteLen];
            Array.Copy(bytes, retOffset, strBytes, 0, strByteLen);
            retOffset += strByteLen;
            retVal = encode.GetString(strBytes);
        }

        /// <summary>
        /// intからバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="val">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void Int32ToBytesAddOffset(int val, ref byte[] retBytes, ref int retOffset)
        {
            Array.Copy(BitConverter.GetBytes(val), 0, retBytes, retOffset, sizeof(int));
            retOffset += sizeof(int);
        }
        
        // Int64ToBytesAddOffsetはGALLOPへの開示のために上に記述

        /// <summary>
        /// uintからバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="val">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void UInt32ToBytesAddOffset(uint val, ref byte[] retBytes, ref int retOffset)
        {
            Array.Copy(BitConverter.GetBytes(val), 0, retBytes, retOffset, sizeof(uint));
            retOffset += sizeof(uint);
        }

        /// <summary>
        /// ushortからバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="val">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void UInt16ToBytesAddOffset(ushort val, ref byte[] retBytes, ref int retOffset)
        {
            Array.Copy(BitConverter.GetBytes(val), 0, retBytes, retOffset, sizeof(ushort));
            retOffset += sizeof(ushort);
        }

        /// <summary>
        /// floatからバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="val">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void FloatToBytesAddOffset(float val, ref byte[] retBytes, ref int retOffset)
        {
            Array.Copy(BitConverter.GetBytes(val), 0, retBytes, retOffset, sizeof(float));
            retOffset += sizeof(float);
        }

        /// <summary>
        /// int配列からバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="vals">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void Int32ArrayToBytesAddOffset(int[] vals, ref byte[] retBytes, ref int retOffset)
        {
            // 配列サイズ。
            Array.Copy(BitConverter.GetBytes(vals.Length), 0, retBytes, retOffset, sizeof(int));
            retOffset += sizeof(int);

            // 配列の中身。
            for (int i = 0, cnt = vals.Length; i < cnt; ++i)
            {
                Array.Copy(BitConverter.GetBytes(vals[i]), 0, retBytes, retOffset, sizeof(int));
                retOffset += sizeof(int);
            }
        }

        /// <summary>
        /// stringからバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="val">文字列。</param>
        /// <param name="retBytes">バイト配列を返却。※「文字列バイト長(int)」の後に「文字列バイト」が続くバイト配列。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        /// <param name="encode">テキストの文字コード指定。</param>
        public static void StringToBytesAddOffset(string val, ref byte[] retBytes, ref int retOffset, System.Text.Encoding encode)
        {
            var stringBytes = string.IsNullOrEmpty(val) ? new byte[0] : encode.GetBytes(val);

            // 文字列バイト長。
            Array.Copy(BitConverter.GetBytes(stringBytes.Length), 0, retBytes, retOffset, sizeof(int));
            retOffset += sizeof(int);

            // 文字列バイト。
            Array.Copy(stringBytes, 0, retBytes, retOffset, stringBytes.Length);
            retOffset += stringBytes.Length;
        }

        /// <summary>
        /// byteからバイト配列取得＆読み出しオフセット加算。
        /// </summary>
        /// <param name="val">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void ByteToBytesAddOffset( byte val, ref byte[] retBytes, ref int retOffset )
        {
            retBytes[retOffset] = val;
            retOffset += sizeof( byte );
        }

        /// <summary>
        /// バイト配列をバイト配列へコピー＆読み出しオフセット加算。
        /// </summary>
        /// <param name="valFrom">値。</param>
        /// <param name="retBytes">バイト配列を返却。</param>
        /// <param name="retOffset">バイト配列書き込みオフセット(in & out)。</param>
        public static void BytesToBytesAddOffset( byte[] valFrom, ref byte[] retBytes, ref int retOffset )
        {
            Array.Copy( valFrom, 0, retBytes, retOffset, valFrom.Length );
            retOffset += valFrom.Length;
        }
    #endregion // <BitConverterユーティリティ>
    }
}
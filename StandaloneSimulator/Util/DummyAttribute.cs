#if STANDALONE_SIMULATOR
[System.AttributeUsage(System.AttributeTargets.Class)]
public class AddComponentMenu : System.Attribute
{
    public AddComponentMenu(string name)
    {
    }
}

[System.AttributeUsage(System.AttributeTargets.Field)]
public class TooltipAttribute : System.Attribute
{
    public TooltipAttribute(string name)
    {
    }
}

[System.AttributeUsage(System.AttributeTargets.Field)]
public class SpaceAttribute : System.Attribute
{
    public SpaceAttribute(int value)
    {
    }
}

[System.AttributeUsage(System.AttributeTargets.Field)]
public class HeaderAttribute : System.Attribute
{
    public HeaderAttribute(string name)
    {
    }
}

public class QuaternionDummy
{
    public float x;
    public float y;
    public float z;
    public float w;
}
#endif

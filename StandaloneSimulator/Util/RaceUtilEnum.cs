#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    public static class RaceUtilEnum
    {
        /// <summary>
        /// Enumを配列で返す
        /// ** 型が不正か等の判定は入ってないので注意
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static T[] GetEnumArray<T>()
        {
            var temp = Enum.GetValues(typeof(T));
            var result = new T[temp.Length];
            for (int i = 0; i < temp.Length; i++)
            {
                result[i] = (T)temp.GetValue(i);
            }
            return result;
        }

        /// <summary>
        /// Enumの要素数を数える
        /// </summary>
        /// <typeparam name="T">要素数を調べたいEnumの型</typeparam>
        /// <returns>要素数</returns>
        public static int GetEnumElementCount<T>()
        {
            return System.Enum.GetNames(typeof(T)).Length;
        }

        /// <summary>
        /// Enum要素の同じ物を取り除いた状態で返す
        /// </summary>
        /// <param name="type">Enumの型</param>
        /// <param name="removeSameValue">同数の場合に項目を削除するか</param>
        /// <returns></returns>
        public static object[] GetEnumValues(System.Type type, bool removeSameValue = true)
        {
            var enums = System.Enum.GetValues(type);

            //同じ値を取り除く
            var newEnums = new System.Collections.Generic.List<object>();
            foreach (var item in enums)
            {
                if (newEnums.IndexOf(item) < 0 || !removeSameValue)
                {
                    newEnums.Add(item);
                }
            }
            return newEnums.ToArray();
        }
    }
}
#endif

using System;
using System.Collections.Generic;
using System.Linq;
using SkillDefine = Gallop.SkillDefine;
#if STANDALONE_SIMULATOR
using System.Text.Json;
#endif
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース関連ユーティリティ：AI関連計算式：GALLOP/.NET兼用
    /// </summary>
    //-------------------------------------------------------------------
    public static partial class RaceUtil
    {
        public const int FINISH_ORDER_NULL = -1;
        public const float FINISH_TIME_NULL = -1;
        public const float FINISH_DIFF_TIME_NULL = 0;
        public const int CUR_ORDER_DEFAULT = 0; // 出走前は全員1位扱い。
        
        #region <速度>
        public const float MAX_SPEED = 30.0f;
        public const float LANE_MOVE_SPEED_MAX = 0.6f;
        public const float LANE_MOVE_SPEED_MIN = 0.0f;
        private const float DECL_PERSEC_OVERRUN = 4.0f;
        
        /// <summary>
        /// 前方ブロックされているときの最大速度計算。
        /// </summary>
        /// <param name="blockHorse">この馬にブロックされている。</param>
#if GALLOP
        public static float CalcMaxSpeedBlock(Gallop.AroundHorse blockHorse)
        {
            var paramDefine = Gallop.RaceManager.Instance.ParamDefine.Block;

            // 前方ブロックしているキャラとどれくらい離れているか。0~1に正規化。
            float distanceGapRate = blockHorse.distanceGap / paramDefine.FrontBlockDistanceGap;
            distanceGapRate = UnityEngine.Mathf.Clamp01(distanceGapRate);

            // 前方のキャラに近いほど(distanceGapRateが小さいほど)最大速度を落とし、車間距離を取るようにする。
            float speedRate = UnityEngine.Mathf.Lerp(
                paramDefine.FrontBlockMaxSpeedRateMin, 
                paramDefine.FrontBlockMaxSpeedRateMax, 
                distanceGapRate);
            float retSpeed = blockHorse.info.GetLastSpeed() * speedRate;

            return retSpeed;
        }
#endif
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public static float CalcMaxSpeedBlockSimulate(Gallop.AroundHorse blockHorse, Gallop.RaceParamDefine.BlockParam paramDefine)
        {
            // 前方ブロックしているキャラとどれくらい離れているか。0~1に正規化。
            float distanceGapRate = blockHorse.distanceGap / paramDefine.FrontBlockDistanceGap;
            distanceGapRate = RaceUtilMath.Clamp(distanceGapRate, 0, 1);

            // 前方のキャラに近いほど(distanceGapRateが小さいほど)最大速度を落とし、車間距離を取るようにする。
            float speedRate = RaceUtilMath.Lerp(
                paramDefine.FrontBlockMaxSpeedRateMin, 
                paramDefine.FrontBlockMaxSpeedRateMax, 
                distanceGapRate);
            float retSpeed = blockHorse.infoSimulate.GetLastSpeed() * speedRate;

            return retSpeed;
        }
#endif
        
        /// <summary>
        /// 減速度計算。
        /// </summary>
        /// <returns>１秒当たりの速度減算値。</returns>
        public static float CalcDeclPerSec(Gallop.RaceDefine.HorsePhase phase, float hp, bool isFinished, bool isPaseDown, Gallop.RaceParamDefine paramDefine)
        {
            // ゴール後は固定の減速度使用。
            if (isFinished)
            {
                return DECL_PERSEC_OVERRUN;
            }

            float rate = 0;

            // レース展開によって減少レートを決める。
            if (phase == Gallop.RaceDefine.HorsePhase.Start)
            {
                rate = paramDefine.declRateStart;
            }
            else if (phase == Gallop.RaceDefine.HorsePhase.MiddleRun)
            {
                rate = paramDefine.declRateMiddle;
            }
            else if (phase >= Gallop.RaceDefine.HorsePhase.End)
            {
                rate = paramDefine.declRateEnd;
            }

            // #89715 ポジション維持によるペースダウンはPhaseの減速度よりも優先。
            if (isPaseDown)
            {
                rate = paramDefine.DeclRatePositionKeepPaseDown;
            }

            // ※これ以降はキャラの状態ごとで減少レートを分ける。より高い減少レートとなる場合だけ採用する。※
            
            // HP枯渇時の減少レート適用。
            if (hp <= 0.0f)
            {
                if (rate < paramDefine.declRateHpZero)
                {
                    rate = paramDefine.declRateHpZero;
                }
            }

            return paramDefine.declBase * rate;
        }

        #endregion <速度>
        
        #region <レーン移動>
        
        /// <summary>
        /// レーン距離からレーンタイプ取得。
        /// </summary>
        public static Gallop.RaceDefine.LaneType CalcLane(float laneDistance)
        {
            const float LANE_UTI_END = 0.2f;        // 内位置    0.0~0.2
            const float LANE_NAKA_END = 0.4f;       // 中位置    0.2~0.4
            const float LANE_SOTO_END = 0.6f;       // 外位置    0.4~0.6
            
            if (laneDistance <= LANE_UTI_END)
            {
                return Gallop.RaceDefine.LaneType.Uti;
            }
            else if (laneDistance <= LANE_NAKA_END)
            {
                return Gallop.RaceDefine.LaneType.Naka;
            }
            else if (laneDistance <= LANE_SOTO_END)
            {
                return Gallop.RaceDefine.LaneType.Soto;
            }
            else
            {
                return Gallop.RaceDefine.LaneType.Oosoto;
            }
        }

#if GALLOP
        public static float CalcLaneMoveSpeedBeforeFirstMoveLanePoint(Gallop.IHorseRaceInfo horse, float speed)
        {
            float laneCoef = horse.GetLaneDistance() / horse.LaneDistanceMax;
            speed *= (1.0f + (laneCoef) * horse.ParamDefine.laneMoveSpeedOutCoef);
            return speed;
        }
#endif
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public static float CalcLaneMoveSpeedBeforeFirstMoveLanePoint(IHorseRaceInfoSimulate horse, float speed)
        {
            float laneCoef = horse.GetLaneDistance() / horse.LaneDistanceMax;
            speed *= (1.0f + (laneCoef) * horse.ParamDefine.laneMoveSpeedOutCoef);
            return speed;
        }
#endif
        
        public static float CalcMoveLaneAccelPerSec(Gallop.RaceParamDefine paramDefine)
        {
            float retAccel = paramDefine.laneMoveSpeedBase * paramDefine.laneMoveAccelMoveSpeedBaseCoef;
            return retAccel;
        }
        #endregion <レーン移動>
        
        #region <スキル>
        /// <summary>
        /// スキル効果のカテゴリ取得。
        /// </summary>
        public static SkillDefine.SkillCategory GetSkillCategory(SkillDefine.SkillAbilityType abilityType)
        {
            switch (abilityType)
            {
                // 速度系。
                case SkillDefine.SkillAbilityType.CurrentSpeed:
                case SkillDefine.SkillAbilityType.CurrentSpeedWithNaturalDeceleration:
                case SkillDefine.SkillAbilityType.TargetSpeed:
                    return SkillDefine.SkillCategory.Speed;
                
                // 回復系。
                case SkillDefine.SkillAbilityType.HpRate:
                case SkillDefine.SkillAbilityType.HpDecRate:
                case SkillDefine.SkillAbilityType.TemptationEndTime:
                    return SkillDefine.SkillCategory.Heal;
                
                // 加速系。
                case SkillDefine.SkillAbilityType.Accel:
                    return SkillDefine.SkillCategory.Accel;
                    
                case SkillDefine.SkillAbilityType.None:
                case SkillDefine.SkillAbilityType.Speed:
                case SkillDefine.SkillAbilityType.Stamina:
                case SkillDefine.SkillAbilityType.Power:
                case SkillDefine.SkillAbilityType.Guts:
                case SkillDefine.SkillAbilityType.Wiz:
                case SkillDefine.SkillAbilityType.AllStatus:
                case SkillDefine.SkillAbilityType.StartDash:
                case SkillDefine.SkillAbilityType.StartDelayFix:
                case SkillDefine.SkillAbilityType.HpRateDemerit:
                case SkillDefine.SkillAbilityType.LaneMoveSpeed:
                case SkillDefine.SkillAbilityType.TemptationPer:
                case SkillDefine.SkillAbilityType.PushPer:
                case SkillDefine.SkillAbilityType.ForceOvertakeIn:
                case SkillDefine.SkillAbilityType.ForceOvertakeOut:
                case SkillDefine.SkillAbilityType.TargetLane:
                case SkillDefine.SkillAbilityType.VisibleDistance:
                case SkillDefine.SkillAbilityType.ChallengeMatchBonus_Old:
                case SkillDefine.SkillAbilityType.ChallengeMatchBonusStatus:
                case SkillDefine.SkillAbilityType.ChallengeMatchBonusMotivation:
                case SkillDefine.SkillAbilityType.RunningStyleExOonige:
                case SkillDefine.SkillAbilityType.ActivateRandomNormalAndRareSkill:
                case SkillDefine.SkillAbilityType.ActivateRandomRareSkill:
                case SkillDefine.SkillAbilityType.DebuffCancel:
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiply:
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiplyOtherActivate:
                case SkillDefine.SkillAbilityType.ActivateSpecificSkill:
                    return SkillDefine.SkillCategory.Null;
                
                default:
                    Debug.LogWarning($"スキル効果種類からカテゴリを計算できません。abilityType={abilityType}");
                    return SkillDefine.SkillCategory.Null;
            }
        }
        
        /// <summary>
        /// スキルが発動者自身にとってデメリットのあるものかどうかを判定する。
        /// </summary>
        /// <returns>デメリットのあるスキルであればtrue。</returns>
        public static bool IsSkillDemerit(SkillDefine.SkillAbilityType abilityType, float abilityValue)
        {
            bool isNegativeValue = abilityValue < 0;
            switch (abilityType)
            {
                // 効果値が負の値だったらデメリットと見なすスキル。
                case SkillDefine.SkillAbilityType.Speed:
                case SkillDefine.SkillAbilityType.Stamina:
                case SkillDefine.SkillAbilityType.Power:
                case SkillDefine.SkillAbilityType.Guts:
                case SkillDefine.SkillAbilityType.Wiz:
                case SkillDefine.SkillAbilityType.AllStatus:
                case SkillDefine.SkillAbilityType.HpRate:
                case SkillDefine.SkillAbilityType.CurrentSpeed:
                case SkillDefine.SkillAbilityType.CurrentSpeedWithNaturalDeceleration:
                case SkillDefine.SkillAbilityType.TargetSpeed:
                case SkillDefine.SkillAbilityType.LaneMoveSpeed:
                case SkillDefine.SkillAbilityType.PushPer:
                case SkillDefine.SkillAbilityType.Accel:
                case SkillDefine.SkillAbilityType.VisibleDistance:
                    return isNegativeValue;

                // 効果値が正の値だったらデメリットと見なすスキル。
                case SkillDefine.SkillAbilityType.TemptationPer:
                    return !isNegativeValue;

                // 効果自体がデメリットと見なすスキル。
                case SkillDefine.SkillAbilityType.TemptationEndTime:
                case SkillDefine.SkillAbilityType.StartDelayFix:
                    return true;

                // 効果自体がデメリットにはならないスキル。
                case SkillDefine.SkillAbilityType.ForceOvertakeIn:
                case SkillDefine.SkillAbilityType.ForceOvertakeOut:
                case SkillDefine.SkillAbilityType.TargetLane:
                case SkillDefine.SkillAbilityType.HpDecRate:
                case SkillDefine.SkillAbilityType.HpRateDemerit:
                case SkillDefine.SkillAbilityType.ChallengeMatchBonus_Old:
                case SkillDefine.SkillAbilityType.ChallengeMatchBonusStatus:
                case SkillDefine.SkillAbilityType.ChallengeMatchBonusMotivation:
                case SkillDefine.SkillAbilityType.RunningStyleExOonige:
                case SkillDefine.SkillAbilityType.ActivateRandomNormalAndRareSkill:
                case SkillDefine.SkillAbilityType.ActivateRandomRareSkill:
                case SkillDefine.SkillAbilityType.DebuffCancel:
                case SkillDefine.SkillAbilityType.ActivateSpecificSkill:
                    return false;

                // 1.0を超えているならデメリットと見なすスキル。
                case SkillDefine.SkillAbilityType.StartDash:
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiply:
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiplyOtherActivate:
                    return abilityValue > 1.0f;

                // 未使用になったスキルはとりあえずエラーも出さずにスルーしておく。
                case SkillDefine.SkillAbilityType.NOUSE:
                case SkillDefine.SkillAbilityType.NOUSE_2:
                case SkillDefine.SkillAbilityType.NOUSE_3:
                case SkillDefine.SkillAbilityType.NOUSE_4:
                case SkillDefine.SkillAbilityType.NOUSE_5:
                case SkillDefine.SkillAbilityType.NOUSE_7:
                case SkillDefine.SkillAbilityType.NOUSE_8:
                case SkillDefine.SkillAbilityType.NOUSE_10:
                case SkillDefine.SkillAbilityType.NOUSE_14:
                case SkillDefine.SkillAbilityType.NOUSE_20:
                case SkillDefine.SkillAbilityType.NOUSE_21:
                    return false;

                // 振り分けられなかったらエラーである。
                default:
                    Debug.Assert(false, string.Format("デメリット有無で判定不可能なAbilityTypeです。AbilityType={0}", abilityType));
                    return false;
            }
        }

        /// <summary>
        /// SkillModifierがデメリットのあるものかどうかを判定する。
        /// </summary>
        /// <returns>デメリットのあるスキルであればtrue。</returns>
        private static bool IsSkillModifierDemerit(SkillDefine.SkillModifierParam skillModifierParamType, float value)
        {
            bool isNegativeValue = value < 0;
            switch (skillModifierParamType)
            {
                // 効果値が負の値だったらデメリットと見なすスキル。
                case SkillDefine.SkillModifierParam.Speed:
                case SkillDefine.SkillModifierParam.Stamina:
                case SkillDefine.SkillModifierParam.Power:
                case SkillDefine.SkillModifierParam.Guts:
                case SkillDefine.SkillModifierParam.Wiz:
                case SkillDefine.SkillModifierParam.Hp:
                case SkillDefine.SkillModifierParam.CurrentSpeed:
                case SkillDefine.SkillModifierParam.CurrentSpeedWithNaturalDeceleration:
                case SkillDefine.SkillModifierParam.TargetSpeed:
                case SkillDefine.SkillModifierParam.LaneMoveSpeed:
                case SkillDefine.SkillModifierParam.PushPer:
                case SkillDefine.SkillModifierParam.Accel:
                case SkillDefine.SkillModifierParam.VisibleDistance:
                    return isNegativeValue;

                // 効果値が正の値だったらデメリットと見なすスキル。
                case SkillDefine.SkillModifierParam.TemptationPer:
                    return !isNegativeValue;

                // 効果自体がデメリットと見なすスキル。
                case SkillDefine.SkillModifierParam.TemptationEndTime:
                case SkillDefine.SkillModifierParam.StartDelayFix:
                case SkillDefine.SkillModifierParam.HpRateDemerit:
                    return true;

                // 効果自体がデメリットにはならないスキル。
                case SkillDefine.SkillModifierParam.ForceOvertakeIn:
                case SkillDefine.SkillModifierParam.ForceOvertakeOut:
                case SkillDefine.SkillModifierParam.TargetLane:
                case SkillDefine.SkillModifierParam.HpDecRate:
                case SkillDefine.SkillModifierParam.RunningStyleExOonige:
                case SkillDefine.SkillModifierParam.ActivateRandomNormalAndRareSkill:
                case SkillDefine.SkillModifierParam.ActivateRandomRareSkill:
                case SkillDefine.SkillModifierParam.DebuffCancel:
                case SkillDefine.SkillModifierParam.TargetSpeedOnMoveLane:
                case SkillDefine.SkillModifierParam.ActivateSpecificSkill:
                    return false;

                // 1.0を超えているならデメリットと見なすスキル。
                case SkillDefine.SkillModifierParam.StartDelayScale:
                case SkillDefine.SkillModifierParam.DebuffAbilityValueMultiply:
                case SkillDefine.SkillModifierParam.DebuffAbilityValueMultiplyOtherActivate:
                    return value > 1.0f;

                // 振り分けられなかったらエラーである。
                default:
                    Debug.Assert(false, $"デメリット有無で判定不可能なSkillModifierParamTypeです。SkillModifierParamType={skillModifierParamType}");
                    return false;
            }
        }
        
        /// <summary>
        /// 指定効果がデバフかどうか。
        /// </summary>
        public static bool IsDebuff(SkillDefine.SkillModifierParam skillModifierParamType, float value)
        {
            // デメリットでなければデバフとして扱わない。
            if (!IsSkillModifierDemerit(skillModifierParamType, value))
            {
                return false;
            }

            return true;
        }
        
        /// <summary>
        /// 指定効果がデバフかどうか。
        /// </summary>
        public static bool IsDebuff(SkillDefine.SkillAbilityType abilityType, float abilityValue)
        {
            // 効果無し。
            if (SkillDefine.SkillAbilityType.None == abilityType)
            {
                return false;
            }

            // デメリットでなければデバフスキルとして扱わない。
            if (!IsSkillDemerit(abilityType, abilityValue))
            {
                return false;
            }

            return true;
        }
        #endregion
        
        
        //---------------------------------------------------------------
        /// <summary>
        /// 前方をブロックされて続けている時間、サイドをブロックされ続けている時間を元に、両方をブロックされ続けている時間を計算する。
        /// </summary>
        //---------------------------------------------------------------
        public static float GetBlockAllContinueTime(float blockFrontTime, float blockSideTime)
        {
            // 前方とサイドの両方が一定時間継続されている状態を判定したいので、
            // 継続時間の少ない方を返却値にする。
            return Math.Min(blockFrontTime, blockSideTime);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// スキル発動条件で「ｎ%」判定に使用する整数閾値の計算。
        /// </summary>
        /// <param name="total">全体数。</param>
        /// <param name="per">％を百分率で指定。１以上を想定。</param>
        /// <returns>閾値となる整数。</returns>
        //---------------------------------------------------------------
        public static int GetIntThresholdByPer(int total, int per)
        {
            /*
             *  例：
             *  horse_order_rate <= 33
             *  出走キャラが１８人の場合
             *  閾値となる順位は、( 18人 * 0.33 ) = 5.94位 → 6位
             *  の「6」を計算する。
            */

            // 1～100で指定してもらう。
            Debug.Assert(per >= 1 && per <= 100);
            // 閾値となる値計算。小数点第１位を四捨五入。
            int retValue = (int)(total * ((float)per / 100.0f) + 0.5f);

            return retValue;
        }
        
    #if CYG_DEBUG
        /// <summary>
        /// スキル発動条件で使う%を、分子・分母から計算。
        /// </summary>
        /// <param name="val">分子</param>
        /// <param name="totalVal">分母</param>
        public static int DbgGetIntPer(int val, int totalVal)
        {
            // GetIntThresholdByPerと実装を合わせる。
            return (int)((val - 0.5f) / totalVal * 100);
        }
    #endif

        //---------------------------------------------------------------
        /// <summary>
        /// 距離をハロンに換算。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil.TestDistance2Furlong(int, float)"/>
        //---------------------------------------------------------------
        public static int Distance2Furlong(float distance)
        {
            // 浮動小数の計算誤差を考慮し、小数点第１位を四捨五入。
            return RaceUtilMath.FloorToInt((distance + 0.5f) * Gallop.RaceDefine.ONE_FURONG_DISTANCE_INV);
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// 指定距離が根幹距離かどうか。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil.TestIsBasisDistance(bool, int)"/>
        //---------------------------------------------------------------
        public static bool IsBasisDistance(int distance)
        {
            const int BASIS_DISTANCE = 400;
            const int NON_BASIS_DISTANCE_REST_THRESHOLD = 100;
        
            // コース距離を400で割った余りが100未満となるのが根幹距離。
            int rest = (distance % BASIS_DISTANCE);
            return (rest < NON_BASIS_DISTANCE_REST_THRESHOLD);
        }
        
        /// <summary>
        /// タイマー更新。
        /// </summary>
        /// <param name="timeRemain">残り時間。</param>
        /// <param name="deltaTime">経過時間。</param>
        /// <returns>残り時間が0になったらtrue。</returns>
        public static bool UpdateTimer( ref float timeRemain, float deltaTime )
        {
            if( timeRemain > 0.0f )
            {
                timeRemain -= deltaTime;
                if( timeRemain < 0.0f )
                {
                    timeRemain = 0.0f;
                }
            }
            return timeRemain <= 0.0f;
        }
        
        /// <summary>
        /// コーナーリスト取得。
        /// </summary>
        public static List<CourseCorner> CalcCornerList(CourseParam[] courseParamArray, int courseDistance)
        {
            if (null == courseParamArray)
            {
                return new List<CourseCorner>();
            }

            // このレースに存在するコーナーをリスト化。
            var cornerList = new List<CourseCorner>();
            for (int i = 0, cnt = courseParamArray.Length; i < cnt; ++i)
            {
                var courseParam = courseParamArray[i];
                int cornerNo = courseParam.Values[CourseParam.CORNER_VALUE_INDEX_NO];

                // コーナー始点・終点を取得。
                if (!RaceUtil.GetCornerDistance(courseParam, out float cornerStart, out float cornerEnd))
                {
                    continue;
                }

                bool isFinalCorner = IsFinalCorner(courseDistance - cornerEnd, cornerNo);

                var newCorner = new CourseCorner(
                    cornerNo, 
                    cornerStart, 
                    cornerEnd,
                    isFinalCorner);
                cornerList.Add(newCorner);
            }

            return cornerList;
        }

        /// <summary>
        /// 坂リスト取得。
        /// </summary>
        public static List<CourseSlope> CalcSlopeList(CourseParam[] slopeEventArray)
        {
            if (null == slopeEventArray)
            {
                return new List<CourseSlope>();
            }

            // このレースに存在する坂をリスト化。
            var slopeList = new List<CourseSlope>();
            for (int i = 0; i < slopeEventArray.Length; ++i)
            {
                var slopeParam = slopeEventArray[i];

                // 坂始点・終点を取得。
                if (!GetSlopeDistance(slopeParam, out float slopeStart, out float slopeEnd))
                {
                    continue;
                }

                if (!CourseParam.GetSlopeValue(slopeParam.Values, out _, out var slopeType, out _))
                {
                    continue;
                }
                var newSlope = new CourseSlope(
                    slopeType, 
                    slopeStart, 
                    slopeEnd);
                slopeList.Add(newSlope);
            }

            return slopeList;
        }
    }
}


#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース関連ユーティリティ：AI関連計算式：シミュレーター専用
    /// </summary>
    //-------------------------------------------------------------------
    public static partial class RaceUtil
    {
        /// <summary>
        /// 現在距離から、今いる区間を計算。
        /// </summary>
        /// <param name="sectionDistance">１区間の距離。</param>
        /// <param name="currentDistance">現在距離。</param>
        /// <returns>区間を1~で返す。</returns>
        public static int Distance2Section(float sectionDistance, float currentDistance)
        {
            // 現在距離→区間に変換。
            int section = RaceUtilMath.FloorToInt(currentDistance / sectionDistance);
            // 区間は1始まり。
            section++;
            return section;
        }

        #region <スキル>
        //---------------------------------------------------------------
        /// <summary>
        /// レース開始時のスキル発動確率計算。
        /// </summary>
        /// <param name="wiz">賢さ。</param>
        /// <returns>発動確率百分率。</returns>
        //---------------------------------------------------------------
        public static float CalcActivatePer(float wiz, Gallop.RaceParamDefine.SkillParam skillParam)
        {
            float per = skillParam.LotActivatePerVal1 - (skillParam.LotActivatePerVal2 / wiz);
            // 最小値でクリッピング。
            per = Math.Max(skillParam.ActivatePerMin, per);
            return per;
        }
        #endregion

        /// <summary>
        /// 指定キャラ群の中での順位を取得。
        /// </summary>
        /// <param name="horse">このキャラが</param>
        /// <param name="horses">このキャラ群の中で何位か</param>
        /// <returns>0~。</returns>
        public static int GetOrderInHorses(IHorseRaceInfoSimulate horse, IHorseRaceInfoSimulate[] horses)
        {
            int order = 0;
            for (int i = 0; i < horses.Length; ++i)
            {
                if (horse.CurOrder > horses[i].CurOrder)
                {
                    ++order;
                }
            }
            return order;
        }
        public static IHorseRaceInfoSimulate GetHorseByOrder(int order, IHorseRaceInfoSimulate[] horseArray)
        {
            if(order < 0 || order >= horseArray.Length)
            {
                return null;
            }

            var sortedHorseArray = horseArray.ToArray();
            System.Array.Sort(sortedHorseArray, (a, b) => a.CurOrder - b.CurOrder);
            return sortedHorseArray[order];
        }

        public static IHorseRaceInfoSimulate GetTopOrderHorses(IHorseRaceInfoSimulate[] horseArray)
        {
            int topOrder = int.MaxValue;
            IHorseRaceInfoSimulate topHorse = null;
            
            for (int i = 0; i < horseArray.Length; ++i)
            {
                var horse = horseArray[i];
                if (horse.CurOrder < topOrder)
                {
                    topOrder = horse.CurOrder;
                    topHorse = horse;
                }
            }
            
            return topHorse;
        }
        
        /// <summary>
        /// 指定走法がレース参加者中で最も多いかどうかを判定。
        /// </summary>
        /// <returns>指定走法が最も多いならtrue。同数以上の走法がある場合はfalse。</returns>
        public static bool CheckRunningStyleCountSameMax(Gallop.RaceDefine.RunningStyle selfStyle, IRaceHorseAccessor horseAccessor)
        {
            var selfStyleCount = horseAccessor.GetRunningStyleCount(selfStyle);

            // 自分と同じ走法を選んでいる人数よりも、他の走法の人数が多い場合、その数をカウントする。
            var checkStyles = Gallop.RaceDefine.HORSE_USABLE_RUNNING_STYLE_ARRAY;
            foreach (var style in checkStyles)
            {
                // 自分と同じ走法は無視。
                if (style == selfStyle)
                {
                    continue;
                }

                // 同数以上の走法が存在する場合、最も多いとは見なさない。
                int styleCount = horseAccessor.GetRunningStyleCount(style);
                if (styleCount >= selfStyleCount)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 指定育成ランクよりも育成ランクの高いキャラ数をカウント。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil.TestCalcFinalGradeHigherCount(int, int[])"/>
        public static int CalcFinalGradeHigherCount(int baseFinalGrade, IRaceHorseAccessor horseAccessor)
        {
            int retCnt = 0;

            var horseArray = horseAccessor.GetHorseRaceInfos();
            foreach (var horse in horseArray)
            {
                if (baseFinalGrade < horse.FinalGrade)
                {
                    ++retCnt;
                }
            }

            return retCnt;
        }

        /// <summary>
        /// 指定育成ランクよりも育成ランクの低いキャラ数をカウント。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil.TestCalcFinalGradeLowerCount(int, int[])"/>
        public static int CalcFinalGradeLowerCount(int baseFinalGrade, IRaceHorseAccessor horseAccessor)
        {
            int retCnt = 0;

            var horseArray = horseAccessor.GetHorseRaceInfos();
            foreach (var horse in horseArray)
            {
                if (baseFinalGrade > horse.FinalGrade)
                {
                    ++retCnt;
                }
            }

            return retCnt;
        }

        /// <summary>
        /// 指定走法の前の走法を取得。
        /// </summary>
        /// <param name="style">基準になる走法。</param>
        /// <param name="retForwardStyle">styleの前の走法を返却。戻り値がfalseで返る場合は参照しないように。</param>
        /// <returns>前の走法が無い場合はfalse。</returns>
        public static bool GetForwardRunningStyle(Gallop.RaceDefine.RunningStyle style, out Gallop.RaceDefine.RunningStyle retForwardStyle)
        {
            switch (style)
            {
                case Gallop.RaceDefine.RunningStyle.Senko: retForwardStyle = Gallop.RaceDefine.RunningStyle.Nige; return true;
                case Gallop.RaceDefine.RunningStyle.Sashi: retForwardStyle = Gallop.RaceDefine.RunningStyle.Senko; return true;
                case Gallop.RaceDefine.RunningStyle.Oikomi: retForwardStyle = Gallop.RaceDefine.RunningStyle.Sashi; return true;
                default: retForwardStyle = Gallop.RaceDefine.RunningStyle.Nige; return false; // 指定走法の前の走法は無い。
            }
        }

        /// <summary>
        /// 第１引数の走法が、第２引数の走法より前かどうか。
        /// </summary>
        public static bool IsForwardRunningStyle(Gallop.RaceDefine.RunningStyle lh, Gallop.RaceDefine.RunningStyle rh)
        {
            return lh < rh;
        }
        
        /// <summary>
        /// 第１引数の走法が、第２引数の走法より後ろかどうか。
        /// </summary>
        public static bool IsBackwardRunningStyle(Gallop.RaceDefine.RunningStyle lh, Gallop.RaceDefine.RunningStyle rh)
        {
            return lh > rh;
        }

        /// <summary>
        /// baseOrderより前に、後ろの走法のキャラがいるかどうか。
        /// </summary>
        public static bool IsExistBackwardRunningStyle(int baseOrder, Gallop.RaceDefine.RunningStyle baseRunningStyle, IRaceHorseAccessor horseAccessor)
        {
            // baseOrderより前に、後ろの走法のキャラがいるか調べる。
            int checkStartOrder = baseOrder - 1;
            for (int checkOrder = checkStartOrder; checkOrder >= 0; checkOrder--)
            {
                var checkHorse = horseAccessor.GetHorseInfoByOrder(checkOrder);
                
                if (IsBackwardRunningStyle(checkHorse.RunningStyle, baseRunningStyle))
                {
                    return true;
                }
            }

            // baseOrderより前に、後ろの走法のキャラはいない。
            return false;
        }

        /// <summary>
        /// SkillAbilityTypeを持ったスキルがあるかどうか。
        /// </summary>
        public static bool HasAbility(SkillDefine.SkillAbilityType type, RaceHorseSkillData[] skillDataArray)
        {
            foreach (var skillData in skillDataArray)
            {
                if (HasAbility(type, skillData.skill_id))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// スキルがSkillAbilityTypeを持っているかどうか。
        /// </summary>
        private static bool HasAbility(SkillDefine.SkillAbilityType type, int skillId)
        {
            if (!MasterManager.HasInstance())
            {
                return false;
            }
            
            var masterSkill = MasterManager.Instance.MasterSkillData.Get(skillId);
            return HasAbility(type, masterSkill);
        }

        public static bool HasAbility(SkillDefine.SkillAbilityType type, ISkillDataAccessor skillData)
        {
            if (skillData.AbilityType11 == (int)type ||
                skillData.AbilityType12 == (int)type ||
                skillData.AbilityType13 == (int)type ||
                skillData.AbilityType21 == (int)type ||
                skillData.AbilityType22 == (int)type ||
                skillData.AbilityType23 == (int)type)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// SkillDetailに対して対象のアビリティタイプがあるかどうかの確認
        /// </summary>
        /// <param name="type"></param>
        /// <param name="skillDetail"></param>
        /// <returns></returns>
        public static bool HasAbility(SkillDefine.SkillAbilityType type, ISkillDetail skillDetail)
        {
            return skillDetail.Abilities.Any(ability => ability.AbilityType == type);
        }
        
        /// <summary>
        /// 追加発動するアビリティかどうか
        /// </summary>
        /// <returns></returns>
        public static bool IsAdditionalActivateAbility(SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType)
        {
            return additionalActivateType != SkillDefine.SkillAbilityAdditionalActivateType.None;
        }
        
    #if CYG_DEBUG
        public static List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCreateCompeteGroupHorseList(IHorseRaceInfoSimulate horse, int group, Dictionary<int, List<IHorseRaceInfoSimulate>> competeGroupDic)
        {
            var retList = new List<RaceSimulateHorseResultDebugData.CompeteGroupHorse>();
            
            var groupHorseList = competeGroupDic[group];
            foreach (var groupHorse in groupHorseList)
            {
                if (groupHorse == horse)
                {
                    continue;
                }
                float distanceDiff = groupHorse.GetDistance() - horse.GetDistance();
                float laneDiff = groupHorse.GetLaneDistance() - horse.GetLaneDistance();
                var info = new RaceSimulateHorseResultDebugData.CompeteGroupHorse(
                    groupHorse.HorseIndex, 
                    distanceDiff, 
                    laneDiff, 
                    group);
                retList.Add(info);
            }

            return retList;
        }
    #endif
    }
}
#endif

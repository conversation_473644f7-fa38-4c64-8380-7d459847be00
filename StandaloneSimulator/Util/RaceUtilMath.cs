using System;
using System.Numerics;

namespace StandaloneSimulator
{
    public static class RaceUtilMath
    {
        /// <summary>
        /// ベクトル系の良く使う初期値。Vector3.zeroだと構造体のNewのコストが発生するので定義しておく
        /// </summary>
        public static readonly System.Numerics.Vector2 VECTOR2_ZERO = new System.Numerics.Vector2(0, 0);
        public static readonly System.Numerics.Vector3 VECTOR3_ZERO = new System.Numerics.Vector3(0, 0, 0);
        public static readonly System.Numerics.Vector4 VECTOR4_ZERO = new System.Numerics.Vector4(0, 0, 0, 0);
        public static readonly System.Numerics.Vector2 VECTOR2_ONE = new System.Numerics.Vector2(1, 1);
        public static readonly System.Numerics.Vector2 VECTOR2_HALF = new System.Numerics.Vector2(0.5f, 0.5f);
        public static readonly System.Numerics.Vector3 VECTOR3_ONE = new System.Numerics.Vector3(1, 1, 1);
        public static readonly System.Numerics.Vector4 VECTOR4_ONE = new System.Numerics.Vector4(1, 1, 1, 1);
        public static readonly System.Numerics.Vector2 VECTOR2_UP = new System.Numerics.Vector2(0, 1);
        public static readonly System.Numerics.Vector3 VECTOR3_UP = new System.Numerics.Vector3(0, 1, 0);
        public static readonly System.Numerics.Vector3 VECTOR3_DOWN = new System.Numerics.Vector3(0, -1, 0);
        public static readonly System.Numerics.Vector3 VECTOR3_FORWARD = new System.Numerics.Vector3(0, 0, 1);
        public static readonly System.Numerics.Vector3 VECTOR3_RIGHT = new System.Numerics.Vector3(1, 0, 0);
        public static readonly System.Numerics.Vector3 VECTOR3_BACK = new System.Numerics.Vector3(0, 0, -1);
        public static readonly System.Numerics.Quaternion QUATERNION_IDENTITY = System.Numerics.Quaternion.Identity;

        public static T Clamp<T>(T value, T min, T max) where T : IComparable<T>
        {
            if (value.CompareTo(min) < 0)
            {
                return min;
            }
            else if (value.CompareTo(max) > 0)
            {
                return max;
            }
            return value;
        }

        public static T Max<T>(T value, T min) where T : IComparable<T>
        {
            if (value.CompareTo(min) < 0)
            {
                return min;
            }

            return value;
        }

        /// <summary>
        /// 浮動小数誤差
        /// Mathf.Epsilonは0,非0で挙動が変わるので使用しない
        /// </summary>
        public const float EPSILON = 0.000001f;

        public static bool Approximately(float a, float b) => Math.Abs(a - b) < EPSILON;
        
        public static int FloorToInt(float val)
        {
            return (int)Math.Floor(val);
        }
        
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        public static float Lerp(float a, float b, float t)
        {
            return a + (b - a) * t;
        }

        private const float DEG_2_RAD = (float)Math.PI / 180.0f;
        private const float RAD_2_DEG = 180.0f / (float)Math.PI;
        public static float ToRadian(float degree)
        {
            return degree * DEG_2_RAD;
        }
        public static float ToDegree(float radian)
        {
            return radian * RAD_2_DEG;
        }

        public static Quaternion QuaternionFromEuler(Vector3 euler)
        {
            return QuaternionFromEuler(euler.X, euler.Y, euler.Z);
        }
        public static Quaternion QuaternionFromEuler(float x, float y, float z)
        {
            // yaw-pitch-rollで渡すので、y-x-zの順で渡す。
            return Quaternion.CreateFromYawPitchRoll(
                ToRadian(y),
                ToRadian(x),
                ToRadian(z));
        }
        
        public static int CeilToInt(float val)
        {
            return (int)Math.Ceiling(val);
        }
#endif
        
        #region マスターデータにおけるfloat<-->int変換
        private const float MasterFloatAccuracy = 10000.0f;
        private const float MasterInt2FloatAccuracy = 1 / MasterFloatAccuracy;
        /// <summary>
        /// float→CSV出力用のIntへ変換。
        /// </summary>
        public static int MasterFloat2Int(float value)
        {
            return (int)(value * MasterFloatAccuracy);
        }
        /// <summary>
        /// CSVで入力されているint→floatへ変換。
        /// </summary>
        public static float MasterInt2Float(int value)
        {
            return value * MasterInt2FloatAccuracy;
        }
        #endregion
    }
}


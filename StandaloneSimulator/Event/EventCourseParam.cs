#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public class EventCourseParam : EventHandlerHorse1ValuesSimulate
    {
        private int[] _calledEventNoList = null;
        private List<CourseParam> _cachedEventList = null;

        public EventCourseParam(IRaceHorseAccessor horseAccessor, CourseEventManager courseEvent)
            : base(horseAccessor, courseEvent)
        {
            _calledEventNoList = new int[HorseCountSimulate];
            for( int i = 0, cnt = _calledEventNoList.Length; i < cnt; ++i )
            {
                _calledEventNoList[ i ] = -1;
            }
        }

        private List<CourseParam> CacheEventList()
        {
            return _courseEvent.GetCourseEventList();
        }

        private void ExecEvent(CourseParam courseEvent, int eventNo, IHorseRaceInfoSimulate horseInfo, int horseIndex, float distance)
        {
            if (_calledEventNoList[horseIndex] < eventNo)
            {
                // クライアント側ではOffsetを考慮するが、これは演出のための値なので、レースシミュレーター側では無視する
                if (courseEvent._distance <= distance)
                {
                    _calledEventNoList[horseIndex] = eventNo;
                    CallbackExec(horseInfo, (int)courseEvent._paramType, courseEvent.Values);
                }
            }
        }

        public override void InitialEvent()
        {
            if (_cachedEventList == null)
            {
                _cachedEventList = CacheEventList();
            }

            for (int eventNo = 0; eventNo < _cachedEventList.Count; eventNo++)
            {
                var courseEvent = _cachedEventList[eventNo];
                for (int i = 0; i < HorseCountSimulate; i++)
                {
                    var horseInfo = GetHorseSimulate(i);
                    ExecEvent(courseEvent, eventNo, horseInfo, i, 0.0f);
                }
            }
        }

        public override void CheckEvent()
        {
            if ( _cachedEventList == null )
            {
                _cachedEventList = CacheEventList();
            }
            for (int eventNo = 0; eventNo < _cachedEventList.Count; eventNo++)
            {
                var courseEvent = _cachedEventList[eventNo];
                for (int i = 0; i < HorseCountSimulate; i++)
                {
                    var horseInfo = GetHorseSimulate(i);
                    ExecEvent(courseEvent, eventNo, horseInfo, i, horseInfo.GetDistance());
                }
            }
        }
    }
}
#endif

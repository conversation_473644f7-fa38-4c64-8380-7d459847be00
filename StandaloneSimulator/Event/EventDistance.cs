#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    /// <summary>
    /// {Param}m走ったとき
    /// </summary>
    public class EventDistance : EventHandlerHorse1Simulate
    {
        private int[] _isCalledDistaceList = null;

        private readonly int checkDistance = 100;   // CallBackを呼ぶ距離単位

        public EventDistance(IRaceHorseAccessor horseAccessor, CourseEventManager courseEvent)
            : base(horseAccessor, courseEvent)
        {
            _isCalledDistaceList = new int[HorseCountSimulate];
        }

        public override void CheckEvent()
        {
            for ( int i = 0 ; i < HorseCountSimulate ; i++ )
            {
                var horse = GetHorseSimulate(i);
                int curDis = (int)horse.GetDistance() / checkDistance;
                if ( curDis > _isCalledDistaceList[i] )
                {
                    _isCalledDistaceList[i] = curDis;
                    CallbackExec(horse , curDis * checkDistance);
                }
            }
        }
    }
}
#endif

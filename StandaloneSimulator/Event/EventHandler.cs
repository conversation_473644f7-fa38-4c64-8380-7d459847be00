using System.Collections.Generic;

namespace StandaloneSimulator
{
    /// <summary>
    /// イベント管理の基底
    /// </summary>
    public class EventHandler<T>
    {
        protected List<T> callbackList;
        
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// 馬の総数
        /// </summary>
        protected int HorseCountSimulate => _horseAccessor.GetHorseNumber();
        
        protected IRaceHorseAccessor _horseAccessor;
        protected CourseEventManager _courseEvent;

        /// <summary> 
        /// コンストラクタ
        /// </summary>
        public EventHandler(IRaceHorseAccessor horseAccessor, CourseEventManager courseEvent)
        {
            callbackList = new List<T>();
            _horseAccessor = horseAccessor;
            _courseEvent = courseEvent;
        }
#endif
#if GALLOP
        /// <summary>
        /// 馬の総数
        /// </summary>
        protected int HorseCount
        {
            get
            {
                return  Gallop.RaceManager.RaceInfo.NumRaceHorses;
            }
        }

        protected Gallop.RaceManager raceManager;

        /// <summary> 
        /// コンストラクタ
        /// </summary>
        /// <param name="raceEvent"></param>
        /// <param name="raceManager"></param>
        public EventHandler(Gallop.RaceManager raceManager){
            callbackList = new List<T>();
            this.raceManager = raceManager;
        }
#endif
        
        /// <summary>
        /// 登録
        /// </summary>
        /// <param name="callback"></param>
        public void Add(T callback)
        {
            if (!callbackList.Contains(callback))
            {
                callbackList.Add(callback);
            }
#if CYG_DEBUG
            else
            {
                Debug.LogError("登録済み "+callback);
            }   
#endif
        }

        /// <summary>
        /// 存在したらtrue
        /// </summary>
        /// <param name="callback"></param>
        /// <returns></returns>
        public bool IsExist(T callback)
        {
            return callbackList.Contains(callback);
        }

        /// <summary>
        /// 削除
        /// </summary>
        /// <param name="callback"></param>
        public void Remove(T callback)
        {
            if (callbackList.Contains(callback))
            {
                callbackList.Remove(callback);
            }
#if CYG_DEBUG
            else
            {
                Debug.LogError( "無いです " + callback );
            }
#endif
        }

#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// 馬取得
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public IHorseRaceInfoSimulate GetHorseSimulate(int index)
        {
            return _horseAccessor.GetHorseInfo(index);
        }
#endif
#if GALLOP
        /// <summary>
        /// 馬取得
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public Gallop.IHorseRaceInfo GetHorse(int index)
        {
            return raceManager.GetHorseInfo(index);
        }
#endif

        /// <summary>
        /// イベント発生チェック
        /// </summary>
        public virtual void CheckEvent(){

        }

        /// <summary>
        /// 初期イベント(初期化直後に呼び出される)
        /// </summary>
        public virtual void InitialEvent()
        {

        }
        public void Release()
        {
            if (callbackList != null)
            {
                callbackList.Clear();
            }
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            _horseAccessor = null;
            _courseEvent = null;
#endif
#if GALLOP
            raceManager = null;
#endif
        }
    }

#if STANDALONE_SIMULATOR || UNITY_EDITOR
    /// <summary>
    /// イベント管理：引数に馬１つ取る。
    /// </summary>
    public class EventHandlerHorse1Simulate : EventHandler<RaceEventCallbackHorse1Simulate>
    {

        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public EventHandlerHorse1Simulate(IRaceHorseAccessor horseAccessor, CourseEventManager courseEvent) : base(
            horseAccessor, courseEvent)
        {
        }

        /// <summary>
        /// イベント発生通知。
        /// </summary>
        protected void CallbackExec(IHorseRaceInfoSimulate horse1, int param = 0)
        {
            for (int i = 0; i < callbackList.Count; i++)
            {
                callbackList[i](horse1, param);
            }
        }
    }
    
    /// <summary>
    /// イベント管理：引数に馬１つ取る。values配列使用。
    /// </summary>
    public class EventHandlerHorse1ValuesSimulate : EventHandler<RaceEventCallbackHorse1ValuesSimulate>
    {
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public EventHandlerHorse1ValuesSimulate(IRaceHorseAccessor horseAccessor, CourseEventManager courseEvent) : base(horseAccessor, courseEvent)
        {
        }
        
        /// <summary>
        /// イベント発生通知。
        /// </summary>
        protected void CallbackExec( IHorseRaceInfoSimulate horse1, int param, int[] values )
        {
            for (int i = 0; i < callbackList.Count; i++)
            {
                callbackList[i]( horse1, param, values );
            }
        }
    }
#endif
    
#if GALLOP
    public class EventHandlerHorse1 : EventHandler<RaceEventCallbackHorse1>
    {
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public EventHandlerHorse1(Gallop.RaceManager raceManager) : base( raceManager )
        {
        }

        /// <summary>
        /// イベント発生通知。
        /// </summary>
        protected void CallbackExec( Gallop.IHorseRaceInfo horse1, int param = 0 )
        {
            for (int i = 0; i < callbackList.Count; i++)
            {
                callbackList[i]( horse1, param );
            }
        }
    }
    
    /// <summary>
    /// イベント管理：引数に馬１つ取る。values配列使用。
    /// </summary>
    public class EventHandlerHorse1Values : EventHandler<RaceEventCallbackHorse1Values>
    {
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public EventHandlerHorse1Values(Gallop.RaceManager raceManager) : base( raceManager )
        {
        }
        
        /// <summary>
        /// イベント発生通知。
        /// </summary>
        protected void CallbackExec( Gallop.IHorseRaceInfo horse1, int param, int[] values )
        {
            for (int i = 0; i < callbackList.Count; i++)
            {
                callbackList[i]( horse1, param, values );
            }
        }
    }
#endif
}


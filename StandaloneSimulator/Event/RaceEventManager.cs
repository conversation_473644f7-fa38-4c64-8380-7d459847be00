using System.Collections.Generic;

namespace StandaloneSimulator
{
    /// <summary>
    /// レース中に発生するイベント
    /// </summary>
    /// <remarks>03_CameraEditor.xlsx 「race_player_camera」シートで参照しているので、並びを合わせること。</remarks>
    public enum RaceEvent
    {
        None = 0,

        /// <summary>
        /// {Param1}m走ったとき horse1 = 馬１, horse2 =null
        /// </summary>
        Distance,

        /// <summary>
        /// 未使用。
        /// </summary>
        NOUSE,

        /// <summary>
        /// イベントパラメータによる判定コールバック horse1 = 馬１, horse2 =null param = パラメータタイプ
        /// </summary>
        EventParam,

        Max,
    }

    /// <summary>
    /// イベント発生時に呼ぶデリゲート
    /// </summary>
    /// <param name="horse1"></param>
    /// <param name="horse2"></param>
#if STANDALONE_SIMULATOR || UNITY_EDITOR
    public delegate void RaceEventCallbackHorse1Simulate(IHorseRaceInfoSimulate horse1, int param);
    public delegate void RaceEventCallbackHorse1ValuesSimulate(IHorseRaceInfoSimulate horse1, int param, int[] values);
#endif
#if GALLOP
    public delegate void RaceEventCallbackHorse1(Gallop.IHorseRaceInfo horse1, int param);
    public delegate void RaceEventCallbackHorse1Values(Gallop.IHorseRaceInfo horse1, int param, int[] values);
#endif
    
    /// <summary>
    /// レース中のイベントを通達する
    /// シミュレート/再生兼用Partialクラス
    /// </summary>
    public partial class RaceEventManager
    {
        public bool IsInitialEvent { get; private set; }
        
        /// <summary>
        /// 初期更新処理
        /// </summary>
        public void InitProcess()
        {
            IsInitialEvent = true;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            foreach (var handle in _handleDictHorse1Simulate)
            {
                handle.Value.InitialEvent();
            }
            foreach (var handle in _handleDictHorse1ValuesSimulate)
            {
                handle.Value.InitialEvent();
            }
#endif
#if GALLOP
            foreach (var handle in _handleDictHorse1)
            {
                handle.Value.InitialEvent();
            }
            foreach (var handle in _handleDictHorse1Values)
            {
                handle.Value.InitialEvent();
            }
#endif
            IsInitialEvent = false;
        }

        /// <summary>
        /// イベント発動チェックを回す
        /// </summary>
        private void _CheckUpdate()
        {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            foreach (var handle in _handleDictHorse1Simulate)
            {
                handle.Value.CheckEvent();
            }
            foreach (var handle in _handleDictHorse1ValuesSimulate)
            {
                handle.Value.CheckEvent();
            }
#endif
#if GALLOP
            foreach (var handle in _handleDictHorse1)
            {
                handle.Value.CheckEvent();
            }
            foreach (var handle in _handleDictHorse1Values)
            {
                handle.Value.CheckEvent();
            }
#endif
        }

        public void Release()
        {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            if (_handleDictHorse1Simulate != null)
            {
                foreach (var handle in _handleDictHorse1Simulate)
                {
                    handle.Value.Release();
                }
                _handleDictHorse1Simulate.Clear();
                _handleDictHorse1Simulate = null;
            }
            if (_handleDictHorse1ValuesSimulate != null)
            {
                foreach (var handle in _handleDictHorse1ValuesSimulate)
                {
                    handle.Value.Release();
                }
                _handleDictHorse1ValuesSimulate.Clear();
                _handleDictHorse1ValuesSimulate = null;
            }
#endif
#if GALLOP
            if (_handleDictHorse1 != null)
            {
                foreach (var handle in _handleDictHorse1)
                {
                    handle.Value.Release();
                }
                _handleDictHorse1.Clear();
                _handleDictHorse1 = null;
            }
            if (_handleDictHorse1Values != null)
            {
                foreach (var handle in _handleDictHorse1Values)
                {
                    handle.Value.Release();
                }
                _handleDictHorse1Values.Clear();
                _handleDictHorse1Values = null;
            }
#endif
        }
    }

#if STANDALONE_SIMULATOR || UNITY_EDITOR
    // シミュレート用Partialクラス
    public partial class RaceEventManager
    {
        //イベント時に呼ぶコールバック達
        Dictionary<RaceEvent, EventHandlerHorse1Simulate>       _handleDictHorse1Simulate       = new Dictionary<RaceEvent, EventHandlerHorse1Simulate>();
        Dictionary<RaceEvent, EventHandlerHorse1ValuesSimulate> _handleDictHorse1ValuesSimulate = new Dictionary<RaceEvent, EventHandlerHorse1ValuesSimulate>();

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public RaceEventManager(IRaceHorseAccessor raceHorseAccessor, CourseEventManager courseEventManager)
        {
            _handleDictHorse1Simulate = new Dictionary<RaceEvent, EventHandlerHorse1Simulate>();
            _handleDictHorse1Simulate.Add(RaceEvent.Distance, new EventDistance(raceHorseAccessor, courseEventManager));
            
            _handleDictHorse1ValuesSimulate = new Dictionary<RaceEvent, EventHandlerHorse1ValuesSimulate>();
            _handleDictHorse1ValuesSimulate.Add(RaceEvent.EventParam, new EventCourseParam(raceHorseAccessor, courseEventManager));
        }
        
        /// <summary>
        /// 追加済みならtrue
        /// </summary>
        /// <param name="raceEvent"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public bool IsExist(RaceEvent raceEvent, RaceEventCallbackHorse1Simulate callback)
        {
            EventHandlerHorse1Simulate handle;
            if (_handleDictHorse1Simulate.TryGetValue(raceEvent, out handle))
            {
                return handle.IsExist(callback);
            }
            return false;
        }
        public bool IsExist(RaceEvent raceEvent, RaceEventCallbackHorse1ValuesSimulate callback)
        {
            EventHandlerHorse1ValuesSimulate handle;
            if (_handleDictHorse1ValuesSimulate.TryGetValue(raceEvent, out handle))
            {
                return handle.IsExist(callback);
            }
            return false;
        }

        /// <summary>
        /// イベント追加
        /// </summary>
        /// <returns>追加できたらtrue</returns>
        public void Add(RaceEvent raceEvent, RaceEventCallbackHorse1Simulate callback)
        {
            EventHandlerHorse1Simulate handle;
            if(_handleDictHorse1Simulate.TryGetValue(raceEvent , out handle))
            {
                handle.Add(callback);
            }
            else
            {
                Debug.LogError( string.Format( "登録しようとしている raceEventとcallback の組み合わせが不正です。raceEvent={0}, callback={1}", raceEvent, callback ) );
            }
        }
        public void Add(RaceEvent raceEvent, RaceEventCallbackHorse1ValuesSimulate callback)
        {
            EventHandlerHorse1ValuesSimulate handle;
            if(_handleDictHorse1ValuesSimulate.TryGetValue(raceEvent , out handle))
            {
                handle.Add(callback);
            }
            else
            {
                Debug.LogError( string.Format( "登録しようとしている raceEventとcallback の組み合わせが不正です。raceEvent={0}, callback={1}", raceEvent, callback ) );
            }
        }

        /// <summary>
        /// イベント削除
        /// </summary>
        public void Remove(RaceEvent raceEvent, RaceEventCallbackHorse1Simulate callback)
        {
            EventHandlerHorse1Simulate handle;
            if (_handleDictHorse1Simulate.TryGetValue(raceEvent, out handle))
            {
                handle.Remove(callback);
            }
        }
        public void Remove(RaceEvent raceEvent, RaceEventCallbackHorse1ValuesSimulate callback)
        {
            EventHandlerHorse1ValuesSimulate handle;
            if (_handleDictHorse1ValuesSimulate.TryGetValue(raceEvent, out handle))
            {
                handle.Remove(callback);
            }
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void UpdateSimulate()
        {
            _CheckUpdate();
        }

    }
#endif
#if GALLOP
    // 再生用Partialクラス
    public partial class RaceEventManager
    {
        //イベント時に呼ぶコールバック達
        Dictionary<StandaloneSimulator.RaceEvent, StandaloneSimulator.EventHandlerHorse1> _handleDictHorse1 = new Dictionary<StandaloneSimulator.RaceEvent, StandaloneSimulator.EventHandlerHorse1>();
        Dictionary<StandaloneSimulator.RaceEvent, StandaloneSimulator.EventHandlerHorse1Values> _handleDictHorse1Values = new Dictionary<StandaloneSimulator.RaceEvent, StandaloneSimulator.EventHandlerHorse1Values>();

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public RaceEventManager()
        {
            _handleDictHorse1 = new Dictionary<StandaloneSimulator.RaceEvent, StandaloneSimulator.EventHandlerHorse1>();
            _handleDictHorse1.Add(StandaloneSimulator.RaceEvent.Distance, new Gallop.EventDistance(Gallop.RaceManager.Instance));

            _handleDictHorse1Values = new Dictionary<StandaloneSimulator.RaceEvent, StandaloneSimulator.EventHandlerHorse1Values>();
            _handleDictHorse1Values.Add(StandaloneSimulator.RaceEvent.EventParam, new Gallop.EventCourseParam(Gallop.RaceManager.Instance));
        }
        
        /// <summary>
        /// 追加済みならtrue
        /// </summary>
        /// <param name="raceEvent"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        public bool IsExist(StandaloneSimulator.RaceEvent raceEvent, StandaloneSimulator.RaceEventCallbackHorse1 callback)
        {
            StandaloneSimulator.EventHandlerHorse1 handle;
            if (_handleDictHorse1.TryGetValue(raceEvent, out handle))
            {
                return handle.IsExist(callback);
            }
            return false;
        }
        public bool IsExist(StandaloneSimulator.RaceEvent raceEvent, StandaloneSimulator.RaceEventCallbackHorse1Values callback)
        {
            StandaloneSimulator.EventHandlerHorse1Values handle;
            if (_handleDictHorse1Values.TryGetValue(raceEvent, out handle))
            {
                return handle.IsExist(callback);
            }
            return false;
        }

        /// <summary>
        /// イベント追加
        /// </summary>
        /// <returns>追加できたらtrue</returns>
        public void Add(StandaloneSimulator.RaceEvent raceEvent, StandaloneSimulator.RaceEventCallbackHorse1 callback)
        {
            StandaloneSimulator.EventHandlerHorse1 handle;
            if(_handleDictHorse1.TryGetValue(raceEvent , out handle))
            {
                handle.Add(callback);
            }
            else
            {
                Debug.LogError( string.Format( "登録しようとしている raceEventとcallback の組み合わせが不正です。raceEvent={0}, callback={1}", raceEvent, callback ) );
            }
        }
        public void Add(StandaloneSimulator.RaceEvent raceEvent, StandaloneSimulator.RaceEventCallbackHorse1Values callback)
        {
            StandaloneSimulator.EventHandlerHorse1Values handle;
            if(_handleDictHorse1Values.TryGetValue(raceEvent , out handle))
            {
                handle.Add(callback);
            }
            else
            {
                Debug.LogError( string.Format( "登録しようとしている raceEventとcallback の組み合わせが不正です。raceEvent={0}, callback={1}", raceEvent, callback ) );
            }
        }

        /// <summary>
        /// イベント削除
        /// </summary>
        public void Remove(StandaloneSimulator.RaceEvent raceEvent, StandaloneSimulator.RaceEventCallbackHorse1 callback)
        {
            StandaloneSimulator.EventHandlerHorse1 handle;
            if (_handleDictHorse1.TryGetValue(raceEvent, out handle))
            {
                handle.Remove(callback);
            }
        }
        public void Remove(StandaloneSimulator.RaceEvent raceEvent, StandaloneSimulator.RaceEventCallbackHorse1Values callback)
        {
            StandaloneSimulator.EventHandlerHorse1Values handle;
            if (_handleDictHorse1Values.TryGetValue(raceEvent, out handle))
            {
                handle.Remove(callback);
            }
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void Update()
        {
            if (!Gallop.RaceManager.Instance) return;

            if (Gallop.RaceManager.Instance.State == Gallop.RaceDefine.RaceState.Race)
            {
                _CheckUpdate();
            }
        }
    }
#endif
}


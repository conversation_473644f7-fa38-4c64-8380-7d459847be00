#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceTrackAccessor
    {
        IRaceTrackAccessor Get(int id);
    }

    public interface IRaceTrackAccessor
    {
        int Id { get; }
        int InitialLaneType { get; }
        int EnableHalfGate { get; }
        int HorseNumGateVariation { get; }
        int TurfVisionType { get; }
        int FootsmokeColorType { get; }
    }

    public class MasterRaceTrackAccessor : IMasterRaceTrackAccessor
    {
        public IRaceTrackAccessor Get(int id)
        {
            var raceTrack = MasterDataManagerAccessor.Instance.masterRaceTrack.Get(id);
            if (raceTrack == null)
            {
                return null;
            }
            return new RaceTrackAccessor(raceTrack);
        }
    }
    
    public class RaceTrackAccessor : IRaceTrackAccessor
    {
        public int Id => _raceTrack.Id;
        public int InitialLaneType => _raceTrack.InitialLaneType;
        public int EnableHalfGate => _raceTrack.EnableHalfGate;
        public int HorseNumGateVariation => _raceTrack.HorseNumGateVariation;
        public int TurfVisionType => _raceTrack.TurfVisionType;
        public int FootsmokeColorType => _raceTrack.FootsmokeColorType;
#if GALLOP
        private readonly Gallop.MasterRaceTrack.RaceTrack _raceTrack;
        public RaceTrackAccessor(Gallop.MasterRaceTrack.RaceTrack raceTrack)
        {
            _raceTrack = raceTrack;
        }
#else
        private readonly MasterRaceTrack.RaceTrack _raceTrack;
        public RaceTrackAccessor(MasterRaceTrack.RaceTrack raceTrack)
        {
            _raceTrack = raceTrack;
        }
#endif
    }
}
#endif
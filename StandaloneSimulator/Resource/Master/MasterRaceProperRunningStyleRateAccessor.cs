#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceProperRunningStyleRateAccessor
    {
        IRaceProperRunningStyleRateAccessor Get(int id);
    }

    public interface IRaceProperRunningStyleRateAccessor
    {
        int Id { get; }
        int ProperRate { get; }
    }

    public class MasterRaceProperRunningStyleRateAccessor : IMasterRaceProperRunningStyleRateAccessor
    {
        public IRaceProperRunningStyleRateAccessor Get(int id)
        {
            var raceProperRunningStyleRate = MasterDataManagerAccessor.Instance.masterRaceProperRunningstyleRate.Get(id);
            if (raceProperRunningStyleRate == null)
            {
                return null;
            }
            return new RaceProperRunningStyleRateAccessor(raceProperRunningStyleRate);
        }
    }
    
    public class RaceProperRunningStyleRateAccessor : IRaceProperRunningStyleRateAccessor
    {
        public int Id => _raceProperRunningStyleRate.Id;
        public int ProperRate => _raceProperRunningStyleRate.ProperRate;
#if GALLOP
        private readonly Gallop.MasterRaceProperRunningstyleRate.RaceProperRunningstyleRate _raceProperRunningStyleRate;
        public RaceProperRunningStyleRateAccessor(Gallop.MasterRaceProperRunningstyleRate.RaceProperRunningstyleRate raceProperRunningStyleRate)
        {
            _raceProperRunningStyleRate = raceProperRunningStyleRate;
        }
#else
        private readonly MasterRaceProperRunningstyleRate.RaceProperRunningstyleRate _raceProperRunningStyleRate;
        public RaceProperRunningStyleRateAccessor(MasterRaceProperRunningstyleRate.RaceProperRunningstyleRate raceProperRunningStyleRate)
        {
            _raceProperRunningStyleRate = raceProperRunningStyleRate;
        }
#endif
    }
}
#endif

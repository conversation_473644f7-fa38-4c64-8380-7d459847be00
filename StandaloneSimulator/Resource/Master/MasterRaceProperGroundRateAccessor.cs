#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceProperGroundRateAccessor
    {
        IRaceProperGroundRateAccessor Get(int id);
    }

    public interface IRaceProperGroundRateAccessor
    {
        int Id { get; }
        int ProperRate { get; }
    }

    public class MasterRaceProperGroundRateAccessor : IMasterRaceProperGroundRateAccessor
    {
        public IRaceProperGroundRateAccessor Get(int id)
        {
            var raceProperGroundRate = MasterDataManagerAccessor.Instance.masterRaceProperGroundRate.Get(id);
            if (raceProperGroundRate == null)
            {
                return null;
            }
            return new RaceProperGroundRateAccessor(raceProperGroundRate);
        }
    }
    
    public class RaceProperGroundRateAccessor : IRaceProperGroundRateAccessor
    {
        public int Id => _raceProperGroundRate.Id;
        public int ProperRate => _raceProperGroundRate.ProperRate;
#if GALLOP
        private readonly Gallop.MasterRaceProperGroundRate.RaceProperGroundRate _raceProperGroundRate;
        public RaceProperGroundRateAccessor(Gallop.MasterRaceProperGroundRate.RaceProperGroundRate raceProperGroundRate)
        {
            _raceProperGroundRate = raceProperGroundRate;
        }
#else
        private readonly MasterRaceProperGroundRate.RaceProperGroundRate _raceProperGroundRate;
        public RaceProperGroundRateAccessor(MasterRaceProperGroundRate.RaceProperGroundRate raceProperGroundRate)
        {
            _raceProperGroundRate = raceProperGroundRate;
        }
#endif
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    public interface IMasterTeamStadiumScoreBonusAccessor
    {
        ITeamStadiumScoreBonusAccessor Get(int id);
        List<ITeamStadiumScoreBonusAccessor> GetListWithConditionTypeOrderByPriorityAsc(int conditionType);
    }

    public interface ITeamStadiumScoreBonusAccessor
    {
        int Id { get; }
        int Priority { get; }
        int ConditionType { get; }
        int ConditionValue1 { get; }
        int ConditionValue2 { get; }
        int ScoreRate { get; }
    }

    public class MasterTeamStadiumScoreBonusAccessor : IMasterTeamStadiumScoreBonusAccessor
    {
        public ITeamStadiumScoreBonusAccessor Get(int id)
        {
            var scoreBonus = MasterDataManagerAccessor.Instance.masterTeamStadiumScoreBonus.Get(id);
            if (scoreBonus == null)
            {
                return null;
            }
            return new TeamStadiumScoreBonusAccessor(scoreBonus);
        }

        public List<ITeamStadiumScoreBonusAccessor> GetListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            return MasterDataManagerAccessor.Instance.masterTeamStadiumScoreBonus
                .GetListWithConditionTypeOrderByPriorityAsc(conditionType)
                .Select(x => (ITeamStadiumScoreBonusAccessor)new TeamStadiumScoreBonusAccessor(x))
                .ToList();
        }
    }
    
    public class TeamStadiumScoreBonusAccessor : ITeamStadiumScoreBonusAccessor
    {
        public int Id => _teamStadiumScoreBonus.Id;
        public int Priority => _teamStadiumScoreBonus.Priority;
        public int ConditionType => _teamStadiumScoreBonus.ConditionType;
        public int ConditionValue1 => _teamStadiumScoreBonus.ConditionValue1;
        public int ConditionValue2 => _teamStadiumScoreBonus.ConditionValue2;
        public int ScoreRate => _teamStadiumScoreBonus.ScoreRate;
#if GALLOP
        private readonly Gallop.MasterTeamStadiumScoreBonus.TeamStadiumScoreBonus _teamStadiumScoreBonus;

        public TeamStadiumScoreBonusAccessor(Gallop.MasterTeamStadiumScoreBonus.TeamStadiumScoreBonus teamStadiumScoreBonus)
        {
            _teamStadiumScoreBonus = teamStadiumScoreBonus;
        }
#else
        private readonly MasterTeamStadiumScoreBonus.TeamStadiumScoreBonus _teamStadiumScoreBonus;

        public TeamStadiumScoreBonusAccessor(MasterTeamStadiumScoreBonus.TeamStadiumScoreBonus teamStadiumScoreBonus)
        {
            _teamStadiumScoreBonus = teamStadiumScoreBonus;
        }
#endif
    }
}
#endif

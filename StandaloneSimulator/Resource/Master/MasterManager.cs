#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// MasterDataManagerへのアクセッサ。
    /// </summary>
    /// <remarks>
    /// このクラスでGallop.MasterDataManagerとStandaloneSimulator.MasterDataManagerへのアクセスを切り替えることで、
    /// MasterDataManagerへアクセスしたい個所ではMasterDataManagerAccessor.Instanceとするだけで済む。
    /// </remarks>
    //-------------------------------------------------------------------
    public class MasterDataManagerAccessor
    {
#if GALLOP
        public static Gallop.MasterDataManager Instance => Gallop.MasterDataManager.Instance;
#else
        public static MasterDataManager Instance => MasterDataManager.Instance;
#endif
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// レースシミュレーターから参照するマスターデータアクセッサ。
    /// </summary>
    //-------------------------------------------------------------------
    public class MasterManager : Singleton<MasterManager>
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public IMasterRaceInstanceAccessor MasterRaceInstance { get; private set; }
        public IMasterRaceAccessor MasterRace { get; private set; }
        public IMasterRaceTrackAccessor MasterRaceTrack { get; private set; }
        public IMasterRaceCourseSetAccessor MasterRaceCourseSet { get; private set; }
        public IMasterRaceCourseSetStatusAccessor MasterRaceCourseSetStatus { get; private set; }
        public IMasterRaceMotivationRateAccessor MasterRaceMotivationRate { get; private set; }
        public IMasterRaceProperDistanceRateAccessor MasterRaceProperDistanceRate { get; private set; }
        public IMasterRaceProperGroundRateAccessor MasterRaceProperGroundRate { get; private set; }
        public IMasterRaceProperRunningStyleRateAccessor MasterRaceProperRunningStyleRate { get; private set; }
        public IMasterSkillDataAccessor MasterSkillData { get; private set; }
        public IMasterSkillLevelValueAccessor MasterSkillLevelValue { get; private set; }
        public IMasterTeamStadiumRawScoreAccessor MasterTeamStadiumRawScore { get; private set; }
        public IMasterTeamStadiumScoreBonusAccessor MasterTeamStadiumScoreBonus { get; private set; }
        public IMasterItemDataAccessor MasterItemData { get; private set; }
        public IMasterChallengeMatchRawPointAccessor MasterChallengeMatchRawPoint { get; private set; }
        public IMasterRaceSingleModeTeamStatusAccessor MasterRaceSingleModeTeamStatus { get; private set; }

        private bool _isInitialized = false;


        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// 初期化。
        /// </summary>
        public void Init()
        {
            if (_isInitialized)
            {
                return;
            }
            
            MasterRaceInstance = new MasterRaceInstanceAccessor();
            MasterRace = new MasterRaceAccessor();
            MasterRaceTrack = new MasterRaceTrackAccessor();
            MasterRaceCourseSet = new MasterRaceCourseSetAccessor();
            MasterRaceCourseSetStatus = new MasterRaceCourseSetStatusAccessor();
            MasterRaceMotivationRate = new MasterRaceMotivationRateAccessor();
            MasterRaceProperDistanceRate = new MasterRaceProperDistanceRateAccessor();
            MasterRaceProperGroundRate = new MasterRaceProperGroundRateAccessor();
            MasterRaceProperRunningStyleRate = new MasterRaceProperRunningStyleRateAccessor();
            MasterSkillData = new MasterSkillDataAccessor();
            MasterSkillLevelValue = new MasterSkillLevelValueAccessor();
            MasterTeamStadiumRawScore = new MasterTeamStadiumRawScoreAccessor();
            MasterTeamStadiumScoreBonus = new MasterTeamStadiumScoreBonusAccessor();
            MasterItemData = new MasterItemDataAccessor();
            MasterChallengeMatchRawPoint = new MasterChallengeMatchRawPointAccessor();
            MasterRaceSingleModeTeamStatus = new MasterRaceSingleModeTeamStatusAccessor();

            _isInitialized = true;
        }
    }
}

#endif

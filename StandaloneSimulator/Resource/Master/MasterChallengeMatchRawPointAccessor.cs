#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public interface IMasterChallengeMatchRawPointAccessor
    {
        IChallengeMatchRawPointAccessor Get(int id);
        List<IChallengeMatchRawPointAccessor> GetListWithConditionTypeOrderByPriorityAsc(int conditionType);
    }

    public interface IChallengeMatchRawPointAccessor
    {
        int Id { get; }
        int Priority { get; }
        int ConditionType { get; }
        int ConditionValue1 { get; }
        int ConditionValue2 { get; }
        int Point { get; }
        int RacePointNameId { get; }
        int SortOrder { get; }
    }

    public class MasterChallengeMatchRawPointAccessor : IMasterChallengeMatchRawPointAccessor
    {
        public IChallengeMatchRawPointAccessor Get(int id)
        {
            var master = MasterDataManagerAccessor.Instance.masterChallengeMatchRawPoint.Get(id);
            if (master == null)
            {
                return null;
            }
            return new ChallengeMatchRawPointAccessor(master);
        }
        
        public List<IChallengeMatchRawPointAccessor> GetListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            return MasterDataManagerAccessor.Instance.masterChallengeMatchRawPoint
                .GetListWithConditionTypeOrderByPriorityAsc(conditionType)
                .Select(x => (IChallengeMatchRawPointAccessor)new ChallengeMatchRawPointAccessor(x))
                .ToList();
        }
    }

    public class ChallengeMatchRawPointAccessor : IChallengeMatchRawPointAccessor
    {
        public int Id => _master.Id;
        public int Priority => _master.Priority;
        public int ConditionType => _master.ConditionType;
        public int ConditionValue1 => _master.ConditionValue1;
        public int ConditionValue2 => _master.ConditionValue2;
        public int Point => _master.Point;
        public int RacePointNameId => _master.RacePointNameId;
        public int SortOrder => _master.SortOrder;

#if GALLOP
        private readonly Gallop.MasterChallengeMatchRawPoint.ChallengeMatchRawPoint _master;
        public ChallengeMatchRawPointAccessor(Gallop.MasterChallengeMatchRawPoint.ChallengeMatchRawPoint master)
        {
            _master = master;
        }
#else
        private readonly MasterChallengeMatchRawPoint.ChallengeMatchRawPoint _master;
        public ChallengeMatchRawPointAccessor(MasterChallengeMatchRawPoint.ChallengeMatchRawPoint master)
        {
            _master = master;
        }
#endif
    }
}

#endif

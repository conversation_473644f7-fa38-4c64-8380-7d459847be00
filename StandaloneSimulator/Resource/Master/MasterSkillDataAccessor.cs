using System.Collections.Generic;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterSkillDataAccessor
    {
        ISkillDataAccessor Get(int id);
    }

    public interface ISkillDataAccessor
    {
        int Id { get; }
        int Rarity { get; }
        int GroupId { get; }
        int GroupRate { get; }
        int FilterSwitch { get; }
        int GradeValue { get; }
        int SkillCategory { get; }
        string TagId { get; }
        int UniqueSkillId1 { get; }
        int UniqueSkillId2 { get; }
        int ExpType { get; }
        int PotentialPerDefault { get; }
        int ActivateLot { get; }
        int Priority { get; }
        string Precondition1 { get; }
        string Condition1 { get; }
        int FloatAbilityTime1 { get; }
        int AbilityTimeUsage1 { get; }
        int FloatCooldownTime1 { get; }
        int AbilityType11 { get; }
        int AbilityValueUsage11 { get; }
        int AbilityValueLevelUsage11 { get; }
        int AdditionalActivateType11 { get; }
        int FloatAbilityValue11 { get; }
        int TargetType11 { get; }
        int TargetValue11 { get; }
        int AbilityType12 { get; }
        int AbilityValueUsage12 { get; }
        int AbilityValueLevelUsage12 { get; }
        int AdditionalActivateType12 { get; }
        int FloatAbilityValue12 { get; }
        int TargetType12 { get; }
        int TargetValue12 { get; }
        int AbilityType13 { get; }
        int AbilityValueUsage13 { get; }
        int AbilityValueLevelUsage13 { get; }
        int AdditionalActivateType13 { get; }
        int FloatAbilityValue13 { get; }
        int TargetType13 { get; }
        int TargetValue13 { get; }
        string Precondition2 { get; }
        string Condition2 { get; }
        int FloatAbilityTime2 { get; }
        int AbilityTimeUsage2 { get; }
        int FloatCooldownTime2 { get; }
        int AbilityType21 { get; }
        int AbilityValueUsage21 { get; }
        int AbilityValueLevelUsage21 { get; }
        int AdditionalActivateType21 { get; }
        int FloatAbilityValue21 { get; }
        int TargetType21 { get; }
        int TargetValue21 { get; }
        int AbilityType22 { get; }
        int AbilityValueUsage22 { get; }
        int AbilityValueLevelUsage22 { get; }
        int AdditionalActivateType22 { get; }
        int FloatAbilityValue22 { get; }
        int TargetType22 { get; }
        int TargetValue22 { get; }
        int AbilityType23 { get; }
        int AbilityValueUsage23 { get; }
        int AbilityValueLevelUsage23 { get; }
        int AdditionalActivateType23 { get; }
        int FloatAbilityValue23 { get; }
        int TargetType23 { get; }
        int TargetValue23 { get; }
        int PopularityAddParam1 { get; }
        int PopularityAddValue1 { get; }
        int PopularityAddParam2 { get; }
        int PopularityAddValue2 { get; }
        int DispOrder { get; }
        int IconId { get; }
        int IsGeneralSkill { get; }
        List<int> GetTagIds();
    }

    public class MasterSkillDataAccessor : IMasterSkillDataAccessor
    {
        public ISkillDataAccessor Get(int id)
        {
            var skillData = MasterDataManagerAccessor.Instance.masterSkillData.Get(id);
            if (skillData == null)
            {
                return null;
            }
            return new SkillDataAccessor(skillData);
        }
    }
    
    public class SkillDataAccessor : ISkillDataAccessor
    {
        public int Id => _skillData.Id;
        public int Rarity => _skillData.Rarity;
        public int GroupId => _skillData.GroupId;
        public int GroupRate => _skillData.GroupRate;
        public int FilterSwitch => _skillData.FilterSwitch;
        public int GradeValue => _skillData.GradeValue;
        public int SkillCategory => _skillData.SkillCategory;
        public string TagId => _skillData.TagId;
        public int UniqueSkillId1 => _skillData.UniqueSkillId1;
        public int UniqueSkillId2 => _skillData.UniqueSkillId2;
        public int ExpType => _skillData.ExpType;
        public int PotentialPerDefault => _skillData.PotentialPerDefault;
        public int ActivateLot => _skillData.ActivateLot;
        public int Priority => _skillData.Priority;
        public string Precondition1 => _skillData.Precondition1;
        public string Condition1 => _skillData.Condition1;
        public int AbilityTimeUsage1 => _skillData.AbilityTimeUsage1;
        public int FloatAbilityTime1 => _skillData.FloatAbilityTime1;
        public int FloatCooldownTime1 => _skillData.FloatCooldownTime1;
        public int AbilityType11 => _skillData.AbilityType11;
        public int AbilityValueUsage11 => _skillData.AbilityValueUsage11;
        public int AbilityValueLevelUsage11 => _skillData.AbilityValueLevelUsage11;
        public int AdditionalActivateType11 => _skillData.AdditionalActivateType11;
        public int FloatAbilityValue11 => _skillData.FloatAbilityValue11;
        public int TargetType11 => _skillData.TargetType11;
        public int TargetValue11 => _skillData.TargetValue11;
        public int AbilityType12 => _skillData.AbilityType12;
        public int AbilityValueUsage12 => _skillData.AbilityValueUsage12;
        public int AbilityValueLevelUsage12 => _skillData.AbilityValueLevelUsage12;
        public int AdditionalActivateType12 => _skillData.AdditionalActivateType12;
        public int FloatAbilityValue12 => _skillData.FloatAbilityValue12;
        public int TargetType12 => _skillData.TargetType12;
        public int TargetValue12 => _skillData.TargetValue12;
        public int AbilityType13 => _skillData.AbilityType13;
        public int AbilityValueUsage13 => _skillData.AbilityValueUsage13;
        public int AbilityValueLevelUsage13 => _skillData.AbilityValueLevelUsage13;
        public int AdditionalActivateType13 => _skillData.AdditionalActivateType13;
        public int FloatAbilityValue13 => _skillData.FloatAbilityValue13;
        public int TargetType13 => _skillData.TargetType13;
        public int TargetValue13 => _skillData.TargetValue13;
        public string Precondition2 => _skillData.Precondition2;
        public string Condition2 => _skillData.Condition2;
        public int AbilityTimeUsage2 => _skillData.AbilityTimeUsage2;
        public int FloatAbilityTime2 => _skillData.FloatAbilityTime2;
        public int FloatCooldownTime2 => _skillData.FloatCooldownTime2;
        public int AbilityType21 => _skillData.AbilityType21;
        public int AbilityValueUsage21 => _skillData.AbilityValueUsage21;
        public int AbilityValueLevelUsage21 => _skillData.AbilityValueLevelUsage21;
        public int AdditionalActivateType21 => _skillData.AdditionalActivateType21;
        public int FloatAbilityValue21 => _skillData.FloatAbilityValue21;
        public int TargetType21 => _skillData.TargetType21;
        public int TargetValue21 => _skillData.TargetValue21;
        public int AbilityType22 => _skillData.AbilityType22;
        public int AbilityValueUsage22 => _skillData.AbilityValueUsage22;
        public int AbilityValueLevelUsage22 => _skillData.AbilityValueLevelUsage22;
        public int AdditionalActivateType22 => _skillData.AdditionalActivateType22;
        public int FloatAbilityValue22 => _skillData.FloatAbilityValue22;
        public int TargetType22 => _skillData.TargetType22;
        public int TargetValue22 => _skillData.TargetValue22;
        public int AbilityType23 => _skillData.AbilityType23;
        public int AbilityValueUsage23 => _skillData.AbilityValueUsage23;
        public int AbilityValueLevelUsage23 => _skillData.AbilityValueLevelUsage23;
        public int AdditionalActivateType23 => _skillData.AdditionalActivateType23;
        public int FloatAbilityValue23 => _skillData.FloatAbilityValue23;
        public int TargetType23 => _skillData.TargetType23;
        public int TargetValue23 => _skillData.TargetValue23;
        public int PopularityAddParam1 => _skillData.PopularityAddParam1;
        public int PopularityAddValue1 => _skillData.PopularityAddValue1;
        public int PopularityAddParam2 => _skillData.PopularityAddParam2;
        public int PopularityAddValue2 => _skillData.PopularityAddValue2;
        public int DispOrder => _skillData.DispOrder;
        public int IconId => _skillData.IconId;
        public int IsGeneralSkill => _skillData.IsGeneralSkill;
        public List<int> GetTagIds()
        {
            return _skillData.GetTagIds();
        }

#if GALLOP
        private readonly Gallop.MasterSkillData.SkillData _skillData;
        public SkillDataAccessor(Gallop.MasterSkillData.SkillData skillData)
        {
            _skillData = skillData;
        }
#else
        private readonly MasterSkillData.SkillData _skillData;
        public SkillDataAccessor(MasterSkillData.SkillData skillData)
        {
            _skillData = skillData;
        }
#endif
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public interface IMasterSkillLevelValueAccessor
    {
        ISkillLevelValueAccessor Get(int id);
        List<ISkillLevelValueAccessor> GetListWithAbilityTypeOrderByLevelAsc(int abilityType);
    }

    public interface ISkillLevelValueAccessor
    {
        int Id { get; }
        int AbilityType { get; }
        int Level { get; }
        int FloatAbilityValueCoef { get; }
    }

    public class MasterSkillLevelValueAccessor : IMasterSkillLevelValueAccessor
    {
        public ISkillLevelValueAccessor Get(int id)
        {
            var skillLevelValue = MasterDataManagerAccessor.Instance.masterSkillLevelValue.Get(id);
            if (skillLevelValue == null)
            {
                return null;
            }
            return new SkillLevelValueAccessor(skillLevelValue);
        }
        public List<ISkillLevelValueAccessor> GetListWithAbilityTypeOrderByLevelAsc(int conditionType)
        {
            return MasterDataManagerAccessor.Instance.masterSkillLevelValue
                .GetListWithAbilityTypeOrderByLevelAsc(conditionType)
                .Select(x => (ISkillLevelValueAccessor)new SkillLevelValueAccessor(x))
                .ToList();
        }
    }
    
    public class SkillLevelValueAccessor : ISkillLevelValueAccessor
    {
        public int Id => _skillLevelValue.Id;
        public int AbilityType => _skillLevelValue.AbilityType;
        public int Level => _skillLevelValue.Level;
        public int FloatAbilityValueCoef => _skillLevelValue.FloatAbilityValueCoef;
#if GALLOP
        private readonly Gallop.MasterSkillLevelValue.SkillLevelValue _skillLevelValue;

        public SkillLevelValueAccessor(Gallop.MasterSkillLevelValue.SkillLevelValue skillLevelValue)
        {
            _skillLevelValue = skillLevelValue;
        }
#else
        private readonly MasterSkillLevelValue.SkillLevelValue _skillLevelValue;

        public SkillLevelValueAccessor(MasterSkillLevelValue.SkillLevelValue skillLevelValue)
        {
            _skillLevelValue = skillLevelValue;
        }
#endif
    }
}
#endif

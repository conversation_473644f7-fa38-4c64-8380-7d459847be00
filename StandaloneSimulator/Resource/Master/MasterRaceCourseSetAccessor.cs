#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceCourseSetAccessor
    {
        IRaceCourseSetAccessor Get(int raceInstanceId);
    }

    public interface IRaceCourseSetAccessor
    {
        int Id { get; }
        int RaceTrackId { get; }
        int Distance { get; }
        int Ground { get; }
        int Inout { get; }
        int Turn { get; }
        bool RunOutSide { get; }
        int FenceSet { get; }
        int FloatLaneMax { get; }
        int CourseSetStatusId { get; }
        int FinishTimeMin { get; }
        int FinishTimeMinRandomRange { get; }
        int FinishTimeMax { get; }
        int FinishTimeMaxRandomRange { get; }
    }

    public class MasterRaceCourseSetAccessor : IMasterRaceCourseSetAccessor
    {
        public IRaceCourseSetAccessor Get(int RaceCourseSetId)
        {
            var raceCourseSet = MasterDataManagerAccessor.Instance.masterRaceCourseSet.Get(RaceCourseSetId);
            if (raceCourseSet == null)
            {
                return null;
            }
            return new RaceCourseSetAccessor(raceCourseSet);
        }
    }
    
    public class RaceCourseSetAccessor : IRaceCourseSetAccessor
    {
        public int Id => _raceCourseSet.Id;
        public int RaceTrackId => _raceCourseSet.RaceTrackId;
        public int Distance => _raceCourseSet.Distance;
        public int Ground => _raceCourseSet.Ground;
        public int Inout => _raceCourseSet.Inout;
        public int Turn => _raceCourseSet.Turn;
        public bool RunOutSide => _raceCourseSet.RunOutside;
        public int FenceSet => _raceCourseSet.FenceSet;
        public int FloatLaneMax => _raceCourseSet.FloatLaneMax;
        public int CourseSetStatusId => _raceCourseSet.CourseSetStatusId;
        public int FinishTimeMin => _raceCourseSet.FinishTimeMin;
        public int FinishTimeMinRandomRange => _raceCourseSet.FinishTimeMinRandomRange;
        public int FinishTimeMax => _raceCourseSet.FinishTimeMax;
        public int FinishTimeMaxRandomRange => _raceCourseSet.FinishTimeMaxRandomRange;

#if GALLOP
        private readonly Gallop.MasterRaceCourseSet.RaceCourseSet _raceCourseSet;
        public RaceCourseSetAccessor(Gallop.MasterRaceCourseSet.RaceCourseSet raceCourseSet)
        {
            _raceCourseSet = raceCourseSet;
        }
#else
        private readonly MasterRaceCourseSet.RaceCourseSet _raceCourseSet;
        public RaceCourseSetAccessor(MasterRaceCourseSet.RaceCourseSet raceCourseSet)
        {
            _raceCourseSet = raceCourseSet;
        }
#endif
    }
}
#endif
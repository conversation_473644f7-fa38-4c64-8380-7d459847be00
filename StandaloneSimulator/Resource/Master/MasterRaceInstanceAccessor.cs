#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceInstanceAccessor
    {
        IRaceInstanceAccessor Get(int raceInstanceId);
    }

    public interface IRaceInstanceAccessor
    {
        int Id { get; }
        int RaceId { get; }
        int NpcGroupId { get; }
        int Date { get; }
        int Time { get; }
        int RaceNumber { get; }

        IRaceCourseSetAccessor GetRaceCourseSetMaster();
    }

    public class MasterRaceInstanceAccessor : IMasterRaceInstanceAccessor
    {
        public IRaceInstanceAccessor Get(int raceInstanceId)
        {
            var raceInstance = MasterDataManagerAccessor.Instance.masterRaceInstance.Get(raceInstanceId);
            if (raceInstance == null)
            {
                return null;
            }
            return new RaceInstanceAccessor(raceInstance);
        }
    }
    
    public class RaceInstanceAccessor : IRaceInstanceAccessor
    {
        public int Id => _raceInstance.Id;
        public int RaceId => _raceInstance.RaceId;
        public int NpcGroupId => _raceInstance.NpcGroupId;
        public int Date => _raceInstance.Date;
        public int Time => _raceInstance.Time;
        public int RaceNumber => _raceInstance.RaceNumber;
#if GALLOP
        private readonly Gallop.MasterRaceInstance.RaceInstance _raceInstance;
        public RaceInstanceAccessor(Gallop.MasterRaceInstance.RaceInstance raceInstance)
        {
            _raceInstance = raceInstance;
        }
#else
        private readonly MasterRaceInstance.RaceInstance _raceInstance;
        public RaceInstanceAccessor(MasterRaceInstance.RaceInstance raceInstance)
        {
            _raceInstance = raceInstance;
        }
#endif

        public IRaceCourseSetAccessor GetRaceCourseSetMaster()
        {
            var masterRace = MasterDataManagerAccessor.Instance.masterRace.Get(RaceId);
            if (masterRace == null)
            {
                return null;
            }

            var masterCourseSet = MasterDataManagerAccessor.Instance.masterRaceCourseSet.Get(masterRace.CourseSet);
            return new RaceCourseSetAccessor(masterCourseSet);
        }
    }
}
#endif
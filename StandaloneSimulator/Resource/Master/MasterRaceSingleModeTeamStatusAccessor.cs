#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    public interface IMasterRaceSingleModeTeamStatusAccessor
    {
        IRaceSingleModeTeamStatusAccessor Get(int id);
        IRaceSingleModeTeamStatusAccessor[] GetAllOrderByTeamRankThreshold();
    }

    public interface IRaceSingleModeTeamStatusAccessor
    {
        int Id { get; }
        int TeamRankThreshold { get; }
        int AddStatus { get; }
    }

    public class MasterRaceSingleModeTeamStatusAccessor : IMasterRaceSingleModeTeamStatusAccessor
    {
        public IRaceSingleModeTeamStatusAccessor Get(int id)
        {
            var teamStatus = MasterDataManagerAccessor.Instance.masterRaceSingleModeTeamStatus.Get(id);
            if (teamStatus == null)
            {
                return null;
            }
            return new RaceSingleModeTeamStatusAccessor(teamStatus);
        }
        
        /// <remarks>
        /// dictionaryの全要素でRaceSingleModeTeamStatusAccessorを生成して配列で返すのでGCが発生する。多用は避けること。
        /// </remarks>
        public IRaceSingleModeTeamStatusAccessor[] GetAllOrderByTeamRankThreshold()
        {
            return MasterDataManagerAccessor.Instance.masterRaceSingleModeTeamStatus.dictionary.Values
                .OrderBy(x => x.TeamRankThreshold)
                .Select(x => new RaceSingleModeTeamStatusAccessor(x))
                .ToArray();
        }
    }
    
    public class RaceSingleModeTeamStatusAccessor : IRaceSingleModeTeamStatusAccessor
    {
        public int Id => _teamStatus.Id;
        public int TeamRankThreshold => _teamStatus.TeamRankThreshold;
        public int AddStatus => _teamStatus.AddStatus;
#if GALLOP
        private readonly Gallop.MasterRaceSingleModeTeamStatus.RaceSingleModeTeamStatus _teamStatus;
        public RaceSingleModeTeamStatusAccessor(Gallop.MasterRaceSingleModeTeamStatus.RaceSingleModeTeamStatus teamStatus)
        {
            _teamStatus = teamStatus;
        }
#else
        private readonly MasterRaceSingleModeTeamStatus.RaceSingleModeTeamStatus _teamStatus;
        public RaceSingleModeTeamStatusAccessor(MasterRaceSingleModeTeamStatus.RaceSingleModeTeamStatus teamStatus)
        {
            _teamStatus = teamStatus;
        }
#endif
    }
}

#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceProperDistanceRateAccessor
    {
        IRaceProperDistanceRateAccessor Get(int id);
    }

    public interface IRaceProperDistanceRateAccessor
    {
        int Id { get; }
        int ProperRateSpeed { get; }
        int ProperRatePower { get; }
    }

    public class MasterRaceProperDistanceRateAccessor : IMasterRaceProperDistanceRateAccessor
    {
        public IRaceProperDistanceRateAccessor Get(int id)
        {
            var raceProperDistanceRate = MasterDataManagerAccessor.Instance.masterRaceProperDistanceRate.Get(id);
            if (raceProperDistanceRate == null)
            {
                return null;
            }
            return new RaceProperDistanceRateAccessor(raceProperDistanceRate);
        }
    }
    
    public class RaceProperDistanceRateAccessor : IRaceProperDistanceRateAccessor
    {
        public int Id => _raceProperDistanceRate.Id;
        public int ProperRateSpeed => _raceProperDistanceRate.ProperRateSpeed;
        public int ProperRatePower => _raceProperDistanceRate.ProperRatePower;
#if GALLOP
        private readonly Gallop.MasterRaceProperDistanceRate.RaceProperDistanceRate _raceProperDistanceRate;
        public RaceProperDistanceRateAccessor(Gallop.MasterRaceProperDistanceRate.RaceProperDistanceRate raceProperDistanceRate)
        {
            _raceProperDistanceRate = raceProperDistanceRate;
        }
#else
        private readonly MasterRaceProperDistanceRate.RaceProperDistanceRate _raceProperDistanceRate;
        public RaceProperDistanceRateAccessor(MasterRaceProperDistanceRate.RaceProperDistanceRate raceProperDistanceRate)
        {
            _raceProperDistanceRate = raceProperDistanceRate;
        }
#endif
    }
}
#endif

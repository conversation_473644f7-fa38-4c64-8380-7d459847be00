#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterItemDataAccessor
    {
        IItemDataAccessor Get(int id);
    }

    public interface IItemDataAccessor
    {
        int Id { get; }
        int ItemCategory { get; }
        int GroupId { get; }
        int EffectType1 { get; }
        int EffectTarget1 { get; }
        int EffectValue1 { get; }
        int EffectType2 { get; }
        int EffectTarget2 { get; }
        int EffectValue2 { get; }
        int AddValue1 { get; }
        int AddValue2 { get; }
        int AddValue3 { get; }
        int LimitNum { get; }
        int Sort { get; }
        int Rare { get; }
        int EnableRequest { get; }
        int RequestReward { get; }
        string StartDate { get; }
        string EndDate { get; }
    }

    public class MasterItemDataAccessor : IMasterItemDataAccessor
    {
        public IItemDataAccessor Get(int id)
        {
            var itemData = MasterDataManagerAccessor.Instance.masterItemData.Get(id);
            if (itemData == null)
            {
                return null;
            }
            return new ItemDataAccessor(itemData);
        }
    }

    public class ItemDataAccessor : IItemDataAccessor
    {
        public int Id => _itemData.Id;
        public int ItemCategory => _itemData.ItemCategory;
        public int GroupId => _itemData.GroupId;
        public int EffectType1 => _itemData.EffectType1;
        public int EffectTarget1 => _itemData.EffectTarget1;
        public int EffectValue1 => _itemData.EffectValue1;
        public int EffectType2 => _itemData.EffectType2;
        public int EffectTarget2 => _itemData.EffectTarget2;
        public int EffectValue2 => _itemData.EffectValue2;
        public int AddValue1 => _itemData.AddValue1;
        public int AddValue2 => _itemData.AddValue2;
        public int AddValue3 => _itemData.AddValue3;
        public int LimitNum => _itemData.LimitNum;
        public int Sort => _itemData.Sort;
        public int Rare => _itemData.Rare;
        public int EnableRequest => _itemData.EnableRequest;
        public int RequestReward => _itemData.RequestReward;
        public string StartDate => _itemData.StartDate;
        public string EndDate => _itemData.EndDate;

#if GALLOP
        private readonly Gallop.MasterItemData.ItemData _itemData;
        public ItemDataAccessor(Gallop.MasterItemData.ItemData itemData)
        {
            _itemData = itemData;
        }
#else
        private readonly MasterItemData.ItemData _itemData;
        public ItemDataAccessor(MasterItemData.ItemData itemData)
        {
            _itemData = itemData;
        }
#endif
    }
}
#endif

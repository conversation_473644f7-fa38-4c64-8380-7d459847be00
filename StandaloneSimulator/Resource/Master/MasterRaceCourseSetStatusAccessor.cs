#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceCourseSetStatusAccessor
    {
        IRaceCourseSetStatusAccessor Get(int raceInstanceId);
    }

    public interface IRaceCourseSetStatusAccessor
    {
        int CourseSetStatusId { get; }
        int TargetStatus1 { get; }
        int TargetStatus2 { get; }
    }

    public class MasterRaceCourseSetStatusAccessor : IMasterRaceCourseSetStatusAccessor
    {
        public IRaceCourseSetStatusAccessor Get(int RaceCourseSetStatusId)
        {
            var raceCourseSetStatus = MasterDataManagerAccessor.Instance.masterRaceCourseSetStatus.Get(RaceCourseSetStatusId);
            if (raceCourseSetStatus == null)
            {
                return null;
            }
            return new RaceCourseSetStatusAccessor(raceCourseSetStatus);
        }
    }
    
    public class RaceCourseSetStatusAccessor : IRaceCourseSetStatusAccessor
    {
        public int CourseSetStatusId => _raceCourseSetStatus.CourseSetStatusId;
        public int TargetStatus1 => _raceCourseSetStatus.TargetStatus1;
        public int TargetStatus2 => _raceCourseSetStatus.TargetStatus2;

#if GALLOP
        private readonly Gallop.MasterRaceCourseSetStatus.RaceCourseSetStatus _raceCourseSetStatus;
        public RaceCourseSetStatusAccessor(Gallop.MasterRaceCourseSetStatus.RaceCourseSetStatus raceCourseSetStatus)
        {
            _raceCourseSetStatus = raceCourseSetStatus;
        }
#else
        private readonly MasterRaceCourseSetStatus.RaceCourseSetStatus _raceCourseSetStatus;
        public RaceCourseSetStatusAccessor(MasterRaceCourseSetStatus.RaceCourseSetStatus raceCourseSetStatus)
        {
            _raceCourseSetStatus = raceCourseSetStatus;
        }
#endif
    }
}
#endif

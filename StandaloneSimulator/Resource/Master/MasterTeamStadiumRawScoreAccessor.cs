#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public interface IMasterTeamStadiumRawScoreAccessor
    {
        ITeamStadiumRawScoreAccessor Get(int id);
        
        List<ITeamStadiumRawScoreAccessor> GetListWithConditionTypeOrderByPriorityAsc(int conditionType);
    }

    public interface ITeamStadiumRawScoreAccessor
    {
        int Id { get; }
        int Priority { get; }
        int ConditionType { get; }
        int ConditionValue1 { get; }
        int ConditionValue2 { get; }
        int Score { get; }
        int RaceScoreNameId { get; }
        int SortOrder { get; }
    }

    public class MasterTeamStadiumRawScoreAccessor : IMasterTeamStadiumRawScoreAccessor
    {
        public ITeamStadiumRawScoreAccessor Get(int id)
        {
            var rawScore = MasterDataManagerAccessor.Instance.masterTeamStadiumRawScore.Get(id);
            if (rawScore == null)
            {
                return null;
            }
            return new TeamStadiumRawScoreAccessor(rawScore);
        }

        public List<ITeamStadiumRawScoreAccessor> GetListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            return MasterDataManagerAccessor.Instance.masterTeamStadiumRawScore
                .GetListWithConditionTypeOrderByPriorityAsc(conditionType)
                .Select(x => (ITeamStadiumRawScoreAccessor)new TeamStadiumRawScoreAccessor(x))
                .ToList();
        }
    }
    
    public class TeamStadiumRawScoreAccessor : ITeamStadiumRawScoreAccessor
    {
        public int Id => _teamStadiumRawScore.Id;
        public int Priority => _teamStadiumRawScore.Priority;
        public int ConditionType => _teamStadiumRawScore.ConditionType;
        public int ConditionValue1 => _teamStadiumRawScore.ConditionValue1;
        public int ConditionValue2 => _teamStadiumRawScore.ConditionValue2;
        public int Score => _teamStadiumRawScore.Score;
        public int RaceScoreNameId => _teamStadiumRawScore.RaceScoreNameId;
        public int SortOrder => _teamStadiumRawScore.SortOrder;
#if GALLOP
        private readonly Gallop.MasterTeamStadiumRawScore.TeamStadiumRawScore _teamStadiumRawScore;

        public TeamStadiumRawScoreAccessor(Gallop.MasterTeamStadiumRawScore.TeamStadiumRawScore teamStadiumRawScore)
        {
            _teamStadiumRawScore = teamStadiumRawScore;
        }
#else
        private readonly MasterTeamStadiumRawScore.TeamStadiumRawScore _teamStadiumRawScore;

        public TeamStadiumRawScoreAccessor(MasterTeamStadiumRawScore.TeamStadiumRawScore teamStadiumRawScore)
        {
            _teamStadiumRawScore = teamStadiumRawScore;
        }
#endif
    }
}
#endif

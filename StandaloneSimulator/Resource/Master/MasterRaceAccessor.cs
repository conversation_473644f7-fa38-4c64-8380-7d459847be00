#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceAccessor
    {
        IRaceAccessor Get(int raceInstanceId);
    }

    public interface IRaceAccessor
    {
        int Id { get; }
        int Group { get; }
        int Grade { get; }
        int CourseSet { get; }
        int ThumbnailId { get; }
        string FfCueName { get; }
        string FfCuesheetName { get; }
        int FfAnim { get; }
        int FfCamera { get; }
        int FfCameraSub { get; }
        int FfSub { get; }
        int GoalGate { get; }
        int GoalFlower { get; }
        int EntryNum { get; }
        int IsDirtgrade { get; }

        string RaceName { get; }
    }

    public class MasterRaceAccessor : IMasterRaceAccessor
    {
        public IRaceAccessor Get(int raceId)
        {
            var race = MasterDataManagerAccessor.Instance.masterRace.Get(raceId);
            if (race == null)
            {
                return null;
            }
            return new RaceAccessor(race);
        }
    }
    
    public class RaceAccessor : IRaceAccessor
    {
        public int Id => _race.Id;
        public int Group => _race.Group;
        public int Grade => _race.Grade;
        public int CourseSet => _race.CourseSet;
        public int ThumbnailId => _race.ThumbnailId;
        public string FfCueName => _race.FfCueName;
        public string FfCuesheetName => _race.FfCuesheetName;
        public int FfAnim => _race.FfAnim;
        public int FfCamera => _race.FfCamera;
        public int FfCameraSub => _race.FfCameraSub;
        public int FfSub => _race.FfSub;
        public int GoalGate => _race.GoalGate;
        public int GoalFlower => _race.GoalFlower;
        public int EntryNum => _race.EntryNum;
        public int IsDirtgrade => _race.IsDirtgrade;

#if GALLOP
        public string RaceName => _race.RaceName;
        private readonly Gallop.MasterRace.Race _race;
        public RaceAccessor(Gallop.MasterRace.Race race)
        {
            _race = race;
        }
#else
        public string RaceName => "";
        private readonly MasterRace.Race _race;
        public RaceAccessor(MasterRace.Race race)
        {
            _race = race;
        }
#endif
    }
}
#endif
#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public interface IMasterRaceMotivationRateAccessor
    {
        IRaceMotivationRateAccessor Get(int id);
    }

    public interface IRaceMotivationRateAccessor
    {
        int Id { get; }
        int MotivationRate { get; }
    }

    public class MasterRaceMotivationRateAccessor : IMasterRaceMotivationRateAccessor
    {
        public IRaceMotivationRateAccessor Get(int id)
        {
            var motivationRate = MasterDataManagerAccessor.Instance.masterRaceMotivationRate.Get(id);
            if (motivationRate == null)
            {
                return null;
            }
            return new RaceMotivationRateAccessor(motivationRate);
        }
    }
    
    public class RaceMotivationRateAccessor : IRaceMotivationRateAccessor
    {
        public int Id => _motivationRate.Id;
        public int MotivationRate => _motivationRate.MotivationRate;
#if GALLOP
        private readonly Gallop.MasterRaceMotivationRate.RaceMotivationRate _motivationRate;
        public RaceMotivationRateAccessor(Gallop.MasterRaceMotivationRate.RaceMotivationRate motivationRate)
        {
            _motivationRate = motivationRate;
        }
#else
        private readonly MasterRaceMotivationRate.RaceMotivationRate _motivationRate;
        public RaceMotivationRateAccessor(MasterRaceMotivationRate.RaceMotivationRate motivationRate)
        {
            _motivationRate = motivationRate;
        }
#endif
    }
}
#endif

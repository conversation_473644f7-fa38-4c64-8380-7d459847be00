using System;
using System.Text;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// yamlファイルの読み込み。
    /// </summary>
    //-------------------------------------------------------------------
    public static class YamlLoadHelper
    {
#if STANDALONE_SIMULATOR
        /// <summary>
        /// yaml読込。
        /// </summary>
        public static T LoadYaml<T>(string path) where T : IYamlLoadable
        {
            if (!File.Exists(path))
            {
                Debug.LogError($"Yaml file not found. path={path}");
            }

            T ret;
            using (var sr = new StreamReader(path, Encoding.UTF8))
            {
                while (true)
                {
                    var line = sr.ReadLine();

                    // "MonoBehaviour:"の行になったら目当てのコンポーネントと見なす。クラスId=114でフックした方がいいかも。
                    // https://docs.unity3d.com/ja/current/Manual/ClassIDReference.html
                    if (line.Contains("MonoBehaviour:"))
                    {
                        break;
                    }
                }

                // raceParamDefineのアセット側に定義がある状態でC#側に定義が無いとエラーになってしまい一度追加したraceParamDefineのパラメータを消せなくなってしまっていたので
                // C#側で定義が無かったらスキップするよう対応
                var ds = new YamlDotNet.Serialization.DeserializerBuilder().IgnoreUnmatchedProperties().Build();
                ret = ds.Deserialize<T>(sr);
                ret.LoadPostProcess();
            }
            return ret;
        }
#endif

        /// <summary>
        /// 16進文字列をint配列へ。
        /// </summary>
        public static int[] Hex2Int(string hexStr)
        {
            if (string.IsNullOrEmpty(hexStr))
            {
                return new int[0];
            }

            const int ELEMENT_LENGTH = 8;
            Debug.Assert(hexStr.Length % ELEMENT_LENGTH == 0);
            int[] retArray = new int[hexStr.Length / ELEMENT_LENGTH];
            var strBuf = new StringBuilder(ELEMENT_LENGTH);
            strBuf.Length = ELEMENT_LENGTH;
            for (int i = 0; i < retArray.Length; ++i)
            {
                int index = i * ELEMENT_LENGTH;
                strBuf[0] = hexStr[index + 6];
                strBuf[1] = hexStr[index + 7];
                strBuf[2] = hexStr[index + 4];
                strBuf[3] = hexStr[index + 5];
                strBuf[4] = hexStr[index + 2];
                strBuf[5] = hexStr[index + 3];
                strBuf[6] = hexStr[index + 0];
                strBuf[7] = hexStr[index + 1];
                retArray[i] = Convert.ToInt32(strBuf.ToString(), 16);
            }

            return retArray;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// yamlとして読み込み、デシリアライズ可能なクラスのインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IYamlLoadable
    {
        /// <summary>
        /// yaml読み込み後に呼び出される。
        /// </summary>
        void LoadPostProcess();
    }
}

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: challenge_match
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public class MasterChallengeMatchDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterChallengeMatchData masterChallengeMatchData         { get; private set; }
        public MasterChallengeMatchRace masterChallengeMatchRace         { get; private set; }
        public MasterChallengeMatchBossNpc masterChallengeMatchBossNpc   { get; private set; }
        public MasterChallengeMatchRawPoint masterChallengeMatchRawPoint { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChallengeMatchData     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChallengeMatchRace     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChallengeMatchBossNpc  = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChallengeMatchRawPoint = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_challengeMatchRace_challengeMatchId    = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_challengeMatchRace_raceInstanceId      = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_challengeMatchRawPoint_conditionType   = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_challengeMatchRawPoint_racePointNameId = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterChallengeMatchDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterChallengeMatchData     = new MasterChallengeMatchData(this);
            this.masterChallengeMatchRace     = new MasterChallengeMatchRace(this);
            this.masterChallengeMatchBossNpc  = new MasterChallengeMatchBossNpc(this);
            this.masterChallengeMatchRawPoint = new MasterChallengeMatchRawPoint(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet challenge_match database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterChallengeMatchData != null) { _selectQuery_masterChallengeMatchData.Dispose(); _selectQuery_masterChallengeMatchData = null; }
            if (_selectQuery_masterChallengeMatchRace != null) { _selectQuery_masterChallengeMatchRace.Dispose(); _selectQuery_masterChallengeMatchRace = null; }
            if (_indexedSelectQuery_challengeMatchRace_challengeMatchId != null) { _indexedSelectQuery_challengeMatchRace_challengeMatchId.Dispose(); _indexedSelectQuery_challengeMatchRace_challengeMatchId = null; }
            if (_indexedSelectQuery_challengeMatchRace_raceInstanceId != null) { _indexedSelectQuery_challengeMatchRace_raceInstanceId.Dispose(); _indexedSelectQuery_challengeMatchRace_raceInstanceId = null; }
            if (_selectQuery_masterChallengeMatchBossNpc != null) { _selectQuery_masterChallengeMatchBossNpc.Dispose(); _selectQuery_masterChallengeMatchBossNpc = null; }
            if (_selectQuery_masterChallengeMatchRawPoint != null) { _selectQuery_masterChallengeMatchRawPoint.Dispose(); _selectQuery_masterChallengeMatchRawPoint = null; }
            if (_indexedSelectQuery_challengeMatchRawPoint_conditionType != null) { _indexedSelectQuery_challengeMatchRawPoint_conditionType.Dispose(); _indexedSelectQuery_challengeMatchRawPoint_conditionType = null; }
            if (_indexedSelectQuery_challengeMatchRawPoint_racePointNameId != null) { _indexedSelectQuery_challengeMatchRawPoint_racePointNameId.Dispose(); _indexedSelectQuery_challengeMatchRawPoint_racePointNameId = null; }
            if (this.masterChallengeMatchData != null) { this.masterChallengeMatchData.Unload(); }
            if (this.masterChallengeMatchRace != null) { this.masterChallengeMatchRace.Unload(); }
            if (this.masterChallengeMatchBossNpc != null) { this.masterChallengeMatchBossNpc.Unload(); }
            if (this.masterChallengeMatchRawPoint != null) { this.masterChallengeMatchRawPoint.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for challenge_match/challenge_match_data
        
        /// <summary>
        /// SELECT `announce_id`,`bonus_skill_id`,`bonus_factor_id`,`item_exchange_top_id`,`challenge_match_top_bg_id`,`challenge_match_top_bg_sub_id`,`notice_date`,`start_date`,`ending_date`,`end_date` FROM `challenge_match_data` WHERE `challenge_match_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChallengeMatchData()
        {
            if (_selectQuery_masterChallengeMatchData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChallengeMatchData = connection.PreparedQuery("SELECT `announce_id`,`bonus_skill_id`,`bonus_factor_id`,`item_exchange_top_id`,`challenge_match_top_bg_id`,`challenge_match_top_bg_sub_id`,`notice_date`,`start_date`,`ending_date`,`end_date` FROM `challenge_match_data` WHERE `challenge_match_id`=?;");
            }
        
            return _selectQuery_masterChallengeMatchData;
        }
        
        /// <summary>
        /// SELECT `challenge_match_id`,`announce_id`,`bonus_skill_id`,`bonus_factor_id`,`item_exchange_top_id`,`challenge_match_top_bg_id`,`challenge_match_top_bg_sub_id`,`notice_date`,`start_date`,`ending_date`,`end_date` FROM `challenge_match_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChallengeMatchData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `challenge_match_id`,`announce_id`,`bonus_skill_id`,`bonus_factor_id`,`item_exchange_top_id`,`challenge_match_top_bg_id`,`challenge_match_top_bg_sub_id`,`notice_date`,`start_date`,`ending_date`,`end_date` FROM `challenge_match_data`;");
        }
        
        // SQL statements for challenge_match/challenge_match_race
        
        /// <summary>
        /// SELECT `challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChallengeMatchRace()
        {
            if (_selectQuery_masterChallengeMatchRace == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChallengeMatchRace = connection.PreparedQuery("SELECT `challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChallengeMatchRace;
        }
        
        /// <summary>
        /// SELECT `id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `challenge_match_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChallengeMatchRace_ChallengeMatchId()
        {
            if (_indexedSelectQuery_challengeMatchRace_challengeMatchId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_challengeMatchRace_challengeMatchId = connection.PreparedQuery("SELECT `id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `challenge_match_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_challengeMatchRace_challengeMatchId;
        }
        
        /// <summary>
        /// SELECT `id`,`challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChallengeMatchRace_RaceInstanceId()
        {
            if (_indexedSelectQuery_challengeMatchRace_raceInstanceId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_challengeMatchRace_raceInstanceId = connection.PreparedQuery("SELECT `id`,`challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_challengeMatchRace_raceInstanceId;
        }
        
        /// <summary>
        /// SELECT `id`,`challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChallengeMatchRace()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race`;");
        }
        
        // SQL statements for challenge_match/challenge_match_boss_npc
        
        /// <summary>
        /// SELECT `chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `challenge_match_boss_npc` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChallengeMatchBossNpc()
        {
            if (_selectQuery_masterChallengeMatchBossNpc == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChallengeMatchBossNpc = connection.PreparedQuery("SELECT `chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `challenge_match_boss_npc` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChallengeMatchBossNpc;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `challenge_match_boss_npc`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChallengeMatchBossNpc()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `challenge_match_boss_npc`;");
        }
        
        // SQL statements for challenge_match/challenge_match_raw_point
        
        /// <summary>
        /// SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChallengeMatchRawPoint()
        {
            if (_selectQuery_masterChallengeMatchRawPoint == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChallengeMatchRawPoint = connection.PreparedQuery("SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChallengeMatchRawPoint;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point` WHERE `condition_type`=? ORDER BY `priority` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChallengeMatchRawPoint_ConditionType()
        {
            if (_indexedSelectQuery_challengeMatchRawPoint_conditionType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_challengeMatchRawPoint_conditionType = connection.PreparedQuery("SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point` WHERE `condition_type`=? ORDER BY `priority` ASC;");
            }
        
            return _indexedSelectQuery_challengeMatchRawPoint_conditionType;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`sort_order` FROM `challenge_match_raw_point` WHERE `race_point_name_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChallengeMatchRawPoint_RacePointNameId()
        {
            if (_indexedSelectQuery_challengeMatchRawPoint_racePointNameId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_challengeMatchRawPoint_racePointNameId = connection.PreparedQuery("SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`sort_order` FROM `challenge_match_raw_point` WHERE `race_point_name_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_challengeMatchRawPoint_racePointNameId;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChallengeMatchRawPoint()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point`;");
        }
    }
}
#endif
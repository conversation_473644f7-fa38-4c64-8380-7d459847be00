// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: challenge_match/challenge_match_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterChallengeMatchData : AbstractMasterData
    {
        public const string TABLE_NAME = "challenge_match_data";

        MasterChallengeMatchDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ChallengeMatchData> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, ChallengeMatchData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterChallengeMatchData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterChallengeMatchData(MasterChallengeMatchDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ChallengeMatchData>();
            _db = db;
        }


        public ChallengeMatchData Get(int challengeMatchId)
        {
            int key = (int)challengeMatchId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterChallengeMatchData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(challengeMatchId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterChallengeMatchData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ChallengeMatchData _SelectOne(int challengeMatchId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ChallengeMatchData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchData");
                return null;
            }

            // SELECT `announce_id`,`bonus_skill_id`,`bonus_factor_id`,`item_exchange_top_id`,`challenge_match_top_bg_id`,`challenge_match_top_bg_sub_id`,`notice_date`,`start_date`,`ending_date`,`end_date` FROM `challenge_match_data` WHERE `challenge_match_id`=?;
            if (!query.BindInt(1, challengeMatchId)) { return null; }

            ChallengeMatchData orm = null;

            if (query.Step())
            {
                int announceId               = (int)query.GetInt(0);
                int bonusSkillId             = (int)query.GetInt(1);
                int bonusFactorId            = (int)query.GetInt(2);
                int itemExchangeTopId        = (int)query.GetInt(3);
                int challengeMatchTopBgId    = (int)query.GetInt(4);
                int challengeMatchTopBgSubId = (int)query.GetInt(5);
                long noticeDate              = (long)query.GetLong(6);
                long startDate               = (long)query.GetLong(7);
                long endingDate              = (long)query.GetLong(8);
                long endDate                 = (long)query.GetLong(9);

                orm = new ChallengeMatchData(challengeMatchId, announceId, bonusSkillId, bonusFactorId, itemExchangeTopId, challengeMatchTopBgId, challengeMatchTopBgSubId, noticeDate, startDate, endingDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", challengeMatchId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ChallengeMatchData()) {
                while (query.Step()) {
                    int challengeMatchId         = (int)query.GetInt(0);
                    int announceId               = (int)query.GetInt(1);
                    int bonusSkillId             = (int)query.GetInt(2);
                    int bonusFactorId            = (int)query.GetInt(3);
                    int itemExchangeTopId        = (int)query.GetInt(4);
                    int challengeMatchTopBgId    = (int)query.GetInt(5);
                    int challengeMatchTopBgSubId = (int)query.GetInt(6);
                    long noticeDate              = (long)query.GetLong(7);
                    long startDate               = (long)query.GetLong(8);
                    long endingDate              = (long)query.GetLong(9);
                    long endDate                 = (long)query.GetLong(10);

                    int key = (int)challengeMatchId;
                    ChallengeMatchData orm = new ChallengeMatchData(challengeMatchId, announceId, bonusSkillId, bonusFactorId, itemExchangeTopId, challengeMatchTopBgId, challengeMatchTopBgSubId, noticeDate, startDate, endingDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class ChallengeMatchData
        {
            /// <summary> (CSV column: challenge_match_id) </summary>
            public readonly int ChallengeMatchId;
            /// <summary> (CSV column: announce_id) </summary>
            public readonly int AnnounceId;
            /// <summary> (CSV column: bonus_skill_id) </summary>
            public readonly int BonusSkillId;
            /// <summary> (CSV column: bonus_factor_id) </summary>
            public readonly int BonusFactorId;
            /// <summary> (CSV column: item_exchange_top_id) </summary>
            public readonly int ItemExchangeTopId;
            /// <summary> (CSV column: challenge_match_top_bg_id) </summary>
            public readonly int ChallengeMatchTopBgId;
            /// <summary> (CSV column: challenge_match_top_bg_sub_id) </summary>
            public readonly int ChallengeMatchTopBgSubId;
            /// <summary> (CSV column: notice_date) </summary>
            public readonly long NoticeDate;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: ending_date) </summary>
            public readonly long EndingDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ChallengeMatchData(int challengeMatchId = 0, int announceId = 0, int bonusSkillId = 0, int bonusFactorId = 0, int itemExchangeTopId = 0, int challengeMatchTopBgId = 0, int challengeMatchTopBgSubId = 0, long noticeDate = 0, long startDate = 0, long endingDate = 0, long endDate = 0)
            {
                this.ChallengeMatchId         = challengeMatchId;
                this.AnnounceId               = announceId;
                this.BonusSkillId             = bonusSkillId;
                this.BonusFactorId            = bonusFactorId;
                this.ItemExchangeTopId        = itemExchangeTopId;
                this.ChallengeMatchTopBgId    = challengeMatchTopBgId;
                this.ChallengeMatchTopBgSubId = challengeMatchTopBgSubId;
                this.NoticeDate               = noticeDate;
                this.StartDate                = startDate;
                this.EndingDate               = endingDate;
                this.EndDate                  = endDate;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: challenge_match/challenge_match_raw_point
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:condition_type], [:race_point_name_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterChallengeMatchRawPoint : AbstractMasterData
    {
        public const string TABLE_NAME = "challenge_match_raw_point";

        MasterChallengeMatchDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ChallengeMatchRawPoint> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ChallengeMatchRawPoint>> _dictionaryWithConditionType = null;
        private Dictionary<int, List<ChallengeMatchRawPoint>> _dictionaryWithRacePointNameId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, ChallengeMatchRawPoint> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterChallengeMatchRawPoint");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterChallengeMatchRawPoint(MasterChallengeMatchDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ChallengeMatchRawPoint>();
            _dictionaryWithConditionType = new Dictionary<int, List<ChallengeMatchRawPoint>>();
            _dictionaryWithRacePointNameId = new Dictionary<int, List<ChallengeMatchRawPoint>>();
            _db = db;
        }


        public ChallengeMatchRawPoint Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterChallengeMatchRawPoint");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterChallengeMatchRawPoint", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ChallengeMatchRawPoint _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ChallengeMatchRawPoint();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRawPoint");
                return null;
            }

            // SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            ChallengeMatchRawPoint orm = null;

            if (query.Step())
            {
                int priority        = (int)query.GetInt(0);
                int conditionType   = (int)query.GetInt(1);
                int conditionValue1 = (int)query.GetInt(2);
                int conditionValue2 = (int)query.GetInt(3);
                int point           = (int)query.GetInt(4);
                int racePointNameId = (int)query.GetInt(5);
                int sortOrder       = (int)query.GetInt(6);

                orm = new ChallengeMatchRawPoint(id, priority, conditionType, conditionValue1, conditionValue2, point, racePointNameId, sortOrder);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public ChallengeMatchRawPoint GetWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            ChallengeMatchRawPoint orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionTypeOrderByPriorityAsc(conditionType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ChallengeMatchRawPoint _SelectWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRawPoint_ConditionType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRawPoint");
                return null;
            }

            // SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point` WHERE `condition_type`=? ORDER BY `priority` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            ChallengeMatchRawPoint orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(query, conditionType);
            }

            query.Reset();

            return orm;
        }

        public List<ChallengeMatchRawPoint> GetListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            int key = (int)conditionType;

            if (!_dictionaryWithConditionType.ContainsKey(key)) {
                _dictionaryWithConditionType.Add(key, _ListSelectWithConditionTypeOrderByPriorityAsc(conditionType));
            }

            return _dictionaryWithConditionType[key];
        }

        public List<ChallengeMatchRawPoint> MaybeListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            List<ChallengeMatchRawPoint> list = GetListWithConditionTypeOrderByPriorityAsc(conditionType);
            return list.Count > 0 ? list : null;
        }

        private List<ChallengeMatchRawPoint> _ListSelectWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            List<ChallengeMatchRawPoint> _list = new List<ChallengeMatchRawPoint>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRawPoint_ConditionType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRawPoint");
                return null;
            }

            // SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`point`,`race_point_name_id`,`sort_order` FROM `challenge_match_raw_point` WHERE `condition_type`=? ORDER BY `priority` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            while (query.Step()) {
                ChallengeMatchRawPoint orm = _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(query, conditionType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ChallengeMatchRawPoint _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(LibNative.Sqlite3.PreparedQuery query, int conditionType)
        {
            int id              = (int)query.GetInt(0);
            int priority        = (int)query.GetInt(1);
            int conditionValue1 = (int)query.GetInt(2);
            int conditionValue2 = (int)query.GetInt(3);
            int point           = (int)query.GetInt(4);
            int racePointNameId = (int)query.GetInt(5);
            int sortOrder       = (int)query.GetInt(6);

            return new ChallengeMatchRawPoint(id, priority, conditionType, conditionValue1, conditionValue2, point, racePointNameId, sortOrder);
        }

        public ChallengeMatchRawPoint GetWithRacePointNameIdOrderByIdAsc(int racePointNameId)
        {
            ChallengeMatchRawPoint orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRacePointNameIdOrderByIdAsc(racePointNameId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", racePointNameId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ChallengeMatchRawPoint _SelectWithRacePointNameIdOrderByIdAsc(int racePointNameId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRawPoint_RacePointNameId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRawPoint");
                return null;
            }

            // SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`sort_order` FROM `challenge_match_raw_point` WHERE `race_point_name_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, racePointNameId)) { return null; }

            ChallengeMatchRawPoint orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRacePointNameIdOrderByIdAsc(query, racePointNameId);
            }

            query.Reset();

            return orm;
        }

        public List<ChallengeMatchRawPoint> GetListWithRacePointNameIdOrderByIdAsc(int racePointNameId)
        {
            int key = (int)racePointNameId;

            if (!_dictionaryWithRacePointNameId.ContainsKey(key)) {
                _dictionaryWithRacePointNameId.Add(key, _ListSelectWithRacePointNameIdOrderByIdAsc(racePointNameId));
            }

            return _dictionaryWithRacePointNameId[key];
        }

        public List<ChallengeMatchRawPoint> MaybeListWithRacePointNameIdOrderByIdAsc(int racePointNameId)
        {
            List<ChallengeMatchRawPoint> list = GetListWithRacePointNameIdOrderByIdAsc(racePointNameId);
            return list.Count > 0 ? list : null;
        }

        private List<ChallengeMatchRawPoint> _ListSelectWithRacePointNameIdOrderByIdAsc(int racePointNameId)
        {
            List<ChallengeMatchRawPoint> _list = new List<ChallengeMatchRawPoint>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRawPoint_RacePointNameId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRawPoint");
                return null;
            }

            // SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`point`,`sort_order` FROM `challenge_match_raw_point` WHERE `race_point_name_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, racePointNameId)) { return null; }

            while (query.Step()) {
                ChallengeMatchRawPoint orm = _CreateOrmByQueryResultWithRacePointNameIdOrderByIdAsc(query, racePointNameId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ChallengeMatchRawPoint _CreateOrmByQueryResultWithRacePointNameIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int racePointNameId)
        {
            int id              = (int)query.GetInt(0);
            int priority        = (int)query.GetInt(1);
            int conditionType   = (int)query.GetInt(2);
            int conditionValue1 = (int)query.GetInt(3);
            int conditionValue2 = (int)query.GetInt(4);
            int point           = (int)query.GetInt(5);
            int sortOrder       = (int)query.GetInt(6);

            return new ChallengeMatchRawPoint(id, priority, conditionType, conditionValue1, conditionValue2, point, racePointNameId, sortOrder);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithConditionType.Clear();
            _dictionaryWithRacePointNameId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ChallengeMatchRawPoint()) {
                while (query.Step()) {
                    int id              = (int)query.GetInt(0);
                    int priority        = (int)query.GetInt(1);
                    int conditionType   = (int)query.GetInt(2);
                    int conditionValue1 = (int)query.GetInt(3);
                    int conditionValue2 = (int)query.GetInt(4);
                    int point           = (int)query.GetInt(5);
                    int racePointNameId = (int)query.GetInt(6);
                    int sortOrder       = (int)query.GetInt(7);

                    int key = (int)id;
                    ChallengeMatchRawPoint orm = new ChallengeMatchRawPoint(id, priority, conditionType, conditionValue1, conditionValue2, point, racePointNameId, sortOrder);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class ChallengeMatchRawPoint
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: condition_value_1) </summary>
            public readonly int ConditionValue1;
            /// <summary> (CSV column: condition_value_2) </summary>
            public readonly int ConditionValue2;
            /// <summary> (CSV column: point) </summary>
            public readonly int Point;
            /// <summary> (CSV column: race_point_name_id) </summary>
            public readonly int RacePointNameId;
            /// <summary> (CSV column: sort_order) </summary>
            public readonly int SortOrder;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ChallengeMatchRawPoint(int id = 0, int priority = 0, int conditionType = 0, int conditionValue1 = 0, int conditionValue2 = 0, int point = 0, int racePointNameId = 0, int sortOrder = 0)
            {
                this.Id              = id;
                this.Priority        = priority;
                this.ConditionType   = conditionType;
                this.ConditionValue1 = conditionValue1;
                this.ConditionValue2 = conditionValue2;
                this.Point           = point;
                this.RacePointNameId = racePointNameId;
                this.SortOrder       = sortOrder;
            }
        }
    }
}
#endif

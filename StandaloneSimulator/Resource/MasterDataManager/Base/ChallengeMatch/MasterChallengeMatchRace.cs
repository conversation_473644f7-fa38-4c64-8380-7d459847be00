// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: challenge_match/challenge_match_race
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:challenge_match_id], [:race_instance_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterChallengeMatchRace : AbstractMasterData
    {
        public const string TABLE_NAME = "challenge_match_race";

        MasterChallengeMatchDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ChallengeMatchRace> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ChallengeMatchRace>> _dictionaryWithChallengeMatchId = null;
        private Dictionary<int, List<ChallengeMatchRace>> _dictionaryWithRaceInstanceId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, ChallengeMatchRace> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterChallengeMatchRace");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterChallengeMatchRace(MasterChallengeMatchDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ChallengeMatchRace>();
            _dictionaryWithChallengeMatchId = new Dictionary<int, List<ChallengeMatchRace>>();
            _dictionaryWithRaceInstanceId = new Dictionary<int, List<ChallengeMatchRace>>();
            _db = db;
        }


        public ChallengeMatchRace Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterChallengeMatchRace");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterChallengeMatchRace", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ChallengeMatchRace _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ChallengeMatchRace();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRace");
                return null;
            }

            // SELECT `challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            ChallengeMatchRace orm = null;

            if (query.Step())
            {
                int challengeMatchId            = (int)query.GetInt(0);
                int dispOrder                   = (int)query.GetInt(1);
                int challengeMatchBgId          = (int)query.GetInt(2);
                int challengeMatchBgSubId       = (int)query.GetInt(3);
                int difficulty                  = (int)query.GetInt(4);
                int raceType                    = (int)query.GetInt(5);
                int raceAppearanceRate          = (int)query.GetInt(6);
                int raceInstanceId              = (int)query.GetInt(7);
                int season                      = (int)query.GetInt(8);
                int weather                     = (int)query.GetInt(9);
                int ground                      = (int)query.GetInt(10);
                int challengeMatchRaceBossNpcId = (int)query.GetInt(11);
                int costRp                      = (int)query.GetInt(12);
                int dropRewardOddsId            = (int)query.GetInt(13);
                int victoryRewardOddsId         = (int)query.GetInt(14);
                int firstClearItemCategory1     = (int)query.GetInt(15);
                int firstClearItemId1           = (int)query.GetInt(16);
                int firstClearItemNum1          = (int)query.GetInt(17);
                int firstClearItemCategory2     = (int)query.GetInt(18);
                int firstClearItemId2           = (int)query.GetInt(19);
                int firstClearItemNum2          = (int)query.GetInt(20);
                int firstClearItemCategory3     = (int)query.GetInt(21);
                int firstClearItemId3           = (int)query.GetInt(22);
                int firstClearItemNum3          = (int)query.GetInt(23);
                int pickUpItemCategory1         = (int)query.GetInt(24);
                int pickUpItemId1               = (int)query.GetInt(25);
                int pickUpItemNum1              = (int)query.GetInt(26);
                int pickUpItemCategory2         = (int)query.GetInt(27);
                int pickUpItemId2               = (int)query.GetInt(28);
                int pickUpItemNum2              = (int)query.GetInt(29);
                int pickUpItemCategory3         = (int)query.GetInt(30);
                int pickUpItemId3               = (int)query.GetInt(31);
                int pickUpItemNum3              = (int)query.GetInt(32);
                int pickUpItemCategory4         = (int)query.GetInt(33);
                int pickUpItemId4               = (int)query.GetInt(34);
                int pickUpItemNum4              = (int)query.GetInt(35);
                int pickUpItemCategory5         = (int)query.GetInt(36);
                int pickUpItemId5               = (int)query.GetInt(37);
                int pickUpItemNum5              = (int)query.GetInt(38);

                orm = new ChallengeMatchRace(id, challengeMatchId, dispOrder, challengeMatchBgId, challengeMatchBgSubId, difficulty, raceType, raceAppearanceRate, raceInstanceId, season, weather, ground, challengeMatchRaceBossNpcId, costRp, dropRewardOddsId, victoryRewardOddsId, firstClearItemCategory1, firstClearItemId1, firstClearItemNum1, firstClearItemCategory2, firstClearItemId2, firstClearItemNum2, firstClearItemCategory3, firstClearItemId3, firstClearItemNum3, pickUpItemCategory1, pickUpItemId1, pickUpItemNum1, pickUpItemCategory2, pickUpItemId2, pickUpItemNum2, pickUpItemCategory3, pickUpItemId3, pickUpItemNum3, pickUpItemCategory4, pickUpItemId4, pickUpItemNum4, pickUpItemCategory5, pickUpItemId5, pickUpItemNum5);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public ChallengeMatchRace GetWithChallengeMatchIdOrderByIdAsc(int challengeMatchId)
        {
            ChallengeMatchRace orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithChallengeMatchIdOrderByIdAsc(challengeMatchId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", challengeMatchId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ChallengeMatchRace _SelectWithChallengeMatchIdOrderByIdAsc(int challengeMatchId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRace_ChallengeMatchId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRace");
                return null;
            }

            // SELECT `id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `challenge_match_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, challengeMatchId)) { return null; }

            ChallengeMatchRace orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithChallengeMatchIdOrderByIdAsc(query, challengeMatchId);
            }

            query.Reset();

            return orm;
        }

        public List<ChallengeMatchRace> GetListWithChallengeMatchIdOrderByIdAsc(int challengeMatchId)
        {
            int key = (int)challengeMatchId;

            if (!_dictionaryWithChallengeMatchId.ContainsKey(key)) {
                _dictionaryWithChallengeMatchId.Add(key, _ListSelectWithChallengeMatchIdOrderByIdAsc(challengeMatchId));
            }

            return _dictionaryWithChallengeMatchId[key];
        }

        public List<ChallengeMatchRace> MaybeListWithChallengeMatchIdOrderByIdAsc(int challengeMatchId)
        {
            List<ChallengeMatchRace> list = GetListWithChallengeMatchIdOrderByIdAsc(challengeMatchId);
            return list.Count > 0 ? list : null;
        }

        private List<ChallengeMatchRace> _ListSelectWithChallengeMatchIdOrderByIdAsc(int challengeMatchId)
        {
            List<ChallengeMatchRace> _list = new List<ChallengeMatchRace>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRace_ChallengeMatchId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRace");
                return null;
            }

            // SELECT `id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`race_instance_id`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `challenge_match_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, challengeMatchId)) { return null; }

            while (query.Step()) {
                ChallengeMatchRace orm = _CreateOrmByQueryResultWithChallengeMatchIdOrderByIdAsc(query, challengeMatchId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ChallengeMatchRace _CreateOrmByQueryResultWithChallengeMatchIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int challengeMatchId)
        {
            int id                          = (int)query.GetInt(0);
            int dispOrder                   = (int)query.GetInt(1);
            int challengeMatchBgId          = (int)query.GetInt(2);
            int challengeMatchBgSubId       = (int)query.GetInt(3);
            int difficulty                  = (int)query.GetInt(4);
            int raceType                    = (int)query.GetInt(5);
            int raceAppearanceRate          = (int)query.GetInt(6);
            int raceInstanceId              = (int)query.GetInt(7);
            int season                      = (int)query.GetInt(8);
            int weather                     = (int)query.GetInt(9);
            int ground                      = (int)query.GetInt(10);
            int challengeMatchRaceBossNpcId = (int)query.GetInt(11);
            int costRp                      = (int)query.GetInt(12);
            int dropRewardOddsId            = (int)query.GetInt(13);
            int victoryRewardOddsId         = (int)query.GetInt(14);
            int firstClearItemCategory1     = (int)query.GetInt(15);
            int firstClearItemId1           = (int)query.GetInt(16);
            int firstClearItemNum1          = (int)query.GetInt(17);
            int firstClearItemCategory2     = (int)query.GetInt(18);
            int firstClearItemId2           = (int)query.GetInt(19);
            int firstClearItemNum2          = (int)query.GetInt(20);
            int firstClearItemCategory3     = (int)query.GetInt(21);
            int firstClearItemId3           = (int)query.GetInt(22);
            int firstClearItemNum3          = (int)query.GetInt(23);
            int pickUpItemCategory1         = (int)query.GetInt(24);
            int pickUpItemId1               = (int)query.GetInt(25);
            int pickUpItemNum1              = (int)query.GetInt(26);
            int pickUpItemCategory2         = (int)query.GetInt(27);
            int pickUpItemId2               = (int)query.GetInt(28);
            int pickUpItemNum2              = (int)query.GetInt(29);
            int pickUpItemCategory3         = (int)query.GetInt(30);
            int pickUpItemId3               = (int)query.GetInt(31);
            int pickUpItemNum3              = (int)query.GetInt(32);
            int pickUpItemCategory4         = (int)query.GetInt(33);
            int pickUpItemId4               = (int)query.GetInt(34);
            int pickUpItemNum4              = (int)query.GetInt(35);
            int pickUpItemCategory5         = (int)query.GetInt(36);
            int pickUpItemId5               = (int)query.GetInt(37);
            int pickUpItemNum5              = (int)query.GetInt(38);

            return new ChallengeMatchRace(id, challengeMatchId, dispOrder, challengeMatchBgId, challengeMatchBgSubId, difficulty, raceType, raceAppearanceRate, raceInstanceId, season, weather, ground, challengeMatchRaceBossNpcId, costRp, dropRewardOddsId, victoryRewardOddsId, firstClearItemCategory1, firstClearItemId1, firstClearItemNum1, firstClearItemCategory2, firstClearItemId2, firstClearItemNum2, firstClearItemCategory3, firstClearItemId3, firstClearItemNum3, pickUpItemCategory1, pickUpItemId1, pickUpItemNum1, pickUpItemCategory2, pickUpItemId2, pickUpItemNum2, pickUpItemCategory3, pickUpItemId3, pickUpItemNum3, pickUpItemCategory4, pickUpItemId4, pickUpItemNum4, pickUpItemCategory5, pickUpItemId5, pickUpItemNum5);
        }

        public ChallengeMatchRace GetWithRaceInstanceIdOrderByIdAsc(int raceInstanceId)
        {
            ChallengeMatchRace orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceInstanceIdOrderByIdAsc(raceInstanceId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceInstanceId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ChallengeMatchRace _SelectWithRaceInstanceIdOrderByIdAsc(int raceInstanceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRace_RaceInstanceId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRace");
                return null;
            }

            // SELECT `id`,`challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceInstanceId)) { return null; }

            ChallengeMatchRace orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceInstanceIdOrderByIdAsc(query, raceInstanceId);
            }

            query.Reset();

            return orm;
        }

        public List<ChallengeMatchRace> GetListWithRaceInstanceIdOrderByIdAsc(int raceInstanceId)
        {
            int key = (int)raceInstanceId;

            if (!_dictionaryWithRaceInstanceId.ContainsKey(key)) {
                _dictionaryWithRaceInstanceId.Add(key, _ListSelectWithRaceInstanceIdOrderByIdAsc(raceInstanceId));
            }

            return _dictionaryWithRaceInstanceId[key];
        }

        public List<ChallengeMatchRace> MaybeListWithRaceInstanceIdOrderByIdAsc(int raceInstanceId)
        {
            List<ChallengeMatchRace> list = GetListWithRaceInstanceIdOrderByIdAsc(raceInstanceId);
            return list.Count > 0 ? list : null;
        }

        private List<ChallengeMatchRace> _ListSelectWithRaceInstanceIdOrderByIdAsc(int raceInstanceId)
        {
            List<ChallengeMatchRace> _list = new List<ChallengeMatchRace>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChallengeMatchRace_RaceInstanceId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ChallengeMatchRace");
                return null;
            }

            // SELECT `id`,`challenge_match_id`,`disp_order`,`challenge_match_bg_id`,`challenge_match_bg_sub_id`,`difficulty`,`race_type`,`race_appearance_rate`,`season`,`weather`,`ground`,`challenge_match_race_boss_npc_id`,`cost_rp`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`pick_up_item_category_4`,`pick_up_item_id_4`,`pick_up_item_num_4`,`pick_up_item_category_5`,`pick_up_item_id_5`,`pick_up_item_num_5` FROM `challenge_match_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceInstanceId)) { return null; }

            while (query.Step()) {
                ChallengeMatchRace orm = _CreateOrmByQueryResultWithRaceInstanceIdOrderByIdAsc(query, raceInstanceId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ChallengeMatchRace _CreateOrmByQueryResultWithRaceInstanceIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int raceInstanceId)
        {
            int id                          = (int)query.GetInt(0);
            int challengeMatchId            = (int)query.GetInt(1);
            int dispOrder                   = (int)query.GetInt(2);
            int challengeMatchBgId          = (int)query.GetInt(3);
            int challengeMatchBgSubId       = (int)query.GetInt(4);
            int difficulty                  = (int)query.GetInt(5);
            int raceType                    = (int)query.GetInt(6);
            int raceAppearanceRate          = (int)query.GetInt(7);
            int season                      = (int)query.GetInt(8);
            int weather                     = (int)query.GetInt(9);
            int ground                      = (int)query.GetInt(10);
            int challengeMatchRaceBossNpcId = (int)query.GetInt(11);
            int costRp                      = (int)query.GetInt(12);
            int dropRewardOddsId            = (int)query.GetInt(13);
            int victoryRewardOddsId         = (int)query.GetInt(14);
            int firstClearItemCategory1     = (int)query.GetInt(15);
            int firstClearItemId1           = (int)query.GetInt(16);
            int firstClearItemNum1          = (int)query.GetInt(17);
            int firstClearItemCategory2     = (int)query.GetInt(18);
            int firstClearItemId2           = (int)query.GetInt(19);
            int firstClearItemNum2          = (int)query.GetInt(20);
            int firstClearItemCategory3     = (int)query.GetInt(21);
            int firstClearItemId3           = (int)query.GetInt(22);
            int firstClearItemNum3          = (int)query.GetInt(23);
            int pickUpItemCategory1         = (int)query.GetInt(24);
            int pickUpItemId1               = (int)query.GetInt(25);
            int pickUpItemNum1              = (int)query.GetInt(26);
            int pickUpItemCategory2         = (int)query.GetInt(27);
            int pickUpItemId2               = (int)query.GetInt(28);
            int pickUpItemNum2              = (int)query.GetInt(29);
            int pickUpItemCategory3         = (int)query.GetInt(30);
            int pickUpItemId3               = (int)query.GetInt(31);
            int pickUpItemNum3              = (int)query.GetInt(32);
            int pickUpItemCategory4         = (int)query.GetInt(33);
            int pickUpItemId4               = (int)query.GetInt(34);
            int pickUpItemNum4              = (int)query.GetInt(35);
            int pickUpItemCategory5         = (int)query.GetInt(36);
            int pickUpItemId5               = (int)query.GetInt(37);
            int pickUpItemNum5              = (int)query.GetInt(38);

            return new ChallengeMatchRace(id, challengeMatchId, dispOrder, challengeMatchBgId, challengeMatchBgSubId, difficulty, raceType, raceAppearanceRate, raceInstanceId, season, weather, ground, challengeMatchRaceBossNpcId, costRp, dropRewardOddsId, victoryRewardOddsId, firstClearItemCategory1, firstClearItemId1, firstClearItemNum1, firstClearItemCategory2, firstClearItemId2, firstClearItemNum2, firstClearItemCategory3, firstClearItemId3, firstClearItemNum3, pickUpItemCategory1, pickUpItemId1, pickUpItemNum1, pickUpItemCategory2, pickUpItemId2, pickUpItemNum2, pickUpItemCategory3, pickUpItemId3, pickUpItemNum3, pickUpItemCategory4, pickUpItemId4, pickUpItemNum4, pickUpItemCategory5, pickUpItemId5, pickUpItemNum5);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithChallengeMatchId.Clear();
            _dictionaryWithRaceInstanceId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ChallengeMatchRace()) {
                while (query.Step()) {
                    int id                          = (int)query.GetInt(0);
                    int challengeMatchId            = (int)query.GetInt(1);
                    int dispOrder                   = (int)query.GetInt(2);
                    int challengeMatchBgId          = (int)query.GetInt(3);
                    int challengeMatchBgSubId       = (int)query.GetInt(4);
                    int difficulty                  = (int)query.GetInt(5);
                    int raceType                    = (int)query.GetInt(6);
                    int raceAppearanceRate          = (int)query.GetInt(7);
                    int raceInstanceId              = (int)query.GetInt(8);
                    int season                      = (int)query.GetInt(9);
                    int weather                     = (int)query.GetInt(10);
                    int ground                      = (int)query.GetInt(11);
                    int challengeMatchRaceBossNpcId = (int)query.GetInt(12);
                    int costRp                      = (int)query.GetInt(13);
                    int dropRewardOddsId            = (int)query.GetInt(14);
                    int victoryRewardOddsId         = (int)query.GetInt(15);
                    int firstClearItemCategory1     = (int)query.GetInt(16);
                    int firstClearItemId1           = (int)query.GetInt(17);
                    int firstClearItemNum1          = (int)query.GetInt(18);
                    int firstClearItemCategory2     = (int)query.GetInt(19);
                    int firstClearItemId2           = (int)query.GetInt(20);
                    int firstClearItemNum2          = (int)query.GetInt(21);
                    int firstClearItemCategory3     = (int)query.GetInt(22);
                    int firstClearItemId3           = (int)query.GetInt(23);
                    int firstClearItemNum3          = (int)query.GetInt(24);
                    int pickUpItemCategory1         = (int)query.GetInt(25);
                    int pickUpItemId1               = (int)query.GetInt(26);
                    int pickUpItemNum1              = (int)query.GetInt(27);
                    int pickUpItemCategory2         = (int)query.GetInt(28);
                    int pickUpItemId2               = (int)query.GetInt(29);
                    int pickUpItemNum2              = (int)query.GetInt(30);
                    int pickUpItemCategory3         = (int)query.GetInt(31);
                    int pickUpItemId3               = (int)query.GetInt(32);
                    int pickUpItemNum3              = (int)query.GetInt(33);
                    int pickUpItemCategory4         = (int)query.GetInt(34);
                    int pickUpItemId4               = (int)query.GetInt(35);
                    int pickUpItemNum4              = (int)query.GetInt(36);
                    int pickUpItemCategory5         = (int)query.GetInt(37);
                    int pickUpItemId5               = (int)query.GetInt(38);
                    int pickUpItemNum5              = (int)query.GetInt(39);

                    int key = (int)id;
                    ChallengeMatchRace orm = new ChallengeMatchRace(id, challengeMatchId, dispOrder, challengeMatchBgId, challengeMatchBgSubId, difficulty, raceType, raceAppearanceRate, raceInstanceId, season, weather, ground, challengeMatchRaceBossNpcId, costRp, dropRewardOddsId, victoryRewardOddsId, firstClearItemCategory1, firstClearItemId1, firstClearItemNum1, firstClearItemCategory2, firstClearItemId2, firstClearItemNum2, firstClearItemCategory3, firstClearItemId3, firstClearItemNum3, pickUpItemCategory1, pickUpItemId1, pickUpItemNum1, pickUpItemCategory2, pickUpItemId2, pickUpItemNum2, pickUpItemCategory3, pickUpItemId3, pickUpItemNum3, pickUpItemCategory4, pickUpItemId4, pickUpItemNum4, pickUpItemCategory5, pickUpItemId5, pickUpItemNum5);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class ChallengeMatchRace
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: challenge_match_id) </summary>
            public readonly int ChallengeMatchId;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary> (CSV column: challenge_match_bg_id) </summary>
            public readonly int ChallengeMatchBgId;
            /// <summary> (CSV column: challenge_match_bg_sub_id) </summary>
            public readonly int ChallengeMatchBgSubId;
            /// <summary> (CSV column: difficulty) </summary>
            public readonly int Difficulty;
            /// <summary> (CSV column: race_type) </summary>
            public readonly int RaceType;
            /// <summary> (CSV column: race_appearance_rate) </summary>
            public readonly int RaceAppearanceRate;
            /// <summary> (CSV column: race_instance_id) </summary>
            public readonly int RaceInstanceId;
            /// <summary> (CSV column: season) </summary>
            public readonly int Season;
            /// <summary> (CSV column: weather) </summary>
            public readonly int Weather;
            /// <summary> (CSV column: ground) </summary>
            public readonly int Ground;
            /// <summary> (CSV column: challenge_match_race_boss_npc_id) </summary>
            public readonly int ChallengeMatchRaceBossNpcId;
            /// <summary> (CSV column: cost_rp) </summary>
            public readonly int CostRp;
            /// <summary> (CSV column: drop_reward_odds_id) </summary>
            public readonly int DropRewardOddsId;
            /// <summary> (CSV column: victory_reward_odds_id) </summary>
            public readonly int VictoryRewardOddsId;
            /// <summary> (CSV column: first_clear_item_category_1) </summary>
            public readonly int FirstClearItemCategory1;
            /// <summary> (CSV column: first_clear_item_id_1) </summary>
            public readonly int FirstClearItemId1;
            /// <summary> (CSV column: first_clear_item_num_1) </summary>
            public readonly int FirstClearItemNum1;
            /// <summary> (CSV column: first_clear_item_category_2) </summary>
            public readonly int FirstClearItemCategory2;
            /// <summary> (CSV column: first_clear_item_id_2) </summary>
            public readonly int FirstClearItemId2;
            /// <summary> (CSV column: first_clear_item_num_2) </summary>
            public readonly int FirstClearItemNum2;
            /// <summary> (CSV column: first_clear_item_category_3) </summary>
            public readonly int FirstClearItemCategory3;
            /// <summary> (CSV column: first_clear_item_id_3) </summary>
            public readonly int FirstClearItemId3;
            /// <summary> (CSV column: first_clear_item_num_3) </summary>
            public readonly int FirstClearItemNum3;
            /// <summary> (CSV column: pick_up_item_category_1) </summary>
            public readonly int PickUpItemCategory1;
            /// <summary> (CSV column: pick_up_item_id_1) </summary>
            public readonly int PickUpItemId1;
            /// <summary> (CSV column: pick_up_item_num_1) </summary>
            public readonly int PickUpItemNum1;
            /// <summary> (CSV column: pick_up_item_category_2) </summary>
            public readonly int PickUpItemCategory2;
            /// <summary> (CSV column: pick_up_item_id_2) </summary>
            public readonly int PickUpItemId2;
            /// <summary> (CSV column: pick_up_item_num_2) </summary>
            public readonly int PickUpItemNum2;
            /// <summary> (CSV column: pick_up_item_category_3) </summary>
            public readonly int PickUpItemCategory3;
            /// <summary> (CSV column: pick_up_item_id_3) </summary>
            public readonly int PickUpItemId3;
            /// <summary> (CSV column: pick_up_item_num_3) </summary>
            public readonly int PickUpItemNum3;
            /// <summary> (CSV column: pick_up_item_category_4) </summary>
            public readonly int PickUpItemCategory4;
            /// <summary> (CSV column: pick_up_item_id_4) </summary>
            public readonly int PickUpItemId4;
            /// <summary> (CSV column: pick_up_item_num_4) </summary>
            public readonly int PickUpItemNum4;
            /// <summary> (CSV column: pick_up_item_category_5) </summary>
            public readonly int PickUpItemCategory5;
            /// <summary> (CSV column: pick_up_item_id_5) </summary>
            public readonly int PickUpItemId5;
            /// <summary> (CSV column: pick_up_item_num_5) </summary>
            public readonly int PickUpItemNum5;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ChallengeMatchRace(int id = 0, int challengeMatchId = 0, int dispOrder = 0, int challengeMatchBgId = 0, int challengeMatchBgSubId = 0, int difficulty = 0, int raceType = 0, int raceAppearanceRate = 0, int raceInstanceId = 0, int season = 0, int weather = 0, int ground = 0, int challengeMatchRaceBossNpcId = 0, int costRp = 0, int dropRewardOddsId = 0, int victoryRewardOddsId = 0, int firstClearItemCategory1 = 0, int firstClearItemId1 = 0, int firstClearItemNum1 = 0, int firstClearItemCategory2 = 0, int firstClearItemId2 = 0, int firstClearItemNum2 = 0, int firstClearItemCategory3 = 0, int firstClearItemId3 = 0, int firstClearItemNum3 = 0, int pickUpItemCategory1 = 0, int pickUpItemId1 = 0, int pickUpItemNum1 = 0, int pickUpItemCategory2 = 0, int pickUpItemId2 = 0, int pickUpItemNum2 = 0, int pickUpItemCategory3 = 0, int pickUpItemId3 = 0, int pickUpItemNum3 = 0, int pickUpItemCategory4 = 0, int pickUpItemId4 = 0, int pickUpItemNum4 = 0, int pickUpItemCategory5 = 0, int pickUpItemId5 = 0, int pickUpItemNum5 = 0)
            {
                this.Id                          = id;
                this.ChallengeMatchId            = challengeMatchId;
                this.DispOrder                   = dispOrder;
                this.ChallengeMatchBgId          = challengeMatchBgId;
                this.ChallengeMatchBgSubId       = challengeMatchBgSubId;
                this.Difficulty                  = difficulty;
                this.RaceType                    = raceType;
                this.RaceAppearanceRate          = raceAppearanceRate;
                this.RaceInstanceId              = raceInstanceId;
                this.Season                      = season;
                this.Weather                     = weather;
                this.Ground                      = ground;
                this.ChallengeMatchRaceBossNpcId = challengeMatchRaceBossNpcId;
                this.CostRp                      = costRp;
                this.DropRewardOddsId            = dropRewardOddsId;
                this.VictoryRewardOddsId         = victoryRewardOddsId;
                this.FirstClearItemCategory1     = firstClearItemCategory1;
                this.FirstClearItemId1           = firstClearItemId1;
                this.FirstClearItemNum1          = firstClearItemNum1;
                this.FirstClearItemCategory2     = firstClearItemCategory2;
                this.FirstClearItemId2           = firstClearItemId2;
                this.FirstClearItemNum2          = firstClearItemNum2;
                this.FirstClearItemCategory3     = firstClearItemCategory3;
                this.FirstClearItemId3           = firstClearItemId3;
                this.FirstClearItemNum3          = firstClearItemNum3;
                this.PickUpItemCategory1         = pickUpItemCategory1;
                this.PickUpItemId1               = pickUpItemId1;
                this.PickUpItemNum1              = pickUpItemNum1;
                this.PickUpItemCategory2         = pickUpItemCategory2;
                this.PickUpItemId2               = pickUpItemId2;
                this.PickUpItemNum2              = pickUpItemNum2;
                this.PickUpItemCategory3         = pickUpItemCategory3;
                this.PickUpItemId3               = pickUpItemId3;
                this.PickUpItemNum3              = pickUpItemNum3;
                this.PickUpItemCategory4         = pickUpItemCategory4;
                this.PickUpItemId4               = pickUpItemId4;
                this.PickUpItemNum4              = pickUpItemNum4;
                this.PickUpItemCategory5         = pickUpItemCategory5;
                this.PickUpItemId5               = pickUpItemId5;
                this.PickUpItemNum5              = pickUpItemNum5;
            }
        }
    }
}
#endif

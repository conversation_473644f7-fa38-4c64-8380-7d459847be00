// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public class MasterTeamStadiumDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterTeamStadiumRawScore masterTeamStadiumRawScore                 { get; private set; }
        public MasterTeamStadiumScoreBonus masterTeamStadiumScoreBonus             { get; private set; }
        public MasterTeamStadium masterTeamStadium                                 { get; private set; }
        public MasterTeamStadiumClass masterTeamStadiumClass                       { get; private set; }
        public MasterTeamStadiumClassReward masterTeamStadiumClassReward           { get; private set; }
        public MasterTeamStadiumRank masterTeamStadiumRank                         { get; private set; }
        public MasterTeamStadiumEvaluationRate masterTeamStadiumEvaluationRate     { get; private set; }
        public MasterTeamStadiumStandMotion masterTeamStadiumStandMotion           { get; private set; }
        public MasterTeamStadiumRaceResultMotion masterTeamStadiumRaceResultMotion { get; private set; }
        public MasterTeamStadiumBgm masterTeamStadiumBgm                           { get; private set; }
        public MasterTeamStadiumSupportText masterTeamStadiumSupportText           { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumRawScore       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumScoreBonus     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadium               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumClass          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumClassReward    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumRank           = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumEvaluationRate = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumBgm            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTeamStadiumSupportText    = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumRawScore_conditionType                    = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumRawScore_raceScoreNameId                  = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumScoreBonus_conditionType                  = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumClass_teamStadiumId                       = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumClassReward_teamStadiumId_classRewardType = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumStandMotion_characterId_type              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumRaceResultMotion_characterId              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumBgm_sceneType                             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_teamStadiumSupportText_type                          = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterTeamStadiumDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterTeamStadiumRawScore         = new MasterTeamStadiumRawScore(this);
            this.masterTeamStadiumScoreBonus       = new MasterTeamStadiumScoreBonus(this);
            this.masterTeamStadium                 = new MasterTeamStadium(this);
            this.masterTeamStadiumClass            = new MasterTeamStadiumClass(this);
            this.masterTeamStadiumClassReward      = new MasterTeamStadiumClassReward(this);
            this.masterTeamStadiumRank             = new MasterTeamStadiumRank(this);
            this.masterTeamStadiumEvaluationRate   = new MasterTeamStadiumEvaluationRate(this);
            this.masterTeamStadiumStandMotion      = new MasterTeamStadiumStandMotion(this);
            this.masterTeamStadiumRaceResultMotion = new MasterTeamStadiumRaceResultMotion(this);
            this.masterTeamStadiumBgm              = new MasterTeamStadiumBgm(this);
            this.masterTeamStadiumSupportText      = new MasterTeamStadiumSupportText(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet team_stadium database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterTeamStadiumRawScore != null) { _selectQuery_masterTeamStadiumRawScore.Dispose(); _selectQuery_masterTeamStadiumRawScore = null; }
            if (_indexedSelectQuery_teamStadiumRawScore_conditionType != null) { _indexedSelectQuery_teamStadiumRawScore_conditionType.Dispose(); _indexedSelectQuery_teamStadiumRawScore_conditionType = null; }
            if (_indexedSelectQuery_teamStadiumRawScore_raceScoreNameId != null) { _indexedSelectQuery_teamStadiumRawScore_raceScoreNameId.Dispose(); _indexedSelectQuery_teamStadiumRawScore_raceScoreNameId = null; }
            if (_selectQuery_masterTeamStadiumScoreBonus != null) { _selectQuery_masterTeamStadiumScoreBonus.Dispose(); _selectQuery_masterTeamStadiumScoreBonus = null; }
            if (_indexedSelectQuery_teamStadiumScoreBonus_conditionType != null) { _indexedSelectQuery_teamStadiumScoreBonus_conditionType.Dispose(); _indexedSelectQuery_teamStadiumScoreBonus_conditionType = null; }
            if (_selectQuery_masterTeamStadium != null) { _selectQuery_masterTeamStadium.Dispose(); _selectQuery_masterTeamStadium = null; }
            if (_selectQuery_masterTeamStadiumClass != null) { _selectQuery_masterTeamStadiumClass.Dispose(); _selectQuery_masterTeamStadiumClass = null; }
            if (_indexedSelectQuery_teamStadiumClass_teamStadiumId != null) { _indexedSelectQuery_teamStadiumClass_teamStadiumId.Dispose(); _indexedSelectQuery_teamStadiumClass_teamStadiumId = null; }
            if (_selectQuery_masterTeamStadiumClassReward != null) { _selectQuery_masterTeamStadiumClassReward.Dispose(); _selectQuery_masterTeamStadiumClassReward = null; }
            if (_indexedSelectQuery_teamStadiumClassReward_teamStadiumId_classRewardType != null) { _indexedSelectQuery_teamStadiumClassReward_teamStadiumId_classRewardType.Dispose(); _indexedSelectQuery_teamStadiumClassReward_teamStadiumId_classRewardType = null; }
            if (_selectQuery_masterTeamStadiumRank != null) { _selectQuery_masterTeamStadiumRank.Dispose(); _selectQuery_masterTeamStadiumRank = null; }
            if (_selectQuery_masterTeamStadiumEvaluationRate != null) { _selectQuery_masterTeamStadiumEvaluationRate.Dispose(); _selectQuery_masterTeamStadiumEvaluationRate = null; }
            if (_indexedSelectQuery_teamStadiumStandMotion_characterId_type != null) { _indexedSelectQuery_teamStadiumStandMotion_characterId_type.Dispose(); _indexedSelectQuery_teamStadiumStandMotion_characterId_type = null; }
            if (_indexedSelectQuery_teamStadiumRaceResultMotion_characterId != null) { _indexedSelectQuery_teamStadiumRaceResultMotion_characterId.Dispose(); _indexedSelectQuery_teamStadiumRaceResultMotion_characterId = null; }
            if (_selectQuery_masterTeamStadiumBgm != null) { _selectQuery_masterTeamStadiumBgm.Dispose(); _selectQuery_masterTeamStadiumBgm = null; }
            if (_indexedSelectQuery_teamStadiumBgm_sceneType != null) { _indexedSelectQuery_teamStadiumBgm_sceneType.Dispose(); _indexedSelectQuery_teamStadiumBgm_sceneType = null; }
            if (_selectQuery_masterTeamStadiumSupportText != null) { _selectQuery_masterTeamStadiumSupportText.Dispose(); _selectQuery_masterTeamStadiumSupportText = null; }
            if (_indexedSelectQuery_teamStadiumSupportText_type != null) { _indexedSelectQuery_teamStadiumSupportText_type.Dispose(); _indexedSelectQuery_teamStadiumSupportText_type = null; }
            if (this.masterTeamStadiumRawScore != null) { this.masterTeamStadiumRawScore.Unload(); }
            if (this.masterTeamStadiumScoreBonus != null) { this.masterTeamStadiumScoreBonus.Unload(); }
            if (this.masterTeamStadium != null) { this.masterTeamStadium.Unload(); }
            if (this.masterTeamStadiumClass != null) { this.masterTeamStadiumClass.Unload(); }
            if (this.masterTeamStadiumClassReward != null) { this.masterTeamStadiumClassReward.Unload(); }
            if (this.masterTeamStadiumRank != null) { this.masterTeamStadiumRank.Unload(); }
            if (this.masterTeamStadiumEvaluationRate != null) { this.masterTeamStadiumEvaluationRate.Unload(); }
            if (this.masterTeamStadiumStandMotion != null) { this.masterTeamStadiumStandMotion.Unload(); }
            if (this.masterTeamStadiumRaceResultMotion != null) { this.masterTeamStadiumRaceResultMotion.Unload(); }
            if (this.masterTeamStadiumBgm != null) { this.masterTeamStadiumBgm.Unload(); }
            if (this.masterTeamStadiumSupportText != null) { this.masterTeamStadiumSupportText.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for team_stadium/team_stadium_raw_score
        
        /// <summary>
        /// SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumRawScore()
        {
            if (_selectQuery_masterTeamStadiumRawScore == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumRawScore = connection.PreparedQuery("SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadiumRawScore;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score` WHERE `condition_type`=? ORDER BY `priority` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumRawScore_ConditionType()
        {
            if (_indexedSelectQuery_teamStadiumRawScore_conditionType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumRawScore_conditionType = connection.PreparedQuery("SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score` WHERE `condition_type`=? ORDER BY `priority` ASC;");
            }
        
            return _indexedSelectQuery_teamStadiumRawScore_conditionType;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`sort_order` FROM `team_stadium_raw_score` WHERE `race_score_name_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumRawScore_RaceScoreNameId()
        {
            if (_indexedSelectQuery_teamStadiumRawScore_raceScoreNameId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumRawScore_raceScoreNameId = connection.PreparedQuery("SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`sort_order` FROM `team_stadium_raw_score` WHERE `race_score_name_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_teamStadiumRawScore_raceScoreNameId;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumRawScore()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score`;");
        }
        
        // SQL statements for team_stadium/team_stadium_score_bonus
        
        /// <summary>
        /// SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumScoreBonus()
        {
            if (_selectQuery_masterTeamStadiumScoreBonus == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumScoreBonus = connection.PreparedQuery("SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadiumScoreBonus;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus` WHERE `condition_type`=? ORDER BY `priority` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumScoreBonus_ConditionType()
        {
            if (_indexedSelectQuery_teamStadiumScoreBonus_conditionType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumScoreBonus_conditionType = connection.PreparedQuery("SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus` WHERE `condition_type`=? ORDER BY `priority` ASC;");
            }
        
            return _indexedSelectQuery_teamStadiumScoreBonus_conditionType;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumScoreBonus()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus`;");
        }
        
        // SQL statements for team_stadium/team_stadium
        
        /// <summary>
        /// SELECT `race_start_date`,`race_start_time`,`race_end_date`,`race_end_time`,`interval_start_date`,`interval_start_time`,`interval_end_date`,`interval_end_time`,`calc_start_date`,`calc_start_time`,`calc_end_date`,`calc_end_time`,`start_date`,`end_date` FROM `team_stadium` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadium()
        {
            if (_selectQuery_masterTeamStadium == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadium = connection.PreparedQuery("SELECT `race_start_date`,`race_start_time`,`race_end_date`,`race_end_time`,`interval_start_date`,`interval_start_time`,`interval_end_date`,`interval_end_time`,`calc_start_date`,`calc_start_time`,`calc_end_date`,`calc_end_time`,`start_date`,`end_date` FROM `team_stadium` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadium;
        }
        
        /// <summary>
        /// SELECT `id`,`race_start_date`,`race_start_time`,`race_end_date`,`race_end_time`,`interval_start_date`,`interval_start_time`,`interval_end_date`,`interval_end_time`,`calc_start_date`,`calc_start_time`,`calc_end_date`,`calc_end_time`,`start_date`,`end_date` FROM `team_stadium`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadium()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`race_start_date`,`race_start_time`,`race_end_date`,`race_end_time`,`interval_start_date`,`interval_start_time`,`interval_end_date`,`interval_end_time`,`calc_start_date`,`calc_start_time`,`calc_end_date`,`calc_end_time`,`start_date`,`end_date` FROM `team_stadium`;");
        }
        
        // SQL statements for team_stadium/team_stadium_class
        
        /// <summary>
        /// SELECT `team_stadium_id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumClass()
        {
            if (_selectQuery_masterTeamStadiumClass == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumClass = connection.PreparedQuery("SELECT `team_stadium_id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadiumClass;
        }
        
        /// <summary>
        /// SELECT `id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class` WHERE `team_stadium_id`=? ORDER BY `team_class` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumClass_TeamStadiumId()
        {
            if (_indexedSelectQuery_teamStadiumClass_teamStadiumId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumClass_teamStadiumId = connection.PreparedQuery("SELECT `id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class` WHERE `team_stadium_id`=? ORDER BY `team_class` ASC;");
            }
        
            return _indexedSelectQuery_teamStadiumClass_teamStadiumId;
        }
        
        /// <summary>
        /// SELECT `id`,`team_stadium_id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumClass()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`team_stadium_id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class`;");
        }
        
        // SQL statements for team_stadium/team_stadium_class_reward
        
        /// <summary>
        /// SELECT `team_stadium_id`,`team_class`,`class_reward_type`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumClassReward()
        {
            if (_selectQuery_masterTeamStadiumClassReward == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumClassReward = connection.PreparedQuery("SELECT `team_stadium_id`,`team_class`,`class_reward_type`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadiumClassReward;
        }
        
        /// <summary>
        /// SELECT `id`,`team_class`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward` WHERE `team_stadium_id`=? AND `class_reward_type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumClassReward_TeamStadiumId_ClassRewardType()
        {
            if (_indexedSelectQuery_teamStadiumClassReward_teamStadiumId_classRewardType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumClassReward_teamStadiumId_classRewardType = connection.PreparedQuery("SELECT `id`,`team_class`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward` WHERE `team_stadium_id`=? AND `class_reward_type`=?;");
            }
        
            return _indexedSelectQuery_teamStadiumClassReward_teamStadiumId_classRewardType;
        }
        
        /// <summary>
        /// SELECT `id`,`team_stadium_id`,`team_class`,`class_reward_type`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumClassReward()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`team_stadium_id`,`team_class`,`class_reward_type`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward`;");
        }
        
        // SQL statements for team_stadium/team_stadium_rank
        
        /// <summary>
        /// SELECT `team_rank`,`team_rank_group`,`team_min_value`,`team_max_value`,`item_category`,`item_id`,`item_num` FROM `team_stadium_rank` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumRank()
        {
            if (_selectQuery_masterTeamStadiumRank == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumRank = connection.PreparedQuery("SELECT `team_rank`,`team_rank_group`,`team_min_value`,`team_max_value`,`item_category`,`item_id`,`item_num` FROM `team_stadium_rank` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadiumRank;
        }
        
        /// <summary>
        /// SELECT `id`,`team_rank`,`team_rank_group`,`team_min_value`,`team_max_value`,`item_category`,`item_id`,`item_num` FROM `team_stadium_rank`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumRank()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`team_rank`,`team_rank_group`,`team_min_value`,`team_max_value`,`item_category`,`item_id`,`item_num` FROM `team_stadium_rank`;");
        }
        
        // SQL statements for team_stadium/team_stadium_evaluation_rate
        
        /// <summary>
        /// SELECT `id`,`rate` FROM `team_stadium_evaluation_rate` WHERE `proper_type`=? AND `proper_rank`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumEvaluationRate()
        {
            if (_selectQuery_masterTeamStadiumEvaluationRate == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumEvaluationRate = connection.PreparedQuery("SELECT `id`,`rate` FROM `team_stadium_evaluation_rate` WHERE `proper_type`=? AND `proper_rank`=?;");
            }
        
            return _selectQuery_masterTeamStadiumEvaluationRate;
        }
        
        /// <summary>
        /// SELECT `id`,`proper_type`,`proper_rank`,`rate` FROM `team_stadium_evaluation_rate`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumEvaluationRate()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`proper_type`,`proper_rank`,`rate` FROM `team_stadium_evaluation_rate`;");
        }
        
        // SQL statements for team_stadium/team_stadium_stand_motion
        
        /// <summary>
        /// SELECT `race_dress_id`,`position`,`motion_set`,`rotation`,`position_x` FROM `team_stadium_stand_motion` WHERE `character_id`=? AND `type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumStandMotion_CharacterId_Type()
        {
            if (_indexedSelectQuery_teamStadiumStandMotion_characterId_type == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumStandMotion_characterId_type = connection.PreparedQuery("SELECT `race_dress_id`,`position`,`motion_set`,`rotation`,`position_x` FROM `team_stadium_stand_motion` WHERE `character_id`=? AND `type`=?;");
            }
        
            return _indexedSelectQuery_teamStadiumStandMotion_characterId_type;
        }
        
        /// <summary>
        /// SELECT `character_id`,`type`,`race_dress_id`,`position`,`motion_set`,`rotation`,`position_x` FROM `team_stadium_stand_motion`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumStandMotion()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `character_id`,`type`,`race_dress_id`,`position`,`motion_set`,`rotation`,`position_x` FROM `team_stadium_stand_motion`;");
        }
        
        // SQL statements for team_stadium/team_stadium_race_result_motion
        
        /// <summary>
        /// SELECT `win_1`,`win_2`,`win_2_position_y`,`win_3`,`win_3_position_y`,`win_4`,`win_4_position_y`,`win_5`,`lose_1`,`lose_2`,`lose_3`,`lose_4`,`lose_5`,`draw_1`,`draw_2`,`draw_3`,`draw_4`,`draw_5`,`position_y` FROM `team_stadium_race_result_motion` WHERE `character_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumRaceResultMotion_CharacterId()
        {
            if (_indexedSelectQuery_teamStadiumRaceResultMotion_characterId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumRaceResultMotion_characterId = connection.PreparedQuery("SELECT `win_1`,`win_2`,`win_2_position_y`,`win_3`,`win_3_position_y`,`win_4`,`win_4_position_y`,`win_5`,`lose_1`,`lose_2`,`lose_3`,`lose_4`,`lose_5`,`draw_1`,`draw_2`,`draw_3`,`draw_4`,`draw_5`,`position_y` FROM `team_stadium_race_result_motion` WHERE `character_id`=?;");
            }
        
            return _indexedSelectQuery_teamStadiumRaceResultMotion_characterId;
        }
        
        /// <summary>
        /// SELECT `character_id`,`win_1`,`win_2`,`win_2_position_y`,`win_3`,`win_3_position_y`,`win_4`,`win_4_position_y`,`win_5`,`lose_1`,`lose_2`,`lose_3`,`lose_4`,`lose_5`,`draw_1`,`draw_2`,`draw_3`,`draw_4`,`draw_5`,`position_y` FROM `team_stadium_race_result_motion`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumRaceResultMotion()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `character_id`,`win_1`,`win_2`,`win_2_position_y`,`win_3`,`win_3_position_y`,`win_4`,`win_4_position_y`,`win_5`,`lose_1`,`lose_2`,`lose_3`,`lose_4`,`lose_5`,`draw_1`,`draw_2`,`draw_3`,`draw_4`,`draw_5`,`position_y` FROM `team_stadium_race_result_motion`;");
        }
        
        // SQL statements for team_stadium/team_stadium_bgm
        
        /// <summary>
        /// SELECT `scene_type`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumBgm()
        {
            if (_selectQuery_masterTeamStadiumBgm == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumBgm = connection.PreparedQuery("SELECT `scene_type`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadiumBgm;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm` WHERE `scene_type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumBgm_SceneType()
        {
            if (_indexedSelectQuery_teamStadiumBgm_sceneType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumBgm_sceneType = connection.PreparedQuery("SELECT `id`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm` WHERE `scene_type`=?;");
            }
        
            return _indexedSelectQuery_teamStadiumBgm_sceneType;
        }
        
        /// <summary>
        /// SELECT `id`,`scene_type`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumBgm()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`scene_type`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm`;");
        }
        
        // SQL statements for team_stadium/team_stadium_support_text
        
        /// <summary>
        /// SELECT `type`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TeamStadiumSupportText()
        {
            if (_selectQuery_masterTeamStadiumSupportText == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTeamStadiumSupportText = connection.PreparedQuery("SELECT `type`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTeamStadiumSupportText;
        }
        
        /// <summary>
        /// SELECT `id`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text` WHERE `type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TeamStadiumSupportText_Type()
        {
            if (_indexedSelectQuery_teamStadiumSupportText_type == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_teamStadiumSupportText_type = connection.PreparedQuery("SELECT `id`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text` WHERE `type`=?;");
            }
        
            return _indexedSelectQuery_teamStadiumSupportText_type;
        }
        
        /// <summary>
        /// SELECT `id`,`type`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TeamStadiumSupportText()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`type`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text`;");
        }
    }
}
#endif
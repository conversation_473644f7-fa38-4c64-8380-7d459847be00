// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/card_talent_upgrade
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCardTalentUpgrade : AbstractMasterData
    {
        public const string TABLE_NAME = "card_talent_upgrade";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CardTalentUpgrade> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCardTalentUpgrade(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CardTalentUpgrade>();
            _db = db;
        }


        public CardTalentUpgrade Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCardTalentUpgrade");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCardTalentUpgrade", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CardTalentUpgrade _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CardTalentUpgrade();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardTalentUpgrade");
                return null;
            }

            // SELECT `talent_group_id`,`talent_level`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CardTalentUpgrade orm = null;

            if (query.Step())
            {
                int talentGroupId = (int)query.GetInt(0);
                int talentLevel   = (int)query.GetInt(1);
                int itemCategory1 = (int)query.GetInt(2);
                int itemId1       = (int)query.GetInt(3);
                int itemNum1      = (int)query.GetInt(4);
                int itemCategory2 = (int)query.GetInt(5);
                int itemId2       = (int)query.GetInt(6);
                int itemNum2      = (int)query.GetInt(7);
                int itemCategory3 = (int)query.GetInt(8);
                int itemId3       = (int)query.GetInt(9);
                int itemNum3      = (int)query.GetInt(10);
                int itemCategory4 = (int)query.GetInt(11);
                int itemId4       = (int)query.GetInt(12);
                int itemNum4      = (int)query.GetInt(13);
                int itemCategory5 = (int)query.GetInt(14);
                int itemId5       = (int)query.GetInt(15);
                int itemNum5      = (int)query.GetInt(16);
                int itemCategory6 = (int)query.GetInt(17);
                int itemId6       = (int)query.GetInt(18);
                int itemNum6      = (int)query.GetInt(19);

                orm = new CardTalentUpgrade(id, talentGroupId, talentLevel, itemCategory1, itemId1, itemNum1, itemCategory2, itemId2, itemNum2, itemCategory3, itemId3, itemNum3, itemCategory4, itemId4, itemNum4, itemCategory5, itemId5, itemNum5, itemCategory6, itemId6, itemNum6);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CardTalentUpgrade GetWithTalentGroupIdAndTalentLevel(int talentGroupId, int talentLevel)
        {
            CardTalentUpgrade orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithTalentGroupIdAndTalentLevel(talentGroupId, talentLevel);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", talentGroupId, talentLevel));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CardTalentUpgrade _SelectWithTalentGroupIdAndTalentLevel(int talentGroupId, int talentLevel)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardTalentUpgrade_TalentGroupId_TalentLevel();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardTalentUpgrade");
                return null;
            }

            // SELECT `id`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade` WHERE `talent_group_id`=? AND `talent_level`=?;
            if (!query.BindInt(1, talentGroupId)) { return null; }
            if (!query.BindInt(2, talentLevel))   { return null; }

            CardTalentUpgrade orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithTalentGroupIdAndTalentLevel(query, talentGroupId, talentLevel);
            }

            query.Reset();

            return orm;
        }

        private CardTalentUpgrade _CreateOrmByQueryResultWithTalentGroupIdAndTalentLevel(LibNative.Sqlite3.PreparedQuery query, int talentGroupId, int talentLevel)
        {
            int id            = (int)query.GetInt(0);
            int itemCategory1 = (int)query.GetInt(1);
            int itemId1       = (int)query.GetInt(2);
            int itemNum1      = (int)query.GetInt(3);
            int itemCategory2 = (int)query.GetInt(4);
            int itemId2       = (int)query.GetInt(5);
            int itemNum2      = (int)query.GetInt(6);
            int itemCategory3 = (int)query.GetInt(7);
            int itemId3       = (int)query.GetInt(8);
            int itemNum3      = (int)query.GetInt(9);
            int itemCategory4 = (int)query.GetInt(10);
            int itemId4       = (int)query.GetInt(11);
            int itemNum4      = (int)query.GetInt(12);
            int itemCategory5 = (int)query.GetInt(13);
            int itemId5       = (int)query.GetInt(14);
            int itemNum5      = (int)query.GetInt(15);
            int itemCategory6 = (int)query.GetInt(16);
            int itemId6       = (int)query.GetInt(17);
            int itemNum6      = (int)query.GetInt(18);

            return new CardTalentUpgrade(id, talentGroupId, talentLevel, itemCategory1, itemId1, itemNum1, itemCategory2, itemId2, itemNum2, itemCategory3, itemId3, itemNum3, itemCategory4, itemId4, itemNum4, itemCategory5, itemId5, itemNum5, itemCategory6, itemId6, itemNum6);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class CardTalentUpgrade
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: talent_group_id) </summary>
            public readonly int TalentGroupId;
            /// <summary> (CSV column: talent_level) </summary>
            public readonly int TalentLevel;
            /// <summary> (CSV column: item_category_1) </summary>
            public readonly int ItemCategory1;
            /// <summary> (CSV column: item_id_1) </summary>
            public readonly int ItemId1;
            /// <summary> (CSV column: item_num_1) </summary>
            public readonly int ItemNum1;
            /// <summary> (CSV column: item_category_2) </summary>
            public readonly int ItemCategory2;
            /// <summary> (CSV column: item_id_2) </summary>
            public readonly int ItemId2;
            /// <summary> (CSV column: item_num_2) </summary>
            public readonly int ItemNum2;
            /// <summary> (CSV column: item_category_3) </summary>
            public readonly int ItemCategory3;
            /// <summary> (CSV column: item_id_3) </summary>
            public readonly int ItemId3;
            /// <summary> (CSV column: item_num_3) </summary>
            public readonly int ItemNum3;
            /// <summary> (CSV column: item_category_4) </summary>
            public readonly int ItemCategory4;
            /// <summary> (CSV column: item_id_4) </summary>
            public readonly int ItemId4;
            /// <summary> (CSV column: item_num_4) </summary>
            public readonly int ItemNum4;
            /// <summary> (CSV column: item_category_5) </summary>
            public readonly int ItemCategory5;
            /// <summary> (CSV column: item_id_5) </summary>
            public readonly int ItemId5;
            /// <summary> (CSV column: item_num_5) </summary>
            public readonly int ItemNum5;
            /// <summary> (CSV column: item_category_6) </summary>
            public readonly int ItemCategory6;
            /// <summary> (CSV column: item_id_6) </summary>
            public readonly int ItemId6;
            /// <summary> (CSV column: item_num_6) </summary>
            public readonly int ItemNum6;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CardTalentUpgrade(int id = 0, int talentGroupId = 0, int talentLevel = 0, int itemCategory1 = 0, int itemId1 = 0, int itemNum1 = 0, int itemCategory2 = 0, int itemId2 = 0, int itemNum2 = 0, int itemCategory3 = 0, int itemId3 = 0, int itemNum3 = 0, int itemCategory4 = 0, int itemId4 = 0, int itemNum4 = 0, int itemCategory5 = 0, int itemId5 = 0, int itemNum5 = 0, int itemCategory6 = 0, int itemId6 = 0, int itemNum6 = 0)
            {
                this.Id            = id;
                this.TalentGroupId = talentGroupId;
                this.TalentLevel   = talentLevel;
                this.ItemCategory1 = itemCategory1;
                this.ItemId1       = itemId1;
                this.ItemNum1      = itemNum1;
                this.ItemCategory2 = itemCategory2;
                this.ItemId2       = itemId2;
                this.ItemNum2      = itemNum2;
                this.ItemCategory3 = itemCategory3;
                this.ItemId3       = itemId3;
                this.ItemNum3      = itemNum3;
                this.ItemCategory4 = itemCategory4;
                this.ItemId4       = itemId4;
                this.ItemNum4      = itemNum4;
                this.ItemCategory5 = itemCategory5;
                this.ItemId5       = itemId5;
                this.ItemNum5      = itemNum5;
                this.ItemCategory6 = itemCategory6;
                this.ItemId6       = itemId6;
                this.ItemNum6      = itemNum6;
            }
        }
    }
}
#endif

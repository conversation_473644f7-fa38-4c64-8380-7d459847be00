// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/random_ear_tail_motion
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:parts_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRandomEarTailMotion : AbstractMasterData
    {
        public const string TABLE_NAME = "random_ear_tail_motion";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RandomEarTailMotion> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RandomEarTailMotion>> _dictionaryWithPartsType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRandomEarTailMotion(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RandomEarTailMotion>();
            _dictionaryWithPartsType = new Dictionary<int, List<RandomEarTailMotion>>();
            _db = db;
        }


        public RandomEarTailMotion Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRandomEarTailMotion");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRandomEarTailMotion", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RandomEarTailMotion _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RandomEarTailMotion();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RandomEarTailMotion");
                return null;
            }

            // SELECT `parts_type`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RandomEarTailMotion orm = null;

            if (query.Step())
            {
                byte partsType    = (byte)query.GetInt(0);
                string motionName = query.GetText(1);
                int earType       = (int)query.GetInt(2);
                bool useStory     = (query.GetInt(3) == 1);
                bool useTheater   = (query.GetInt(4) == 1);

                orm = new RandomEarTailMotion(id, partsType, motionName, earType, useStory, useTheater);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RandomEarTailMotion GetWithPartsType(byte partsType)
        {
            RandomEarTailMotion orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithPartsType(partsType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", partsType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RandomEarTailMotion _SelectWithPartsType(byte partsType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RandomEarTailMotion_PartsType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RandomEarTailMotion");
                return null;
            }

            // SELECT `id`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion` WHERE `parts_type`=?;
            if (!query.BindInt(1, partsType)) { return null; }

            RandomEarTailMotion orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithPartsType(query, partsType);
            }

            query.Reset();

            return orm;
        }

        public List<RandomEarTailMotion> GetListWithPartsType(byte partsType)
        {
            int key = (int)((byte)partsType);

            if (!_dictionaryWithPartsType.ContainsKey(key)) {
                _dictionaryWithPartsType.Add(key, _ListSelectWithPartsType(partsType));
            }

            return _dictionaryWithPartsType[key];
        }

        public List<RandomEarTailMotion> MaybeListWithPartsType(byte partsType)
        {
            List<RandomEarTailMotion> list = GetListWithPartsType(partsType);
            return list.Count > 0 ? list : null;
        }

        private List<RandomEarTailMotion> _ListSelectWithPartsType(byte partsType)
        {
            List<RandomEarTailMotion> _list = new List<RandomEarTailMotion>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RandomEarTailMotion_PartsType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RandomEarTailMotion");
                return null;
            }

            // SELECT `id`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion` WHERE `parts_type`=?;
            if (!query.BindInt(1, partsType)) { return null; }

            while (query.Step()) {
                RandomEarTailMotion orm = _CreateOrmByQueryResultWithPartsType(query, partsType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RandomEarTailMotion _CreateOrmByQueryResultWithPartsType(LibNative.Sqlite3.PreparedQuery query, byte partsType)
        {
            int id            = (int)query.GetInt(0);
            string motionName = query.GetText(1);
            int earType       = (int)query.GetInt(2);
            bool useStory     = (query.GetInt(3) == 1);
            bool useTheater   = (query.GetInt(4) == 1);

            return new RandomEarTailMotion(id, partsType, motionName, earType, useStory, useTheater);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithPartsType.Clear();
        }

        public sealed partial class RandomEarTailMotion
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary>
            /// (CSV column: parts_type)
            /// (enum ePartsType)
            /// </summary>
            public readonly byte PartsType;
            /// <summary> (CSV column: motion_name) </summary>
            public readonly string MotionName;
            /// <summary> (CSV column: ear_type) </summary>
            public readonly int EarType;
            /// <summary> (CSV column: use_story) </summary>
            public readonly bool UseStory;
            /// <summary> (CSV column: use_theater) </summary>
            public readonly bool UseTheater;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RandomEarTailMotion(int id = 0, byte partsType = 0, string motionName = "", int earType = 0, bool useStory = false, bool useTheater = false)
            {
                this.Id         = id;
                this.PartsType  = partsType;
                this.MotionName = motionName;
                this.EarType    = earType;
                this.UseStory   = useStory;
                this.UseTheater = useTheater;
            }
        }
    }
}
#endif

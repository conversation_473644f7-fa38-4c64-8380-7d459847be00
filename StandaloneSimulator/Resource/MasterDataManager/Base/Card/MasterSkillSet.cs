// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillSet : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_set";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillSet> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SkillSet> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSkillSet");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillSet(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillSet>();
            _db = db;
        }


        public SkillSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillSet");
                return null;
            }

            // SELECT `skill_id1`,`skill_level1`,`skill_id2`,`skill_level2`,`skill_id3`,`skill_level3`,`skill_id4`,`skill_level4`,`skill_id5`,`skill_level5`,`skill_id6`,`skill_level6`,`skill_id7`,`skill_level7`,`skill_id8`,`skill_level8`,`skill_id9`,`skill_level9`,`skill_id10`,`skill_level10`,`skill_id11`,`skill_level11`,`skill_id12`,`skill_level12`,`skill_id13`,`skill_level13`,`skill_id14`,`skill_level14`,`skill_id15`,`skill_level15`,`skill_id16`,`skill_level16`,`skill_id17`,`skill_level17`,`skill_id18`,`skill_level18`,`skill_id19`,`skill_level19`,`skill_id20`,`skill_level20` FROM `skill_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillSet orm = null;

            if (query.Step())
            {
                int skillId1     = (int)query.GetInt(0);
                int skillLevel1  = (int)query.GetInt(1);
                int skillId2     = (int)query.GetInt(2);
                int skillLevel2  = (int)query.GetInt(3);
                int skillId3     = (int)query.GetInt(4);
                int skillLevel3  = (int)query.GetInt(5);
                int skillId4     = (int)query.GetInt(6);
                int skillLevel4  = (int)query.GetInt(7);
                int skillId5     = (int)query.GetInt(8);
                int skillLevel5  = (int)query.GetInt(9);
                int skillId6     = (int)query.GetInt(10);
                int skillLevel6  = (int)query.GetInt(11);
                int skillId7     = (int)query.GetInt(12);
                int skillLevel7  = (int)query.GetInt(13);
                int skillId8     = (int)query.GetInt(14);
                int skillLevel8  = (int)query.GetInt(15);
                int skillId9     = (int)query.GetInt(16);
                int skillLevel9  = (int)query.GetInt(17);
                int skillId10    = (int)query.GetInt(18);
                int skillLevel10 = (int)query.GetInt(19);
                int skillId11    = (int)query.GetInt(20);
                int skillLevel11 = (int)query.GetInt(21);
                int skillId12    = (int)query.GetInt(22);
                int skillLevel12 = (int)query.GetInt(23);
                int skillId13    = (int)query.GetInt(24);
                int skillLevel13 = (int)query.GetInt(25);
                int skillId14    = (int)query.GetInt(26);
                int skillLevel14 = (int)query.GetInt(27);
                int skillId15    = (int)query.GetInt(28);
                int skillLevel15 = (int)query.GetInt(29);
                int skillId16    = (int)query.GetInt(30);
                int skillLevel16 = (int)query.GetInt(31);
                int skillId17    = (int)query.GetInt(32);
                int skillLevel17 = (int)query.GetInt(33);
                int skillId18    = (int)query.GetInt(34);
                int skillLevel18 = (int)query.GetInt(35);
                int skillId19    = (int)query.GetInt(36);
                int skillLevel19 = (int)query.GetInt(37);
                int skillId20    = (int)query.GetInt(38);
                int skillLevel20 = (int)query.GetInt(39);

                orm = new SkillSet(id, skillId1, skillLevel1, skillId2, skillLevel2, skillId3, skillLevel3, skillId4, skillLevel4, skillId5, skillLevel5, skillId6, skillLevel6, skillId7, skillLevel7, skillId8, skillLevel8, skillId9, skillLevel9, skillId10, skillLevel10, skillId11, skillLevel11, skillId12, skillLevel12, skillId13, skillLevel13, skillId14, skillLevel14, skillId15, skillLevel15, skillId16, skillLevel16, skillId17, skillLevel17, skillId18, skillLevel18, skillId19, skillLevel19, skillId20, skillLevel20);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SkillSet()) {
                while (query.Step()) {
                    int id           = (int)query.GetInt(0);
                    int skillId1     = (int)query.GetInt(1);
                    int skillLevel1  = (int)query.GetInt(2);
                    int skillId2     = (int)query.GetInt(3);
                    int skillLevel2  = (int)query.GetInt(4);
                    int skillId3     = (int)query.GetInt(5);
                    int skillLevel3  = (int)query.GetInt(6);
                    int skillId4     = (int)query.GetInt(7);
                    int skillLevel4  = (int)query.GetInt(8);
                    int skillId5     = (int)query.GetInt(9);
                    int skillLevel5  = (int)query.GetInt(10);
                    int skillId6     = (int)query.GetInt(11);
                    int skillLevel6  = (int)query.GetInt(12);
                    int skillId7     = (int)query.GetInt(13);
                    int skillLevel7  = (int)query.GetInt(14);
                    int skillId8     = (int)query.GetInt(15);
                    int skillLevel8  = (int)query.GetInt(16);
                    int skillId9     = (int)query.GetInt(17);
                    int skillLevel9  = (int)query.GetInt(18);
                    int skillId10    = (int)query.GetInt(19);
                    int skillLevel10 = (int)query.GetInt(20);
                    int skillId11    = (int)query.GetInt(21);
                    int skillLevel11 = (int)query.GetInt(22);
                    int skillId12    = (int)query.GetInt(23);
                    int skillLevel12 = (int)query.GetInt(24);
                    int skillId13    = (int)query.GetInt(25);
                    int skillLevel13 = (int)query.GetInt(26);
                    int skillId14    = (int)query.GetInt(27);
                    int skillLevel14 = (int)query.GetInt(28);
                    int skillId15    = (int)query.GetInt(29);
                    int skillLevel15 = (int)query.GetInt(30);
                    int skillId16    = (int)query.GetInt(31);
                    int skillLevel16 = (int)query.GetInt(32);
                    int skillId17    = (int)query.GetInt(33);
                    int skillLevel17 = (int)query.GetInt(34);
                    int skillId18    = (int)query.GetInt(35);
                    int skillLevel18 = (int)query.GetInt(36);
                    int skillId19    = (int)query.GetInt(37);
                    int skillLevel19 = (int)query.GetInt(38);
                    int skillId20    = (int)query.GetInt(39);
                    int skillLevel20 = (int)query.GetInt(40);

                    int key = (int)id;
                    SkillSet orm = new SkillSet(id, skillId1, skillLevel1, skillId2, skillLevel2, skillId3, skillLevel3, skillId4, skillLevel4, skillId5, skillLevel5, skillId6, skillLevel6, skillId7, skillLevel7, skillId8, skillLevel8, skillId9, skillLevel9, skillId10, skillLevel10, skillId11, skillLevel11, skillId12, skillLevel12, skillId13, skillLevel13, skillId14, skillLevel14, skillId15, skillLevel15, skillId16, skillLevel16, skillId17, skillLevel17, skillId18, skillLevel18, skillId19, skillLevel19, skillId20, skillLevel20);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SkillSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: skill_id1) </summary>
            public readonly int SkillId1;
            /// <summary> (CSV column: skill_level1) </summary>
            public readonly int SkillLevel1;
            /// <summary> (CSV column: skill_id2) </summary>
            public readonly int SkillId2;
            /// <summary> (CSV column: skill_level2) </summary>
            public readonly int SkillLevel2;
            /// <summary> (CSV column: skill_id3) </summary>
            public readonly int SkillId3;
            /// <summary> (CSV column: skill_level3) </summary>
            public readonly int SkillLevel3;
            /// <summary> (CSV column: skill_id4) </summary>
            public readonly int SkillId4;
            /// <summary> (CSV column: skill_level4) </summary>
            public readonly int SkillLevel4;
            /// <summary> (CSV column: skill_id5) </summary>
            public readonly int SkillId5;
            /// <summary> (CSV column: skill_level5) </summary>
            public readonly int SkillLevel5;
            /// <summary> (CSV column: skill_id6) </summary>
            public readonly int SkillId6;
            /// <summary> (CSV column: skill_level6) </summary>
            public readonly int SkillLevel6;
            /// <summary> (CSV column: skill_id7) </summary>
            public readonly int SkillId7;
            /// <summary> (CSV column: skill_level7) </summary>
            public readonly int SkillLevel7;
            /// <summary> (CSV column: skill_id8) </summary>
            public readonly int SkillId8;
            /// <summary> (CSV column: skill_level8) </summary>
            public readonly int SkillLevel8;
            /// <summary> (CSV column: skill_id9) </summary>
            public readonly int SkillId9;
            /// <summary> (CSV column: skill_level9) </summary>
            public readonly int SkillLevel9;
            /// <summary> (CSV column: skill_id10) </summary>
            public readonly int SkillId10;
            /// <summary> (CSV column: skill_level10) </summary>
            public readonly int SkillLevel10;
            /// <summary> (CSV column: skill_id11) </summary>
            public readonly int SkillId11;
            /// <summary> (CSV column: skill_level11) </summary>
            public readonly int SkillLevel11;
            /// <summary> (CSV column: skill_id12) </summary>
            public readonly int SkillId12;
            /// <summary> (CSV column: skill_level12) </summary>
            public readonly int SkillLevel12;
            /// <summary> (CSV column: skill_id13) </summary>
            public readonly int SkillId13;
            /// <summary> (CSV column: skill_level13) </summary>
            public readonly int SkillLevel13;
            /// <summary> (CSV column: skill_id14) </summary>
            public readonly int SkillId14;
            /// <summary> (CSV column: skill_level14) </summary>
            public readonly int SkillLevel14;
            /// <summary> (CSV column: skill_id15) </summary>
            public readonly int SkillId15;
            /// <summary> (CSV column: skill_level15) </summary>
            public readonly int SkillLevel15;
            /// <summary> (CSV column: skill_id16) </summary>
            public readonly int SkillId16;
            /// <summary> (CSV column: skill_level16) </summary>
            public readonly int SkillLevel16;
            /// <summary> (CSV column: skill_id17) </summary>
            public readonly int SkillId17;
            /// <summary> (CSV column: skill_level17) </summary>
            public readonly int SkillLevel17;
            /// <summary> (CSV column: skill_id18) </summary>
            public readonly int SkillId18;
            /// <summary> (CSV column: skill_level18) </summary>
            public readonly int SkillLevel18;
            /// <summary> (CSV column: skill_id19) </summary>
            public readonly int SkillId19;
            /// <summary> (CSV column: skill_level19) </summary>
            public readonly int SkillLevel19;
            /// <summary> (CSV column: skill_id20) </summary>
            public readonly int SkillId20;
            /// <summary> (CSV column: skill_level20) </summary>
            public readonly int SkillLevel20;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillSet(int id = 0, int skillId1 = 0, int skillLevel1 = 0, int skillId2 = 0, int skillLevel2 = 0, int skillId3 = 0, int skillLevel3 = 0, int skillId4 = 0, int skillLevel4 = 0, int skillId5 = 0, int skillLevel5 = 0, int skillId6 = 0, int skillLevel6 = 0, int skillId7 = 0, int skillLevel7 = 0, int skillId8 = 0, int skillLevel8 = 0, int skillId9 = 0, int skillLevel9 = 0, int skillId10 = 0, int skillLevel10 = 0, int skillId11 = 0, int skillLevel11 = 0, int skillId12 = 0, int skillLevel12 = 0, int skillId13 = 0, int skillLevel13 = 0, int skillId14 = 0, int skillLevel14 = 0, int skillId15 = 0, int skillLevel15 = 0, int skillId16 = 0, int skillLevel16 = 0, int skillId17 = 0, int skillLevel17 = 0, int skillId18 = 0, int skillLevel18 = 0, int skillId19 = 0, int skillLevel19 = 0, int skillId20 = 0, int skillLevel20 = 0)
            {
                this.Id           = id;
                this.SkillId1     = skillId1;
                this.SkillLevel1  = skillLevel1;
                this.SkillId2     = skillId2;
                this.SkillLevel2  = skillLevel2;
                this.SkillId3     = skillId3;
                this.SkillLevel3  = skillLevel3;
                this.SkillId4     = skillId4;
                this.SkillLevel4  = skillLevel4;
                this.SkillId5     = skillId5;
                this.SkillLevel5  = skillLevel5;
                this.SkillId6     = skillId6;
                this.SkillLevel6  = skillLevel6;
                this.SkillId7     = skillId7;
                this.SkillLevel7  = skillLevel7;
                this.SkillId8     = skillId8;
                this.SkillLevel8  = skillLevel8;
                this.SkillId9     = skillId9;
                this.SkillLevel9  = skillLevel9;
                this.SkillId10    = skillId10;
                this.SkillLevel10 = skillLevel10;
                this.SkillId11    = skillId11;
                this.SkillLevel11 = skillLevel11;
                this.SkillId12    = skillId12;
                this.SkillLevel12 = skillLevel12;
                this.SkillId13    = skillId13;
                this.SkillLevel13 = skillLevel13;
                this.SkillId14    = skillId14;
                this.SkillLevel14 = skillLevel14;
                this.SkillId15    = skillId15;
                this.SkillLevel15 = skillLevel15;
                this.SkillId16    = skillId16;
                this.SkillLevel16 = skillLevel16;
                this.SkillId17    = skillId17;
                this.SkillLevel17 = skillLevel17;
                this.SkillId18    = skillId18;
                this.SkillLevel18 = skillLevel18;
                this.SkillId19    = skillId19;
                this.SkillLevel19 = skillLevel19;
                this.SkillId20    = skillId20;
                this.SkillLevel20 = skillLevel20;
            }
        }
    }
}
#endif

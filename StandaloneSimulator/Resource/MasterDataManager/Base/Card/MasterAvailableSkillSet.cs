// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/available_skill_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:available_skill_set_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterAvailableSkillSet : AbstractMasterData
    {
        public const string TABLE_NAME = "available_skill_set";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, AvailableSkillSet> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<AvailableSkillSet>> _dictionaryWithAvailableSkillSetId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterAvailableSkillSet(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, AvailableSkillSet>();
            _dictionaryWithAvailableSkillSetId = new Dictionary<int, List<AvailableSkillSet>>();
            _db = db;
        }


        public AvailableSkillSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterAvailableSkillSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterAvailableSkillSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private AvailableSkillSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_AvailableSkillSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for AvailableSkillSet");
                return null;
            }

            // SELECT `available_skill_set_id`,`skill_id`,`need_rank` FROM `available_skill_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            AvailableSkillSet orm = null;

            if (query.Step())
            {
                int availableSkillSetId = (int)query.GetInt(0);
                int skillId             = (int)query.GetInt(1);
                int needRank            = (int)query.GetInt(2);

                orm = new AvailableSkillSet(id, availableSkillSetId, skillId, needRank);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public AvailableSkillSet GetWithAvailableSkillSetIdOrderByIdAsc(int availableSkillSetId)
        {
            AvailableSkillSet orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithAvailableSkillSetIdOrderByIdAsc(availableSkillSetId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", availableSkillSetId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private AvailableSkillSet _SelectWithAvailableSkillSetIdOrderByIdAsc(int availableSkillSetId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AvailableSkillSet_AvailableSkillSetId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for AvailableSkillSet");
                return null;
            }

            // SELECT `id`,`skill_id`,`need_rank` FROM `available_skill_set` WHERE `available_skill_set_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, availableSkillSetId)) { return null; }

            AvailableSkillSet orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithAvailableSkillSetIdOrderByIdAsc(query, availableSkillSetId);
            }

            query.Reset();

            return orm;
        }

        public List<AvailableSkillSet> GetListWithAvailableSkillSetIdOrderByIdAsc(int availableSkillSetId)
        {
            int key = (int)availableSkillSetId;

            if (!_dictionaryWithAvailableSkillSetId.ContainsKey(key)) {
                _dictionaryWithAvailableSkillSetId.Add(key, _ListSelectWithAvailableSkillSetIdOrderByIdAsc(availableSkillSetId));
            }

            return _dictionaryWithAvailableSkillSetId[key];
        }

        public List<AvailableSkillSet> MaybeListWithAvailableSkillSetIdOrderByIdAsc(int availableSkillSetId)
        {
            List<AvailableSkillSet> list = GetListWithAvailableSkillSetIdOrderByIdAsc(availableSkillSetId);
            return list.Count > 0 ? list : null;
        }

        private List<AvailableSkillSet> _ListSelectWithAvailableSkillSetIdOrderByIdAsc(int availableSkillSetId)
        {
            List<AvailableSkillSet> _list = new List<AvailableSkillSet>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AvailableSkillSet_AvailableSkillSetId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for AvailableSkillSet");
                return null;
            }

            // SELECT `id`,`skill_id`,`need_rank` FROM `available_skill_set` WHERE `available_skill_set_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, availableSkillSetId)) { return null; }

            while (query.Step()) {
                AvailableSkillSet orm = _CreateOrmByQueryResultWithAvailableSkillSetIdOrderByIdAsc(query, availableSkillSetId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private AvailableSkillSet _CreateOrmByQueryResultWithAvailableSkillSetIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int availableSkillSetId)
        {
            int id       = (int)query.GetInt(0);
            int skillId  = (int)query.GetInt(1);
            int needRank = (int)query.GetInt(2);

            return new AvailableSkillSet(id, availableSkillSetId, skillId, needRank);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithAvailableSkillSetId.Clear();
        }

        public sealed partial class AvailableSkillSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: available_skill_set_id) </summary>
            public readonly int AvailableSkillSetId;
            /// <summary> (CSV column: skill_id) </summary>
            public readonly int SkillId;
            /// <summary> (CSV column: need_rank) </summary>
            public readonly int NeedRank;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public AvailableSkillSet(int id = 0, int availableSkillSetId = 0, int skillId = 0, int needRank = 0)
            {
                this.Id                  = id;
                this.AvailableSkillSetId = availableSkillSetId;
                this.SkillId             = skillId;
                this.NeedRank            = needRank;
            }
        }
    }
}
#endif

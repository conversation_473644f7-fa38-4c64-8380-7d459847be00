// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_upgrade_description
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:card_id], [:card_id, :rank]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillUpgradeDescription : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_upgrade_description";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillUpgradeDescription> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SkillUpgradeDescription>> _dictionaryWithCardId = null;
        private Dictionary<ulong, List<SkillUpgradeDescription>> _dictionaryWithCardIdAndRank = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillUpgradeDescription(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillUpgradeDescription>();
            _dictionaryWithCardId = new Dictionary<int, List<SkillUpgradeDescription>>();
            _dictionaryWithCardIdAndRank = new Dictionary<ulong, List<SkillUpgradeDescription>>();
            _db = db;
        }


        public SkillUpgradeDescription Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillUpgradeDescription");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillUpgradeDescription", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeDescription _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillUpgradeDescription();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeDescription");
                return null;
            }

            // SELECT `card_id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillUpgradeDescription orm = null;

            if (query.Step())
            {
                int cardId     = (int)query.GetInt(0);
                int rank       = (int)query.GetInt(1);
                int skillId    = (int)query.GetInt(2);
                long startDate = (long)query.GetLong(3);

                orm = new SkillUpgradeDescription(id, cardId, rank, skillId, startDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SkillUpgradeDescription GetWithCardIdOrderByRankAsc(int cardId)
        {
            SkillUpgradeDescription orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCardIdOrderByRankAsc(cardId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", cardId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeDescription _SelectWithCardIdOrderByRankAsc(int cardId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeDescription_CardId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeDescription");
                return null;
            }

            // SELECT `id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? ORDER BY `rank` ASC;
            if (!query.BindInt(1, cardId)) { return null; }

            SkillUpgradeDescription orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCardIdOrderByRankAsc(query, cardId);
            }

            query.Reset();

            return orm;
        }

        public List<SkillUpgradeDescription> GetListWithCardIdOrderByRankAsc(int cardId)
        {
            int key = (int)cardId;

            if (!_dictionaryWithCardId.ContainsKey(key)) {
                _dictionaryWithCardId.Add(key, _ListSelectWithCardIdOrderByRankAsc(cardId));
            }

            return _dictionaryWithCardId[key];
        }

        public List<SkillUpgradeDescription> MaybeListWithCardIdOrderByRankAsc(int cardId)
        {
            List<SkillUpgradeDescription> list = GetListWithCardIdOrderByRankAsc(cardId);
            return list.Count > 0 ? list : null;
        }

        private List<SkillUpgradeDescription> _ListSelectWithCardIdOrderByRankAsc(int cardId)
        {
            List<SkillUpgradeDescription> _list = new List<SkillUpgradeDescription>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeDescription_CardId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeDescription");
                return null;
            }

            // SELECT `id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? ORDER BY `rank` ASC;
            if (!query.BindInt(1, cardId)) { return null; }

            while (query.Step()) {
                SkillUpgradeDescription orm = _CreateOrmByQueryResultWithCardIdOrderByRankAsc(query, cardId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillUpgradeDescription _CreateOrmByQueryResultWithCardIdOrderByRankAsc(LibNative.Sqlite3.PreparedQuery query, int cardId)
        {
            int id         = (int)query.GetInt(0);
            int rank       = (int)query.GetInt(1);
            int skillId    = (int)query.GetInt(2);
            long startDate = (long)query.GetLong(3);

            return new SkillUpgradeDescription(id, cardId, rank, skillId, startDate);
        }

        public SkillUpgradeDescription GetWithCardIdAndRankOrderByRankAsc(int cardId, int rank)
        {
            SkillUpgradeDescription orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCardIdAndRankOrderByRankAsc(cardId, rank);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", cardId, rank));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeDescription _SelectWithCardIdAndRankOrderByRankAsc(int cardId, int rank)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeDescription_CardId_Rank();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeDescription");
                return null;
            }

            // SELECT `id`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? AND `rank`=? ORDER BY `rank` ASC;
            if (!query.BindInt(1, cardId)) { return null; }
            if (!query.BindInt(2, rank))   { return null; }

            SkillUpgradeDescription orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCardIdAndRankOrderByRankAsc(query, cardId, rank);
            }

            query.Reset();

            return orm;
        }

        public List<SkillUpgradeDescription> GetListWithCardIdAndRankOrderByRankAsc(int cardId, int rank)
        {
            ulong key = ((uint)unchecked((ulong)((int)cardId))) | ((((ulong)unchecked((ulong)((int)rank)))) << 32);

            if (!_dictionaryWithCardIdAndRank.ContainsKey(key)) {
                _dictionaryWithCardIdAndRank.Add(key, _ListSelectWithCardIdAndRankOrderByRankAsc(cardId, rank));
            }

            return _dictionaryWithCardIdAndRank[key];
        }

        public List<SkillUpgradeDescription> MaybeListWithCardIdAndRankOrderByRankAsc(int cardId, int rank)
        {
            List<SkillUpgradeDescription> list = GetListWithCardIdAndRankOrderByRankAsc(cardId, rank);
            return list.Count > 0 ? list : null;
        }

        private List<SkillUpgradeDescription> _ListSelectWithCardIdAndRankOrderByRankAsc(int cardId, int rank)
        {
            List<SkillUpgradeDescription> _list = new List<SkillUpgradeDescription>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeDescription_CardId_Rank();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeDescription");
                return null;
            }

            // SELECT `id`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? AND `rank`=? ORDER BY `rank` ASC;
            if (!query.BindInt(1, cardId)) { return null; }
            if (!query.BindInt(2, rank))   { return null; }

            while (query.Step()) {
                SkillUpgradeDescription orm = _CreateOrmByQueryResultWithCardIdAndRankOrderByRankAsc(query, cardId, rank);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillUpgradeDescription _CreateOrmByQueryResultWithCardIdAndRankOrderByRankAsc(LibNative.Sqlite3.PreparedQuery query, int cardId, int rank)
        {
            int id         = (int)query.GetInt(0);
            int skillId    = (int)query.GetInt(1);
            long startDate = (long)query.GetLong(2);

            return new SkillUpgradeDescription(id, cardId, rank, skillId, startDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCardId.Clear();
            _dictionaryWithCardIdAndRank.Clear();
        }

        public sealed partial class SkillUpgradeDescription
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: card_id) </summary>
            public readonly int CardId;
            /// <summary> (CSV column: rank) </summary>
            public readonly int Rank;
            /// <summary> (CSV column: skill_id) </summary>
            public readonly int SkillId;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillUpgradeDescription(int id = 0, int cardId = 0, int rank = 0, int skillId = 0, long startDate = 0)
            {
                this.Id        = id;
                this.CardId    = cardId;
                this.Rank      = rank;
                this.SkillId   = skillId;
                this.StartDate = startDate;
            }
        }
    }
}
#endif

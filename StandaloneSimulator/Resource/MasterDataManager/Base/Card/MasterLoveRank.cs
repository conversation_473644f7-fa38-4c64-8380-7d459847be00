// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/love_rank
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterLoveRank : AbstractMasterData
    {
        public const string TABLE_NAME = "love_rank";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, LoveRank> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, LoveRank> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterLoveRank");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterLoveRank(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, LoveRank>();
            _db = db;
        }


        public LoveRank Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterLoveRank");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterLoveRank", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private LoveRank _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_LoveRank();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for LoveRank");
                return null;
            }

            // SELECT `rank`,`total_point` FROM `love_rank` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            LoveRank orm = null;

            if (query.Step())
            {
                int rank       = (int)query.GetInt(0);
                int totalPoint = (int)query.GetInt(1);

                orm = new LoveRank(id, rank, totalPoint);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public LoveRank GetWithRank(int rank)
        {
            LoveRank orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRank(rank);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", rank));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private LoveRank _SelectWithRank(int rank)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_LoveRank_Rank();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for LoveRank");
                return null;
            }

            // SELECT `id`,`total_point` FROM `love_rank` WHERE `rank`=?;
            if (!query.BindInt(1, rank)) { return null; }

            LoveRank orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRank(query, rank);
            }

            query.Reset();

            return orm;
        }

        private LoveRank _CreateOrmByQueryResultWithRank(LibNative.Sqlite3.PreparedQuery query, int rank)
        {
            int id         = (int)query.GetInt(0);
            int totalPoint = (int)query.GetInt(1);

            return new LoveRank(id, rank, totalPoint);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_LoveRank()) {
                while (query.Step()) {
                    int id         = (int)query.GetInt(0);
                    int rank       = (int)query.GetInt(1);
                    int totalPoint = (int)query.GetInt(2);

                    int key = (int)id;
                    LoveRank orm = new LoveRank(id, rank, totalPoint);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class LoveRank
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: rank) </summary>
            public readonly int Rank;
            /// <summary> (CSV column: total_point) </summary>
            public readonly int TotalPoint;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public LoveRank(int id = 0, int rank = 0, int totalPoint = 0)
            {
                this.Id         = id;
                this.Rank       = rank;
                this.TotalPoint = totalPoint;
            }
        }
    }
}
#endif

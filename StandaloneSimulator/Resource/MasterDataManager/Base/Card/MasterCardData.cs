// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/card_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:chara_id], [:get_piece_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCardData : AbstractMasterData
    {
        public const string TABLE_NAME = "card_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CardData> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<CardData>> _dictionaryWithCharaId = null;
        private Dictionary<int, List<CardData>> _dictionaryWithGetPieceId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, CardData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterCardData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCardData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CardData>();
            _dictionaryWithCharaId = new Dictionary<int, List<CardData>>();
            _dictionaryWithGetPieceId = new Dictionary<int, List<CardData>>();
            _db = db;
        }


        public CardData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCardData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCardData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CardData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CardData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardData");
                return null;
            }

            // SELECT `chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CardData orm = null;

            if (query.Step())
            {
                int charaId             = (int)query.GetInt(0);
                int defaultRarity       = (int)query.GetInt(1);
                int limitedChara        = (int)query.GetInt(2);
                int availableSkillSetId = (int)query.GetInt(3);
                int talentSpeed         = (int)query.GetInt(4);
                int talentStamina       = (int)query.GetInt(5);
                int talentPow           = (int)query.GetInt(6);
                int talentGuts          = (int)query.GetInt(7);
                int talentWiz           = (int)query.GetInt(8);
                int talentGroupId       = (int)query.GetInt(9);
                int bgId                = (int)query.GetInt(10);
                int getPieceId          = (int)query.GetInt(11);
                int runningStyle        = (int)query.GetInt(12);

                orm = new CardData(id, charaId, defaultRarity, limitedChara, availableSkillSetId, talentSpeed, talentStamina, talentPow, talentGuts, talentWiz, talentGroupId, bgId, getPieceId, runningStyle);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CardData GetWithCharaIdOrderByIdAsc(int charaId)
        {
            CardData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaIdOrderByIdAsc(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CardData _SelectWithCharaIdOrderByIdAsc(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardData_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardData");
                return null;
            }

            // SELECT `id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data` WHERE `chara_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, charaId)) { return null; }

            CardData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<CardData> GetListWithCharaIdOrderByIdAsc(int charaId)
        {
            int key = (int)charaId;

            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaIdOrderByIdAsc(charaId));
            }

            return _dictionaryWithCharaId[key];
        }

        public List<CardData> MaybeListWithCharaIdOrderByIdAsc(int charaId)
        {
            List<CardData> list = GetListWithCharaIdOrderByIdAsc(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<CardData> _ListSelectWithCharaIdOrderByIdAsc(int charaId)
        {
            List<CardData> _list = new List<CardData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardData_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CardData");
                return null;
            }

            // SELECT `id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data` WHERE `chara_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                CardData orm = _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CardData _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id                  = (int)query.GetInt(0);
            int defaultRarity       = (int)query.GetInt(1);
            int limitedChara        = (int)query.GetInt(2);
            int availableSkillSetId = (int)query.GetInt(3);
            int talentSpeed         = (int)query.GetInt(4);
            int talentStamina       = (int)query.GetInt(5);
            int talentPow           = (int)query.GetInt(6);
            int talentGuts          = (int)query.GetInt(7);
            int talentWiz           = (int)query.GetInt(8);
            int talentGroupId       = (int)query.GetInt(9);
            int bgId                = (int)query.GetInt(10);
            int getPieceId          = (int)query.GetInt(11);
            int runningStyle        = (int)query.GetInt(12);

            return new CardData(id, charaId, defaultRarity, limitedChara, availableSkillSetId, talentSpeed, talentStamina, talentPow, talentGuts, talentWiz, talentGroupId, bgId, getPieceId, runningStyle);
        }

        public CardData GetWithGetPieceIdOrderByIdAsc(int getPieceId)
        {
            CardData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGetPieceIdOrderByIdAsc(getPieceId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", getPieceId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CardData _SelectWithGetPieceIdOrderByIdAsc(int getPieceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardData_GetPieceId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardData");
                return null;
            }

            // SELECT `id`,`chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`running_style` FROM `card_data` WHERE `get_piece_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, getPieceId)) { return null; }

            CardData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGetPieceIdOrderByIdAsc(query, getPieceId);
            }

            query.Reset();

            return orm;
        }

        public List<CardData> GetListWithGetPieceIdOrderByIdAsc(int getPieceId)
        {
            int key = (int)getPieceId;

            if (!_dictionaryWithGetPieceId.ContainsKey(key)) {
                _dictionaryWithGetPieceId.Add(key, _ListSelectWithGetPieceIdOrderByIdAsc(getPieceId));
            }

            return _dictionaryWithGetPieceId[key];
        }

        public List<CardData> MaybeListWithGetPieceIdOrderByIdAsc(int getPieceId)
        {
            List<CardData> list = GetListWithGetPieceIdOrderByIdAsc(getPieceId);
            return list.Count > 0 ? list : null;
        }

        private List<CardData> _ListSelectWithGetPieceIdOrderByIdAsc(int getPieceId)
        {
            List<CardData> _list = new List<CardData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardData_GetPieceId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CardData");
                return null;
            }

            // SELECT `id`,`chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`running_style` FROM `card_data` WHERE `get_piece_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, getPieceId)) { return null; }

            while (query.Step()) {
                CardData orm = _CreateOrmByQueryResultWithGetPieceIdOrderByIdAsc(query, getPieceId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CardData _CreateOrmByQueryResultWithGetPieceIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int getPieceId)
        {
            int id                  = (int)query.GetInt(0);
            int charaId             = (int)query.GetInt(1);
            int defaultRarity       = (int)query.GetInt(2);
            int limitedChara        = (int)query.GetInt(3);
            int availableSkillSetId = (int)query.GetInt(4);
            int talentSpeed         = (int)query.GetInt(5);
            int talentStamina       = (int)query.GetInt(6);
            int talentPow           = (int)query.GetInt(7);
            int talentGuts          = (int)query.GetInt(8);
            int talentWiz           = (int)query.GetInt(9);
            int talentGroupId       = (int)query.GetInt(10);
            int bgId                = (int)query.GetInt(11);
            int runningStyle        = (int)query.GetInt(12);

            return new CardData(id, charaId, defaultRarity, limitedChara, availableSkillSetId, talentSpeed, talentStamina, talentPow, talentGuts, talentWiz, talentGroupId, bgId, getPieceId, runningStyle);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharaId.Clear();
            _dictionaryWithGetPieceId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_CardData()) {
                while (query.Step()) {
                    int id                  = (int)query.GetInt(0);
                    int charaId             = (int)query.GetInt(1);
                    int defaultRarity       = (int)query.GetInt(2);
                    int limitedChara        = (int)query.GetInt(3);
                    int availableSkillSetId = (int)query.GetInt(4);
                    int talentSpeed         = (int)query.GetInt(5);
                    int talentStamina       = (int)query.GetInt(6);
                    int talentPow           = (int)query.GetInt(7);
                    int talentGuts          = (int)query.GetInt(8);
                    int talentWiz           = (int)query.GetInt(9);
                    int talentGroupId       = (int)query.GetInt(10);
                    int bgId                = (int)query.GetInt(11);
                    int getPieceId          = (int)query.GetInt(12);
                    int runningStyle        = (int)query.GetInt(13);

                    int key = (int)id;
                    CardData orm = new CardData(id, charaId, defaultRarity, limitedChara, availableSkillSetId, talentSpeed, talentStamina, talentPow, talentGuts, talentWiz, talentGroupId, bgId, getPieceId, runningStyle);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class CardData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: default_rarity) </summary>
            public readonly int DefaultRarity;
            /// <summary> (CSV column: limited_chara) </summary>
            public readonly int LimitedChara;
            /// <summary> (CSV column: available_skill_set_id) </summary>
            public readonly int AvailableSkillSetId;
            /// <summary> (CSV column: talent_speed) </summary>
            public readonly int TalentSpeed;
            /// <summary> (CSV column: talent_stamina) </summary>
            public readonly int TalentStamina;
            /// <summary> (CSV column: talent_pow) </summary>
            public readonly int TalentPow;
            /// <summary> (CSV column: talent_guts) </summary>
            public readonly int TalentGuts;
            /// <summary> (CSV column: talent_wiz) </summary>
            public readonly int TalentWiz;
            /// <summary> (CSV column: talent_group_id) </summary>
            public readonly int TalentGroupId;
            /// <summary> (CSV column: bg_id) </summary>
            public readonly int BgId;
            /// <summary> (CSV column: get_piece_id) </summary>
            public readonly int GetPieceId;
            /// <summary> (CSV column: running_style) </summary>
            public readonly int RunningStyle;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CardData(int id = 0, int charaId = 0, int defaultRarity = 0, int limitedChara = 0, int availableSkillSetId = 0, int talentSpeed = 0, int talentStamina = 0, int talentPow = 0, int talentGuts = 0, int talentWiz = 0, int talentGroupId = 0, int bgId = 0, int getPieceId = 0, int runningStyle = 0)
            {
                this.Id                  = id;
                this.CharaId             = charaId;
                this.DefaultRarity       = defaultRarity;
                this.LimitedChara        = limitedChara;
                this.AvailableSkillSetId = availableSkillSetId;
                this.TalentSpeed         = talentSpeed;
                this.TalentStamina       = talentStamina;
                this.TalentPow           = talentPow;
                this.TalentGuts          = talentGuts;
                this.TalentWiz           = talentWiz;
                this.TalentGroupId       = talentGroupId;
                this.BgId                = bgId;
                this.GetPieceId          = getPieceId;
                this.RunningStyle        = runningStyle;
            }
        }
    }
}
#endif

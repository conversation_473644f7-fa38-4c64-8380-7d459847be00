// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_limit_break
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardLimitBreak : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_limit_break";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardLimitBreak> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SupportCardLimitBreak> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSupportCardLimitBreak");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardLimitBreak(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardLimitBreak>();
            _db = db;
        }


        public SupportCardLimitBreak Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardLimitBreak");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardLimitBreak", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardLimitBreak _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardLimitBreak();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardLimitBreak");
                return null;
            }

            // SELECT `rarity`,`item_id`,`item_num`,`disp_order` FROM `support_card_limit_break` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SupportCardLimitBreak orm = null;

            if (query.Step())
            {
                int rarity    = (int)query.GetInt(0);
                int itemId    = (int)query.GetInt(1);
                int itemNum   = (int)query.GetInt(2);
                int dispOrder = (int)query.GetInt(3);

                orm = new SupportCardLimitBreak(id, rarity, itemId, itemNum, dispOrder);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SupportCardLimitBreak()) {
                while (query.Step()) {
                    int id        = (int)query.GetInt(0);
                    int rarity    = (int)query.GetInt(1);
                    int itemId    = (int)query.GetInt(2);
                    int itemNum   = (int)query.GetInt(3);
                    int dispOrder = (int)query.GetInt(4);

                    int key = (int)id;
                    SupportCardLimitBreak orm = new SupportCardLimitBreak(id, rarity, itemId, itemNum, dispOrder);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SupportCardLimitBreak
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: item_id) </summary>
            public readonly int ItemId;
            /// <summary> (CSV column: item_num) </summary>
            public readonly int ItemNum;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardLimitBreak(int id = 0, int rarity = 0, int itemId = 0, int itemNum = 0, int dispOrder = 0)
            {
                this.Id        = id;
                this.Rarity    = rarity;
                this.ItemId    = itemId;
                this.ItemNum   = itemNum;
                this.DispOrder = dispOrder;
            }
        }
    }
}
#endif

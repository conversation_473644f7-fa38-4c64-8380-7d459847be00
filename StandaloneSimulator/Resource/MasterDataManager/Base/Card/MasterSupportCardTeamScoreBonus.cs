// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_team_score_bonus
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardTeamScoreBonus : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_team_score_bonus";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardTeamScoreBonus> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SupportCardTeamScoreBonus> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSupportCardTeamScoreBonus");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardTeamScoreBonus(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardTeamScoreBonus>();
            _db = db;
        }


        public SupportCardTeamScoreBonus Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardTeamScoreBonus");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardTeamScoreBonus", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardTeamScoreBonus _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardTeamScoreBonus();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardTeamScoreBonus");
                return null;
            }

            // SELECT `level`,`score_rate` FROM `support_card_team_score_bonus` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SupportCardTeamScoreBonus orm = null;

            if (query.Step())
            {
                int level     = (int)query.GetInt(0);
                int scoreRate = (int)query.GetInt(1);

                orm = new SupportCardTeamScoreBonus(id, level, scoreRate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SupportCardTeamScoreBonus GetWithLevel(int level)
        {
            SupportCardTeamScoreBonus orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithLevel(level);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", level));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SupportCardTeamScoreBonus _SelectWithLevel(int level)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardTeamScoreBonus_Level();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardTeamScoreBonus");
                return null;
            }

            // SELECT `id`,`score_rate` FROM `support_card_team_score_bonus` WHERE `level`=?;
            if (!query.BindInt(1, level)) { return null; }

            SupportCardTeamScoreBonus orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithLevel(query, level);
            }

            query.Reset();

            return orm;
        }

        private SupportCardTeamScoreBonus _CreateOrmByQueryResultWithLevel(LibNative.Sqlite3.PreparedQuery query, int level)
        {
            int id        = (int)query.GetInt(0);
            int scoreRate = (int)query.GetInt(1);

            return new SupportCardTeamScoreBonus(id, level, scoreRate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SupportCardTeamScoreBonus()) {
                while (query.Step()) {
                    int id        = (int)query.GetInt(0);
                    int level     = (int)query.GetInt(1);
                    int scoreRate = (int)query.GetInt(2);

                    int key = (int)id;
                    SupportCardTeamScoreBonus orm = new SupportCardTeamScoreBonus(id, level, scoreRate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SupportCardTeamScoreBonus
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: level) </summary>
            public readonly int Level;
            /// <summary> (CSV column: score_rate) </summary>
            public readonly int ScoreRate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardTeamScoreBonus(int id = 0, int level = 0, int scoreRate = 0)
            {
                this.Id        = id;
                this.Level     = level;
                this.ScoreRate = scoreRate;
            }
        }
    }
}
#endif

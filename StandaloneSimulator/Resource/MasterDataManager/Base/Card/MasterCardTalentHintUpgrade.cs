// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/card_talent_hint_upgrade
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:rarity, :talent_level]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCardTalentHintUpgrade : AbstractMasterData
    {
        public const string TABLE_NAME = "card_talent_hint_upgrade";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CardTalentHintUpgrade> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<CardTalentHintUpgrade>> _dictionaryWithRarityAndTalentLevel = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCardTalentHintUpgrade(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CardTalentHintUpgrade>();
            _dictionaryWithRarityAndTalentLevel = new Dictionary<ulong, List<CardTalentHintUpgrade>>();
            _db = db;
        }


        public CardTalentHintUpgrade Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCardTalentHintUpgrade");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCardTalentHintUpgrade", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CardTalentHintUpgrade _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CardTalentHintUpgrade();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardTalentHintUpgrade");
                return null;
            }

            // SELECT `rarity`,`talent_level`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CardTalentHintUpgrade orm = null;

            if (query.Step())
            {
                int rarity         = (int)query.GetInt(0);
                int talentLevel    = (int)query.GetInt(1);
                int itemCategory1  = (int)query.GetInt(2);
                int itemId1        = (int)query.GetInt(3);
                int itemDispOrder1 = (int)query.GetInt(4);
                int itemNum1       = (int)query.GetInt(5);
                int itemCategory2  = (int)query.GetInt(6);
                int itemId2        = (int)query.GetInt(7);
                int itemDispOrder2 = (int)query.GetInt(8);
                int itemNum2       = (int)query.GetInt(9);
                int moneyNum       = (int)query.GetInt(10);

                orm = new CardTalentHintUpgrade(id, rarity, talentLevel, itemCategory1, itemId1, itemDispOrder1, itemNum1, itemCategory2, itemId2, itemDispOrder2, itemNum2, moneyNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CardTalentHintUpgrade GetWithRarityAndTalentLevelOrderByIdAsc(int rarity, int talentLevel)
        {
            CardTalentHintUpgrade orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRarityAndTalentLevelOrderByIdAsc(rarity, talentLevel);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", rarity, talentLevel));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CardTalentHintUpgrade _SelectWithRarityAndTalentLevelOrderByIdAsc(int rarity, int talentLevel)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardTalentHintUpgrade_Rarity_TalentLevel();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardTalentHintUpgrade");
                return null;
            }

            // SELECT `id`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade` WHERE `rarity`=? AND `talent_level`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, rarity))      { return null; }
            if (!query.BindInt(2, talentLevel)) { return null; }

            CardTalentHintUpgrade orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRarityAndTalentLevelOrderByIdAsc(query, rarity, talentLevel);
            }

            query.Reset();

            return orm;
        }

        public List<CardTalentHintUpgrade> GetListWithRarityAndTalentLevelOrderByIdAsc(int rarity, int talentLevel)
        {
            ulong key = ((uint)unchecked((ulong)((int)rarity))) | ((((ulong)unchecked((ulong)((int)talentLevel)))) << 32);

            if (!_dictionaryWithRarityAndTalentLevel.ContainsKey(key)) {
                _dictionaryWithRarityAndTalentLevel.Add(key, _ListSelectWithRarityAndTalentLevelOrderByIdAsc(rarity, talentLevel));
            }

            return _dictionaryWithRarityAndTalentLevel[key];
        }

        public List<CardTalentHintUpgrade> MaybeListWithRarityAndTalentLevelOrderByIdAsc(int rarity, int talentLevel)
        {
            List<CardTalentHintUpgrade> list = GetListWithRarityAndTalentLevelOrderByIdAsc(rarity, talentLevel);
            return list.Count > 0 ? list : null;
        }

        private List<CardTalentHintUpgrade> _ListSelectWithRarityAndTalentLevelOrderByIdAsc(int rarity, int talentLevel)
        {
            List<CardTalentHintUpgrade> _list = new List<CardTalentHintUpgrade>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardTalentHintUpgrade_Rarity_TalentLevel();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CardTalentHintUpgrade");
                return null;
            }

            // SELECT `id`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade` WHERE `rarity`=? AND `talent_level`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, rarity))      { return null; }
            if (!query.BindInt(2, talentLevel)) { return null; }

            while (query.Step()) {
                CardTalentHintUpgrade orm = _CreateOrmByQueryResultWithRarityAndTalentLevelOrderByIdAsc(query, rarity, talentLevel);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CardTalentHintUpgrade _CreateOrmByQueryResultWithRarityAndTalentLevelOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int rarity, int talentLevel)
        {
            int id             = (int)query.GetInt(0);
            int itemCategory1  = (int)query.GetInt(1);
            int itemId1        = (int)query.GetInt(2);
            int itemDispOrder1 = (int)query.GetInt(3);
            int itemNum1       = (int)query.GetInt(4);
            int itemCategory2  = (int)query.GetInt(5);
            int itemId2        = (int)query.GetInt(6);
            int itemDispOrder2 = (int)query.GetInt(7);
            int itemNum2       = (int)query.GetInt(8);
            int moneyNum       = (int)query.GetInt(9);

            return new CardTalentHintUpgrade(id, rarity, talentLevel, itemCategory1, itemId1, itemDispOrder1, itemNum1, itemCategory2, itemId2, itemDispOrder2, itemNum2, moneyNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithRarityAndTalentLevel.Clear();
        }

        public sealed partial class CardTalentHintUpgrade
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: talent_level) </summary>
            public readonly int TalentLevel;
            /// <summary> (CSV column: item_category_1) </summary>
            public readonly int ItemCategory1;
            /// <summary> (CSV column: item_id_1) </summary>
            public readonly int ItemId1;
            /// <summary> (CSV column: item_disp_order_1) </summary>
            public readonly int ItemDispOrder1;
            /// <summary> (CSV column: item_num_1) </summary>
            public readonly int ItemNum1;
            /// <summary> (CSV column: item_category_2) </summary>
            public readonly int ItemCategory2;
            /// <summary> (CSV column: item_id_2) </summary>
            public readonly int ItemId2;
            /// <summary> (CSV column: item_disp_order_2) </summary>
            public readonly int ItemDispOrder2;
            /// <summary> (CSV column: item_num_2) </summary>
            public readonly int ItemNum2;
            /// <summary> (CSV column: money_num) </summary>
            public readonly int MoneyNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CardTalentHintUpgrade(int id = 0, int rarity = 0, int talentLevel = 0, int itemCategory1 = 0, int itemId1 = 0, int itemDispOrder1 = 0, int itemNum1 = 0, int itemCategory2 = 0, int itemId2 = 0, int itemDispOrder2 = 0, int itemNum2 = 0, int moneyNum = 0)
            {
                this.Id             = id;
                this.Rarity         = rarity;
                this.TalentLevel    = talentLevel;
                this.ItemCategory1  = itemCategory1;
                this.ItemId1        = itemId1;
                this.ItemDispOrder1 = itemDispOrder1;
                this.ItemNum1       = itemNum1;
                this.ItemCategory2  = itemCategory2;
                this.ItemId2        = itemId2;
                this.ItemDispOrder2 = itemDispOrder2;
                this.ItemNum2       = itemNum2;
                this.MoneyNum       = moneyNum;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_upgrade_speciality
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:scenario_id], [:base_skill_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillUpgradeSpeciality : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_upgrade_speciality";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillUpgradeSpeciality> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SkillUpgradeSpeciality>> _dictionaryWithScenarioId = null;
        private Dictionary<int, List<SkillUpgradeSpeciality>> _dictionaryWithBaseSkillId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillUpgradeSpeciality(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillUpgradeSpeciality>();
            _dictionaryWithScenarioId = new Dictionary<int, List<SkillUpgradeSpeciality>>();
            _dictionaryWithBaseSkillId = new Dictionary<int, List<SkillUpgradeSpeciality>>();
            _db = db;
        }


        public SkillUpgradeSpeciality Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillUpgradeSpeciality");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillUpgradeSpeciality", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeSpeciality _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillUpgradeSpeciality();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeSpeciality");
                return null;
            }

            // SELECT `scenario_id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillUpgradeSpeciality orm = null;

            if (query.Step())
            {
                int scenarioId  = (int)query.GetInt(0);
                int baseSkillId = (int)query.GetInt(1);
                int skillId     = (int)query.GetInt(2);
                long startDate  = (long)query.GetLong(3);

                orm = new SkillUpgradeSpeciality(id, scenarioId, baseSkillId, skillId, startDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SkillUpgradeSpeciality GetWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            SkillUpgradeSpeciality orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithScenarioIdOrderByIdAsc(scenarioId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", scenarioId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeSpeciality _SelectWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeSpeciality_ScenarioId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeSpeciality");
                return null;
            }

            // SELECT `id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `scenario_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            SkillUpgradeSpeciality orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(query, scenarioId);
            }

            query.Reset();

            return orm;
        }

        public List<SkillUpgradeSpeciality> GetListWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            int key = (int)scenarioId;

            if (!_dictionaryWithScenarioId.ContainsKey(key)) {
                _dictionaryWithScenarioId.Add(key, _ListSelectWithScenarioIdOrderByIdAsc(scenarioId));
            }

            return _dictionaryWithScenarioId[key];
        }

        public List<SkillUpgradeSpeciality> MaybeListWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            List<SkillUpgradeSpeciality> list = GetListWithScenarioIdOrderByIdAsc(scenarioId);
            return list.Count > 0 ? list : null;
        }

        private List<SkillUpgradeSpeciality> _ListSelectWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            List<SkillUpgradeSpeciality> _list = new List<SkillUpgradeSpeciality>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeSpeciality_ScenarioId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeSpeciality");
                return null;
            }

            // SELECT `id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `scenario_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            while (query.Step()) {
                SkillUpgradeSpeciality orm = _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(query, scenarioId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillUpgradeSpeciality _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int scenarioId)
        {
            int id          = (int)query.GetInt(0);
            int baseSkillId = (int)query.GetInt(1);
            int skillId     = (int)query.GetInt(2);
            long startDate  = (long)query.GetLong(3);

            return new SkillUpgradeSpeciality(id, scenarioId, baseSkillId, skillId, startDate);
        }

        public SkillUpgradeSpeciality GetWithBaseSkillIdOrderByScenarioIdAsc(int baseSkillId)
        {
            SkillUpgradeSpeciality orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithBaseSkillIdOrderByScenarioIdAsc(baseSkillId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", baseSkillId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeSpeciality _SelectWithBaseSkillIdOrderByScenarioIdAsc(int baseSkillId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeSpeciality_BaseSkillId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeSpeciality");
                return null;
            }

            // SELECT `id`,`scenario_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `base_skill_id`=? ORDER BY `scenario_id` ASC;
            if (!query.BindInt(1, baseSkillId)) { return null; }

            SkillUpgradeSpeciality orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithBaseSkillIdOrderByScenarioIdAsc(query, baseSkillId);
            }

            query.Reset();

            return orm;
        }

        public List<SkillUpgradeSpeciality> GetListWithBaseSkillIdOrderByScenarioIdAsc(int baseSkillId)
        {
            int key = (int)baseSkillId;

            if (!_dictionaryWithBaseSkillId.ContainsKey(key)) {
                _dictionaryWithBaseSkillId.Add(key, _ListSelectWithBaseSkillIdOrderByScenarioIdAsc(baseSkillId));
            }

            return _dictionaryWithBaseSkillId[key];
        }

        public List<SkillUpgradeSpeciality> MaybeListWithBaseSkillIdOrderByScenarioIdAsc(int baseSkillId)
        {
            List<SkillUpgradeSpeciality> list = GetListWithBaseSkillIdOrderByScenarioIdAsc(baseSkillId);
            return list.Count > 0 ? list : null;
        }

        private List<SkillUpgradeSpeciality> _ListSelectWithBaseSkillIdOrderByScenarioIdAsc(int baseSkillId)
        {
            List<SkillUpgradeSpeciality> _list = new List<SkillUpgradeSpeciality>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeSpeciality_BaseSkillId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeSpeciality");
                return null;
            }

            // SELECT `id`,`scenario_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `base_skill_id`=? ORDER BY `scenario_id` ASC;
            if (!query.BindInt(1, baseSkillId)) { return null; }

            while (query.Step()) {
                SkillUpgradeSpeciality orm = _CreateOrmByQueryResultWithBaseSkillIdOrderByScenarioIdAsc(query, baseSkillId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillUpgradeSpeciality _CreateOrmByQueryResultWithBaseSkillIdOrderByScenarioIdAsc(LibNative.Sqlite3.PreparedQuery query, int baseSkillId)
        {
            int id         = (int)query.GetInt(0);
            int scenarioId = (int)query.GetInt(1);
            int skillId    = (int)query.GetInt(2);
            long startDate = (long)query.GetLong(3);

            return new SkillUpgradeSpeciality(id, scenarioId, baseSkillId, skillId, startDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithScenarioId.Clear();
            _dictionaryWithBaseSkillId.Clear();
        }

        public sealed partial class SkillUpgradeSpeciality
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: scenario_id) </summary>
            public readonly int ScenarioId;
            /// <summary> (CSV column: base_skill_id) </summary>
            public readonly int BaseSkillId;
            /// <summary> (CSV column: skill_id) </summary>
            public readonly int SkillId;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillUpgradeSpeciality(int id = 0, int scenarioId = 0, int baseSkillId = 0, int skillId = 0, long startDate = 0)
            {
                this.Id          = id;
                this.ScenarioId  = scenarioId;
                this.BaseSkillId = baseSkillId;
                this.SkillId     = skillId;
                this.StartDate   = startDate;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/character_system_text
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:character_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharacterSystemText : AbstractMasterData
    {
        public const string TABLE_NAME = "character_system_text";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<ulong> _notFounds = null;

        // cache dictionary
        private Dictionary<ulong, CharacterSystemText> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<CharacterSystemText>> _dictionaryWithCharacterId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharacterSystemText(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<ulong, CharacterSystemText>();
            _dictionaryWithCharacterId = new Dictionary<int, List<CharacterSystemText>>();
            _db = db;
        }


        public ulong GetKey(int characterId, int voiceId)
        {
            return ((uint)unchecked((ulong)((int)characterId))) | ((((ulong)unchecked((ulong)((int)voiceId)))) << 32);
        }

        public CharacterSystemText Get(ulong key)
        {
            int characterId = (int)unchecked((int)((key >> 0) & 0xFFFFFFFF));
            int voiceId     = (int)unchecked((int)((key >> 32) & 0xFFFFFFFF));

            return Get(characterId, voiceId);
        }

        public CharacterSystemText Get(int characterId, int voiceId)
        {
            ulong key = ((uint)unchecked((ulong)((int)characterId))) | ((((ulong)unchecked((ulong)((int)voiceId)))) << 32);

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharacterSystemText");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(characterId, voiceId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<ulong>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharacterSystemText", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharacterSystemText _SelectOne(int characterId, int voiceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharacterSystemText();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemText");
                return null;
            }

            // SELECT `text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `character_id`=? AND `voice_id`=?;
            if (!query.BindInt(1, characterId)) { return null; }
            if (!query.BindInt(2, voiceId))     { return null; }

            CharacterSystemText orm = null;

            if (query.Step())
            {
                string text           = query.GetText(0);
                string cueSheet       = query.GetText(1);
                int cueId             = (int)query.GetInt(2);
                int motionSet         = (int)query.GetInt(3);
                int scene             = (int)query.GetInt(4);
                int useGallery        = (int)query.GetInt(5);
                int cardId            = (int)query.GetInt(6);
                string lipSyncData    = query.GetText(7);
                int gender            = (int)query.GetInt(8);
                int motionSecondSet   = (int)query.GetInt(9);
                int motionSecondStart = (int)query.GetInt(10);
                long startDate        = (long)query.GetLong(11);

                orm = new CharacterSystemText(characterId, voiceId, text, cueSheet, cueId, motionSet, scene, useGallery, cardId, lipSyncData, gender, motionSecondSet, motionSecondStart, startDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0} {1}", characterId, voiceId));
            }

            query.Reset();

            return orm;
        }

        public CharacterSystemText GetWithCharacterId(int characterId)
        {
            CharacterSystemText orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharacterId(characterId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", characterId));
                } else {
                    ulong key = ((uint)unchecked((ulong)((int)orm.CharacterId))) | ((((ulong)unchecked((ulong)((int)orm.VoiceId)))) << 32);

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharacterSystemText _SelectWithCharacterId(int characterId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemText_CharacterId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemText");
                return null;
            }

            // SELECT `voice_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `character_id`=?;
            if (!query.BindInt(1, characterId)) { return null; }

            CharacterSystemText orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharacterId(query, characterId);
            }

            query.Reset();

            return orm;
        }

        public List<CharacterSystemText> GetListWithCharacterId(int characterId)
        {
            int key = (int)characterId;

            if (!_dictionaryWithCharacterId.ContainsKey(key)) {
                _dictionaryWithCharacterId.Add(key, _ListSelectWithCharacterId(characterId));
            }

            return _dictionaryWithCharacterId[key];
        }

        public List<CharacterSystemText> MaybeListWithCharacterId(int characterId)
        {
            List<CharacterSystemText> list = GetListWithCharacterId(characterId);
            return list.Count > 0 ? list : null;
        }

        private List<CharacterSystemText> _ListSelectWithCharacterId(int characterId)
        {
            List<CharacterSystemText> _list = new List<CharacterSystemText>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemText_CharacterId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemText");
                return null;
            }

            // SELECT `voice_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `character_id`=?;
            if (!query.BindInt(1, characterId)) { return null; }

            while (query.Step()) {
                CharacterSystemText orm = _CreateOrmByQueryResultWithCharacterId(query, characterId);
                ulong key = ((uint)unchecked((ulong)((int)orm.CharacterId))) | ((((ulong)unchecked((ulong)((int)orm.VoiceId)))) << 32);

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharacterSystemText _CreateOrmByQueryResultWithCharacterId(LibNative.Sqlite3.PreparedQuery query, int characterId)
        {
            int voiceId           = (int)query.GetInt(0);
            string text           = query.GetText(1);
            string cueSheet       = query.GetText(2);
            int cueId             = (int)query.GetInt(3);
            int motionSet         = (int)query.GetInt(4);
            int scene             = (int)query.GetInt(5);
            int useGallery        = (int)query.GetInt(6);
            int cardId            = (int)query.GetInt(7);
            string lipSyncData    = query.GetText(8);
            int gender            = (int)query.GetInt(9);
            int motionSecondSet   = (int)query.GetInt(10);
            int motionSecondStart = (int)query.GetInt(11);
            long startDate        = (long)query.GetLong(12);

            return new CharacterSystemText(characterId, voiceId, text, cueSheet, cueId, motionSet, scene, useGallery, cardId, lipSyncData, gender, motionSecondSet, motionSecondStart, startDate);
        }

        public CharacterSystemText GetWithVoiceId(int voiceId)
        {
            CharacterSystemText orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithVoiceId(voiceId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", voiceId));
                } else {
                    ulong key = ((uint)unchecked((ulong)((int)orm.CharacterId))) | ((((ulong)unchecked((ulong)((int)orm.VoiceId)))) << 32);

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharacterSystemText _SelectWithVoiceId(int voiceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemText_VoiceId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemText");
                return null;
            }

            // SELECT `character_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `voice_id`=?;
            if (!query.BindInt(1, voiceId)) { return null; }

            CharacterSystemText orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithVoiceId(query, voiceId);
            }

            query.Reset();

            return orm;
        }

        private CharacterSystemText _CreateOrmByQueryResultWithVoiceId(LibNative.Sqlite3.PreparedQuery query, int voiceId)
        {
            int characterId       = (int)query.GetInt(0);
            string text           = query.GetText(1);
            string cueSheet       = query.GetText(2);
            int cueId             = (int)query.GetInt(3);
            int motionSet         = (int)query.GetInt(4);
            int scene             = (int)query.GetInt(5);
            int useGallery        = (int)query.GetInt(6);
            int cardId            = (int)query.GetInt(7);
            string lipSyncData    = query.GetText(8);
            int gender            = (int)query.GetInt(9);
            int motionSecondSet   = (int)query.GetInt(10);
            int motionSecondStart = (int)query.GetInt(11);
            long startDate        = (long)query.GetLong(12);

            return new CharacterSystemText(characterId, voiceId, text, cueSheet, cueId, motionSet, scene, useGallery, cardId, lipSyncData, gender, motionSecondSet, motionSecondStart, startDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharacterId.Clear();
        }

        public sealed partial class CharacterSystemText
        {
            /// <summary> (CSV column: character_id) </summary>
            public readonly int CharacterId;
            /// <summary> (CSV column: voice_id) </summary>
            public readonly int VoiceId;
            /// <summary> (CSV column: text) </summary>
            public readonly string Text;
            /// <summary> (CSV column: cue_sheet) </summary>
            public readonly string CueSheet;
            /// <summary> (CSV column: cue_id) </summary>
            public readonly int CueId;
            /// <summary> (CSV column: motion_set) </summary>
            public readonly int MotionSet;
            /// <summary> (CSV column: scene) </summary>
            public readonly int Scene;
            /// <summary> (CSV column: use_gallery) </summary>
            public readonly int UseGallery;
            /// <summary> (CSV column: card_id) </summary>
            public readonly int CardId;
            /// <summary> (CSV column: lip_sync_data) </summary>
            public readonly string LipSyncData;
            /// <summary> (CSV column: gender) </summary>
            public readonly int Gender;
            /// <summary> (CSV column: motion_second_set) </summary>
            public readonly int MotionSecondSet;
            /// <summary> (CSV column: motion_second_start) </summary>
            public readonly int MotionSecondStart;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharacterSystemText(int characterId = 0, int voiceId = 0, string text = "", string cueSheet = "", int cueId = 0, int motionSet = 0, int scene = 0, int useGallery = 0, int cardId = 0, string lipSyncData = "", int gender = 0, int motionSecondSet = 0, int motionSecondStart = 0, long startDate = 0)
            {
                this.CharacterId       = characterId;
                this.VoiceId           = voiceId;
                this.Text              = text;
                this.CueSheet          = cueSheet;
                this.CueId             = cueId;
                this.MotionSet         = motionSet;
                this.Scene             = scene;
                this.UseGallery        = useGallery;
                this.CardId            = cardId;
                this.LipSyncData       = lipSyncData;
                this.Gender            = gender;
                this.MotionSecondSet   = motionSecondSet;
                this.MotionSecondStart = motionSecondStart;
                this.StartDate         = startDate;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_effect_filter_group
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardEffectFilterGroup : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_effect_filter_group";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardEffectFilterGroup> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SupportCardEffectFilterGroup> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSupportCardEffectFilterGroup");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardEffectFilterGroup(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardEffectFilterGroup>();
            _db = db;
        }


        public SupportCardEffectFilterGroup Get(int groupId)
        {
            int key = (int)groupId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardEffectFilterGroup");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(groupId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardEffectFilterGroup", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardEffectFilterGroup _SelectOne(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardEffectFilterGroup();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardEffectFilterGroup");
                return null;
            }

            // SELECT `sort_id`,`start_date` FROM `support_card_effect_filter_group` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            SupportCardEffectFilterGroup orm = null;

            if (query.Step())
            {
                int sortId     = (int)query.GetInt(0);
                long startDate = (long)query.GetLong(1);

                orm = new SupportCardEffectFilterGroup(groupId, sortId, startDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", groupId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SupportCardEffectFilterGroup()) {
                while (query.Step()) {
                    int groupId    = (int)query.GetInt(0);
                    int sortId     = (int)query.GetInt(1);
                    long startDate = (long)query.GetLong(2);

                    int key = (int)groupId;
                    SupportCardEffectFilterGroup orm = new SupportCardEffectFilterGroup(groupId, sortId, startDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SupportCardEffectFilterGroup
        {
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: sort_id) </summary>
            public readonly int SortId;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardEffectFilterGroup(int groupId = 0, int sortId = 0, long startDate = 0)
            {
                this.GroupId   = groupId;
                this.SortId    = sortId;
                this.StartDate = startDate;
            }
        }
    }
}
#endif

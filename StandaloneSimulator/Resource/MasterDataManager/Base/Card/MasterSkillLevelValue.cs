// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_level_value
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:ability_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillLevelValue : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_level_value";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillLevelValue> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SkillLevelValue>> _dictionaryWithAbilityType = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SkillLevelValue> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSkillLevelValue");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillLevelValue(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillLevelValue>();
            _dictionaryWithAbilityType = new Dictionary<int, List<SkillLevelValue>>();
            _db = db;
        }


        public SkillLevelValue Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillLevelValue");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillLevelValue", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillLevelValue _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillLevelValue();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillLevelValue");
                return null;
            }

            // SELECT `ability_type`,`level`,`float_ability_value_coef` FROM `skill_level_value` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillLevelValue orm = null;

            if (query.Step())
            {
                int abilityType           = (int)query.GetInt(0);
                int level                 = (int)query.GetInt(1);
                int floatAbilityValueCoef = (int)query.GetInt(2);

                orm = new SkillLevelValue(id, abilityType, level, floatAbilityValueCoef);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SkillLevelValue GetWithAbilityTypeOrderByLevelAsc(int abilityType)
        {
            SkillLevelValue orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithAbilityTypeOrderByLevelAsc(abilityType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", abilityType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillLevelValue _SelectWithAbilityTypeOrderByLevelAsc(int abilityType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillLevelValue_AbilityType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillLevelValue");
                return null;
            }

            // SELECT `id`,`level`,`float_ability_value_coef` FROM `skill_level_value` WHERE `ability_type`=? ORDER BY `level` ASC;
            if (!query.BindInt(1, abilityType)) { return null; }

            SkillLevelValue orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithAbilityTypeOrderByLevelAsc(query, abilityType);
            }

            query.Reset();

            return orm;
        }

        public List<SkillLevelValue> GetListWithAbilityTypeOrderByLevelAsc(int abilityType)
        {
            int key = (int)abilityType;

            if (!_dictionaryWithAbilityType.ContainsKey(key)) {
                _dictionaryWithAbilityType.Add(key, _ListSelectWithAbilityTypeOrderByLevelAsc(abilityType));
            }

            return _dictionaryWithAbilityType[key];
        }

        public List<SkillLevelValue> MaybeListWithAbilityTypeOrderByLevelAsc(int abilityType)
        {
            List<SkillLevelValue> list = GetListWithAbilityTypeOrderByLevelAsc(abilityType);
            return list.Count > 0 ? list : null;
        }

        private List<SkillLevelValue> _ListSelectWithAbilityTypeOrderByLevelAsc(int abilityType)
        {
            List<SkillLevelValue> _list = new List<SkillLevelValue>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillLevelValue_AbilityType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillLevelValue");
                return null;
            }

            // SELECT `id`,`level`,`float_ability_value_coef` FROM `skill_level_value` WHERE `ability_type`=? ORDER BY `level` ASC;
            if (!query.BindInt(1, abilityType)) { return null; }

            while (query.Step()) {
                SkillLevelValue orm = _CreateOrmByQueryResultWithAbilityTypeOrderByLevelAsc(query, abilityType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillLevelValue _CreateOrmByQueryResultWithAbilityTypeOrderByLevelAsc(LibNative.Sqlite3.PreparedQuery query, int abilityType)
        {
            int id                    = (int)query.GetInt(0);
            int level                 = (int)query.GetInt(1);
            int floatAbilityValueCoef = (int)query.GetInt(2);

            return new SkillLevelValue(id, abilityType, level, floatAbilityValueCoef);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithAbilityType.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SkillLevelValue()) {
                while (query.Step()) {
                    int id                    = (int)query.GetInt(0);
                    int abilityType           = (int)query.GetInt(1);
                    int level                 = (int)query.GetInt(2);
                    int floatAbilityValueCoef = (int)query.GetInt(3);

                    int key = (int)id;
                    SkillLevelValue orm = new SkillLevelValue(id, abilityType, level, floatAbilityValueCoef);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SkillLevelValue
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: ability_type) </summary>
            public readonly int AbilityType;
            /// <summary> (CSV column: level) </summary>
            public readonly int Level;
            /// <summary> (CSV column: float_ability_value_coef) </summary>
            public readonly int FloatAbilityValueCoef;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillLevelValue(int id = 0, int abilityType = 0, int level = 0, int floatAbilityValueCoef = 0)
            {
                this.Id                    = id;
                this.AbilityType           = abilityType;
                this.Level                 = level;
                this.FloatAbilityValueCoef = floatAbilityValueCoef;
            }
        }
    }
}
#endif

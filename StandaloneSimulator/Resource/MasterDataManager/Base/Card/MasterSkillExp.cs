// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_exp
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillExp : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_exp";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillExp> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SkillExp>> _dictionaryWithType = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SkillExp> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSkillExp");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillExp(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillExp>();
            _dictionaryWithType = new Dictionary<int, List<SkillExp>>();
            _db = db;
        }


        public SkillExp Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillExp");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillExp", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillExp _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillExp();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillExp");
                return null;
            }

            // SELECT `type`,`level`,`scale` FROM `skill_exp` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillExp orm = null;

            if (query.Step())
            {
                int type  = (int)query.GetInt(0);
                int level = (int)query.GetInt(1);
                int scale = (int)query.GetInt(2);

                orm = new SkillExp(id, type, level, scale);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SkillExp GetWithTypeOrderByLevelAsc(int type)
        {
            SkillExp orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithTypeOrderByLevelAsc(type);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", type));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillExp _SelectWithTypeOrderByLevelAsc(int type)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillExp_Type();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillExp");
                return null;
            }

            // SELECT `id`,`level`,`scale` FROM `skill_exp` WHERE `type`=? ORDER BY `level` ASC;
            if (!query.BindInt(1, type)) { return null; }

            SkillExp orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithTypeOrderByLevelAsc(query, type);
            }

            query.Reset();

            return orm;
        }

        public List<SkillExp> GetListWithTypeOrderByLevelAsc(int type)
        {
            int key = (int)type;

            if (!_dictionaryWithType.ContainsKey(key)) {
                _dictionaryWithType.Add(key, _ListSelectWithTypeOrderByLevelAsc(type));
            }

            return _dictionaryWithType[key];
        }

        public List<SkillExp> MaybeListWithTypeOrderByLevelAsc(int type)
        {
            List<SkillExp> list = GetListWithTypeOrderByLevelAsc(type);
            return list.Count > 0 ? list : null;
        }

        private List<SkillExp> _ListSelectWithTypeOrderByLevelAsc(int type)
        {
            List<SkillExp> _list = new List<SkillExp>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillExp_Type();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillExp");
                return null;
            }

            // SELECT `id`,`level`,`scale` FROM `skill_exp` WHERE `type`=? ORDER BY `level` ASC;
            if (!query.BindInt(1, type)) { return null; }

            while (query.Step()) {
                SkillExp orm = _CreateOrmByQueryResultWithTypeOrderByLevelAsc(query, type);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillExp _CreateOrmByQueryResultWithTypeOrderByLevelAsc(LibNative.Sqlite3.PreparedQuery query, int type)
        {
            int id    = (int)query.GetInt(0);
            int level = (int)query.GetInt(1);
            int scale = (int)query.GetInt(2);

            return new SkillExp(id, type, level, scale);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithType.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SkillExp()) {
                while (query.Step()) {
                    int id    = (int)query.GetInt(0);
                    int type  = (int)query.GetInt(1);
                    int level = (int)query.GetInt(2);
                    int scale = (int)query.GetInt(3);

                    int key = (int)id;
                    SkillExp orm = new SkillExp(id, type, level, scale);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SkillExp
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: type) </summary>
            public readonly int Type;
            /// <summary> (CSV column: level) </summary>
            public readonly int Level;
            /// <summary> (CSV column: scale) </summary>
            public readonly int Scale;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillExp(int id = 0, int type = 0, int level = 0, int scale = 0)
            {
                this.Id    = id;
                this.Type  = type;
                this.Level = level;
                this.Scale = scale;
            }
        }
    }
}
#endif

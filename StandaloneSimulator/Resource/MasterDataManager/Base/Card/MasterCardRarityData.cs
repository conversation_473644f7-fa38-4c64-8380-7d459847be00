// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/card_rarity_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:card_id], [:race_dress_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCardRarityData : AbstractMasterData
    {
        public const string TABLE_NAME = "card_rarity_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CardRarityData> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<CardRarityData>> _dictionaryWithCardId = null;
        private Dictionary<int, List<CardRarityData>> _dictionaryWithRaceDressId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, CardRarityData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterCardRarityData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCardRarityData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CardRarityData>();
            _dictionaryWithCardId = new Dictionary<int, List<CardRarityData>>();
            _dictionaryWithRaceDressId = new Dictionary<int, List<CardRarityData>>();
            _db = db;
        }


        public CardRarityData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCardRarityData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCardRarityData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CardRarityData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CardRarityData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardRarityData");
                return null;
            }

            // SELECT `card_id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CardRarityData orm = null;

            if (query.Step())
            {
                int cardId                   = (int)query.GetInt(0);
                int rarity                   = (int)query.GetInt(1);
                int raceDressId              = (int)query.GetInt(2);
                int skillSet                 = (int)query.GetInt(3);
                int speed                    = (int)query.GetInt(4);
                int stamina                  = (int)query.GetInt(5);
                int pow                      = (int)query.GetInt(6);
                int guts                     = (int)query.GetInt(7);
                int wiz                      = (int)query.GetInt(8);
                int maxSpeed                 = (int)query.GetInt(9);
                int maxStamina               = (int)query.GetInt(10);
                int maxPow                   = (int)query.GetInt(11);
                int maxGuts                  = (int)query.GetInt(12);
                int maxWiz                   = (int)query.GetInt(13);
                int properDistanceShort      = (int)query.GetInt(14);
                int properDistanceMile       = (int)query.GetInt(15);
                int properDistanceMiddle     = (int)query.GetInt(16);
                int properDistanceLong       = (int)query.GetInt(17);
                int properRunningStyleNige   = (int)query.GetInt(18);
                int properRunningStyleSenko  = (int)query.GetInt(19);
                int properRunningStyleSashi  = (int)query.GetInt(20);
                int properRunningStyleOikomi = (int)query.GetInt(21);
                int properGroundTurf         = (int)query.GetInt(22);
                int properGroundDirt         = (int)query.GetInt(23);
                int getDressId1              = (int)query.GetInt(24);
                int getDressId2              = (int)query.GetInt(25);

                orm = new CardRarityData(id, cardId, rarity, raceDressId, skillSet, speed, stamina, pow, guts, wiz, maxSpeed, maxStamina, maxPow, maxGuts, maxWiz, properDistanceShort, properDistanceMile, properDistanceMiddle, properDistanceLong, properRunningStyleNige, properRunningStyleSenko, properRunningStyleSashi, properRunningStyleOikomi, properGroundTurf, properGroundDirt, getDressId1, getDressId2);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CardRarityData GetWithCardIdOrderByRarityAsc(int cardId)
        {
            CardRarityData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCardIdOrderByRarityAsc(cardId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", cardId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CardRarityData _SelectWithCardIdOrderByRarityAsc(int cardId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardRarityData_CardId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardRarityData");
                return null;
            }

            // SELECT `id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `card_id`=? ORDER BY `rarity` ASC;
            if (!query.BindInt(1, cardId)) { return null; }

            CardRarityData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCardIdOrderByRarityAsc(query, cardId);
            }

            query.Reset();

            return orm;
        }

        public List<CardRarityData> GetListWithCardIdOrderByRarityAsc(int cardId)
        {
            int key = (int)cardId;

            if (!_dictionaryWithCardId.ContainsKey(key)) {
                _dictionaryWithCardId.Add(key, _ListSelectWithCardIdOrderByRarityAsc(cardId));
            }

            return _dictionaryWithCardId[key];
        }

        public List<CardRarityData> MaybeListWithCardIdOrderByRarityAsc(int cardId)
        {
            List<CardRarityData> list = GetListWithCardIdOrderByRarityAsc(cardId);
            return list.Count > 0 ? list : null;
        }

        private List<CardRarityData> _ListSelectWithCardIdOrderByRarityAsc(int cardId)
        {
            List<CardRarityData> _list = new List<CardRarityData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardRarityData_CardId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CardRarityData");
                return null;
            }

            // SELECT `id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `card_id`=? ORDER BY `rarity` ASC;
            if (!query.BindInt(1, cardId)) { return null; }

            while (query.Step()) {
                CardRarityData orm = _CreateOrmByQueryResultWithCardIdOrderByRarityAsc(query, cardId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CardRarityData _CreateOrmByQueryResultWithCardIdOrderByRarityAsc(LibNative.Sqlite3.PreparedQuery query, int cardId)
        {
            int id                       = (int)query.GetInt(0);
            int rarity                   = (int)query.GetInt(1);
            int raceDressId              = (int)query.GetInt(2);
            int skillSet                 = (int)query.GetInt(3);
            int speed                    = (int)query.GetInt(4);
            int stamina                  = (int)query.GetInt(5);
            int pow                      = (int)query.GetInt(6);
            int guts                     = (int)query.GetInt(7);
            int wiz                      = (int)query.GetInt(8);
            int maxSpeed                 = (int)query.GetInt(9);
            int maxStamina               = (int)query.GetInt(10);
            int maxPow                   = (int)query.GetInt(11);
            int maxGuts                  = (int)query.GetInt(12);
            int maxWiz                   = (int)query.GetInt(13);
            int properDistanceShort      = (int)query.GetInt(14);
            int properDistanceMile       = (int)query.GetInt(15);
            int properDistanceMiddle     = (int)query.GetInt(16);
            int properDistanceLong       = (int)query.GetInt(17);
            int properRunningStyleNige   = (int)query.GetInt(18);
            int properRunningStyleSenko  = (int)query.GetInt(19);
            int properRunningStyleSashi  = (int)query.GetInt(20);
            int properRunningStyleOikomi = (int)query.GetInt(21);
            int properGroundTurf         = (int)query.GetInt(22);
            int properGroundDirt         = (int)query.GetInt(23);
            int getDressId1              = (int)query.GetInt(24);
            int getDressId2              = (int)query.GetInt(25);

            return new CardRarityData(id, cardId, rarity, raceDressId, skillSet, speed, stamina, pow, guts, wiz, maxSpeed, maxStamina, maxPow, maxGuts, maxWiz, properDistanceShort, properDistanceMile, properDistanceMiddle, properDistanceLong, properRunningStyleNige, properRunningStyleSenko, properRunningStyleSashi, properRunningStyleOikomi, properGroundTurf, properGroundDirt, getDressId1, getDressId2);
        }

        public CardRarityData GetWithRaceDressIdOrderByIdAsc(int raceDressId)
        {
            CardRarityData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceDressIdOrderByIdAsc(raceDressId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceDressId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CardRarityData _SelectWithRaceDressIdOrderByIdAsc(int raceDressId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardRarityData_RaceDressId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardRarityData");
                return null;
            }

            // SELECT `id`,`card_id`,`rarity`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `race_dress_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceDressId)) { return null; }

            CardRarityData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceDressIdOrderByIdAsc(query, raceDressId);
            }

            query.Reset();

            return orm;
        }

        public List<CardRarityData> GetListWithRaceDressIdOrderByIdAsc(int raceDressId)
        {
            int key = (int)raceDressId;

            if (!_dictionaryWithRaceDressId.ContainsKey(key)) {
                _dictionaryWithRaceDressId.Add(key, _ListSelectWithRaceDressIdOrderByIdAsc(raceDressId));
            }

            return _dictionaryWithRaceDressId[key];
        }

        public List<CardRarityData> MaybeListWithRaceDressIdOrderByIdAsc(int raceDressId)
        {
            List<CardRarityData> list = GetListWithRaceDressIdOrderByIdAsc(raceDressId);
            return list.Count > 0 ? list : null;
        }

        private List<CardRarityData> _ListSelectWithRaceDressIdOrderByIdAsc(int raceDressId)
        {
            List<CardRarityData> _list = new List<CardRarityData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardRarityData_RaceDressId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CardRarityData");
                return null;
            }

            // SELECT `id`,`card_id`,`rarity`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `race_dress_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceDressId)) { return null; }

            while (query.Step()) {
                CardRarityData orm = _CreateOrmByQueryResultWithRaceDressIdOrderByIdAsc(query, raceDressId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CardRarityData _CreateOrmByQueryResultWithRaceDressIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int raceDressId)
        {
            int id                       = (int)query.GetInt(0);
            int cardId                   = (int)query.GetInt(1);
            int rarity                   = (int)query.GetInt(2);
            int skillSet                 = (int)query.GetInt(3);
            int speed                    = (int)query.GetInt(4);
            int stamina                  = (int)query.GetInt(5);
            int pow                      = (int)query.GetInt(6);
            int guts                     = (int)query.GetInt(7);
            int wiz                      = (int)query.GetInt(8);
            int maxSpeed                 = (int)query.GetInt(9);
            int maxStamina               = (int)query.GetInt(10);
            int maxPow                   = (int)query.GetInt(11);
            int maxGuts                  = (int)query.GetInt(12);
            int maxWiz                   = (int)query.GetInt(13);
            int properDistanceShort      = (int)query.GetInt(14);
            int properDistanceMile       = (int)query.GetInt(15);
            int properDistanceMiddle     = (int)query.GetInt(16);
            int properDistanceLong       = (int)query.GetInt(17);
            int properRunningStyleNige   = (int)query.GetInt(18);
            int properRunningStyleSenko  = (int)query.GetInt(19);
            int properRunningStyleSashi  = (int)query.GetInt(20);
            int properRunningStyleOikomi = (int)query.GetInt(21);
            int properGroundTurf         = (int)query.GetInt(22);
            int properGroundDirt         = (int)query.GetInt(23);
            int getDressId1              = (int)query.GetInt(24);
            int getDressId2              = (int)query.GetInt(25);

            return new CardRarityData(id, cardId, rarity, raceDressId, skillSet, speed, stamina, pow, guts, wiz, maxSpeed, maxStamina, maxPow, maxGuts, maxWiz, properDistanceShort, properDistanceMile, properDistanceMiddle, properDistanceLong, properRunningStyleNige, properRunningStyleSenko, properRunningStyleSashi, properRunningStyleOikomi, properGroundTurf, properGroundDirt, getDressId1, getDressId2);
        }

        public CardRarityData GetWithCardIdAndRarity(int cardId, int rarity)
        {
            CardRarityData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCardIdAndRarity(cardId, rarity);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", cardId, rarity));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CardRarityData _SelectWithCardIdAndRarity(int cardId, int rarity)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CardRarityData_CardId_Rarity();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CardRarityData");
                return null;
            }

            // SELECT `id`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `card_id`=? AND `rarity`=?;
            if (!query.BindInt(1, cardId)) { return null; }
            if (!query.BindInt(2, rarity)) { return null; }

            CardRarityData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCardIdAndRarity(query, cardId, rarity);
            }

            query.Reset();

            return orm;
        }

        private CardRarityData _CreateOrmByQueryResultWithCardIdAndRarity(LibNative.Sqlite3.PreparedQuery query, int cardId, int rarity)
        {
            int id                       = (int)query.GetInt(0);
            int raceDressId              = (int)query.GetInt(1);
            int skillSet                 = (int)query.GetInt(2);
            int speed                    = (int)query.GetInt(3);
            int stamina                  = (int)query.GetInt(4);
            int pow                      = (int)query.GetInt(5);
            int guts                     = (int)query.GetInt(6);
            int wiz                      = (int)query.GetInt(7);
            int maxSpeed                 = (int)query.GetInt(8);
            int maxStamina               = (int)query.GetInt(9);
            int maxPow                   = (int)query.GetInt(10);
            int maxGuts                  = (int)query.GetInt(11);
            int maxWiz                   = (int)query.GetInt(12);
            int properDistanceShort      = (int)query.GetInt(13);
            int properDistanceMile       = (int)query.GetInt(14);
            int properDistanceMiddle     = (int)query.GetInt(15);
            int properDistanceLong       = (int)query.GetInt(16);
            int properRunningStyleNige   = (int)query.GetInt(17);
            int properRunningStyleSenko  = (int)query.GetInt(18);
            int properRunningStyleSashi  = (int)query.GetInt(19);
            int properRunningStyleOikomi = (int)query.GetInt(20);
            int properGroundTurf         = (int)query.GetInt(21);
            int properGroundDirt         = (int)query.GetInt(22);
            int getDressId1              = (int)query.GetInt(23);
            int getDressId2              = (int)query.GetInt(24);

            return new CardRarityData(id, cardId, rarity, raceDressId, skillSet, speed, stamina, pow, guts, wiz, maxSpeed, maxStamina, maxPow, maxGuts, maxWiz, properDistanceShort, properDistanceMile, properDistanceMiddle, properDistanceLong, properRunningStyleNige, properRunningStyleSenko, properRunningStyleSashi, properRunningStyleOikomi, properGroundTurf, properGroundDirt, getDressId1, getDressId2);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCardId.Clear();
            _dictionaryWithRaceDressId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_CardRarityData()) {
                while (query.Step()) {
                    int id                       = (int)query.GetInt(0);
                    int cardId                   = (int)query.GetInt(1);
                    int rarity                   = (int)query.GetInt(2);
                    int raceDressId              = (int)query.GetInt(3);
                    int skillSet                 = (int)query.GetInt(4);
                    int speed                    = (int)query.GetInt(5);
                    int stamina                  = (int)query.GetInt(6);
                    int pow                      = (int)query.GetInt(7);
                    int guts                     = (int)query.GetInt(8);
                    int wiz                      = (int)query.GetInt(9);
                    int maxSpeed                 = (int)query.GetInt(10);
                    int maxStamina               = (int)query.GetInt(11);
                    int maxPow                   = (int)query.GetInt(12);
                    int maxGuts                  = (int)query.GetInt(13);
                    int maxWiz                   = (int)query.GetInt(14);
                    int properDistanceShort      = (int)query.GetInt(15);
                    int properDistanceMile       = (int)query.GetInt(16);
                    int properDistanceMiddle     = (int)query.GetInt(17);
                    int properDistanceLong       = (int)query.GetInt(18);
                    int properRunningStyleNige   = (int)query.GetInt(19);
                    int properRunningStyleSenko  = (int)query.GetInt(20);
                    int properRunningStyleSashi  = (int)query.GetInt(21);
                    int properRunningStyleOikomi = (int)query.GetInt(22);
                    int properGroundTurf         = (int)query.GetInt(23);
                    int properGroundDirt         = (int)query.GetInt(24);
                    int getDressId1              = (int)query.GetInt(25);
                    int getDressId2              = (int)query.GetInt(26);

                    int key = (int)id;
                    CardRarityData orm = new CardRarityData(id, cardId, rarity, raceDressId, skillSet, speed, stamina, pow, guts, wiz, maxSpeed, maxStamina, maxPow, maxGuts, maxWiz, properDistanceShort, properDistanceMile, properDistanceMiddle, properDistanceLong, properRunningStyleNige, properRunningStyleSenko, properRunningStyleSashi, properRunningStyleOikomi, properGroundTurf, properGroundDirt, getDressId1, getDressId2);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class CardRarityData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: card_id) </summary>
            public readonly int CardId;
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: race_dress_id) </summary>
            public readonly int RaceDressId;
            /// <summary> (CSV column: skill_set) </summary>
            public readonly int SkillSet;
            /// <summary> (CSV column: speed) </summary>
            public readonly int Speed;
            /// <summary> (CSV column: stamina) </summary>
            public readonly int Stamina;
            /// <summary> (CSV column: pow) </summary>
            public readonly int Pow;
            /// <summary> (CSV column: guts) </summary>
            public readonly int Guts;
            /// <summary> (CSV column: wiz) </summary>
            public readonly int Wiz;
            /// <summary> (CSV column: max_speed) </summary>
            public readonly int MaxSpeed;
            /// <summary> (CSV column: max_stamina) </summary>
            public readonly int MaxStamina;
            /// <summary> (CSV column: max_pow) </summary>
            public readonly int MaxPow;
            /// <summary> (CSV column: max_guts) </summary>
            public readonly int MaxGuts;
            /// <summary> (CSV column: max_wiz) </summary>
            public readonly int MaxWiz;
            /// <summary> (CSV column: proper_distance_short) </summary>
            public readonly int ProperDistanceShort;
            /// <summary> (CSV column: proper_distance_mile) </summary>
            public readonly int ProperDistanceMile;
            /// <summary> (CSV column: proper_distance_middle) </summary>
            public readonly int ProperDistanceMiddle;
            /// <summary> (CSV column: proper_distance_long) </summary>
            public readonly int ProperDistanceLong;
            /// <summary> (CSV column: proper_running_style_nige) </summary>
            public readonly int ProperRunningStyleNige;
            /// <summary> (CSV column: proper_running_style_senko) </summary>
            public readonly int ProperRunningStyleSenko;
            /// <summary> (CSV column: proper_running_style_sashi) </summary>
            public readonly int ProperRunningStyleSashi;
            /// <summary> (CSV column: proper_running_style_oikomi) </summary>
            public readonly int ProperRunningStyleOikomi;
            /// <summary> (CSV column: proper_ground_turf) </summary>
            public readonly int ProperGroundTurf;
            /// <summary> (CSV column: proper_ground_dirt) </summary>
            public readonly int ProperGroundDirt;
            /// <summary> (CSV column: get_dress_id_1) </summary>
            public readonly int GetDressId1;
            /// <summary> (CSV column: get_dress_id_2) </summary>
            public readonly int GetDressId2;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CardRarityData(int id = 0, int cardId = 0, int rarity = 0, int raceDressId = 0, int skillSet = 0, int speed = 0, int stamina = 0, int pow = 0, int guts = 0, int wiz = 0, int maxSpeed = 0, int maxStamina = 0, int maxPow = 0, int maxGuts = 0, int maxWiz = 0, int properDistanceShort = 0, int properDistanceMile = 0, int properDistanceMiddle = 0, int properDistanceLong = 0, int properRunningStyleNige = 0, int properRunningStyleSenko = 0, int properRunningStyleSashi = 0, int properRunningStyleOikomi = 0, int properGroundTurf = 0, int properGroundDirt = 0, int getDressId1 = 0, int getDressId2 = 0)
            {
                this.Id                       = id;
                this.CardId                   = cardId;
                this.Rarity                   = rarity;
                this.RaceDressId              = raceDressId;
                this.SkillSet                 = skillSet;
                this.Speed                    = speed;
                this.Stamina                  = stamina;
                this.Pow                      = pow;
                this.Guts                     = guts;
                this.Wiz                      = wiz;
                this.MaxSpeed                 = maxSpeed;
                this.MaxStamina               = maxStamina;
                this.MaxPow                   = maxPow;
                this.MaxGuts                  = maxGuts;
                this.MaxWiz                   = maxWiz;
                this.ProperDistanceShort      = properDistanceShort;
                this.ProperDistanceMile       = properDistanceMile;
                this.ProperDistanceMiddle     = properDistanceMiddle;
                this.ProperDistanceLong       = properDistanceLong;
                this.ProperRunningStyleNige   = properRunningStyleNige;
                this.ProperRunningStyleSenko  = properRunningStyleSenko;
                this.ProperRunningStyleSashi  = properRunningStyleSashi;
                this.ProperRunningStyleOikomi = properRunningStyleOikomi;
                this.ProperGroundTurf         = properGroundTurf;
                this.ProperGroundDirt         = properGroundDirt;
                this.GetDressId1              = getDressId1;
                this.GetDressId2              = getDressId2;
            }
        }
    }
}
#endif

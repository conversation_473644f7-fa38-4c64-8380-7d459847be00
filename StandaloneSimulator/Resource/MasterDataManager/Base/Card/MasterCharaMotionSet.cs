// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_motion_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaMotionSet : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_motion_set";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharaMotionSet> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, CharaMotionSet> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterCharaMotionSet");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaMotionSet(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharaMotionSet>();
            _db = db;
        }


        public CharaMotionSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaMotionSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaMotionSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharaMotionSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaMotionSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaMotionSet");
                return null;
            }

            // SELECT `body_motion`,`body_motion_type`,`body_motion_play_type`,`face_type`,`cheek`,`eye_default`,`ear_motion`,`tail_motion`,`tail_motion_type` FROM `chara_motion_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharaMotionSet orm = null;

            if (query.Step())
            {
                string bodyMotion      = query.GetText(0);
                int bodyMotionType     = (int)query.GetInt(1);
                int bodyMotionPlayType = (int)query.GetInt(2);
                string faceType        = query.GetText(3);
                int cheek              = (int)query.GetInt(4);
                int eyeDefault         = (int)query.GetInt(5);
                string earMotion       = query.GetText(6);
                string tailMotion      = query.GetText(7);
                int tailMotionType     = (int)query.GetInt(8);

                orm = new CharaMotionSet(id, bodyMotion, bodyMotionType, bodyMotionPlayType, faceType, cheek, eyeDefault, earMotion, tailMotion, tailMotionType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_CharaMotionSet()) {
                while (query.Step()) {
                    int id                 = (int)query.GetInt(0);
                    string bodyMotion      = query.GetText(1);
                    int bodyMotionType     = (int)query.GetInt(2);
                    int bodyMotionPlayType = (int)query.GetInt(3);
                    string faceType        = query.GetText(4);
                    int cheek              = (int)query.GetInt(5);
                    int eyeDefault         = (int)query.GetInt(6);
                    string earMotion       = query.GetText(7);
                    string tailMotion      = query.GetText(8);
                    int tailMotionType     = (int)query.GetInt(9);

                    int key = (int)id;
                    CharaMotionSet orm = new CharaMotionSet(id, bodyMotion, bodyMotionType, bodyMotionPlayType, faceType, cheek, eyeDefault, earMotion, tailMotion, tailMotionType);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class CharaMotionSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: body_motion) </summary>
            public readonly string BodyMotion;
            /// <summary> (CSV column: body_motion_type) </summary>
            public readonly int BodyMotionType;
            /// <summary> (CSV column: body_motion_play_type) </summary>
            public readonly int BodyMotionPlayType;
            /// <summary> (CSV column: face_type) </summary>
            public readonly string FaceType;
            /// <summary> (CSV column: cheek) </summary>
            public readonly int Cheek;
            /// <summary> (CSV column: eye_default) </summary>
            public readonly int EyeDefault;
            /// <summary> (CSV column: ear_motion) </summary>
            public readonly string EarMotion;
            /// <summary> (CSV column: tail_motion) </summary>
            public readonly string TailMotion;
            /// <summary> (CSV column: tail_motion_type) </summary>
            public readonly int TailMotionType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaMotionSet(int id = 0, string bodyMotion = "", int bodyMotionType = 0, int bodyMotionPlayType = 0, string faceType = "", int cheek = 0, int eyeDefault = 0, string earMotion = "", string tailMotion = "", int tailMotionType = 0)
            {
                this.Id                 = id;
                this.BodyMotion         = bodyMotion;
                this.BodyMotionType     = bodyMotionType;
                this.BodyMotionPlayType = bodyMotionPlayType;
                this.FaceType           = faceType;
                this.Cheek              = cheek;
                this.EyeDefault         = eyeDefault;
                this.EarMotion          = earMotion;
                this.TailMotion         = tailMotion;
                this.TailMotionType     = tailMotionType;
            }
        }
    }
}
#endif

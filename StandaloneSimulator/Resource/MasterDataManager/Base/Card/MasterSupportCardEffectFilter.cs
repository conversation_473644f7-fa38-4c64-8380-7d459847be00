// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_effect_filter
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardEffectFilter : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_effect_filter";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardEffectFilter> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SupportCardEffectFilter>> _dictionaryWithGroupId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardEffectFilter(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardEffectFilter>();
            _dictionaryWithGroupId = new Dictionary<int, List<SupportCardEffectFilter>>();
            _db = db;
        }


        public SupportCardEffectFilter Get(int type)
        {
            int key = (int)type;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardEffectFilter");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(type);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardEffectFilter", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardEffectFilter _SelectOne(int type)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardEffectFilter();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardEffectFilter");
                return null;
            }

            // SELECT `group_id`,`sort_id`,`start_date` FROM `support_card_effect_filter` WHERE `type`=?;
            if (!query.BindInt(1, type)) { return null; }

            SupportCardEffectFilter orm = null;

            if (query.Step())
            {
                int groupId    = (int)query.GetInt(0);
                int sortId     = (int)query.GetInt(1);
                long startDate = (long)query.GetLong(2);

                orm = new SupportCardEffectFilter(type, groupId, sortId, startDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", type));
            }

            query.Reset();

            return orm;
        }

        public SupportCardEffectFilter GetWithGroupIdOrderBySortIdAsc(int groupId)
        {
            SupportCardEffectFilter orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupIdOrderBySortIdAsc(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Type;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SupportCardEffectFilter _SelectWithGroupIdOrderBySortIdAsc(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardEffectFilter_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardEffectFilter");
                return null;
            }

            // SELECT `type`,`sort_id`,`start_date` FROM `support_card_effect_filter` WHERE `group_id`=? ORDER BY `sort_id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            SupportCardEffectFilter orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupIdOrderBySortIdAsc(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<SupportCardEffectFilter> GetListWithGroupIdOrderBySortIdAsc(int groupId)
        {
            int key = (int)groupId;

            if (!_dictionaryWithGroupId.ContainsKey(key)) {
                _dictionaryWithGroupId.Add(key, _ListSelectWithGroupIdOrderBySortIdAsc(groupId));
            }

            return _dictionaryWithGroupId[key];
        }

        public List<SupportCardEffectFilter> MaybeListWithGroupIdOrderBySortIdAsc(int groupId)
        {
            List<SupportCardEffectFilter> list = GetListWithGroupIdOrderBySortIdAsc(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<SupportCardEffectFilter> _ListSelectWithGroupIdOrderBySortIdAsc(int groupId)
        {
            List<SupportCardEffectFilter> _list = new List<SupportCardEffectFilter>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardEffectFilter_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardEffectFilter");
                return null;
            }

            // SELECT `type`,`sort_id`,`start_date` FROM `support_card_effect_filter` WHERE `group_id`=? ORDER BY `sort_id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                SupportCardEffectFilter orm = _CreateOrmByQueryResultWithGroupIdOrderBySortIdAsc(query, groupId);
                int key = (int)orm.Type;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SupportCardEffectFilter _CreateOrmByQueryResultWithGroupIdOrderBySortIdAsc(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int type       = (int)query.GetInt(0);
            int sortId     = (int)query.GetInt(1);
            long startDate = (long)query.GetLong(2);

            return new SupportCardEffectFilter(type, groupId, sortId, startDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGroupId.Clear();
        }

        public sealed partial class SupportCardEffectFilter
        {
            /// <summary> (CSV column: type) </summary>
            public readonly int Type;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: sort_id) </summary>
            public readonly int SortId;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardEffectFilter(int type = 0, int groupId = 0, int sortId = 0, long startDate = 0)
            {
                this.Type      = type;
                this.GroupId   = groupId;
                this.SortId    = sortId;
                this.StartDate = startDate;
            }
        }
    }
}
#endif

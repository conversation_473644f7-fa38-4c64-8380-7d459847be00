// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillData : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillData> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SkillData>> _dictionaryWithGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SkillData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSkillData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillData>();
            _dictionaryWithGroupId = new Dictionary<int, List<SkillData>>();
            _db = db;
        }


        public SkillData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillData");
                return null;
            }

            // SELECT `rarity`,`group_id`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillData orm = null;

            if (query.Step())
            {
                int rarity                   = (int)query.GetInt(0);
                int groupId                  = (int)query.GetInt(1);
                int groupRate                = (int)query.GetInt(2);
                int filterSwitch             = (int)query.GetInt(3);
                int gradeValue               = (int)query.GetInt(4);
                int skillCategory            = (int)query.GetInt(5);
                string tagId                 = query.GetText(6);
                int uniqueSkillId1           = (int)query.GetInt(7);
                int uniqueSkillId2           = (int)query.GetInt(8);
                int expType                  = (int)query.GetInt(9);
                int potentialPerDefault      = (int)query.GetInt(10);
                int activateLot              = (int)query.GetInt(11);
                int priority                 = (int)query.GetInt(12);
                string precondition1         = query.GetText(13);
                string condition1            = query.GetText(14);
                int floatAbilityTime1        = (int)query.GetInt(15);
                int abilityTimeUsage1        = (int)query.GetInt(16);
                int floatCooldownTime1       = (int)query.GetInt(17);
                int abilityType11            = (int)query.GetInt(18);
                int abilityValueUsage11      = (int)query.GetInt(19);
                int additionalActivateType11 = (int)query.GetInt(20);
                int abilityValueLevelUsage11 = (int)query.GetInt(21);
                int floatAbilityValue11      = (int)query.GetInt(22);
                int targetType11             = (int)query.GetInt(23);
                int targetValue11            = (int)query.GetInt(24);
                int abilityType12            = (int)query.GetInt(25);
                int abilityValueUsage12      = (int)query.GetInt(26);
                int additionalActivateType12 = (int)query.GetInt(27);
                int abilityValueLevelUsage12 = (int)query.GetInt(28);
                int floatAbilityValue12      = (int)query.GetInt(29);
                int targetType12             = (int)query.GetInt(30);
                int targetValue12            = (int)query.GetInt(31);
                int abilityType13            = (int)query.GetInt(32);
                int abilityValueUsage13      = (int)query.GetInt(33);
                int additionalActivateType13 = (int)query.GetInt(34);
                int abilityValueLevelUsage13 = (int)query.GetInt(35);
                int floatAbilityValue13      = (int)query.GetInt(36);
                int targetType13             = (int)query.GetInt(37);
                int targetValue13            = (int)query.GetInt(38);
                string precondition2         = query.GetText(39);
                string condition2            = query.GetText(40);
                int floatAbilityTime2        = (int)query.GetInt(41);
                int abilityTimeUsage2        = (int)query.GetInt(42);
                int floatCooldownTime2       = (int)query.GetInt(43);
                int abilityType21            = (int)query.GetInt(44);
                int abilityValueUsage21      = (int)query.GetInt(45);
                int additionalActivateType21 = (int)query.GetInt(46);
                int abilityValueLevelUsage21 = (int)query.GetInt(47);
                int floatAbilityValue21      = (int)query.GetInt(48);
                int targetType21             = (int)query.GetInt(49);
                int targetValue21            = (int)query.GetInt(50);
                int abilityType22            = (int)query.GetInt(51);
                int abilityValueUsage22      = (int)query.GetInt(52);
                int additionalActivateType22 = (int)query.GetInt(53);
                int abilityValueLevelUsage22 = (int)query.GetInt(54);
                int floatAbilityValue22      = (int)query.GetInt(55);
                int targetType22             = (int)query.GetInt(56);
                int targetValue22            = (int)query.GetInt(57);
                int abilityType23            = (int)query.GetInt(58);
                int abilityValueUsage23      = (int)query.GetInt(59);
                int additionalActivateType23 = (int)query.GetInt(60);
                int abilityValueLevelUsage23 = (int)query.GetInt(61);
                int floatAbilityValue23      = (int)query.GetInt(62);
                int targetType23             = (int)query.GetInt(63);
                int targetValue23            = (int)query.GetInt(64);
                int popularityAddParam1      = (int)query.GetInt(65);
                int popularityAddValue1      = (int)query.GetInt(66);
                int popularityAddParam2      = (int)query.GetInt(67);
                int popularityAddValue2      = (int)query.GetInt(68);
                int dispOrder                = (int)query.GetInt(69);
                int iconId                   = (int)query.GetInt(70);
                int plateType                = (int)query.GetInt(71);
                int disableSinglemode        = (int)query.GetInt(72);
                int disableCountCondition    = (int)query.GetInt(73);
                int isGeneralSkill           = (int)query.GetInt(74);
                long startDate               = (long)query.GetLong(75);
                long endDate                 = (long)query.GetLong(76);

                orm = new SkillData(id, rarity, groupId, groupRate, filterSwitch, gradeValue, skillCategory, tagId, uniqueSkillId1, uniqueSkillId2, expType, potentialPerDefault, activateLot, priority, precondition1, condition1, floatAbilityTime1, abilityTimeUsage1, floatCooldownTime1, abilityType11, abilityValueUsage11, additionalActivateType11, abilityValueLevelUsage11, floatAbilityValue11, targetType11, targetValue11, abilityType12, abilityValueUsage12, additionalActivateType12, abilityValueLevelUsage12, floatAbilityValue12, targetType12, targetValue12, abilityType13, abilityValueUsage13, additionalActivateType13, abilityValueLevelUsage13, floatAbilityValue13, targetType13, targetValue13, precondition2, condition2, floatAbilityTime2, abilityTimeUsage2, floatCooldownTime2, abilityType21, abilityValueUsage21, additionalActivateType21, abilityValueLevelUsage21, floatAbilityValue21, targetType21, targetValue21, abilityType22, abilityValueUsage22, additionalActivateType22, abilityValueLevelUsage22, floatAbilityValue22, targetType22, targetValue22, abilityType23, abilityValueUsage23, additionalActivateType23, abilityValueLevelUsage23, floatAbilityValue23, targetType23, targetValue23, popularityAddParam1, popularityAddValue1, popularityAddParam2, popularityAddValue2, dispOrder, iconId, plateType, disableSinglemode, disableCountCondition, isGeneralSkill, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SkillData GetWithGroupIdOrderByIdAsc(int groupId)
        {
            SkillData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupIdOrderByIdAsc(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillData _SelectWithGroupIdOrderByIdAsc(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillData_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillData");
                return null;
            }

            // SELECT `id`,`rarity`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data` WHERE `group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            SkillData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<SkillData> GetListWithGroupIdOrderByIdAsc(int groupId)
        {
            int key = (int)groupId;

            if (!_dictionaryWithGroupId.ContainsKey(key)) {
                _dictionaryWithGroupId.Add(key, _ListSelectWithGroupIdOrderByIdAsc(groupId));
            }

            return _dictionaryWithGroupId[key];
        }

        public List<SkillData> MaybeListWithGroupIdOrderByIdAsc(int groupId)
        {
            List<SkillData> list = GetListWithGroupIdOrderByIdAsc(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<SkillData> _ListSelectWithGroupIdOrderByIdAsc(int groupId)
        {
            List<SkillData> _list = new List<SkillData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillData_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillData");
                return null;
            }

            // SELECT `id`,`rarity`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data` WHERE `group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                SkillData orm = _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(query, groupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillData _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id                       = (int)query.GetInt(0);
            int rarity                   = (int)query.GetInt(1);
            int groupRate                = (int)query.GetInt(2);
            int filterSwitch             = (int)query.GetInt(3);
            int gradeValue               = (int)query.GetInt(4);
            int skillCategory            = (int)query.GetInt(5);
            string tagId                 = query.GetText(6);
            int uniqueSkillId1           = (int)query.GetInt(7);
            int uniqueSkillId2           = (int)query.GetInt(8);
            int expType                  = (int)query.GetInt(9);
            int potentialPerDefault      = (int)query.GetInt(10);
            int activateLot              = (int)query.GetInt(11);
            int priority                 = (int)query.GetInt(12);
            string precondition1         = query.GetText(13);
            string condition1            = query.GetText(14);
            int floatAbilityTime1        = (int)query.GetInt(15);
            int abilityTimeUsage1        = (int)query.GetInt(16);
            int floatCooldownTime1       = (int)query.GetInt(17);
            int abilityType11            = (int)query.GetInt(18);
            int abilityValueUsage11      = (int)query.GetInt(19);
            int additionalActivateType11 = (int)query.GetInt(20);
            int abilityValueLevelUsage11 = (int)query.GetInt(21);
            int floatAbilityValue11      = (int)query.GetInt(22);
            int targetType11             = (int)query.GetInt(23);
            int targetValue11            = (int)query.GetInt(24);
            int abilityType12            = (int)query.GetInt(25);
            int abilityValueUsage12      = (int)query.GetInt(26);
            int additionalActivateType12 = (int)query.GetInt(27);
            int abilityValueLevelUsage12 = (int)query.GetInt(28);
            int floatAbilityValue12      = (int)query.GetInt(29);
            int targetType12             = (int)query.GetInt(30);
            int targetValue12            = (int)query.GetInt(31);
            int abilityType13            = (int)query.GetInt(32);
            int abilityValueUsage13      = (int)query.GetInt(33);
            int additionalActivateType13 = (int)query.GetInt(34);
            int abilityValueLevelUsage13 = (int)query.GetInt(35);
            int floatAbilityValue13      = (int)query.GetInt(36);
            int targetType13             = (int)query.GetInt(37);
            int targetValue13            = (int)query.GetInt(38);
            string precondition2         = query.GetText(39);
            string condition2            = query.GetText(40);
            int floatAbilityTime2        = (int)query.GetInt(41);
            int abilityTimeUsage2        = (int)query.GetInt(42);
            int floatCooldownTime2       = (int)query.GetInt(43);
            int abilityType21            = (int)query.GetInt(44);
            int abilityValueUsage21      = (int)query.GetInt(45);
            int additionalActivateType21 = (int)query.GetInt(46);
            int abilityValueLevelUsage21 = (int)query.GetInt(47);
            int floatAbilityValue21      = (int)query.GetInt(48);
            int targetType21             = (int)query.GetInt(49);
            int targetValue21            = (int)query.GetInt(50);
            int abilityType22            = (int)query.GetInt(51);
            int abilityValueUsage22      = (int)query.GetInt(52);
            int additionalActivateType22 = (int)query.GetInt(53);
            int abilityValueLevelUsage22 = (int)query.GetInt(54);
            int floatAbilityValue22      = (int)query.GetInt(55);
            int targetType22             = (int)query.GetInt(56);
            int targetValue22            = (int)query.GetInt(57);
            int abilityType23            = (int)query.GetInt(58);
            int abilityValueUsage23      = (int)query.GetInt(59);
            int additionalActivateType23 = (int)query.GetInt(60);
            int abilityValueLevelUsage23 = (int)query.GetInt(61);
            int floatAbilityValue23      = (int)query.GetInt(62);
            int targetType23             = (int)query.GetInt(63);
            int targetValue23            = (int)query.GetInt(64);
            int popularityAddParam1      = (int)query.GetInt(65);
            int popularityAddValue1      = (int)query.GetInt(66);
            int popularityAddParam2      = (int)query.GetInt(67);
            int popularityAddValue2      = (int)query.GetInt(68);
            int dispOrder                = (int)query.GetInt(69);
            int iconId                   = (int)query.GetInt(70);
            int plateType                = (int)query.GetInt(71);
            int disableSinglemode        = (int)query.GetInt(72);
            int disableCountCondition    = (int)query.GetInt(73);
            int isGeneralSkill           = (int)query.GetInt(74);
            long startDate               = (long)query.GetLong(75);
            long endDate                 = (long)query.GetLong(76);

            return new SkillData(id, rarity, groupId, groupRate, filterSwitch, gradeValue, skillCategory, tagId, uniqueSkillId1, uniqueSkillId2, expType, potentialPerDefault, activateLot, priority, precondition1, condition1, floatAbilityTime1, abilityTimeUsage1, floatCooldownTime1, abilityType11, abilityValueUsage11, additionalActivateType11, abilityValueLevelUsage11, floatAbilityValue11, targetType11, targetValue11, abilityType12, abilityValueUsage12, additionalActivateType12, abilityValueLevelUsage12, floatAbilityValue12, targetType12, targetValue12, abilityType13, abilityValueUsage13, additionalActivateType13, abilityValueLevelUsage13, floatAbilityValue13, targetType13, targetValue13, precondition2, condition2, floatAbilityTime2, abilityTimeUsage2, floatCooldownTime2, abilityType21, abilityValueUsage21, additionalActivateType21, abilityValueLevelUsage21, floatAbilityValue21, targetType21, targetValue21, abilityType22, abilityValueUsage22, additionalActivateType22, abilityValueLevelUsage22, floatAbilityValue22, targetType22, targetValue22, abilityType23, abilityValueUsage23, additionalActivateType23, abilityValueLevelUsage23, floatAbilityValue23, targetType23, targetValue23, popularityAddParam1, popularityAddValue1, popularityAddParam2, popularityAddValue2, dispOrder, iconId, plateType, disableSinglemode, disableCountCondition, isGeneralSkill, startDate, endDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SkillData()) {
                while (query.Step()) {
                    int id                       = (int)query.GetInt(0);
                    int rarity                   = (int)query.GetInt(1);
                    int groupId                  = (int)query.GetInt(2);
                    int groupRate                = (int)query.GetInt(3);
                    int filterSwitch             = (int)query.GetInt(4);
                    int gradeValue               = (int)query.GetInt(5);
                    int skillCategory            = (int)query.GetInt(6);
                    string tagId                 = query.GetText(7);
                    int uniqueSkillId1           = (int)query.GetInt(8);
                    int uniqueSkillId2           = (int)query.GetInt(9);
                    int expType                  = (int)query.GetInt(10);
                    int potentialPerDefault      = (int)query.GetInt(11);
                    int activateLot              = (int)query.GetInt(12);
                    int priority                 = (int)query.GetInt(13);
                    string precondition1         = query.GetText(14);
                    string condition1            = query.GetText(15);
                    int floatAbilityTime1        = (int)query.GetInt(16);
                    int abilityTimeUsage1        = (int)query.GetInt(17);
                    int floatCooldownTime1       = (int)query.GetInt(18);
                    int abilityType11            = (int)query.GetInt(19);
                    int abilityValueUsage11      = (int)query.GetInt(20);
                    int additionalActivateType11 = (int)query.GetInt(21);
                    int abilityValueLevelUsage11 = (int)query.GetInt(22);
                    int floatAbilityValue11      = (int)query.GetInt(23);
                    int targetType11             = (int)query.GetInt(24);
                    int targetValue11            = (int)query.GetInt(25);
                    int abilityType12            = (int)query.GetInt(26);
                    int abilityValueUsage12      = (int)query.GetInt(27);
                    int additionalActivateType12 = (int)query.GetInt(28);
                    int abilityValueLevelUsage12 = (int)query.GetInt(29);
                    int floatAbilityValue12      = (int)query.GetInt(30);
                    int targetType12             = (int)query.GetInt(31);
                    int targetValue12            = (int)query.GetInt(32);
                    int abilityType13            = (int)query.GetInt(33);
                    int abilityValueUsage13      = (int)query.GetInt(34);
                    int additionalActivateType13 = (int)query.GetInt(35);
                    int abilityValueLevelUsage13 = (int)query.GetInt(36);
                    int floatAbilityValue13      = (int)query.GetInt(37);
                    int targetType13             = (int)query.GetInt(38);
                    int targetValue13            = (int)query.GetInt(39);
                    string precondition2         = query.GetText(40);
                    string condition2            = query.GetText(41);
                    int floatAbilityTime2        = (int)query.GetInt(42);
                    int abilityTimeUsage2        = (int)query.GetInt(43);
                    int floatCooldownTime2       = (int)query.GetInt(44);
                    int abilityType21            = (int)query.GetInt(45);
                    int abilityValueUsage21      = (int)query.GetInt(46);
                    int additionalActivateType21 = (int)query.GetInt(47);
                    int abilityValueLevelUsage21 = (int)query.GetInt(48);
                    int floatAbilityValue21      = (int)query.GetInt(49);
                    int targetType21             = (int)query.GetInt(50);
                    int targetValue21            = (int)query.GetInt(51);
                    int abilityType22            = (int)query.GetInt(52);
                    int abilityValueUsage22      = (int)query.GetInt(53);
                    int additionalActivateType22 = (int)query.GetInt(54);
                    int abilityValueLevelUsage22 = (int)query.GetInt(55);
                    int floatAbilityValue22      = (int)query.GetInt(56);
                    int targetType22             = (int)query.GetInt(57);
                    int targetValue22            = (int)query.GetInt(58);
                    int abilityType23            = (int)query.GetInt(59);
                    int abilityValueUsage23      = (int)query.GetInt(60);
                    int additionalActivateType23 = (int)query.GetInt(61);
                    int abilityValueLevelUsage23 = (int)query.GetInt(62);
                    int floatAbilityValue23      = (int)query.GetInt(63);
                    int targetType23             = (int)query.GetInt(64);
                    int targetValue23            = (int)query.GetInt(65);
                    int popularityAddParam1      = (int)query.GetInt(66);
                    int popularityAddValue1      = (int)query.GetInt(67);
                    int popularityAddParam2      = (int)query.GetInt(68);
                    int popularityAddValue2      = (int)query.GetInt(69);
                    int dispOrder                = (int)query.GetInt(70);
                    int iconId                   = (int)query.GetInt(71);
                    int plateType                = (int)query.GetInt(72);
                    int disableSinglemode        = (int)query.GetInt(73);
                    int disableCountCondition    = (int)query.GetInt(74);
                    int isGeneralSkill           = (int)query.GetInt(75);
                    long startDate               = (long)query.GetLong(76);
                    long endDate                 = (long)query.GetLong(77);

                    int key = (int)id;
                    SkillData orm = new SkillData(id, rarity, groupId, groupRate, filterSwitch, gradeValue, skillCategory, tagId, uniqueSkillId1, uniqueSkillId2, expType, potentialPerDefault, activateLot, priority, precondition1, condition1, floatAbilityTime1, abilityTimeUsage1, floatCooldownTime1, abilityType11, abilityValueUsage11, additionalActivateType11, abilityValueLevelUsage11, floatAbilityValue11, targetType11, targetValue11, abilityType12, abilityValueUsage12, additionalActivateType12, abilityValueLevelUsage12, floatAbilityValue12, targetType12, targetValue12, abilityType13, abilityValueUsage13, additionalActivateType13, abilityValueLevelUsage13, floatAbilityValue13, targetType13, targetValue13, precondition2, condition2, floatAbilityTime2, abilityTimeUsage2, floatCooldownTime2, abilityType21, abilityValueUsage21, additionalActivateType21, abilityValueLevelUsage21, floatAbilityValue21, targetType21, targetValue21, abilityType22, abilityValueUsage22, additionalActivateType22, abilityValueLevelUsage22, floatAbilityValue22, targetType22, targetValue22, abilityType23, abilityValueUsage23, additionalActivateType23, abilityValueLevelUsage23, floatAbilityValue23, targetType23, targetValue23, popularityAddParam1, popularityAddValue1, popularityAddParam2, popularityAddValue2, dispOrder, iconId, plateType, disableSinglemode, disableCountCondition, isGeneralSkill, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SkillData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: group_rate) </summary>
            public readonly int GroupRate;
            /// <summary> (CSV column: filter_switch) </summary>
            public readonly int FilterSwitch;
            /// <summary> (CSV column: grade_value) </summary>
            public readonly int GradeValue;
            /// <summary> (CSV column: skill_category) </summary>
            public readonly int SkillCategory;
            /// <summary> (CSV column: tag_id) </summary>
            public readonly string TagId;
            /// <summary> (CSV column: unique_skill_id_1) </summary>
            public readonly int UniqueSkillId1;
            /// <summary> (CSV column: unique_skill_id_2) </summary>
            public readonly int UniqueSkillId2;
            /// <summary> (CSV column: exp_type) </summary>
            public readonly int ExpType;
            /// <summary> (CSV column: potential_per_default) </summary>
            public readonly int PotentialPerDefault;
            /// <summary> (CSV column: activate_lot) </summary>
            public readonly int ActivateLot;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: precondition_1) </summary>
            public readonly string Precondition1;
            /// <summary> (CSV column: condition_1) </summary>
            public readonly string Condition1;
            /// <summary> (CSV column: float_ability_time_1) </summary>
            public readonly int FloatAbilityTime1;
            /// <summary> (CSV column: ability_time_usage_1) </summary>
            public readonly int AbilityTimeUsage1;
            /// <summary> (CSV column: float_cooldown_time_1) </summary>
            public readonly int FloatCooldownTime1;
            /// <summary> (CSV column: ability_type_1_1) </summary>
            public readonly int AbilityType11;
            /// <summary> (CSV column: ability_value_usage_1_1) </summary>
            public readonly int AbilityValueUsage11;
            /// <summary> (CSV column: additional_activate_type_1_1) </summary>
            public readonly int AdditionalActivateType11;
            /// <summary> (CSV column: ability_value_level_usage_1_1) </summary>
            public readonly int AbilityValueLevelUsage11;
            /// <summary> (CSV column: float_ability_value_1_1) </summary>
            public readonly int FloatAbilityValue11;
            /// <summary> (CSV column: target_type_1_1) </summary>
            public readonly int TargetType11;
            /// <summary> (CSV column: target_value_1_1) </summary>
            public readonly int TargetValue11;
            /// <summary> (CSV column: ability_type_1_2) </summary>
            public readonly int AbilityType12;
            /// <summary> (CSV column: ability_value_usage_1_2) </summary>
            public readonly int AbilityValueUsage12;
            /// <summary> (CSV column: additional_activate_type_1_2) </summary>
            public readonly int AdditionalActivateType12;
            /// <summary> (CSV column: ability_value_level_usage_1_2) </summary>
            public readonly int AbilityValueLevelUsage12;
            /// <summary> (CSV column: float_ability_value_1_2) </summary>
            public readonly int FloatAbilityValue12;
            /// <summary> (CSV column: target_type_1_2) </summary>
            public readonly int TargetType12;
            /// <summary> (CSV column: target_value_1_2) </summary>
            public readonly int TargetValue12;
            /// <summary> (CSV column: ability_type_1_3) </summary>
            public readonly int AbilityType13;
            /// <summary> (CSV column: ability_value_usage_1_3) </summary>
            public readonly int AbilityValueUsage13;
            /// <summary> (CSV column: additional_activate_type_1_3) </summary>
            public readonly int AdditionalActivateType13;
            /// <summary> (CSV column: ability_value_level_usage_1_3) </summary>
            public readonly int AbilityValueLevelUsage13;
            /// <summary> (CSV column: float_ability_value_1_3) </summary>
            public readonly int FloatAbilityValue13;
            /// <summary> (CSV column: target_type_1_3) </summary>
            public readonly int TargetType13;
            /// <summary> (CSV column: target_value_1_3) </summary>
            public readonly int TargetValue13;
            /// <summary> (CSV column: precondition_2) </summary>
            public readonly string Precondition2;
            /// <summary> (CSV column: condition_2) </summary>
            public readonly string Condition2;
            /// <summary> (CSV column: float_ability_time_2) </summary>
            public readonly int FloatAbilityTime2;
            /// <summary> (CSV column: ability_time_usage_2) </summary>
            public readonly int AbilityTimeUsage2;
            /// <summary> (CSV column: float_cooldown_time_2) </summary>
            public readonly int FloatCooldownTime2;
            /// <summary> (CSV column: ability_type_2_1) </summary>
            public readonly int AbilityType21;
            /// <summary> (CSV column: ability_value_usage_2_1) </summary>
            public readonly int AbilityValueUsage21;
            /// <summary> (CSV column: additional_activate_type_2_1) </summary>
            public readonly int AdditionalActivateType21;
            /// <summary> (CSV column: ability_value_level_usage_2_1) </summary>
            public readonly int AbilityValueLevelUsage21;
            /// <summary> (CSV column: float_ability_value_2_1) </summary>
            public readonly int FloatAbilityValue21;
            /// <summary> (CSV column: target_type_2_1) </summary>
            public readonly int TargetType21;
            /// <summary> (CSV column: target_value_2_1) </summary>
            public readonly int TargetValue21;
            /// <summary> (CSV column: ability_type_2_2) </summary>
            public readonly int AbilityType22;
            /// <summary> (CSV column: ability_value_usage_2_2) </summary>
            public readonly int AbilityValueUsage22;
            /// <summary> (CSV column: additional_activate_type_2_2) </summary>
            public readonly int AdditionalActivateType22;
            /// <summary> (CSV column: ability_value_level_usage_2_2) </summary>
            public readonly int AbilityValueLevelUsage22;
            /// <summary> (CSV column: float_ability_value_2_2) </summary>
            public readonly int FloatAbilityValue22;
            /// <summary> (CSV column: target_type_2_2) </summary>
            public readonly int TargetType22;
            /// <summary> (CSV column: target_value_2_2) </summary>
            public readonly int TargetValue22;
            /// <summary> (CSV column: ability_type_2_3) </summary>
            public readonly int AbilityType23;
            /// <summary> (CSV column: ability_value_usage_2_3) </summary>
            public readonly int AbilityValueUsage23;
            /// <summary> (CSV column: additional_activate_type_2_3) </summary>
            public readonly int AdditionalActivateType23;
            /// <summary> (CSV column: ability_value_level_usage_2_3) </summary>
            public readonly int AbilityValueLevelUsage23;
            /// <summary> (CSV column: float_ability_value_2_3) </summary>
            public readonly int FloatAbilityValue23;
            /// <summary> (CSV column: target_type_2_3) </summary>
            public readonly int TargetType23;
            /// <summary> (CSV column: target_value_2_3) </summary>
            public readonly int TargetValue23;
            /// <summary> (CSV column: popularity_add_param_1) </summary>
            public readonly int PopularityAddParam1;
            /// <summary> (CSV column: popularity_add_value_1) </summary>
            public readonly int PopularityAddValue1;
            /// <summary> (CSV column: popularity_add_param_2) </summary>
            public readonly int PopularityAddParam2;
            /// <summary> (CSV column: popularity_add_value_2) </summary>
            public readonly int PopularityAddValue2;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary> (CSV column: icon_id) </summary>
            public readonly int IconId;
            /// <summary> (CSV column: plate_type) </summary>
            public readonly int PlateType;
            /// <summary> (CSV column: disable_singlemode) </summary>
            public readonly int DisableSinglemode;
            /// <summary> (CSV column: disable_count_condition) </summary>
            public readonly int DisableCountCondition;
            /// <summary> (CSV column: is_general_skill) </summary>
            public readonly int IsGeneralSkill;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillData(int id = 0, int rarity = 0, int groupId = 0, int groupRate = 0, int filterSwitch = 0, int gradeValue = 0, int skillCategory = 0, string tagId = "", int uniqueSkillId1 = 0, int uniqueSkillId2 = 0, int expType = 0, int potentialPerDefault = 0, int activateLot = 0, int priority = 0, string precondition1 = "", string condition1 = "", int floatAbilityTime1 = 0, int abilityTimeUsage1 = 0, int floatCooldownTime1 = 0, int abilityType11 = 0, int abilityValueUsage11 = 0, int additionalActivateType11 = 0, int abilityValueLevelUsage11 = 0, int floatAbilityValue11 = 0, int targetType11 = 0, int targetValue11 = 0, int abilityType12 = 0, int abilityValueUsage12 = 0, int additionalActivateType12 = 0, int abilityValueLevelUsage12 = 0, int floatAbilityValue12 = 0, int targetType12 = 0, int targetValue12 = 0, int abilityType13 = 0, int abilityValueUsage13 = 0, int additionalActivateType13 = 0, int abilityValueLevelUsage13 = 0, int floatAbilityValue13 = 0, int targetType13 = 0, int targetValue13 = 0, string precondition2 = "", string condition2 = "", int floatAbilityTime2 = 0, int abilityTimeUsage2 = 0, int floatCooldownTime2 = 0, int abilityType21 = 0, int abilityValueUsage21 = 0, int additionalActivateType21 = 0, int abilityValueLevelUsage21 = 0, int floatAbilityValue21 = 0, int targetType21 = 0, int targetValue21 = 0, int abilityType22 = 0, int abilityValueUsage22 = 0, int additionalActivateType22 = 0, int abilityValueLevelUsage22 = 0, int floatAbilityValue22 = 0, int targetType22 = 0, int targetValue22 = 0, int abilityType23 = 0, int abilityValueUsage23 = 0, int additionalActivateType23 = 0, int abilityValueLevelUsage23 = 0, int floatAbilityValue23 = 0, int targetType23 = 0, int targetValue23 = 0, int popularityAddParam1 = 0, int popularityAddValue1 = 0, int popularityAddParam2 = 0, int popularityAddValue2 = 0, int dispOrder = 0, int iconId = 0, int plateType = 0, int disableSinglemode = 0, int disableCountCondition = 0, int isGeneralSkill = 0, long startDate = 0, long endDate = 0)
            {
                this.Id                       = id;
                this.Rarity                   = rarity;
                this.GroupId                  = groupId;
                this.GroupRate                = groupRate;
                this.FilterSwitch             = filterSwitch;
                this.GradeValue               = gradeValue;
                this.SkillCategory            = skillCategory;
                this.TagId                    = tagId;
                this.UniqueSkillId1           = uniqueSkillId1;
                this.UniqueSkillId2           = uniqueSkillId2;
                this.ExpType                  = expType;
                this.PotentialPerDefault      = potentialPerDefault;
                this.ActivateLot              = activateLot;
                this.Priority                 = priority;
                this.Precondition1            = precondition1;
                this.Condition1               = condition1;
                this.FloatAbilityTime1        = floatAbilityTime1;
                this.AbilityTimeUsage1        = abilityTimeUsage1;
                this.FloatCooldownTime1       = floatCooldownTime1;
                this.AbilityType11            = abilityType11;
                this.AbilityValueUsage11      = abilityValueUsage11;
                this.AdditionalActivateType11 = additionalActivateType11;
                this.AbilityValueLevelUsage11 = abilityValueLevelUsage11;
                this.FloatAbilityValue11      = floatAbilityValue11;
                this.TargetType11             = targetType11;
                this.TargetValue11            = targetValue11;
                this.AbilityType12            = abilityType12;
                this.AbilityValueUsage12      = abilityValueUsage12;
                this.AdditionalActivateType12 = additionalActivateType12;
                this.AbilityValueLevelUsage12 = abilityValueLevelUsage12;
                this.FloatAbilityValue12      = floatAbilityValue12;
                this.TargetType12             = targetType12;
                this.TargetValue12            = targetValue12;
                this.AbilityType13            = abilityType13;
                this.AbilityValueUsage13      = abilityValueUsage13;
                this.AdditionalActivateType13 = additionalActivateType13;
                this.AbilityValueLevelUsage13 = abilityValueLevelUsage13;
                this.FloatAbilityValue13      = floatAbilityValue13;
                this.TargetType13             = targetType13;
                this.TargetValue13            = targetValue13;
                this.Precondition2            = precondition2;
                this.Condition2               = condition2;
                this.FloatAbilityTime2        = floatAbilityTime2;
                this.AbilityTimeUsage2        = abilityTimeUsage2;
                this.FloatCooldownTime2       = floatCooldownTime2;
                this.AbilityType21            = abilityType21;
                this.AbilityValueUsage21      = abilityValueUsage21;
                this.AdditionalActivateType21 = additionalActivateType21;
                this.AbilityValueLevelUsage21 = abilityValueLevelUsage21;
                this.FloatAbilityValue21      = floatAbilityValue21;
                this.TargetType21             = targetType21;
                this.TargetValue21            = targetValue21;
                this.AbilityType22            = abilityType22;
                this.AbilityValueUsage22      = abilityValueUsage22;
                this.AdditionalActivateType22 = additionalActivateType22;
                this.AbilityValueLevelUsage22 = abilityValueLevelUsage22;
                this.FloatAbilityValue22      = floatAbilityValue22;
                this.TargetType22             = targetType22;
                this.TargetValue22            = targetValue22;
                this.AbilityType23            = abilityType23;
                this.AbilityValueUsage23      = abilityValueUsage23;
                this.AdditionalActivateType23 = additionalActivateType23;
                this.AbilityValueLevelUsage23 = abilityValueLevelUsage23;
                this.FloatAbilityValue23      = floatAbilityValue23;
                this.TargetType23             = targetType23;
                this.TargetValue23            = targetValue23;
                this.PopularityAddParam1      = popularityAddParam1;
                this.PopularityAddValue1      = popularityAddValue1;
                this.PopularityAddParam2      = popularityAddParam2;
                this.PopularityAddValue2      = popularityAddValue2;
                this.DispOrder                = dispOrder;
                this.IconId                   = iconId;
                this.PlateType                = plateType;
                this.DisableSinglemode        = disableSinglemode;
                this.DisableCountCondition    = disableCountCondition;
                this.IsGeneralSkill           = isGeneralSkill;
                this.StartDate                = startDate;
                this.EndDate                  = endDate;
            }
        }
    }
}
#endif

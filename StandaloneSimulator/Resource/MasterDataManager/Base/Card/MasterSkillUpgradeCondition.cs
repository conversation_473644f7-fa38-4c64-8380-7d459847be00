// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_upgrade_condition
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:description_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillUpgradeCondition : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_upgrade_condition";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillUpgradeCondition> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SkillUpgradeCondition>> _dictionaryWithDescriptionId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillUpgradeCondition(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillUpgradeCondition>();
            _dictionaryWithDescriptionId = new Dictionary<int, List<SkillUpgradeCondition>>();
            _db = db;
        }


        public SkillUpgradeCondition Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillUpgradeCondition");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillUpgradeCondition", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeCondition _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillUpgradeCondition();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeCondition");
                return null;
            }

            // SELECT `upgrade_type`,`description_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillUpgradeCondition orm = null;

            if (query.Step())
            {
                int upgradeType   = (int)query.GetInt(0);
                int descriptionId = (int)query.GetInt(1);
                int num           = (int)query.GetInt(2);
                int subNum        = (int)query.GetInt(3);
                int timingType    = (int)query.GetInt(4);
                int countType     = (int)query.GetInt(5);

                orm = new SkillUpgradeCondition(id, upgradeType, descriptionId, num, subNum, timingType, countType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SkillUpgradeCondition GetWithDescriptionIdOrderByNumAsc(int descriptionId)
        {
            SkillUpgradeCondition orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithDescriptionIdOrderByNumAsc(descriptionId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", descriptionId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillUpgradeCondition _SelectWithDescriptionIdOrderByNumAsc(int descriptionId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeCondition_DescriptionId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeCondition");
                return null;
            }

            // SELECT `id`,`upgrade_type`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition` WHERE `description_id`=? ORDER BY `num` ASC;
            if (!query.BindInt(1, descriptionId)) { return null; }

            SkillUpgradeCondition orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithDescriptionIdOrderByNumAsc(query, descriptionId);
            }

            query.Reset();

            return orm;
        }

        public List<SkillUpgradeCondition> GetListWithDescriptionIdOrderByNumAsc(int descriptionId)
        {
            int key = (int)descriptionId;

            if (!_dictionaryWithDescriptionId.ContainsKey(key)) {
                _dictionaryWithDescriptionId.Add(key, _ListSelectWithDescriptionIdOrderByNumAsc(descriptionId));
            }

            return _dictionaryWithDescriptionId[key];
        }

        public List<SkillUpgradeCondition> MaybeListWithDescriptionIdOrderByNumAsc(int descriptionId)
        {
            List<SkillUpgradeCondition> list = GetListWithDescriptionIdOrderByNumAsc(descriptionId);
            return list.Count > 0 ? list : null;
        }

        private List<SkillUpgradeCondition> _ListSelectWithDescriptionIdOrderByNumAsc(int descriptionId)
        {
            List<SkillUpgradeCondition> _list = new List<SkillUpgradeCondition>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpgradeCondition_DescriptionId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpgradeCondition");
                return null;
            }

            // SELECT `id`,`upgrade_type`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition` WHERE `description_id`=? ORDER BY `num` ASC;
            if (!query.BindInt(1, descriptionId)) { return null; }

            while (query.Step()) {
                SkillUpgradeCondition orm = _CreateOrmByQueryResultWithDescriptionIdOrderByNumAsc(query, descriptionId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillUpgradeCondition _CreateOrmByQueryResultWithDescriptionIdOrderByNumAsc(LibNative.Sqlite3.PreparedQuery query, int descriptionId)
        {
            int id          = (int)query.GetInt(0);
            int upgradeType = (int)query.GetInt(1);
            int num         = (int)query.GetInt(2);
            int subNum      = (int)query.GetInt(3);
            int timingType  = (int)query.GetInt(4);
            int countType   = (int)query.GetInt(5);

            return new SkillUpgradeCondition(id, upgradeType, descriptionId, num, subNum, timingType, countType);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithDescriptionId.Clear();
        }

        public sealed partial class SkillUpgradeCondition
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: upgrade_type) </summary>
            public readonly int UpgradeType;
            /// <summary> (CSV column: description_id) </summary>
            public readonly int DescriptionId;
            /// <summary> (CSV column: num) </summary>
            public readonly int Num;
            /// <summary> (CSV column: sub_num) </summary>
            public readonly int SubNum;
            /// <summary> (CSV column: timing_type) </summary>
            public readonly int TimingType;
            /// <summary> (CSV column: count_type) </summary>
            public readonly int CountType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillUpgradeCondition(int id = 0, int upgradeType = 0, int descriptionId = 0, int num = 0, int subNum = 0, int timingType = 0, int countType = 0)
            {
                this.Id            = id;
                this.UpgradeType   = upgradeType;
                this.DescriptionId = descriptionId;
                this.Num           = num;
                this.SubNum        = subNum;
                this.TimingType    = timingType;
                this.CountType     = countType;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/need_piece_num_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterNeedPieceNumData : AbstractMasterData
    {
        public const string TABLE_NAME = "need_piece_num_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, NeedPieceNumData> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, NeedPieceNumData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterNeedPieceNumData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterNeedPieceNumData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, NeedPieceNumData>();
            _db = db;
        }


        public NeedPieceNumData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterNeedPieceNumData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterNeedPieceNumData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private NeedPieceNumData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_NeedPieceNumData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for NeedPieceNumData");
                return null;
            }

            // SELECT `rarity`,`piece_num` FROM `need_piece_num_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            NeedPieceNumData orm = null;

            if (query.Step())
            {
                int rarity   = (int)query.GetInt(0);
                int pieceNum = (int)query.GetInt(1);

                orm = new NeedPieceNumData(id, rarity, pieceNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public NeedPieceNumData GetWithRarity(int rarity)
        {
            NeedPieceNumData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRarity(rarity);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", rarity));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private NeedPieceNumData _SelectWithRarity(int rarity)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_NeedPieceNumData_Rarity();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for NeedPieceNumData");
                return null;
            }

            // SELECT `id`,`piece_num` FROM `need_piece_num_data` WHERE `rarity`=?;
            if (!query.BindInt(1, rarity)) { return null; }

            NeedPieceNumData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRarity(query, rarity);
            }

            query.Reset();

            return orm;
        }

        private NeedPieceNumData _CreateOrmByQueryResultWithRarity(LibNative.Sqlite3.PreparedQuery query, int rarity)
        {
            int id       = (int)query.GetInt(0);
            int pieceNum = (int)query.GetInt(1);

            return new NeedPieceNumData(id, rarity, pieceNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_NeedPieceNumData()) {
                while (query.Step()) {
                    int id       = (int)query.GetInt(0);
                    int rarity   = (int)query.GetInt(1);
                    int pieceNum = (int)query.GetInt(2);

                    int key = (int)id;
                    NeedPieceNumData orm = new NeedPieceNumData(id, rarity, pieceNum);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class NeedPieceNumData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: piece_num) </summary>
            public readonly int PieceNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public NeedPieceNumData(int id = 0, int rarity = 0, int pieceNum = 0)
            {
                this.Id       = id;
                this.Rarity   = rarity;
                this.PieceNum = pieceNum;
            }
        }
    }
}
#endif

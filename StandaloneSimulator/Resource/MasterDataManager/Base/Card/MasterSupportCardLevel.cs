// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_level
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:rarity]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardLevel : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_level";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardLevel> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SupportCardLevel>> _dictionaryWithRarity = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardLevel(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardLevel>();
            _dictionaryWithRarity = new Dictionary<int, List<SupportCardLevel>>();
            _db = db;
        }


        public SupportCardLevel Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardLevel");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardLevel", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardLevel _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardLevel();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardLevel");
                return null;
            }

            // SELECT `rarity`,`level`,`total_exp` FROM `support_card_level` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SupportCardLevel orm = null;

            if (query.Step())
            {
                int rarity   = (int)query.GetInt(0);
                int level    = (int)query.GetInt(1);
                int totalExp = (int)query.GetInt(2);

                orm = new SupportCardLevel(id, rarity, level, totalExp);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SupportCardLevel GetWithRarityOrderByIdAsc(int rarity)
        {
            SupportCardLevel orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRarityOrderByIdAsc(rarity);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", rarity));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SupportCardLevel _SelectWithRarityOrderByIdAsc(int rarity)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardLevel_Rarity();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardLevel");
                return null;
            }

            // SELECT `id`,`level`,`total_exp` FROM `support_card_level` WHERE `rarity`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, rarity)) { return null; }

            SupportCardLevel orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRarityOrderByIdAsc(query, rarity);
            }

            query.Reset();

            return orm;
        }

        public List<SupportCardLevel> GetListWithRarityOrderByIdAsc(int rarity)
        {
            int key = (int)rarity;

            if (!_dictionaryWithRarity.ContainsKey(key)) {
                _dictionaryWithRarity.Add(key, _ListSelectWithRarityOrderByIdAsc(rarity));
            }

            return _dictionaryWithRarity[key];
        }

        public List<SupportCardLevel> MaybeListWithRarityOrderByIdAsc(int rarity)
        {
            List<SupportCardLevel> list = GetListWithRarityOrderByIdAsc(rarity);
            return list.Count > 0 ? list : null;
        }

        private List<SupportCardLevel> _ListSelectWithRarityOrderByIdAsc(int rarity)
        {
            List<SupportCardLevel> _list = new List<SupportCardLevel>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardLevel_Rarity();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardLevel");
                return null;
            }

            // SELECT `id`,`level`,`total_exp` FROM `support_card_level` WHERE `rarity`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, rarity)) { return null; }

            while (query.Step()) {
                SupportCardLevel orm = _CreateOrmByQueryResultWithRarityOrderByIdAsc(query, rarity);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SupportCardLevel _CreateOrmByQueryResultWithRarityOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int rarity)
        {
            int id       = (int)query.GetInt(0);
            int level    = (int)query.GetInt(1);
            int totalExp = (int)query.GetInt(2);

            return new SupportCardLevel(id, rarity, level, totalExp);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithRarity.Clear();
        }

        public sealed partial class SupportCardLevel
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: level) </summary>
            public readonly int Level;
            /// <summary> (CSV column: total_exp) </summary>
            public readonly int TotalExp;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardLevel(int id = 0, int rarity = 0, int level = 0, int totalExp = 0)
            {
                this.Id       = id;
                this.Rarity   = rarity;
                this.Level    = level;
                this.TotalExp = totalExp;
            }
        }
    }
}
#endif

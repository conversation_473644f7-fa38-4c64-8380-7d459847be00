// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_motion_act
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:chara_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaMotionAct : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_motion_act";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharaMotionAct> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<CharaMotionAct>> _dictionaryWithCharaId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaMotionAct(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharaMotionAct>();
            _dictionaryWithCharaId = new Dictionary<int, List<CharaMotionAct>>();
            _db = db;
        }


        public CharaMotionAct Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaMotionAct");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaMotionAct", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharaMotionAct _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaMotionAct();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaMotionAct");
                return null;
            }

            // SELECT `chara_id`,`target_motion`,`command_name` FROM `chara_motion_act` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharaMotionAct orm = null;

            if (query.Step())
            {
                int charaId        = (int)query.GetInt(0);
                int targetMotion   = (int)query.GetInt(1);
                string commandName = query.GetText(2);

                orm = new CharaMotionAct(id, charaId, targetMotion, commandName);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CharaMotionAct GetWithCharaIdAndCommandName(int charaId, string commandName)
        {
            CharaMotionAct orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaIdAndCommandName(charaId, commandName);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", charaId, commandName));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharaMotionAct _SelectWithCharaIdAndCommandName(int charaId, string commandName)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaMotionAct_CharaId_CommandName();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaMotionAct");
                return null;
            }

            // SELECT `id`,`target_motion` FROM `chara_motion_act` WHERE `chara_id`=? AND `command_name`=?;
            if (!query.BindInt(1, charaId))      { return null; }
            if (!query.BindText(2, commandName)) { return null; }

            CharaMotionAct orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaIdAndCommandName(query, charaId, commandName);
            }

            query.Reset();

            return orm;
        }

        private CharaMotionAct _CreateOrmByQueryResultWithCharaIdAndCommandName(LibNative.Sqlite3.PreparedQuery query, int charaId, string commandName)
        {
            int id           = (int)query.GetInt(0);
            int targetMotion = (int)query.GetInt(1);

            return new CharaMotionAct(id, charaId, targetMotion, commandName);
        }

        public CharaMotionAct GetWithCharaId(int charaId)
        {
            CharaMotionAct orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaId(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharaMotionAct _SelectWithCharaId(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaMotionAct_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaMotionAct");
                return null;
            }

            // SELECT `id`,`target_motion`,`command_name` FROM `chara_motion_act` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            CharaMotionAct orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<CharaMotionAct> GetListWithCharaId(int charaId)
        {
            int key = (int)charaId;

            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaId(charaId));
            }

            return _dictionaryWithCharaId[key];
        }

        public List<CharaMotionAct> MaybeListWithCharaId(int charaId)
        {
            List<CharaMotionAct> list = GetListWithCharaId(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<CharaMotionAct> _ListSelectWithCharaId(int charaId)
        {
            List<CharaMotionAct> _list = new List<CharaMotionAct>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaMotionAct_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaMotionAct");
                return null;
            }

            // SELECT `id`,`target_motion`,`command_name` FROM `chara_motion_act` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                CharaMotionAct orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharaMotionAct _CreateOrmByQueryResultWithCharaId(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id             = (int)query.GetInt(0);
            int targetMotion   = (int)query.GetInt(1);
            string commandName = query.GetText(2);

            return new CharaMotionAct(id, charaId, targetMotion, commandName);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharaId.Clear();
        }

        public sealed partial class CharaMotionAct
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: target_motion) </summary>
            public readonly int TargetMotion;
            /// <summary> (CSV column: command_name) </summary>
            public readonly string CommandName;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaMotionAct(int id = 0, int charaId = 0, int targetMotion = 0, string commandName = "")
            {
                this.Id           = id;
                this.CharaId      = charaId;
                this.TargetMotion = targetMotion;
                this.CommandName  = commandName;
            }
        }
    }
}
#endif

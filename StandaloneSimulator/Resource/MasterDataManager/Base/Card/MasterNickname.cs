// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/nickname
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:user_show]]
        unused - [:receive_condition_type]
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterNickname : AbstractMasterData
    {
        public const string TABLE_NAME = "nickname";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, Nickname> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<Nickname>> _dictionaryWithUserShow = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterNickname(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, Nickname>();
            _dictionaryWithUserShow = new Dictionary<int, List<Nickname>>();
            _db = db;
        }


        public Nickname Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterNickname");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterNickname", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private Nickname _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_Nickname();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for Nickname");
                return null;
            }

            // SELECT `type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`user_show`,`start_date`,`end_date` FROM `nickname` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            Nickname orm = null;

            if (query.Step())
            {
                int type        = (int)query.GetInt(0);
                int scenarioId  = (int)query.GetInt(1);
                int dispOrder   = (int)query.GetInt(2);
                int groupId     = (int)query.GetInt(3);
                int rank        = (int)query.GetInt(4);
                int charaDataId = (int)query.GetInt(5);
                int userShow    = (int)query.GetInt(6);
                long startDate  = (long)query.GetLong(7);
                long endDate    = (long)query.GetLong(8);

                orm = new Nickname(id, type, scenarioId, dispOrder, groupId, rank, charaDataId, userShow, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public Nickname GetWithUserShowOrderByDispOrderAsc(int userShow)
        {
            Nickname orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithUserShowOrderByDispOrderAsc(userShow);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", userShow));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private Nickname _SelectWithUserShowOrderByDispOrderAsc(int userShow)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_Nickname_UserShow();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for Nickname");
                return null;
            }

            // SELECT `id`,`type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`start_date`,`end_date` FROM `nickname` WHERE `user_show`=? ORDER BY `disp_order` ASC;
            if (!query.BindInt(1, userShow)) { return null; }

            Nickname orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithUserShowOrderByDispOrderAsc(query, userShow);
            }

            query.Reset();

            return orm;
        }

        public List<Nickname> GetListWithUserShowOrderByDispOrderAsc(int userShow)
        {
            int key = (int)userShow;

            if (!_dictionaryWithUserShow.ContainsKey(key)) {
                _dictionaryWithUserShow.Add(key, _ListSelectWithUserShowOrderByDispOrderAsc(userShow));
            }

            return _dictionaryWithUserShow[key];
        }

        public List<Nickname> MaybeListWithUserShowOrderByDispOrderAsc(int userShow)
        {
            List<Nickname> list = GetListWithUserShowOrderByDispOrderAsc(userShow);
            return list.Count > 0 ? list : null;
        }

        private List<Nickname> _ListSelectWithUserShowOrderByDispOrderAsc(int userShow)
        {
            List<Nickname> _list = new List<Nickname>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_Nickname_UserShow();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for Nickname");
                return null;
            }

            // SELECT `id`,`type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`start_date`,`end_date` FROM `nickname` WHERE `user_show`=? ORDER BY `disp_order` ASC;
            if (!query.BindInt(1, userShow)) { return null; }

            while (query.Step()) {
                Nickname orm = _CreateOrmByQueryResultWithUserShowOrderByDispOrderAsc(query, userShow);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private Nickname _CreateOrmByQueryResultWithUserShowOrderByDispOrderAsc(LibNative.Sqlite3.PreparedQuery query, int userShow)
        {
            int id          = (int)query.GetInt(0);
            int type        = (int)query.GetInt(1);
            int scenarioId  = (int)query.GetInt(2);
            int dispOrder   = (int)query.GetInt(3);
            int groupId     = (int)query.GetInt(4);
            int rank        = (int)query.GetInt(5);
            int charaDataId = (int)query.GetInt(6);
            long startDate  = (long)query.GetLong(7);
            long endDate    = (long)query.GetLong(8);

            return new Nickname(id, type, scenarioId, dispOrder, groupId, rank, charaDataId, userShow, startDate, endDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithUserShow.Clear();
        }

        public sealed partial class Nickname
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: type) </summary>
            public readonly int Type;
            /// <summary> (CSV column: scenario_id) </summary>
            public readonly int ScenarioId;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: rank) </summary>
            public readonly int Rank;
            /// <summary> (CSV column: chara_data_id) </summary>
            public readonly int CharaDataId;
            /// <summary> (CSV column: user_show) </summary>
            public readonly int UserShow;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public Nickname(int id = 0, int type = 0, int scenarioId = 0, int dispOrder = 0, int groupId = 0, int rank = 0, int charaDataId = 0, int userShow = 0, long startDate = 0, long endDate = 0)
            {
                this.Id          = id;
                this.Type        = type;
                this.ScenarioId  = scenarioId;
                this.DispOrder   = dispOrder;
                this.GroupId     = groupId;
                this.Rank        = rank;
                this.CharaDataId = charaDataId;
                this.UserShow    = userShow;
                this.StartDate   = startDate;
                this.EndDate     = endDate;
            }
        }
    }
}
#endif

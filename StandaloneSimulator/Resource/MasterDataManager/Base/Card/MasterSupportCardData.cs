// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:chara_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardData : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardData> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SupportCardData>> _dictionaryWithCharaId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SupportCardData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSupportCardData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardData>();
            _dictionaryWithCharaId = new Dictionary<int, List<SupportCardData>>();
            _db = db;
        }


        public SupportCardData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardData");
                return null;
            }

            // SELECT `chara_id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SupportCardData orm = null;

            if (query.Step())
            {
                int charaId         = (int)query.GetInt(0);
                int rarity          = (int)query.GetInt(1);
                int exchangeItemId  = (int)query.GetInt(2);
                int effectTableId   = (int)query.GetInt(3);
                int uniqueEffectId  = (int)query.GetInt(4);
                int commandType     = (int)query.GetInt(5);
                int commandId       = (int)query.GetInt(6);
                int supportCardType = (int)query.GetInt(7);
                int skillSetId      = (int)query.GetInt(8);
                int detailPosX      = (int)query.GetInt(9);
                int detailPosY      = (int)query.GetInt(10);
                int detailScale     = (int)query.GetInt(11);
                int detailRotZ      = (int)query.GetInt(12);
                long startDate      = (long)query.GetLong(13);
                int outingMax       = (int)query.GetInt(14);
                int effectId        = (int)query.GetInt(15);

                orm = new SupportCardData(id, charaId, rarity, exchangeItemId, effectTableId, uniqueEffectId, commandType, commandId, supportCardType, skillSetId, detailPosX, detailPosY, detailScale, detailRotZ, startDate, outingMax, effectId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SupportCardData GetWithCharaIdOrderByIdAsc(int charaId)
        {
            SupportCardData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaIdOrderByIdAsc(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SupportCardData _SelectWithCharaIdOrderByIdAsc(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardData_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardData");
                return null;
            }

            // SELECT `id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data` WHERE `chara_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, charaId)) { return null; }

            SupportCardData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<SupportCardData> GetListWithCharaIdOrderByIdAsc(int charaId)
        {
            int key = (int)charaId;

            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaIdOrderByIdAsc(charaId));
            }

            return _dictionaryWithCharaId[key];
        }

        public List<SupportCardData> MaybeListWithCharaIdOrderByIdAsc(int charaId)
        {
            List<SupportCardData> list = GetListWithCharaIdOrderByIdAsc(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<SupportCardData> _ListSelectWithCharaIdOrderByIdAsc(int charaId)
        {
            List<SupportCardData> _list = new List<SupportCardData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardData_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardData");
                return null;
            }

            // SELECT `id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data` WHERE `chara_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                SupportCardData orm = _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SupportCardData _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id              = (int)query.GetInt(0);
            int rarity          = (int)query.GetInt(1);
            int exchangeItemId  = (int)query.GetInt(2);
            int effectTableId   = (int)query.GetInt(3);
            int uniqueEffectId  = (int)query.GetInt(4);
            int commandType     = (int)query.GetInt(5);
            int commandId       = (int)query.GetInt(6);
            int supportCardType = (int)query.GetInt(7);
            int skillSetId      = (int)query.GetInt(8);
            int detailPosX      = (int)query.GetInt(9);
            int detailPosY      = (int)query.GetInt(10);
            int detailScale     = (int)query.GetInt(11);
            int detailRotZ      = (int)query.GetInt(12);
            long startDate      = (long)query.GetLong(13);
            int outingMax       = (int)query.GetInt(14);
            int effectId        = (int)query.GetInt(15);

            return new SupportCardData(id, charaId, rarity, exchangeItemId, effectTableId, uniqueEffectId, commandType, commandId, supportCardType, skillSetId, detailPosX, detailPosY, detailScale, detailRotZ, startDate, outingMax, effectId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharaId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SupportCardData()) {
                while (query.Step()) {
                    int id              = (int)query.GetInt(0);
                    int charaId         = (int)query.GetInt(1);
                    int rarity          = (int)query.GetInt(2);
                    int exchangeItemId  = (int)query.GetInt(3);
                    int effectTableId   = (int)query.GetInt(4);
                    int uniqueEffectId  = (int)query.GetInt(5);
                    int commandType     = (int)query.GetInt(6);
                    int commandId       = (int)query.GetInt(7);
                    int supportCardType = (int)query.GetInt(8);
                    int skillSetId      = (int)query.GetInt(9);
                    int detailPosX      = (int)query.GetInt(10);
                    int detailPosY      = (int)query.GetInt(11);
                    int detailScale     = (int)query.GetInt(12);
                    int detailRotZ      = (int)query.GetInt(13);
                    long startDate      = (long)query.GetLong(14);
                    int outingMax       = (int)query.GetInt(15);
                    int effectId        = (int)query.GetInt(16);

                    int key = (int)id;
                    SupportCardData orm = new SupportCardData(id, charaId, rarity, exchangeItemId, effectTableId, uniqueEffectId, commandType, commandId, supportCardType, skillSetId, detailPosX, detailPosY, detailScale, detailRotZ, startDate, outingMax, effectId);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class SupportCardData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: exchange_item_id) </summary>
            public readonly int ExchangeItemId;
            /// <summary> (CSV column: effect_table_id) </summary>
            public readonly int EffectTableId;
            /// <summary> (CSV column: unique_effect_id) </summary>
            public readonly int UniqueEffectId;
            /// <summary> (CSV column: command_type) </summary>
            public readonly int CommandType;
            /// <summary> (CSV column: command_id) </summary>
            public readonly int CommandId;
            /// <summary> (CSV column: support_card_type) </summary>
            public readonly int SupportCardType;
            /// <summary> (CSV column: skill_set_id) </summary>
            public readonly int SkillSetId;
            /// <summary> (CSV column: detail_pos_x) </summary>
            public readonly int DetailPosX;
            /// <summary> (CSV column: detail_pos_y) </summary>
            public readonly int DetailPosY;
            /// <summary> (CSV column: detail_scale) </summary>
            public readonly int DetailScale;
            /// <summary> (CSV column: detail_rot_z) </summary>
            public readonly int DetailRotZ;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: outing_max) </summary>
            public readonly int OutingMax;
            /// <summary> (CSV column: effect_id) </summary>
            public readonly int EffectId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardData(int id = 0, int charaId = 0, int rarity = 0, int exchangeItemId = 0, int effectTableId = 0, int uniqueEffectId = 0, int commandType = 0, int commandId = 0, int supportCardType = 0, int skillSetId = 0, int detailPosX = 0, int detailPosY = 0, int detailScale = 0, int detailRotZ = 0, long startDate = 0, int outingMax = 0, int effectId = 0)
            {
                this.Id              = id;
                this.CharaId         = charaId;
                this.Rarity          = rarity;
                this.ExchangeItemId  = exchangeItemId;
                this.EffectTableId   = effectTableId;
                this.UniqueEffectId  = uniqueEffectId;
                this.CommandType     = commandType;
                this.CommandId       = commandId;
                this.SupportCardType = supportCardType;
                this.SkillSetId      = skillSetId;
                this.DetailPosX      = detailPosX;
                this.DetailPosY      = detailPosY;
                this.DetailScale     = detailScale;
                this.DetailRotZ      = detailRotZ;
                this.StartDate       = startDate;
                this.OutingMax       = outingMax;
                this.EffectId        = effectId;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaData : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharaData> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, CharaData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterCharaData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharaData>();
            _db = db;
        }


        public CharaData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharaData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaData");
                return null;
            }

            // SELECT `birth_year`,`birth_month`,`birth_day`,`last_year`,`sex`,`image_color_main`,`image_color_sub`,`ui_color_main`,`ui_color_sub`,`ui_training_color_1`,`ui_training_color_2`,`ui_border_color`,`ui_num_color_1`,`ui_num_color_2`,`ui_turn_color`,`ui_wipe_color_1`,`ui_wipe_color_2`,`ui_wipe_color_3`,`ui_speech_color_1`,`ui_speech_color_2`,`ui_nameplate_color_1`,`ui_nameplate_color_2`,`height`,`bust`,`scale`,`skin`,`shape`,`socks`,`personal_dress`,`tail_model_id`,`race_running_type`,`ear_random_time_min`,`ear_random_time_max`,`tail_random_time_min`,`tail_random_time_max`,`story_ear_random_time_min`,`story_ear_random_time_max`,`story_tail_random_time_min`,`story_tail_random_time_max`,`attachment_model_id`,`mini_mayu_shader_type`,`start_date`,`chara_category`,`love_rank_limit` FROM `chara_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharaData orm = null;

            if (query.Step())
            {
                int birthYear              = (int)query.GetInt(0);
                int birthMonth             = (int)query.GetInt(1);
                int birthDay               = (int)query.GetInt(2);
                int lastYear               = (int)query.GetInt(3);
                int sex                    = (int)query.GetInt(4);
                string imageColorMain      = query.GetText(5);
                string imageColorSub       = query.GetText(6);
                string uiColorMain         = query.GetText(7);
                string uiColorSub          = query.GetText(8);
                string uiTrainingColor1    = query.GetText(9);
                string uiTrainingColor2    = query.GetText(10);
                string uiBorderColor       = query.GetText(11);
                string uiNumColor1         = query.GetText(12);
                string uiNumColor2         = query.GetText(13);
                string uiTurnColor         = query.GetText(14);
                string uiWipeColor1        = query.GetText(15);
                string uiWipeColor2        = query.GetText(16);
                string uiWipeColor3        = query.GetText(17);
                string uiSpeechColor1      = query.GetText(18);
                string uiSpeechColor2      = query.GetText(19);
                string uiNameplateColor1   = query.GetText(20);
                string uiNameplateColor2   = query.GetText(21);
                int height                 = (int)query.GetInt(22);
                int bust                   = (int)query.GetInt(23);
                int scale                  = (int)query.GetInt(24);
                int skin                   = (int)query.GetInt(25);
                int shape                  = (int)query.GetInt(26);
                int socks                  = (int)query.GetInt(27);
                int personalDress          = (int)query.GetInt(28);
                int tailModelId            = (int)query.GetInt(29);
                int raceRunningType        = (int)query.GetInt(30);
                int earRandomTimeMin       = (int)query.GetInt(31);
                int earRandomTimeMax       = (int)query.GetInt(32);
                int tailRandomTimeMin      = (int)query.GetInt(33);
                int tailRandomTimeMax      = (int)query.GetInt(34);
                int storyEarRandomTimeMin  = (int)query.GetInt(35);
                int storyEarRandomTimeMax  = (int)query.GetInt(36);
                int storyTailRandomTimeMin = (int)query.GetInt(37);
                int storyTailRandomTimeMax = (int)query.GetInt(38);
                int attachmentModelId      = (int)query.GetInt(39);
                int miniMayuShaderType     = (int)query.GetInt(40);
                long startDate             = (long)query.GetLong(41);
                int charaCategory          = (int)query.GetInt(42);
                int loveRankLimit          = (int)query.GetInt(43);

                orm = new CharaData(id, birthYear, birthMonth, birthDay, lastYear, sex, imageColorMain, imageColorSub, uiColorMain, uiColorSub, uiTrainingColor1, uiTrainingColor2, uiBorderColor, uiNumColor1, uiNumColor2, uiTurnColor, uiWipeColor1, uiWipeColor2, uiWipeColor3, uiSpeechColor1, uiSpeechColor2, uiNameplateColor1, uiNameplateColor2, height, bust, scale, skin, shape, socks, personalDress, tailModelId, raceRunningType, earRandomTimeMin, earRandomTimeMax, tailRandomTimeMin, tailRandomTimeMax, storyEarRandomTimeMin, storyEarRandomTimeMax, storyTailRandomTimeMin, storyTailRandomTimeMax, attachmentModelId, miniMayuShaderType, startDate, charaCategory, loveRankLimit);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_CharaData()) {
                while (query.Step()) {
                    int id                     = (int)query.GetInt(0);
                    int birthYear              = (int)query.GetInt(1);
                    int birthMonth             = (int)query.GetInt(2);
                    int birthDay               = (int)query.GetInt(3);
                    int lastYear               = (int)query.GetInt(4);
                    int sex                    = (int)query.GetInt(5);
                    string imageColorMain      = query.GetText(6);
                    string imageColorSub       = query.GetText(7);
                    string uiColorMain         = query.GetText(8);
                    string uiColorSub          = query.GetText(9);
                    string uiTrainingColor1    = query.GetText(10);
                    string uiTrainingColor2    = query.GetText(11);
                    string uiBorderColor       = query.GetText(12);
                    string uiNumColor1         = query.GetText(13);
                    string uiNumColor2         = query.GetText(14);
                    string uiTurnColor         = query.GetText(15);
                    string uiWipeColor1        = query.GetText(16);
                    string uiWipeColor2        = query.GetText(17);
                    string uiWipeColor3        = query.GetText(18);
                    string uiSpeechColor1      = query.GetText(19);
                    string uiSpeechColor2      = query.GetText(20);
                    string uiNameplateColor1   = query.GetText(21);
                    string uiNameplateColor2   = query.GetText(22);
                    int height                 = (int)query.GetInt(23);
                    int bust                   = (int)query.GetInt(24);
                    int scale                  = (int)query.GetInt(25);
                    int skin                   = (int)query.GetInt(26);
                    int shape                  = (int)query.GetInt(27);
                    int socks                  = (int)query.GetInt(28);
                    int personalDress          = (int)query.GetInt(29);
                    int tailModelId            = (int)query.GetInt(30);
                    int raceRunningType        = (int)query.GetInt(31);
                    int earRandomTimeMin       = (int)query.GetInt(32);
                    int earRandomTimeMax       = (int)query.GetInt(33);
                    int tailRandomTimeMin      = (int)query.GetInt(34);
                    int tailRandomTimeMax      = (int)query.GetInt(35);
                    int storyEarRandomTimeMin  = (int)query.GetInt(36);
                    int storyEarRandomTimeMax  = (int)query.GetInt(37);
                    int storyTailRandomTimeMin = (int)query.GetInt(38);
                    int storyTailRandomTimeMax = (int)query.GetInt(39);
                    int attachmentModelId      = (int)query.GetInt(40);
                    int miniMayuShaderType     = (int)query.GetInt(41);
                    long startDate             = (long)query.GetLong(42);
                    int charaCategory          = (int)query.GetInt(43);
                    int loveRankLimit          = (int)query.GetInt(44);

                    int key = (int)id;
                    CharaData orm = new CharaData(id, birthYear, birthMonth, birthDay, lastYear, sex, imageColorMain, imageColorSub, uiColorMain, uiColorSub, uiTrainingColor1, uiTrainingColor2, uiBorderColor, uiNumColor1, uiNumColor2, uiTurnColor, uiWipeColor1, uiWipeColor2, uiWipeColor3, uiSpeechColor1, uiSpeechColor2, uiNameplateColor1, uiNameplateColor2, height, bust, scale, skin, shape, socks, personalDress, tailModelId, raceRunningType, earRandomTimeMin, earRandomTimeMax, tailRandomTimeMin, tailRandomTimeMax, storyEarRandomTimeMin, storyEarRandomTimeMax, storyTailRandomTimeMin, storyTailRandomTimeMax, attachmentModelId, miniMayuShaderType, startDate, charaCategory, loveRankLimit);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class CharaData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: birth_year) </summary>
            public readonly int BirthYear;
            /// <summary> (CSV column: birth_month) </summary>
            public readonly int BirthMonth;
            /// <summary> (CSV column: birth_day) </summary>
            public readonly int BirthDay;
            /// <summary> (CSV column: last_year) </summary>
            public readonly int LastYear;
            /// <summary> (CSV column: sex) </summary>
            public readonly int Sex;
            /// <summary> (CSV column: image_color_main) </summary>
            public readonly string ImageColorMain;
            /// <summary> (CSV column: image_color_sub) </summary>
            public readonly string ImageColorSub;
            /// <summary> (CSV column: ui_color_main) </summary>
            public readonly string UiColorMain;
            /// <summary> (CSV column: ui_color_sub) </summary>
            public readonly string UiColorSub;
            /// <summary> (CSV column: ui_training_color_1) </summary>
            public readonly string UiTrainingColor1;
            /// <summary> (CSV column: ui_training_color_2) </summary>
            public readonly string UiTrainingColor2;
            /// <summary> (CSV column: ui_border_color) </summary>
            public readonly string UiBorderColor;
            /// <summary> (CSV column: ui_num_color_1) </summary>
            public readonly string UiNumColor1;
            /// <summary> (CSV column: ui_num_color_2) </summary>
            public readonly string UiNumColor2;
            /// <summary> (CSV column: ui_turn_color) </summary>
            public readonly string UiTurnColor;
            /// <summary> (CSV column: ui_wipe_color_1) </summary>
            public readonly string UiWipeColor1;
            /// <summary> (CSV column: ui_wipe_color_2) </summary>
            public readonly string UiWipeColor2;
            /// <summary> (CSV column: ui_wipe_color_3) </summary>
            public readonly string UiWipeColor3;
            /// <summary> (CSV column: ui_speech_color_1) </summary>
            public readonly string UiSpeechColor1;
            /// <summary> (CSV column: ui_speech_color_2) </summary>
            public readonly string UiSpeechColor2;
            /// <summary> (CSV column: ui_nameplate_color_1) </summary>
            public readonly string UiNameplateColor1;
            /// <summary> (CSV column: ui_nameplate_color_2) </summary>
            public readonly string UiNameplateColor2;
            /// <summary> (CSV column: height) </summary>
            public readonly int Height;
            /// <summary> (CSV column: bust) </summary>
            public readonly int Bust;
            /// <summary> (CSV column: scale) </summary>
            public readonly int Scale;
            /// <summary> (CSV column: skin) </summary>
            public readonly int Skin;
            /// <summary> (CSV column: shape) </summary>
            public readonly int Shape;
            /// <summary> (CSV column: socks) </summary>
            public readonly int Socks;
            /// <summary> (CSV column: personal_dress) </summary>
            public readonly int PersonalDress;
            /// <summary> (CSV column: tail_model_id) </summary>
            public readonly int TailModelId;
            /// <summary> (CSV column: race_running_type) </summary>
            public readonly int RaceRunningType;
            /// <summary> (CSV column: ear_random_time_min) </summary>
            public readonly int EarRandomTimeMin;
            /// <summary> (CSV column: ear_random_time_max) </summary>
            public readonly int EarRandomTimeMax;
            /// <summary> (CSV column: tail_random_time_min) </summary>
            public readonly int TailRandomTimeMin;
            /// <summary> (CSV column: tail_random_time_max) </summary>
            public readonly int TailRandomTimeMax;
            /// <summary> (CSV column: story_ear_random_time_min) </summary>
            public readonly int StoryEarRandomTimeMin;
            /// <summary> (CSV column: story_ear_random_time_max) </summary>
            public readonly int StoryEarRandomTimeMax;
            /// <summary> (CSV column: story_tail_random_time_min) </summary>
            public readonly int StoryTailRandomTimeMin;
            /// <summary> (CSV column: story_tail_random_time_max) </summary>
            public readonly int StoryTailRandomTimeMax;
            /// <summary> (CSV column: attachment_model_id) </summary>
            public readonly int AttachmentModelId;
            /// <summary> (CSV column: mini_mayu_shader_type) </summary>
            public readonly int MiniMayuShaderType;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: chara_category) </summary>
            public readonly int CharaCategory;
            /// <summary> (CSV column: love_rank_limit) </summary>
            public readonly int LoveRankLimit;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaData(int id = 0, int birthYear = 0, int birthMonth = 0, int birthDay = 0, int lastYear = 0, int sex = 0, string imageColorMain = "", string imageColorSub = "", string uiColorMain = "", string uiColorSub = "", string uiTrainingColor1 = "", string uiTrainingColor2 = "", string uiBorderColor = "", string uiNumColor1 = "", string uiNumColor2 = "", string uiTurnColor = "", string uiWipeColor1 = "", string uiWipeColor2 = "", string uiWipeColor3 = "", string uiSpeechColor1 = "", string uiSpeechColor2 = "", string uiNameplateColor1 = "", string uiNameplateColor2 = "", int height = 0, int bust = 0, int scale = 0, int skin = 0, int shape = 0, int socks = 0, int personalDress = 0, int tailModelId = 0, int raceRunningType = 0, int earRandomTimeMin = 0, int earRandomTimeMax = 0, int tailRandomTimeMin = 0, int tailRandomTimeMax = 0, int storyEarRandomTimeMin = 0, int storyEarRandomTimeMax = 0, int storyTailRandomTimeMin = 0, int storyTailRandomTimeMax = 0, int attachmentModelId = 0, int miniMayuShaderType = 0, long startDate = 0, int charaCategory = 0, int loveRankLimit = 0)
            {
                this.Id                     = id;
                this.BirthYear              = birthYear;
                this.BirthMonth             = birthMonth;
                this.BirthDay               = birthDay;
                this.LastYear               = lastYear;
                this.Sex                    = sex;
                this.ImageColorMain         = imageColorMain;
                this.ImageColorSub          = imageColorSub;
                this.UiColorMain            = uiColorMain;
                this.UiColorSub             = uiColorSub;
                this.UiTrainingColor1       = uiTrainingColor1;
                this.UiTrainingColor2       = uiTrainingColor2;
                this.UiBorderColor          = uiBorderColor;
                this.UiNumColor1            = uiNumColor1;
                this.UiNumColor2            = uiNumColor2;
                this.UiTurnColor            = uiTurnColor;
                this.UiWipeColor1           = uiWipeColor1;
                this.UiWipeColor2           = uiWipeColor2;
                this.UiWipeColor3           = uiWipeColor3;
                this.UiSpeechColor1         = uiSpeechColor1;
                this.UiSpeechColor2         = uiSpeechColor2;
                this.UiNameplateColor1      = uiNameplateColor1;
                this.UiNameplateColor2      = uiNameplateColor2;
                this.Height                 = height;
                this.Bust                   = bust;
                this.Scale                  = scale;
                this.Skin                   = skin;
                this.Shape                  = shape;
                this.Socks                  = socks;
                this.PersonalDress          = personalDress;
                this.TailModelId            = tailModelId;
                this.RaceRunningType        = raceRunningType;
                this.EarRandomTimeMin       = earRandomTimeMin;
                this.EarRandomTimeMax       = earRandomTimeMax;
                this.TailRandomTimeMin      = tailRandomTimeMin;
                this.TailRandomTimeMax      = tailRandomTimeMax;
                this.StoryEarRandomTimeMin  = storyEarRandomTimeMin;
                this.StoryEarRandomTimeMax  = storyEarRandomTimeMax;
                this.StoryTailRandomTimeMin = storyTailRandomTimeMin;
                this.StoryTailRandomTimeMax = storyTailRandomTimeMax;
                this.AttachmentModelId      = attachmentModelId;
                this.MiniMayuShaderType     = miniMayuShaderType;
                this.StartDate              = startDate;
                this.CharaCategory          = charaCategory;
                this.LoveRankLimit          = loveRankLimit;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_dress_color_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:dress_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaDressColorSet : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_dress_color_set";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharaDressColorSet> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<CharaDressColorSet>> _dictionaryWithDressId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaDressColorSet(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharaDressColorSet>();
            _dictionaryWithDressId = new Dictionary<int, List<CharaDressColorSet>>();
            _db = db;
        }


        public CharaDressColorSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaDressColorSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaDressColorSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharaDressColorSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaDressColorSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDressColorSet");
                return null;
            }

            // SELECT `dress_id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharaDressColorSet orm = null;

            if (query.Step())
            {
                int dressId        = (int)query.GetInt(0);
                int areaMapId      = (int)query.GetInt(1);
                string colorR1     = query.GetText(2);
                string colorR2     = query.GetText(3);
                string colorG1     = query.GetText(4);
                string colorG2     = query.GetText(5);
                string colorB1     = query.GetText(6);
                string colorB2     = query.GetText(7);
                string toonColorR1 = query.GetText(8);
                string toonColorR2 = query.GetText(9);
                string toonColorG1 = query.GetText(10);
                string toonColorG2 = query.GetText(11);
                string toonColorB1 = query.GetText(12);
                string toonColorB2 = query.GetText(13);

                orm = new CharaDressColorSet(id, dressId, areaMapId, colorR1, colorR2, colorG1, colorG2, colorB1, colorB2, toonColorR1, toonColorR2, toonColorG1, toonColorG2, toonColorB1, toonColorB2);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CharaDressColorSet GetWithDressId(int dressId)
        {
            CharaDressColorSet orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithDressId(dressId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", dressId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharaDressColorSet _SelectWithDressId(int dressId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaDressColorSet_DressId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDressColorSet");
                return null;
            }

            // SELECT `id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set` WHERE `dress_id`=?;
            if (!query.BindInt(1, dressId)) { return null; }

            CharaDressColorSet orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithDressId(query, dressId);
            }

            query.Reset();

            return orm;
        }

        public List<CharaDressColorSet> GetListWithDressId(int dressId)
        {
            int key = (int)dressId;

            if (!_dictionaryWithDressId.ContainsKey(key)) {
                _dictionaryWithDressId.Add(key, _ListSelectWithDressId(dressId));
            }

            return _dictionaryWithDressId[key];
        }

        public List<CharaDressColorSet> MaybeListWithDressId(int dressId)
        {
            List<CharaDressColorSet> list = GetListWithDressId(dressId);
            return list.Count > 0 ? list : null;
        }

        private List<CharaDressColorSet> _ListSelectWithDressId(int dressId)
        {
            List<CharaDressColorSet> _list = new List<CharaDressColorSet>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaDressColorSet_DressId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDressColorSet");
                return null;
            }

            // SELECT `id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set` WHERE `dress_id`=?;
            if (!query.BindInt(1, dressId)) { return null; }

            while (query.Step()) {
                CharaDressColorSet orm = _CreateOrmByQueryResultWithDressId(query, dressId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharaDressColorSet _CreateOrmByQueryResultWithDressId(LibNative.Sqlite3.PreparedQuery query, int dressId)
        {
            int id             = (int)query.GetInt(0);
            int areaMapId      = (int)query.GetInt(1);
            string colorR1     = query.GetText(2);
            string colorR2     = query.GetText(3);
            string colorG1     = query.GetText(4);
            string colorG2     = query.GetText(5);
            string colorB1     = query.GetText(6);
            string colorB2     = query.GetText(7);
            string toonColorR1 = query.GetText(8);
            string toonColorR2 = query.GetText(9);
            string toonColorG1 = query.GetText(10);
            string toonColorG2 = query.GetText(11);
            string toonColorB1 = query.GetText(12);
            string toonColorB2 = query.GetText(13);

            return new CharaDressColorSet(id, dressId, areaMapId, colorR1, colorR2, colorG1, colorG2, colorB1, colorB2, toonColorR1, toonColorR2, toonColorG1, toonColorG2, toonColorB1, toonColorB2);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithDressId.Clear();
        }

        public sealed partial class CharaDressColorSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: dress_id) </summary>
            public readonly int DressId;
            /// <summary> (CSV column: area_map_id) </summary>
            public readonly int AreaMapId;
            /// <summary> (CSV column: color_r1) </summary>
            public readonly string ColorR1;
            /// <summary> (CSV column: color_r2) </summary>
            public readonly string ColorR2;
            /// <summary> (CSV column: color_g1) </summary>
            public readonly string ColorG1;
            /// <summary> (CSV column: color_g2) </summary>
            public readonly string ColorG2;
            /// <summary> (CSV column: color_b1) </summary>
            public readonly string ColorB1;
            /// <summary> (CSV column: color_b2) </summary>
            public readonly string ColorB2;
            /// <summary> (CSV column: toon_color_r1) </summary>
            public readonly string ToonColorR1;
            /// <summary> (CSV column: toon_color_r2) </summary>
            public readonly string ToonColorR2;
            /// <summary> (CSV column: toon_color_g1) </summary>
            public readonly string ToonColorG1;
            /// <summary> (CSV column: toon_color_g2) </summary>
            public readonly string ToonColorG2;
            /// <summary> (CSV column: toon_color_b1) </summary>
            public readonly string ToonColorB1;
            /// <summary> (CSV column: toon_color_b2) </summary>
            public readonly string ToonColorB2;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaDressColorSet(int id = 0, int dressId = 0, int areaMapId = 0, string colorR1 = "", string colorR2 = "", string colorG1 = "", string colorG2 = "", string colorB1 = "", string colorB2 = "", string toonColorR1 = "", string toonColorR2 = "", string toonColorG1 = "", string toonColorG2 = "", string toonColorB1 = "", string toonColorB2 = "")
            {
                this.Id          = id;
                this.DressId     = dressId;
                this.AreaMapId   = areaMapId;
                this.ColorR1     = colorR1;
                this.ColorR2     = colorR2;
                this.ColorG1     = colorG1;
                this.ColorG2     = colorG2;
                this.ColorB1     = colorB1;
                this.ColorB2     = colorB2;
                this.ToonColorR1 = toonColorR1;
                this.ToonColorR2 = toonColorR2;
                this.ToonColorG1 = toonColorG1;
                this.ToonColorG2 = toonColorG2;
                this.ToonColorB1 = toonColorB1;
                this.ToonColorB2 = toonColorB2;
            }
        }
    }
}
#endif

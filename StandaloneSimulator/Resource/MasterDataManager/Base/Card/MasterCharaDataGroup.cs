// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_data_group
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaDataGroup : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_data_group";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharaDataGroup> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaDataGroup(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharaDataGroup>();
            _db = db;
        }


        public CharaDataGroup Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaDataGroup");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaDataGroup", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharaDataGroup _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaDataGroup();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDataGroup");
                return null;
            }

            // SELECT `group_id`,`chara_id` FROM `chara_data_group` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharaDataGroup orm = null;

            if (query.Step())
            {
                int groupId = (int)query.GetInt(0);
                int charaId = (int)query.GetInt(1);

                orm = new CharaDataGroup(id, groupId, charaId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CharaDataGroup GetWithGroupId(int groupId)
        {
            CharaDataGroup orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupId(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharaDataGroup _SelectWithGroupId(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaDataGroup_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDataGroup");
                return null;
            }

            // SELECT `id`,`chara_id` FROM `chara_data_group` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            CharaDataGroup orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupId(query, groupId);
            }

            query.Reset();

            return orm;
        }

        private CharaDataGroup _CreateOrmByQueryResultWithGroupId(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id      = (int)query.GetInt(0);
            int charaId = (int)query.GetInt(1);

            return new CharaDataGroup(id, groupId, charaId);
        }

        public CharaDataGroup GetWithCharaId(int charaId)
        {
            CharaDataGroup orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaId(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharaDataGroup _SelectWithCharaId(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaDataGroup_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDataGroup");
                return null;
            }

            // SELECT `id`,`group_id` FROM `chara_data_group` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            CharaDataGroup orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
            }

            query.Reset();

            return orm;
        }

        private CharaDataGroup _CreateOrmByQueryResultWithCharaId(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id      = (int)query.GetInt(0);
            int groupId = (int)query.GetInt(1);

            return new CharaDataGroup(id, groupId, charaId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class CharaDataGroup
        {
            /// <summary>
            /// ID
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary>
            /// グループID
            /// (CSV column: group_id)
            /// </summary>
            public readonly int GroupId;
            /// <summary>
            /// キャラID
            /// (CSV column: chara_id)
            /// </summary>
            public readonly int CharaId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaDataGroup(int id = 0, int groupId = 0, int charaId = 0)
            {
                this.Id      = id;
                this.GroupId = groupId;
                this.CharaId = charaId;
            }
        }
    }
}
#endif

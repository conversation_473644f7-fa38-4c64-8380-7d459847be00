// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/skill_up_scenario_condition
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:scenario_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSkillUpScenarioCondition : AbstractMasterData
    {
        public const string TABLE_NAME = "skill_up_scenario_condition";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SkillUpScenarioCondition> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SkillUpScenarioCondition>> _dictionaryWithScenarioId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSkillUpScenarioCondition(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SkillUpScenarioCondition>();
            _dictionaryWithScenarioId = new Dictionary<int, List<SkillUpScenarioCondition>>();
            _db = db;
        }


        public SkillUpScenarioCondition Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSkillUpScenarioCondition");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSkillUpScenarioCondition", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SkillUpScenarioCondition _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SkillUpScenarioCondition();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpScenarioCondition");
                return null;
            }

            // SELECT `scenario_id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SkillUpScenarioCondition orm = null;

            if (query.Step())
            {
                int scenarioId  = (int)query.GetInt(0);
                int rank        = (int)query.GetInt(1);
                int conditionId = (int)query.GetInt(2);
                int num         = (int)query.GetInt(3);
                int subNum      = (int)query.GetInt(4);
                int timingType  = (int)query.GetInt(5);
                int countType   = (int)query.GetInt(6);

                orm = new SkillUpScenarioCondition(id, scenarioId, rank, conditionId, num, subNum, timingType, countType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SkillUpScenarioCondition GetWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            SkillUpScenarioCondition orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithScenarioIdOrderByIdAsc(scenarioId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", scenarioId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SkillUpScenarioCondition _SelectWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpScenarioCondition_ScenarioId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpScenarioCondition");
                return null;
            }

            // SELECT `id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition` WHERE `scenario_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            SkillUpScenarioCondition orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(query, scenarioId);
            }

            query.Reset();

            return orm;
        }

        public List<SkillUpScenarioCondition> GetListWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            int key = (int)scenarioId;

            if (!_dictionaryWithScenarioId.ContainsKey(key)) {
                _dictionaryWithScenarioId.Add(key, _ListSelectWithScenarioIdOrderByIdAsc(scenarioId));
            }

            return _dictionaryWithScenarioId[key];
        }

        public List<SkillUpScenarioCondition> MaybeListWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            List<SkillUpScenarioCondition> list = GetListWithScenarioIdOrderByIdAsc(scenarioId);
            return list.Count > 0 ? list : null;
        }

        private List<SkillUpScenarioCondition> _ListSelectWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            List<SkillUpScenarioCondition> _list = new List<SkillUpScenarioCondition>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SkillUpScenarioCondition_ScenarioId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SkillUpScenarioCondition");
                return null;
            }

            // SELECT `id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition` WHERE `scenario_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            while (query.Step()) {
                SkillUpScenarioCondition orm = _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(query, scenarioId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SkillUpScenarioCondition _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int scenarioId)
        {
            int id          = (int)query.GetInt(0);
            int rank        = (int)query.GetInt(1);
            int conditionId = (int)query.GetInt(2);
            int num         = (int)query.GetInt(3);
            int subNum      = (int)query.GetInt(4);
            int timingType  = (int)query.GetInt(5);
            int countType   = (int)query.GetInt(6);

            return new SkillUpScenarioCondition(id, scenarioId, rank, conditionId, num, subNum, timingType, countType);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithScenarioId.Clear();
        }

        public sealed partial class SkillUpScenarioCondition
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: scenario_id) </summary>
            public readonly int ScenarioId;
            /// <summary> (CSV column: rank) </summary>
            public readonly int Rank;
            /// <summary> (CSV column: condition_id) </summary>
            public readonly int ConditionId;
            /// <summary> (CSV column: num) </summary>
            public readonly int Num;
            /// <summary> (CSV column: sub_num) </summary>
            public readonly int SubNum;
            /// <summary> (CSV column: timing_type) </summary>
            public readonly int TimingType;
            /// <summary> (CSV column: count_type) </summary>
            public readonly int CountType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SkillUpScenarioCondition(int id = 0, int scenarioId = 0, int rank = 0, int conditionId = 0, int num = 0, int subNum = 0, int timingType = 0, int countType = 0)
            {
                this.Id          = id;
                this.ScenarioId  = scenarioId;
                this.Rank        = rank;
                this.ConditionId = conditionId;
                this.Num         = num;
                this.SubNum      = subNum;
                this.TimingType  = timingType;
                this.CountType   = countType;
            }
        }
    }
}
#endif

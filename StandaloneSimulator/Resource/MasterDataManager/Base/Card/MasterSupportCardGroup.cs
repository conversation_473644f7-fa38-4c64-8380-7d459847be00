// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_group
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:support_card_id], [:chara_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardGroup : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_group";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardGroup> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SupportCardGroup>> _dictionaryWithSupportCardId = null;
        private Dictionary<int, List<SupportCardGroup>> _dictionaryWithCharaId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardGroup(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardGroup>();
            _dictionaryWithSupportCardId = new Dictionary<int, List<SupportCardGroup>>();
            _dictionaryWithCharaId = new Dictionary<int, List<SupportCardGroup>>();
            _db = db;
        }


        public SupportCardGroup Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardGroup");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardGroup", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardGroup _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardGroup();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardGroup");
                return null;
            }

            // SELECT `support_card_id`,`chara_id`,`outing_max` FROM `support_card_group` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SupportCardGroup orm = null;

            if (query.Step())
            {
                int supportCardId = (int)query.GetInt(0);
                int charaId       = (int)query.GetInt(1);
                int outingMax     = (int)query.GetInt(2);

                orm = new SupportCardGroup(id, supportCardId, charaId, outingMax);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SupportCardGroup GetWithSupportCardId(int supportCardId)
        {
            SupportCardGroup orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithSupportCardId(supportCardId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", supportCardId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SupportCardGroup _SelectWithSupportCardId(int supportCardId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardGroup_SupportCardId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardGroup");
                return null;
            }

            // SELECT `id`,`chara_id`,`outing_max` FROM `support_card_group` WHERE `support_card_id`=?;
            if (!query.BindInt(1, supportCardId)) { return null; }

            SupportCardGroup orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithSupportCardId(query, supportCardId);
            }

            query.Reset();

            return orm;
        }

        public List<SupportCardGroup> GetListWithSupportCardId(int supportCardId)
        {
            int key = (int)supportCardId;

            if (!_dictionaryWithSupportCardId.ContainsKey(key)) {
                _dictionaryWithSupportCardId.Add(key, _ListSelectWithSupportCardId(supportCardId));
            }

            return _dictionaryWithSupportCardId[key];
        }

        public List<SupportCardGroup> MaybeListWithSupportCardId(int supportCardId)
        {
            List<SupportCardGroup> list = GetListWithSupportCardId(supportCardId);
            return list.Count > 0 ? list : null;
        }

        private List<SupportCardGroup> _ListSelectWithSupportCardId(int supportCardId)
        {
            List<SupportCardGroup> _list = new List<SupportCardGroup>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardGroup_SupportCardId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardGroup");
                return null;
            }

            // SELECT `id`,`chara_id`,`outing_max` FROM `support_card_group` WHERE `support_card_id`=?;
            if (!query.BindInt(1, supportCardId)) { return null; }

            while (query.Step()) {
                SupportCardGroup orm = _CreateOrmByQueryResultWithSupportCardId(query, supportCardId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SupportCardGroup _CreateOrmByQueryResultWithSupportCardId(LibNative.Sqlite3.PreparedQuery query, int supportCardId)
        {
            int id        = (int)query.GetInt(0);
            int charaId   = (int)query.GetInt(1);
            int outingMax = (int)query.GetInt(2);

            return new SupportCardGroup(id, supportCardId, charaId, outingMax);
        }

        public SupportCardGroup GetWithCharaId(int charaId)
        {
            SupportCardGroup orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaId(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SupportCardGroup _SelectWithCharaId(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardGroup_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardGroup");
                return null;
            }

            // SELECT `id`,`support_card_id`,`outing_max` FROM `support_card_group` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            SupportCardGroup orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<SupportCardGroup> GetListWithCharaId(int charaId)
        {
            int key = (int)charaId;

            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaId(charaId));
            }

            return _dictionaryWithCharaId[key];
        }

        public List<SupportCardGroup> MaybeListWithCharaId(int charaId)
        {
            List<SupportCardGroup> list = GetListWithCharaId(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<SupportCardGroup> _ListSelectWithCharaId(int charaId)
        {
            List<SupportCardGroup> _list = new List<SupportCardGroup>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardGroup_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardGroup");
                return null;
            }

            // SELECT `id`,`support_card_id`,`outing_max` FROM `support_card_group` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                SupportCardGroup orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SupportCardGroup _CreateOrmByQueryResultWithCharaId(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id            = (int)query.GetInt(0);
            int supportCardId = (int)query.GetInt(1);
            int outingMax     = (int)query.GetInt(2);

            return new SupportCardGroup(id, supportCardId, charaId, outingMax);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithSupportCardId.Clear();
            _dictionaryWithCharaId.Clear();
        }

        public sealed partial class SupportCardGroup
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: support_card_id) </summary>
            public readonly int SupportCardId;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: outing_max) </summary>
            public readonly int OutingMax;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardGroup(int id = 0, int supportCardId = 0, int charaId = 0, int outingMax = 0)
            {
                this.Id            = id;
                this.SupportCardId = supportCardId;
                this.CharaId       = charaId;
                this.OutingMax     = outingMax;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/dress_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:chara_id], [:condition_type], [:body_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterDressData : AbstractMasterData
    {
        public const string TABLE_NAME = "dress_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, DressData> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<DressData>> _dictionaryWithCharaId = null;
        private Dictionary<int, List<DressData>> _dictionaryWithConditionType = null;
        private Dictionary<int, List<DressData>> _dictionaryWithBodyType = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, DressData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterDressData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterDressData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, DressData>();
            _dictionaryWithCharaId = new Dictionary<int, List<DressData>>();
            _dictionaryWithConditionType = new Dictionary<int, List<DressData>>();
            _dictionaryWithBodyType = new Dictionary<int, List<DressData>>();
            _db = db;
        }


        public DressData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterDressData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterDressData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private DressData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_DressData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            DressData orm = null;

            if (query.Step())
            {
                int conditionType      = (int)query.GetInt(0);
                bool haveMini          = (query.GetInt(1) == 1);
                int generalPurpose     = (int)query.GetInt(2);
                int costumeType        = (int)query.GetInt(3);
                int charaId            = (int)query.GetInt(4);
                int useGender          = (int)query.GetInt(5);
                int bodyShape          = (int)query.GetInt(6);
                int bodyType           = (int)query.GetInt(7);
                int bodyTypeSub        = (int)query.GetInt(8);
                int bodySetting        = (int)query.GetInt(9);
                int useRace            = (int)query.GetInt(10);
                int useLive            = (int)query.GetInt(11);
                int useLiveTheater     = (int)query.GetInt(12);
                int useHome            = (int)query.GetInt(13);
                int useDressChange     = (int)query.GetInt(14);
                int isWet              = (int)query.GetInt(15);
                int isDirt             = (int)query.GetInt(16);
                int headSubId          = (int)query.GetInt(17);
                int useSeason          = (int)query.GetInt(18);
                string dressColorMain  = query.GetText(19);
                string dressColorSub   = query.GetText(20);
                int colorNum           = (int)query.GetInt(21);
                int dispOrder          = (int)query.GetInt(22);
                int tailModelId        = (int)query.GetInt(23);
                int tailModelSubId     = (int)query.GetInt(24);
                int miniMayuShaderType = (int)query.GetInt(25);
                long startTime         = (long)query.GetLong(26);
                long endTime           = (long)query.GetLong(27);

                orm = new DressData(id, conditionType, haveMini, generalPurpose, costumeType, charaId, useGender, bodyShape, bodyType, bodyTypeSub, bodySetting, useRace, useLive, useLiveTheater, useHome, useDressChange, isWet, isDirt, headSubId, useSeason, dressColorMain, dressColorSub, colorNum, dispOrder, tailModelId, tailModelSubId, miniMayuShaderType, startTime, endTime);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public DressData GetWithCharaIdOrderByIdAsc(int charaId)
        {
            DressData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaIdOrderByIdAsc(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private DressData _SelectWithCharaIdOrderByIdAsc(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_DressData_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `chara_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, charaId)) { return null; }

            DressData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<DressData> GetListWithCharaIdOrderByIdAsc(int charaId)
        {
            int key = (int)charaId;

            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaIdOrderByIdAsc(charaId));
            }

            return _dictionaryWithCharaId[key];
        }

        public List<DressData> MaybeListWithCharaIdOrderByIdAsc(int charaId)
        {
            List<DressData> list = GetListWithCharaIdOrderByIdAsc(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<DressData> _ListSelectWithCharaIdOrderByIdAsc(int charaId)
        {
            List<DressData> _list = new List<DressData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_DressData_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `chara_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                DressData orm = _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private DressData _CreateOrmByQueryResultWithCharaIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id                 = (int)query.GetInt(0);
            int conditionType      = (int)query.GetInt(1);
            bool haveMini          = (query.GetInt(2) == 1);
            int generalPurpose     = (int)query.GetInt(3);
            int costumeType        = (int)query.GetInt(4);
            int useGender          = (int)query.GetInt(5);
            int bodyShape          = (int)query.GetInt(6);
            int bodyType           = (int)query.GetInt(7);
            int bodyTypeSub        = (int)query.GetInt(8);
            int bodySetting        = (int)query.GetInt(9);
            int useRace            = (int)query.GetInt(10);
            int useLive            = (int)query.GetInt(11);
            int useLiveTheater     = (int)query.GetInt(12);
            int useHome            = (int)query.GetInt(13);
            int useDressChange     = (int)query.GetInt(14);
            int isWet              = (int)query.GetInt(15);
            int isDirt             = (int)query.GetInt(16);
            int headSubId          = (int)query.GetInt(17);
            int useSeason          = (int)query.GetInt(18);
            string dressColorMain  = query.GetText(19);
            string dressColorSub   = query.GetText(20);
            int colorNum           = (int)query.GetInt(21);
            int dispOrder          = (int)query.GetInt(22);
            int tailModelId        = (int)query.GetInt(23);
            int tailModelSubId     = (int)query.GetInt(24);
            int miniMayuShaderType = (int)query.GetInt(25);
            long startTime         = (long)query.GetLong(26);
            long endTime           = (long)query.GetLong(27);

            return new DressData(id, conditionType, haveMini, generalPurpose, costumeType, charaId, useGender, bodyShape, bodyType, bodyTypeSub, bodySetting, useRace, useLive, useLiveTheater, useHome, useDressChange, isWet, isDirt, headSubId, useSeason, dressColorMain, dressColorSub, colorNum, dispOrder, tailModelId, tailModelSubId, miniMayuShaderType, startTime, endTime);
        }

        public DressData GetWithConditionTypeOrderByIdAsc(int conditionType)
        {
            DressData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionTypeOrderByIdAsc(conditionType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private DressData _SelectWithConditionTypeOrderByIdAsc(int conditionType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_DressData_ConditionType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `id`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `condition_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            DressData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionTypeOrderByIdAsc(query, conditionType);
            }

            query.Reset();

            return orm;
        }

        public List<DressData> GetListWithConditionTypeOrderByIdAsc(int conditionType)
        {
            int key = (int)conditionType;

            if (!_dictionaryWithConditionType.ContainsKey(key)) {
                _dictionaryWithConditionType.Add(key, _ListSelectWithConditionTypeOrderByIdAsc(conditionType));
            }

            return _dictionaryWithConditionType[key];
        }

        public List<DressData> MaybeListWithConditionTypeOrderByIdAsc(int conditionType)
        {
            List<DressData> list = GetListWithConditionTypeOrderByIdAsc(conditionType);
            return list.Count > 0 ? list : null;
        }

        private List<DressData> _ListSelectWithConditionTypeOrderByIdAsc(int conditionType)
        {
            List<DressData> _list = new List<DressData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_DressData_ConditionType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `id`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `condition_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            while (query.Step()) {
                DressData orm = _CreateOrmByQueryResultWithConditionTypeOrderByIdAsc(query, conditionType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private DressData _CreateOrmByQueryResultWithConditionTypeOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int conditionType)
        {
            int id                 = (int)query.GetInt(0);
            bool haveMini          = (query.GetInt(1) == 1);
            int generalPurpose     = (int)query.GetInt(2);
            int costumeType        = (int)query.GetInt(3);
            int charaId            = (int)query.GetInt(4);
            int useGender          = (int)query.GetInt(5);
            int bodyShape          = (int)query.GetInt(6);
            int bodyType           = (int)query.GetInt(7);
            int bodyTypeSub        = (int)query.GetInt(8);
            int bodySetting        = (int)query.GetInt(9);
            int useRace            = (int)query.GetInt(10);
            int useLive            = (int)query.GetInt(11);
            int useLiveTheater     = (int)query.GetInt(12);
            int useHome            = (int)query.GetInt(13);
            int useDressChange     = (int)query.GetInt(14);
            int isWet              = (int)query.GetInt(15);
            int isDirt             = (int)query.GetInt(16);
            int headSubId          = (int)query.GetInt(17);
            int useSeason          = (int)query.GetInt(18);
            string dressColorMain  = query.GetText(19);
            string dressColorSub   = query.GetText(20);
            int colorNum           = (int)query.GetInt(21);
            int dispOrder          = (int)query.GetInt(22);
            int tailModelId        = (int)query.GetInt(23);
            int tailModelSubId     = (int)query.GetInt(24);
            int miniMayuShaderType = (int)query.GetInt(25);
            long startTime         = (long)query.GetLong(26);
            long endTime           = (long)query.GetLong(27);

            return new DressData(id, conditionType, haveMini, generalPurpose, costumeType, charaId, useGender, bodyShape, bodyType, bodyTypeSub, bodySetting, useRace, useLive, useLiveTheater, useHome, useDressChange, isWet, isDirt, headSubId, useSeason, dressColorMain, dressColorSub, colorNum, dispOrder, tailModelId, tailModelSubId, miniMayuShaderType, startTime, endTime);
        }

        public DressData GetWithBodyTypeOrderByIdAsc(int bodyType)
        {
            DressData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithBodyTypeOrderByIdAsc(bodyType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", bodyType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private DressData _SelectWithBodyTypeOrderByIdAsc(int bodyType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_DressData_BodyType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `body_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, bodyType)) { return null; }

            DressData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithBodyTypeOrderByIdAsc(query, bodyType);
            }

            query.Reset();

            return orm;
        }

        public List<DressData> GetListWithBodyTypeOrderByIdAsc(int bodyType)
        {
            int key = (int)bodyType;

            if (!_dictionaryWithBodyType.ContainsKey(key)) {
                _dictionaryWithBodyType.Add(key, _ListSelectWithBodyTypeOrderByIdAsc(bodyType));
            }

            return _dictionaryWithBodyType[key];
        }

        public List<DressData> MaybeListWithBodyTypeOrderByIdAsc(int bodyType)
        {
            List<DressData> list = GetListWithBodyTypeOrderByIdAsc(bodyType);
            return list.Count > 0 ? list : null;
        }

        private List<DressData> _ListSelectWithBodyTypeOrderByIdAsc(int bodyType)
        {
            List<DressData> _list = new List<DressData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_DressData_BodyType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `body_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, bodyType)) { return null; }

            while (query.Step()) {
                DressData orm = _CreateOrmByQueryResultWithBodyTypeOrderByIdAsc(query, bodyType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private DressData _CreateOrmByQueryResultWithBodyTypeOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int bodyType)
        {
            int id                 = (int)query.GetInt(0);
            int conditionType      = (int)query.GetInt(1);
            bool haveMini          = (query.GetInt(2) == 1);
            int generalPurpose     = (int)query.GetInt(3);
            int costumeType        = (int)query.GetInt(4);
            int charaId            = (int)query.GetInt(5);
            int useGender          = (int)query.GetInt(6);
            int bodyShape          = (int)query.GetInt(7);
            int bodyTypeSub        = (int)query.GetInt(8);
            int bodySetting        = (int)query.GetInt(9);
            int useRace            = (int)query.GetInt(10);
            int useLive            = (int)query.GetInt(11);
            int useLiveTheater     = (int)query.GetInt(12);
            int useHome            = (int)query.GetInt(13);
            int useDressChange     = (int)query.GetInt(14);
            int isWet              = (int)query.GetInt(15);
            int isDirt             = (int)query.GetInt(16);
            int headSubId          = (int)query.GetInt(17);
            int useSeason          = (int)query.GetInt(18);
            string dressColorMain  = query.GetText(19);
            string dressColorSub   = query.GetText(20);
            int colorNum           = (int)query.GetInt(21);
            int dispOrder          = (int)query.GetInt(22);
            int tailModelId        = (int)query.GetInt(23);
            int tailModelSubId     = (int)query.GetInt(24);
            int miniMayuShaderType = (int)query.GetInt(25);
            long startTime         = (long)query.GetLong(26);
            long endTime           = (long)query.GetLong(27);

            return new DressData(id, conditionType, haveMini, generalPurpose, costumeType, charaId, useGender, bodyShape, bodyType, bodyTypeSub, bodySetting, useRace, useLive, useLiveTheater, useHome, useDressChange, isWet, isDirt, headSubId, useSeason, dressColorMain, dressColorSub, colorNum, dispOrder, tailModelId, tailModelSubId, miniMayuShaderType, startTime, endTime);
        }

        public DressData GetWithCharaIdAndCostumeType(int charaId, int costumeType)
        {
            DressData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaIdAndCostumeType(charaId, costumeType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", charaId, costumeType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private DressData _SelectWithCharaIdAndCostumeType(int charaId, int costumeType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_DressData_CharaId_CostumeType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for DressData");
                return null;
            }

            // SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `chara_id`=? AND `costume_type`=?;
            if (!query.BindInt(1, charaId))     { return null; }
            if (!query.BindInt(2, costumeType)) { return null; }

            DressData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaIdAndCostumeType(query, charaId, costumeType);
            }

            query.Reset();

            return orm;
        }

        private DressData _CreateOrmByQueryResultWithCharaIdAndCostumeType(LibNative.Sqlite3.PreparedQuery query, int charaId, int costumeType)
        {
            int id                 = (int)query.GetInt(0);
            int conditionType      = (int)query.GetInt(1);
            bool haveMini          = (query.GetInt(2) == 1);
            int generalPurpose     = (int)query.GetInt(3);
            int useGender          = (int)query.GetInt(4);
            int bodyShape          = (int)query.GetInt(5);
            int bodyType           = (int)query.GetInt(6);
            int bodyTypeSub        = (int)query.GetInt(7);
            int bodySetting        = (int)query.GetInt(8);
            int useRace            = (int)query.GetInt(9);
            int useLive            = (int)query.GetInt(10);
            int useLiveTheater     = (int)query.GetInt(11);
            int useHome            = (int)query.GetInt(12);
            int useDressChange     = (int)query.GetInt(13);
            int isWet              = (int)query.GetInt(14);
            int isDirt             = (int)query.GetInt(15);
            int headSubId          = (int)query.GetInt(16);
            int useSeason          = (int)query.GetInt(17);
            string dressColorMain  = query.GetText(18);
            string dressColorSub   = query.GetText(19);
            int colorNum           = (int)query.GetInt(20);
            int dispOrder          = (int)query.GetInt(21);
            int tailModelId        = (int)query.GetInt(22);
            int tailModelSubId     = (int)query.GetInt(23);
            int miniMayuShaderType = (int)query.GetInt(24);
            long startTime         = (long)query.GetLong(25);
            long endTime           = (long)query.GetLong(26);

            return new DressData(id, conditionType, haveMini, generalPurpose, costumeType, charaId, useGender, bodyShape, bodyType, bodyTypeSub, bodySetting, useRace, useLive, useLiveTheater, useHome, useDressChange, isWet, isDirt, headSubId, useSeason, dressColorMain, dressColorSub, colorNum, dispOrder, tailModelId, tailModelSubId, miniMayuShaderType, startTime, endTime);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharaId.Clear();
            _dictionaryWithConditionType.Clear();
            _dictionaryWithBodyType.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_DressData()) {
                while (query.Step()) {
                    int id                 = (int)query.GetInt(0);
                    int conditionType      = (int)query.GetInt(1);
                    bool haveMini          = (query.GetInt(2) == 1);
                    int generalPurpose     = (int)query.GetInt(3);
                    int costumeType        = (int)query.GetInt(4);
                    int charaId            = (int)query.GetInt(5);
                    int useGender          = (int)query.GetInt(6);
                    int bodyShape          = (int)query.GetInt(7);
                    int bodyType           = (int)query.GetInt(8);
                    int bodyTypeSub        = (int)query.GetInt(9);
                    int bodySetting        = (int)query.GetInt(10);
                    int useRace            = (int)query.GetInt(11);
                    int useLive            = (int)query.GetInt(12);
                    int useLiveTheater     = (int)query.GetInt(13);
                    int useHome            = (int)query.GetInt(14);
                    int useDressChange     = (int)query.GetInt(15);
                    int isWet              = (int)query.GetInt(16);
                    int isDirt             = (int)query.GetInt(17);
                    int headSubId          = (int)query.GetInt(18);
                    int useSeason          = (int)query.GetInt(19);
                    string dressColorMain  = query.GetText(20);
                    string dressColorSub   = query.GetText(21);
                    int colorNum           = (int)query.GetInt(22);
                    int dispOrder          = (int)query.GetInt(23);
                    int tailModelId        = (int)query.GetInt(24);
                    int tailModelSubId     = (int)query.GetInt(25);
                    int miniMayuShaderType = (int)query.GetInt(26);
                    long startTime         = (long)query.GetLong(27);
                    long endTime           = (long)query.GetLong(28);

                    int key = (int)id;
                    DressData orm = new DressData(id, conditionType, haveMini, generalPurpose, costumeType, charaId, useGender, bodyShape, bodyType, bodyTypeSub, bodySetting, useRace, useLive, useLiveTheater, useHome, useDressChange, isWet, isDirt, headSubId, useSeason, dressColorMain, dressColorSub, colorNum, dispOrder, tailModelId, tailModelSubId, miniMayuShaderType, startTime, endTime);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class DressData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: have_mini) </summary>
            public readonly bool HaveMini;
            /// <summary> (CSV column: general_purpose) </summary>
            public readonly int GeneralPurpose;
            /// <summary> (CSV column: costume_type) </summary>
            public readonly int CostumeType;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: use_gender) </summary>
            public readonly int UseGender;
            /// <summary> (CSV column: body_shape) </summary>
            public readonly int BodyShape;
            /// <summary> (CSV column: body_type) </summary>
            public readonly int BodyType;
            /// <summary> (CSV column: body_type_sub) </summary>
            public readonly int BodyTypeSub;
            /// <summary> (CSV column: body_setting) </summary>
            public readonly int BodySetting;
            /// <summary> (CSV column: use_race) </summary>
            public readonly int UseRace;
            /// <summary> (CSV column: use_live) </summary>
            public readonly int UseLive;
            /// <summary> (CSV column: use_live_theater) </summary>
            public readonly int UseLiveTheater;
            /// <summary> (CSV column: use_home) </summary>
            public readonly int UseHome;
            /// <summary> (CSV column: use_dress_change) </summary>
            public readonly int UseDressChange;
            /// <summary> (CSV column: is_wet) </summary>
            public readonly int IsWet;
            /// <summary> (CSV column: is_dirt) </summary>
            public readonly int IsDirt;
            /// <summary> (CSV column: head_sub_id) </summary>
            public readonly int HeadSubId;
            /// <summary> (CSV column: use_season) </summary>
            public readonly int UseSeason;
            /// <summary> (CSV column: dress_color_main) </summary>
            public readonly string DressColorMain;
            /// <summary> (CSV column: dress_color_sub) </summary>
            public readonly string DressColorSub;
            /// <summary> (CSV column: color_num) </summary>
            public readonly int ColorNum;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary> (CSV column: tail_model_id) </summary>
            public readonly int TailModelId;
            /// <summary> (CSV column: tail_model_sub_id) </summary>
            public readonly int TailModelSubId;
            /// <summary> (CSV column: mini_mayu_shader_type) </summary>
            public readonly int MiniMayuShaderType;
            /// <summary> (CSV column: start_time) </summary>
            public readonly long StartTime;
            /// <summary> (CSV column: end_time) </summary>
            public readonly long EndTime;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public DressData(int id = 0, int conditionType = 0, bool haveMini = false, int generalPurpose = 0, int costumeType = 0, int charaId = 0, int useGender = 0, int bodyShape = 0, int bodyType = 0, int bodyTypeSub = 0, int bodySetting = 0, int useRace = 0, int useLive = 0, int useLiveTheater = 0, int useHome = 0, int useDressChange = 0, int isWet = 0, int isDirt = 0, int headSubId = 0, int useSeason = 0, string dressColorMain = "", string dressColorSub = "", int colorNum = 0, int dispOrder = 0, int tailModelId = 0, int tailModelSubId = 0, int miniMayuShaderType = 0, long startTime = 0, long endTime = 0)
            {
                this.Id                 = id;
                this.ConditionType      = conditionType;
                this.HaveMini           = haveMini;
                this.GeneralPurpose     = generalPurpose;
                this.CostumeType        = costumeType;
                this.CharaId            = charaId;
                this.UseGender          = useGender;
                this.BodyShape          = bodyShape;
                this.BodyType           = bodyType;
                this.BodyTypeSub        = bodyTypeSub;
                this.BodySetting        = bodySetting;
                this.UseRace            = useRace;
                this.UseLive            = useLive;
                this.UseLiveTheater     = useLiveTheater;
                this.UseHome            = useHome;
                this.UseDressChange     = useDressChange;
                this.IsWet              = isWet;
                this.IsDirt             = isDirt;
                this.HeadSubId          = headSubId;
                this.UseSeason          = useSeason;
                this.DressColorMain     = dressColorMain;
                this.DressColorSub      = dressColorSub;
                this.ColorNum           = colorNum;
                this.DispOrder          = dispOrder;
                this.TailModelId        = tailModelId;
                this.TailModelSubId     = tailModelSubId;
                this.MiniMayuShaderType = miniMayuShaderType;
                this.StartTime          = startTime;
                this.EndTime            = endTime;
            }
        }
    }
}
#endif

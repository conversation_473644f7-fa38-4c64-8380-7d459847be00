// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/character_prop_animation
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:prop_id, :scene_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharacterPropAnimation : AbstractMasterData
    {
        public const string TABLE_NAME = "character_prop_animation";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharacterPropAnimation> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<CharacterPropAnimation>> _dictionaryWithPropIdAndSceneType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharacterPropAnimation(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharacterPropAnimation>();
            _dictionaryWithPropIdAndSceneType = new Dictionary<ulong, List<CharacterPropAnimation>>();
            _db = db;
        }


        public CharacterPropAnimation Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharacterPropAnimation");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharacterPropAnimation", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharacterPropAnimation _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharacterPropAnimation();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterPropAnimation");
                return null;
            }

            // SELECT `prop_id`,`prop_anim_id`,`layer_index`,`use_state_name`,`scene_type` FROM `character_prop_animation` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharacterPropAnimation orm = null;

            if (query.Step())
            {
                int propId          = (int)query.GetInt(0);
                int propAnimId      = (int)query.GetInt(1);
                int layerIndex      = (int)query.GetInt(2);
                string useStateName = query.GetText(3);
                int sceneType       = (int)query.GetInt(4);

                orm = new CharacterPropAnimation(id, propId, propAnimId, layerIndex, useStateName, sceneType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CharacterPropAnimation GetWithPropIdAndSceneTypeOrderByIdAsc(int propId, int sceneType)
        {
            CharacterPropAnimation orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithPropIdAndSceneTypeOrderByIdAsc(propId, sceneType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", propId, sceneType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharacterPropAnimation _SelectWithPropIdAndSceneTypeOrderByIdAsc(int propId, int sceneType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterPropAnimation_PropId_SceneType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterPropAnimation");
                return null;
            }

            // SELECT `id`,`prop_anim_id`,`layer_index`,`use_state_name` FROM `character_prop_animation` WHERE `prop_id`=? AND `scene_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, propId))    { return null; }
            if (!query.BindInt(2, sceneType)) { return null; }

            CharacterPropAnimation orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithPropIdAndSceneTypeOrderByIdAsc(query, propId, sceneType);
            }

            query.Reset();

            return orm;
        }

        public List<CharacterPropAnimation> GetListWithPropIdAndSceneTypeOrderByIdAsc(int propId, int sceneType)
        {
            ulong key = ((uint)unchecked((ulong)((int)propId))) | ((((ulong)unchecked((ulong)((int)sceneType)))) << 32);

            if (!_dictionaryWithPropIdAndSceneType.ContainsKey(key)) {
                _dictionaryWithPropIdAndSceneType.Add(key, _ListSelectWithPropIdAndSceneTypeOrderByIdAsc(propId, sceneType));
            }

            return _dictionaryWithPropIdAndSceneType[key];
        }

        public List<CharacterPropAnimation> MaybeListWithPropIdAndSceneTypeOrderByIdAsc(int propId, int sceneType)
        {
            List<CharacterPropAnimation> list = GetListWithPropIdAndSceneTypeOrderByIdAsc(propId, sceneType);
            return list.Count > 0 ? list : null;
        }

        private List<CharacterPropAnimation> _ListSelectWithPropIdAndSceneTypeOrderByIdAsc(int propId, int sceneType)
        {
            List<CharacterPropAnimation> _list = new List<CharacterPropAnimation>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterPropAnimation_PropId_SceneType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterPropAnimation");
                return null;
            }

            // SELECT `id`,`prop_anim_id`,`layer_index`,`use_state_name` FROM `character_prop_animation` WHERE `prop_id`=? AND `scene_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, propId))    { return null; }
            if (!query.BindInt(2, sceneType)) { return null; }

            while (query.Step()) {
                CharacterPropAnimation orm = _CreateOrmByQueryResultWithPropIdAndSceneTypeOrderByIdAsc(query, propId, sceneType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharacterPropAnimation _CreateOrmByQueryResultWithPropIdAndSceneTypeOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int propId, int sceneType)
        {
            int id              = (int)query.GetInt(0);
            int propAnimId      = (int)query.GetInt(1);
            int layerIndex      = (int)query.GetInt(2);
            string useStateName = query.GetText(3);

            return new CharacterPropAnimation(id, propId, propAnimId, layerIndex, useStateName, sceneType);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithPropIdAndSceneType.Clear();
        }

        public sealed partial class CharacterPropAnimation
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: prop_id) </summary>
            public readonly int PropId;
            /// <summary> (CSV column: prop_anim_id) </summary>
            public readonly int PropAnimId;
            /// <summary> (CSV column: layer_index) </summary>
            public readonly int LayerIndex;
            /// <summary> (CSV column: use_state_name) </summary>
            public readonly string UseStateName;
            /// <summary> (CSV column: scene_type) </summary>
            public readonly int SceneType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharacterPropAnimation(int id = 0, int propId = 0, int propAnimId = 0, int layerIndex = 0, string useStateName = "", int sceneType = 0)
            {
                this.Id           = id;
                this.PropId       = propId;
                this.PropAnimId   = propAnimId;
                this.LayerIndex   = layerIndex;
                this.UseStateName = useStateName;
                this.SceneType    = sceneType;
            }
        }
    }
}
#endif

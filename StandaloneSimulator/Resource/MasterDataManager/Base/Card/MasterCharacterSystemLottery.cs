// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/character_system_lottery
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:chara_id], [:chara_id, :trigger], [:trigger, :param1]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharacterSystemLottery : AbstractMasterData
    {
        public const string TABLE_NAME = "character_system_lottery";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharacterSystemLottery> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<CharacterSystemLottery>> _dictionaryWithCharaId = null;
        private Dictionary<ulong, List<CharacterSystemLottery>> _dictionaryWithCharaIdAndTrigger = null;
        private Dictionary<ulong, List<CharacterSystemLottery>> _dictionaryWithTriggerAndParam1 = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharacterSystemLottery(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharacterSystemLottery>();
            _dictionaryWithCharaId = new Dictionary<int, List<CharacterSystemLottery>>();
            _dictionaryWithCharaIdAndTrigger = new Dictionary<ulong, List<CharacterSystemLottery>>();
            _dictionaryWithTriggerAndParam1 = new Dictionary<ulong, List<CharacterSystemLottery>>();
            _db = db;
        }


        public CharacterSystemLottery Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharacterSystemLottery");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharacterSystemLottery", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharacterSystemLottery _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharacterSystemLottery();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemLottery");
                return null;
            }

            // SELECT `chara_id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharacterSystemLottery orm = null;

            if (query.Step())
            {
                int charaId      = (int)query.GetInt(0);
                int cardId       = (int)query.GetInt(1);
                int cardRarityId = (int)query.GetInt(2);
                int trigger      = (int)query.GetInt(3);
                int param1       = (int)query.GetInt(4);
                int per          = (int)query.GetInt(5);
                int priority     = (int)query.GetInt(6);
                int sysTextId    = (int)query.GetInt(7);

                orm = new CharacterSystemLottery(id, charaId, cardId, cardRarityId, trigger, param1, per, priority, sysTextId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CharacterSystemLottery GetWithCharaId(int charaId)
        {
            CharacterSystemLottery orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaId(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharacterSystemLottery _SelectWithCharaId(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemLottery_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemLottery");
                return null;
            }

            // SELECT `id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            CharacterSystemLottery orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<CharacterSystemLottery> GetListWithCharaId(int charaId)
        {
            int key = (int)charaId;

            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaId(charaId));
            }

            return _dictionaryWithCharaId[key];
        }

        public List<CharacterSystemLottery> MaybeListWithCharaId(int charaId)
        {
            List<CharacterSystemLottery> list = GetListWithCharaId(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<CharacterSystemLottery> _ListSelectWithCharaId(int charaId)
        {
            List<CharacterSystemLottery> _list = new List<CharacterSystemLottery>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemLottery_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemLottery");
                return null;
            }

            // SELECT `id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                CharacterSystemLottery orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharacterSystemLottery _CreateOrmByQueryResultWithCharaId(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id           = (int)query.GetInt(0);
            int cardId       = (int)query.GetInt(1);
            int cardRarityId = (int)query.GetInt(2);
            int trigger      = (int)query.GetInt(3);
            int param1       = (int)query.GetInt(4);
            int per          = (int)query.GetInt(5);
            int priority     = (int)query.GetInt(6);
            int sysTextId    = (int)query.GetInt(7);

            return new CharacterSystemLottery(id, charaId, cardId, cardRarityId, trigger, param1, per, priority, sysTextId);
        }

        public CharacterSystemLottery GetWithCharaIdAndTrigger(int charaId, int trigger)
        {
            CharacterSystemLottery orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaIdAndTrigger(charaId, trigger);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", charaId, trigger));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharacterSystemLottery _SelectWithCharaIdAndTrigger(int charaId, int trigger)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemLottery_CharaId_Trigger();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemLottery");
                return null;
            }

            // SELECT `id`,`card_id`,`card_rarity_id`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=? AND `trigger`=?;
            if (!query.BindInt(1, charaId)) { return null; }
            if (!query.BindInt(2, trigger)) { return null; }

            CharacterSystemLottery orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaIdAndTrigger(query, charaId, trigger);
            }

            query.Reset();

            return orm;
        }

        public List<CharacterSystemLottery> GetListWithCharaIdAndTrigger(int charaId, int trigger)
        {
            ulong key = ((uint)unchecked((ulong)((int)charaId))) | ((((ulong)unchecked((ulong)((int)trigger)))) << 32);

            if (!_dictionaryWithCharaIdAndTrigger.ContainsKey(key)) {
                _dictionaryWithCharaIdAndTrigger.Add(key, _ListSelectWithCharaIdAndTrigger(charaId, trigger));
            }

            return _dictionaryWithCharaIdAndTrigger[key];
        }

        public List<CharacterSystemLottery> MaybeListWithCharaIdAndTrigger(int charaId, int trigger)
        {
            List<CharacterSystemLottery> list = GetListWithCharaIdAndTrigger(charaId, trigger);
            return list.Count > 0 ? list : null;
        }

        private List<CharacterSystemLottery> _ListSelectWithCharaIdAndTrigger(int charaId, int trigger)
        {
            List<CharacterSystemLottery> _list = new List<CharacterSystemLottery>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemLottery_CharaId_Trigger();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemLottery");
                return null;
            }

            // SELECT `id`,`card_id`,`card_rarity_id`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=? AND `trigger`=?;
            if (!query.BindInt(1, charaId)) { return null; }
            if (!query.BindInt(2, trigger)) { return null; }

            while (query.Step()) {
                CharacterSystemLottery orm = _CreateOrmByQueryResultWithCharaIdAndTrigger(query, charaId, trigger);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharacterSystemLottery _CreateOrmByQueryResultWithCharaIdAndTrigger(LibNative.Sqlite3.PreparedQuery query, int charaId, int trigger)
        {
            int id           = (int)query.GetInt(0);
            int cardId       = (int)query.GetInt(1);
            int cardRarityId = (int)query.GetInt(2);
            int param1       = (int)query.GetInt(3);
            int per          = (int)query.GetInt(4);
            int priority     = (int)query.GetInt(5);
            int sysTextId    = (int)query.GetInt(6);

            return new CharacterSystemLottery(id, charaId, cardId, cardRarityId, trigger, param1, per, priority, sysTextId);
        }

        public CharacterSystemLottery GetWithTriggerAndParam1(int trigger, int param1)
        {
            CharacterSystemLottery orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithTriggerAndParam1(trigger, param1);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", trigger, param1));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharacterSystemLottery _SelectWithTriggerAndParam1(int trigger, int param1)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemLottery_Trigger_Param1();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemLottery");
                return null;
            }

            // SELECT `id`,`chara_id`,`card_id`,`card_rarity_id`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `trigger`=? AND `param1`=?;
            if (!query.BindInt(1, trigger)) { return null; }
            if (!query.BindInt(2, param1))  { return null; }

            CharacterSystemLottery orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithTriggerAndParam1(query, trigger, param1);
            }

            query.Reset();

            return orm;
        }

        public List<CharacterSystemLottery> GetListWithTriggerAndParam1(int trigger, int param1)
        {
            ulong key = ((uint)unchecked((ulong)((int)trigger))) | ((((ulong)unchecked((ulong)((int)param1)))) << 32);

            if (!_dictionaryWithTriggerAndParam1.ContainsKey(key)) {
                _dictionaryWithTriggerAndParam1.Add(key, _ListSelectWithTriggerAndParam1(trigger, param1));
            }

            return _dictionaryWithTriggerAndParam1[key];
        }

        public List<CharacterSystemLottery> MaybeListWithTriggerAndParam1(int trigger, int param1)
        {
            List<CharacterSystemLottery> list = GetListWithTriggerAndParam1(trigger, param1);
            return list.Count > 0 ? list : null;
        }

        private List<CharacterSystemLottery> _ListSelectWithTriggerAndParam1(int trigger, int param1)
        {
            List<CharacterSystemLottery> _list = new List<CharacterSystemLottery>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharacterSystemLottery_Trigger_Param1();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharacterSystemLottery");
                return null;
            }

            // SELECT `id`,`chara_id`,`card_id`,`card_rarity_id`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `trigger`=? AND `param1`=?;
            if (!query.BindInt(1, trigger)) { return null; }
            if (!query.BindInt(2, param1))  { return null; }

            while (query.Step()) {
                CharacterSystemLottery orm = _CreateOrmByQueryResultWithTriggerAndParam1(query, trigger, param1);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharacterSystemLottery _CreateOrmByQueryResultWithTriggerAndParam1(LibNative.Sqlite3.PreparedQuery query, int trigger, int param1)
        {
            int id           = (int)query.GetInt(0);
            int charaId      = (int)query.GetInt(1);
            int cardId       = (int)query.GetInt(2);
            int cardRarityId = (int)query.GetInt(3);
            int per          = (int)query.GetInt(4);
            int priority     = (int)query.GetInt(5);
            int sysTextId    = (int)query.GetInt(6);

            return new CharacterSystemLottery(id, charaId, cardId, cardRarityId, trigger, param1, per, priority, sysTextId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharaId.Clear();
            _dictionaryWithCharaIdAndTrigger.Clear();
            _dictionaryWithTriggerAndParam1.Clear();
        }

        public sealed partial class CharacterSystemLottery
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: card_id) </summary>
            public readonly int CardId;
            /// <summary> (CSV column: card_rarity_id) </summary>
            public readonly int CardRarityId;
            /// <summary> (CSV column: trigger) </summary>
            public readonly int Trigger;
            /// <summary> (CSV column: param1) </summary>
            public readonly int Param1;
            /// <summary> (CSV column: per) </summary>
            public readonly int Per;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: sys_text_id) </summary>
            public readonly int SysTextId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharacterSystemLottery(int id = 0, int charaId = 0, int cardId = 0, int cardRarityId = 0, int trigger = 0, int param1 = 0, int per = 0, int priority = 0, int sysTextId = 0)
            {
                this.Id           = id;
                this.CharaId      = charaId;
                this.CardId       = cardId;
                this.CardRarityId = cardRarityId;
                this.Trigger      = trigger;
                this.Param1       = param1;
                this.Per          = per;
                this.Priority     = priority;
                this.SysTextId    = sysTextId;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/facial_mouth_change
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:chara_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterFacialMouthChange : AbstractMasterData
    {
        public const string TABLE_NAME = "facial_mouth_change";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, FacialMouthChange> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<FacialMouthChange>> _dictionaryWithCharaId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterFacialMouthChange(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, FacialMouthChange>();
            _dictionaryWithCharaId = new Dictionary<int, List<FacialMouthChange>>();
            _db = db;
        }


        public FacialMouthChange Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterFacialMouthChange");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterFacialMouthChange", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private FacialMouthChange _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_FacialMouthChange();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for FacialMouthChange");
                return null;
            }

            // SELECT `chara_id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            FacialMouthChange orm = null;

            if (query.Step())
            {
                int charaId             = (int)query.GetInt(0);
                string beforeFacialname = query.GetText(1);
                string afterFacialname  = query.GetText(2);

                orm = new FacialMouthChange(id, charaId, beforeFacialname, afterFacialname);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public FacialMouthChange GetWithCharaIdAndBeforeFacialname(int charaId, string beforeFacialname)
        {
            FacialMouthChange orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaIdAndBeforeFacialname(charaId, beforeFacialname);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", charaId, beforeFacialname));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private FacialMouthChange _SelectWithCharaIdAndBeforeFacialname(int charaId, string beforeFacialname)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_FacialMouthChange_CharaId_BeforeFacialname();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for FacialMouthChange");
                return null;
            }

            // SELECT `id`,`after_facialname` FROM `facial_mouth_change` WHERE `chara_id`=? AND `before_facialname`=?;
            if (!query.BindInt(1, charaId))           { return null; }
            if (!query.BindText(2, beforeFacialname)) { return null; }

            FacialMouthChange orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaIdAndBeforeFacialname(query, charaId, beforeFacialname);
            }

            query.Reset();

            return orm;
        }

        private FacialMouthChange _CreateOrmByQueryResultWithCharaIdAndBeforeFacialname(LibNative.Sqlite3.PreparedQuery query, int charaId, string beforeFacialname)
        {
            int id                 = (int)query.GetInt(0);
            string afterFacialname = query.GetText(1);

            return new FacialMouthChange(id, charaId, beforeFacialname, afterFacialname);
        }

        public FacialMouthChange GetWithCharaId(int charaId)
        {
            FacialMouthChange orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaId(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private FacialMouthChange _SelectWithCharaId(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_FacialMouthChange_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for FacialMouthChange");
                return null;
            }

            // SELECT `id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            FacialMouthChange orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<FacialMouthChange> GetListWithCharaId(int charaId)
        {
            int key = (int)charaId;

            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaId(charaId));
            }

            return _dictionaryWithCharaId[key];
        }

        public List<FacialMouthChange> MaybeListWithCharaId(int charaId)
        {
            List<FacialMouthChange> list = GetListWithCharaId(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<FacialMouthChange> _ListSelectWithCharaId(int charaId)
        {
            List<FacialMouthChange> _list = new List<FacialMouthChange>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_FacialMouthChange_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for FacialMouthChange");
                return null;
            }

            // SELECT `id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                FacialMouthChange orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private FacialMouthChange _CreateOrmByQueryResultWithCharaId(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id                  = (int)query.GetInt(0);
            string beforeFacialname = query.GetText(1);
            string afterFacialname  = query.GetText(2);

            return new FacialMouthChange(id, charaId, beforeFacialname, afterFacialname);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharaId.Clear();
        }

        public sealed partial class FacialMouthChange
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: before_facialname) </summary>
            public readonly string BeforeFacialname;
            /// <summary> (CSV column: after_facialname) </summary>
            public readonly string AfterFacialname;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public FacialMouthChange(int id = 0, int charaId = 0, string beforeFacialname = "", string afterFacialname = "")
            {
                this.Id               = id;
                this.CharaId          = charaId;
                this.BeforeFacialname = beforeFacialname;
                this.AfterFacialname  = afterFacialname;
            }
        }
    }
}
#endif

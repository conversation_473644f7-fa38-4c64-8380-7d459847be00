// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_type
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:target_scene, :target_cut]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaType : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_type";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<string> _notFounds = null;

        // cache dictionary
        private Dictionary<string, CharaType> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<CharaType>> _dictionaryWithTargetSceneAndTargetCut = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaType(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<string, CharaType>();
            _dictionaryWithTargetSceneAndTargetCut = new Dictionary<ulong, List<CharaType>>();
            _db = db;
        }


        public CharaType Get(string id)
        {
            string key = id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaType");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<string>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaType", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharaType _SelectOne(string id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaType");
                return null;
            }

            // SELECT `chara_id`,`target_scene`,`target_cut`,`target_type`,`value` FROM `chara_type` WHERE `id`=?;
            if (!query.BindText(1, id)) { return null; }

            CharaType orm = null;

            if (query.Step())
            {
                int charaId     = (int)query.GetInt(0);
                int targetScene = (int)query.GetInt(1);
                int targetCut   = (int)query.GetInt(2);
                int targetType  = (int)query.GetInt(3);
                int value       = (int)query.GetInt(4);

                orm = new CharaType(id, charaId, targetScene, targetCut, targetType, value);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CharaType GetWithTargetSceneAndTargetCutOrderByTargetSceneAsc(int targetScene, int targetCut)
        {
            CharaType orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithTargetSceneAndTargetCutOrderByTargetSceneAsc(targetScene, targetCut);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", targetScene, targetCut));
                } else {
                    string key = orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharaType _SelectWithTargetSceneAndTargetCutOrderByTargetSceneAsc(int targetScene, int targetCut)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaType_TargetScene_TargetCut();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaType");
                return null;
            }

            // SELECT `id`,`chara_id`,`target_type`,`value` FROM `chara_type` WHERE `target_scene`=? AND `target_cut`=? ORDER BY `target_scene` ASC;
            if (!query.BindInt(1, targetScene)) { return null; }
            if (!query.BindInt(2, targetCut))   { return null; }

            CharaType orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithTargetSceneAndTargetCutOrderByTargetSceneAsc(query, targetScene, targetCut);
            }

            query.Reset();

            return orm;
        }

        public List<CharaType> GetListWithTargetSceneAndTargetCutOrderByTargetSceneAsc(int targetScene, int targetCut)
        {
            ulong key = ((uint)unchecked((ulong)((int)targetScene))) | ((((ulong)unchecked((ulong)((int)targetCut)))) << 32);

            if (!_dictionaryWithTargetSceneAndTargetCut.ContainsKey(key)) {
                _dictionaryWithTargetSceneAndTargetCut.Add(key, _ListSelectWithTargetSceneAndTargetCutOrderByTargetSceneAsc(targetScene, targetCut));
            }

            return _dictionaryWithTargetSceneAndTargetCut[key];
        }

        public List<CharaType> MaybeListWithTargetSceneAndTargetCutOrderByTargetSceneAsc(int targetScene, int targetCut)
        {
            List<CharaType> list = GetListWithTargetSceneAndTargetCutOrderByTargetSceneAsc(targetScene, targetCut);
            return list.Count > 0 ? list : null;
        }

        private List<CharaType> _ListSelectWithTargetSceneAndTargetCutOrderByTargetSceneAsc(int targetScene, int targetCut)
        {
            List<CharaType> _list = new List<CharaType>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaType_TargetScene_TargetCut();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaType");
                return null;
            }

            // SELECT `id`,`chara_id`,`target_type`,`value` FROM `chara_type` WHERE `target_scene`=? AND `target_cut`=? ORDER BY `target_scene` ASC;
            if (!query.BindInt(1, targetScene)) { return null; }
            if (!query.BindInt(2, targetCut))   { return null; }

            while (query.Step()) {
                CharaType orm = _CreateOrmByQueryResultWithTargetSceneAndTargetCutOrderByTargetSceneAsc(query, targetScene, targetCut);
                string key = orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharaType _CreateOrmByQueryResultWithTargetSceneAndTargetCutOrderByTargetSceneAsc(LibNative.Sqlite3.PreparedQuery query, int targetScene, int targetCut)
        {
            string id      = query.GetText(0);
            int charaId    = (int)query.GetInt(1);
            int targetType = (int)query.GetInt(2);
            int value      = (int)query.GetInt(3);

            return new CharaType(id, charaId, targetScene, targetCut, targetType, value);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithTargetSceneAndTargetCut.Clear();
        }

        public sealed partial class CharaType
        {
            /// <summary>
            /// ダミーのID
            /// (CSV column: id)
            /// </summary>
            public readonly string Id;
            /// <summary>
            /// CharaID
            /// (CSV column: chara_id)
            /// </summary>
            public readonly int CharaId;
            /// <summary>
            /// chara_type内のシーン番号
            /// (CSV column: target_scene)
            /// </summary>
            public readonly int TargetScene;
            /// <summary>
            /// カット番号。育成CutであればCommandId
            /// (CSV column: target_cut)
            /// </summary>
            public readonly int TargetCut;
            /// <summary>
            /// カット内指定。1つのカット内で複数のIDを使いたい場合に使う
            /// (CSV column: target_type)
            /// </summary>
            public readonly int TargetType;
            /// <summary>
            /// 性格値
            /// (CSV column: value)
            /// </summary>
            public readonly int Value;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaType(string id = "", int charaId = 0, int targetScene = 0, int targetCut = 0, int targetType = 0, int value = 0)
            {
                this.Id          = id;
                this.CharaId     = charaId;
                this.TargetScene = targetScene;
                this.TargetCut   = targetCut;
                this.TargetType  = targetType;
                this.Value       = value;
            }
        }
    }
}
#endif

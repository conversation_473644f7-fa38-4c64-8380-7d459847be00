// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_limit
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardLimit : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_limit";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardLimit> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardLimit(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardLimit>();
            _db = db;
        }


        public SupportCardLimit Get(int rarity)
        {
            int key = (int)rarity;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardLimit");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(rarity);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardLimit", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardLimit _SelectOne(int rarity)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardLimit();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardLimit");
                return null;
            }

            // SELECT `limit_0`,`limit_1`,`limit_2`,`limit_3`,`limit_4` FROM `support_card_limit` WHERE `rarity`=?;
            if (!query.BindInt(1, rarity)) { return null; }

            SupportCardLimit orm = null;

            if (query.Step())
            {
                int limit0 = (int)query.GetInt(0);
                int limit1 = (int)query.GetInt(1);
                int limit2 = (int)query.GetInt(2);
                int limit3 = (int)query.GetInt(3);
                int limit4 = (int)query.GetInt(4);

                orm = new SupportCardLimit(rarity, limit0, limit1, limit2, limit3, limit4);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", rarity));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class SupportCardLimit
        {
            /// <summary> (CSV column: rarity) </summary>
            public readonly int Rarity;
            /// <summary> (CSV column: limit_0) </summary>
            public readonly int Limit0;
            /// <summary> (CSV column: limit_1) </summary>
            public readonly int Limit1;
            /// <summary> (CSV column: limit_2) </summary>
            public readonly int Limit2;
            /// <summary> (CSV column: limit_3) </summary>
            public readonly int Limit3;
            /// <summary> (CSV column: limit_4) </summary>
            public readonly int Limit4;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardLimit(int rarity = 0, int limit0 = 0, int limit1 = 0, int limit2 = 0, int limit3 = 0, int limit4 = 0)
            {
                this.Rarity = rarity;
                this.Limit0 = limit0;
                this.Limit1 = limit1;
                this.Limit2 = limit2;
                this.Limit3 = limit3;
                this.Limit4 = limit4;
            }
        }
    }
}
#endif

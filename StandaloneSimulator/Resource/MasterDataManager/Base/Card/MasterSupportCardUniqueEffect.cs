// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_unique_effect
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - [:type_0, :value_0, :value_0_1, :value_0_2, :value_0_3, :value_0_4, :type_1, :value_1, :value_1_1, :value_1_2, :value_1_3, :value_1_4]
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardUniqueEffect : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_unique_effect";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SupportCardUniqueEffect> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardUniqueEffect(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SupportCardUniqueEffect>();
            _db = db;
        }


        public SupportCardUniqueEffect Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardUniqueEffect");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardUniqueEffect", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardUniqueEffect _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardUniqueEffect();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardUniqueEffect");
                return null;
            }

            // SELECT `lv` FROM `support_card_unique_effect` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SupportCardUniqueEffect orm = null;

            if (query.Step())
            {
                int lv = (int)query.GetInt(0);

                orm = new SupportCardUniqueEffect(id, lv);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class SupportCardUniqueEffect
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: lv) </summary>
            public readonly int Lv;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardUniqueEffect(int id = 0, int lv = 0)
            {
                this.Id = id;
                this.Lv = lv;
            }
        }
    }
}
#endif

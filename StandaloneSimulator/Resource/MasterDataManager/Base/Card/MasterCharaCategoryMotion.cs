// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_category_motion
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaCategoryMotion : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_category_motion";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharaCategoryMotion> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, CharaCategoryMotion> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterCharaCategoryMotion");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaCategoryMotion(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharaCategoryMotion>();
            _db = db;
        }


        public CharaCategoryMotion Get(int charaId)
        {
            int key = (int)charaId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaCategoryMotion");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(charaId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaCategoryMotion", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private CharaCategoryMotion _SelectOne(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaCategoryMotion();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaCategoryMotion");
                return null;
            }

            // SELECT `standby_motion_1`,`standby_motion_2`,`standby_motion_3`,`standby_motion_4`,`standby_motion_5`,`standby_motion_6` FROM `chara_category_motion` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            CharaCategoryMotion orm = null;

            if (query.Step())
            {
                int standbyMotion1 = (int)query.GetInt(0);
                int standbyMotion2 = (int)query.GetInt(1);
                int standbyMotion3 = (int)query.GetInt(2);
                int standbyMotion4 = (int)query.GetInt(3);
                int standbyMotion5 = (int)query.GetInt(4);
                int standbyMotion6 = (int)query.GetInt(5);

                orm = new CharaCategoryMotion(charaId, standbyMotion1, standbyMotion2, standbyMotion3, standbyMotion4, standbyMotion5, standbyMotion6);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", charaId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_CharaCategoryMotion()) {
                while (query.Step()) {
                    int charaId        = (int)query.GetInt(0);
                    int standbyMotion1 = (int)query.GetInt(1);
                    int standbyMotion2 = (int)query.GetInt(2);
                    int standbyMotion3 = (int)query.GetInt(3);
                    int standbyMotion4 = (int)query.GetInt(4);
                    int standbyMotion5 = (int)query.GetInt(5);
                    int standbyMotion6 = (int)query.GetInt(6);

                    int key = (int)charaId;
                    CharaCategoryMotion orm = new CharaCategoryMotion(charaId, standbyMotion1, standbyMotion2, standbyMotion3, standbyMotion4, standbyMotion5, standbyMotion6);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class CharaCategoryMotion
        {
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: standby_motion_1) </summary>
            public readonly int StandbyMotion1;
            /// <summary> (CSV column: standby_motion_2) </summary>
            public readonly int StandbyMotion2;
            /// <summary> (CSV column: standby_motion_3) </summary>
            public readonly int StandbyMotion3;
            /// <summary> (CSV column: standby_motion_4) </summary>
            public readonly int StandbyMotion4;
            /// <summary> (CSV column: standby_motion_5) </summary>
            public readonly int StandbyMotion5;
            /// <summary> (CSV column: standby_motion_6) </summary>
            public readonly int StandbyMotion6;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaCategoryMotion(int charaId = 0, int standbyMotion1 = 0, int standbyMotion2 = 0, int standbyMotion3 = 0, int standbyMotion4 = 0, int standbyMotion5 = 0, int standbyMotion6 = 0)
            {
                this.CharaId        = charaId;
                this.StandbyMotion1 = standbyMotion1;
                this.StandbyMotion2 = standbyMotion2;
                this.StandbyMotion3 = standbyMotion3;
                this.StandbyMotion4 = standbyMotion4;
                this.StandbyMotion5 = standbyMotion5;
                this.StandbyMotion6 = standbyMotion6;
            }
        }
    }
}
#endif

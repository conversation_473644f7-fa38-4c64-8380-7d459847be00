// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public class MasterCardDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterCardData masterCardData                                         { get; private set; }
        public MasterCardRarityData masterCardRarityData                             { get; private set; }
        public MasterCardTalentUpgrade masterCardTalentUpgrade                       { get; private set; }
        public MasterSupportCardData masterSupportCardData                           { get; private set; }
        public MasterSupportCardLimit masterSupportCardLimit                         { get; private set; }
        public MasterSupportCardLevel masterSupportCardLevel                         { get; private set; }
        public MasterSupportCardEffectTable masterSupportCardEffectTable             { get; private set; }
        public MasterSupportCardUniqueEffect masterSupportCardUniqueEffect           { get; private set; }
        public MasterSupportCardEffectFilter masterSupportCardEffectFilter           { get; private set; }
        public MasterSupportCardEffectFilterGroup masterSupportCardEffectFilterGroup { get; private set; }
        public MasterSupportCardTeamScoreBonus masterSupportCardTeamScoreBonus       { get; private set; }
        public MasterCharaData masterCharaData                                       { get; private set; }
        public MasterCharaDataGroup masterCharaDataGroup                             { get; private set; }
        public MasterCharaType masterCharaType                                       { get; private set; }
        public MasterCharaCategoryMotion masterCharaCategoryMotion                   { get; private set; }
        public MasterCharaMotionSet masterCharaMotionSet                             { get; private set; }
        public MasterCharacterPropAnimation masterCharacterPropAnimation             { get; private set; }
        public MasterDressData masterDressData                                       { get; private set; }
        public MasterLoveRank masterLoveRank                                         { get; private set; }
        public MasterNeedPieceNumData masterNeedPieceNumData                         { get; private set; }
        public MasterSkillData masterSkillData                                       { get; private set; }
        public MasterSkillSet masterSkillSet                                         { get; private set; }
        public MasterAvailableSkillSet masterAvailableSkillSet                       { get; private set; }
        public MasterSkillExp masterSkillExp                                         { get; private set; }
        public MasterSkillLevelValue masterSkillLevelValue                           { get; private set; }
        public MasterSkillUpgradeDescription masterSkillUpgradeDescription           { get; private set; }
        public MasterSkillUpgradeSpeciality masterSkillUpgradeSpeciality             { get; private set; }
        public MasterSkillUpgradeCondition masterSkillUpgradeCondition               { get; private set; }
        public MasterSkillUpScenarioCondition masterSkillUpScenarioCondition         { get; private set; }
        public MasterCharacterSystemText masterCharacterSystemText                   { get; private set; }
        public MasterCharacterSystemLottery masterCharacterSystemLottery             { get; private set; }
        public MasterFaceTypeData masterFaceTypeData                                 { get; private set; }
        public MasterFacialMouthChange masterFacialMouthChange                       { get; private set; }
        public MasterCharaMotionAct masterCharaMotionAct                             { get; private set; }
        public MasterRandomEarTailMotion masterRandomEarTailMotion                   { get; private set; }
        public MasterNickname masterNickname                                         { get; private set; }
        public MasterSupportCardLimitBreak masterSupportCardLimitBreak               { get; private set; }
        public MasterCardTalentHintUpgrade masterCardTalentHintUpgrade               { get; private set; }
        public MasterSupportCardGroup masterSupportCardGroup                         { get; private set; }
        public MasterCharaDressColorSetDefault masterCharaDressColorSetDefault       { get; private set; }
        public MasterCharaDressColorSet masterCharaDressColorSet                     { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCardData                     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCardRarityData               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCardTalentUpgrade            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardData              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardLimit             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardLevel             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardEffectTable       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardUniqueEffect      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardEffectFilter      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardEffectFilterGroup = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardTeamScoreBonus    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaData                    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaDataGroup               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaType                    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaCategoryMotion          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaMotionSet               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharacterPropAnimation       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterDressData                    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLoveRank                     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterNeedPieceNumData             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillData                    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillSet                     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterAvailableSkillSet            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillExp                     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillLevelValue              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillUpgradeDescription      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillUpgradeSpeciality       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillUpgradeCondition        = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSkillUpScenarioCondition     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharacterSystemText          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharacterSystemLottery       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterFaceTypeData                 = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterFacialMouthChange            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaMotionAct               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRandomEarTailMotion          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterNickname                     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardLimitBreak        = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCardTalentHintUpgrade        = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterSupportCardGroup             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaDressColorSetDefault    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCharaDressColorSet           = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_cardData_charaId                            = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_cardData_getPieceId                         = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_cardRarityData_cardId                       = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_cardRarityData_raceDressId                  = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_cardRarityData_cardId_rarity                = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_cardTalentUpgrade_talentGroupId_talentLevel = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_supportCardData_charaId                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_supportCardLevel_rarity                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_supportCardEffectTable_id                   = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_supportCardEffectFilter_groupId             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_supportCardTeamScoreBonus_level             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_charaDataGroup_groupId                      = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_charaDataGroup_charaId                      = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_charaType_targetScene_targetCut             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_characterPropAnimation_propId_sceneType     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_dressData_charaId                           = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_dressData_conditionType                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_dressData_bodyType                          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_dressData_charaId_costumeType               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_loveRank_rank                               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_needPieceNumData_rarity                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillData_groupId                           = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_availableSkillSet_availableSkillSetId       = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillExp_type                               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillLevelValue_abilityType                 = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillUpgradeDescription_cardId              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillUpgradeDescription_cardId_rank         = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillUpgradeSpeciality_scenarioId           = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillUpgradeSpeciality_baseSkillId          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillUpgradeCondition_descriptionId         = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_skillUpScenarioCondition_scenarioId         = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_characterSystemText_characterId             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_characterSystemText_voiceId                 = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_characterSystemLottery_charaId              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_characterSystemLottery_charaId_trigger      = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_characterSystemLottery_trigger_param1       = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_facialMouthChange_charaId_beforeFacialname  = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_facialMouthChange_charaId                   = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_charaMotionAct_charaId_commandName          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_charaMotionAct_charaId                      = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_randomEarTailMotion_partsType               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_nickname_userShow                           = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_cardTalentHintUpgrade_rarity_talentLevel    = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_supportCardGroup_supportCardId              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_supportCardGroup_charaId                    = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_charaDressColorSetDefault_charaId           = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_charaDressColorSet_dressId                  = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterCardDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterCardData                     = new MasterCardData(this);
            this.masterCardRarityData               = new MasterCardRarityData(this);
            this.masterCardTalentUpgrade            = new MasterCardTalentUpgrade(this);
            this.masterSupportCardData              = new MasterSupportCardData(this);
            this.masterSupportCardLimit             = new MasterSupportCardLimit(this);
            this.masterSupportCardLevel             = new MasterSupportCardLevel(this);
            this.masterSupportCardEffectTable       = new MasterSupportCardEffectTable(this);
            this.masterSupportCardUniqueEffect      = new MasterSupportCardUniqueEffect(this);
            this.masterSupportCardEffectFilter      = new MasterSupportCardEffectFilter(this);
            this.masterSupportCardEffectFilterGroup = new MasterSupportCardEffectFilterGroup(this);
            this.masterSupportCardTeamScoreBonus    = new MasterSupportCardTeamScoreBonus(this);
            this.masterCharaData                    = new MasterCharaData(this);
            this.masterCharaDataGroup               = new MasterCharaDataGroup(this);
            this.masterCharaType                    = new MasterCharaType(this);
            this.masterCharaCategoryMotion          = new MasterCharaCategoryMotion(this);
            this.masterCharaMotionSet               = new MasterCharaMotionSet(this);
            this.masterCharacterPropAnimation       = new MasterCharacterPropAnimation(this);
            this.masterDressData                    = new MasterDressData(this);
            this.masterLoveRank                     = new MasterLoveRank(this);
            this.masterNeedPieceNumData             = new MasterNeedPieceNumData(this);
            this.masterSkillData                    = new MasterSkillData(this);
            this.masterSkillSet                     = new MasterSkillSet(this);
            this.masterAvailableSkillSet            = new MasterAvailableSkillSet(this);
            this.masterSkillExp                     = new MasterSkillExp(this);
            this.masterSkillLevelValue              = new MasterSkillLevelValue(this);
            this.masterSkillUpgradeDescription      = new MasterSkillUpgradeDescription(this);
            this.masterSkillUpgradeSpeciality       = new MasterSkillUpgradeSpeciality(this);
            this.masterSkillUpgradeCondition        = new MasterSkillUpgradeCondition(this);
            this.masterSkillUpScenarioCondition     = new MasterSkillUpScenarioCondition(this);
            this.masterCharacterSystemText          = new MasterCharacterSystemText(this);
            this.masterCharacterSystemLottery       = new MasterCharacterSystemLottery(this);
            this.masterFaceTypeData                 = new MasterFaceTypeData(this);
            this.masterFacialMouthChange            = new MasterFacialMouthChange(this);
            this.masterCharaMotionAct               = new MasterCharaMotionAct(this);
            this.masterRandomEarTailMotion          = new MasterRandomEarTailMotion(this);
            this.masterNickname                     = new MasterNickname(this);
            this.masterSupportCardLimitBreak        = new MasterSupportCardLimitBreak(this);
            this.masterCardTalentHintUpgrade        = new MasterCardTalentHintUpgrade(this);
            this.masterSupportCardGroup             = new MasterSupportCardGroup(this);
            this.masterCharaDressColorSetDefault    = new MasterCharaDressColorSetDefault(this);
            this.masterCharaDressColorSet           = new MasterCharaDressColorSet(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet card database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterCardData != null) { _selectQuery_masterCardData.Dispose(); _selectQuery_masterCardData = null; }
            if (_indexedSelectQuery_cardData_charaId != null) { _indexedSelectQuery_cardData_charaId.Dispose(); _indexedSelectQuery_cardData_charaId = null; }
            if (_indexedSelectQuery_cardData_getPieceId != null) { _indexedSelectQuery_cardData_getPieceId.Dispose(); _indexedSelectQuery_cardData_getPieceId = null; }
            if (_selectQuery_masterCardRarityData != null) { _selectQuery_masterCardRarityData.Dispose(); _selectQuery_masterCardRarityData = null; }
            if (_indexedSelectQuery_cardRarityData_cardId != null) { _indexedSelectQuery_cardRarityData_cardId.Dispose(); _indexedSelectQuery_cardRarityData_cardId = null; }
            if (_indexedSelectQuery_cardRarityData_raceDressId != null) { _indexedSelectQuery_cardRarityData_raceDressId.Dispose(); _indexedSelectQuery_cardRarityData_raceDressId = null; }
            if (_indexedSelectQuery_cardRarityData_cardId_rarity != null) { _indexedSelectQuery_cardRarityData_cardId_rarity.Dispose(); _indexedSelectQuery_cardRarityData_cardId_rarity = null; }
            if (_selectQuery_masterCardTalentUpgrade != null) { _selectQuery_masterCardTalentUpgrade.Dispose(); _selectQuery_masterCardTalentUpgrade = null; }
            if (_indexedSelectQuery_cardTalentUpgrade_talentGroupId_talentLevel != null) { _indexedSelectQuery_cardTalentUpgrade_talentGroupId_talentLevel.Dispose(); _indexedSelectQuery_cardTalentUpgrade_talentGroupId_talentLevel = null; }
            if (_selectQuery_masterSupportCardData != null) { _selectQuery_masterSupportCardData.Dispose(); _selectQuery_masterSupportCardData = null; }
            if (_indexedSelectQuery_supportCardData_charaId != null) { _indexedSelectQuery_supportCardData_charaId.Dispose(); _indexedSelectQuery_supportCardData_charaId = null; }
            if (_selectQuery_masterSupportCardLimit != null) { _selectQuery_masterSupportCardLimit.Dispose(); _selectQuery_masterSupportCardLimit = null; }
            if (_selectQuery_masterSupportCardLevel != null) { _selectQuery_masterSupportCardLevel.Dispose(); _selectQuery_masterSupportCardLevel = null; }
            if (_indexedSelectQuery_supportCardLevel_rarity != null) { _indexedSelectQuery_supportCardLevel_rarity.Dispose(); _indexedSelectQuery_supportCardLevel_rarity = null; }
            if (_selectQuery_masterSupportCardEffectTable != null) { _selectQuery_masterSupportCardEffectTable.Dispose(); _selectQuery_masterSupportCardEffectTable = null; }
            if (_indexedSelectQuery_supportCardEffectTable_id != null) { _indexedSelectQuery_supportCardEffectTable_id.Dispose(); _indexedSelectQuery_supportCardEffectTable_id = null; }
            if (_selectQuery_masterSupportCardUniqueEffect != null) { _selectQuery_masterSupportCardUniqueEffect.Dispose(); _selectQuery_masterSupportCardUniqueEffect = null; }
            if (_selectQuery_masterSupportCardEffectFilter != null) { _selectQuery_masterSupportCardEffectFilter.Dispose(); _selectQuery_masterSupportCardEffectFilter = null; }
            if (_indexedSelectQuery_supportCardEffectFilter_groupId != null) { _indexedSelectQuery_supportCardEffectFilter_groupId.Dispose(); _indexedSelectQuery_supportCardEffectFilter_groupId = null; }
            if (_selectQuery_masterSupportCardEffectFilterGroup != null) { _selectQuery_masterSupportCardEffectFilterGroup.Dispose(); _selectQuery_masterSupportCardEffectFilterGroup = null; }
            if (_selectQuery_masterSupportCardTeamScoreBonus != null) { _selectQuery_masterSupportCardTeamScoreBonus.Dispose(); _selectQuery_masterSupportCardTeamScoreBonus = null; }
            if (_indexedSelectQuery_supportCardTeamScoreBonus_level != null) { _indexedSelectQuery_supportCardTeamScoreBonus_level.Dispose(); _indexedSelectQuery_supportCardTeamScoreBonus_level = null; }
            if (_selectQuery_masterCharaData != null) { _selectQuery_masterCharaData.Dispose(); _selectQuery_masterCharaData = null; }
            if (_selectQuery_masterCharaDataGroup != null) { _selectQuery_masterCharaDataGroup.Dispose(); _selectQuery_masterCharaDataGroup = null; }
            if (_indexedSelectQuery_charaDataGroup_groupId != null) { _indexedSelectQuery_charaDataGroup_groupId.Dispose(); _indexedSelectQuery_charaDataGroup_groupId = null; }
            if (_indexedSelectQuery_charaDataGroup_charaId != null) { _indexedSelectQuery_charaDataGroup_charaId.Dispose(); _indexedSelectQuery_charaDataGroup_charaId = null; }
            if (_selectQuery_masterCharaType != null) { _selectQuery_masterCharaType.Dispose(); _selectQuery_masterCharaType = null; }
            if (_indexedSelectQuery_charaType_targetScene_targetCut != null) { _indexedSelectQuery_charaType_targetScene_targetCut.Dispose(); _indexedSelectQuery_charaType_targetScene_targetCut = null; }
            if (_selectQuery_masterCharaCategoryMotion != null) { _selectQuery_masterCharaCategoryMotion.Dispose(); _selectQuery_masterCharaCategoryMotion = null; }
            if (_selectQuery_masterCharaMotionSet != null) { _selectQuery_masterCharaMotionSet.Dispose(); _selectQuery_masterCharaMotionSet = null; }
            if (_selectQuery_masterCharacterPropAnimation != null) { _selectQuery_masterCharacterPropAnimation.Dispose(); _selectQuery_masterCharacterPropAnimation = null; }
            if (_indexedSelectQuery_characterPropAnimation_propId_sceneType != null) { _indexedSelectQuery_characterPropAnimation_propId_sceneType.Dispose(); _indexedSelectQuery_characterPropAnimation_propId_sceneType = null; }
            if (_selectQuery_masterDressData != null) { _selectQuery_masterDressData.Dispose(); _selectQuery_masterDressData = null; }
            if (_indexedSelectQuery_dressData_charaId != null) { _indexedSelectQuery_dressData_charaId.Dispose(); _indexedSelectQuery_dressData_charaId = null; }
            if (_indexedSelectQuery_dressData_conditionType != null) { _indexedSelectQuery_dressData_conditionType.Dispose(); _indexedSelectQuery_dressData_conditionType = null; }
            if (_indexedSelectQuery_dressData_bodyType != null) { _indexedSelectQuery_dressData_bodyType.Dispose(); _indexedSelectQuery_dressData_bodyType = null; }
            if (_indexedSelectQuery_dressData_charaId_costumeType != null) { _indexedSelectQuery_dressData_charaId_costumeType.Dispose(); _indexedSelectQuery_dressData_charaId_costumeType = null; }
            if (_selectQuery_masterLoveRank != null) { _selectQuery_masterLoveRank.Dispose(); _selectQuery_masterLoveRank = null; }
            if (_indexedSelectQuery_loveRank_rank != null) { _indexedSelectQuery_loveRank_rank.Dispose(); _indexedSelectQuery_loveRank_rank = null; }
            if (_selectQuery_masterNeedPieceNumData != null) { _selectQuery_masterNeedPieceNumData.Dispose(); _selectQuery_masterNeedPieceNumData = null; }
            if (_indexedSelectQuery_needPieceNumData_rarity != null) { _indexedSelectQuery_needPieceNumData_rarity.Dispose(); _indexedSelectQuery_needPieceNumData_rarity = null; }
            if (_selectQuery_masterSkillData != null) { _selectQuery_masterSkillData.Dispose(); _selectQuery_masterSkillData = null; }
            if (_indexedSelectQuery_skillData_groupId != null) { _indexedSelectQuery_skillData_groupId.Dispose(); _indexedSelectQuery_skillData_groupId = null; }
            if (_selectQuery_masterSkillSet != null) { _selectQuery_masterSkillSet.Dispose(); _selectQuery_masterSkillSet = null; }
            if (_selectQuery_masterAvailableSkillSet != null) { _selectQuery_masterAvailableSkillSet.Dispose(); _selectQuery_masterAvailableSkillSet = null; }
            if (_indexedSelectQuery_availableSkillSet_availableSkillSetId != null) { _indexedSelectQuery_availableSkillSet_availableSkillSetId.Dispose(); _indexedSelectQuery_availableSkillSet_availableSkillSetId = null; }
            if (_selectQuery_masterSkillExp != null) { _selectQuery_masterSkillExp.Dispose(); _selectQuery_masterSkillExp = null; }
            if (_indexedSelectQuery_skillExp_type != null) { _indexedSelectQuery_skillExp_type.Dispose(); _indexedSelectQuery_skillExp_type = null; }
            if (_selectQuery_masterSkillLevelValue != null) { _selectQuery_masterSkillLevelValue.Dispose(); _selectQuery_masterSkillLevelValue = null; }
            if (_indexedSelectQuery_skillLevelValue_abilityType != null) { _indexedSelectQuery_skillLevelValue_abilityType.Dispose(); _indexedSelectQuery_skillLevelValue_abilityType = null; }
            if (_selectQuery_masterSkillUpgradeDescription != null) { _selectQuery_masterSkillUpgradeDescription.Dispose(); _selectQuery_masterSkillUpgradeDescription = null; }
            if (_indexedSelectQuery_skillUpgradeDescription_cardId != null) { _indexedSelectQuery_skillUpgradeDescription_cardId.Dispose(); _indexedSelectQuery_skillUpgradeDescription_cardId = null; }
            if (_indexedSelectQuery_skillUpgradeDescription_cardId_rank != null) { _indexedSelectQuery_skillUpgradeDescription_cardId_rank.Dispose(); _indexedSelectQuery_skillUpgradeDescription_cardId_rank = null; }
            if (_selectQuery_masterSkillUpgradeSpeciality != null) { _selectQuery_masterSkillUpgradeSpeciality.Dispose(); _selectQuery_masterSkillUpgradeSpeciality = null; }
            if (_indexedSelectQuery_skillUpgradeSpeciality_scenarioId != null) { _indexedSelectQuery_skillUpgradeSpeciality_scenarioId.Dispose(); _indexedSelectQuery_skillUpgradeSpeciality_scenarioId = null; }
            if (_indexedSelectQuery_skillUpgradeSpeciality_baseSkillId != null) { _indexedSelectQuery_skillUpgradeSpeciality_baseSkillId.Dispose(); _indexedSelectQuery_skillUpgradeSpeciality_baseSkillId = null; }
            if (_selectQuery_masterSkillUpgradeCondition != null) { _selectQuery_masterSkillUpgradeCondition.Dispose(); _selectQuery_masterSkillUpgradeCondition = null; }
            if (_indexedSelectQuery_skillUpgradeCondition_descriptionId != null) { _indexedSelectQuery_skillUpgradeCondition_descriptionId.Dispose(); _indexedSelectQuery_skillUpgradeCondition_descriptionId = null; }
            if (_selectQuery_masterSkillUpScenarioCondition != null) { _selectQuery_masterSkillUpScenarioCondition.Dispose(); _selectQuery_masterSkillUpScenarioCondition = null; }
            if (_indexedSelectQuery_skillUpScenarioCondition_scenarioId != null) { _indexedSelectQuery_skillUpScenarioCondition_scenarioId.Dispose(); _indexedSelectQuery_skillUpScenarioCondition_scenarioId = null; }
            if (_selectQuery_masterCharacterSystemText != null) { _selectQuery_masterCharacterSystemText.Dispose(); _selectQuery_masterCharacterSystemText = null; }
            if (_indexedSelectQuery_characterSystemText_characterId != null) { _indexedSelectQuery_characterSystemText_characterId.Dispose(); _indexedSelectQuery_characterSystemText_characterId = null; }
            if (_indexedSelectQuery_characterSystemText_voiceId != null) { _indexedSelectQuery_characterSystemText_voiceId.Dispose(); _indexedSelectQuery_characterSystemText_voiceId = null; }
            if (_selectQuery_masterCharacterSystemLottery != null) { _selectQuery_masterCharacterSystemLottery.Dispose(); _selectQuery_masterCharacterSystemLottery = null; }
            if (_indexedSelectQuery_characterSystemLottery_charaId != null) { _indexedSelectQuery_characterSystemLottery_charaId.Dispose(); _indexedSelectQuery_characterSystemLottery_charaId = null; }
            if (_indexedSelectQuery_characterSystemLottery_charaId_trigger != null) { _indexedSelectQuery_characterSystemLottery_charaId_trigger.Dispose(); _indexedSelectQuery_characterSystemLottery_charaId_trigger = null; }
            if (_indexedSelectQuery_characterSystemLottery_trigger_param1 != null) { _indexedSelectQuery_characterSystemLottery_trigger_param1.Dispose(); _indexedSelectQuery_characterSystemLottery_trigger_param1 = null; }
            if (_selectQuery_masterFaceTypeData != null) { _selectQuery_masterFaceTypeData.Dispose(); _selectQuery_masterFaceTypeData = null; }
            if (_selectQuery_masterFacialMouthChange != null) { _selectQuery_masterFacialMouthChange.Dispose(); _selectQuery_masterFacialMouthChange = null; }
            if (_indexedSelectQuery_facialMouthChange_charaId_beforeFacialname != null) { _indexedSelectQuery_facialMouthChange_charaId_beforeFacialname.Dispose(); _indexedSelectQuery_facialMouthChange_charaId_beforeFacialname = null; }
            if (_indexedSelectQuery_facialMouthChange_charaId != null) { _indexedSelectQuery_facialMouthChange_charaId.Dispose(); _indexedSelectQuery_facialMouthChange_charaId = null; }
            if (_selectQuery_masterCharaMotionAct != null) { _selectQuery_masterCharaMotionAct.Dispose(); _selectQuery_masterCharaMotionAct = null; }
            if (_indexedSelectQuery_charaMotionAct_charaId_commandName != null) { _indexedSelectQuery_charaMotionAct_charaId_commandName.Dispose(); _indexedSelectQuery_charaMotionAct_charaId_commandName = null; }
            if (_indexedSelectQuery_charaMotionAct_charaId != null) { _indexedSelectQuery_charaMotionAct_charaId.Dispose(); _indexedSelectQuery_charaMotionAct_charaId = null; }
            if (_selectQuery_masterRandomEarTailMotion != null) { _selectQuery_masterRandomEarTailMotion.Dispose(); _selectQuery_masterRandomEarTailMotion = null; }
            if (_indexedSelectQuery_randomEarTailMotion_partsType != null) { _indexedSelectQuery_randomEarTailMotion_partsType.Dispose(); _indexedSelectQuery_randomEarTailMotion_partsType = null; }
            if (_selectQuery_masterNickname != null) { _selectQuery_masterNickname.Dispose(); _selectQuery_masterNickname = null; }
            if (_indexedSelectQuery_nickname_userShow != null) { _indexedSelectQuery_nickname_userShow.Dispose(); _indexedSelectQuery_nickname_userShow = null; }
            if (_selectQuery_masterSupportCardLimitBreak != null) { _selectQuery_masterSupportCardLimitBreak.Dispose(); _selectQuery_masterSupportCardLimitBreak = null; }
            if (_selectQuery_masterCardTalentHintUpgrade != null) { _selectQuery_masterCardTalentHintUpgrade.Dispose(); _selectQuery_masterCardTalentHintUpgrade = null; }
            if (_indexedSelectQuery_cardTalentHintUpgrade_rarity_talentLevel != null) { _indexedSelectQuery_cardTalentHintUpgrade_rarity_talentLevel.Dispose(); _indexedSelectQuery_cardTalentHintUpgrade_rarity_talentLevel = null; }
            if (_selectQuery_masterSupportCardGroup != null) { _selectQuery_masterSupportCardGroup.Dispose(); _selectQuery_masterSupportCardGroup = null; }
            if (_indexedSelectQuery_supportCardGroup_supportCardId != null) { _indexedSelectQuery_supportCardGroup_supportCardId.Dispose(); _indexedSelectQuery_supportCardGroup_supportCardId = null; }
            if (_indexedSelectQuery_supportCardGroup_charaId != null) { _indexedSelectQuery_supportCardGroup_charaId.Dispose(); _indexedSelectQuery_supportCardGroup_charaId = null; }
            if (_selectQuery_masterCharaDressColorSetDefault != null) { _selectQuery_masterCharaDressColorSetDefault.Dispose(); _selectQuery_masterCharaDressColorSetDefault = null; }
            if (_indexedSelectQuery_charaDressColorSetDefault_charaId != null) { _indexedSelectQuery_charaDressColorSetDefault_charaId.Dispose(); _indexedSelectQuery_charaDressColorSetDefault_charaId = null; }
            if (_selectQuery_masterCharaDressColorSet != null) { _selectQuery_masterCharaDressColorSet.Dispose(); _selectQuery_masterCharaDressColorSet = null; }
            if (_indexedSelectQuery_charaDressColorSet_dressId != null) { _indexedSelectQuery_charaDressColorSet_dressId.Dispose(); _indexedSelectQuery_charaDressColorSet_dressId = null; }
            if (this.masterCardData != null) { this.masterCardData.Unload(); }
            if (this.masterCardRarityData != null) { this.masterCardRarityData.Unload(); }
            if (this.masterCardTalentUpgrade != null) { this.masterCardTalentUpgrade.Unload(); }
            if (this.masterSupportCardData != null) { this.masterSupportCardData.Unload(); }
            if (this.masterSupportCardLimit != null) { this.masterSupportCardLimit.Unload(); }
            if (this.masterSupportCardLevel != null) { this.masterSupportCardLevel.Unload(); }
            if (this.masterSupportCardEffectTable != null) { this.masterSupportCardEffectTable.Unload(); }
            if (this.masterSupportCardUniqueEffect != null) { this.masterSupportCardUniqueEffect.Unload(); }
            if (this.masterSupportCardEffectFilter != null) { this.masterSupportCardEffectFilter.Unload(); }
            if (this.masterSupportCardEffectFilterGroup != null) { this.masterSupportCardEffectFilterGroup.Unload(); }
            if (this.masterSupportCardTeamScoreBonus != null) { this.masterSupportCardTeamScoreBonus.Unload(); }
            if (this.masterCharaData != null) { this.masterCharaData.Unload(); }
            if (this.masterCharaDataGroup != null) { this.masterCharaDataGroup.Unload(); }
            if (this.masterCharaType != null) { this.masterCharaType.Unload(); }
            if (this.masterCharaCategoryMotion != null) { this.masterCharaCategoryMotion.Unload(); }
            if (this.masterCharaMotionSet != null) { this.masterCharaMotionSet.Unload(); }
            if (this.masterCharacterPropAnimation != null) { this.masterCharacterPropAnimation.Unload(); }
            if (this.masterDressData != null) { this.masterDressData.Unload(); }
            if (this.masterLoveRank != null) { this.masterLoveRank.Unload(); }
            if (this.masterNeedPieceNumData != null) { this.masterNeedPieceNumData.Unload(); }
            if (this.masterSkillData != null) { this.masterSkillData.Unload(); }
            if (this.masterSkillSet != null) { this.masterSkillSet.Unload(); }
            if (this.masterAvailableSkillSet != null) { this.masterAvailableSkillSet.Unload(); }
            if (this.masterSkillExp != null) { this.masterSkillExp.Unload(); }
            if (this.masterSkillLevelValue != null) { this.masterSkillLevelValue.Unload(); }
            if (this.masterSkillUpgradeDescription != null) { this.masterSkillUpgradeDescription.Unload(); }
            if (this.masterSkillUpgradeSpeciality != null) { this.masterSkillUpgradeSpeciality.Unload(); }
            if (this.masterSkillUpgradeCondition != null) { this.masterSkillUpgradeCondition.Unload(); }
            if (this.masterSkillUpScenarioCondition != null) { this.masterSkillUpScenarioCondition.Unload(); }
            if (this.masterCharacterSystemText != null) { this.masterCharacterSystemText.Unload(); }
            if (this.masterCharacterSystemLottery != null) { this.masterCharacterSystemLottery.Unload(); }
            if (this.masterFaceTypeData != null) { this.masterFaceTypeData.Unload(); }
            if (this.masterFacialMouthChange != null) { this.masterFacialMouthChange.Unload(); }
            if (this.masterCharaMotionAct != null) { this.masterCharaMotionAct.Unload(); }
            if (this.masterRandomEarTailMotion != null) { this.masterRandomEarTailMotion.Unload(); }
            if (this.masterNickname != null) { this.masterNickname.Unload(); }
            if (this.masterSupportCardLimitBreak != null) { this.masterSupportCardLimitBreak.Unload(); }
            if (this.masterCardTalentHintUpgrade != null) { this.masterCardTalentHintUpgrade.Unload(); }
            if (this.masterSupportCardGroup != null) { this.masterSupportCardGroup.Unload(); }
            if (this.masterCharaDressColorSetDefault != null) { this.masterCharaDressColorSetDefault.Unload(); }
            if (this.masterCharaDressColorSet != null) { this.masterCharaDressColorSet.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for card/card_data
        
        /// <summary>
        /// SELECT `chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CardData()
        {
            if (_selectQuery_masterCardData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCardData = connection.PreparedQuery("SELECT `chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCardData;
        }
        
        /// <summary>
        /// SELECT `id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data` WHERE `chara_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CardData_CharaId()
        {
            if (_indexedSelectQuery_cardData_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_cardData_charaId = connection.PreparedQuery("SELECT `id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data` WHERE `chara_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_cardData_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`running_style` FROM `card_data` WHERE `get_piece_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CardData_GetPieceId()
        {
            if (_indexedSelectQuery_cardData_getPieceId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_cardData_getPieceId = connection.PreparedQuery("SELECT `id`,`chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`running_style` FROM `card_data` WHERE `get_piece_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_cardData_getPieceId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CardData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`default_rarity`,`limited_chara`,`available_skill_set_id`,`talent_speed`,`talent_stamina`,`talent_pow`,`talent_guts`,`talent_wiz`,`talent_group_id`,`bg_id`,`get_piece_id`,`running_style` FROM `card_data`;");
        }
        
        // SQL statements for card/card_rarity_data
        
        /// <summary>
        /// SELECT `card_id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CardRarityData()
        {
            if (_selectQuery_masterCardRarityData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCardRarityData = connection.PreparedQuery("SELECT `card_id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCardRarityData;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `card_id`=? ORDER BY `rarity` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CardRarityData_CardId()
        {
            if (_indexedSelectQuery_cardRarityData_cardId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_cardRarityData_cardId = connection.PreparedQuery("SELECT `id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `card_id`=? ORDER BY `rarity` ASC;");
            }
        
            return _indexedSelectQuery_cardRarityData_cardId;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`rarity`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `race_dress_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CardRarityData_RaceDressId()
        {
            if (_indexedSelectQuery_cardRarityData_raceDressId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_cardRarityData_raceDressId = connection.PreparedQuery("SELECT `id`,`card_id`,`rarity`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `race_dress_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_cardRarityData_raceDressId;
        }
        
        /// <summary>
        /// SELECT `id`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `card_id`=? AND `rarity`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CardRarityData_CardId_Rarity()
        {
            if (_indexedSelectQuery_cardRarityData_cardId_rarity == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_cardRarityData_cardId_rarity = connection.PreparedQuery("SELECT `id`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data` WHERE `card_id`=? AND `rarity`=?;");
            }
        
            return _indexedSelectQuery_cardRarityData_cardId_rarity;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CardRarityData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`card_id`,`rarity`,`race_dress_id`,`skill_set`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`max_speed`,`max_stamina`,`max_pow`,`max_guts`,`max_wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`get_dress_id_1`,`get_dress_id_2` FROM `card_rarity_data`;");
        }
        
        // SQL statements for card/card_talent_upgrade
        
        /// <summary>
        /// SELECT `talent_group_id`,`talent_level`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CardTalentUpgrade()
        {
            if (_selectQuery_masterCardTalentUpgrade == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCardTalentUpgrade = connection.PreparedQuery("SELECT `talent_group_id`,`talent_level`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCardTalentUpgrade;
        }
        
        /// <summary>
        /// SELECT `id`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade` WHERE `talent_group_id`=? AND `talent_level`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CardTalentUpgrade_TalentGroupId_TalentLevel()
        {
            if (_indexedSelectQuery_cardTalentUpgrade_talentGroupId_talentLevel == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_cardTalentUpgrade_talentGroupId_talentLevel = connection.PreparedQuery("SELECT `id`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade` WHERE `talent_group_id`=? AND `talent_level`=?;");
            }
        
            return _indexedSelectQuery_cardTalentUpgrade_talentGroupId_talentLevel;
        }
        
        /// <summary>
        /// SELECT `id`,`talent_group_id`,`talent_level`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CardTalentUpgrade()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`talent_group_id`,`talent_level`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5`,`item_category_6`,`item_id_6`,`item_num_6` FROM `card_talent_upgrade`;");
        }
        
        // SQL statements for card/support_card_data
        
        /// <summary>
        /// SELECT `chara_id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardData()
        {
            if (_selectQuery_masterSupportCardData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardData = connection.PreparedQuery("SELECT `chara_id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSupportCardData;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data` WHERE `chara_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SupportCardData_CharaId()
        {
            if (_indexedSelectQuery_supportCardData_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_supportCardData_charaId = connection.PreparedQuery("SELECT `id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data` WHERE `chara_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_supportCardData_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`rarity`,`exchange_item_id`,`effect_table_id`,`unique_effect_id`,`command_type`,`command_id`,`support_card_type`,`skill_set_id`,`detail_pos_x`,`detail_pos_y`,`detail_scale`,`detail_rot_z`,`start_date`,`outing_max`,`effect_id` FROM `support_card_data`;");
        }
        
        // SQL statements for card/support_card_limit
        
        /// <summary>
        /// SELECT `limit_0`,`limit_1`,`limit_2`,`limit_3`,`limit_4` FROM `support_card_limit` WHERE `rarity`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardLimit()
        {
            if (_selectQuery_masterSupportCardLimit == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardLimit = connection.PreparedQuery("SELECT `limit_0`,`limit_1`,`limit_2`,`limit_3`,`limit_4` FROM `support_card_limit` WHERE `rarity`=?;");
            }
        
            return _selectQuery_masterSupportCardLimit;
        }
        
        /// <summary>
        /// SELECT `rarity`,`limit_0`,`limit_1`,`limit_2`,`limit_3`,`limit_4` FROM `support_card_limit`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardLimit()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `rarity`,`limit_0`,`limit_1`,`limit_2`,`limit_3`,`limit_4` FROM `support_card_limit`;");
        }
        
        // SQL statements for card/support_card_level
        
        /// <summary>
        /// SELECT `rarity`,`level`,`total_exp` FROM `support_card_level` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardLevel()
        {
            if (_selectQuery_masterSupportCardLevel == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardLevel = connection.PreparedQuery("SELECT `rarity`,`level`,`total_exp` FROM `support_card_level` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSupportCardLevel;
        }
        
        /// <summary>
        /// SELECT `id`,`level`,`total_exp` FROM `support_card_level` WHERE `rarity`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SupportCardLevel_Rarity()
        {
            if (_indexedSelectQuery_supportCardLevel_rarity == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_supportCardLevel_rarity = connection.PreparedQuery("SELECT `id`,`level`,`total_exp` FROM `support_card_level` WHERE `rarity`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_supportCardLevel_rarity;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`level`,`total_exp` FROM `support_card_level`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardLevel()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`rarity`,`level`,`total_exp` FROM `support_card_level`;");
        }
        
        // SQL statements for card/support_card_effect_table
        
        /// <summary>
        /// SELECT `init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table` WHERE `id`=? AND `type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardEffectTable()
        {
            if (_selectQuery_masterSupportCardEffectTable == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardEffectTable = connection.PreparedQuery("SELECT `init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table` WHERE `id`=? AND `type`=?;");
            }
        
            return _selectQuery_masterSupportCardEffectTable;
        }
        
        /// <summary>
        /// SELECT `type`,`init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table` WHERE `id`=? ORDER BY `type` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SupportCardEffectTable_Id()
        {
            if (_indexedSelectQuery_supportCardEffectTable_id == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_supportCardEffectTable_id = connection.PreparedQuery("SELECT `type`,`init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table` WHERE `id`=? ORDER BY `type` ASC;");
            }
        
            return _indexedSelectQuery_supportCardEffectTable_id;
        }
        
        /// <summary>
        /// SELECT `id`,`type`,`init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardEffectTable()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`type`,`init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table`;");
        }
        
        // SQL statements for card/support_card_unique_effect
        
        /// <summary>
        /// SELECT `lv` FROM `support_card_unique_effect` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardUniqueEffect()
        {
            if (_selectQuery_masterSupportCardUniqueEffect == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardUniqueEffect = connection.PreparedQuery("SELECT `lv` FROM `support_card_unique_effect` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSupportCardUniqueEffect;
        }
        
        /// <summary>
        /// SELECT `id`,`lv` FROM `support_card_unique_effect`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardUniqueEffect()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`lv` FROM `support_card_unique_effect`;");
        }
        
        // SQL statements for card/support_card_effect_filter
        
        /// <summary>
        /// SELECT `group_id`,`sort_id`,`start_date` FROM `support_card_effect_filter` WHERE `type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardEffectFilter()
        {
            if (_selectQuery_masterSupportCardEffectFilter == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardEffectFilter = connection.PreparedQuery("SELECT `group_id`,`sort_id`,`start_date` FROM `support_card_effect_filter` WHERE `type`=?;");
            }
        
            return _selectQuery_masterSupportCardEffectFilter;
        }
        
        /// <summary>
        /// SELECT `type`,`sort_id`,`start_date` FROM `support_card_effect_filter` WHERE `group_id`=? ORDER BY `sort_id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SupportCardEffectFilter_GroupId()
        {
            if (_indexedSelectQuery_supportCardEffectFilter_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_supportCardEffectFilter_groupId = connection.PreparedQuery("SELECT `type`,`sort_id`,`start_date` FROM `support_card_effect_filter` WHERE `group_id`=? ORDER BY `sort_id` ASC;");
            }
        
            return _indexedSelectQuery_supportCardEffectFilter_groupId;
        }
        
        /// <summary>
        /// SELECT `type`,`group_id`,`sort_id`,`start_date` FROM `support_card_effect_filter`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardEffectFilter()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `type`,`group_id`,`sort_id`,`start_date` FROM `support_card_effect_filter`;");
        }
        
        // SQL statements for card/support_card_effect_filter_group
        
        /// <summary>
        /// SELECT `sort_id`,`start_date` FROM `support_card_effect_filter_group` WHERE `group_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardEffectFilterGroup()
        {
            if (_selectQuery_masterSupportCardEffectFilterGroup == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardEffectFilterGroup = connection.PreparedQuery("SELECT `sort_id`,`start_date` FROM `support_card_effect_filter_group` WHERE `group_id`=?;");
            }
        
            return _selectQuery_masterSupportCardEffectFilterGroup;
        }
        
        /// <summary>
        /// SELECT `group_id`,`sort_id`,`start_date` FROM `support_card_effect_filter_group`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardEffectFilterGroup()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `group_id`,`sort_id`,`start_date` FROM `support_card_effect_filter_group`;");
        }
        
        // SQL statements for card/support_card_team_score_bonus
        
        /// <summary>
        /// SELECT `level`,`score_rate` FROM `support_card_team_score_bonus` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardTeamScoreBonus()
        {
            if (_selectQuery_masterSupportCardTeamScoreBonus == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardTeamScoreBonus = connection.PreparedQuery("SELECT `level`,`score_rate` FROM `support_card_team_score_bonus` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSupportCardTeamScoreBonus;
        }
        
        /// <summary>
        /// SELECT `id`,`score_rate` FROM `support_card_team_score_bonus` WHERE `level`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SupportCardTeamScoreBonus_Level()
        {
            if (_indexedSelectQuery_supportCardTeamScoreBonus_level == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_supportCardTeamScoreBonus_level = connection.PreparedQuery("SELECT `id`,`score_rate` FROM `support_card_team_score_bonus` WHERE `level`=?;");
            }
        
            return _indexedSelectQuery_supportCardTeamScoreBonus_level;
        }
        
        /// <summary>
        /// SELECT `id`,`level`,`score_rate` FROM `support_card_team_score_bonus`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardTeamScoreBonus()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`level`,`score_rate` FROM `support_card_team_score_bonus`;");
        }
        
        // SQL statements for card/chara_data
        
        /// <summary>
        /// SELECT `birth_year`,`birth_month`,`birth_day`,`last_year`,`sex`,`image_color_main`,`image_color_sub`,`ui_color_main`,`ui_color_sub`,`ui_training_color_1`,`ui_training_color_2`,`ui_border_color`,`ui_num_color_1`,`ui_num_color_2`,`ui_turn_color`,`ui_wipe_color_1`,`ui_wipe_color_2`,`ui_wipe_color_3`,`ui_speech_color_1`,`ui_speech_color_2`,`ui_nameplate_color_1`,`ui_nameplate_color_2`,`height`,`bust`,`scale`,`skin`,`shape`,`socks`,`personal_dress`,`tail_model_id`,`race_running_type`,`ear_random_time_min`,`ear_random_time_max`,`tail_random_time_min`,`tail_random_time_max`,`story_ear_random_time_min`,`story_ear_random_time_max`,`story_tail_random_time_min`,`story_tail_random_time_max`,`attachment_model_id`,`mini_mayu_shader_type`,`start_date`,`chara_category`,`love_rank_limit` FROM `chara_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaData()
        {
            if (_selectQuery_masterCharaData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaData = connection.PreparedQuery("SELECT `birth_year`,`birth_month`,`birth_day`,`last_year`,`sex`,`image_color_main`,`image_color_sub`,`ui_color_main`,`ui_color_sub`,`ui_training_color_1`,`ui_training_color_2`,`ui_border_color`,`ui_num_color_1`,`ui_num_color_2`,`ui_turn_color`,`ui_wipe_color_1`,`ui_wipe_color_2`,`ui_wipe_color_3`,`ui_speech_color_1`,`ui_speech_color_2`,`ui_nameplate_color_1`,`ui_nameplate_color_2`,`height`,`bust`,`scale`,`skin`,`shape`,`socks`,`personal_dress`,`tail_model_id`,`race_running_type`,`ear_random_time_min`,`ear_random_time_max`,`tail_random_time_min`,`tail_random_time_max`,`story_ear_random_time_min`,`story_ear_random_time_max`,`story_tail_random_time_min`,`story_tail_random_time_max`,`attachment_model_id`,`mini_mayu_shader_type`,`start_date`,`chara_category`,`love_rank_limit` FROM `chara_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharaData;
        }
        
        /// <summary>
        /// SELECT `id`,`birth_year`,`birth_month`,`birth_day`,`last_year`,`sex`,`image_color_main`,`image_color_sub`,`ui_color_main`,`ui_color_sub`,`ui_training_color_1`,`ui_training_color_2`,`ui_border_color`,`ui_num_color_1`,`ui_num_color_2`,`ui_turn_color`,`ui_wipe_color_1`,`ui_wipe_color_2`,`ui_wipe_color_3`,`ui_speech_color_1`,`ui_speech_color_2`,`ui_nameplate_color_1`,`ui_nameplate_color_2`,`height`,`bust`,`scale`,`skin`,`shape`,`socks`,`personal_dress`,`tail_model_id`,`race_running_type`,`ear_random_time_min`,`ear_random_time_max`,`tail_random_time_min`,`tail_random_time_max`,`story_ear_random_time_min`,`story_ear_random_time_max`,`story_tail_random_time_min`,`story_tail_random_time_max`,`attachment_model_id`,`mini_mayu_shader_type`,`start_date`,`chara_category`,`love_rank_limit` FROM `chara_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`birth_year`,`birth_month`,`birth_day`,`last_year`,`sex`,`image_color_main`,`image_color_sub`,`ui_color_main`,`ui_color_sub`,`ui_training_color_1`,`ui_training_color_2`,`ui_border_color`,`ui_num_color_1`,`ui_num_color_2`,`ui_turn_color`,`ui_wipe_color_1`,`ui_wipe_color_2`,`ui_wipe_color_3`,`ui_speech_color_1`,`ui_speech_color_2`,`ui_nameplate_color_1`,`ui_nameplate_color_2`,`height`,`bust`,`scale`,`skin`,`shape`,`socks`,`personal_dress`,`tail_model_id`,`race_running_type`,`ear_random_time_min`,`ear_random_time_max`,`tail_random_time_min`,`tail_random_time_max`,`story_ear_random_time_min`,`story_ear_random_time_max`,`story_tail_random_time_min`,`story_tail_random_time_max`,`attachment_model_id`,`mini_mayu_shader_type`,`start_date`,`chara_category`,`love_rank_limit` FROM `chara_data`;");
        }
        
        // SQL statements for card/chara_data_group
        
        /// <summary>
        /// SELECT `group_id`,`chara_id` FROM `chara_data_group` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaDataGroup()
        {
            if (_selectQuery_masterCharaDataGroup == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaDataGroup = connection.PreparedQuery("SELECT `group_id`,`chara_id` FROM `chara_data_group` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharaDataGroup;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id` FROM `chara_data_group` WHERE `group_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharaDataGroup_GroupId()
        {
            if (_indexedSelectQuery_charaDataGroup_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_charaDataGroup_groupId = connection.PreparedQuery("SELECT `id`,`chara_id` FROM `chara_data_group` WHERE `group_id`=?;");
            }
        
            return _indexedSelectQuery_charaDataGroup_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id` FROM `chara_data_group` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharaDataGroup_CharaId()
        {
            if (_indexedSelectQuery_charaDataGroup_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_charaDataGroup_charaId = connection.PreparedQuery("SELECT `id`,`group_id` FROM `chara_data_group` WHERE `chara_id`=?;");
            }
        
            return _indexedSelectQuery_charaDataGroup_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`chara_id` FROM `chara_data_group`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaDataGroup()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group_id`,`chara_id` FROM `chara_data_group`;");
        }
        
        // SQL statements for card/chara_type
        
        /// <summary>
        /// SELECT `chara_id`,`target_scene`,`target_cut`,`target_type`,`value` FROM `chara_type` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaType()
        {
            if (_selectQuery_masterCharaType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaType = connection.PreparedQuery("SELECT `chara_id`,`target_scene`,`target_cut`,`target_type`,`value` FROM `chara_type` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharaType;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`target_type`,`value` FROM `chara_type` WHERE `target_scene`=? AND `target_cut`=? ORDER BY `target_scene` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharaType_TargetScene_TargetCut()
        {
            if (_indexedSelectQuery_charaType_targetScene_targetCut == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_charaType_targetScene_targetCut = connection.PreparedQuery("SELECT `id`,`chara_id`,`target_type`,`value` FROM `chara_type` WHERE `target_scene`=? AND `target_cut`=? ORDER BY `target_scene` ASC;");
            }
        
            return _indexedSelectQuery_charaType_targetScene_targetCut;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`target_scene`,`target_cut`,`target_type`,`value` FROM `chara_type`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaType()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`target_scene`,`target_cut`,`target_type`,`value` FROM `chara_type`;");
        }
        
        // SQL statements for card/chara_category_motion
        
        /// <summary>
        /// SELECT `standby_motion_1`,`standby_motion_2`,`standby_motion_3`,`standby_motion_4`,`standby_motion_5`,`standby_motion_6` FROM `chara_category_motion` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaCategoryMotion()
        {
            if (_selectQuery_masterCharaCategoryMotion == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaCategoryMotion = connection.PreparedQuery("SELECT `standby_motion_1`,`standby_motion_2`,`standby_motion_3`,`standby_motion_4`,`standby_motion_5`,`standby_motion_6` FROM `chara_category_motion` WHERE `chara_id`=?;");
            }
        
            return _selectQuery_masterCharaCategoryMotion;
        }
        
        /// <summary>
        /// SELECT `chara_id`,`standby_motion_1`,`standby_motion_2`,`standby_motion_3`,`standby_motion_4`,`standby_motion_5`,`standby_motion_6` FROM `chara_category_motion`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaCategoryMotion()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `chara_id`,`standby_motion_1`,`standby_motion_2`,`standby_motion_3`,`standby_motion_4`,`standby_motion_5`,`standby_motion_6` FROM `chara_category_motion`;");
        }
        
        // SQL statements for card/chara_motion_set
        
        /// <summary>
        /// SELECT `body_motion`,`body_motion_type`,`body_motion_play_type`,`face_type`,`cheek`,`eye_default`,`ear_motion`,`tail_motion`,`tail_motion_type` FROM `chara_motion_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaMotionSet()
        {
            if (_selectQuery_masterCharaMotionSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaMotionSet = connection.PreparedQuery("SELECT `body_motion`,`body_motion_type`,`body_motion_play_type`,`face_type`,`cheek`,`eye_default`,`ear_motion`,`tail_motion`,`tail_motion_type` FROM `chara_motion_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharaMotionSet;
        }
        
        /// <summary>
        /// SELECT `id`,`body_motion`,`body_motion_type`,`body_motion_play_type`,`face_type`,`cheek`,`eye_default`,`ear_motion`,`tail_motion`,`tail_motion_type` FROM `chara_motion_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaMotionSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`body_motion`,`body_motion_type`,`body_motion_play_type`,`face_type`,`cheek`,`eye_default`,`ear_motion`,`tail_motion`,`tail_motion_type` FROM `chara_motion_set`;");
        }
        
        // SQL statements for card/character_prop_animation
        
        /// <summary>
        /// SELECT `prop_id`,`prop_anim_id`,`layer_index`,`use_state_name`,`scene_type` FROM `character_prop_animation` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharacterPropAnimation()
        {
            if (_selectQuery_masterCharacterPropAnimation == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharacterPropAnimation = connection.PreparedQuery("SELECT `prop_id`,`prop_anim_id`,`layer_index`,`use_state_name`,`scene_type` FROM `character_prop_animation` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharacterPropAnimation;
        }
        
        /// <summary>
        /// SELECT `id`,`prop_anim_id`,`layer_index`,`use_state_name` FROM `character_prop_animation` WHERE `prop_id`=? AND `scene_type`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharacterPropAnimation_PropId_SceneType()
        {
            if (_indexedSelectQuery_characterPropAnimation_propId_sceneType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_characterPropAnimation_propId_sceneType = connection.PreparedQuery("SELECT `id`,`prop_anim_id`,`layer_index`,`use_state_name` FROM `character_prop_animation` WHERE `prop_id`=? AND `scene_type`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_characterPropAnimation_propId_sceneType;
        }
        
        /// <summary>
        /// SELECT `id`,`prop_id`,`prop_anim_id`,`layer_index`,`use_state_name`,`scene_type` FROM `character_prop_animation`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharacterPropAnimation()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`prop_id`,`prop_anim_id`,`layer_index`,`use_state_name`,`scene_type` FROM `character_prop_animation`;");
        }
        
        // SQL statements for card/dress_data
        
        /// <summary>
        /// SELECT `condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_DressData()
        {
            if (_selectQuery_masterDressData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterDressData = connection.PreparedQuery("SELECT `condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterDressData;
        }
        
        /// <summary>
        /// SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `chara_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_DressData_CharaId()
        {
            if (_indexedSelectQuery_dressData_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_dressData_charaId = connection.PreparedQuery("SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `chara_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_dressData_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `condition_type`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_DressData_ConditionType()
        {
            if (_indexedSelectQuery_dressData_conditionType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_dressData_conditionType = connection.PreparedQuery("SELECT `id`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `condition_type`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_dressData_conditionType;
        }
        
        /// <summary>
        /// SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `body_type`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_DressData_BodyType()
        {
            if (_indexedSelectQuery_dressData_bodyType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_dressData_bodyType = connection.PreparedQuery("SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `body_type`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_dressData_bodyType;
        }
        
        /// <summary>
        /// SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `chara_id`=? AND `costume_type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_DressData_CharaId_CostumeType()
        {
            if (_indexedSelectQuery_dressData_charaId_costumeType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_dressData_charaId_costumeType = connection.PreparedQuery("SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data` WHERE `chara_id`=? AND `costume_type`=?;");
            }
        
            return _indexedSelectQuery_dressData_charaId_costumeType;
        }
        
        /// <summary>
        /// SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_DressData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`condition_type`,`have_mini`,`general_purpose`,`costume_type`,`chara_id`,`use_gender`,`body_shape`,`body_type`,`body_type_sub`,`body_setting`,`use_race`,`use_live`,`use_live_theater`,`use_home`,`use_dress_change`,`is_wet`,`is_dirt`,`head_sub_id`,`use_season`,`dress_color_main`,`dress_color_sub`,`color_num`,`disp_order`,`tail_model_id`,`tail_model_sub_id`,`mini_mayu_shader_type`,`start_time`,`end_time` FROM `dress_data`;");
        }
        
        // SQL statements for card/love_rank
        
        /// <summary>
        /// SELECT `rank`,`total_point` FROM `love_rank` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LoveRank()
        {
            if (_selectQuery_masterLoveRank == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLoveRank = connection.PreparedQuery("SELECT `rank`,`total_point` FROM `love_rank` WHERE `id`=?;");
            }
        
            return _selectQuery_masterLoveRank;
        }
        
        /// <summary>
        /// SELECT `id`,`total_point` FROM `love_rank` WHERE `rank`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_LoveRank_Rank()
        {
            if (_indexedSelectQuery_loveRank_rank == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_loveRank_rank = connection.PreparedQuery("SELECT `id`,`total_point` FROM `love_rank` WHERE `rank`=?;");
            }
        
            return _indexedSelectQuery_loveRank_rank;
        }
        
        /// <summary>
        /// SELECT `id`,`rank`,`total_point` FROM `love_rank`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LoveRank()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`rank`,`total_point` FROM `love_rank`;");
        }
        
        // SQL statements for card/need_piece_num_data
        
        /// <summary>
        /// SELECT `rarity`,`piece_num` FROM `need_piece_num_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_NeedPieceNumData()
        {
            if (_selectQuery_masterNeedPieceNumData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterNeedPieceNumData = connection.PreparedQuery("SELECT `rarity`,`piece_num` FROM `need_piece_num_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterNeedPieceNumData;
        }
        
        /// <summary>
        /// SELECT `id`,`piece_num` FROM `need_piece_num_data` WHERE `rarity`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_NeedPieceNumData_Rarity()
        {
            if (_indexedSelectQuery_needPieceNumData_rarity == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_needPieceNumData_rarity = connection.PreparedQuery("SELECT `id`,`piece_num` FROM `need_piece_num_data` WHERE `rarity`=?;");
            }
        
            return _indexedSelectQuery_needPieceNumData_rarity;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`piece_num` FROM `need_piece_num_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_NeedPieceNumData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`rarity`,`piece_num` FROM `need_piece_num_data`;");
        }
        
        // SQL statements for card/skill_data
        
        /// <summary>
        /// SELECT `rarity`,`group_id`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillData()
        {
            if (_selectQuery_masterSkillData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillData = connection.PreparedQuery("SELECT `rarity`,`group_id`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillData;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data` WHERE `group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillData_GroupId()
        {
            if (_indexedSelectQuery_skillData_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillData_groupId = connection.PreparedQuery("SELECT `id`,`rarity`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data` WHERE `group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_skillData_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`group_id`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`rarity`,`group_id`,`group_rate`,`filter_switch`,`grade_value`,`skill_category`,`tag_id`,`unique_skill_id_1`,`unique_skill_id_2`,`exp_type`,`potential_per_default`,`activate_lot`,`priority`,`precondition_1`,`condition_1`,`float_ability_time_1`,`ability_time_usage_1`,`float_cooldown_time_1`,`ability_type_1_1`,`ability_value_usage_1_1`,`additional_activate_type_1_1`,`ability_value_level_usage_1_1`,`float_ability_value_1_1`,`target_type_1_1`,`target_value_1_1`,`ability_type_1_2`,`ability_value_usage_1_2`,`additional_activate_type_1_2`,`ability_value_level_usage_1_2`,`float_ability_value_1_2`,`target_type_1_2`,`target_value_1_2`,`ability_type_1_3`,`ability_value_usage_1_3`,`additional_activate_type_1_3`,`ability_value_level_usage_1_3`,`float_ability_value_1_3`,`target_type_1_3`,`target_value_1_3`,`precondition_2`,`condition_2`,`float_ability_time_2`,`ability_time_usage_2`,`float_cooldown_time_2`,`ability_type_2_1`,`ability_value_usage_2_1`,`additional_activate_type_2_1`,`ability_value_level_usage_2_1`,`float_ability_value_2_1`,`target_type_2_1`,`target_value_2_1`,`ability_type_2_2`,`ability_value_usage_2_2`,`additional_activate_type_2_2`,`ability_value_level_usage_2_2`,`float_ability_value_2_2`,`target_type_2_2`,`target_value_2_2`,`ability_type_2_3`,`ability_value_usage_2_3`,`additional_activate_type_2_3`,`ability_value_level_usage_2_3`,`float_ability_value_2_3`,`target_type_2_3`,`target_value_2_3`,`popularity_add_param_1`,`popularity_add_value_1`,`popularity_add_param_2`,`popularity_add_value_2`,`disp_order`,`icon_id`,`plate_type`,`disable_singlemode`,`disable_count_condition`,`is_general_skill`,`start_date`,`end_date` FROM `skill_data`;");
        }
        
        // SQL statements for card/skill_set
        
        /// <summary>
        /// SELECT `skill_id1`,`skill_level1`,`skill_id2`,`skill_level2`,`skill_id3`,`skill_level3`,`skill_id4`,`skill_level4`,`skill_id5`,`skill_level5`,`skill_id6`,`skill_level6`,`skill_id7`,`skill_level7`,`skill_id8`,`skill_level8`,`skill_id9`,`skill_level9`,`skill_id10`,`skill_level10`,`skill_id11`,`skill_level11`,`skill_id12`,`skill_level12`,`skill_id13`,`skill_level13`,`skill_id14`,`skill_level14`,`skill_id15`,`skill_level15`,`skill_id16`,`skill_level16`,`skill_id17`,`skill_level17`,`skill_id18`,`skill_level18`,`skill_id19`,`skill_level19`,`skill_id20`,`skill_level20` FROM `skill_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillSet()
        {
            if (_selectQuery_masterSkillSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillSet = connection.PreparedQuery("SELECT `skill_id1`,`skill_level1`,`skill_id2`,`skill_level2`,`skill_id3`,`skill_level3`,`skill_id4`,`skill_level4`,`skill_id5`,`skill_level5`,`skill_id6`,`skill_level6`,`skill_id7`,`skill_level7`,`skill_id8`,`skill_level8`,`skill_id9`,`skill_level9`,`skill_id10`,`skill_level10`,`skill_id11`,`skill_level11`,`skill_id12`,`skill_level12`,`skill_id13`,`skill_level13`,`skill_id14`,`skill_level14`,`skill_id15`,`skill_level15`,`skill_id16`,`skill_level16`,`skill_id17`,`skill_level17`,`skill_id18`,`skill_level18`,`skill_id19`,`skill_level19`,`skill_id20`,`skill_level20` FROM `skill_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillSet;
        }
        
        /// <summary>
        /// SELECT `id`,`skill_id1`,`skill_level1`,`skill_id2`,`skill_level2`,`skill_id3`,`skill_level3`,`skill_id4`,`skill_level4`,`skill_id5`,`skill_level5`,`skill_id6`,`skill_level6`,`skill_id7`,`skill_level7`,`skill_id8`,`skill_level8`,`skill_id9`,`skill_level9`,`skill_id10`,`skill_level10`,`skill_id11`,`skill_level11`,`skill_id12`,`skill_level12`,`skill_id13`,`skill_level13`,`skill_id14`,`skill_level14`,`skill_id15`,`skill_level15`,`skill_id16`,`skill_level16`,`skill_id17`,`skill_level17`,`skill_id18`,`skill_level18`,`skill_id19`,`skill_level19`,`skill_id20`,`skill_level20` FROM `skill_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`skill_id1`,`skill_level1`,`skill_id2`,`skill_level2`,`skill_id3`,`skill_level3`,`skill_id4`,`skill_level4`,`skill_id5`,`skill_level5`,`skill_id6`,`skill_level6`,`skill_id7`,`skill_level7`,`skill_id8`,`skill_level8`,`skill_id9`,`skill_level9`,`skill_id10`,`skill_level10`,`skill_id11`,`skill_level11`,`skill_id12`,`skill_level12`,`skill_id13`,`skill_level13`,`skill_id14`,`skill_level14`,`skill_id15`,`skill_level15`,`skill_id16`,`skill_level16`,`skill_id17`,`skill_level17`,`skill_id18`,`skill_level18`,`skill_id19`,`skill_level19`,`skill_id20`,`skill_level20` FROM `skill_set`;");
        }
        
        // SQL statements for card/available_skill_set
        
        /// <summary>
        /// SELECT `available_skill_set_id`,`skill_id`,`need_rank` FROM `available_skill_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_AvailableSkillSet()
        {
            if (_selectQuery_masterAvailableSkillSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterAvailableSkillSet = connection.PreparedQuery("SELECT `available_skill_set_id`,`skill_id`,`need_rank` FROM `available_skill_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterAvailableSkillSet;
        }
        
        /// <summary>
        /// SELECT `id`,`skill_id`,`need_rank` FROM `available_skill_set` WHERE `available_skill_set_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_AvailableSkillSet_AvailableSkillSetId()
        {
            if (_indexedSelectQuery_availableSkillSet_availableSkillSetId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_availableSkillSet_availableSkillSetId = connection.PreparedQuery("SELECT `id`,`skill_id`,`need_rank` FROM `available_skill_set` WHERE `available_skill_set_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_availableSkillSet_availableSkillSetId;
        }
        
        /// <summary>
        /// SELECT `id`,`available_skill_set_id`,`skill_id`,`need_rank` FROM `available_skill_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_AvailableSkillSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`available_skill_set_id`,`skill_id`,`need_rank` FROM `available_skill_set`;");
        }
        
        // SQL statements for card/skill_exp
        
        /// <summary>
        /// SELECT `type`,`level`,`scale` FROM `skill_exp` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillExp()
        {
            if (_selectQuery_masterSkillExp == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillExp = connection.PreparedQuery("SELECT `type`,`level`,`scale` FROM `skill_exp` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillExp;
        }
        
        /// <summary>
        /// SELECT `id`,`level`,`scale` FROM `skill_exp` WHERE `type`=? ORDER BY `level` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillExp_Type()
        {
            if (_indexedSelectQuery_skillExp_type == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillExp_type = connection.PreparedQuery("SELECT `id`,`level`,`scale` FROM `skill_exp` WHERE `type`=? ORDER BY `level` ASC;");
            }
        
            return _indexedSelectQuery_skillExp_type;
        }
        
        /// <summary>
        /// SELECT `id`,`type`,`level`,`scale` FROM `skill_exp`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillExp()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`type`,`level`,`scale` FROM `skill_exp`;");
        }
        
        // SQL statements for card/skill_level_value
        
        /// <summary>
        /// SELECT `ability_type`,`level`,`float_ability_value_coef` FROM `skill_level_value` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillLevelValue()
        {
            if (_selectQuery_masterSkillLevelValue == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillLevelValue = connection.PreparedQuery("SELECT `ability_type`,`level`,`float_ability_value_coef` FROM `skill_level_value` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillLevelValue;
        }
        
        /// <summary>
        /// SELECT `id`,`level`,`float_ability_value_coef` FROM `skill_level_value` WHERE `ability_type`=? ORDER BY `level` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillLevelValue_AbilityType()
        {
            if (_indexedSelectQuery_skillLevelValue_abilityType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillLevelValue_abilityType = connection.PreparedQuery("SELECT `id`,`level`,`float_ability_value_coef` FROM `skill_level_value` WHERE `ability_type`=? ORDER BY `level` ASC;");
            }
        
            return _indexedSelectQuery_skillLevelValue_abilityType;
        }
        
        /// <summary>
        /// SELECT `id`,`ability_type`,`level`,`float_ability_value_coef` FROM `skill_level_value`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillLevelValue()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`ability_type`,`level`,`float_ability_value_coef` FROM `skill_level_value`;");
        }
        
        // SQL statements for card/skill_upgrade_description
        
        /// <summary>
        /// SELECT `card_id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillUpgradeDescription()
        {
            if (_selectQuery_masterSkillUpgradeDescription == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillUpgradeDescription = connection.PreparedQuery("SELECT `card_id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillUpgradeDescription;
        }
        
        /// <summary>
        /// SELECT `id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? ORDER BY `rank` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillUpgradeDescription_CardId()
        {
            if (_indexedSelectQuery_skillUpgradeDescription_cardId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillUpgradeDescription_cardId = connection.PreparedQuery("SELECT `id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? ORDER BY `rank` ASC;");
            }
        
            return _indexedSelectQuery_skillUpgradeDescription_cardId;
        }
        
        /// <summary>
        /// SELECT `id`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? AND `rank`=? ORDER BY `rank` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillUpgradeDescription_CardId_Rank()
        {
            if (_indexedSelectQuery_skillUpgradeDescription_cardId_rank == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillUpgradeDescription_cardId_rank = connection.PreparedQuery("SELECT `id`,`skill_id`,`start_date` FROM `skill_upgrade_description` WHERE `card_id`=? AND `rank`=? ORDER BY `rank` ASC;");
            }
        
            return _indexedSelectQuery_skillUpgradeDescription_cardId_rank;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillUpgradeDescription()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`card_id`,`rank`,`skill_id`,`start_date` FROM `skill_upgrade_description`;");
        }
        
        // SQL statements for card/skill_upgrade_speciality
        
        /// <summary>
        /// SELECT `scenario_id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillUpgradeSpeciality()
        {
            if (_selectQuery_masterSkillUpgradeSpeciality == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillUpgradeSpeciality = connection.PreparedQuery("SELECT `scenario_id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillUpgradeSpeciality;
        }
        
        /// <summary>
        /// SELECT `id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `scenario_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillUpgradeSpeciality_ScenarioId()
        {
            if (_indexedSelectQuery_skillUpgradeSpeciality_scenarioId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillUpgradeSpeciality_scenarioId = connection.PreparedQuery("SELECT `id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `scenario_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_skillUpgradeSpeciality_scenarioId;
        }
        
        /// <summary>
        /// SELECT `id`,`scenario_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `base_skill_id`=? ORDER BY `scenario_id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillUpgradeSpeciality_BaseSkillId()
        {
            if (_indexedSelectQuery_skillUpgradeSpeciality_baseSkillId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillUpgradeSpeciality_baseSkillId = connection.PreparedQuery("SELECT `id`,`scenario_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality` WHERE `base_skill_id`=? ORDER BY `scenario_id` ASC;");
            }
        
            return _indexedSelectQuery_skillUpgradeSpeciality_baseSkillId;
        }
        
        /// <summary>
        /// SELECT `id`,`scenario_id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillUpgradeSpeciality()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`scenario_id`,`base_skill_id`,`skill_id`,`start_date` FROM `skill_upgrade_speciality`;");
        }
        
        // SQL statements for card/skill_upgrade_condition
        
        /// <summary>
        /// SELECT `upgrade_type`,`description_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillUpgradeCondition()
        {
            if (_selectQuery_masterSkillUpgradeCondition == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillUpgradeCondition = connection.PreparedQuery("SELECT `upgrade_type`,`description_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillUpgradeCondition;
        }
        
        /// <summary>
        /// SELECT `id`,`upgrade_type`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition` WHERE `description_id`=? ORDER BY `num` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillUpgradeCondition_DescriptionId()
        {
            if (_indexedSelectQuery_skillUpgradeCondition_descriptionId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillUpgradeCondition_descriptionId = connection.PreparedQuery("SELECT `id`,`upgrade_type`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition` WHERE `description_id`=? ORDER BY `num` ASC;");
            }
        
            return _indexedSelectQuery_skillUpgradeCondition_descriptionId;
        }
        
        /// <summary>
        /// SELECT `id`,`upgrade_type`,`description_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillUpgradeCondition()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`upgrade_type`,`description_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_upgrade_condition`;");
        }
        
        // SQL statements for card/skill_up_scenario_condition
        
        /// <summary>
        /// SELECT `scenario_id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SkillUpScenarioCondition()
        {
            if (_selectQuery_masterSkillUpScenarioCondition == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSkillUpScenarioCondition = connection.PreparedQuery("SELECT `scenario_id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSkillUpScenarioCondition;
        }
        
        /// <summary>
        /// SELECT `id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition` WHERE `scenario_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SkillUpScenarioCondition_ScenarioId()
        {
            if (_indexedSelectQuery_skillUpScenarioCondition_scenarioId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_skillUpScenarioCondition_scenarioId = connection.PreparedQuery("SELECT `id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition` WHERE `scenario_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_skillUpScenarioCondition_scenarioId;
        }
        
        /// <summary>
        /// SELECT `id`,`scenario_id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SkillUpScenarioCondition()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`scenario_id`,`rank`,`condition_id`,`num`,`sub_num`,`timing_type`,`count_type` FROM `skill_up_scenario_condition`;");
        }
        
        // SQL statements for card/character_system_text
        
        /// <summary>
        /// SELECT `text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `character_id`=? AND `voice_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharacterSystemText()
        {
            if (_selectQuery_masterCharacterSystemText == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharacterSystemText = connection.PreparedQuery("SELECT `text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `character_id`=? AND `voice_id`=?;");
            }
        
            return _selectQuery_masterCharacterSystemText;
        }
        
        /// <summary>
        /// SELECT `voice_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `character_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharacterSystemText_CharacterId()
        {
            if (_indexedSelectQuery_characterSystemText_characterId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_characterSystemText_characterId = connection.PreparedQuery("SELECT `voice_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `character_id`=?;");
            }
        
            return _indexedSelectQuery_characterSystemText_characterId;
        }
        
        /// <summary>
        /// SELECT `character_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `voice_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharacterSystemText_VoiceId()
        {
            if (_indexedSelectQuery_characterSystemText_voiceId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_characterSystemText_voiceId = connection.PreparedQuery("SELECT `character_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text` WHERE `voice_id`=?;");
            }
        
            return _indexedSelectQuery_characterSystemText_voiceId;
        }
        
        /// <summary>
        /// SELECT `character_id`,`voice_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharacterSystemText()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `character_id`,`voice_id`,`text`,`cue_sheet`,`cue_id`,`motion_set`,`scene`,`use_gallery`,`card_id`,`lip_sync_data`,`gender`,`motion_second_set`,`motion_second_start`,`start_date` FROM `character_system_text`;");
        }
        
        // SQL statements for card/character_system_lottery
        
        /// <summary>
        /// SELECT `chara_id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharacterSystemLottery()
        {
            if (_selectQuery_masterCharacterSystemLottery == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharacterSystemLottery = connection.PreparedQuery("SELECT `chara_id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharacterSystemLottery;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharacterSystemLottery_CharaId()
        {
            if (_indexedSelectQuery_characterSystemLottery_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_characterSystemLottery_charaId = connection.PreparedQuery("SELECT `id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=?;");
            }
        
            return _indexedSelectQuery_characterSystemLottery_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`card_rarity_id`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=? AND `trigger`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharacterSystemLottery_CharaId_Trigger()
        {
            if (_indexedSelectQuery_characterSystemLottery_charaId_trigger == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_characterSystemLottery_charaId_trigger = connection.PreparedQuery("SELECT `id`,`card_id`,`card_rarity_id`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `chara_id`=? AND `trigger`=?;");
            }
        
            return _indexedSelectQuery_characterSystemLottery_charaId_trigger;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`card_id`,`card_rarity_id`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `trigger`=? AND `param1`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharacterSystemLottery_Trigger_Param1()
        {
            if (_indexedSelectQuery_characterSystemLottery_trigger_param1 == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_characterSystemLottery_trigger_param1 = connection.PreparedQuery("SELECT `id`,`chara_id`,`card_id`,`card_rarity_id`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery` WHERE `trigger`=? AND `param1`=?;");
            }
        
            return _indexedSelectQuery_characterSystemLottery_trigger_param1;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharacterSystemLottery()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`card_id`,`card_rarity_id`,`trigger`,`param1`,`per`,`priority`,`sys_text_id` FROM `character_system_lottery`;");
        }
        
        // SQL statements for card/face_type_data
        
        /// <summary>
        /// SELECT `eyebrow_l`,`eyebrow_r`,`eye_l`,`eye_r`,`mouth`,`mouth_shape_type`,`inverce_face_type`,`set_face_group` FROM `face_type_data` WHERE `label`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_FaceTypeData()
        {
            if (_selectQuery_masterFaceTypeData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterFaceTypeData = connection.PreparedQuery("SELECT `eyebrow_l`,`eyebrow_r`,`eye_l`,`eye_r`,`mouth`,`mouth_shape_type`,`inverce_face_type`,`set_face_group` FROM `face_type_data` WHERE `label`=?;");
            }
        
            return _selectQuery_masterFaceTypeData;
        }
        
        /// <summary>
        /// SELECT `label`,`eyebrow_l`,`eyebrow_r`,`eye_l`,`eye_r`,`mouth`,`mouth_shape_type`,`inverce_face_type`,`set_face_group` FROM `face_type_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_FaceTypeData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `label`,`eyebrow_l`,`eyebrow_r`,`eye_l`,`eye_r`,`mouth`,`mouth_shape_type`,`inverce_face_type`,`set_face_group` FROM `face_type_data`;");
        }
        
        // SQL statements for card/facial_mouth_change
        
        /// <summary>
        /// SELECT `chara_id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_FacialMouthChange()
        {
            if (_selectQuery_masterFacialMouthChange == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterFacialMouthChange = connection.PreparedQuery("SELECT `chara_id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change` WHERE `id`=?;");
            }
        
            return _selectQuery_masterFacialMouthChange;
        }
        
        /// <summary>
        /// SELECT `id`,`after_facialname` FROM `facial_mouth_change` WHERE `chara_id`=? AND `before_facialname`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_FacialMouthChange_CharaId_BeforeFacialname()
        {
            if (_indexedSelectQuery_facialMouthChange_charaId_beforeFacialname == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_facialMouthChange_charaId_beforeFacialname = connection.PreparedQuery("SELECT `id`,`after_facialname` FROM `facial_mouth_change` WHERE `chara_id`=? AND `before_facialname`=?;");
            }
        
            return _indexedSelectQuery_facialMouthChange_charaId_beforeFacialname;
        }
        
        /// <summary>
        /// SELECT `id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_FacialMouthChange_CharaId()
        {
            if (_indexedSelectQuery_facialMouthChange_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_facialMouthChange_charaId = connection.PreparedQuery("SELECT `id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change` WHERE `chara_id`=?;");
            }
        
            return _indexedSelectQuery_facialMouthChange_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_FacialMouthChange()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`before_facialname`,`after_facialname` FROM `facial_mouth_change`;");
        }
        
        // SQL statements for card/chara_motion_act
        
        /// <summary>
        /// SELECT `chara_id`,`target_motion`,`command_name` FROM `chara_motion_act` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaMotionAct()
        {
            if (_selectQuery_masterCharaMotionAct == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaMotionAct = connection.PreparedQuery("SELECT `chara_id`,`target_motion`,`command_name` FROM `chara_motion_act` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharaMotionAct;
        }
        
        /// <summary>
        /// SELECT `id`,`target_motion` FROM `chara_motion_act` WHERE `chara_id`=? AND `command_name`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharaMotionAct_CharaId_CommandName()
        {
            if (_indexedSelectQuery_charaMotionAct_charaId_commandName == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_charaMotionAct_charaId_commandName = connection.PreparedQuery("SELECT `id`,`target_motion` FROM `chara_motion_act` WHERE `chara_id`=? AND `command_name`=?;");
            }
        
            return _indexedSelectQuery_charaMotionAct_charaId_commandName;
        }
        
        /// <summary>
        /// SELECT `id`,`target_motion`,`command_name` FROM `chara_motion_act` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharaMotionAct_CharaId()
        {
            if (_indexedSelectQuery_charaMotionAct_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_charaMotionAct_charaId = connection.PreparedQuery("SELECT `id`,`target_motion`,`command_name` FROM `chara_motion_act` WHERE `chara_id`=?;");
            }
        
            return _indexedSelectQuery_charaMotionAct_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`target_motion`,`command_name` FROM `chara_motion_act`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaMotionAct()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`target_motion`,`command_name` FROM `chara_motion_act`;");
        }
        
        // SQL statements for card/random_ear_tail_motion
        
        /// <summary>
        /// SELECT `parts_type`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RandomEarTailMotion()
        {
            if (_selectQuery_masterRandomEarTailMotion == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRandomEarTailMotion = connection.PreparedQuery("SELECT `parts_type`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRandomEarTailMotion;
        }
        
        /// <summary>
        /// SELECT `id`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion` WHERE `parts_type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RandomEarTailMotion_PartsType()
        {
            if (_indexedSelectQuery_randomEarTailMotion_partsType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_randomEarTailMotion_partsType = connection.PreparedQuery("SELECT `id`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion` WHERE `parts_type`=?;");
            }
        
            return _indexedSelectQuery_randomEarTailMotion_partsType;
        }
        
        /// <summary>
        /// SELECT `id`,`parts_type`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RandomEarTailMotion()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`parts_type`,`motion_name`,`ear_type`,`use_story`,`use_theater` FROM `random_ear_tail_motion`;");
        }
        
        // SQL statements for card/nickname
        
        /// <summary>
        /// SELECT `type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`user_show`,`start_date`,`end_date` FROM `nickname` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_Nickname()
        {
            if (_selectQuery_masterNickname == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterNickname = connection.PreparedQuery("SELECT `type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`user_show`,`start_date`,`end_date` FROM `nickname` WHERE `id`=?;");
            }
        
            return _selectQuery_masterNickname;
        }
        
        /// <summary>
        /// SELECT `id`,`type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`start_date`,`end_date` FROM `nickname` WHERE `user_show`=? ORDER BY `disp_order` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_Nickname_UserShow()
        {
            if (_indexedSelectQuery_nickname_userShow == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_nickname_userShow = connection.PreparedQuery("SELECT `id`,`type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`start_date`,`end_date` FROM `nickname` WHERE `user_show`=? ORDER BY `disp_order` ASC;");
            }
        
            return _indexedSelectQuery_nickname_userShow;
        }
        
        /// <summary>
        /// SELECT `id`,`type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`user_show`,`start_date`,`end_date` FROM `nickname`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_Nickname()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`type`,`scenario_id`,`disp_order`,`group_id`,`rank`,`chara_data_id`,`user_show`,`start_date`,`end_date` FROM `nickname`;");
        }
        
        // SQL statements for card/support_card_limit_break
        
        /// <summary>
        /// SELECT `rarity`,`item_id`,`item_num`,`disp_order` FROM `support_card_limit_break` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardLimitBreak()
        {
            if (_selectQuery_masterSupportCardLimitBreak == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardLimitBreak = connection.PreparedQuery("SELECT `rarity`,`item_id`,`item_num`,`disp_order` FROM `support_card_limit_break` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSupportCardLimitBreak;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`item_id`,`item_num`,`disp_order` FROM `support_card_limit_break`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardLimitBreak()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`rarity`,`item_id`,`item_num`,`disp_order` FROM `support_card_limit_break`;");
        }
        
        // SQL statements for card/card_talent_hint_upgrade
        
        /// <summary>
        /// SELECT `rarity`,`talent_level`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CardTalentHintUpgrade()
        {
            if (_selectQuery_masterCardTalentHintUpgrade == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCardTalentHintUpgrade = connection.PreparedQuery("SELECT `rarity`,`talent_level`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCardTalentHintUpgrade;
        }
        
        /// <summary>
        /// SELECT `id`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade` WHERE `rarity`=? AND `talent_level`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CardTalentHintUpgrade_Rarity_TalentLevel()
        {
            if (_indexedSelectQuery_cardTalentHintUpgrade_rarity_talentLevel == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_cardTalentHintUpgrade_rarity_talentLevel = connection.PreparedQuery("SELECT `id`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade` WHERE `rarity`=? AND `talent_level`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_cardTalentHintUpgrade_rarity_talentLevel;
        }
        
        /// <summary>
        /// SELECT `id`,`rarity`,`talent_level`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CardTalentHintUpgrade()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`rarity`,`talent_level`,`item_category_1`,`item_id_1`,`item_disp_order_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_disp_order_2`,`item_num_2`,`money_num` FROM `card_talent_hint_upgrade`;");
        }
        
        // SQL statements for card/support_card_group
        
        /// <summary>
        /// SELECT `support_card_id`,`chara_id`,`outing_max` FROM `support_card_group` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_SupportCardGroup()
        {
            if (_selectQuery_masterSupportCardGroup == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterSupportCardGroup = connection.PreparedQuery("SELECT `support_card_id`,`chara_id`,`outing_max` FROM `support_card_group` WHERE `id`=?;");
            }
        
            return _selectQuery_masterSupportCardGroup;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`outing_max` FROM `support_card_group` WHERE `support_card_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SupportCardGroup_SupportCardId()
        {
            if (_indexedSelectQuery_supportCardGroup_supportCardId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_supportCardGroup_supportCardId = connection.PreparedQuery("SELECT `id`,`chara_id`,`outing_max` FROM `support_card_group` WHERE `support_card_id`=?;");
            }
        
            return _indexedSelectQuery_supportCardGroup_supportCardId;
        }
        
        /// <summary>
        /// SELECT `id`,`support_card_id`,`outing_max` FROM `support_card_group` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_SupportCardGroup_CharaId()
        {
            if (_indexedSelectQuery_supportCardGroup_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_supportCardGroup_charaId = connection.PreparedQuery("SELECT `id`,`support_card_id`,`outing_max` FROM `support_card_group` WHERE `chara_id`=?;");
            }
        
            return _indexedSelectQuery_supportCardGroup_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`support_card_id`,`chara_id`,`outing_max` FROM `support_card_group`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_SupportCardGroup()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`support_card_id`,`chara_id`,`outing_max` FROM `support_card_group`;");
        }
        
        // SQL statements for card/chara_dress_color_set_default
        
        /// <summary>
        /// SELECT `chara_id`,`color_set_id` FROM `chara_dress_color_set_default` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaDressColorSetDefault()
        {
            if (_selectQuery_masterCharaDressColorSetDefault == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaDressColorSetDefault = connection.PreparedQuery("SELECT `chara_id`,`color_set_id` FROM `chara_dress_color_set_default` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharaDressColorSetDefault;
        }
        
        /// <summary>
        /// SELECT `id`,`color_set_id` FROM `chara_dress_color_set_default` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharaDressColorSetDefault_CharaId()
        {
            if (_indexedSelectQuery_charaDressColorSetDefault_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_charaDressColorSetDefault_charaId = connection.PreparedQuery("SELECT `id`,`color_set_id` FROM `chara_dress_color_set_default` WHERE `chara_id`=?;");
            }
        
            return _indexedSelectQuery_charaDressColorSetDefault_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`color_set_id` FROM `chara_dress_color_set_default`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaDressColorSetDefault()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`color_set_id` FROM `chara_dress_color_set_default`;");
        }
        
        // SQL statements for card/chara_dress_color_set
        
        /// <summary>
        /// SELECT `dress_id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CharaDressColorSet()
        {
            if (_selectQuery_masterCharaDressColorSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCharaDressColorSet = connection.PreparedQuery("SELECT `dress_id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCharaDressColorSet;
        }
        
        /// <summary>
        /// SELECT `id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set` WHERE `dress_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CharaDressColorSet_DressId()
        {
            if (_indexedSelectQuery_charaDressColorSet_dressId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_charaDressColorSet_dressId = connection.PreparedQuery("SELECT `id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set` WHERE `dress_id`=?;");
            }
        
            return _indexedSelectQuery_charaDressColorSet_dressId;
        }
        
        /// <summary>
        /// SELECT `id`,`dress_id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CharaDressColorSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`dress_id`,`area_map_id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `chara_dress_color_set`;");
        }
    }
}
#endif
// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_overrun_pattern
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceOverrunPattern : AbstractMasterData
    {
        public const string TABLE_NAME = "race_overrun_pattern";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceOverrunPattern> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceOverrunPattern> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceOverrunPattern");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceOverrunPattern(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceOverrunPattern>();
            _db = db;
        }


        public RaceOverrunPattern Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceOverrunPattern");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceOverrunPattern", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceOverrunPattern _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceOverrunPattern();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceOverrunPattern");
                return null;
            }

            // SELECT `finish_order_0_type`,`finish_order_1_type`,`finish_order_2_type`,`finish_order_3_type`,`finish_order_4_type`,`finish_order_lose_type` FROM `race_overrun_pattern` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceOverrunPattern orm = null;

            if (query.Step())
            {
                int finishOrder0Type    = (int)query.GetInt(0);
                int finishOrder1Type    = (int)query.GetInt(1);
                int finishOrder2Type    = (int)query.GetInt(2);
                int finishOrder3Type    = (int)query.GetInt(3);
                int finishOrder4Type    = (int)query.GetInt(4);
                int finishOrderLoseType = (int)query.GetInt(5);

                orm = new RaceOverrunPattern(id, finishOrder0Type, finishOrder1Type, finishOrder2Type, finishOrder3Type, finishOrder4Type, finishOrderLoseType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceOverrunPattern()) {
                while (query.Step()) {
                    int id                  = (int)query.GetInt(0);
                    int finishOrder0Type    = (int)query.GetInt(1);
                    int finishOrder1Type    = (int)query.GetInt(2);
                    int finishOrder2Type    = (int)query.GetInt(3);
                    int finishOrder3Type    = (int)query.GetInt(4);
                    int finishOrder4Type    = (int)query.GetInt(5);
                    int finishOrderLoseType = (int)query.GetInt(6);

                    int key = (int)id;
                    RaceOverrunPattern orm = new RaceOverrunPattern(id, finishOrder0Type, finishOrder1Type, finishOrder2Type, finishOrder3Type, finishOrder4Type, finishOrderLoseType);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceOverrunPattern
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: finish_order_0_type) </summary>
            public readonly int FinishOrder0Type;
            /// <summary> (CSV column: finish_order_1_type) </summary>
            public readonly int FinishOrder1Type;
            /// <summary> (CSV column: finish_order_2_type) </summary>
            public readonly int FinishOrder2Type;
            /// <summary> (CSV column: finish_order_3_type) </summary>
            public readonly int FinishOrder3Type;
            /// <summary> (CSV column: finish_order_4_type) </summary>
            public readonly int FinishOrder4Type;
            /// <summary> (CSV column: finish_order_lose_type) </summary>
            public readonly int FinishOrderLoseType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceOverrunPattern(int id = 0, int finishOrder0Type = 0, int finishOrder1Type = 0, int finishOrder2Type = 0, int finishOrder3Type = 0, int finishOrder4Type = 0, int finishOrderLoseType = 0)
            {
                this.Id                  = id;
                this.FinishOrder0Type    = finishOrder0Type;
                this.FinishOrder1Type    = finishOrder1Type;
                this.FinishOrder2Type    = finishOrder2Type;
                this.FinishOrder3Type    = finishOrder3Type;
                this.FinishOrder4Type    = finishOrder4Type;
                this.FinishOrderLoseType = finishOrderLoseType;
            }
        }
    }
}
#endif

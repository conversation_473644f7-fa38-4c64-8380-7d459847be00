// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_instance
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:race_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceInstance : AbstractMasterData
    {
        public const string TABLE_NAME = "race_instance";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceInstance> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceInstance>> _dictionaryWithRaceId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceInstance> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceInstance");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceInstance(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceInstance>();
            _dictionaryWithRaceId = new Dictionary<int, List<RaceInstance>>();
            _db = db;
        }


        public RaceInstance Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceInstance");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceInstance", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceInstance _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceInstance();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceInstance");
                return null;
            }

            // SELECT `race_id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceInstance orm = null;

            if (query.Step())
            {
                int raceId     = (int)query.GetInt(0);
                int npcGroupId = (int)query.GetInt(1);
                int date       = (int)query.GetInt(2);
                int time       = (int)query.GetInt(3);
                int clockTime  = (int)query.GetInt(4);
                int raceNumber = (int)query.GetInt(5);

                orm = new RaceInstance(id, raceId, npcGroupId, date, time, clockTime, raceNumber);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceInstance GetWithRaceIdOrderByIdAsc(int raceId)
        {
            RaceInstance orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceIdOrderByIdAsc(raceId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceInstance _SelectWithRaceIdOrderByIdAsc(int raceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceInstance_RaceId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceInstance");
                return null;
            }

            // SELECT `id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance` WHERE `race_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceId)) { return null; }

            RaceInstance orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceIdOrderByIdAsc(query, raceId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceInstance> GetListWithRaceIdOrderByIdAsc(int raceId)
        {
            int key = (int)raceId;

            if (!_dictionaryWithRaceId.ContainsKey(key)) {
                _dictionaryWithRaceId.Add(key, _ListSelectWithRaceIdOrderByIdAsc(raceId));
            }

            return _dictionaryWithRaceId[key];
        }

        public List<RaceInstance> MaybeListWithRaceIdOrderByIdAsc(int raceId)
        {
            List<RaceInstance> list = GetListWithRaceIdOrderByIdAsc(raceId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceInstance> _ListSelectWithRaceIdOrderByIdAsc(int raceId)
        {
            List<RaceInstance> _list = new List<RaceInstance>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceInstance_RaceId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceInstance");
                return null;
            }

            // SELECT `id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance` WHERE `race_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceId)) { return null; }

            while (query.Step()) {
                RaceInstance orm = _CreateOrmByQueryResultWithRaceIdOrderByIdAsc(query, raceId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceInstance _CreateOrmByQueryResultWithRaceIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int raceId)
        {
            int id         = (int)query.GetInt(0);
            int npcGroupId = (int)query.GetInt(1);
            int date       = (int)query.GetInt(2);
            int time       = (int)query.GetInt(3);
            int clockTime  = (int)query.GetInt(4);
            int raceNumber = (int)query.GetInt(5);

            return new RaceInstance(id, raceId, npcGroupId, date, time, clockTime, raceNumber);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithRaceId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceInstance()) {
                while (query.Step()) {
                    int id         = (int)query.GetInt(0);
                    int raceId     = (int)query.GetInt(1);
                    int npcGroupId = (int)query.GetInt(2);
                    int date       = (int)query.GetInt(3);
                    int time       = (int)query.GetInt(4);
                    int clockTime  = (int)query.GetInt(5);
                    int raceNumber = (int)query.GetInt(6);

                    int key = (int)id;
                    RaceInstance orm = new RaceInstance(id, raceId, npcGroupId, date, time, clockTime, raceNumber);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceInstance
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: race_id) </summary>
            public readonly int RaceId;
            /// <summary> (CSV column: npc_group_id) </summary>
            public readonly int NpcGroupId;
            /// <summary> (CSV column: date) </summary>
            public readonly int Date;
            /// <summary> (CSV column: time) </summary>
            public readonly int Time;
            /// <summary> (CSV column: clock_time) </summary>
            public readonly int ClockTime;
            /// <summary> (CSV column: race_number) </summary>
            public readonly int RaceNumber;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceInstance(int id = 0, int raceId = 0, int npcGroupId = 0, int date = 0, int time = 0, int clockTime = 0, int raceNumber = 0)
            {
                this.Id         = id;
                this.RaceId     = raceId;
                this.NpcGroupId = npcGroupId;
                this.Date       = date;
                this.Time       = time;
                this.ClockTime  = clockTime;
                this.RaceNumber = raceNumber;
            }
        }
    }
}
#endif

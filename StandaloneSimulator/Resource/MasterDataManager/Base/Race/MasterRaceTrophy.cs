// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_trophy
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:trophy_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceTrophy : AbstractMasterData
    {
        public const string TABLE_NAME = "race_trophy";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceTrophy> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceTrophy>> _dictionaryWithTrophyId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceTrophy> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceTrophy");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceTrophy(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceTrophy>();
            _dictionaryWithTrophyId = new Dictionary<int, List<RaceTrophy>>();
            _db = db;
        }


        public RaceTrophy Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceTrophy");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceTrophy", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceTrophy _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceTrophy();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceTrophy");
                return null;
            }

            // SELECT `trophy_id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceTrophy orm = null;

            if (query.Step())
            {
                int trophyId          = (int)query.GetInt(0);
                int raceInstanceId    = (int)query.GetInt(1);
                int originalFlag      = (int)query.GetInt(2);
                int dispOrder         = (int)query.GetInt(3);
                int size              = (int)query.GetInt(4);
                int eventType         = (int)query.GetInt(5);
                string startDate      = query.GetText(6);
                string displayEndDate = query.GetText(7);

                orm = new RaceTrophy(id, trophyId, raceInstanceId, originalFlag, dispOrder, size, eventType, startDate, displayEndDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceTrophy GetWithTrophyIdOrderByIdAsc(int trophyId)
        {
            RaceTrophy orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithTrophyIdOrderByIdAsc(trophyId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", trophyId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceTrophy _SelectWithTrophyIdOrderByIdAsc(int trophyId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceTrophy_TrophyId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceTrophy");
                return null;
            }

            // SELECT `id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `trophy_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, trophyId)) { return null; }

            RaceTrophy orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithTrophyIdOrderByIdAsc(query, trophyId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceTrophy> GetListWithTrophyIdOrderByIdAsc(int trophyId)
        {
            int key = (int)trophyId;

            if (!_dictionaryWithTrophyId.ContainsKey(key)) {
                _dictionaryWithTrophyId.Add(key, _ListSelectWithTrophyIdOrderByIdAsc(trophyId));
            }

            return _dictionaryWithTrophyId[key];
        }

        public List<RaceTrophy> MaybeListWithTrophyIdOrderByIdAsc(int trophyId)
        {
            List<RaceTrophy> list = GetListWithTrophyIdOrderByIdAsc(trophyId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceTrophy> _ListSelectWithTrophyIdOrderByIdAsc(int trophyId)
        {
            List<RaceTrophy> _list = new List<RaceTrophy>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceTrophy_TrophyId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceTrophy");
                return null;
            }

            // SELECT `id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `trophy_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, trophyId)) { return null; }

            while (query.Step()) {
                RaceTrophy orm = _CreateOrmByQueryResultWithTrophyIdOrderByIdAsc(query, trophyId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceTrophy _CreateOrmByQueryResultWithTrophyIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int trophyId)
        {
            int id                = (int)query.GetInt(0);
            int raceInstanceId    = (int)query.GetInt(1);
            int originalFlag      = (int)query.GetInt(2);
            int dispOrder         = (int)query.GetInt(3);
            int size              = (int)query.GetInt(4);
            int eventType         = (int)query.GetInt(5);
            string startDate      = query.GetText(6);
            string displayEndDate = query.GetText(7);

            return new RaceTrophy(id, trophyId, raceInstanceId, originalFlag, dispOrder, size, eventType, startDate, displayEndDate);
        }

        public RaceTrophy GetWithRaceInstanceId(int raceInstanceId)
        {
            RaceTrophy orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceInstanceId(raceInstanceId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceInstanceId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceTrophy _SelectWithRaceInstanceId(int raceInstanceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceTrophy_RaceInstanceId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceTrophy");
                return null;
            }

            // SELECT `id`,`trophy_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `race_instance_id`=?;
            if (!query.BindInt(1, raceInstanceId)) { return null; }

            RaceTrophy orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceInstanceId(query, raceInstanceId);
            }

            query.Reset();

            return orm;
        }

        private RaceTrophy _CreateOrmByQueryResultWithRaceInstanceId(LibNative.Sqlite3.PreparedQuery query, int raceInstanceId)
        {
            int id                = (int)query.GetInt(0);
            int trophyId          = (int)query.GetInt(1);
            int originalFlag      = (int)query.GetInt(2);
            int dispOrder         = (int)query.GetInt(3);
            int size              = (int)query.GetInt(4);
            int eventType         = (int)query.GetInt(5);
            string startDate      = query.GetText(6);
            string displayEndDate = query.GetText(7);

            return new RaceTrophy(id, trophyId, raceInstanceId, originalFlag, dispOrder, size, eventType, startDate, displayEndDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithTrophyId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceTrophy()) {
                while (query.Step()) {
                    int id                = (int)query.GetInt(0);
                    int trophyId          = (int)query.GetInt(1);
                    int raceInstanceId    = (int)query.GetInt(2);
                    int originalFlag      = (int)query.GetInt(3);
                    int dispOrder         = (int)query.GetInt(4);
                    int size              = (int)query.GetInt(5);
                    int eventType         = (int)query.GetInt(6);
                    string startDate      = query.GetText(7);
                    string displayEndDate = query.GetText(8);

                    int key = (int)id;
                    RaceTrophy orm = new RaceTrophy(id, trophyId, raceInstanceId, originalFlag, dispOrder, size, eventType, startDate, displayEndDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceTrophy
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: trophy_id) </summary>
            public readonly int TrophyId;
            /// <summary> (CSV column: race_instance_id) </summary>
            public readonly int RaceInstanceId;
            /// <summary> (CSV column: original_flag) </summary>
            public readonly int OriginalFlag;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary> (CSV column: size) </summary>
            public readonly int Size;
            /// <summary> (CSV column: event_type) </summary>
            public readonly int EventType;
            /// <summary> (CSV column: start_date) </summary>
            public readonly string StartDate;
            /// <summary> (CSV column: display_end_date) </summary>
            public readonly string DisplayEndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceTrophy(int id = 0, int trophyId = 0, int raceInstanceId = 0, int originalFlag = 0, int dispOrder = 0, int size = 0, int eventType = 0, string startDate = "", string displayEndDate = "")
            {
                this.Id             = id;
                this.TrophyId       = trophyId;
                this.RaceInstanceId = raceInstanceId;
                this.OriginalFlag   = originalFlag;
                this.DispOrder      = dispOrder;
                this.Size           = size;
                this.EventType      = eventType;
                this.StartDate      = startDate;
                this.DisplayEndDate = displayEndDate;
            }
        }
    }
}
#endif

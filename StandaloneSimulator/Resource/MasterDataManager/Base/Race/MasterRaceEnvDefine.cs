// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_env_define
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:race_track_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceEnvDefine : AbstractMasterData
    {
        public const string TABLE_NAME = "race_env_define";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceEnvDefine> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceEnvDefine>> _dictionaryWithRaceTrackId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceEnvDefine(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceEnvDefine>();
            _dictionaryWithRaceTrackId = new Dictionary<int, List<RaceEnvDefine>>();
            _db = db;
        }


        public RaceEnvDefine Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceEnvDefine");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceEnvDefine", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceEnvDefine _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceEnvDefine();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceEnvDefine");
                return null;
            }

            // SELECT `race_track_id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceEnvDefine orm = null;

            if (query.Step())
            {
                int raceTrackId = (int)query.GetInt(0);
                int season      = (int)query.GetInt(1);
                int weather     = (int)query.GetInt(2);
                int timezone    = (int)query.GetInt(3);
                int resource    = (int)query.GetInt(4);

                orm = new RaceEnvDefine(id, raceTrackId, season, weather, timezone, resource);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceEnvDefine GetWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            RaceEnvDefine orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceTrackIdOrderByIdAsc(raceTrackId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceTrackId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceEnvDefine _SelectWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceEnvDefine_RaceTrackId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceEnvDefine");
                return null;
            }

            // SELECT `id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define` WHERE `race_track_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceTrackId)) { return null; }

            RaceEnvDefine orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceTrackIdOrderByIdAsc(query, raceTrackId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceEnvDefine> GetListWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            int key = (int)raceTrackId;

            if (!_dictionaryWithRaceTrackId.ContainsKey(key)) {
                _dictionaryWithRaceTrackId.Add(key, _ListSelectWithRaceTrackIdOrderByIdAsc(raceTrackId));
            }

            return _dictionaryWithRaceTrackId[key];
        }

        public List<RaceEnvDefine> MaybeListWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            List<RaceEnvDefine> list = GetListWithRaceTrackIdOrderByIdAsc(raceTrackId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceEnvDefine> _ListSelectWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            List<RaceEnvDefine> _list = new List<RaceEnvDefine>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceEnvDefine_RaceTrackId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceEnvDefine");
                return null;
            }

            // SELECT `id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define` WHERE `race_track_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceTrackId)) { return null; }

            while (query.Step()) {
                RaceEnvDefine orm = _CreateOrmByQueryResultWithRaceTrackIdOrderByIdAsc(query, raceTrackId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceEnvDefine _CreateOrmByQueryResultWithRaceTrackIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int raceTrackId)
        {
            int id       = (int)query.GetInt(0);
            int season   = (int)query.GetInt(1);
            int weather  = (int)query.GetInt(2);
            int timezone = (int)query.GetInt(3);
            int resource = (int)query.GetInt(4);

            return new RaceEnvDefine(id, raceTrackId, season, weather, timezone, resource);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithRaceTrackId.Clear();
        }

        public sealed partial class RaceEnvDefine
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: race_track_id) </summary>
            public readonly int RaceTrackId;
            /// <summary> (CSV column: season) </summary>
            public readonly int Season;
            /// <summary> (CSV column: weather) </summary>
            public readonly int Weather;
            /// <summary> (CSV column: timezone) </summary>
            public readonly int Timezone;
            /// <summary> (CSV column: resource) </summary>
            public readonly int Resource;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceEnvDefine(int id = 0, int raceTrackId = 0, int season = 0, int weather = 0, int timezone = 0, int resource = 0)
            {
                this.Id          = id;
                this.RaceTrackId = raceTrackId;
                this.Season      = season;
                this.Weather     = weather;
                this.Timezone    = timezone;
                this.Resource    = resource;
            }
        }
    }
}
#endif

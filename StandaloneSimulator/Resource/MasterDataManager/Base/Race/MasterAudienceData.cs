// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/audience_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterAudienceData : AbstractMasterData
    {
        public const string TABLE_NAME = "audience_data";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, AudienceData> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterAudienceData(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, AudienceData>();
            _db = db;
        }


        public AudienceData Get(int audienceId)
        {
            int key = (int)audienceId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterAudienceData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(audienceId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterAudienceData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private AudienceData _SelectOne(int audienceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_AudienceData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceData");
                return null;
            }

            // SELECT `chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`prop_id_1`,`prop_attach_node_name_type_1`,`prop_id_2`,`prop_attach_node_name_type_2`,`chara_height`,`shape`,`chara_bust_size`,`dress_id`,`dress_color_id` FROM `audience_data` WHERE `audience_id`=?;
            if (!query.BindInt(1, audienceId)) { return null; }

            AudienceData orm = null;

            if (query.Step())
            {
                int charaFaceModel          = (int)query.GetInt(0);
                int charaSkinColor          = (int)query.GetInt(1);
                int charaHairModel          = (int)query.GetInt(2);
                int charaHairColor          = (int)query.GetInt(3);
                int propId1                 = (int)query.GetInt(4);
                int propAttachNodeNameType1 = (int)query.GetInt(5);
                int propId2                 = (int)query.GetInt(6);
                int propAttachNodeNameType2 = (int)query.GetInt(7);
                int charaHeight             = (int)query.GetInt(8);
                int shape                   = (int)query.GetInt(9);
                int charaBustSize           = (int)query.GetInt(10);
                int dressId                 = (int)query.GetInt(11);
                int dressColorId            = (int)query.GetInt(12);

                orm = new AudienceData(audienceId, charaFaceModel, charaSkinColor, charaHairModel, charaHairColor, propId1, propAttachNodeNameType1, propId2, propAttachNodeNameType2, charaHeight, shape, charaBustSize, dressId, dressColorId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", audienceId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class AudienceData
        {
            /// <summary> (CSV column: audience_id) </summary>
            public readonly int AudienceId;
            /// <summary> (CSV column: chara_face_model) </summary>
            public readonly int CharaFaceModel;
            /// <summary> (CSV column: chara_skin_color) </summary>
            public readonly int CharaSkinColor;
            /// <summary> (CSV column: chara_hair_model) </summary>
            public readonly int CharaHairModel;
            /// <summary> (CSV column: chara_hair_color) </summary>
            public readonly int CharaHairColor;
            /// <summary> (CSV column: prop_id_1) </summary>
            public readonly int PropId1;
            /// <summary> (CSV column: prop_attach_node_name_type_1) </summary>
            public readonly int PropAttachNodeNameType1;
            /// <summary> (CSV column: prop_id_2) </summary>
            public readonly int PropId2;
            /// <summary> (CSV column: prop_attach_node_name_type_2) </summary>
            public readonly int PropAttachNodeNameType2;
            /// <summary> (CSV column: chara_height) </summary>
            public readonly int CharaHeight;
            /// <summary> (CSV column: shape) </summary>
            public readonly int Shape;
            /// <summary> (CSV column: chara_bust_size) </summary>
            public readonly int CharaBustSize;
            /// <summary> (CSV column: dress_id) </summary>
            public readonly int DressId;
            /// <summary> (CSV column: dress_color_id) </summary>
            public readonly int DressColorId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public AudienceData(int audienceId = 0, int charaFaceModel = 0, int charaSkinColor = 0, int charaHairModel = 0, int charaHairColor = 0, int propId1 = 0, int propAttachNodeNameType1 = 0, int propId2 = 0, int propAttachNodeNameType2 = 0, int charaHeight = 0, int shape = 0, int charaBustSize = 0, int dressId = 0, int dressColorId = 0)
            {
                this.AudienceId              = audienceId;
                this.CharaFaceModel          = charaFaceModel;
                this.CharaSkinColor          = charaSkinColor;
                this.CharaHairModel          = charaHairModel;
                this.CharaHairColor          = charaHairColor;
                this.PropId1                 = propId1;
                this.PropAttachNodeNameType1 = propAttachNodeNameType1;
                this.PropId2                 = propId2;
                this.PropAttachNodeNameType2 = propAttachNodeNameType2;
                this.CharaHeight             = charaHeight;
                this.Shape                   = shape;
                this.CharaBustSize           = charaBustSize;
                this.DressId                 = dressId;
                this.DressColorId            = dressColorId;
            }
        }
    }
}
#endif

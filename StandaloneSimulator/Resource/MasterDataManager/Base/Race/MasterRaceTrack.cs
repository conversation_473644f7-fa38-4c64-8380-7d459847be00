// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_track
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceTrack : AbstractMasterData
    {
        public const string TABLE_NAME = "race_track";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceTrack> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceTrack> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceTrack");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceTrack(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceTrack>();
            _db = db;
        }


        public RaceTrack Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceTrack");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceTrack", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceTrack _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceTrack();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceTrack");
                return null;
            }

            // SELECT `initial_lane_type`,`enable_half_gate`,`horse_num_gate_variation`,`turf_vision_type`,`footsmoke_color_type`,`area`,`flag_type`,`gate_panel_type`,`gate_lamp_type`,`board_condition_type`,`result_board_type` FROM `race_track` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceTrack orm = null;

            if (query.Step())
            {
                int initialLaneType       = (int)query.GetInt(0);
                int enableHalfGate        = (int)query.GetInt(1);
                int horseNumGateVariation = (int)query.GetInt(2);
                int turfVisionType        = (int)query.GetInt(3);
                int footsmokeColorType    = (int)query.GetInt(4);
                int area                  = (int)query.GetInt(5);
                int flagType              = (int)query.GetInt(6);
                int gatePanelType         = (int)query.GetInt(7);
                int gateLampType          = (int)query.GetInt(8);
                int boardConditionType    = (int)query.GetInt(9);
                int resultBoardType       = (int)query.GetInt(10);

                orm = new RaceTrack(id, initialLaneType, enableHalfGate, horseNumGateVariation, turfVisionType, footsmokeColorType, area, flagType, gatePanelType, gateLampType, boardConditionType, resultBoardType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceTrack()) {
                while (query.Step()) {
                    int id                    = (int)query.GetInt(0);
                    int initialLaneType       = (int)query.GetInt(1);
                    int enableHalfGate        = (int)query.GetInt(2);
                    int horseNumGateVariation = (int)query.GetInt(3);
                    int turfVisionType        = (int)query.GetInt(4);
                    int footsmokeColorType    = (int)query.GetInt(5);
                    int area                  = (int)query.GetInt(6);
                    int flagType              = (int)query.GetInt(7);
                    int gatePanelType         = (int)query.GetInt(8);
                    int gateLampType          = (int)query.GetInt(9);
                    int boardConditionType    = (int)query.GetInt(10);
                    int resultBoardType       = (int)query.GetInt(11);

                    int key = (int)id;
                    RaceTrack orm = new RaceTrack(id, initialLaneType, enableHalfGate, horseNumGateVariation, turfVisionType, footsmokeColorType, area, flagType, gatePanelType, gateLampType, boardConditionType, resultBoardType);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceTrack
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: initial_lane_type) </summary>
            public readonly int InitialLaneType;
            /// <summary> (CSV column: enable_half_gate) </summary>
            public readonly int EnableHalfGate;
            /// <summary> (CSV column: horse_num_gate_variation) </summary>
            public readonly int HorseNumGateVariation;
            /// <summary> (CSV column: turf_vision_type) </summary>
            public readonly int TurfVisionType;
            /// <summary> (CSV column: footsmoke_color_type) </summary>
            public readonly int FootsmokeColorType;
            /// <summary> (CSV column: area) </summary>
            public readonly int Area;
            /// <summary> (CSV column: flag_type) </summary>
            public readonly int FlagType;
            /// <summary> (CSV column: gate_panel_type) </summary>
            public readonly int GatePanelType;
            /// <summary> (CSV column: gate_lamp_type) </summary>
            public readonly int GateLampType;
            /// <summary> (CSV column: board_condition_type) </summary>
            public readonly int BoardConditionType;
            /// <summary> (CSV column: result_board_type) </summary>
            public readonly int ResultBoardType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceTrack(int id = 0, int initialLaneType = 0, int enableHalfGate = 0, int horseNumGateVariation = 0, int turfVisionType = 0, int footsmokeColorType = 0, int area = 0, int flagType = 0, int gatePanelType = 0, int gateLampType = 0, int boardConditionType = 0, int resultBoardType = 0)
            {
                this.Id                    = id;
                this.InitialLaneType       = initialLaneType;
                this.EnableHalfGate        = enableHalfGate;
                this.HorseNumGateVariation = horseNumGateVariation;
                this.TurfVisionType        = turfVisionType;
                this.FootsmokeColorType    = footsmokeColorType;
                this.Area                  = area;
                this.FlagType              = flagType;
                this.GatePanelType         = gatePanelType;
                this.GateLampType          = gateLampType;
                this.BoardConditionType    = boardConditionType;
                this.ResultBoardType       = resultBoardType;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_jikkyo_comment
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceJikkyoComment : AbstractMasterData
    {
        public const string TABLE_NAME = "race_jikkyo_comment";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceJikkyoComment> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceJikkyoComment>> _dictionaryWithGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceJikkyoComment> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceJikkyoComment");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceJikkyoComment(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceJikkyoComment>();
            _dictionaryWithGroupId = new Dictionary<int, List<RaceJikkyoComment>>();
            _db = db;
        }


        public RaceJikkyoComment Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceJikkyoComment");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceJikkyoComment", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoComment _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceJikkyoComment();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoComment");
                return null;
            }

            // SELECT `group_id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceJikkyoComment orm = null;

            if (query.Step())
            {
                int groupId      = (int)query.GetInt(0);
                string message   = query.GetText(1);
                string voice     = query.GetText(2);
                int per          = (int)query.GetInt(3);
                int messageGroup = (int)query.GetInt(4);
                int omitTag      = (int)query.GetInt(5);

                orm = new RaceJikkyoComment(id, groupId, message, voice, per, messageGroup, omitTag);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceJikkyoComment GetWithGroupId(int groupId)
        {
            RaceJikkyoComment orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupId(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoComment _SelectWithGroupId(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoComment_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoComment");
                return null;
            }

            // SELECT `id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            RaceJikkyoComment orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupId(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceJikkyoComment> GetListWithGroupId(int groupId)
        {
            int key = (int)groupId;

            if (!_dictionaryWithGroupId.ContainsKey(key)) {
                _dictionaryWithGroupId.Add(key, _ListSelectWithGroupId(groupId));
            }

            return _dictionaryWithGroupId[key];
        }

        public List<RaceJikkyoComment> MaybeListWithGroupId(int groupId)
        {
            List<RaceJikkyoComment> list = GetListWithGroupId(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceJikkyoComment> _ListSelectWithGroupId(int groupId)
        {
            List<RaceJikkyoComment> _list = new List<RaceJikkyoComment>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoComment_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoComment");
                return null;
            }

            // SELECT `id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                RaceJikkyoComment orm = _CreateOrmByQueryResultWithGroupId(query, groupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceJikkyoComment _CreateOrmByQueryResultWithGroupId(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id           = (int)query.GetInt(0);
            string message   = query.GetText(1);
            string voice     = query.GetText(2);
            int per          = (int)query.GetInt(3);
            int messageGroup = (int)query.GetInt(4);
            int omitTag      = (int)query.GetInt(5);

            return new RaceJikkyoComment(id, groupId, message, voice, per, messageGroup, omitTag);
        }

        public RaceJikkyoComment GetWithMessageGroup(int messageGroup)
        {
            RaceJikkyoComment orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithMessageGroup(messageGroup);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", messageGroup));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoComment _SelectWithMessageGroup(int messageGroup)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoComment_MessageGroup();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoComment");
                return null;
            }

            // SELECT `id`,`group_id`,`message`,`voice`,`per`,`omit_tag` FROM `race_jikkyo_comment` WHERE `message_group`=?;
            if (!query.BindInt(1, messageGroup)) { return null; }

            RaceJikkyoComment orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithMessageGroup(query, messageGroup);
            }

            query.Reset();

            return orm;
        }

        private RaceJikkyoComment _CreateOrmByQueryResultWithMessageGroup(LibNative.Sqlite3.PreparedQuery query, int messageGroup)
        {
            int id         = (int)query.GetInt(0);
            int groupId    = (int)query.GetInt(1);
            string message = query.GetText(2);
            string voice   = query.GetText(3);
            int per        = (int)query.GetInt(4);
            int omitTag    = (int)query.GetInt(5);

            return new RaceJikkyoComment(id, groupId, message, voice, per, messageGroup, omitTag);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceJikkyoComment()) {
                while (query.Step()) {
                    int id           = (int)query.GetInt(0);
                    int groupId      = (int)query.GetInt(1);
                    string message   = query.GetText(2);
                    string voice     = query.GetText(3);
                    int per          = (int)query.GetInt(4);
                    int messageGroup = (int)query.GetInt(5);
                    int omitTag      = (int)query.GetInt(6);

                    int key = (int)id;
                    RaceJikkyoComment orm = new RaceJikkyoComment(id, groupId, message, voice, per, messageGroup, omitTag);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceJikkyoComment
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: message) </summary>
            public readonly string Message;
            /// <summary> (CSV column: voice) </summary>
            public readonly string Voice;
            /// <summary> (CSV column: per) </summary>
            public readonly int Per;
            /// <summary> (CSV column: message_group) </summary>
            public readonly int MessageGroup;
            /// <summary> (CSV column: omit_tag) </summary>
            public readonly int OmitTag;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceJikkyoComment(int id = 0, int groupId = 0, string message = "", string voice = "", int per = 0, int messageGroup = 0, int omitTag = 0)
            {
                this.Id           = id;
                this.GroupId      = groupId;
                this.Message      = message;
                this.Voice        = voice;
                this.Per          = per;
                this.MessageGroup = messageGroup;
                this.OmitTag      = omitTag;
            }
        }
    }
}
#endif

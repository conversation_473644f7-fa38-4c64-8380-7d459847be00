// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/mob_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterMobData : AbstractMasterData
    {
        public const string TABLE_NAME = "mob_data";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, MobData> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, MobData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterMobData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterMobData(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, MobData>();
            _db = db;
        }


        public MobData Get(int mobId)
        {
            int key = (int)mobId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterMobData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(mobId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterMobData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private MobData _SelectOne(int mobId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_MobData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for MobData");
                return null;
            }

            // SELECT `chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`chara_height`,`chara_bust_size`,`socks`,`default_personality`,`race_personality`,`race_running_type`,`sex`,`dress_id`,`dress_color_id`,`use_live`,`hair_cutoff`,`attachment_model_id`,`capture_type`,`mayu_visible_type` FROM `mob_data` WHERE `mob_id`=?;
            if (!query.BindInt(1, mobId)) { return null; }

            MobData orm = null;

            if (query.Step())
            {
                int charaFaceModel     = (int)query.GetInt(0);
                int charaSkinColor     = (int)query.GetInt(1);
                int charaHairModel     = (int)query.GetInt(2);
                int charaHairColor     = (int)query.GetInt(3);
                int charaHeight        = (int)query.GetInt(4);
                int charaBustSize      = (int)query.GetInt(5);
                int socks              = (int)query.GetInt(6);
                int defaultPersonality = (int)query.GetInt(7);
                int racePersonality    = (int)query.GetInt(8);
                int raceRunningType    = (int)query.GetInt(9);
                int sex                = (int)query.GetInt(10);
                int dressId            = (int)query.GetInt(11);
                int dressColorId       = (int)query.GetInt(12);
                int useLive            = (int)query.GetInt(13);
                int hairCutoff         = (int)query.GetInt(14);
                int attachmentModelId  = (int)query.GetInt(15);
                int captureType        = (int)query.GetInt(16);
                int mayuVisibleType    = (int)query.GetInt(17);

                orm = new MobData(mobId, charaFaceModel, charaSkinColor, charaHairModel, charaHairColor, charaHeight, charaBustSize, socks, defaultPersonality, racePersonality, raceRunningType, sex, dressId, dressColorId, useLive, hairCutoff, attachmentModelId, captureType, mayuVisibleType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", mobId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_MobData()) {
                while (query.Step()) {
                    int mobId              = (int)query.GetInt(0);
                    int charaFaceModel     = (int)query.GetInt(1);
                    int charaSkinColor     = (int)query.GetInt(2);
                    int charaHairModel     = (int)query.GetInt(3);
                    int charaHairColor     = (int)query.GetInt(4);
                    int charaHeight        = (int)query.GetInt(5);
                    int charaBustSize      = (int)query.GetInt(6);
                    int socks              = (int)query.GetInt(7);
                    int defaultPersonality = (int)query.GetInt(8);
                    int racePersonality    = (int)query.GetInt(9);
                    int raceRunningType    = (int)query.GetInt(10);
                    int sex                = (int)query.GetInt(11);
                    int dressId            = (int)query.GetInt(12);
                    int dressColorId       = (int)query.GetInt(13);
                    int useLive            = (int)query.GetInt(14);
                    int hairCutoff         = (int)query.GetInt(15);
                    int attachmentModelId  = (int)query.GetInt(16);
                    int captureType        = (int)query.GetInt(17);
                    int mayuVisibleType    = (int)query.GetInt(18);

                    int key = (int)mobId;
                    MobData orm = new MobData(mobId, charaFaceModel, charaSkinColor, charaHairModel, charaHairColor, charaHeight, charaBustSize, socks, defaultPersonality, racePersonality, raceRunningType, sex, dressId, dressColorId, useLive, hairCutoff, attachmentModelId, captureType, mayuVisibleType);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class MobData
        {
            /// <summary> (CSV column: mob_id) </summary>
            public readonly int MobId;
            /// <summary> (CSV column: chara_face_model) </summary>
            public readonly int CharaFaceModel;
            /// <summary> (CSV column: chara_skin_color) </summary>
            public readonly int CharaSkinColor;
            /// <summary> (CSV column: chara_hair_model) </summary>
            public readonly int CharaHairModel;
            /// <summary> (CSV column: chara_hair_color) </summary>
            public readonly int CharaHairColor;
            /// <summary> (CSV column: chara_height) </summary>
            public readonly int CharaHeight;
            /// <summary> (CSV column: chara_bust_size) </summary>
            public readonly int CharaBustSize;
            /// <summary> (CSV column: socks) </summary>
            public readonly int Socks;
            /// <summary> (CSV column: default_personality) </summary>
            public readonly int DefaultPersonality;
            /// <summary> (CSV column: race_personality) </summary>
            public readonly int RacePersonality;
            /// <summary> (CSV column: race_running_type) </summary>
            public readonly int RaceRunningType;
            /// <summary> (CSV column: sex) </summary>
            public readonly int Sex;
            /// <summary> (CSV column: dress_id) </summary>
            public readonly int DressId;
            /// <summary> (CSV column: dress_color_id) </summary>
            public readonly int DressColorId;
            /// <summary> (CSV column: use_live) </summary>
            public readonly int UseLive;
            /// <summary> (CSV column: hair_cutoff) </summary>
            public readonly int HairCutoff;
            /// <summary> (CSV column: attachment_model_id) </summary>
            public readonly int AttachmentModelId;
            /// <summary> (CSV column: capture_type) </summary>
            public readonly int CaptureType;
            /// <summary> (CSV column: mayu_visible_type) </summary>
            public readonly int MayuVisibleType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public MobData(int mobId = 0, int charaFaceModel = 0, int charaSkinColor = 0, int charaHairModel = 0, int charaHairColor = 0, int charaHeight = 0, int charaBustSize = 0, int socks = 0, int defaultPersonality = 0, int racePersonality = 0, int raceRunningType = 0, int sex = 0, int dressId = 0, int dressColorId = 0, int useLive = 0, int hairCutoff = 0, int attachmentModelId = 0, int captureType = 0, int mayuVisibleType = 0)
            {
                this.MobId              = mobId;
                this.CharaFaceModel     = charaFaceModel;
                this.CharaSkinColor     = charaSkinColor;
                this.CharaHairModel     = charaHairModel;
                this.CharaHairColor     = charaHairColor;
                this.CharaHeight        = charaHeight;
                this.CharaBustSize      = charaBustSize;
                this.Socks              = socks;
                this.DefaultPersonality = defaultPersonality;
                this.RacePersonality    = racePersonality;
                this.RaceRunningType    = raceRunningType;
                this.Sex                = sex;
                this.DressId            = dressId;
                this.DressColorId       = dressColorId;
                this.UseLive            = useLive;
                this.HairCutoff         = hairCutoff;
                this.AttachmentModelId  = attachmentModelId;
                this.CaptureType        = captureType;
                this.MayuVisibleType    = mayuVisibleType;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_course_set_status
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceCourseSetStatus : AbstractMasterData
    {
        public const string TABLE_NAME = "race_course_set_status";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceCourseSetStatus> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceCourseSetStatus> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceCourseSetStatus");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceCourseSetStatus(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceCourseSetStatus>();
            _db = db;
        }


        public RaceCourseSetStatus Get(int courseSetStatusId)
        {
            int key = (int)courseSetStatusId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceCourseSetStatus");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(courseSetStatusId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceCourseSetStatus", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceCourseSetStatus _SelectOne(int courseSetStatusId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceCourseSetStatus();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCourseSetStatus");
                return null;
            }

            // SELECT `target_status_1`,`target_status_2` FROM `race_course_set_status` WHERE `course_set_status_id`=?;
            if (!query.BindInt(1, courseSetStatusId)) { return null; }

            RaceCourseSetStatus orm = null;

            if (query.Step())
            {
                int targetStatus1 = (int)query.GetInt(0);
                int targetStatus2 = (int)query.GetInt(1);

                orm = new RaceCourseSetStatus(courseSetStatusId, targetStatus1, targetStatus2);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", courseSetStatusId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceCourseSetStatus()) {
                while (query.Step()) {
                    int courseSetStatusId = (int)query.GetInt(0);
                    int targetStatus1     = (int)query.GetInt(1);
                    int targetStatus2     = (int)query.GetInt(2);

                    int key = (int)courseSetStatusId;
                    RaceCourseSetStatus orm = new RaceCourseSetStatus(courseSetStatusId, targetStatus1, targetStatus2);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceCourseSetStatus
        {
            /// <summary> (CSV column: course_set_status_id) </summary>
            public readonly int CourseSetStatusId;
            /// <summary> (CSV column: target_status_1) </summary>
            public readonly int TargetStatus1;
            /// <summary> (CSV column: target_status_2) </summary>
            public readonly int TargetStatus2;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceCourseSetStatus(int courseSetStatusId = 0, int targetStatus1 = 0, int targetStatus2 = 0)
            {
                this.CourseSetStatusId = courseSetStatusId;
                this.TargetStatus1     = targetStatus1;
                this.TargetStatus2     = targetStatus2;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_player_camera
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:category]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRacePlayerCamera : AbstractMasterData
    {
        public const string TABLE_NAME = "race_player_camera";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RacePlayerCamera> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RacePlayerCamera>> _dictionaryWithCategory = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RacePlayerCamera> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRacePlayerCamera");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRacePlayerCamera(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RacePlayerCamera>();
            _dictionaryWithCategory = new Dictionary<int, List<RacePlayerCamera>>();
            _db = db;
        }


        public RacePlayerCamera Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRacePlayerCamera");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRacePlayerCamera", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RacePlayerCamera _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RacePlayerCamera();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RacePlayerCamera");
                return null;
            }

            // SELECT `priority`,`prefab_name`,`category` FROM `race_player_camera` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RacePlayerCamera orm = null;

            if (query.Step())
            {
                int priority      = (int)query.GetInt(0);
                string prefabName = query.GetText(1);
                int category      = (int)query.GetInt(2);

                orm = new RacePlayerCamera(id, priority, prefabName, category);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RacePlayerCamera GetWithPrefabName(string prefabName)
        {
            RacePlayerCamera orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithPrefabName(prefabName);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", prefabName));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RacePlayerCamera _SelectWithPrefabName(string prefabName)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RacePlayerCamera_PrefabName();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RacePlayerCamera");
                return null;
            }

            // SELECT `id`,`priority`,`category` FROM `race_player_camera` WHERE `prefab_name`=?;
            if (!query.BindText(1, prefabName)) { return null; }

            RacePlayerCamera orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithPrefabName(query, prefabName);
            }

            query.Reset();

            return orm;
        }

        private RacePlayerCamera _CreateOrmByQueryResultWithPrefabName(LibNative.Sqlite3.PreparedQuery query, string prefabName)
        {
            int id       = (int)query.GetInt(0);
            int priority = (int)query.GetInt(1);
            int category = (int)query.GetInt(2);

            return new RacePlayerCamera(id, priority, prefabName, category);
        }

        public RacePlayerCamera GetWithCategoryOrderByIdAsc(int category)
        {
            RacePlayerCamera orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCategoryOrderByIdAsc(category);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", category));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RacePlayerCamera _SelectWithCategoryOrderByIdAsc(int category)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RacePlayerCamera_Category();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RacePlayerCamera");
                return null;
            }

            // SELECT `id`,`priority`,`prefab_name` FROM `race_player_camera` WHERE `category`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, category)) { return null; }

            RacePlayerCamera orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCategoryOrderByIdAsc(query, category);
            }

            query.Reset();

            return orm;
        }

        public List<RacePlayerCamera> GetListWithCategoryOrderByIdAsc(int category)
        {
            int key = (int)category;

            if (!_dictionaryWithCategory.ContainsKey(key)) {
                _dictionaryWithCategory.Add(key, _ListSelectWithCategoryOrderByIdAsc(category));
            }

            return _dictionaryWithCategory[key];
        }

        public List<RacePlayerCamera> MaybeListWithCategoryOrderByIdAsc(int category)
        {
            List<RacePlayerCamera> list = GetListWithCategoryOrderByIdAsc(category);
            return list.Count > 0 ? list : null;
        }

        private List<RacePlayerCamera> _ListSelectWithCategoryOrderByIdAsc(int category)
        {
            List<RacePlayerCamera> _list = new List<RacePlayerCamera>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RacePlayerCamera_Category();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RacePlayerCamera");
                return null;
            }

            // SELECT `id`,`priority`,`prefab_name` FROM `race_player_camera` WHERE `category`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, category)) { return null; }

            while (query.Step()) {
                RacePlayerCamera orm = _CreateOrmByQueryResultWithCategoryOrderByIdAsc(query, category);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RacePlayerCamera _CreateOrmByQueryResultWithCategoryOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int category)
        {
            int id            = (int)query.GetInt(0);
            int priority      = (int)query.GetInt(1);
            string prefabName = query.GetText(2);

            return new RacePlayerCamera(id, priority, prefabName, category);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCategory.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RacePlayerCamera()) {
                while (query.Step()) {
                    int id            = (int)query.GetInt(0);
                    int priority      = (int)query.GetInt(1);
                    string prefabName = query.GetText(2);
                    int category      = (int)query.GetInt(3);

                    int key = (int)id;
                    RacePlayerCamera orm = new RacePlayerCamera(id, priority, prefabName, category);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RacePlayerCamera
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: prefab_name) </summary>
            private readonly string _prefabName;
            /// <summary> (CSV column: category) </summary>
            public readonly int Category;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RacePlayerCamera(int id = 0, int priority = 0, string prefabName = "", int category = 0)
            {
                this.Id          = id;
                this.Priority    = priority;
                this._prefabName = prefabName;
                this.Category    = category;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_motivation_rate
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceMotivationRate : AbstractMasterData
    {
        public const string TABLE_NAME = "race_motivation_rate";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceMotivationRate> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceMotivationRate> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceMotivationRate");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceMotivationRate(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceMotivationRate>();
            _db = db;
        }


        public RaceMotivationRate Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceMotivationRate");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceMotivationRate", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceMotivationRate _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceMotivationRate();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceMotivationRate");
                return null;
            }

            // SELECT `motivation_rate` FROM `race_motivation_rate` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceMotivationRate orm = null;

            if (query.Step())
            {
                int motivationRate = (int)query.GetInt(0);

                orm = new RaceMotivationRate(id, motivationRate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceMotivationRate()) {
                while (query.Step()) {
                    int id             = (int)query.GetInt(0);
                    int motivationRate = (int)query.GetInt(1);

                    int key = (int)id;
                    RaceMotivationRate orm = new RaceMotivationRate(id, motivationRate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceMotivationRate
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: motivation_rate) </summary>
            public readonly int MotivationRate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceMotivationRate(int id = 0, int motivationRate = 0)
            {
                this.Id             = id;
                this.MotivationRate = motivationRate;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_bgm_pattern
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceBgmPattern : AbstractMasterData
    {
        public const string TABLE_NAME = "race_bgm_pattern";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceBgmPattern> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceBgmPattern(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceBgmPattern>();
            _db = db;
        }


        public RaceBgmPattern Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceBgmPattern");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceBgmPattern", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceBgmPattern _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceBgmPattern();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgmPattern");
                return null;
            }

            // SELECT `bgm_time_1`,`bgm_cue_name_1`,`bgm_cuesheet_name_1`,`bgm_time_2`,`bgm_cue_name_2`,`bgm_cuesheet_name_2`,`bgm_time_3`,`bgm_cue_name_3`,`bgm_cuesheet_name_3`,`bgm_time_4`,`bgm_cue_name_4`,`bgm_cuesheet_name_4`,`bgm_time_5`,`bgm_cue_name_5`,`bgm_cuesheet_name_5`,`bgm_time_6`,`bgm_cue_name_6`,`bgm_cuesheet_name_6`,`bgm_time_7`,`bgm_cue_name_7`,`bgm_cuesheet_name_7`,`bgm_time_8`,`bgm_cue_name_8`,`bgm_cuesheet_name_8`,`bgm_time_9`,`bgm_cue_name_9`,`bgm_cuesheet_name_9`,`bgm_time_10`,`bgm_cue_name_10`,`bgm_cuesheet_name_10`,`bgm_time_11`,`bgm_cue_name_11`,`bgm_cuesheet_name_11`,`bgm_time_12`,`bgm_cue_name_12`,`bgm_cuesheet_name_12`,`bgm_time_13`,`bgm_cue_name_13`,`bgm_cuesheet_name_13`,`bgm_time_14`,`bgm_cue_name_14`,`bgm_cuesheet_name_14`,`bgm_time_15`,`bgm_cue_name_15`,`bgm_cuesheet_name_15`,`bgm_time_16`,`bgm_cue_name_16`,`bgm_cuesheet_name_16`,`bgm_time_17`,`bgm_cue_name_17`,`bgm_cuesheet_name_17`,`bgm_time_18`,`bgm_cue_name_18`,`bgm_cuesheet_name_18`,`bgm_time_19`,`bgm_cue_name_19`,`bgm_cuesheet_name_19`,`bgm_time_20`,`bgm_cue_name_20`,`bgm_cuesheet_name_20`,`bgm_time_21`,`bgm_cue_name_21`,`bgm_cuesheet_name_21`,`bgm_time_22`,`bgm_cue_name_22`,`bgm_cuesheet_name_22`,`bgm_time_23`,`bgm_cue_name_23`,`bgm_cuesheet_name_23`,`bgm_time_24`,`bgm_cue_name_24`,`bgm_cuesheet_name_24`,`bgm_time_25`,`bgm_cue_name_25`,`bgm_cuesheet_name_25`,`bgm_time_26`,`bgm_cue_name_26`,`bgm_cuesheet_name_26`,`bgm_time_27`,`bgm_cue_name_27`,`bgm_cuesheet_name_27`,`bgm_time_28`,`bgm_cue_name_28`,`bgm_cuesheet_name_28`,`bgm_time_29`,`bgm_cue_name_29`,`bgm_cuesheet_name_29`,`bgm_time_30`,`bgm_cue_name_30`,`bgm_cuesheet_name_30`,`bgm_trigger_time_ago`,`bgm_trigger_cue_name`,`bgm_trigger_cuesheet_name`,`bgm_skip_trigger_cue_name`,`bgm_skip_trigger_cuesheet_name` FROM `race_bgm_pattern` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceBgmPattern orm = null;

            if (query.Step())
            {
                int bgmTime1                      = (int)query.GetInt(0);
                string bgmCueName1                = query.GetText(1);
                string bgmCuesheetName1           = query.GetText(2);
                int bgmTime2                      = (int)query.GetInt(3);
                string bgmCueName2                = query.GetText(4);
                string bgmCuesheetName2           = query.GetText(5);
                int bgmTime3                      = (int)query.GetInt(6);
                string bgmCueName3                = query.GetText(7);
                string bgmCuesheetName3           = query.GetText(8);
                int bgmTime4                      = (int)query.GetInt(9);
                string bgmCueName4                = query.GetText(10);
                string bgmCuesheetName4           = query.GetText(11);
                int bgmTime5                      = (int)query.GetInt(12);
                string bgmCueName5                = query.GetText(13);
                string bgmCuesheetName5           = query.GetText(14);
                int bgmTime6                      = (int)query.GetInt(15);
                string bgmCueName6                = query.GetText(16);
                string bgmCuesheetName6           = query.GetText(17);
                int bgmTime7                      = (int)query.GetInt(18);
                string bgmCueName7                = query.GetText(19);
                string bgmCuesheetName7           = query.GetText(20);
                int bgmTime8                      = (int)query.GetInt(21);
                string bgmCueName8                = query.GetText(22);
                string bgmCuesheetName8           = query.GetText(23);
                int bgmTime9                      = (int)query.GetInt(24);
                string bgmCueName9                = query.GetText(25);
                string bgmCuesheetName9           = query.GetText(26);
                int bgmTime10                     = (int)query.GetInt(27);
                string bgmCueName10               = query.GetText(28);
                string bgmCuesheetName10          = query.GetText(29);
                int bgmTime11                     = (int)query.GetInt(30);
                string bgmCueName11               = query.GetText(31);
                string bgmCuesheetName11          = query.GetText(32);
                int bgmTime12                     = (int)query.GetInt(33);
                string bgmCueName12               = query.GetText(34);
                string bgmCuesheetName12          = query.GetText(35);
                int bgmTime13                     = (int)query.GetInt(36);
                string bgmCueName13               = query.GetText(37);
                string bgmCuesheetName13          = query.GetText(38);
                int bgmTime14                     = (int)query.GetInt(39);
                string bgmCueName14               = query.GetText(40);
                string bgmCuesheetName14          = query.GetText(41);
                int bgmTime15                     = (int)query.GetInt(42);
                string bgmCueName15               = query.GetText(43);
                string bgmCuesheetName15          = query.GetText(44);
                int bgmTime16                     = (int)query.GetInt(45);
                string bgmCueName16               = query.GetText(46);
                string bgmCuesheetName16          = query.GetText(47);
                int bgmTime17                     = (int)query.GetInt(48);
                string bgmCueName17               = query.GetText(49);
                string bgmCuesheetName17          = query.GetText(50);
                int bgmTime18                     = (int)query.GetInt(51);
                string bgmCueName18               = query.GetText(52);
                string bgmCuesheetName18          = query.GetText(53);
                int bgmTime19                     = (int)query.GetInt(54);
                string bgmCueName19               = query.GetText(55);
                string bgmCuesheetName19          = query.GetText(56);
                int bgmTime20                     = (int)query.GetInt(57);
                string bgmCueName20               = query.GetText(58);
                string bgmCuesheetName20          = query.GetText(59);
                int bgmTime21                     = (int)query.GetInt(60);
                string bgmCueName21               = query.GetText(61);
                string bgmCuesheetName21          = query.GetText(62);
                int bgmTime22                     = (int)query.GetInt(63);
                string bgmCueName22               = query.GetText(64);
                string bgmCuesheetName22          = query.GetText(65);
                int bgmTime23                     = (int)query.GetInt(66);
                string bgmCueName23               = query.GetText(67);
                string bgmCuesheetName23          = query.GetText(68);
                int bgmTime24                     = (int)query.GetInt(69);
                string bgmCueName24               = query.GetText(70);
                string bgmCuesheetName24          = query.GetText(71);
                int bgmTime25                     = (int)query.GetInt(72);
                string bgmCueName25               = query.GetText(73);
                string bgmCuesheetName25          = query.GetText(74);
                int bgmTime26                     = (int)query.GetInt(75);
                string bgmCueName26               = query.GetText(76);
                string bgmCuesheetName26          = query.GetText(77);
                int bgmTime27                     = (int)query.GetInt(78);
                string bgmCueName27               = query.GetText(79);
                string bgmCuesheetName27          = query.GetText(80);
                int bgmTime28                     = (int)query.GetInt(81);
                string bgmCueName28               = query.GetText(82);
                string bgmCuesheetName28          = query.GetText(83);
                int bgmTime29                     = (int)query.GetInt(84);
                string bgmCueName29               = query.GetText(85);
                string bgmCuesheetName29          = query.GetText(86);
                int bgmTime30                     = (int)query.GetInt(87);
                string bgmCueName30               = query.GetText(88);
                string bgmCuesheetName30          = query.GetText(89);
                int bgmTriggerTimeAgo             = (int)query.GetInt(90);
                string bgmTriggerCueName          = query.GetText(91);
                string bgmTriggerCuesheetName     = query.GetText(92);
                string bgmSkipTriggerCueName      = query.GetText(93);
                string bgmSkipTriggerCuesheetName = query.GetText(94);

                orm = new RaceBgmPattern(id, bgmTime1, bgmCueName1, bgmCuesheetName1, bgmTime2, bgmCueName2, bgmCuesheetName2, bgmTime3, bgmCueName3, bgmCuesheetName3, bgmTime4, bgmCueName4, bgmCuesheetName4, bgmTime5, bgmCueName5, bgmCuesheetName5, bgmTime6, bgmCueName6, bgmCuesheetName6, bgmTime7, bgmCueName7, bgmCuesheetName7, bgmTime8, bgmCueName8, bgmCuesheetName8, bgmTime9, bgmCueName9, bgmCuesheetName9, bgmTime10, bgmCueName10, bgmCuesheetName10, bgmTime11, bgmCueName11, bgmCuesheetName11, bgmTime12, bgmCueName12, bgmCuesheetName12, bgmTime13, bgmCueName13, bgmCuesheetName13, bgmTime14, bgmCueName14, bgmCuesheetName14, bgmTime15, bgmCueName15, bgmCuesheetName15, bgmTime16, bgmCueName16, bgmCuesheetName16, bgmTime17, bgmCueName17, bgmCuesheetName17, bgmTime18, bgmCueName18, bgmCuesheetName18, bgmTime19, bgmCueName19, bgmCuesheetName19, bgmTime20, bgmCueName20, bgmCuesheetName20, bgmTime21, bgmCueName21, bgmCuesheetName21, bgmTime22, bgmCueName22, bgmCuesheetName22, bgmTime23, bgmCueName23, bgmCuesheetName23, bgmTime24, bgmCueName24, bgmCuesheetName24, bgmTime25, bgmCueName25, bgmCuesheetName25, bgmTime26, bgmCueName26, bgmCuesheetName26, bgmTime27, bgmCueName27, bgmCuesheetName27, bgmTime28, bgmCueName28, bgmCuesheetName28, bgmTime29, bgmCueName29, bgmCuesheetName29, bgmTime30, bgmCueName30, bgmCuesheetName30, bgmTriggerTimeAgo, bgmTriggerCueName, bgmTriggerCuesheetName, bgmSkipTriggerCueName, bgmSkipTriggerCuesheetName);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class RaceBgmPattern
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: bgm_time_1) </summary>
            public readonly int BgmTime1;
            /// <summary> (CSV column: bgm_cue_name_1) </summary>
            public readonly string BgmCueName1;
            /// <summary> (CSV column: bgm_cuesheet_name_1) </summary>
            public readonly string BgmCuesheetName1;
            /// <summary> (CSV column: bgm_time_2) </summary>
            public readonly int BgmTime2;
            /// <summary> (CSV column: bgm_cue_name_2) </summary>
            public readonly string BgmCueName2;
            /// <summary> (CSV column: bgm_cuesheet_name_2) </summary>
            public readonly string BgmCuesheetName2;
            /// <summary> (CSV column: bgm_time_3) </summary>
            public readonly int BgmTime3;
            /// <summary> (CSV column: bgm_cue_name_3) </summary>
            public readonly string BgmCueName3;
            /// <summary> (CSV column: bgm_cuesheet_name_3) </summary>
            public readonly string BgmCuesheetName3;
            /// <summary> (CSV column: bgm_time_4) </summary>
            public readonly int BgmTime4;
            /// <summary> (CSV column: bgm_cue_name_4) </summary>
            public readonly string BgmCueName4;
            /// <summary> (CSV column: bgm_cuesheet_name_4) </summary>
            public readonly string BgmCuesheetName4;
            /// <summary> (CSV column: bgm_time_5) </summary>
            public readonly int BgmTime5;
            /// <summary> (CSV column: bgm_cue_name_5) </summary>
            public readonly string BgmCueName5;
            /// <summary> (CSV column: bgm_cuesheet_name_5) </summary>
            public readonly string BgmCuesheetName5;
            /// <summary> (CSV column: bgm_time_6) </summary>
            public readonly int BgmTime6;
            /// <summary> (CSV column: bgm_cue_name_6) </summary>
            public readonly string BgmCueName6;
            /// <summary> (CSV column: bgm_cuesheet_name_6) </summary>
            public readonly string BgmCuesheetName6;
            /// <summary> (CSV column: bgm_time_7) </summary>
            public readonly int BgmTime7;
            /// <summary> (CSV column: bgm_cue_name_7) </summary>
            public readonly string BgmCueName7;
            /// <summary> (CSV column: bgm_cuesheet_name_7) </summary>
            public readonly string BgmCuesheetName7;
            /// <summary> (CSV column: bgm_time_8) </summary>
            public readonly int BgmTime8;
            /// <summary> (CSV column: bgm_cue_name_8) </summary>
            public readonly string BgmCueName8;
            /// <summary> (CSV column: bgm_cuesheet_name_8) </summary>
            public readonly string BgmCuesheetName8;
            /// <summary> (CSV column: bgm_time_9) </summary>
            public readonly int BgmTime9;
            /// <summary> (CSV column: bgm_cue_name_9) </summary>
            public readonly string BgmCueName9;
            /// <summary> (CSV column: bgm_cuesheet_name_9) </summary>
            public readonly string BgmCuesheetName9;
            /// <summary> (CSV column: bgm_time_10) </summary>
            public readonly int BgmTime10;
            /// <summary> (CSV column: bgm_cue_name_10) </summary>
            public readonly string BgmCueName10;
            /// <summary> (CSV column: bgm_cuesheet_name_10) </summary>
            public readonly string BgmCuesheetName10;
            /// <summary> (CSV column: bgm_time_11) </summary>
            public readonly int BgmTime11;
            /// <summary> (CSV column: bgm_cue_name_11) </summary>
            public readonly string BgmCueName11;
            /// <summary> (CSV column: bgm_cuesheet_name_11) </summary>
            public readonly string BgmCuesheetName11;
            /// <summary> (CSV column: bgm_time_12) </summary>
            public readonly int BgmTime12;
            /// <summary> (CSV column: bgm_cue_name_12) </summary>
            public readonly string BgmCueName12;
            /// <summary> (CSV column: bgm_cuesheet_name_12) </summary>
            public readonly string BgmCuesheetName12;
            /// <summary> (CSV column: bgm_time_13) </summary>
            public readonly int BgmTime13;
            /// <summary> (CSV column: bgm_cue_name_13) </summary>
            public readonly string BgmCueName13;
            /// <summary> (CSV column: bgm_cuesheet_name_13) </summary>
            public readonly string BgmCuesheetName13;
            /// <summary> (CSV column: bgm_time_14) </summary>
            public readonly int BgmTime14;
            /// <summary> (CSV column: bgm_cue_name_14) </summary>
            public readonly string BgmCueName14;
            /// <summary> (CSV column: bgm_cuesheet_name_14) </summary>
            public readonly string BgmCuesheetName14;
            /// <summary> (CSV column: bgm_time_15) </summary>
            public readonly int BgmTime15;
            /// <summary> (CSV column: bgm_cue_name_15) </summary>
            public readonly string BgmCueName15;
            /// <summary> (CSV column: bgm_cuesheet_name_15) </summary>
            public readonly string BgmCuesheetName15;
            /// <summary> (CSV column: bgm_time_16) </summary>
            public readonly int BgmTime16;
            /// <summary> (CSV column: bgm_cue_name_16) </summary>
            public readonly string BgmCueName16;
            /// <summary> (CSV column: bgm_cuesheet_name_16) </summary>
            public readonly string BgmCuesheetName16;
            /// <summary> (CSV column: bgm_time_17) </summary>
            public readonly int BgmTime17;
            /// <summary> (CSV column: bgm_cue_name_17) </summary>
            public readonly string BgmCueName17;
            /// <summary> (CSV column: bgm_cuesheet_name_17) </summary>
            public readonly string BgmCuesheetName17;
            /// <summary> (CSV column: bgm_time_18) </summary>
            public readonly int BgmTime18;
            /// <summary> (CSV column: bgm_cue_name_18) </summary>
            public readonly string BgmCueName18;
            /// <summary> (CSV column: bgm_cuesheet_name_18) </summary>
            public readonly string BgmCuesheetName18;
            /// <summary> (CSV column: bgm_time_19) </summary>
            public readonly int BgmTime19;
            /// <summary> (CSV column: bgm_cue_name_19) </summary>
            public readonly string BgmCueName19;
            /// <summary> (CSV column: bgm_cuesheet_name_19) </summary>
            public readonly string BgmCuesheetName19;
            /// <summary> (CSV column: bgm_time_20) </summary>
            public readonly int BgmTime20;
            /// <summary> (CSV column: bgm_cue_name_20) </summary>
            public readonly string BgmCueName20;
            /// <summary> (CSV column: bgm_cuesheet_name_20) </summary>
            public readonly string BgmCuesheetName20;
            /// <summary> (CSV column: bgm_time_21) </summary>
            public readonly int BgmTime21;
            /// <summary> (CSV column: bgm_cue_name_21) </summary>
            public readonly string BgmCueName21;
            /// <summary> (CSV column: bgm_cuesheet_name_21) </summary>
            public readonly string BgmCuesheetName21;
            /// <summary> (CSV column: bgm_time_22) </summary>
            public readonly int BgmTime22;
            /// <summary> (CSV column: bgm_cue_name_22) </summary>
            public readonly string BgmCueName22;
            /// <summary> (CSV column: bgm_cuesheet_name_22) </summary>
            public readonly string BgmCuesheetName22;
            /// <summary> (CSV column: bgm_time_23) </summary>
            public readonly int BgmTime23;
            /// <summary> (CSV column: bgm_cue_name_23) </summary>
            public readonly string BgmCueName23;
            /// <summary> (CSV column: bgm_cuesheet_name_23) </summary>
            public readonly string BgmCuesheetName23;
            /// <summary> (CSV column: bgm_time_24) </summary>
            public readonly int BgmTime24;
            /// <summary> (CSV column: bgm_cue_name_24) </summary>
            public readonly string BgmCueName24;
            /// <summary> (CSV column: bgm_cuesheet_name_24) </summary>
            public readonly string BgmCuesheetName24;
            /// <summary> (CSV column: bgm_time_25) </summary>
            public readonly int BgmTime25;
            /// <summary> (CSV column: bgm_cue_name_25) </summary>
            public readonly string BgmCueName25;
            /// <summary> (CSV column: bgm_cuesheet_name_25) </summary>
            public readonly string BgmCuesheetName25;
            /// <summary> (CSV column: bgm_time_26) </summary>
            public readonly int BgmTime26;
            /// <summary> (CSV column: bgm_cue_name_26) </summary>
            public readonly string BgmCueName26;
            /// <summary> (CSV column: bgm_cuesheet_name_26) </summary>
            public readonly string BgmCuesheetName26;
            /// <summary> (CSV column: bgm_time_27) </summary>
            public readonly int BgmTime27;
            /// <summary> (CSV column: bgm_cue_name_27) </summary>
            public readonly string BgmCueName27;
            /// <summary> (CSV column: bgm_cuesheet_name_27) </summary>
            public readonly string BgmCuesheetName27;
            /// <summary> (CSV column: bgm_time_28) </summary>
            public readonly int BgmTime28;
            /// <summary> (CSV column: bgm_cue_name_28) </summary>
            public readonly string BgmCueName28;
            /// <summary> (CSV column: bgm_cuesheet_name_28) </summary>
            public readonly string BgmCuesheetName28;
            /// <summary> (CSV column: bgm_time_29) </summary>
            public readonly int BgmTime29;
            /// <summary> (CSV column: bgm_cue_name_29) </summary>
            public readonly string BgmCueName29;
            /// <summary> (CSV column: bgm_cuesheet_name_29) </summary>
            public readonly string BgmCuesheetName29;
            /// <summary> (CSV column: bgm_time_30) </summary>
            public readonly int BgmTime30;
            /// <summary> (CSV column: bgm_cue_name_30) </summary>
            public readonly string BgmCueName30;
            /// <summary> (CSV column: bgm_cuesheet_name_30) </summary>
            public readonly string BgmCuesheetName30;
            /// <summary> (CSV column: bgm_trigger_time_ago) </summary>
            public readonly int BgmTriggerTimeAgo;
            /// <summary> (CSV column: bgm_trigger_cue_name) </summary>
            public readonly string BgmTriggerCueName;
            /// <summary> (CSV column: bgm_trigger_cuesheet_name) </summary>
            public readonly string BgmTriggerCuesheetName;
            /// <summary> (CSV column: bgm_skip_trigger_cue_name) </summary>
            public readonly string BgmSkipTriggerCueName;
            /// <summary> (CSV column: bgm_skip_trigger_cuesheet_name) </summary>
            public readonly string BgmSkipTriggerCuesheetName;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceBgmPattern(int id = 0, int bgmTime1 = 0, string bgmCueName1 = "", string bgmCuesheetName1 = "", int bgmTime2 = 0, string bgmCueName2 = "", string bgmCuesheetName2 = "", int bgmTime3 = 0, string bgmCueName3 = "", string bgmCuesheetName3 = "", int bgmTime4 = 0, string bgmCueName4 = "", string bgmCuesheetName4 = "", int bgmTime5 = 0, string bgmCueName5 = "", string bgmCuesheetName5 = "", int bgmTime6 = 0, string bgmCueName6 = "", string bgmCuesheetName6 = "", int bgmTime7 = 0, string bgmCueName7 = "", string bgmCuesheetName7 = "", int bgmTime8 = 0, string bgmCueName8 = "", string bgmCuesheetName8 = "", int bgmTime9 = 0, string bgmCueName9 = "", string bgmCuesheetName9 = "", int bgmTime10 = 0, string bgmCueName10 = "", string bgmCuesheetName10 = "", int bgmTime11 = 0, string bgmCueName11 = "", string bgmCuesheetName11 = "", int bgmTime12 = 0, string bgmCueName12 = "", string bgmCuesheetName12 = "", int bgmTime13 = 0, string bgmCueName13 = "", string bgmCuesheetName13 = "", int bgmTime14 = 0, string bgmCueName14 = "", string bgmCuesheetName14 = "", int bgmTime15 = 0, string bgmCueName15 = "", string bgmCuesheetName15 = "", int bgmTime16 = 0, string bgmCueName16 = "", string bgmCuesheetName16 = "", int bgmTime17 = 0, string bgmCueName17 = "", string bgmCuesheetName17 = "", int bgmTime18 = 0, string bgmCueName18 = "", string bgmCuesheetName18 = "", int bgmTime19 = 0, string bgmCueName19 = "", string bgmCuesheetName19 = "", int bgmTime20 = 0, string bgmCueName20 = "", string bgmCuesheetName20 = "", int bgmTime21 = 0, string bgmCueName21 = "", string bgmCuesheetName21 = "", int bgmTime22 = 0, string bgmCueName22 = "", string bgmCuesheetName22 = "", int bgmTime23 = 0, string bgmCueName23 = "", string bgmCuesheetName23 = "", int bgmTime24 = 0, string bgmCueName24 = "", string bgmCuesheetName24 = "", int bgmTime25 = 0, string bgmCueName25 = "", string bgmCuesheetName25 = "", int bgmTime26 = 0, string bgmCueName26 = "", string bgmCuesheetName26 = "", int bgmTime27 = 0, string bgmCueName27 = "", string bgmCuesheetName27 = "", int bgmTime28 = 0, string bgmCueName28 = "", string bgmCuesheetName28 = "", int bgmTime29 = 0, string bgmCueName29 = "", string bgmCuesheetName29 = "", int bgmTime30 = 0, string bgmCueName30 = "", string bgmCuesheetName30 = "", int bgmTriggerTimeAgo = 0, string bgmTriggerCueName = "", string bgmTriggerCuesheetName = "", string bgmSkipTriggerCueName = "", string bgmSkipTriggerCuesheetName = "")
            {
                this.Id                         = id;
                this.BgmTime1                   = bgmTime1;
                this.BgmCueName1                = bgmCueName1;
                this.BgmCuesheetName1           = bgmCuesheetName1;
                this.BgmTime2                   = bgmTime2;
                this.BgmCueName2                = bgmCueName2;
                this.BgmCuesheetName2           = bgmCuesheetName2;
                this.BgmTime3                   = bgmTime3;
                this.BgmCueName3                = bgmCueName3;
                this.BgmCuesheetName3           = bgmCuesheetName3;
                this.BgmTime4                   = bgmTime4;
                this.BgmCueName4                = bgmCueName4;
                this.BgmCuesheetName4           = bgmCuesheetName4;
                this.BgmTime5                   = bgmTime5;
                this.BgmCueName5                = bgmCueName5;
                this.BgmCuesheetName5           = bgmCuesheetName5;
                this.BgmTime6                   = bgmTime6;
                this.BgmCueName6                = bgmCueName6;
                this.BgmCuesheetName6           = bgmCuesheetName6;
                this.BgmTime7                   = bgmTime7;
                this.BgmCueName7                = bgmCueName7;
                this.BgmCuesheetName7           = bgmCuesheetName7;
                this.BgmTime8                   = bgmTime8;
                this.BgmCueName8                = bgmCueName8;
                this.BgmCuesheetName8           = bgmCuesheetName8;
                this.BgmTime9                   = bgmTime9;
                this.BgmCueName9                = bgmCueName9;
                this.BgmCuesheetName9           = bgmCuesheetName9;
                this.BgmTime10                  = bgmTime10;
                this.BgmCueName10               = bgmCueName10;
                this.BgmCuesheetName10          = bgmCuesheetName10;
                this.BgmTime11                  = bgmTime11;
                this.BgmCueName11               = bgmCueName11;
                this.BgmCuesheetName11          = bgmCuesheetName11;
                this.BgmTime12                  = bgmTime12;
                this.BgmCueName12               = bgmCueName12;
                this.BgmCuesheetName12          = bgmCuesheetName12;
                this.BgmTime13                  = bgmTime13;
                this.BgmCueName13               = bgmCueName13;
                this.BgmCuesheetName13          = bgmCuesheetName13;
                this.BgmTime14                  = bgmTime14;
                this.BgmCueName14               = bgmCueName14;
                this.BgmCuesheetName14          = bgmCuesheetName14;
                this.BgmTime15                  = bgmTime15;
                this.BgmCueName15               = bgmCueName15;
                this.BgmCuesheetName15          = bgmCuesheetName15;
                this.BgmTime16                  = bgmTime16;
                this.BgmCueName16               = bgmCueName16;
                this.BgmCuesheetName16          = bgmCuesheetName16;
                this.BgmTime17                  = bgmTime17;
                this.BgmCueName17               = bgmCueName17;
                this.BgmCuesheetName17          = bgmCuesheetName17;
                this.BgmTime18                  = bgmTime18;
                this.BgmCueName18               = bgmCueName18;
                this.BgmCuesheetName18          = bgmCuesheetName18;
                this.BgmTime19                  = bgmTime19;
                this.BgmCueName19               = bgmCueName19;
                this.BgmCuesheetName19          = bgmCuesheetName19;
                this.BgmTime20                  = bgmTime20;
                this.BgmCueName20               = bgmCueName20;
                this.BgmCuesheetName20          = bgmCuesheetName20;
                this.BgmTime21                  = bgmTime21;
                this.BgmCueName21               = bgmCueName21;
                this.BgmCuesheetName21          = bgmCuesheetName21;
                this.BgmTime22                  = bgmTime22;
                this.BgmCueName22               = bgmCueName22;
                this.BgmCuesheetName22          = bgmCuesheetName22;
                this.BgmTime23                  = bgmTime23;
                this.BgmCueName23               = bgmCueName23;
                this.BgmCuesheetName23          = bgmCuesheetName23;
                this.BgmTime24                  = bgmTime24;
                this.BgmCueName24               = bgmCueName24;
                this.BgmCuesheetName24          = bgmCuesheetName24;
                this.BgmTime25                  = bgmTime25;
                this.BgmCueName25               = bgmCueName25;
                this.BgmCuesheetName25          = bgmCuesheetName25;
                this.BgmTime26                  = bgmTime26;
                this.BgmCueName26               = bgmCueName26;
                this.BgmCuesheetName26          = bgmCuesheetName26;
                this.BgmTime27                  = bgmTime27;
                this.BgmCueName27               = bgmCueName27;
                this.BgmCuesheetName27          = bgmCuesheetName27;
                this.BgmTime28                  = bgmTime28;
                this.BgmCueName28               = bgmCueName28;
                this.BgmCuesheetName28          = bgmCuesheetName28;
                this.BgmTime29                  = bgmTime29;
                this.BgmCueName29               = bgmCueName29;
                this.BgmCuesheetName29          = bgmCuesheetName29;
                this.BgmTime30                  = bgmTime30;
                this.BgmCueName30               = bgmCueName30;
                this.BgmCuesheetName30          = bgmCuesheetName30;
                this.BgmTriggerTimeAgo          = bgmTriggerTimeAgo;
                this.BgmTriggerCueName          = bgmTriggerCueName;
                this.BgmTriggerCuesheetName     = bgmTriggerCuesheetName;
                this.BgmSkipTriggerCueName      = bgmSkipTriggerCueName;
                this.BgmSkipTriggerCuesheetName = bgmSkipTriggerCuesheetName;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_jikkyo_cue
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:cuesheet_type]]
        unused - [:cuesheet_id]
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceJikkyoCue : AbstractMasterData
    {
        public const string TABLE_NAME = "race_jikkyo_cue";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<ulong> _notFounds = null;

        // cache dictionary
        private Dictionary<ulong, RaceJikkyoCue> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceJikkyoCue>> _dictionaryWithCuesheetType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceJikkyoCue(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<ulong, RaceJikkyoCue>();
            _dictionaryWithCuesheetType = new Dictionary<int, List<RaceJikkyoCue>>();
            _db = db;
        }


        public ulong GetKey(int id, int cuesheetType)
        {
            return ((uint)unchecked((ulong)((int)id))) | ((((ulong)unchecked((ulong)((int)cuesheetType)))) << 32);
        }

        public RaceJikkyoCue Get(ulong key)
        {
            int id           = (int)unchecked((int)((key >> 0) & 0xFFFFFFFF));
            int cuesheetType = (int)unchecked((int)((key >> 32) & 0xFFFFFFFF));

            return Get(id, cuesheetType);
        }

        public RaceJikkyoCue Get(int id, int cuesheetType)
        {
            ulong key = ((uint)unchecked((ulong)((int)id))) | ((((ulong)unchecked((ulong)((int)cuesheetType)))) << 32);

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceJikkyoCue");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id, cuesheetType);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<ulong>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceJikkyoCue", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoCue _SelectOne(int id, int cuesheetType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceJikkyoCue();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoCue");
                return null;
            }

            // SELECT `condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name` FROM `race_jikkyo_cue` WHERE `id`=? AND `cuesheet_type`=?;
            if (!query.BindInt(1, id))           { return null; }
            if (!query.BindInt(2, cuesheetType)) { return null; }

            RaceJikkyoCue orm = null;

            if (query.Step())
            {
                int conditionType1  = (int)query.GetInt(0);
                int conditionValue1 = (int)query.GetInt(1);
                int conditionType2  = (int)query.GetInt(2);
                int conditionValue2 = (int)query.GetInt(3);
                int conditionType3  = (int)query.GetInt(4);
                int conditionValue3 = (int)query.GetInt(5);
                int cuesheetName    = (int)query.GetInt(6);

                orm = new RaceJikkyoCue(id, conditionType1, conditionValue1, conditionType2, conditionValue2, conditionType3, conditionValue3, cuesheetName, cuesheetType);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0} {1}", id, cuesheetType));
            }

            query.Reset();

            return orm;
        }

        public RaceJikkyoCue GetWithCuesheetType(int cuesheetType)
        {
            RaceJikkyoCue orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCuesheetType(cuesheetType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", cuesheetType));
                } else {
                    ulong key = ((uint)unchecked((ulong)((int)orm.Id))) | ((((ulong)unchecked((ulong)((int)orm.CuesheetType)))) << 32);

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoCue _SelectWithCuesheetType(int cuesheetType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoCue_CuesheetType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoCue");
                return null;
            }

            // SELECT `id`,`condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name` FROM `race_jikkyo_cue` WHERE `cuesheet_type`=?;
            if (!query.BindInt(1, cuesheetType)) { return null; }

            RaceJikkyoCue orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCuesheetType(query, cuesheetType);
            }

            query.Reset();

            return orm;
        }

        public List<RaceJikkyoCue> GetListWithCuesheetType(int cuesheetType)
        {
            int key = (int)cuesheetType;

            if (!_dictionaryWithCuesheetType.ContainsKey(key)) {
                _dictionaryWithCuesheetType.Add(key, _ListSelectWithCuesheetType(cuesheetType));
            }

            return _dictionaryWithCuesheetType[key];
        }

        public List<RaceJikkyoCue> MaybeListWithCuesheetType(int cuesheetType)
        {
            List<RaceJikkyoCue> list = GetListWithCuesheetType(cuesheetType);
            return list.Count > 0 ? list : null;
        }

        private List<RaceJikkyoCue> _ListSelectWithCuesheetType(int cuesheetType)
        {
            List<RaceJikkyoCue> _list = new List<RaceJikkyoCue>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoCue_CuesheetType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoCue");
                return null;
            }

            // SELECT `id`,`condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name` FROM `race_jikkyo_cue` WHERE `cuesheet_type`=?;
            if (!query.BindInt(1, cuesheetType)) { return null; }

            while (query.Step()) {
                RaceJikkyoCue orm = _CreateOrmByQueryResultWithCuesheetType(query, cuesheetType);
                ulong key = ((uint)unchecked((ulong)((int)orm.Id))) | ((((ulong)unchecked((ulong)((int)orm.CuesheetType)))) << 32);

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceJikkyoCue _CreateOrmByQueryResultWithCuesheetType(LibNative.Sqlite3.PreparedQuery query, int cuesheetType)
        {
            int id              = (int)query.GetInt(0);
            int conditionType1  = (int)query.GetInt(1);
            int conditionValue1 = (int)query.GetInt(2);
            int conditionType2  = (int)query.GetInt(3);
            int conditionValue2 = (int)query.GetInt(4);
            int conditionType3  = (int)query.GetInt(5);
            int conditionValue3 = (int)query.GetInt(6);
            int cuesheetName    = (int)query.GetInt(7);

            return new RaceJikkyoCue(id, conditionType1, conditionValue1, conditionType2, conditionValue2, conditionType3, conditionValue3, cuesheetName, cuesheetType);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCuesheetType.Clear();
        }

        public sealed partial class RaceJikkyoCue
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: condition_type_1) </summary>
            public readonly int ConditionType1;
            /// <summary> (CSV column: condition_value_1) </summary>
            public readonly int ConditionValue1;
            /// <summary> (CSV column: condition_type_2) </summary>
            public readonly int ConditionType2;
            /// <summary> (CSV column: condition_value_2) </summary>
            public readonly int ConditionValue2;
            /// <summary> (CSV column: condition_type_3) </summary>
            public readonly int ConditionType3;
            /// <summary> (CSV column: condition_value_3) </summary>
            public readonly int ConditionValue3;
            /// <summary> (CSV column: cuesheet_name) </summary>
            public readonly int CuesheetName;
            /// <summary> (CSV column: cuesheet_type) </summary>
            public readonly int CuesheetType;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceJikkyoCue(int id = 0, int conditionType1 = 0, int conditionValue1 = 0, int conditionType2 = 0, int conditionValue2 = 0, int conditionType3 = 0, int conditionValue3 = 0, int cuesheetName = 0, int cuesheetType = 0)
            {
                this.Id              = id;
                this.ConditionType1  = conditionType1;
                this.ConditionValue1 = conditionValue1;
                this.ConditionType2  = conditionType2;
                this.ConditionValue2 = conditionValue2;
                this.ConditionType3  = conditionType3;
                this.ConditionValue3 = conditionValue3;
                this.CuesheetName    = cuesheetName;
                this.CuesheetType    = cuesheetType;
            }
        }
    }
}
#endif

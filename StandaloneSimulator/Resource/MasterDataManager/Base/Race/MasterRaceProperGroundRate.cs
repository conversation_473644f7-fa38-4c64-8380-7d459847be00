// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_proper_ground_rate
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceProperGroundRate : AbstractMasterData
    {
        public const string TABLE_NAME = "race_proper_ground_rate";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceProperGroundRate> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceProperGroundRate(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceProperGroundRate>();
            _db = db;
        }


        public RaceProperGroundRate Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceProperGroundRate");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceProperGroundRate", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceProperGroundRate _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceProperGroundRate();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceProperGroundRate");
                return null;
            }

            // SELECT `proper_rate` FROM `race_proper_ground_rate` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceProperGroundRate orm = null;

            if (query.Step())
            {
                int properRate = (int)query.GetInt(0);

                orm = new RaceProperGroundRate(id, properRate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class RaceProperGroundRate
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: proper_rate) </summary>
            public readonly int ProperRate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceProperGroundRate(int id = 0, int properRate = 0)
            {
                this.Id         = id;
                this.ProperRate = properRate;
            }
        }
    }
}
#endif

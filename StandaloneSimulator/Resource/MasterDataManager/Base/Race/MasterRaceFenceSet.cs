// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_fence_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceFenceSet : AbstractMasterData
    {
        public const string TABLE_NAME = "race_fence_set";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceFenceSet> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceFenceSet(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceFenceSet>();
            _db = db;
        }


        public RaceFenceSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceFenceSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceFenceSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceFenceSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceFenceSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceFenceSet");
                return null;
            }

            // SELECT `fence_1`,`fence_2`,`fence_3`,`fence_4`,`fence_5`,`fence_6`,`fence_7`,`fence_8` FROM `race_fence_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceFenceSet orm = null;

            if (query.Step())
            {
                int fence1 = (int)query.GetInt(0);
                int fence2 = (int)query.GetInt(1);
                int fence3 = (int)query.GetInt(2);
                int fence4 = (int)query.GetInt(3);
                int fence5 = (int)query.GetInt(4);
                int fence6 = (int)query.GetInt(5);
                int fence7 = (int)query.GetInt(6);
                int fence8 = (int)query.GetInt(7);

                orm = new RaceFenceSet(id, fence1, fence2, fence3, fence4, fence5, fence6, fence7, fence8);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class RaceFenceSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: fence_1) </summary>
            public readonly int Fence1;
            /// <summary> (CSV column: fence_2) </summary>
            public readonly int Fence2;
            /// <summary> (CSV column: fence_3) </summary>
            public readonly int Fence3;
            /// <summary> (CSV column: fence_4) </summary>
            public readonly int Fence4;
            /// <summary> (CSV column: fence_5) </summary>
            public readonly int Fence5;
            /// <summary> (CSV column: fence_6) </summary>
            public readonly int Fence6;
            /// <summary> (CSV column: fence_7) </summary>
            public readonly int Fence7;
            /// <summary> (CSV column: fence_8) </summary>
            public readonly int Fence8;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceFenceSet(int id = 0, int fence1 = 0, int fence2 = 0, int fence3 = 0, int fence4 = 0, int fence5 = 0, int fence6 = 0, int fence7 = 0, int fence8 = 0)
            {
                this.Id     = id;
                this.Fence1 = fence1;
                this.Fence2 = fence2;
                this.Fence3 = fence3;
                this.Fence4 = fence4;
                this.Fence5 = fence5;
                this.Fence6 = fence6;
                this.Fence7 = fence7;
                this.Fence8 = fence8;
            }
        }
    }
}
#endif

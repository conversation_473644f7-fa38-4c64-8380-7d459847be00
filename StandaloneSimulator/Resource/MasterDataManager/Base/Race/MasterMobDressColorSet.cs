// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/mob_dress_color_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterMobDressColorSet : AbstractMasterData
    {
        public const string TABLE_NAME = "mob_dress_color_set";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, MobDressColorSet> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterMobDressColorSet(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, MobDressColorSet>();
            _db = db;
        }


        public MobDressColorSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterMobDressColorSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterMobDressColorSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private MobDressColorSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_MobDressColorSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for MobDressColorSet");
                return null;
            }

            // SELECT `color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `mob_dress_color_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            MobDressColorSet orm = null;

            if (query.Step())
            {
                string colorR1     = query.GetText(0);
                string colorR2     = query.GetText(1);
                string colorG1     = query.GetText(2);
                string colorG2     = query.GetText(3);
                string colorB1     = query.GetText(4);
                string colorB2     = query.GetText(5);
                string toonColorR1 = query.GetText(6);
                string toonColorR2 = query.GetText(7);
                string toonColorG1 = query.GetText(8);
                string toonColorG2 = query.GetText(9);
                string toonColorB1 = query.GetText(10);
                string toonColorB2 = query.GetText(11);

                orm = new MobDressColorSet(id, colorR1, colorR2, colorG1, colorG2, colorB1, colorB2, toonColorR1, toonColorR2, toonColorG1, toonColorG2, toonColorB1, toonColorB2);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class MobDressColorSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: color_r1) </summary>
            public readonly string ColorR1;
            /// <summary> (CSV column: color_r2) </summary>
            public readonly string ColorR2;
            /// <summary> (CSV column: color_g1) </summary>
            public readonly string ColorG1;
            /// <summary> (CSV column: color_g2) </summary>
            public readonly string ColorG2;
            /// <summary> (CSV column: color_b1) </summary>
            public readonly string ColorB1;
            /// <summary> (CSV column: color_b2) </summary>
            public readonly string ColorB2;
            /// <summary> (CSV column: toon_color_r1) </summary>
            public readonly string ToonColorR1;
            /// <summary> (CSV column: toon_color_r2) </summary>
            public readonly string ToonColorR2;
            /// <summary> (CSV column: toon_color_g1) </summary>
            public readonly string ToonColorG1;
            /// <summary> (CSV column: toon_color_g2) </summary>
            public readonly string ToonColorG2;
            /// <summary> (CSV column: toon_color_b1) </summary>
            public readonly string ToonColorB1;
            /// <summary> (CSV column: toon_color_b2) </summary>
            public readonly string ToonColorB2;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public MobDressColorSet(int id = 0, string colorR1 = "", string colorR2 = "", string colorG1 = "", string colorG2 = "", string colorB1 = "", string colorB2 = "", string toonColorR1 = "", string toonColorR2 = "", string toonColorG1 = "", string toonColorG2 = "", string toonColorB1 = "", string toonColorB2 = "")
            {
                this.Id          = id;
                this.ColorR1     = colorR1;
                this.ColorR2     = colorR2;
                this.ColorG1     = colorG1;
                this.ColorG2     = colorG2;
                this.ColorB1     = colorB1;
                this.ColorB2     = colorB2;
                this.ToonColorR1 = toonColorR1;
                this.ToonColorR2 = toonColorR2;
                this.ToonColorG1 = toonColorG1;
                this.ToonColorG2 = toonColorG2;
                this.ToonColorB1 = toonColorB1;
                this.ToonColorB2 = toonColorB2;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/audience_impostor_color_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:color_group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterAudienceImpostorColorSet : AbstractMasterData
    {
        public const string TABLE_NAME = "audience_impostor_color_set";

        MasterRaceDatabase _db = null;


        // cache dictionary
        private Dictionary<int, AudienceImpostorColorSet> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<AudienceImpostorColorSet>> _dictionaryWithColorGroupId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterAudienceImpostorColorSet(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, AudienceImpostorColorSet>();
            _dictionaryWithColorGroupId = new Dictionary<int, List<AudienceImpostorColorSet>>();
            _db = db;
        }



        public AudienceImpostorColorSet GetWithColorGroupId(int colorGroupId)
        {
            AudienceImpostorColorSet orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithColorGroupId(colorGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", colorGroupId));
                }
            }

            return orm;
        }

        private AudienceImpostorColorSet _SelectWithColorGroupId(int colorGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AudienceImpostorColorSet_ColorGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceImpostorColorSet");
                return null;
            }

            // SELECT `color`,`odds` FROM `audience_impostor_color_set` WHERE `color_group_id`=?;
            if (!query.BindInt(1, colorGroupId)) { return null; }

            AudienceImpostorColorSet orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithColorGroupId(query, colorGroupId);
            }

            query.Reset();

            return orm;
        }

        public List<AudienceImpostorColorSet> GetListWithColorGroupId(int colorGroupId)
        {
            int key = (int)colorGroupId;

            if (!_dictionaryWithColorGroupId.ContainsKey(key)) {
                _dictionaryWithColorGroupId.Add(key, _ListSelectWithColorGroupId(colorGroupId));
            }

            return _dictionaryWithColorGroupId[key];
        }

        public List<AudienceImpostorColorSet> MaybeListWithColorGroupId(int colorGroupId)
        {
            List<AudienceImpostorColorSet> list = GetListWithColorGroupId(colorGroupId);
            return list.Count > 0 ? list : null;
        }

        private List<AudienceImpostorColorSet> _ListSelectWithColorGroupId(int colorGroupId)
        {
            List<AudienceImpostorColorSet> _list = new List<AudienceImpostorColorSet>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AudienceImpostorColorSet_ColorGroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceImpostorColorSet");
                return null;
            }

            // SELECT `color`,`odds` FROM `audience_impostor_color_set` WHERE `color_group_id`=?;
            if (!query.BindInt(1, colorGroupId)) { return null; }

            while (query.Step()) {
                AudienceImpostorColorSet orm = _CreateOrmByQueryResultWithColorGroupId(query, colorGroupId);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private AudienceImpostorColorSet _CreateOrmByQueryResultWithColorGroupId(LibNative.Sqlite3.PreparedQuery query, int colorGroupId)
        {
            string color = query.GetText(0);
            int odds     = (int)query.GetInt(1);

            return new AudienceImpostorColorSet(colorGroupId, color, odds);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithColorGroupId.Clear();
        }

        public sealed partial class AudienceImpostorColorSet
        {
            /// <summary> (CSV column: color_group_id) </summary>
            public readonly int ColorGroupId;
            /// <summary> (CSV column: color) </summary>
            public readonly string Color;
            /// <summary> (CSV column: odds) </summary>
            public readonly int Odds;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public AudienceImpostorColorSet(int colorGroupId = 0, string color = "", int odds = 0)
            {
                this.ColorGroupId = colorGroupId;
                this.Color        = color;
                this.Odds         = odds;
            }
        }
    }
}
#endif

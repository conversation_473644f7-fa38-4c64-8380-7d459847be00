// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_single_mode_team_status
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceSingleModeTeamStatus : AbstractMasterData
    {
        public const string TABLE_NAME = "race_single_mode_team_status";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceSingleModeTeamStatus> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceSingleModeTeamStatus> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceSingleModeTeamStatus");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceSingleModeTeamStatus(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceSingleModeTeamStatus>();
            _db = db;
        }


        public RaceSingleModeTeamStatus Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceSingleModeTeamStatus");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceSingleModeTeamStatus", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceSingleModeTeamStatus _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceSingleModeTeamStatus();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceSingleModeTeamStatus");
                return null;
            }

            // SELECT `team_rank_threshold`,`add_status` FROM `race_single_mode_team_status` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceSingleModeTeamStatus orm = null;

            if (query.Step())
            {
                int teamRankThreshold = (int)query.GetInt(0);
                int addStatus         = (int)query.GetInt(1);

                orm = new RaceSingleModeTeamStatus(id, teamRankThreshold, addStatus);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceSingleModeTeamStatus()) {
                while (query.Step()) {
                    int id                = (int)query.GetInt(0);
                    int teamRankThreshold = (int)query.GetInt(1);
                    int addStatus         = (int)query.GetInt(2);

                    int key = (int)id;
                    RaceSingleModeTeamStatus orm = new RaceSingleModeTeamStatus(id, teamRankThreshold, addStatus);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceSingleModeTeamStatus
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: team_rank_threshold) </summary>
            public readonly int TeamRankThreshold;
            /// <summary> (CSV column: add_status) </summary>
            public readonly int AddStatus;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceSingleModeTeamStatus(int id = 0, int teamRankThreshold = 0, int addStatus = 0)
            {
                this.Id                = id;
                this.TeamRankThreshold = teamRankThreshold;
                this.AddStatus         = addStatus;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_condition
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceCondition : AbstractMasterData
    {
        public const string TABLE_NAME = "race_condition";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceCondition> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceCondition> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceCondition");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceCondition(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceCondition>();
            _db = db;
        }


        public RaceCondition Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceCondition");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceCondition", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceCondition _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceCondition();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCondition");
                return null;
            }

            // SELECT `area`,`season`,`weather`,`ground`,`rate` FROM `race_condition` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceCondition orm = null;

            if (query.Step())
            {
                int area    = (int)query.GetInt(0);
                int season  = (int)query.GetInt(1);
                int weather = (int)query.GetInt(2);
                int ground  = (int)query.GetInt(3);
                int rate    = (int)query.GetInt(4);

                orm = new RaceCondition(id, area, season, weather, ground, rate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceCondition GetWithSeasonAndGround(int season, int ground)
        {
            RaceCondition orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithSeasonAndGround(season, ground);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", season, ground));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceCondition _SelectWithSeasonAndGround(int season, int ground)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceCondition_Season_Ground();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCondition");
                return null;
            }

            // SELECT `id`,`area`,`weather`,`rate` FROM `race_condition` WHERE `season`=? AND `ground`=?;
            if (!query.BindInt(1, season)) { return null; }
            if (!query.BindInt(2, ground)) { return null; }

            RaceCondition orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithSeasonAndGround(query, season, ground);
            }

            query.Reset();

            return orm;
        }

        private RaceCondition _CreateOrmByQueryResultWithSeasonAndGround(LibNative.Sqlite3.PreparedQuery query, int season, int ground)
        {
            int id      = (int)query.GetInt(0);
            int area    = (int)query.GetInt(1);
            int weather = (int)query.GetInt(2);
            int rate    = (int)query.GetInt(3);

            return new RaceCondition(id, area, season, weather, ground, rate);
        }

        public RaceCondition GetWithSeasonAndWeather(int season, int weather)
        {
            RaceCondition orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithSeasonAndWeather(season, weather);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", season, weather));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceCondition _SelectWithSeasonAndWeather(int season, int weather)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceCondition_Season_Weather();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCondition");
                return null;
            }

            // SELECT `id`,`area`,`ground`,`rate` FROM `race_condition` WHERE `season`=? AND `weather`=?;
            if (!query.BindInt(1, season))  { return null; }
            if (!query.BindInt(2, weather)) { return null; }

            RaceCondition orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithSeasonAndWeather(query, season, weather);
            }

            query.Reset();

            return orm;
        }

        private RaceCondition _CreateOrmByQueryResultWithSeasonAndWeather(LibNative.Sqlite3.PreparedQuery query, int season, int weather)
        {
            int id     = (int)query.GetInt(0);
            int area   = (int)query.GetInt(1);
            int ground = (int)query.GetInt(2);
            int rate   = (int)query.GetInt(3);

            return new RaceCondition(id, area, season, weather, ground, rate);
        }

        public RaceCondition GetWithWeatherAndGround(int weather, int ground)
        {
            RaceCondition orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithWeatherAndGround(weather, ground);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", weather, ground));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceCondition _SelectWithWeatherAndGround(int weather, int ground)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceCondition_Weather_Ground();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCondition");
                return null;
            }

            // SELECT `id`,`area`,`season`,`rate` FROM `race_condition` WHERE `weather`=? AND `ground`=?;
            if (!query.BindInt(1, weather)) { return null; }
            if (!query.BindInt(2, ground))  { return null; }

            RaceCondition orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithWeatherAndGround(query, weather, ground);
            }

            query.Reset();

            return orm;
        }

        private RaceCondition _CreateOrmByQueryResultWithWeatherAndGround(LibNative.Sqlite3.PreparedQuery query, int weather, int ground)
        {
            int id     = (int)query.GetInt(0);
            int area   = (int)query.GetInt(1);
            int season = (int)query.GetInt(2);
            int rate   = (int)query.GetInt(3);

            return new RaceCondition(id, area, season, weather, ground, rate);
        }

        public RaceCondition GetWithGroundAndWeather(int ground, int weather)
        {
            RaceCondition orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroundAndWeather(ground, weather);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", ground, weather));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceCondition _SelectWithGroundAndWeather(int ground, int weather)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceCondition_Ground_Weather();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCondition");
                return null;
            }

            // SELECT `id`,`area`,`season`,`rate` FROM `race_condition` WHERE `ground`=? AND `weather`=?;
            if (!query.BindInt(1, ground))  { return null; }
            if (!query.BindInt(2, weather)) { return null; }

            RaceCondition orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroundAndWeather(query, ground, weather);
            }

            query.Reset();

            return orm;
        }

        private RaceCondition _CreateOrmByQueryResultWithGroundAndWeather(LibNative.Sqlite3.PreparedQuery query, int ground, int weather)
        {
            int id     = (int)query.GetInt(0);
            int area   = (int)query.GetInt(1);
            int season = (int)query.GetInt(2);
            int rate   = (int)query.GetInt(3);

            return new RaceCondition(id, area, season, weather, ground, rate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceCondition()) {
                while (query.Step()) {
                    int id      = (int)query.GetInt(0);
                    int area    = (int)query.GetInt(1);
                    int season  = (int)query.GetInt(2);
                    int weather = (int)query.GetInt(3);
                    int ground  = (int)query.GetInt(4);
                    int rate    = (int)query.GetInt(5);

                    int key = (int)id;
                    RaceCondition orm = new RaceCondition(id, area, season, weather, ground, rate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceCondition
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: area) </summary>
            public readonly int Area;
            /// <summary> (CSV column: season) </summary>
            public readonly int Season;
            /// <summary> (CSV column: weather) </summary>
            public readonly int Weather;
            /// <summary> (CSV column: ground) </summary>
            public readonly int Ground;
            /// <summary> (CSV column: rate) </summary>
            public readonly int Rate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceCondition(int id = 0, int area = 0, int season = 0, int weather = 0, int ground = 0, int rate = 0)
            {
                this.Id      = id;
                this.Area    = area;
                this.Season  = season;
                this.Weather = weather;
                this.Ground  = ground;
                this.Rate    = rate;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_bgm_cutin
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:bgm_group_id], [:card_id, :bgm_group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceBgmCutin : AbstractMasterData
    {
        public const string TABLE_NAME = "race_bgm_cutin";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceBgmCutin> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceBgmCutin>> _dictionaryWithBgmGroupId = null;
        private Dictionary<ulong, List<RaceBgmCutin>> _dictionaryWithCardIdAndBgmGroupId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceBgmCutin(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceBgmCutin>();
            _dictionaryWithBgmGroupId = new Dictionary<int, List<RaceBgmCutin>>();
            _dictionaryWithCardIdAndBgmGroupId = new Dictionary<ulong, List<RaceBgmCutin>>();
            _db = db;
        }


        public RaceBgmCutin Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceBgmCutin");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceBgmCutin", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceBgmCutin _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceBgmCutin();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgmCutin");
                return null;
            }

            // SELECT `card_id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceBgmCutin orm = null;

            if (query.Step())
            {
                int cardId                       = (int)query.GetInt(0);
                int bgmGroupId                   = (int)query.GetInt(1);
                int fadeoutTime                  = (int)query.GetInt(2);
                bool exclusiveControl            = (query.GetInt(3) == 1);
                string shortCutinBgmCueName      = query.GetText(4);
                string shortCutinBgmCuesheetName = query.GetText(5);
                string longCutinBgmCueName       = query.GetText(6);
                string longCutinBgmCuesheetName  = query.GetText(7);

                orm = new RaceBgmCutin(id, cardId, bgmGroupId, fadeoutTime, exclusiveControl, shortCutinBgmCueName, shortCutinBgmCuesheetName, longCutinBgmCueName, longCutinBgmCuesheetName);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceBgmCutin GetWithCardId(int cardId)
        {
            RaceBgmCutin orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCardId(cardId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", cardId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceBgmCutin _SelectWithCardId(int cardId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceBgmCutin_CardId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgmCutin");
                return null;
            }

            // SELECT `id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `card_id`=?;
            if (!query.BindInt(1, cardId)) { return null; }

            RaceBgmCutin orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCardId(query, cardId);
            }

            query.Reset();

            return orm;
        }

        private RaceBgmCutin _CreateOrmByQueryResultWithCardId(LibNative.Sqlite3.PreparedQuery query, int cardId)
        {
            int id                           = (int)query.GetInt(0);
            int bgmGroupId                   = (int)query.GetInt(1);
            int fadeoutTime                  = (int)query.GetInt(2);
            bool exclusiveControl            = (query.GetInt(3) == 1);
            string shortCutinBgmCueName      = query.GetText(4);
            string shortCutinBgmCuesheetName = query.GetText(5);
            string longCutinBgmCueName       = query.GetText(6);
            string longCutinBgmCuesheetName  = query.GetText(7);

            return new RaceBgmCutin(id, cardId, bgmGroupId, fadeoutTime, exclusiveControl, shortCutinBgmCueName, shortCutinBgmCuesheetName, longCutinBgmCueName, longCutinBgmCuesheetName);
        }

        public RaceBgmCutin GetWithBgmGroupIdOrderByIdAsc(int bgmGroupId)
        {
            RaceBgmCutin orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithBgmGroupIdOrderByIdAsc(bgmGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", bgmGroupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceBgmCutin _SelectWithBgmGroupIdOrderByIdAsc(int bgmGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceBgmCutin_BgmGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgmCutin");
                return null;
            }

            // SELECT `id`,`card_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `bgm_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, bgmGroupId)) { return null; }

            RaceBgmCutin orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithBgmGroupIdOrderByIdAsc(query, bgmGroupId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceBgmCutin> GetListWithBgmGroupIdOrderByIdAsc(int bgmGroupId)
        {
            int key = (int)bgmGroupId;

            if (!_dictionaryWithBgmGroupId.ContainsKey(key)) {
                _dictionaryWithBgmGroupId.Add(key, _ListSelectWithBgmGroupIdOrderByIdAsc(bgmGroupId));
            }

            return _dictionaryWithBgmGroupId[key];
        }

        public List<RaceBgmCutin> MaybeListWithBgmGroupIdOrderByIdAsc(int bgmGroupId)
        {
            List<RaceBgmCutin> list = GetListWithBgmGroupIdOrderByIdAsc(bgmGroupId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceBgmCutin> _ListSelectWithBgmGroupIdOrderByIdAsc(int bgmGroupId)
        {
            List<RaceBgmCutin> _list = new List<RaceBgmCutin>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceBgmCutin_BgmGroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgmCutin");
                return null;
            }

            // SELECT `id`,`card_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `bgm_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, bgmGroupId)) { return null; }

            while (query.Step()) {
                RaceBgmCutin orm = _CreateOrmByQueryResultWithBgmGroupIdOrderByIdAsc(query, bgmGroupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceBgmCutin _CreateOrmByQueryResultWithBgmGroupIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int bgmGroupId)
        {
            int id                           = (int)query.GetInt(0);
            int cardId                       = (int)query.GetInt(1);
            int fadeoutTime                  = (int)query.GetInt(2);
            bool exclusiveControl            = (query.GetInt(3) == 1);
            string shortCutinBgmCueName      = query.GetText(4);
            string shortCutinBgmCuesheetName = query.GetText(5);
            string longCutinBgmCueName       = query.GetText(6);
            string longCutinBgmCuesheetName  = query.GetText(7);

            return new RaceBgmCutin(id, cardId, bgmGroupId, fadeoutTime, exclusiveControl, shortCutinBgmCueName, shortCutinBgmCuesheetName, longCutinBgmCueName, longCutinBgmCuesheetName);
        }

        public RaceBgmCutin GetWithCardIdAndBgmGroupIdOrderByIdAsc(int cardId, int bgmGroupId)
        {
            RaceBgmCutin orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCardIdAndBgmGroupIdOrderByIdAsc(cardId, bgmGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", cardId, bgmGroupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceBgmCutin _SelectWithCardIdAndBgmGroupIdOrderByIdAsc(int cardId, int bgmGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceBgmCutin_CardId_BgmGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgmCutin");
                return null;
            }

            // SELECT `id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `card_id`=? AND `bgm_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, cardId))     { return null; }
            if (!query.BindInt(2, bgmGroupId)) { return null; }

            RaceBgmCutin orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCardIdAndBgmGroupIdOrderByIdAsc(query, cardId, bgmGroupId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceBgmCutin> GetListWithCardIdAndBgmGroupIdOrderByIdAsc(int cardId, int bgmGroupId)
        {
            ulong key = ((uint)unchecked((ulong)((int)cardId))) | ((((ulong)unchecked((ulong)((int)bgmGroupId)))) << 32);

            if (!_dictionaryWithCardIdAndBgmGroupId.ContainsKey(key)) {
                _dictionaryWithCardIdAndBgmGroupId.Add(key, _ListSelectWithCardIdAndBgmGroupIdOrderByIdAsc(cardId, bgmGroupId));
            }

            return _dictionaryWithCardIdAndBgmGroupId[key];
        }

        public List<RaceBgmCutin> MaybeListWithCardIdAndBgmGroupIdOrderByIdAsc(int cardId, int bgmGroupId)
        {
            List<RaceBgmCutin> list = GetListWithCardIdAndBgmGroupIdOrderByIdAsc(cardId, bgmGroupId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceBgmCutin> _ListSelectWithCardIdAndBgmGroupIdOrderByIdAsc(int cardId, int bgmGroupId)
        {
            List<RaceBgmCutin> _list = new List<RaceBgmCutin>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceBgmCutin_CardId_BgmGroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgmCutin");
                return null;
            }

            // SELECT `id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `card_id`=? AND `bgm_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, cardId))     { return null; }
            if (!query.BindInt(2, bgmGroupId)) { return null; }

            while (query.Step()) {
                RaceBgmCutin orm = _CreateOrmByQueryResultWithCardIdAndBgmGroupIdOrderByIdAsc(query, cardId, bgmGroupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceBgmCutin _CreateOrmByQueryResultWithCardIdAndBgmGroupIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int cardId, int bgmGroupId)
        {
            int id                           = (int)query.GetInt(0);
            int fadeoutTime                  = (int)query.GetInt(1);
            bool exclusiveControl            = (query.GetInt(2) == 1);
            string shortCutinBgmCueName      = query.GetText(3);
            string shortCutinBgmCuesheetName = query.GetText(4);
            string longCutinBgmCueName       = query.GetText(5);
            string longCutinBgmCuesheetName  = query.GetText(6);

            return new RaceBgmCutin(id, cardId, bgmGroupId, fadeoutTime, exclusiveControl, shortCutinBgmCueName, shortCutinBgmCuesheetName, longCutinBgmCueName, longCutinBgmCuesheetName);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithBgmGroupId.Clear();
            _dictionaryWithCardIdAndBgmGroupId.Clear();
        }

        public sealed partial class RaceBgmCutin
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: card_id) </summary>
            public readonly int CardId;
            /// <summary> (CSV column: bgm_group_id) </summary>
            public readonly int BgmGroupId;
            /// <summary> (CSV column: fadeout_time) </summary>
            public readonly int FadeoutTime;
            /// <summary> (CSV column: exclusive_control) </summary>
            public readonly bool ExclusiveControl;
            /// <summary> (CSV column: short_cutin_bgm_cue_name) </summary>
            public readonly string ShortCutinBgmCueName;
            /// <summary> (CSV column: short_cutin_bgm_cuesheet_name) </summary>
            public readonly string ShortCutinBgmCuesheetName;
            /// <summary> (CSV column: long_cutin_bgm_cue_name) </summary>
            public readonly string LongCutinBgmCueName;
            /// <summary> (CSV column: long_cutin_bgm_cuesheet_name) </summary>
            public readonly string LongCutinBgmCuesheetName;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceBgmCutin(int id = 0, int cardId = 0, int bgmGroupId = 0, int fadeoutTime = 0, bool exclusiveControl = false, string shortCutinBgmCueName = "", string shortCutinBgmCuesheetName = "", string longCutinBgmCueName = "", string longCutinBgmCuesheetName = "")
            {
                this.Id                        = id;
                this.CardId                    = cardId;
                this.BgmGroupId                = bgmGroupId;
                this.FadeoutTime               = fadeoutTime;
                this.ExclusiveControl          = exclusiveControl;
                this.ShortCutinBgmCueName      = shortCutinBgmCueName;
                this.ShortCutinBgmCuesheetName = shortCutinBgmCuesheetName;
                this.LongCutinBgmCueName       = longCutinBgmCueName;
                this.LongCutinBgmCuesheetName  = longCutinBgmCuesheetName;
            }
        }
    }
}
#endif

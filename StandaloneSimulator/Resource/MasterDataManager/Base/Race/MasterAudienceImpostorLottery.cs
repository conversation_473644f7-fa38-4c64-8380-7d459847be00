// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/audience_impostor_lottery
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id, :pattern]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterAudienceImpostorLottery : AbstractMasterData
    {
        public const string TABLE_NAME = "audience_impostor_lottery";

        MasterRaceDatabase _db = null;


        // cache dictionary
        private Dictionary<int, AudienceImpostorLottery> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<AudienceImpostorLottery>> _dictionaryWithGroupIdAndPattern = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterAudienceImpostorLottery(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, AudienceImpostorLottery>();
            _dictionaryWithGroupIdAndPattern = new Dictionary<ulong, List<AudienceImpostorLottery>>();
            _db = db;
        }



        public AudienceImpostorLottery GetWithGroupIdAndPattern(int groupId, int pattern)
        {
            AudienceImpostorLottery orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupIdAndPattern(groupId, pattern);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", groupId, pattern));
                }
            }

            return orm;
        }

        private AudienceImpostorLottery _SelectWithGroupIdAndPattern(int groupId, int pattern)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AudienceImpostorLottery_GroupId_Pattern();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceImpostorLottery");
                return null;
            }

            // SELECT `asset_id`,`odds` FROM `audience_impostor_lottery` WHERE `group_id`=? AND `pattern`=?;
            if (!query.BindInt(1, groupId)) { return null; }
            if (!query.BindInt(2, pattern)) { return null; }

            AudienceImpostorLottery orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupIdAndPattern(query, groupId, pattern);
            }

            query.Reset();

            return orm;
        }

        public List<AudienceImpostorLottery> GetListWithGroupIdAndPattern(int groupId, int pattern)
        {
            ulong key = ((uint)unchecked((ulong)((int)groupId))) | ((((ulong)unchecked((ulong)((int)pattern)))) << 32);

            if (!_dictionaryWithGroupIdAndPattern.ContainsKey(key)) {
                _dictionaryWithGroupIdAndPattern.Add(key, _ListSelectWithGroupIdAndPattern(groupId, pattern));
            }

            return _dictionaryWithGroupIdAndPattern[key];
        }

        public List<AudienceImpostorLottery> MaybeListWithGroupIdAndPattern(int groupId, int pattern)
        {
            List<AudienceImpostorLottery> list = GetListWithGroupIdAndPattern(groupId, pattern);
            return list.Count > 0 ? list : null;
        }

        private List<AudienceImpostorLottery> _ListSelectWithGroupIdAndPattern(int groupId, int pattern)
        {
            List<AudienceImpostorLottery> _list = new List<AudienceImpostorLottery>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AudienceImpostorLottery_GroupId_Pattern();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceImpostorLottery");
                return null;
            }

            // SELECT `asset_id`,`odds` FROM `audience_impostor_lottery` WHERE `group_id`=? AND `pattern`=?;
            if (!query.BindInt(1, groupId)) { return null; }
            if (!query.BindInt(2, pattern)) { return null; }

            while (query.Step()) {
                AudienceImpostorLottery orm = _CreateOrmByQueryResultWithGroupIdAndPattern(query, groupId, pattern);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private AudienceImpostorLottery _CreateOrmByQueryResultWithGroupIdAndPattern(LibNative.Sqlite3.PreparedQuery query, int groupId, int pattern)
        {
            int assetId = (int)query.GetInt(0);
            int odds    = (int)query.GetInt(1);

            return new AudienceImpostorLottery(groupId, pattern, assetId, odds);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGroupIdAndPattern.Clear();
        }

        public sealed partial class AudienceImpostorLottery
        {
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: pattern) </summary>
            public readonly int Pattern;
            /// <summary> (CSV column: asset_id) </summary>
            public readonly int AssetId;
            /// <summary> (CSV column: odds) </summary>
            public readonly int Odds;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public AudienceImpostorLottery(int groupId = 0, int pattern = 0, int assetId = 0, int odds = 0)
            {
                this.GroupId = groupId;
                this.Pattern = pattern;
                this.AssetId = assetId;
                this.Odds    = odds;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_jikkyo_trigger
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceJikkyoTrigger : AbstractMasterData
    {
        public const string TABLE_NAME = "race_jikkyo_trigger";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceJikkyoTrigger> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceJikkyoTrigger> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceJikkyoTrigger");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceJikkyoTrigger(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceJikkyoTrigger>();
            _db = db;
        }


        public RaceJikkyoTrigger Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceJikkyoTrigger");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceJikkyoTrigger", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoTrigger _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceJikkyoTrigger();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoTrigger");
                return null;
            }

            // SELECT `command`,`inequality`,`horse1`,`horse2`,`param1`,`param2` FROM `race_jikkyo_trigger` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceJikkyoTrigger orm = null;

            if (query.Step())
            {
                int command    = (int)query.GetInt(0);
                int inequality = (int)query.GetInt(1);
                int horse1     = (int)query.GetInt(2);
                int horse2     = (int)query.GetInt(3);
                int param1     = (int)query.GetInt(4);
                int param2     = (int)query.GetInt(5);

                orm = new RaceJikkyoTrigger(id, command, inequality, horse1, horse2, param1, param2);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceJikkyoTrigger()) {
                while (query.Step()) {
                    int id         = (int)query.GetInt(0);
                    int command    = (int)query.GetInt(1);
                    int inequality = (int)query.GetInt(2);
                    int horse1     = (int)query.GetInt(3);
                    int horse2     = (int)query.GetInt(4);
                    int param1     = (int)query.GetInt(5);
                    int param2     = (int)query.GetInt(6);

                    int key = (int)id;
                    RaceJikkyoTrigger orm = new RaceJikkyoTrigger(id, command, inequality, horse1, horse2, param1, param2);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceJikkyoTrigger
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: command) </summary>
            public readonly int Command;
            /// <summary> (CSV column: inequality) </summary>
            public readonly int Inequality;
            /// <summary> (CSV column: horse1) </summary>
            public readonly int Horse1;
            /// <summary> (CSV column: horse2) </summary>
            public readonly int Horse2;
            /// <summary> (CSV column: param1) </summary>
            public readonly int Param1;
            /// <summary> (CSV column: param2) </summary>
            public readonly int Param2;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceJikkyoTrigger(int id = 0, int command = 0, int inequality = 0, int horse1 = 0, int horse2 = 0, int param1 = 0, int param2 = 0)
            {
                this.Id         = id;
                this.Command    = command;
                this.Inequality = inequality;
                this.Horse1     = horse1;
                this.Horse2     = horse2;
                this.Param1     = param1;
                this.Param2     = param2;
            }
        }
    }
}
#endif

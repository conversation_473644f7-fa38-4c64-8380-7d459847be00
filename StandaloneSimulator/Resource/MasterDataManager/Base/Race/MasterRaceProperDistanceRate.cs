// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_proper_distance_rate
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceProperDistanceRate : AbstractMasterData
    {
        public const string TABLE_NAME = "race_proper_distance_rate";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceProperDistanceRate> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceProperDistanceRate(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceProperDistanceRate>();
            _db = db;
        }


        public RaceProperDistanceRate Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceProperDistanceRate");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceProperDistanceRate", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceProperDistanceRate _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceProperDistanceRate();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceProperDistanceRate");
                return null;
            }

            // SELECT `proper_rate_speed`,`proper_rate_power` FROM `race_proper_distance_rate` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceProperDistanceRate orm = null;

            if (query.Step())
            {
                int properRateSpeed = (int)query.GetInt(0);
                int properRatePower = (int)query.GetInt(1);

                orm = new RaceProperDistanceRate(id, properRateSpeed, properRatePower);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class RaceProperDistanceRate
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: proper_rate_speed) </summary>
            public readonly int ProperRateSpeed;
            /// <summary> (CSV column: proper_rate_power) </summary>
            public readonly int ProperRatePower;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceProperDistanceRate(int id = 0, int properRateSpeed = 0, int properRatePower = 0)
            {
                this.Id              = id;
                this.ProperRateSpeed = properRateSpeed;
                this.ProperRatePower = properRatePower;
            }
        }
    }
}
#endif

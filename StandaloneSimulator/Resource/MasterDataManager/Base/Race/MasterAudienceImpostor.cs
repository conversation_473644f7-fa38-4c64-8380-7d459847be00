// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/audience_impostor
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:season, :weather]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterAudienceImpostor : AbstractMasterData
    {
        public const string TABLE_NAME = "audience_impostor";

        MasterRaceDatabase _db = null;


        // cache dictionary
        private Dictionary<int, AudienceImpostor> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<AudienceImpostor>> _dictionaryWithSeasonAndWeather = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterAudienceImpostor(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, AudienceImpostor>();
            _dictionaryWithSeasonAndWeather = new Dictionary<ulong, List<AudienceImpostor>>();
            _db = db;
        }



        public AudienceImpostor GetWithSeasonAndWeather(int season, int weather)
        {
            AudienceImpostor orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithSeasonAndWeather(season, weather);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", season, weather));
                }
            }

            return orm;
        }

        private AudienceImpostor _SelectWithSeasonAndWeather(int season, int weather)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AudienceImpostor_Season_Weather();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceImpostor");
                return null;
            }

            // SELECT `scene_type`,`audience_group_id`,`group_id`,`pattern_1`,`pattern_2`,`pattern_3`,`pattern_4`,`pattern_5`,`pattern_6`,`pattern_7`,`pattern_8`,`pattern_9`,`pattern_10` FROM `audience_impostor` WHERE `season`=? AND `weather`=?;
            if (!query.BindInt(1, season))  { return null; }
            if (!query.BindInt(2, weather)) { return null; }

            AudienceImpostor orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithSeasonAndWeather(query, season, weather);
            }

            query.Reset();

            return orm;
        }

        public List<AudienceImpostor> GetListWithSeasonAndWeather(int season, int weather)
        {
            ulong key = ((uint)unchecked((ulong)((int)season))) | ((((ulong)unchecked((ulong)((int)weather)))) << 32);

            if (!_dictionaryWithSeasonAndWeather.ContainsKey(key)) {
                _dictionaryWithSeasonAndWeather.Add(key, _ListSelectWithSeasonAndWeather(season, weather));
            }

            return _dictionaryWithSeasonAndWeather[key];
        }

        public List<AudienceImpostor> MaybeListWithSeasonAndWeather(int season, int weather)
        {
            List<AudienceImpostor> list = GetListWithSeasonAndWeather(season, weather);
            return list.Count > 0 ? list : null;
        }

        private List<AudienceImpostor> _ListSelectWithSeasonAndWeather(int season, int weather)
        {
            List<AudienceImpostor> _list = new List<AudienceImpostor>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_AudienceImpostor_Season_Weather();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceImpostor");
                return null;
            }

            // SELECT `scene_type`,`audience_group_id`,`group_id`,`pattern_1`,`pattern_2`,`pattern_3`,`pattern_4`,`pattern_5`,`pattern_6`,`pattern_7`,`pattern_8`,`pattern_9`,`pattern_10` FROM `audience_impostor` WHERE `season`=? AND `weather`=?;
            if (!query.BindInt(1, season))  { return null; }
            if (!query.BindInt(2, weather)) { return null; }

            while (query.Step()) {
                AudienceImpostor orm = _CreateOrmByQueryResultWithSeasonAndWeather(query, season, weather);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private AudienceImpostor _CreateOrmByQueryResultWithSeasonAndWeather(LibNative.Sqlite3.PreparedQuery query, int season, int weather)
        {
            int sceneType       = (int)query.GetInt(0);
            int audienceGroupId = (int)query.GetInt(1);
            int groupId         = (int)query.GetInt(2);
            int pattern1        = (int)query.GetInt(3);
            int pattern2        = (int)query.GetInt(4);
            int pattern3        = (int)query.GetInt(5);
            int pattern4        = (int)query.GetInt(6);
            int pattern5        = (int)query.GetInt(7);
            int pattern6        = (int)query.GetInt(8);
            int pattern7        = (int)query.GetInt(9);
            int pattern8        = (int)query.GetInt(10);
            int pattern9        = (int)query.GetInt(11);
            int pattern10       = (int)query.GetInt(12);

            return new AudienceImpostor(sceneType, audienceGroupId, season, weather, groupId, pattern1, pattern2, pattern3, pattern4, pattern5, pattern6, pattern7, pattern8, pattern9, pattern10);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithSeasonAndWeather.Clear();
        }

        public sealed partial class AudienceImpostor
        {
            /// <summary> (CSV column: scene_type) </summary>
            public readonly int SceneType;
            /// <summary> (CSV column: audience_group_id) </summary>
            public readonly int AudienceGroupId;
            /// <summary> (CSV column: season) </summary>
            public readonly int Season;
            /// <summary> (CSV column: weather) </summary>
            public readonly int Weather;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: pattern_1) </summary>
            public readonly int Pattern1;
            /// <summary> (CSV column: pattern_2) </summary>
            public readonly int Pattern2;
            /// <summary> (CSV column: pattern_3) </summary>
            public readonly int Pattern3;
            /// <summary> (CSV column: pattern_4) </summary>
            public readonly int Pattern4;
            /// <summary> (CSV column: pattern_5) </summary>
            public readonly int Pattern5;
            /// <summary> (CSV column: pattern_6) </summary>
            public readonly int Pattern6;
            /// <summary> (CSV column: pattern_7) </summary>
            public readonly int Pattern7;
            /// <summary> (CSV column: pattern_8) </summary>
            public readonly int Pattern8;
            /// <summary> (CSV column: pattern_9) </summary>
            public readonly int Pattern9;
            /// <summary> (CSV column: pattern_10) </summary>
            public readonly int Pattern10;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public AudienceImpostor(int sceneType = 0, int audienceGroupId = 0, int season = 0, int weather = 0, int groupId = 0, int pattern1 = 0, int pattern2 = 0, int pattern3 = 0, int pattern4 = 0, int pattern5 = 0, int pattern6 = 0, int pattern7 = 0, int pattern8 = 0, int pattern9 = 0, int pattern10 = 0)
            {
                this.SceneType       = sceneType;
                this.AudienceGroupId = audienceGroupId;
                this.Season          = season;
                this.Weather         = weather;
                this.GroupId         = groupId;
                this.Pattern1        = pattern1;
                this.Pattern2        = pattern2;
                this.Pattern3        = pattern3;
                this.Pattern4        = pattern4;
                this.Pattern5        = pattern5;
                this.Pattern6        = pattern6;
                this.Pattern7        = pattern7;
                this.Pattern8        = pattern8;
                this.Pattern9        = pattern9;
                this.Pattern10       = pattern10;
            }
        }
    }
}
#endif

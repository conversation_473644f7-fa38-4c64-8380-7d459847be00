// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/audience_hair_color_set
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterAudienceHairColorSet : AbstractMasterData
    {
        public const string TABLE_NAME = "audience_hair_color_set";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, AudienceHairColorSet> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterAudienceHairColorSet(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, AudienceHairColorSet>();
            _db = db;
        }


        public AudienceHairColorSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterAudienceHairColorSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterAudienceHairColorSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private AudienceHairColorSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_AudienceHairColorSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for AudienceHairColorSet");
                return null;
            }

            // SELECT `hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2` FROM `audience_hair_color_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            AudienceHairColorSet orm = null;

            if (query.Step())
            {
                string hairColorR1     = query.GetText(0);
                string hairColorR2     = query.GetText(1);
                string hairColorG1     = query.GetText(2);
                string hairColorG2     = query.GetText(3);
                string hairColorB1     = query.GetText(4);
                string hairColorB2     = query.GetText(5);
                string hairToonColorR1 = query.GetText(6);
                string hairToonColorR2 = query.GetText(7);
                string hairToonColorG1 = query.GetText(8);
                string hairToonColorG2 = query.GetText(9);
                string hairToonColorB1 = query.GetText(10);
                string hairToonColorB2 = query.GetText(11);
                string mayuColorR1     = query.GetText(12);
                string mayuColorR2     = query.GetText(13);
                string mayuColorG1     = query.GetText(14);
                string mayuColorG2     = query.GetText(15);
                string mayuColorB1     = query.GetText(16);
                string mayuColorB2     = query.GetText(17);
                string mayuToonColorR1 = query.GetText(18);
                string mayuToonColorR2 = query.GetText(19);
                string mayuToonColorG1 = query.GetText(20);
                string mayuToonColorG2 = query.GetText(21);
                string mayuToonColorB1 = query.GetText(22);
                string mayuToonColorB2 = query.GetText(23);
                string eyeColorR1      = query.GetText(24);
                string eyeColorR2      = query.GetText(25);
                string eyeColorG1      = query.GetText(26);
                string eyeColorG2      = query.GetText(27);
                string eyeColorB1      = query.GetText(28);
                string eyeColorB2      = query.GetText(29);

                orm = new AudienceHairColorSet(id, hairColorR1, hairColorR2, hairColorG1, hairColorG2, hairColorB1, hairColorB2, hairToonColorR1, hairToonColorR2, hairToonColorG1, hairToonColorG2, hairToonColorB1, hairToonColorB2, mayuColorR1, mayuColorR2, mayuColorG1, mayuColorG2, mayuColorB1, mayuColorB2, mayuToonColorR1, mayuToonColorR2, mayuToonColorG1, mayuToonColorG2, mayuToonColorB1, mayuToonColorB2, eyeColorR1, eyeColorR2, eyeColorG1, eyeColorG2, eyeColorB1, eyeColorB2);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }

        public sealed partial class AudienceHairColorSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: hair_color_r1) </summary>
            public readonly string HairColorR1;
            /// <summary> (CSV column: hair_color_r2) </summary>
            public readonly string HairColorR2;
            /// <summary> (CSV column: hair_color_g1) </summary>
            public readonly string HairColorG1;
            /// <summary> (CSV column: hair_color_g2) </summary>
            public readonly string HairColorG2;
            /// <summary> (CSV column: hair_color_b1) </summary>
            public readonly string HairColorB1;
            /// <summary> (CSV column: hair_color_b2) </summary>
            public readonly string HairColorB2;
            /// <summary> (CSV column: hair_toon_color_r1) </summary>
            public readonly string HairToonColorR1;
            /// <summary> (CSV column: hair_toon_color_r2) </summary>
            public readonly string HairToonColorR2;
            /// <summary> (CSV column: hair_toon_color_g1) </summary>
            public readonly string HairToonColorG1;
            /// <summary> (CSV column: hair_toon_color_g2) </summary>
            public readonly string HairToonColorG2;
            /// <summary> (CSV column: hair_toon_color_b1) </summary>
            public readonly string HairToonColorB1;
            /// <summary> (CSV column: hair_toon_color_b2) </summary>
            public readonly string HairToonColorB2;
            /// <summary> (CSV column: mayu_color_r1) </summary>
            public readonly string MayuColorR1;
            /// <summary> (CSV column: mayu_color_r2) </summary>
            public readonly string MayuColorR2;
            /// <summary> (CSV column: mayu_color_g1) </summary>
            public readonly string MayuColorG1;
            /// <summary> (CSV column: mayu_color_g2) </summary>
            public readonly string MayuColorG2;
            /// <summary> (CSV column: mayu_color_b1) </summary>
            public readonly string MayuColorB1;
            /// <summary> (CSV column: mayu_color_b2) </summary>
            public readonly string MayuColorB2;
            /// <summary> (CSV column: mayu_toon_color_r1) </summary>
            public readonly string MayuToonColorR1;
            /// <summary> (CSV column: mayu_toon_color_r2) </summary>
            public readonly string MayuToonColorR2;
            /// <summary> (CSV column: mayu_toon_color_g1) </summary>
            public readonly string MayuToonColorG1;
            /// <summary> (CSV column: mayu_toon_color_g2) </summary>
            public readonly string MayuToonColorG2;
            /// <summary> (CSV column: mayu_toon_color_b1) </summary>
            public readonly string MayuToonColorB1;
            /// <summary> (CSV column: mayu_toon_color_b2) </summary>
            public readonly string MayuToonColorB2;
            /// <summary> (CSV column: eye_color_r1) </summary>
            public readonly string EyeColorR1;
            /// <summary> (CSV column: eye_color_r2) </summary>
            public readonly string EyeColorR2;
            /// <summary> (CSV column: eye_color_g1) </summary>
            public readonly string EyeColorG1;
            /// <summary> (CSV column: eye_color_g2) </summary>
            public readonly string EyeColorG2;
            /// <summary> (CSV column: eye_color_b1) </summary>
            public readonly string EyeColorB1;
            /// <summary> (CSV column: eye_color_b2) </summary>
            public readonly string EyeColorB2;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public AudienceHairColorSet(int id = 0, string hairColorR1 = "", string hairColorR2 = "", string hairColorG1 = "", string hairColorG2 = "", string hairColorB1 = "", string hairColorB2 = "", string hairToonColorR1 = "", string hairToonColorR2 = "", string hairToonColorG1 = "", string hairToonColorG2 = "", string hairToonColorB1 = "", string hairToonColorB2 = "", string mayuColorR1 = "", string mayuColorR2 = "", string mayuColorG1 = "", string mayuColorG2 = "", string mayuColorB1 = "", string mayuColorB2 = "", string mayuToonColorR1 = "", string mayuToonColorR2 = "", string mayuToonColorG1 = "", string mayuToonColorG2 = "", string mayuToonColorB1 = "", string mayuToonColorB2 = "", string eyeColorR1 = "", string eyeColorR2 = "", string eyeColorG1 = "", string eyeColorG2 = "", string eyeColorB1 = "", string eyeColorB2 = "")
            {
                this.Id              = id;
                this.HairColorR1     = hairColorR1;
                this.HairColorR2     = hairColorR2;
                this.HairColorG1     = hairColorG1;
                this.HairColorG2     = hairColorG2;
                this.HairColorB1     = hairColorB1;
                this.HairColorB2     = hairColorB2;
                this.HairToonColorR1 = hairToonColorR1;
                this.HairToonColorR2 = hairToonColorR2;
                this.HairToonColorG1 = hairToonColorG1;
                this.HairToonColorG2 = hairToonColorG2;
                this.HairToonColorB1 = hairToonColorB1;
                this.HairToonColorB2 = hairToonColorB2;
                this.MayuColorR1     = mayuColorR1;
                this.MayuColorR2     = mayuColorR2;
                this.MayuColorG1     = mayuColorG1;
                this.MayuColorG2     = mayuColorG2;
                this.MayuColorB1     = mayuColorB1;
                this.MayuColorB2     = mayuColorB2;
                this.MayuToonColorR1 = mayuToonColorR1;
                this.MayuToonColorR2 = mayuToonColorR2;
                this.MayuToonColorG1 = mayuToonColorG1;
                this.MayuToonColorG2 = mayuToonColorG2;
                this.MayuToonColorB1 = mayuToonColorB1;
                this.MayuToonColorB2 = mayuToonColorB2;
                this.EyeColorR1      = eyeColorR1;
                this.EyeColorR2      = eyeColorR2;
                this.EyeColorG1      = eyeColorG1;
                this.EyeColorG2      = eyeColorG2;
                this.EyeColorB1      = eyeColorB1;
                this.EyeColorB2      = eyeColorB2;
            }
        }
    }
}
#endif

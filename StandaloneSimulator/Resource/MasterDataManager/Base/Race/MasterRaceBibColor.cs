// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_bib_color
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceBibColor : AbstractMasterData
    {
        public const string TABLE_NAME = "race_bib_color";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<ulong> _notFounds = null;

        // cache dictionary
        private Dictionary<ulong, RaceBibColor> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<ulong, RaceBibColor> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceBibColor");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceBibColor(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<ulong, RaceBibColor>();
            _db = db;
        }


        public ulong GetKey(int grade, int raceId)
        {
            return ((uint)unchecked((ulong)((int)grade))) | ((((ulong)unchecked((ulong)((int)raceId)))) << 32);
        }

        public RaceBibColor Get(ulong key)
        {
            int grade  = (int)unchecked((int)((key >> 0) & 0xFFFFFFFF));
            int raceId = (int)unchecked((int)((key >> 32) & 0xFFFFFFFF));

            return Get(grade, raceId);
        }

        public RaceBibColor Get(int grade, int raceId)
        {
            ulong key = ((uint)unchecked((ulong)((int)grade))) | ((((ulong)unchecked((ulong)((int)raceId)))) << 32);

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceBibColor");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(grade, raceId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<ulong>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceBibColor", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceBibColor _SelectOne(int grade, int raceId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceBibColor();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBibColor");
                return null;
            }

            // SELECT `bib_color`,`font_color` FROM `race_bib_color` WHERE `grade`=? AND `race_id`=?;
            if (!query.BindInt(1, grade))  { return null; }
            if (!query.BindInt(2, raceId)) { return null; }

            RaceBibColor orm = null;

            if (query.Step())
            {
                int bibColor  = (int)query.GetInt(0);
                int fontColor = (int)query.GetInt(1);

                orm = new RaceBibColor(grade, raceId, bibColor, fontColor);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0} {1}", grade, raceId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceBibColor()) {
                while (query.Step()) {
                    int grade     = (int)query.GetInt(0);
                    int raceId    = (int)query.GetInt(1);
                    int bibColor  = (int)query.GetInt(2);
                    int fontColor = (int)query.GetInt(3);

                    ulong key = ((uint)unchecked((ulong)((int)grade))) | ((((ulong)unchecked((ulong)((int)raceId)))) << 32);
                    RaceBibColor orm = new RaceBibColor(grade, raceId, bibColor, fontColor);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceBibColor
        {
            /// <summary> (CSV column: grade) </summary>
            public readonly int Grade;
            /// <summary> (CSV column: race_id) </summary>
            public readonly int RaceId;
            /// <summary> (CSV column: bib_color) </summary>
            public readonly int BibColor;
            /// <summary> (CSV column: font_color) </summary>
            public readonly int FontColor;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceBibColor(int grade = 0, int raceId = 0, int bibColor = 0, int fontColor = 0)
            {
                this.Grade     = grade;
                this.RaceId    = raceId;
                this.BibColor  = bibColor;
                this.FontColor = fontColor;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:course_set], [:group]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRace : AbstractMasterData
    {
        public const string TABLE_NAME = "race";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, Race> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<Race>> _dictionaryWithCourseSet = null;
        private Dictionary<int, List<Race>> _dictionaryWithGroup = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, Race> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRace");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRace(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, Race>();
            _dictionaryWithCourseSet = new Dictionary<int, List<Race>>();
            _dictionaryWithGroup = new Dictionary<int, List<Race>>();
            _db = db;
        }


        public Race Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRace");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRace", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private Race _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_Race();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for Race");
                return null;
            }

            // SELECT `group`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            Race orm = null;

            if (query.Step())
            {
                int group                 = (int)query.GetInt(0);
                int grade                 = (int)query.GetInt(1);
                int courseSet             = (int)query.GetInt(2);
                int thumbnailId           = (int)query.GetInt(3);
                string ffCueName          = query.GetText(4);
                string ffCuesheetName     = query.GetText(5);
                string realFfCueName      = query.GetText(6);
                string realFfCuesheetName = query.GetText(7);
                int ffAnim                = (int)query.GetInt(8);
                int ffCamera              = (int)query.GetInt(9);
                int ffCameraSub           = (int)query.GetInt(10);
                int ffSub                 = (int)query.GetInt(11);
                int startGate             = (int)query.GetInt(12);
                int startGatePanel        = (int)query.GetInt(13);
                int goalGate              = (int)query.GetInt(14);
                int goalFlower            = (int)query.GetInt(15);
                int resultPodium          = (int)query.GetInt(16);
                int audienceGroupId       = (int)query.GetInt(17);
                int paddockBgId           = (int)query.GetInt(18);
                int audience              = (int)query.GetInt(19);
                int entryNum              = (int)query.GetInt(20);
                int changeFullGate        = (int)query.GetInt(21);
                int isDirtgrade           = (int)query.GetInt(22);
                long startDate            = (long)query.GetLong(23);

                orm = new Race(id, group, grade, courseSet, thumbnailId, ffCueName, ffCuesheetName, realFfCueName, realFfCuesheetName, ffAnim, ffCamera, ffCameraSub, ffSub, startGate, startGatePanel, goalGate, goalFlower, resultPodium, audienceGroupId, paddockBgId, audience, entryNum, changeFullGate, isDirtgrade, startDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public Race GetWithCourseSetOrderByIdAsc(int courseSet)
        {
            Race orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCourseSetOrderByIdAsc(courseSet);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", courseSet));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private Race _SelectWithCourseSetOrderByIdAsc(int courseSet)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_Race_CourseSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for Race");
                return null;
            }

            // SELECT `id`,`group`,`grade`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `course_set`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, courseSet)) { return null; }

            Race orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCourseSetOrderByIdAsc(query, courseSet);
            }

            query.Reset();

            return orm;
        }

        public List<Race> GetListWithCourseSetOrderByIdAsc(int courseSet)
        {
            int key = (int)courseSet;

            if (!_dictionaryWithCourseSet.ContainsKey(key)) {
                _dictionaryWithCourseSet.Add(key, _ListSelectWithCourseSetOrderByIdAsc(courseSet));
            }

            return _dictionaryWithCourseSet[key];
        }

        public List<Race> MaybeListWithCourseSetOrderByIdAsc(int courseSet)
        {
            List<Race> list = GetListWithCourseSetOrderByIdAsc(courseSet);
            return list.Count > 0 ? list : null;
        }

        private List<Race> _ListSelectWithCourseSetOrderByIdAsc(int courseSet)
        {
            List<Race> _list = new List<Race>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_Race_CourseSet();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for Race");
                return null;
            }

            // SELECT `id`,`group`,`grade`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `course_set`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, courseSet)) { return null; }

            while (query.Step()) {
                Race orm = _CreateOrmByQueryResultWithCourseSetOrderByIdAsc(query, courseSet);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private Race _CreateOrmByQueryResultWithCourseSetOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int courseSet)
        {
            int id                    = (int)query.GetInt(0);
            int group                 = (int)query.GetInt(1);
            int grade                 = (int)query.GetInt(2);
            int thumbnailId           = (int)query.GetInt(3);
            string ffCueName          = query.GetText(4);
            string ffCuesheetName     = query.GetText(5);
            string realFfCueName      = query.GetText(6);
            string realFfCuesheetName = query.GetText(7);
            int ffAnim                = (int)query.GetInt(8);
            int ffCamera              = (int)query.GetInt(9);
            int ffCameraSub           = (int)query.GetInt(10);
            int ffSub                 = (int)query.GetInt(11);
            int startGate             = (int)query.GetInt(12);
            int startGatePanel        = (int)query.GetInt(13);
            int goalGate              = (int)query.GetInt(14);
            int goalFlower            = (int)query.GetInt(15);
            int resultPodium          = (int)query.GetInt(16);
            int audienceGroupId       = (int)query.GetInt(17);
            int paddockBgId           = (int)query.GetInt(18);
            int audience              = (int)query.GetInt(19);
            int entryNum              = (int)query.GetInt(20);
            int changeFullGate        = (int)query.GetInt(21);
            int isDirtgrade           = (int)query.GetInt(22);
            long startDate            = (long)query.GetLong(23);

            return new Race(id, group, grade, courseSet, thumbnailId, ffCueName, ffCuesheetName, realFfCueName, realFfCuesheetName, ffAnim, ffCamera, ffCameraSub, ffSub, startGate, startGatePanel, goalGate, goalFlower, resultPodium, audienceGroupId, paddockBgId, audience, entryNum, changeFullGate, isDirtgrade, startDate);
        }

        public Race GetWithGroupOrderByIdAsc(int group)
        {
            Race orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupOrderByIdAsc(group);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", group));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private Race _SelectWithGroupOrderByIdAsc(int group)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_Race_Group();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for Race");
                return null;
            }

            // SELECT `id`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `group`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, group)) { return null; }

            Race orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupOrderByIdAsc(query, group);
            }

            query.Reset();

            return orm;
        }

        public List<Race> GetListWithGroupOrderByIdAsc(int group)
        {
            int key = (int)group;

            if (!_dictionaryWithGroup.ContainsKey(key)) {
                _dictionaryWithGroup.Add(key, _ListSelectWithGroupOrderByIdAsc(group));
            }

            return _dictionaryWithGroup[key];
        }

        public List<Race> MaybeListWithGroupOrderByIdAsc(int group)
        {
            List<Race> list = GetListWithGroupOrderByIdAsc(group);
            return list.Count > 0 ? list : null;
        }

        private List<Race> _ListSelectWithGroupOrderByIdAsc(int group)
        {
            List<Race> _list = new List<Race>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_Race_Group();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for Race");
                return null;
            }

            // SELECT `id`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `group`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, group)) { return null; }

            while (query.Step()) {
                Race orm = _CreateOrmByQueryResultWithGroupOrderByIdAsc(query, group);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private Race _CreateOrmByQueryResultWithGroupOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int group)
        {
            int id                    = (int)query.GetInt(0);
            int grade                 = (int)query.GetInt(1);
            int courseSet             = (int)query.GetInt(2);
            int thumbnailId           = (int)query.GetInt(3);
            string ffCueName          = query.GetText(4);
            string ffCuesheetName     = query.GetText(5);
            string realFfCueName      = query.GetText(6);
            string realFfCuesheetName = query.GetText(7);
            int ffAnim                = (int)query.GetInt(8);
            int ffCamera              = (int)query.GetInt(9);
            int ffCameraSub           = (int)query.GetInt(10);
            int ffSub                 = (int)query.GetInt(11);
            int startGate             = (int)query.GetInt(12);
            int startGatePanel        = (int)query.GetInt(13);
            int goalGate              = (int)query.GetInt(14);
            int goalFlower            = (int)query.GetInt(15);
            int resultPodium          = (int)query.GetInt(16);
            int audienceGroupId       = (int)query.GetInt(17);
            int paddockBgId           = (int)query.GetInt(18);
            int audience              = (int)query.GetInt(19);
            int entryNum              = (int)query.GetInt(20);
            int changeFullGate        = (int)query.GetInt(21);
            int isDirtgrade           = (int)query.GetInt(22);
            long startDate            = (long)query.GetLong(23);

            return new Race(id, group, grade, courseSet, thumbnailId, ffCueName, ffCuesheetName, realFfCueName, realFfCuesheetName, ffAnim, ffCamera, ffCameraSub, ffSub, startGate, startGatePanel, goalGate, goalFlower, resultPodium, audienceGroupId, paddockBgId, audience, entryNum, changeFullGate, isDirtgrade, startDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCourseSet.Clear();
            _dictionaryWithGroup.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_Race()) {
                while (query.Step()) {
                    int id                    = (int)query.GetInt(0);
                    int group                 = (int)query.GetInt(1);
                    int grade                 = (int)query.GetInt(2);
                    int courseSet             = (int)query.GetInt(3);
                    int thumbnailId           = (int)query.GetInt(4);
                    string ffCueName          = query.GetText(5);
                    string ffCuesheetName     = query.GetText(6);
                    string realFfCueName      = query.GetText(7);
                    string realFfCuesheetName = query.GetText(8);
                    int ffAnim                = (int)query.GetInt(9);
                    int ffCamera              = (int)query.GetInt(10);
                    int ffCameraSub           = (int)query.GetInt(11);
                    int ffSub                 = (int)query.GetInt(12);
                    int startGate             = (int)query.GetInt(13);
                    int startGatePanel        = (int)query.GetInt(14);
                    int goalGate              = (int)query.GetInt(15);
                    int goalFlower            = (int)query.GetInt(16);
                    int resultPodium          = (int)query.GetInt(17);
                    int audienceGroupId       = (int)query.GetInt(18);
                    int paddockBgId           = (int)query.GetInt(19);
                    int audience              = (int)query.GetInt(20);
                    int entryNum              = (int)query.GetInt(21);
                    int changeFullGate        = (int)query.GetInt(22);
                    int isDirtgrade           = (int)query.GetInt(23);
                    long startDate            = (long)query.GetLong(24);

                    int key = (int)id;
                    Race orm = new Race(id, group, grade, courseSet, thumbnailId, ffCueName, ffCuesheetName, realFfCueName, realFfCuesheetName, ffAnim, ffCamera, ffCameraSub, ffSub, startGate, startGatePanel, goalGate, goalFlower, resultPodium, audienceGroupId, paddockBgId, audience, entryNum, changeFullGate, isDirtgrade, startDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class Race
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: group) </summary>
            public readonly int Group;
            /// <summary> (CSV column: grade) </summary>
            public readonly int Grade;
            /// <summary> (CSV column: course_set) </summary>
            private readonly int _courseSet;
            /// <summary> (CSV column: thumbnail_id) </summary>
            public readonly int ThumbnailId;
            /// <summary> (CSV column: ff_cue_name) </summary>
            public readonly string FfCueName;
            /// <summary> (CSV column: ff_cuesheet_name) </summary>
            public readonly string FfCuesheetName;
            /// <summary> (CSV column: real_ff_cue_name) </summary>
            public readonly string RealFfCueName;
            /// <summary> (CSV column: real_ff_cuesheet_name) </summary>
            public readonly string RealFfCuesheetName;
            /// <summary> (CSV column: ff_anim) </summary>
            public readonly int FfAnim;
            /// <summary> (CSV column: ff_camera) </summary>
            public readonly int FfCamera;
            /// <summary> (CSV column: ff_camera_sub) </summary>
            public readonly int FfCameraSub;
            /// <summary> (CSV column: ff_sub) </summary>
            public readonly int FfSub;
            /// <summary> (CSV column: start_gate) </summary>
            public readonly int StartGate;
            /// <summary> (CSV column: start_gate_panel) </summary>
            public readonly int StartGatePanel;
            /// <summary> (CSV column: goal_gate) </summary>
            public readonly int GoalGate;
            /// <summary> (CSV column: goal_flower) </summary>
            public readonly int GoalFlower;
            /// <summary> (CSV column: result_podium) </summary>
            public readonly int ResultPodium;
            /// <summary> (CSV column: audience_group_id) </summary>
            public readonly int AudienceGroupId;
            /// <summary> (CSV column: paddock_bg_id) </summary>
            public readonly int PaddockBgId;
            /// <summary> (CSV column: audience) </summary>
            public readonly int Audience;
            /// <summary> (CSV column: entry_num) </summary>
            public readonly int EntryNum;
            /// <summary> (CSV column: change_full_gate) </summary>
            public readonly int ChangeFullGate;
            /// <summary> (CSV column: is_dirtgrade) </summary>
            public readonly int IsDirtgrade;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public Race(int id = 0, int group = 0, int grade = 0, int courseSet = 0, int thumbnailId = 0, string ffCueName = "", string ffCuesheetName = "", string realFfCueName = "", string realFfCuesheetName = "", int ffAnim = 0, int ffCamera = 0, int ffCameraSub = 0, int ffSub = 0, int startGate = 0, int startGatePanel = 0, int goalGate = 0, int goalFlower = 0, int resultPodium = 0, int audienceGroupId = 0, int paddockBgId = 0, int audience = 0, int entryNum = 0, int changeFullGate = 0, int isDirtgrade = 0, long startDate = 0)
            {
                this.Id                 = id;
                this.Group              = group;
                this.Grade              = grade;
                this._courseSet         = courseSet;
                this.ThumbnailId        = thumbnailId;
                this.FfCueName          = ffCueName;
                this.FfCuesheetName     = ffCuesheetName;
                this.RealFfCueName      = realFfCueName;
                this.RealFfCuesheetName = realFfCuesheetName;
                this.FfAnim             = ffAnim;
                this.FfCamera           = ffCamera;
                this.FfCameraSub        = ffCameraSub;
                this.FfSub              = ffSub;
                this.StartGate          = startGate;
                this.StartGatePanel     = startGatePanel;
                this.GoalGate           = goalGate;
                this.GoalFlower         = goalFlower;
                this.ResultPodium       = resultPodium;
                this.AudienceGroupId    = audienceGroupId;
                this.PaddockBgId        = paddockBgId;
                this.Audience           = audience;
                this.EntryNum           = entryNum;
                this.ChangeFullGate     = changeFullGate;
                this.IsDirtgrade        = isDirtgrade;
                this.StartDate          = startDate;
            }
        }
    }
}
#endif

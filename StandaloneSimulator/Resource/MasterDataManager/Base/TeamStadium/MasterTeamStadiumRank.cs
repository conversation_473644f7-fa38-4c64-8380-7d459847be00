// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_rank
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumRank : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_rank";

        MasterTeamStadiumDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadiumRank> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, TeamStadiumRank> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterTeamStadiumRank");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumRank(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumRank>();
            _db = db;
        }


        public TeamStadiumRank Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumRank");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumRank", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumRank _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumRank();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRank");
                return null;
            }

            // SELECT `team_rank`,`team_rank_group`,`team_min_value`,`team_max_value`,`item_category`,`item_id`,`item_num` FROM `team_stadium_rank` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadiumRank orm = null;

            if (query.Step())
            {
                int teamRank      = (int)query.GetInt(0);
                int teamRankGroup = (int)query.GetInt(1);
                int teamMinValue  = (int)query.GetInt(2);
                int teamMaxValue  = (int)query.GetInt(3);
                int itemCategory  = (int)query.GetInt(4);
                int itemId        = (int)query.GetInt(5);
                int itemNum       = (int)query.GetInt(6);

                orm = new TeamStadiumRank(id, teamRank, teamRankGroup, teamMinValue, teamMaxValue, itemCategory, itemId, itemNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_TeamStadiumRank()) {
                while (query.Step()) {
                    int id            = (int)query.GetInt(0);
                    int teamRank      = (int)query.GetInt(1);
                    int teamRankGroup = (int)query.GetInt(2);
                    int teamMinValue  = (int)query.GetInt(3);
                    int teamMaxValue  = (int)query.GetInt(4);
                    int itemCategory  = (int)query.GetInt(5);
                    int itemId        = (int)query.GetInt(6);
                    int itemNum       = (int)query.GetInt(7);

                    int key = (int)id;
                    TeamStadiumRank orm = new TeamStadiumRank(id, teamRank, teamRankGroup, teamMinValue, teamMaxValue, itemCategory, itemId, itemNum);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class TeamStadiumRank
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: team_rank) </summary>
            public readonly int TeamRank;
            /// <summary> (CSV column: team_rank_group) </summary>
            public readonly int TeamRankGroup;
            /// <summary> (CSV column: team_min_value) </summary>
            public readonly int TeamMinValue;
            /// <summary> (CSV column: team_max_value) </summary>
            public readonly int TeamMaxValue;
            /// <summary> (CSV column: item_category) </summary>
            public readonly int ItemCategory;
            /// <summary> (CSV column: item_id) </summary>
            public readonly int ItemId;
            /// <summary> (CSV column: item_num) </summary>
            public readonly int ItemNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumRank(int id = 0, int teamRank = 0, int teamRankGroup = 0, int teamMinValue = 0, int teamMaxValue = 0, int itemCategory = 0, int itemId = 0, int itemNum = 0)
            {
                this.Id            = id;
                this.TeamRank      = teamRank;
                this.TeamRankGroup = teamRankGroup;
                this.TeamMinValue  = teamMinValue;
                this.TeamMaxValue  = teamMaxValue;
                this.ItemCategory  = itemCategory;
                this.ItemId        = itemId;
                this.ItemNum       = itemNum;
            }
        }
    }
}
#endif

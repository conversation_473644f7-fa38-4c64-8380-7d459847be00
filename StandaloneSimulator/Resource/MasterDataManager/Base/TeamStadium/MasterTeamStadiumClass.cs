// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_class
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:team_stadium_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumClass : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_class";

        MasterTeamStadiumDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadiumClass> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<TeamStadiumClass>> _dictionaryWithTeamStadiumId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumClass(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumClass>();
            _dictionaryWithTeamStadiumId = new Dictionary<int, List<TeamStadiumClass>>();
            _db = db;
        }


        public TeamStadiumClass Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumClass");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumClass", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumClass _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumClass();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumClass");
                return null;
            }

            // SELECT `team_stadium_id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadiumClass orm = null;

            if (query.Step())
            {
                int teamStadiumId  = (int)query.GetInt(0);
                int teamClass      = (int)query.GetInt(1);
                int unitMaxNum     = (int)query.GetInt(2);
                int classUpRange   = (int)query.GetInt(3);
                int classDownRange = (int)query.GetInt(4);

                orm = new TeamStadiumClass(id, teamStadiumId, teamClass, unitMaxNum, classUpRange, classDownRange);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TeamStadiumClass GetWithTeamStadiumIdOrderByTeamClassAsc(int teamStadiumId)
        {
            TeamStadiumClass orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithTeamStadiumIdOrderByTeamClassAsc(teamStadiumId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", teamStadiumId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamStadiumClass _SelectWithTeamStadiumIdOrderByTeamClassAsc(int teamStadiumId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumClass_TeamStadiumId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumClass");
                return null;
            }

            // SELECT `id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class` WHERE `team_stadium_id`=? ORDER BY `team_class` ASC;
            if (!query.BindInt(1, teamStadiumId)) { return null; }

            TeamStadiumClass orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithTeamStadiumIdOrderByTeamClassAsc(query, teamStadiumId);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumClass> GetListWithTeamStadiumIdOrderByTeamClassAsc(int teamStadiumId)
        {
            int key = (int)teamStadiumId;

            if (!_dictionaryWithTeamStadiumId.ContainsKey(key)) {
                _dictionaryWithTeamStadiumId.Add(key, _ListSelectWithTeamStadiumIdOrderByTeamClassAsc(teamStadiumId));
            }

            return _dictionaryWithTeamStadiumId[key];
        }

        public List<TeamStadiumClass> MaybeListWithTeamStadiumIdOrderByTeamClassAsc(int teamStadiumId)
        {
            List<TeamStadiumClass> list = GetListWithTeamStadiumIdOrderByTeamClassAsc(teamStadiumId);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumClass> _ListSelectWithTeamStadiumIdOrderByTeamClassAsc(int teamStadiumId)
        {
            List<TeamStadiumClass> _list = new List<TeamStadiumClass>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumClass_TeamStadiumId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumClass");
                return null;
            }

            // SELECT `id`,`team_class`,`unit_max_num`,`class_up_range`,`class_down_range` FROM `team_stadium_class` WHERE `team_stadium_id`=? ORDER BY `team_class` ASC;
            if (!query.BindInt(1, teamStadiumId)) { return null; }

            while (query.Step()) {
                TeamStadiumClass orm = _CreateOrmByQueryResultWithTeamStadiumIdOrderByTeamClassAsc(query, teamStadiumId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumClass _CreateOrmByQueryResultWithTeamStadiumIdOrderByTeamClassAsc(LibNative.Sqlite3.PreparedQuery query, int teamStadiumId)
        {
            int id             = (int)query.GetInt(0);
            int teamClass      = (int)query.GetInt(1);
            int unitMaxNum     = (int)query.GetInt(2);
            int classUpRange   = (int)query.GetInt(3);
            int classDownRange = (int)query.GetInt(4);

            return new TeamStadiumClass(id, teamStadiumId, teamClass, unitMaxNum, classUpRange, classDownRange);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithTeamStadiumId.Clear();
        }

        public sealed partial class TeamStadiumClass
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: team_stadium_id) </summary>
            public readonly int TeamStadiumId;
            /// <summary> (CSV column: team_class) </summary>
            public readonly int TeamClass;
            /// <summary> (CSV column: unit_max_num) </summary>
            public readonly int UnitMaxNum;
            /// <summary> (CSV column: class_up_range) </summary>
            public readonly int ClassUpRange;
            /// <summary> (CSV column: class_down_range) </summary>
            public readonly int ClassDownRange;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumClass(int id = 0, int teamStadiumId = 0, int teamClass = 0, int unitMaxNum = 0, int classUpRange = 0, int classDownRange = 0)
            {
                this.Id             = id;
                this.TeamStadiumId  = teamStadiumId;
                this.TeamClass      = teamClass;
                this.UnitMaxNum     = unitMaxNum;
                this.ClassUpRange   = classUpRange;
                this.ClassDownRange = classDownRange;
            }
        }
    }
}
#endif

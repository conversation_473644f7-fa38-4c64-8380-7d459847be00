// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_raw_score
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:condition_type], [:race_score_name_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumRawScore : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_raw_score";

        MasterTeamStadiumDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadiumRawScore> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<TeamStadiumRawScore>> _dictionaryWithConditionType = null;
        private Dictionary<int, List<TeamStadiumRawScore>> _dictionaryWithRaceScoreNameId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, TeamStadiumRawScore> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterTeamStadiumRawScore");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumRawScore(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumRawScore>();
            _dictionaryWithConditionType = new Dictionary<int, List<TeamStadiumRawScore>>();
            _dictionaryWithRaceScoreNameId = new Dictionary<int, List<TeamStadiumRawScore>>();
            _db = db;
        }


        public TeamStadiumRawScore Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumRawScore");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumRawScore", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumRawScore _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumRawScore();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRawScore");
                return null;
            }

            // SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadiumRawScore orm = null;

            if (query.Step())
            {
                int priority        = (int)query.GetInt(0);
                int conditionType   = (int)query.GetInt(1);
                int conditionValue1 = (int)query.GetInt(2);
                int conditionValue2 = (int)query.GetInt(3);
                int score           = (int)query.GetInt(4);
                int raceScoreNameId = (int)query.GetInt(5);
                int sortOrder       = (int)query.GetInt(6);

                orm = new TeamStadiumRawScore(id, priority, conditionType, conditionValue1, conditionValue2, score, raceScoreNameId, sortOrder);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TeamStadiumRawScore GetWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            TeamStadiumRawScore orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionTypeOrderByPriorityAsc(conditionType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamStadiumRawScore _SelectWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumRawScore_ConditionType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRawScore");
                return null;
            }

            // SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score` WHERE `condition_type`=? ORDER BY `priority` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            TeamStadiumRawScore orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(query, conditionType);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumRawScore> GetListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            int key = (int)conditionType;

            if (!_dictionaryWithConditionType.ContainsKey(key)) {
                _dictionaryWithConditionType.Add(key, _ListSelectWithConditionTypeOrderByPriorityAsc(conditionType));
            }

            return _dictionaryWithConditionType[key];
        }

        public List<TeamStadiumRawScore> MaybeListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            List<TeamStadiumRawScore> list = GetListWithConditionTypeOrderByPriorityAsc(conditionType);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumRawScore> _ListSelectWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            List<TeamStadiumRawScore> _list = new List<TeamStadiumRawScore>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumRawScore_ConditionType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRawScore");
                return null;
            }

            // SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score`,`race_score_name_id`,`sort_order` FROM `team_stadium_raw_score` WHERE `condition_type`=? ORDER BY `priority` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            while (query.Step()) {
                TeamStadiumRawScore orm = _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(query, conditionType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumRawScore _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(LibNative.Sqlite3.PreparedQuery query, int conditionType)
        {
            int id              = (int)query.GetInt(0);
            int priority        = (int)query.GetInt(1);
            int conditionValue1 = (int)query.GetInt(2);
            int conditionValue2 = (int)query.GetInt(3);
            int score           = (int)query.GetInt(4);
            int raceScoreNameId = (int)query.GetInt(5);
            int sortOrder       = (int)query.GetInt(6);

            return new TeamStadiumRawScore(id, priority, conditionType, conditionValue1, conditionValue2, score, raceScoreNameId, sortOrder);
        }

        public TeamStadiumRawScore GetWithRaceScoreNameIdOrderByIdAsc(int raceScoreNameId)
        {
            TeamStadiumRawScore orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceScoreNameIdOrderByIdAsc(raceScoreNameId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceScoreNameId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamStadiumRawScore _SelectWithRaceScoreNameIdOrderByIdAsc(int raceScoreNameId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumRawScore_RaceScoreNameId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRawScore");
                return null;
            }

            // SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`sort_order` FROM `team_stadium_raw_score` WHERE `race_score_name_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceScoreNameId)) { return null; }

            TeamStadiumRawScore orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceScoreNameIdOrderByIdAsc(query, raceScoreNameId);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumRawScore> GetListWithRaceScoreNameIdOrderByIdAsc(int raceScoreNameId)
        {
            int key = (int)raceScoreNameId;

            if (!_dictionaryWithRaceScoreNameId.ContainsKey(key)) {
                _dictionaryWithRaceScoreNameId.Add(key, _ListSelectWithRaceScoreNameIdOrderByIdAsc(raceScoreNameId));
            }

            return _dictionaryWithRaceScoreNameId[key];
        }

        public List<TeamStadiumRawScore> MaybeListWithRaceScoreNameIdOrderByIdAsc(int raceScoreNameId)
        {
            List<TeamStadiumRawScore> list = GetListWithRaceScoreNameIdOrderByIdAsc(raceScoreNameId);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumRawScore> _ListSelectWithRaceScoreNameIdOrderByIdAsc(int raceScoreNameId)
        {
            List<TeamStadiumRawScore> _list = new List<TeamStadiumRawScore>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumRawScore_RaceScoreNameId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRawScore");
                return null;
            }

            // SELECT `id`,`priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score`,`sort_order` FROM `team_stadium_raw_score` WHERE `race_score_name_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceScoreNameId)) { return null; }

            while (query.Step()) {
                TeamStadiumRawScore orm = _CreateOrmByQueryResultWithRaceScoreNameIdOrderByIdAsc(query, raceScoreNameId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumRawScore _CreateOrmByQueryResultWithRaceScoreNameIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int raceScoreNameId)
        {
            int id              = (int)query.GetInt(0);
            int priority        = (int)query.GetInt(1);
            int conditionType   = (int)query.GetInt(2);
            int conditionValue1 = (int)query.GetInt(3);
            int conditionValue2 = (int)query.GetInt(4);
            int score           = (int)query.GetInt(5);
            int sortOrder       = (int)query.GetInt(6);

            return new TeamStadiumRawScore(id, priority, conditionType, conditionValue1, conditionValue2, score, raceScoreNameId, sortOrder);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithConditionType.Clear();
            _dictionaryWithRaceScoreNameId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_TeamStadiumRawScore()) {
                while (query.Step()) {
                    int id              = (int)query.GetInt(0);
                    int priority        = (int)query.GetInt(1);
                    int conditionType   = (int)query.GetInt(2);
                    int conditionValue1 = (int)query.GetInt(3);
                    int conditionValue2 = (int)query.GetInt(4);
                    int score           = (int)query.GetInt(5);
                    int raceScoreNameId = (int)query.GetInt(6);
                    int sortOrder       = (int)query.GetInt(7);

                    int key = (int)id;
                    TeamStadiumRawScore orm = new TeamStadiumRawScore(id, priority, conditionType, conditionValue1, conditionValue2, score, raceScoreNameId, sortOrder);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class TeamStadiumRawScore
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: condition_value_1) </summary>
            public readonly int ConditionValue1;
            /// <summary> (CSV column: condition_value_2) </summary>
            public readonly int ConditionValue2;
            /// <summary> (CSV column: score) </summary>
            public readonly int Score;
            /// <summary> (CSV column: race_score_name_id) </summary>
            public readonly int RaceScoreNameId;
            /// <summary> (CSV column: sort_order) </summary>
            public readonly int SortOrder;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumRawScore(int id = 0, int priority = 0, int conditionType = 0, int conditionValue1 = 0, int conditionValue2 = 0, int score = 0, int raceScoreNameId = 0, int sortOrder = 0)
            {
                this.Id              = id;
                this.Priority        = priority;
                this.ConditionType   = conditionType;
                this.ConditionValue1 = conditionValue1;
                this.ConditionValue2 = conditionValue2;
                this.Score           = score;
                this.RaceScoreNameId = raceScoreNameId;
                this.SortOrder       = sortOrder;
            }
        }
    }
}
#endif

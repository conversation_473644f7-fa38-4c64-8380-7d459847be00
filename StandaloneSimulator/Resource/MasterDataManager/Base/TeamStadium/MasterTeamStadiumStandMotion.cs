// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_stand_motion
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:character_id, :type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumStandMotion : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_stand_motion";

        MasterTeamStadiumDatabase _db = null;


        // cache dictionary
        private Dictionary<int, TeamStadiumStandMotion> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<TeamStadiumStandMotion>> _dictionaryWithCharacterIdAndType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumStandMotion(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumStandMotion>();
            _dictionaryWithCharacterIdAndType = new Dictionary<ulong, List<TeamStadiumStandMotion>>();
            _db = db;
        }



        public TeamStadiumStandMotion GetWithCharacterIdAndType(int characterId, int type)
        {
            TeamStadiumStandMotion orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharacterIdAndType(characterId, type);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", characterId, type));
                }
            }

            return orm;
        }

        private TeamStadiumStandMotion _SelectWithCharacterIdAndType(int characterId, int type)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumStandMotion_CharacterId_Type();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumStandMotion");
                return null;
            }

            // SELECT `race_dress_id`,`position`,`motion_set`,`rotation`,`position_x` FROM `team_stadium_stand_motion` WHERE `character_id`=? AND `type`=?;
            if (!query.BindInt(1, characterId)) { return null; }
            if (!query.BindInt(2, type))        { return null; }

            TeamStadiumStandMotion orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharacterIdAndType(query, characterId, type);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumStandMotion> GetListWithCharacterIdAndType(int characterId, int type)
        {
            ulong key = ((uint)unchecked((ulong)((int)characterId))) | ((((ulong)unchecked((ulong)((int)type)))) << 32);

            if (!_dictionaryWithCharacterIdAndType.ContainsKey(key)) {
                _dictionaryWithCharacterIdAndType.Add(key, _ListSelectWithCharacterIdAndType(characterId, type));
            }

            return _dictionaryWithCharacterIdAndType[key];
        }

        public List<TeamStadiumStandMotion> MaybeListWithCharacterIdAndType(int characterId, int type)
        {
            List<TeamStadiumStandMotion> list = GetListWithCharacterIdAndType(characterId, type);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumStandMotion> _ListSelectWithCharacterIdAndType(int characterId, int type)
        {
            List<TeamStadiumStandMotion> _list = new List<TeamStadiumStandMotion>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumStandMotion_CharacterId_Type();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumStandMotion");
                return null;
            }

            // SELECT `race_dress_id`,`position`,`motion_set`,`rotation`,`position_x` FROM `team_stadium_stand_motion` WHERE `character_id`=? AND `type`=?;
            if (!query.BindInt(1, characterId)) { return null; }
            if (!query.BindInt(2, type))        { return null; }

            while (query.Step()) {
                TeamStadiumStandMotion orm = _CreateOrmByQueryResultWithCharacterIdAndType(query, characterId, type);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumStandMotion _CreateOrmByQueryResultWithCharacterIdAndType(LibNative.Sqlite3.PreparedQuery query, int characterId, int type)
        {
            int raceDressId = (int)query.GetInt(0);
            int position    = (int)query.GetInt(1);
            int motionSet   = (int)query.GetInt(2);
            int rotation    = (int)query.GetInt(3);
            int positionX   = (int)query.GetInt(4);

            return new TeamStadiumStandMotion(characterId, type, raceDressId, position, motionSet, rotation, positionX);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharacterIdAndType.Clear();
        }

        public sealed partial class TeamStadiumStandMotion
        {
            /// <summary> (CSV column: character_id) </summary>
            public readonly int CharacterId;
            /// <summary> (CSV column: type) </summary>
            public readonly int Type;
            /// <summary> (CSV column: race_dress_id) </summary>
            public readonly int RaceDressId;
            /// <summary> (CSV column: position) </summary>
            public readonly int Position;
            /// <summary> (CSV column: motion_set) </summary>
            public readonly int MotionSet;
            /// <summary> (CSV column: rotation) </summary>
            public readonly int Rotation;
            /// <summary> (CSV column: position_x) </summary>
            public readonly int PositionX;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumStandMotion(int characterId = 0, int type = 0, int raceDressId = 0, int position = 0, int motionSet = 0, int rotation = 0, int positionX = 0)
            {
                this.CharacterId = characterId;
                this.Type        = type;
                this.RaceDressId = raceDressId;
                this.Position    = position;
                this.MotionSet   = motionSet;
                this.Rotation    = rotation;
                this.PositionX   = positionX;
            }
        }
    }
}
#endif

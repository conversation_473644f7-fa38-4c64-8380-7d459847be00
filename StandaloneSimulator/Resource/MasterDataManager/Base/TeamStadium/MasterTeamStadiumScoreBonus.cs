// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_score_bonus
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:condition_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumScoreBonus : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_score_bonus";

        MasterTeamStadiumDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadiumScoreBonus> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<TeamStadiumScoreBonus>> _dictionaryWithConditionType = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, TeamStadiumScoreBonus> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterTeamStadiumScoreBonus");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumScoreBonus(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumScoreBonus>();
            _dictionaryWithConditionType = new Dictionary<int, List<TeamStadiumScoreBonus>>();
            _db = db;
        }


        public TeamStadiumScoreBonus Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumScoreBonus");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumScoreBonus", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumScoreBonus _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumScoreBonus();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumScoreBonus");
                return null;
            }

            // SELECT `priority`,`condition_type`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadiumScoreBonus orm = null;

            if (query.Step())
            {
                int priority        = (int)query.GetInt(0);
                int conditionType   = (int)query.GetInt(1);
                int conditionValue1 = (int)query.GetInt(2);
                int conditionValue2 = (int)query.GetInt(3);
                int scoreRate       = (int)query.GetInt(4);

                orm = new TeamStadiumScoreBonus(id, priority, conditionType, conditionValue1, conditionValue2, scoreRate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TeamStadiumScoreBonus GetWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            TeamStadiumScoreBonus orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionTypeOrderByPriorityAsc(conditionType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamStadiumScoreBonus _SelectWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumScoreBonus_ConditionType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumScoreBonus");
                return null;
            }

            // SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus` WHERE `condition_type`=? ORDER BY `priority` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            TeamStadiumScoreBonus orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(query, conditionType);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumScoreBonus> GetListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            int key = (int)conditionType;

            if (!_dictionaryWithConditionType.ContainsKey(key)) {
                _dictionaryWithConditionType.Add(key, _ListSelectWithConditionTypeOrderByPriorityAsc(conditionType));
            }

            return _dictionaryWithConditionType[key];
        }

        public List<TeamStadiumScoreBonus> MaybeListWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            List<TeamStadiumScoreBonus> list = GetListWithConditionTypeOrderByPriorityAsc(conditionType);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumScoreBonus> _ListSelectWithConditionTypeOrderByPriorityAsc(int conditionType)
        {
            List<TeamStadiumScoreBonus> _list = new List<TeamStadiumScoreBonus>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumScoreBonus_ConditionType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumScoreBonus");
                return null;
            }

            // SELECT `id`,`priority`,`condition_value_1`,`condition_value_2`,`score_rate` FROM `team_stadium_score_bonus` WHERE `condition_type`=? ORDER BY `priority` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            while (query.Step()) {
                TeamStadiumScoreBonus orm = _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(query, conditionType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumScoreBonus _CreateOrmByQueryResultWithConditionTypeOrderByPriorityAsc(LibNative.Sqlite3.PreparedQuery query, int conditionType)
        {
            int id              = (int)query.GetInt(0);
            int priority        = (int)query.GetInt(1);
            int conditionValue1 = (int)query.GetInt(2);
            int conditionValue2 = (int)query.GetInt(3);
            int scoreRate       = (int)query.GetInt(4);

            return new TeamStadiumScoreBonus(id, priority, conditionType, conditionValue1, conditionValue2, scoreRate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithConditionType.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_TeamStadiumScoreBonus()) {
                while (query.Step()) {
                    int id              = (int)query.GetInt(0);
                    int priority        = (int)query.GetInt(1);
                    int conditionType   = (int)query.GetInt(2);
                    int conditionValue1 = (int)query.GetInt(3);
                    int conditionValue2 = (int)query.GetInt(4);
                    int scoreRate       = (int)query.GetInt(5);

                    int key = (int)id;
                    TeamStadiumScoreBonus orm = new TeamStadiumScoreBonus(id, priority, conditionType, conditionValue1, conditionValue2, scoreRate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class TeamStadiumScoreBonus
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: condition_value_1) </summary>
            public readonly int ConditionValue1;
            /// <summary> (CSV column: condition_value_2) </summary>
            public readonly int ConditionValue2;
            /// <summary> (CSV column: score_rate) </summary>
            public readonly int ScoreRate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumScoreBonus(int id = 0, int priority = 0, int conditionType = 0, int conditionValue1 = 0, int conditionValue2 = 0, int scoreRate = 0)
            {
                this.Id              = id;
                this.Priority        = priority;
                this.ConditionType   = conditionType;
                this.ConditionValue1 = conditionValue1;
                this.ConditionValue2 = conditionValue2;
                this.ScoreRate       = scoreRate;
            }
        }
    }
}
#endif

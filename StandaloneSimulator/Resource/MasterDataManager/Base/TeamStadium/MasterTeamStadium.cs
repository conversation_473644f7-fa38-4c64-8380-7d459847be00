// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadium : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium";

        MasterTeamStadiumDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadium> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, TeamStadium> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterTeamStadium");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadium(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadium>();
            _db = db;
        }


        public TeamStadium Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadium");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadium", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadium _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadium();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadium");
                return null;
            }

            // SELECT `race_start_date`,`race_start_time`,`race_end_date`,`race_end_time`,`interval_start_date`,`interval_start_time`,`interval_end_date`,`interval_end_time`,`calc_start_date`,`calc_start_time`,`calc_end_date`,`calc_end_time`,`start_date`,`end_date` FROM `team_stadium` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadium orm = null;

            if (query.Step())
            {
                int raceStartDate        = (int)query.GetInt(0);
                string raceStartTime     = query.GetText(1);
                int raceEndDate          = (int)query.GetInt(2);
                string raceEndTime       = query.GetText(3);
                int intervalStartDate    = (int)query.GetInt(4);
                string intervalStartTime = query.GetText(5);
                int intervalEndDate      = (int)query.GetInt(6);
                string intervalEndTime   = query.GetText(7);
                int calcStartDate        = (int)query.GetInt(8);
                string calcStartTime     = query.GetText(9);
                int calcEndDate          = (int)query.GetInt(10);
                string calcEndTime       = query.GetText(11);
                string startDate         = query.GetText(12);
                string endDate           = query.GetText(13);

                orm = new TeamStadium(id, raceStartDate, raceStartTime, raceEndDate, raceEndTime, intervalStartDate, intervalStartTime, intervalEndDate, intervalEndTime, calcStartDate, calcStartTime, calcEndDate, calcEndTime, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_TeamStadium()) {
                while (query.Step()) {
                    int id                   = (int)query.GetInt(0);
                    int raceStartDate        = (int)query.GetInt(1);
                    string raceStartTime     = query.GetText(2);
                    int raceEndDate          = (int)query.GetInt(3);
                    string raceEndTime       = query.GetText(4);
                    int intervalStartDate    = (int)query.GetInt(5);
                    string intervalStartTime = query.GetText(6);
                    int intervalEndDate      = (int)query.GetInt(7);
                    string intervalEndTime   = query.GetText(8);
                    int calcStartDate        = (int)query.GetInt(9);
                    string calcStartTime     = query.GetText(10);
                    int calcEndDate          = (int)query.GetInt(11);
                    string calcEndTime       = query.GetText(12);
                    string startDate         = query.GetText(13);
                    string endDate           = query.GetText(14);

                    int key = (int)id;
                    TeamStadium orm = new TeamStadium(id, raceStartDate, raceStartTime, raceEndDate, raceEndTime, intervalStartDate, intervalStartTime, intervalEndDate, intervalEndTime, calcStartDate, calcStartTime, calcEndDate, calcEndTime, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class TeamStadium
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: race_start_date) </summary>
            public readonly int RaceStartDate;
            /// <summary> (CSV column: race_start_time) </summary>
            public readonly string RaceStartTime;
            /// <summary> (CSV column: race_end_date) </summary>
            public readonly int RaceEndDate;
            /// <summary> (CSV column: race_end_time) </summary>
            public readonly string RaceEndTime;
            /// <summary> (CSV column: interval_start_date) </summary>
            public readonly int IntervalStartDate;
            /// <summary> (CSV column: interval_start_time) </summary>
            public readonly string IntervalStartTime;
            /// <summary> (CSV column: interval_end_date) </summary>
            public readonly int IntervalEndDate;
            /// <summary> (CSV column: interval_end_time) </summary>
            public readonly string IntervalEndTime;
            /// <summary> (CSV column: calc_start_date) </summary>
            public readonly int CalcStartDate;
            /// <summary> (CSV column: calc_start_time) </summary>
            public readonly string CalcStartTime;
            /// <summary> (CSV column: calc_end_date) </summary>
            public readonly int CalcEndDate;
            /// <summary> (CSV column: calc_end_time) </summary>
            public readonly string CalcEndTime;
            /// <summary> (CSV column: start_date) </summary>
            public readonly string StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly string EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadium(int id = 0, int raceStartDate = 0, string raceStartTime = "", int raceEndDate = 0, string raceEndTime = "", int intervalStartDate = 0, string intervalStartTime = "", int intervalEndDate = 0, string intervalEndTime = "", int calcStartDate = 0, string calcStartTime = "", int calcEndDate = 0, string calcEndTime = "", string startDate = "", string endDate = "")
            {
                this.Id                = id;
                this.RaceStartDate     = raceStartDate;
                this.RaceStartTime     = raceStartTime;
                this.RaceEndDate       = raceEndDate;
                this.RaceEndTime       = raceEndTime;
                this.IntervalStartDate = intervalStartDate;
                this.IntervalStartTime = intervalStartTime;
                this.IntervalEndDate   = intervalEndDate;
                this.IntervalEndTime   = intervalEndTime;
                this.CalcStartDate     = calcStartDate;
                this.CalcStartTime     = calcStartTime;
                this.CalcEndDate       = calcEndDate;
                this.CalcEndTime       = calcEndTime;
                this.StartDate         = startDate;
                this.EndDate           = endDate;
            }
        }
    }
}
#endif

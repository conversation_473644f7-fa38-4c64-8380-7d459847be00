// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_race_result_motion
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:character_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumRaceResultMotion : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_race_result_motion";

        MasterTeamStadiumDatabase _db = null;


        // cache dictionary
        private Dictionary<int, TeamStadiumRaceResultMotion> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<TeamStadiumRaceResultMotion>> _dictionaryWithCharacterId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumRaceResultMotion(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumRaceResultMotion>();
            _dictionaryWithCharacterId = new Dictionary<int, List<TeamStadiumRaceResultMotion>>();
            _db = db;
        }



        public TeamStadiumRaceResultMotion GetWithCharacterId(int characterId)
        {
            TeamStadiumRaceResultMotion orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharacterId(characterId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", characterId));
                }
            }

            return orm;
        }

        private TeamStadiumRaceResultMotion _SelectWithCharacterId(int characterId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumRaceResultMotion_CharacterId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRaceResultMotion");
                return null;
            }

            // SELECT `win_1`,`win_2`,`win_2_position_y`,`win_3`,`win_3_position_y`,`win_4`,`win_4_position_y`,`win_5`,`lose_1`,`lose_2`,`lose_3`,`lose_4`,`lose_5`,`draw_1`,`draw_2`,`draw_3`,`draw_4`,`draw_5`,`position_y` FROM `team_stadium_race_result_motion` WHERE `character_id`=?;
            if (!query.BindInt(1, characterId)) { return null; }

            TeamStadiumRaceResultMotion orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharacterId(query, characterId);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumRaceResultMotion> GetListWithCharacterId(int characterId)
        {
            int key = (int)characterId;

            if (!_dictionaryWithCharacterId.ContainsKey(key)) {
                _dictionaryWithCharacterId.Add(key, _ListSelectWithCharacterId(characterId));
            }

            return _dictionaryWithCharacterId[key];
        }

        public List<TeamStadiumRaceResultMotion> MaybeListWithCharacterId(int characterId)
        {
            List<TeamStadiumRaceResultMotion> list = GetListWithCharacterId(characterId);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumRaceResultMotion> _ListSelectWithCharacterId(int characterId)
        {
            List<TeamStadiumRaceResultMotion> _list = new List<TeamStadiumRaceResultMotion>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumRaceResultMotion_CharacterId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumRaceResultMotion");
                return null;
            }

            // SELECT `win_1`,`win_2`,`win_2_position_y`,`win_3`,`win_3_position_y`,`win_4`,`win_4_position_y`,`win_5`,`lose_1`,`lose_2`,`lose_3`,`lose_4`,`lose_5`,`draw_1`,`draw_2`,`draw_3`,`draw_4`,`draw_5`,`position_y` FROM `team_stadium_race_result_motion` WHERE `character_id`=?;
            if (!query.BindInt(1, characterId)) { return null; }

            while (query.Step()) {
                TeamStadiumRaceResultMotion orm = _CreateOrmByQueryResultWithCharacterId(query, characterId);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumRaceResultMotion _CreateOrmByQueryResultWithCharacterId(LibNative.Sqlite3.PreparedQuery query, int characterId)
        {
            int win1          = (int)query.GetInt(0);
            int win2          = (int)query.GetInt(1);
            int win2PositionY = (int)query.GetInt(2);
            int win3          = (int)query.GetInt(3);
            int win3PositionY = (int)query.GetInt(4);
            int win4          = (int)query.GetInt(5);
            int win4PositionY = (int)query.GetInt(6);
            int win5          = (int)query.GetInt(7);
            int lose1         = (int)query.GetInt(8);
            int lose2         = (int)query.GetInt(9);
            int lose3         = (int)query.GetInt(10);
            int lose4         = (int)query.GetInt(11);
            int lose5         = (int)query.GetInt(12);
            int draw1         = (int)query.GetInt(13);
            int draw2         = (int)query.GetInt(14);
            int draw3         = (int)query.GetInt(15);
            int draw4         = (int)query.GetInt(16);
            int draw5         = (int)query.GetInt(17);
            int positionY     = (int)query.GetInt(18);

            return new TeamStadiumRaceResultMotion(characterId, win1, win2, win2PositionY, win3, win3PositionY, win4, win4PositionY, win5, lose1, lose2, lose3, lose4, lose5, draw1, draw2, draw3, draw4, draw5, positionY);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharacterId.Clear();
        }

        public sealed partial class TeamStadiumRaceResultMotion
        {
            /// <summary> (CSV column: character_id) </summary>
            public readonly int CharacterId;
            /// <summary> (CSV column: win_1) </summary>
            public readonly int Win1;
            /// <summary> (CSV column: win_2) </summary>
            public readonly int Win2;
            /// <summary> (CSV column: win_2_position_y) </summary>
            public readonly int Win2PositionY;
            /// <summary> (CSV column: win_3) </summary>
            public readonly int Win3;
            /// <summary> (CSV column: win_3_position_y) </summary>
            public readonly int Win3PositionY;
            /// <summary> (CSV column: win_4) </summary>
            public readonly int Win4;
            /// <summary> (CSV column: win_4_position_y) </summary>
            public readonly int Win4PositionY;
            /// <summary> (CSV column: win_5) </summary>
            public readonly int Win5;
            /// <summary> (CSV column: lose_1) </summary>
            public readonly int Lose1;
            /// <summary> (CSV column: lose_2) </summary>
            public readonly int Lose2;
            /// <summary> (CSV column: lose_3) </summary>
            public readonly int Lose3;
            /// <summary> (CSV column: lose_4) </summary>
            public readonly int Lose4;
            /// <summary> (CSV column: lose_5) </summary>
            public readonly int Lose5;
            /// <summary> (CSV column: draw_1) </summary>
            public readonly int Draw1;
            /// <summary> (CSV column: draw_2) </summary>
            public readonly int Draw2;
            /// <summary> (CSV column: draw_3) </summary>
            public readonly int Draw3;
            /// <summary> (CSV column: draw_4) </summary>
            public readonly int Draw4;
            /// <summary> (CSV column: draw_5) </summary>
            public readonly int Draw5;
            /// <summary> (CSV column: position_y) </summary>
            public readonly int PositionY;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumRaceResultMotion(int characterId = 0, int win1 = 0, int win2 = 0, int win2PositionY = 0, int win3 = 0, int win3PositionY = 0, int win4 = 0, int win4PositionY = 0, int win5 = 0, int lose1 = 0, int lose2 = 0, int lose3 = 0, int lose4 = 0, int lose5 = 0, int draw1 = 0, int draw2 = 0, int draw3 = 0, int draw4 = 0, int draw5 = 0, int positionY = 0)
            {
                this.CharacterId   = characterId;
                this.Win1          = win1;
                this.Win2          = win2;
                this.Win2PositionY = win2PositionY;
                this.Win3          = win3;
                this.Win3PositionY = win3PositionY;
                this.Win4          = win4;
                this.Win4PositionY = win4PositionY;
                this.Win5          = win5;
                this.Lose1         = lose1;
                this.Lose2         = lose2;
                this.Lose3         = lose3;
                this.Lose4         = lose4;
                this.Lose5         = lose5;
                this.Draw1         = draw1;
                this.Draw2         = draw2;
                this.Draw3         = draw3;
                this.Draw4         = draw4;
                this.Draw5         = draw5;
                this.PositionY     = positionY;
            }
        }
    }
}
#endif

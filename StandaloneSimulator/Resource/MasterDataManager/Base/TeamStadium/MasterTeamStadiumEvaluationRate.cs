// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_evaluation_rate
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumEvaluationRate : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_evaluation_rate";

        MasterTeamStadiumDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<ulong> _notFounds = null;

        // cache dictionary
        private Dictionary<ulong, TeamStadiumEvaluationRate> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<ulong, TeamStadiumEvaluationRate> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterTeamStadiumEvaluationRate");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumEvaluationRate(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<ulong, TeamStadiumEvaluationRate>();
            _db = db;
        }


        public ulong GetKey(int properType, int properRank)
        {
            return ((uint)unchecked((ulong)((int)properType))) | ((((ulong)unchecked((ulong)((int)properRank)))) << 32);
        }

        public TeamStadiumEvaluationRate Get(ulong key)
        {
            int properType = (int)unchecked((int)((key >> 0) & 0xFFFFFFFF));
            int properRank = (int)unchecked((int)((key >> 32) & 0xFFFFFFFF));

            return Get(properType, properRank);
        }

        public TeamStadiumEvaluationRate Get(int properType, int properRank)
        {
            ulong key = ((uint)unchecked((ulong)((int)properType))) | ((((ulong)unchecked((ulong)((int)properRank)))) << 32);

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumEvaluationRate");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(properType, properRank);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<ulong>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumEvaluationRate", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumEvaluationRate _SelectOne(int properType, int properRank)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumEvaluationRate();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumEvaluationRate");
                return null;
            }

            // SELECT `id`,`rate` FROM `team_stadium_evaluation_rate` WHERE `proper_type`=? AND `proper_rank`=?;
            if (!query.BindInt(1, properType)) { return null; }
            if (!query.BindInt(2, properRank)) { return null; }

            TeamStadiumEvaluationRate orm = null;

            if (query.Step())
            {
                int id   = (int)query.GetInt(0);
                int rate = (int)query.GetInt(1);

                orm = new TeamStadiumEvaluationRate(id, properType, properRank, rate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0} {1}", properType, properRank));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_TeamStadiumEvaluationRate()) {
                while (query.Step()) {
                    int id         = (int)query.GetInt(0);
                    int properType = (int)query.GetInt(1);
                    int properRank = (int)query.GetInt(2);
                    int rate       = (int)query.GetInt(3);

                    ulong key = ((uint)unchecked((ulong)((int)properType))) | ((((ulong)unchecked((ulong)((int)properRank)))) << 32);
                    TeamStadiumEvaluationRate orm = new TeamStadiumEvaluationRate(id, properType, properRank, rate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class TeamStadiumEvaluationRate
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: proper_type) </summary>
            public readonly int ProperType;
            /// <summary> (CSV column: proper_rank) </summary>
            public readonly int ProperRank;
            /// <summary> (CSV column: rate) </summary>
            public readonly int Rate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumEvaluationRate(int id = 0, int properType = 0, int properRank = 0, int rate = 0)
            {
                this.Id         = id;
                this.ProperType = properType;
                this.ProperRank = properRank;
                this.Rate       = rate;
            }
        }
    }
}
#endif

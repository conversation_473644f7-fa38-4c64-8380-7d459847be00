// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_class_reward
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:team_stadium_id, :class_reward_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumClassReward : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_class_reward";

        MasterTeamStadiumDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadiumClassReward> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<TeamStadiumClassReward>> _dictionaryWithTeamStadiumIdAndClassRewardType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumClassReward(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumClassReward>();
            _dictionaryWithTeamStadiumIdAndClassRewardType = new Dictionary<ulong, List<TeamStadiumClassReward>>();
            _db = db;
        }


        public TeamStadiumClassReward Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumClassReward");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumClassReward", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumClassReward _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumClassReward();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumClassReward");
                return null;
            }

            // SELECT `team_stadium_id`,`team_class`,`class_reward_type`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadiumClassReward orm = null;

            if (query.Step())
            {
                int teamStadiumId   = (int)query.GetInt(0);
                int teamClass       = (int)query.GetInt(1);
                int classRewardType = (int)query.GetInt(2);
                int itemCategory1   = (int)query.GetInt(3);
                int itemId1         = (int)query.GetInt(4);
                int itemNum1        = (int)query.GetInt(5);
                int itemCategory2   = (int)query.GetInt(6);
                int itemId2         = (int)query.GetInt(7);
                int itemNum2        = (int)query.GetInt(8);
                int itemCategory3   = (int)query.GetInt(9);
                int itemId3         = (int)query.GetInt(10);
                int itemNum3        = (int)query.GetInt(11);
                int itemCategory4   = (int)query.GetInt(12);
                int itemId4         = (int)query.GetInt(13);
                int itemNum4        = (int)query.GetInt(14);
                int itemCategory5   = (int)query.GetInt(15);
                int itemId5         = (int)query.GetInt(16);
                int itemNum5        = (int)query.GetInt(17);

                orm = new TeamStadiumClassReward(id, teamStadiumId, teamClass, classRewardType, itemCategory1, itemId1, itemNum1, itemCategory2, itemId2, itemNum2, itemCategory3, itemId3, itemNum3, itemCategory4, itemId4, itemNum4, itemCategory5, itemId5, itemNum5);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TeamStadiumClassReward GetWithTeamStadiumIdAndClassRewardType(int teamStadiumId, int classRewardType)
        {
            TeamStadiumClassReward orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithTeamStadiumIdAndClassRewardType(teamStadiumId, classRewardType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", teamStadiumId, classRewardType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamStadiumClassReward _SelectWithTeamStadiumIdAndClassRewardType(int teamStadiumId, int classRewardType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumClassReward_TeamStadiumId_ClassRewardType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumClassReward");
                return null;
            }

            // SELECT `id`,`team_class`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward` WHERE `team_stadium_id`=? AND `class_reward_type`=?;
            if (!query.BindInt(1, teamStadiumId))   { return null; }
            if (!query.BindInt(2, classRewardType)) { return null; }

            TeamStadiumClassReward orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithTeamStadiumIdAndClassRewardType(query, teamStadiumId, classRewardType);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumClassReward> GetListWithTeamStadiumIdAndClassRewardType(int teamStadiumId, int classRewardType)
        {
            ulong key = ((uint)unchecked((ulong)((int)teamStadiumId))) | ((((ulong)unchecked((ulong)((int)classRewardType)))) << 32);

            if (!_dictionaryWithTeamStadiumIdAndClassRewardType.ContainsKey(key)) {
                _dictionaryWithTeamStadiumIdAndClassRewardType.Add(key, _ListSelectWithTeamStadiumIdAndClassRewardType(teamStadiumId, classRewardType));
            }

            return _dictionaryWithTeamStadiumIdAndClassRewardType[key];
        }

        public List<TeamStadiumClassReward> MaybeListWithTeamStadiumIdAndClassRewardType(int teamStadiumId, int classRewardType)
        {
            List<TeamStadiumClassReward> list = GetListWithTeamStadiumIdAndClassRewardType(teamStadiumId, classRewardType);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumClassReward> _ListSelectWithTeamStadiumIdAndClassRewardType(int teamStadiumId, int classRewardType)
        {
            List<TeamStadiumClassReward> _list = new List<TeamStadiumClassReward>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumClassReward_TeamStadiumId_ClassRewardType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumClassReward");
                return null;
            }

            // SELECT `id`,`team_class`,`item_category_1`,`item_id_1`,`item_num_1`,`item_category_2`,`item_id_2`,`item_num_2`,`item_category_3`,`item_id_3`,`item_num_3`,`item_category_4`,`item_id_4`,`item_num_4`,`item_category_5`,`item_id_5`,`item_num_5` FROM `team_stadium_class_reward` WHERE `team_stadium_id`=? AND `class_reward_type`=?;
            if (!query.BindInt(1, teamStadiumId))   { return null; }
            if (!query.BindInt(2, classRewardType)) { return null; }

            while (query.Step()) {
                TeamStadiumClassReward orm = _CreateOrmByQueryResultWithTeamStadiumIdAndClassRewardType(query, teamStadiumId, classRewardType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumClassReward _CreateOrmByQueryResultWithTeamStadiumIdAndClassRewardType(LibNative.Sqlite3.PreparedQuery query, int teamStadiumId, int classRewardType)
        {
            int id            = (int)query.GetInt(0);
            int teamClass     = (int)query.GetInt(1);
            int itemCategory1 = (int)query.GetInt(2);
            int itemId1       = (int)query.GetInt(3);
            int itemNum1      = (int)query.GetInt(4);
            int itemCategory2 = (int)query.GetInt(5);
            int itemId2       = (int)query.GetInt(6);
            int itemNum2      = (int)query.GetInt(7);
            int itemCategory3 = (int)query.GetInt(8);
            int itemId3       = (int)query.GetInt(9);
            int itemNum3      = (int)query.GetInt(10);
            int itemCategory4 = (int)query.GetInt(11);
            int itemId4       = (int)query.GetInt(12);
            int itemNum4      = (int)query.GetInt(13);
            int itemCategory5 = (int)query.GetInt(14);
            int itemId5       = (int)query.GetInt(15);
            int itemNum5      = (int)query.GetInt(16);

            return new TeamStadiumClassReward(id, teamStadiumId, teamClass, classRewardType, itemCategory1, itemId1, itemNum1, itemCategory2, itemId2, itemNum2, itemCategory3, itemId3, itemNum3, itemCategory4, itemId4, itemNum4, itemCategory5, itemId5, itemNum5);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithTeamStadiumIdAndClassRewardType.Clear();
        }

        public sealed partial class TeamStadiumClassReward
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: team_stadium_id) </summary>
            public readonly int TeamStadiumId;
            /// <summary> (CSV column: team_class) </summary>
            public readonly int TeamClass;
            /// <summary> (CSV column: class_reward_type) </summary>
            public readonly int ClassRewardType;
            /// <summary> (CSV column: item_category_1) </summary>
            public readonly int ItemCategory1;
            /// <summary> (CSV column: item_id_1) </summary>
            public readonly int ItemId1;
            /// <summary> (CSV column: item_num_1) </summary>
            public readonly int ItemNum1;
            /// <summary> (CSV column: item_category_2) </summary>
            public readonly int ItemCategory2;
            /// <summary> (CSV column: item_id_2) </summary>
            public readonly int ItemId2;
            /// <summary> (CSV column: item_num_2) </summary>
            public readonly int ItemNum2;
            /// <summary> (CSV column: item_category_3) </summary>
            public readonly int ItemCategory3;
            /// <summary> (CSV column: item_id_3) </summary>
            public readonly int ItemId3;
            /// <summary> (CSV column: item_num_3) </summary>
            public readonly int ItemNum3;
            /// <summary> (CSV column: item_category_4) </summary>
            public readonly int ItemCategory4;
            /// <summary> (CSV column: item_id_4) </summary>
            public readonly int ItemId4;
            /// <summary> (CSV column: item_num_4) </summary>
            public readonly int ItemNum4;
            /// <summary> (CSV column: item_category_5) </summary>
            public readonly int ItemCategory5;
            /// <summary> (CSV column: item_id_5) </summary>
            public readonly int ItemId5;
            /// <summary> (CSV column: item_num_5) </summary>
            public readonly int ItemNum5;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumClassReward(int id = 0, int teamStadiumId = 0, int teamClass = 0, int classRewardType = 0, int itemCategory1 = 0, int itemId1 = 0, int itemNum1 = 0, int itemCategory2 = 0, int itemId2 = 0, int itemNum2 = 0, int itemCategory3 = 0, int itemId3 = 0, int itemNum3 = 0, int itemCategory4 = 0, int itemId4 = 0, int itemNum4 = 0, int itemCategory5 = 0, int itemId5 = 0, int itemNum5 = 0)
            {
                this.Id              = id;
                this.TeamStadiumId   = teamStadiumId;
                this.TeamClass       = teamClass;
                this.ClassRewardType = classRewardType;
                this.ItemCategory1   = itemCategory1;
                this.ItemId1         = itemId1;
                this.ItemNum1        = itemNum1;
                this.ItemCategory2   = itemCategory2;
                this.ItemId2         = itemId2;
                this.ItemNum2        = itemNum2;
                this.ItemCategory3   = itemCategory3;
                this.ItemId3         = itemId3;
                this.ItemNum3        = itemNum3;
                this.ItemCategory4   = itemCategory4;
                this.ItemId4         = itemId4;
                this.ItemNum4        = itemNum4;
                this.ItemCategory5   = itemCategory5;
                this.ItemId5         = itemId5;
                this.ItemNum5        = itemNum5;
            }
        }
    }
}
#endif

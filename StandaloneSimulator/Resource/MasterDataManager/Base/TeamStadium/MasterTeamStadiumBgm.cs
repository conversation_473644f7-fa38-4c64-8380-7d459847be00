// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_bgm
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:scene_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumBgm : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_bgm";

        MasterTeamStadiumDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadiumBgm> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<TeamStadiumBgm>> _dictionaryWithSceneType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumBgm(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumBgm>();
            _dictionaryWithSceneType = new Dictionary<int, List<TeamStadiumBgm>>();
            _db = db;
        }


        public TeamStadiumBgm Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumBgm");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumBgm", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumBgm _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumBgm();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumBgm");
                return null;
            }

            // SELECT `scene_type`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadiumBgm orm = null;

            if (query.Step())
            {
                int sceneType       = (int)query.GetInt(0);
                int priority        = (int)query.GetInt(1);
                string cueName      = query.GetText(2);
                string cuesheetName = query.GetText(3);

                orm = new TeamStadiumBgm(id, sceneType, priority, cueName, cuesheetName);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TeamStadiumBgm GetWithSceneType(int sceneType)
        {
            TeamStadiumBgm orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithSceneType(sceneType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", sceneType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamStadiumBgm _SelectWithSceneType(int sceneType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumBgm_SceneType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumBgm");
                return null;
            }

            // SELECT `id`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm` WHERE `scene_type`=?;
            if (!query.BindInt(1, sceneType)) { return null; }

            TeamStadiumBgm orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithSceneType(query, sceneType);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumBgm> GetListWithSceneType(int sceneType)
        {
            int key = (int)sceneType;

            if (!_dictionaryWithSceneType.ContainsKey(key)) {
                _dictionaryWithSceneType.Add(key, _ListSelectWithSceneType(sceneType));
            }

            return _dictionaryWithSceneType[key];
        }

        public List<TeamStadiumBgm> MaybeListWithSceneType(int sceneType)
        {
            List<TeamStadiumBgm> list = GetListWithSceneType(sceneType);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumBgm> _ListSelectWithSceneType(int sceneType)
        {
            List<TeamStadiumBgm> _list = new List<TeamStadiumBgm>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumBgm_SceneType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumBgm");
                return null;
            }

            // SELECT `id`,`priority`,`cue_name`,`cuesheet_name` FROM `team_stadium_bgm` WHERE `scene_type`=?;
            if (!query.BindInt(1, sceneType)) { return null; }

            while (query.Step()) {
                TeamStadiumBgm orm = _CreateOrmByQueryResultWithSceneType(query, sceneType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumBgm _CreateOrmByQueryResultWithSceneType(LibNative.Sqlite3.PreparedQuery query, int sceneType)
        {
            int id              = (int)query.GetInt(0);
            int priority        = (int)query.GetInt(1);
            string cueName      = query.GetText(2);
            string cuesheetName = query.GetText(3);

            return new TeamStadiumBgm(id, sceneType, priority, cueName, cuesheetName);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithSceneType.Clear();
        }

        public sealed partial class TeamStadiumBgm
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: scene_type) </summary>
            public readonly int SceneType;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: cue_name) </summary>
            public readonly string CueName;
            /// <summary> (CSV column: cuesheet_name) </summary>
            public readonly string CuesheetName;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumBgm(int id = 0, int sceneType = 0, int priority = 0, string cueName = "", string cuesheetName = "")
            {
                this.Id           = id;
                this.SceneType    = sceneType;
                this.Priority     = priority;
                this.CueName      = cueName;
                this.CuesheetName = cuesheetName;
            }
        }
    }
}
#endif

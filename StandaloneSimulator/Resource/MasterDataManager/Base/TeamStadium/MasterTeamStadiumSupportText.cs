// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_stadium/team_stadium_support_text
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamStadiumSupportText : AbstractMasterData
    {
        public const string TABLE_NAME = "team_stadium_support_text";

        MasterTeamStadiumDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamStadiumSupportText> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<TeamStadiumSupportText>> _dictionaryWithType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamStadiumSupportText(MasterTeamStadiumDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamStadiumSupportText>();
            _dictionaryWithType = new Dictionary<int, List<TeamStadiumSupportText>>();
            _db = db;
        }


        public TeamStadiumSupportText Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamStadiumSupportText");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamStadiumSupportText", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private TeamStadiumSupportText _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamStadiumSupportText();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumSupportText");
                return null;
            }

            // SELECT `type`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamStadiumSupportText orm = null;

            if (query.Step())
            {
                int type     = (int)query.GetInt(0);
                int minBonus = (int)query.GetInt(1);
                int maxBonus = (int)query.GetInt(2);

                orm = new TeamStadiumSupportText(id, type, minBonus, maxBonus);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TeamStadiumSupportText GetWithType(int type)
        {
            TeamStadiumSupportText orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithType(type);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", type));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamStadiumSupportText _SelectWithType(int type)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumSupportText_Type();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumSupportText");
                return null;
            }

            // SELECT `id`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text` WHERE `type`=?;
            if (!query.BindInt(1, type)) { return null; }

            TeamStadiumSupportText orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithType(query, type);
            }

            query.Reset();

            return orm;
        }

        public List<TeamStadiumSupportText> GetListWithType(int type)
        {
            int key = (int)type;

            if (!_dictionaryWithType.ContainsKey(key)) {
                _dictionaryWithType.Add(key, _ListSelectWithType(type));
            }

            return _dictionaryWithType[key];
        }

        public List<TeamStadiumSupportText> MaybeListWithType(int type)
        {
            List<TeamStadiumSupportText> list = GetListWithType(type);
            return list.Count > 0 ? list : null;
        }

        private List<TeamStadiumSupportText> _ListSelectWithType(int type)
        {
            List<TeamStadiumSupportText> _list = new List<TeamStadiumSupportText>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamStadiumSupportText_Type();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamStadiumSupportText");
                return null;
            }

            // SELECT `id`,`min_bonus`,`max_bonus` FROM `team_stadium_support_text` WHERE `type`=?;
            if (!query.BindInt(1, type)) { return null; }

            while (query.Step()) {
                TeamStadiumSupportText orm = _CreateOrmByQueryResultWithType(query, type);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamStadiumSupportText _CreateOrmByQueryResultWithType(LibNative.Sqlite3.PreparedQuery query, int type)
        {
            int id       = (int)query.GetInt(0);
            int minBonus = (int)query.GetInt(1);
            int maxBonus = (int)query.GetInt(2);

            return new TeamStadiumSupportText(id, type, minBonus, maxBonus);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithType.Clear();
        }

        public sealed partial class TeamStadiumSupportText
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: type) </summary>
            public readonly int Type;
            /// <summary> (CSV column: min_bonus) </summary>
            public readonly int MinBonus;
            /// <summary> (CSV column: max_bonus) </summary>
            public readonly int MaxBonus;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamStadiumSupportText(int id = 0, int type = 0, int minBonus = 0, int maxBonus = 0)
            {
                this.Id       = id;
                this.Type     = type;
                this.MinBonus = minBonus;
                this.MaxBonus = maxBonus;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public class MasterRaceDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterRace masterRace                                             { get; private set; }
        public MasterRaceMotivationRate masterRaceMotivationRate                 { get; private set; }
        public MasterRaceProperDistanceRate masterRaceProperDistanceRate         { get; private set; }
        public MasterRaceProperRunningstyleRate masterRaceProperRunningstyleRate { get; private set; }
        public MasterRaceProperGroundRate masterRaceProperGroundRate             { get; private set; }
        public MasterRaceBibColor masterRaceBibColor                             { get; private set; }
        public MasterRaceCourseSet masterRaceCourseSet                           { get; private set; }
        public MasterRaceSingleModeTeamStatus masterRaceSingleModeTeamStatus     { get; private set; }
        public MasterRaceCourseSetStatus masterRaceCourseSetStatus               { get; private set; }
        public MasterRaceEnvDefine masterRaceEnvDefine                           { get; private set; }
        public MasterRaceFenceSet masterRaceFenceSet                             { get; private set; }
        public MasterRaceInstance masterRaceInstance                             { get; private set; }
        public MasterRaceBgm masterRaceBgm                                       { get; private set; }
        public MasterRaceBgmPattern masterRaceBgmPattern                         { get; private set; }
        public MasterRaceBgmCutin masterRaceBgmCutin                             { get; private set; }
        public MasterRaceJikkyoBase masterRaceJikkyoBase                         { get; private set; }
        public MasterRaceJikkyoBaseVenus masterRaceJikkyoBaseVenus               { get; private set; }
        public MasterRaceJikkyoComment masterRaceJikkyoComment                   { get; private set; }
        public MasterRaceJikkyoCue masterRaceJikkyoCue                           { get; private set; }
        public MasterRaceJikkyoMessage masterRaceJikkyoMessage                   { get; private set; }
        public MasterRaceJikkyoRace masterRaceJikkyoRace                         { get; private set; }
        public MasterRaceJikkyoTrigger masterRaceJikkyoTrigger                   { get; private set; }
        public MasterRacePlayerCamera masterRacePlayerCamera                     { get; private set; }
        public MasterRaceTrack masterRaceTrack                                   { get; private set; }
        public MasterRaceCondition masterRaceCondition                           { get; private set; }
        public MasterRaceTrophy masterRaceTrophy                                 { get; private set; }
        public MasterRaceOverrunPattern masterRaceOverrunPattern                 { get; private set; }
        public MasterMobData masterMobData                                       { get; private set; }
        public MasterMobDressColorSet masterMobDressColorSet                     { get; private set; }
        public MasterMobHairColorSet masterMobHairColorSet                       { get; private set; }
        public MasterAudienceData masterAudienceData                             { get; private set; }
        public MasterAudienceDressColorSet masterAudienceDressColorSet           { get; private set; }
        public MasterAudienceHairColorSet masterAudienceHairColorSet             { get; private set; }
        public MasterAudienceImpostor masterAudienceImpostor                     { get; private set; }
        public MasterAudienceImpostorLottery masterAudienceImpostorLottery       { get; private set; }
        public MasterAudienceImpostorColorSet masterAudienceImpostorColorSet     { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRace                       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceMotivationRate         = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceProperDistanceRate     = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceProperRunningstyleRate = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceProperGroundRate       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceBibColor               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceCourseSet              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceSingleModeTeamStatus   = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceCourseSetStatus        = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceEnvDefine              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceFenceSet               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceInstance               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceBgm                    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceBgmPattern             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceBgmCutin               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceJikkyoBase             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceJikkyoBaseVenus        = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceJikkyoComment          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceJikkyoCue              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceJikkyoMessage          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceJikkyoRace             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceJikkyoTrigger          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRacePlayerCamera           = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceTrack                  = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceCondition              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceTrophy                 = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRaceOverrunPattern         = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterMobData                    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterMobDressColorSet           = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterMobHairColorSet            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterAudienceData               = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterAudienceDressColorSet      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterAudienceHairColorSet       = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_race_courseSet                          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_race_group                              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceCourseSet_raceTrackId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceEnvDefine_raceTrackId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceInstance_raceId                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceBgm_raceType                        = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceBgmCutin_cardId                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceBgmCutin_bgmGroupId                 = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceBgmCutin_cardId_bgmGroupId          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceJikkyoBase_mode                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceJikkyoBaseVenus_mode                = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceJikkyoComment_groupId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceJikkyoComment_messageGroup          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceJikkyoCue_cuesheetType              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceJikkyoMessage_groupId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceJikkyoMessage_commentGroup          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_racePlayerCamera_prefabName             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_racePlayerCamera_category               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceCondition_season_ground             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceCondition_season_weather            = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceCondition_weather_ground            = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceCondition_ground_weather            = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceTrophy_trophyId                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_raceTrophy_raceInstanceId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_audienceImpostor_season_weather         = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_audienceImpostorLottery_groupId_pattern = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_audienceImpostorColorSet_colorGroupId   = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterRaceDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterRace                       = new MasterRace(this);
            this.masterRaceMotivationRate         = new MasterRaceMotivationRate(this);
            this.masterRaceProperDistanceRate     = new MasterRaceProperDistanceRate(this);
            this.masterRaceProperRunningstyleRate = new MasterRaceProperRunningstyleRate(this);
            this.masterRaceProperGroundRate       = new MasterRaceProperGroundRate(this);
            this.masterRaceBibColor               = new MasterRaceBibColor(this);
            this.masterRaceCourseSet              = new MasterRaceCourseSet(this);
            this.masterRaceSingleModeTeamStatus   = new MasterRaceSingleModeTeamStatus(this);
            this.masterRaceCourseSetStatus        = new MasterRaceCourseSetStatus(this);
            this.masterRaceEnvDefine              = new MasterRaceEnvDefine(this);
            this.masterRaceFenceSet               = new MasterRaceFenceSet(this);
            this.masterRaceInstance               = new MasterRaceInstance(this);
            this.masterRaceBgm                    = new MasterRaceBgm(this);
            this.masterRaceBgmPattern             = new MasterRaceBgmPattern(this);
            this.masterRaceBgmCutin               = new MasterRaceBgmCutin(this);
            this.masterRaceJikkyoBase             = new MasterRaceJikkyoBase(this);
            this.masterRaceJikkyoBaseVenus        = new MasterRaceJikkyoBaseVenus(this);
            this.masterRaceJikkyoComment          = new MasterRaceJikkyoComment(this);
            this.masterRaceJikkyoCue              = new MasterRaceJikkyoCue(this);
            this.masterRaceJikkyoMessage          = new MasterRaceJikkyoMessage(this);
            this.masterRaceJikkyoRace             = new MasterRaceJikkyoRace(this);
            this.masterRaceJikkyoTrigger          = new MasterRaceJikkyoTrigger(this);
            this.masterRacePlayerCamera           = new MasterRacePlayerCamera(this);
            this.masterRaceTrack                  = new MasterRaceTrack(this);
            this.masterRaceCondition              = new MasterRaceCondition(this);
            this.masterRaceTrophy                 = new MasterRaceTrophy(this);
            this.masterRaceOverrunPattern         = new MasterRaceOverrunPattern(this);
            this.masterMobData                    = new MasterMobData(this);
            this.masterMobDressColorSet           = new MasterMobDressColorSet(this);
            this.masterMobHairColorSet            = new MasterMobHairColorSet(this);
            this.masterAudienceData               = new MasterAudienceData(this);
            this.masterAudienceDressColorSet      = new MasterAudienceDressColorSet(this);
            this.masterAudienceHairColorSet       = new MasterAudienceHairColorSet(this);
            this.masterAudienceImpostor           = new MasterAudienceImpostor(this);
            this.masterAudienceImpostorLottery    = new MasterAudienceImpostorLottery(this);
            this.masterAudienceImpostorColorSet   = new MasterAudienceImpostorColorSet(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet race database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterRace != null) { _selectQuery_masterRace.Dispose(); _selectQuery_masterRace = null; }
            if (_indexedSelectQuery_race_courseSet != null) { _indexedSelectQuery_race_courseSet.Dispose(); _indexedSelectQuery_race_courseSet = null; }
            if (_indexedSelectQuery_race_group != null) { _indexedSelectQuery_race_group.Dispose(); _indexedSelectQuery_race_group = null; }
            if (_selectQuery_masterRaceMotivationRate != null) { _selectQuery_masterRaceMotivationRate.Dispose(); _selectQuery_masterRaceMotivationRate = null; }
            if (_selectQuery_masterRaceProperDistanceRate != null) { _selectQuery_masterRaceProperDistanceRate.Dispose(); _selectQuery_masterRaceProperDistanceRate = null; }
            if (_selectQuery_masterRaceProperRunningstyleRate != null) { _selectQuery_masterRaceProperRunningstyleRate.Dispose(); _selectQuery_masterRaceProperRunningstyleRate = null; }
            if (_selectQuery_masterRaceProperGroundRate != null) { _selectQuery_masterRaceProperGroundRate.Dispose(); _selectQuery_masterRaceProperGroundRate = null; }
            if (_selectQuery_masterRaceBibColor != null) { _selectQuery_masterRaceBibColor.Dispose(); _selectQuery_masterRaceBibColor = null; }
            if (_selectQuery_masterRaceCourseSet != null) { _selectQuery_masterRaceCourseSet.Dispose(); _selectQuery_masterRaceCourseSet = null; }
            if (_indexedSelectQuery_raceCourseSet_raceTrackId != null) { _indexedSelectQuery_raceCourseSet_raceTrackId.Dispose(); _indexedSelectQuery_raceCourseSet_raceTrackId = null; }
            if (_selectQuery_masterRaceSingleModeTeamStatus != null) { _selectQuery_masterRaceSingleModeTeamStatus.Dispose(); _selectQuery_masterRaceSingleModeTeamStatus = null; }
            if (_selectQuery_masterRaceCourseSetStatus != null) { _selectQuery_masterRaceCourseSetStatus.Dispose(); _selectQuery_masterRaceCourseSetStatus = null; }
            if (_selectQuery_masterRaceEnvDefine != null) { _selectQuery_masterRaceEnvDefine.Dispose(); _selectQuery_masterRaceEnvDefine = null; }
            if (_indexedSelectQuery_raceEnvDefine_raceTrackId != null) { _indexedSelectQuery_raceEnvDefine_raceTrackId.Dispose(); _indexedSelectQuery_raceEnvDefine_raceTrackId = null; }
            if (_selectQuery_masterRaceFenceSet != null) { _selectQuery_masterRaceFenceSet.Dispose(); _selectQuery_masterRaceFenceSet = null; }
            if (_selectQuery_masterRaceInstance != null) { _selectQuery_masterRaceInstance.Dispose(); _selectQuery_masterRaceInstance = null; }
            if (_indexedSelectQuery_raceInstance_raceId != null) { _indexedSelectQuery_raceInstance_raceId.Dispose(); _indexedSelectQuery_raceInstance_raceId = null; }
            if (_selectQuery_masterRaceBgm != null) { _selectQuery_masterRaceBgm.Dispose(); _selectQuery_masterRaceBgm = null; }
            if (_indexedSelectQuery_raceBgm_raceType != null) { _indexedSelectQuery_raceBgm_raceType.Dispose(); _indexedSelectQuery_raceBgm_raceType = null; }
            if (_selectQuery_masterRaceBgmPattern != null) { _selectQuery_masterRaceBgmPattern.Dispose(); _selectQuery_masterRaceBgmPattern = null; }
            if (_selectQuery_masterRaceBgmCutin != null) { _selectQuery_masterRaceBgmCutin.Dispose(); _selectQuery_masterRaceBgmCutin = null; }
            if (_indexedSelectQuery_raceBgmCutin_cardId != null) { _indexedSelectQuery_raceBgmCutin_cardId.Dispose(); _indexedSelectQuery_raceBgmCutin_cardId = null; }
            if (_indexedSelectQuery_raceBgmCutin_bgmGroupId != null) { _indexedSelectQuery_raceBgmCutin_bgmGroupId.Dispose(); _indexedSelectQuery_raceBgmCutin_bgmGroupId = null; }
            if (_indexedSelectQuery_raceBgmCutin_cardId_bgmGroupId != null) { _indexedSelectQuery_raceBgmCutin_cardId_bgmGroupId.Dispose(); _indexedSelectQuery_raceBgmCutin_cardId_bgmGroupId = null; }
            if (_selectQuery_masterRaceJikkyoBase != null) { _selectQuery_masterRaceJikkyoBase.Dispose(); _selectQuery_masterRaceJikkyoBase = null; }
            if (_indexedSelectQuery_raceJikkyoBase_mode != null) { _indexedSelectQuery_raceJikkyoBase_mode.Dispose(); _indexedSelectQuery_raceJikkyoBase_mode = null; }
            if (_selectQuery_masterRaceJikkyoBaseVenus != null) { _selectQuery_masterRaceJikkyoBaseVenus.Dispose(); _selectQuery_masterRaceJikkyoBaseVenus = null; }
            if (_indexedSelectQuery_raceJikkyoBaseVenus_mode != null) { _indexedSelectQuery_raceJikkyoBaseVenus_mode.Dispose(); _indexedSelectQuery_raceJikkyoBaseVenus_mode = null; }
            if (_selectQuery_masterRaceJikkyoComment != null) { _selectQuery_masterRaceJikkyoComment.Dispose(); _selectQuery_masterRaceJikkyoComment = null; }
            if (_indexedSelectQuery_raceJikkyoComment_groupId != null) { _indexedSelectQuery_raceJikkyoComment_groupId.Dispose(); _indexedSelectQuery_raceJikkyoComment_groupId = null; }
            if (_indexedSelectQuery_raceJikkyoComment_messageGroup != null) { _indexedSelectQuery_raceJikkyoComment_messageGroup.Dispose(); _indexedSelectQuery_raceJikkyoComment_messageGroup = null; }
            if (_selectQuery_masterRaceJikkyoCue != null) { _selectQuery_masterRaceJikkyoCue.Dispose(); _selectQuery_masterRaceJikkyoCue = null; }
            if (_indexedSelectQuery_raceJikkyoCue_cuesheetType != null) { _indexedSelectQuery_raceJikkyoCue_cuesheetType.Dispose(); _indexedSelectQuery_raceJikkyoCue_cuesheetType = null; }
            if (_selectQuery_masterRaceJikkyoMessage != null) { _selectQuery_masterRaceJikkyoMessage.Dispose(); _selectQuery_masterRaceJikkyoMessage = null; }
            if (_indexedSelectQuery_raceJikkyoMessage_groupId != null) { _indexedSelectQuery_raceJikkyoMessage_groupId.Dispose(); _indexedSelectQuery_raceJikkyoMessage_groupId = null; }
            if (_indexedSelectQuery_raceJikkyoMessage_commentGroup != null) { _indexedSelectQuery_raceJikkyoMessage_commentGroup.Dispose(); _indexedSelectQuery_raceJikkyoMessage_commentGroup = null; }
            if (_selectQuery_masterRaceJikkyoRace != null) { _selectQuery_masterRaceJikkyoRace.Dispose(); _selectQuery_masterRaceJikkyoRace = null; }
            if (_selectQuery_masterRaceJikkyoTrigger != null) { _selectQuery_masterRaceJikkyoTrigger.Dispose(); _selectQuery_masterRaceJikkyoTrigger = null; }
            if (_selectQuery_masterRacePlayerCamera != null) { _selectQuery_masterRacePlayerCamera.Dispose(); _selectQuery_masterRacePlayerCamera = null; }
            if (_indexedSelectQuery_racePlayerCamera_prefabName != null) { _indexedSelectQuery_racePlayerCamera_prefabName.Dispose(); _indexedSelectQuery_racePlayerCamera_prefabName = null; }
            if (_indexedSelectQuery_racePlayerCamera_category != null) { _indexedSelectQuery_racePlayerCamera_category.Dispose(); _indexedSelectQuery_racePlayerCamera_category = null; }
            if (_selectQuery_masterRaceTrack != null) { _selectQuery_masterRaceTrack.Dispose(); _selectQuery_masterRaceTrack = null; }
            if (_selectQuery_masterRaceCondition != null) { _selectQuery_masterRaceCondition.Dispose(); _selectQuery_masterRaceCondition = null; }
            if (_indexedSelectQuery_raceCondition_season_ground != null) { _indexedSelectQuery_raceCondition_season_ground.Dispose(); _indexedSelectQuery_raceCondition_season_ground = null; }
            if (_indexedSelectQuery_raceCondition_season_weather != null) { _indexedSelectQuery_raceCondition_season_weather.Dispose(); _indexedSelectQuery_raceCondition_season_weather = null; }
            if (_indexedSelectQuery_raceCondition_weather_ground != null) { _indexedSelectQuery_raceCondition_weather_ground.Dispose(); _indexedSelectQuery_raceCondition_weather_ground = null; }
            if (_indexedSelectQuery_raceCondition_ground_weather != null) { _indexedSelectQuery_raceCondition_ground_weather.Dispose(); _indexedSelectQuery_raceCondition_ground_weather = null; }
            if (_selectQuery_masterRaceTrophy != null) { _selectQuery_masterRaceTrophy.Dispose(); _selectQuery_masterRaceTrophy = null; }
            if (_indexedSelectQuery_raceTrophy_trophyId != null) { _indexedSelectQuery_raceTrophy_trophyId.Dispose(); _indexedSelectQuery_raceTrophy_trophyId = null; }
            if (_indexedSelectQuery_raceTrophy_raceInstanceId != null) { _indexedSelectQuery_raceTrophy_raceInstanceId.Dispose(); _indexedSelectQuery_raceTrophy_raceInstanceId = null; }
            if (_selectQuery_masterRaceOverrunPattern != null) { _selectQuery_masterRaceOverrunPattern.Dispose(); _selectQuery_masterRaceOverrunPattern = null; }
            if (_selectQuery_masterMobData != null) { _selectQuery_masterMobData.Dispose(); _selectQuery_masterMobData = null; }
            if (_selectQuery_masterMobDressColorSet != null) { _selectQuery_masterMobDressColorSet.Dispose(); _selectQuery_masterMobDressColorSet = null; }
            if (_selectQuery_masterMobHairColorSet != null) { _selectQuery_masterMobHairColorSet.Dispose(); _selectQuery_masterMobHairColorSet = null; }
            if (_selectQuery_masterAudienceData != null) { _selectQuery_masterAudienceData.Dispose(); _selectQuery_masterAudienceData = null; }
            if (_selectQuery_masterAudienceDressColorSet != null) { _selectQuery_masterAudienceDressColorSet.Dispose(); _selectQuery_masterAudienceDressColorSet = null; }
            if (_selectQuery_masterAudienceHairColorSet != null) { _selectQuery_masterAudienceHairColorSet.Dispose(); _selectQuery_masterAudienceHairColorSet = null; }
            if (_indexedSelectQuery_audienceImpostor_season_weather != null) { _indexedSelectQuery_audienceImpostor_season_weather.Dispose(); _indexedSelectQuery_audienceImpostor_season_weather = null; }
            if (_indexedSelectQuery_audienceImpostorLottery_groupId_pattern != null) { _indexedSelectQuery_audienceImpostorLottery_groupId_pattern.Dispose(); _indexedSelectQuery_audienceImpostorLottery_groupId_pattern = null; }
            if (_indexedSelectQuery_audienceImpostorColorSet_colorGroupId != null) { _indexedSelectQuery_audienceImpostorColorSet_colorGroupId.Dispose(); _indexedSelectQuery_audienceImpostorColorSet_colorGroupId = null; }
            if (this.masterRace != null) { this.masterRace.Unload(); }
            if (this.masterRaceMotivationRate != null) { this.masterRaceMotivationRate.Unload(); }
            if (this.masterRaceProperDistanceRate != null) { this.masterRaceProperDistanceRate.Unload(); }
            if (this.masterRaceProperRunningstyleRate != null) { this.masterRaceProperRunningstyleRate.Unload(); }
            if (this.masterRaceProperGroundRate != null) { this.masterRaceProperGroundRate.Unload(); }
            if (this.masterRaceBibColor != null) { this.masterRaceBibColor.Unload(); }
            if (this.masterRaceCourseSet != null) { this.masterRaceCourseSet.Unload(); }
            if (this.masterRaceSingleModeTeamStatus != null) { this.masterRaceSingleModeTeamStatus.Unload(); }
            if (this.masterRaceCourseSetStatus != null) { this.masterRaceCourseSetStatus.Unload(); }
            if (this.masterRaceEnvDefine != null) { this.masterRaceEnvDefine.Unload(); }
            if (this.masterRaceFenceSet != null) { this.masterRaceFenceSet.Unload(); }
            if (this.masterRaceInstance != null) { this.masterRaceInstance.Unload(); }
            if (this.masterRaceBgm != null) { this.masterRaceBgm.Unload(); }
            if (this.masterRaceBgmPattern != null) { this.masterRaceBgmPattern.Unload(); }
            if (this.masterRaceBgmCutin != null) { this.masterRaceBgmCutin.Unload(); }
            if (this.masterRaceJikkyoBase != null) { this.masterRaceJikkyoBase.Unload(); }
            if (this.masterRaceJikkyoBaseVenus != null) { this.masterRaceJikkyoBaseVenus.Unload(); }
            if (this.masterRaceJikkyoComment != null) { this.masterRaceJikkyoComment.Unload(); }
            if (this.masterRaceJikkyoCue != null) { this.masterRaceJikkyoCue.Unload(); }
            if (this.masterRaceJikkyoMessage != null) { this.masterRaceJikkyoMessage.Unload(); }
            if (this.masterRaceJikkyoRace != null) { this.masterRaceJikkyoRace.Unload(); }
            if (this.masterRaceJikkyoTrigger != null) { this.masterRaceJikkyoTrigger.Unload(); }
            if (this.masterRacePlayerCamera != null) { this.masterRacePlayerCamera.Unload(); }
            if (this.masterRaceTrack != null) { this.masterRaceTrack.Unload(); }
            if (this.masterRaceCondition != null) { this.masterRaceCondition.Unload(); }
            if (this.masterRaceTrophy != null) { this.masterRaceTrophy.Unload(); }
            if (this.masterRaceOverrunPattern != null) { this.masterRaceOverrunPattern.Unload(); }
            if (this.masterMobData != null) { this.masterMobData.Unload(); }
            if (this.masterMobDressColorSet != null) { this.masterMobDressColorSet.Unload(); }
            if (this.masterMobHairColorSet != null) { this.masterMobHairColorSet.Unload(); }
            if (this.masterAudienceData != null) { this.masterAudienceData.Unload(); }
            if (this.masterAudienceDressColorSet != null) { this.masterAudienceDressColorSet.Unload(); }
            if (this.masterAudienceHairColorSet != null) { this.masterAudienceHairColorSet.Unload(); }
            if (this.masterAudienceImpostor != null) { this.masterAudienceImpostor.Unload(); }
            if (this.masterAudienceImpostorLottery != null) { this.masterAudienceImpostorLottery.Unload(); }
            if (this.masterAudienceImpostorColorSet != null) { this.masterAudienceImpostorColorSet.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for race/race
        
        /// <summary>
        /// SELECT `group`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_Race()
        {
            if (_selectQuery_masterRace == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRace = connection.PreparedQuery("SELECT `group`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRace;
        }
        
        /// <summary>
        /// SELECT `id`,`group`,`grade`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `course_set`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_Race_CourseSet()
        {
            if (_indexedSelectQuery_race_courseSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_race_courseSet = connection.PreparedQuery("SELECT `id`,`group`,`grade`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `course_set`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_race_courseSet;
        }
        
        /// <summary>
        /// SELECT `id`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `group`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_Race_Group()
        {
            if (_indexedSelectQuery_race_group == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_race_group = connection.PreparedQuery("SELECT `id`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race` WHERE `group`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_race_group;
        }
        
        /// <summary>
        /// SELECT `id`,`group`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_Race()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group`,`grade`,`course_set`,`thumbnail_id`,`ff_cue_name`,`ff_cuesheet_name`,`real_ff_cue_name`,`real_ff_cuesheet_name`,`ff_anim`,`ff_camera`,`ff_camera_sub`,`ff_sub`,`start_gate`,`start_gate_panel`,`goal_gate`,`goal_flower`,`result_podium`,`audience_group_id`,`paddock_bg_id`,`audience`,`entry_num`,`change_full_gate`,`is_dirtgrade`,`start_date` FROM `race`;");
        }
        
        // SQL statements for race/race_motivation_rate
        
        /// <summary>
        /// SELECT `motivation_rate` FROM `race_motivation_rate` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceMotivationRate()
        {
            if (_selectQuery_masterRaceMotivationRate == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceMotivationRate = connection.PreparedQuery("SELECT `motivation_rate` FROM `race_motivation_rate` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceMotivationRate;
        }
        
        /// <summary>
        /// SELECT `id`,`motivation_rate` FROM `race_motivation_rate`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceMotivationRate()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`motivation_rate` FROM `race_motivation_rate`;");
        }
        
        // SQL statements for race/race_proper_distance_rate
        
        /// <summary>
        /// SELECT `proper_rate_speed`,`proper_rate_power` FROM `race_proper_distance_rate` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceProperDistanceRate()
        {
            if (_selectQuery_masterRaceProperDistanceRate == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceProperDistanceRate = connection.PreparedQuery("SELECT `proper_rate_speed`,`proper_rate_power` FROM `race_proper_distance_rate` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceProperDistanceRate;
        }
        
        /// <summary>
        /// SELECT `id`,`proper_rate_speed`,`proper_rate_power` FROM `race_proper_distance_rate`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceProperDistanceRate()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`proper_rate_speed`,`proper_rate_power` FROM `race_proper_distance_rate`;");
        }
        
        // SQL statements for race/race_proper_runningstyle_rate
        
        /// <summary>
        /// SELECT `proper_rate` FROM `race_proper_runningstyle_rate` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceProperRunningstyleRate()
        {
            if (_selectQuery_masterRaceProperRunningstyleRate == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceProperRunningstyleRate = connection.PreparedQuery("SELECT `proper_rate` FROM `race_proper_runningstyle_rate` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceProperRunningstyleRate;
        }
        
        /// <summary>
        /// SELECT `id`,`proper_rate` FROM `race_proper_runningstyle_rate`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceProperRunningstyleRate()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`proper_rate` FROM `race_proper_runningstyle_rate`;");
        }
        
        // SQL statements for race/race_proper_ground_rate
        
        /// <summary>
        /// SELECT `proper_rate` FROM `race_proper_ground_rate` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceProperGroundRate()
        {
            if (_selectQuery_masterRaceProperGroundRate == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceProperGroundRate = connection.PreparedQuery("SELECT `proper_rate` FROM `race_proper_ground_rate` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceProperGroundRate;
        }
        
        /// <summary>
        /// SELECT `id`,`proper_rate` FROM `race_proper_ground_rate`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceProperGroundRate()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`proper_rate` FROM `race_proper_ground_rate`;");
        }
        
        // SQL statements for race/race_bib_color
        
        /// <summary>
        /// SELECT `bib_color`,`font_color` FROM `race_bib_color` WHERE `grade`=? AND `race_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceBibColor()
        {
            if (_selectQuery_masterRaceBibColor == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceBibColor = connection.PreparedQuery("SELECT `bib_color`,`font_color` FROM `race_bib_color` WHERE `grade`=? AND `race_id`=?;");
            }
        
            return _selectQuery_masterRaceBibColor;
        }
        
        /// <summary>
        /// SELECT `grade`,`race_id`,`bib_color`,`font_color` FROM `race_bib_color`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceBibColor()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `grade`,`race_id`,`bib_color`,`font_color` FROM `race_bib_color`;");
        }
        
        // SQL statements for race/race_course_set
        
        /// <summary>
        /// SELECT `race_track_id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceCourseSet()
        {
            if (_selectQuery_masterRaceCourseSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceCourseSet = connection.PreparedQuery("SELECT `race_track_id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceCourseSet;
        }
        
        /// <summary>
        /// SELECT `id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set` WHERE `race_track_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceCourseSet_RaceTrackId()
        {
            if (_indexedSelectQuery_raceCourseSet_raceTrackId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceCourseSet_raceTrackId = connection.PreparedQuery("SELECT `id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set` WHERE `race_track_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_raceCourseSet_raceTrackId;
        }
        
        /// <summary>
        /// SELECT `id`,`race_track_id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceCourseSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`race_track_id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set`;");
        }
        
        // SQL statements for race/race_single_mode_team_status
        
        /// <summary>
        /// SELECT `team_rank_threshold`,`add_status` FROM `race_single_mode_team_status` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceSingleModeTeamStatus()
        {
            if (_selectQuery_masterRaceSingleModeTeamStatus == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceSingleModeTeamStatus = connection.PreparedQuery("SELECT `team_rank_threshold`,`add_status` FROM `race_single_mode_team_status` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceSingleModeTeamStatus;
        }
        
        /// <summary>
        /// SELECT `id`,`team_rank_threshold`,`add_status` FROM `race_single_mode_team_status`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceSingleModeTeamStatus()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`team_rank_threshold`,`add_status` FROM `race_single_mode_team_status`;");
        }
        
        // SQL statements for race/race_course_set_status
        
        /// <summary>
        /// SELECT `target_status_1`,`target_status_2` FROM `race_course_set_status` WHERE `course_set_status_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceCourseSetStatus()
        {
            if (_selectQuery_masterRaceCourseSetStatus == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceCourseSetStatus = connection.PreparedQuery("SELECT `target_status_1`,`target_status_2` FROM `race_course_set_status` WHERE `course_set_status_id`=?;");
            }
        
            return _selectQuery_masterRaceCourseSetStatus;
        }
        
        /// <summary>
        /// SELECT `course_set_status_id`,`target_status_1`,`target_status_2` FROM `race_course_set_status`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceCourseSetStatus()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `course_set_status_id`,`target_status_1`,`target_status_2` FROM `race_course_set_status`;");
        }
        
        // SQL statements for race/race_env_define
        
        /// <summary>
        /// SELECT `race_track_id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceEnvDefine()
        {
            if (_selectQuery_masterRaceEnvDefine == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceEnvDefine = connection.PreparedQuery("SELECT `race_track_id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceEnvDefine;
        }
        
        /// <summary>
        /// SELECT `id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define` WHERE `race_track_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceEnvDefine_RaceTrackId()
        {
            if (_indexedSelectQuery_raceEnvDefine_raceTrackId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceEnvDefine_raceTrackId = connection.PreparedQuery("SELECT `id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define` WHERE `race_track_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_raceEnvDefine_raceTrackId;
        }
        
        /// <summary>
        /// SELECT `id`,`race_track_id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceEnvDefine()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`race_track_id`,`season`,`weather`,`timezone`,`resource` FROM `race_env_define`;");
        }
        
        // SQL statements for race/race_fence_set
        
        /// <summary>
        /// SELECT `fence_1`,`fence_2`,`fence_3`,`fence_4`,`fence_5`,`fence_6`,`fence_7`,`fence_8` FROM `race_fence_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceFenceSet()
        {
            if (_selectQuery_masterRaceFenceSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceFenceSet = connection.PreparedQuery("SELECT `fence_1`,`fence_2`,`fence_3`,`fence_4`,`fence_5`,`fence_6`,`fence_7`,`fence_8` FROM `race_fence_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceFenceSet;
        }
        
        /// <summary>
        /// SELECT `id`,`fence_1`,`fence_2`,`fence_3`,`fence_4`,`fence_5`,`fence_6`,`fence_7`,`fence_8` FROM `race_fence_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceFenceSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`fence_1`,`fence_2`,`fence_3`,`fence_4`,`fence_5`,`fence_6`,`fence_7`,`fence_8` FROM `race_fence_set`;");
        }
        
        // SQL statements for race/race_instance
        
        /// <summary>
        /// SELECT `race_id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceInstance()
        {
            if (_selectQuery_masterRaceInstance == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceInstance = connection.PreparedQuery("SELECT `race_id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceInstance;
        }
        
        /// <summary>
        /// SELECT `id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance` WHERE `race_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceInstance_RaceId()
        {
            if (_indexedSelectQuery_raceInstance_raceId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceInstance_raceId = connection.PreparedQuery("SELECT `id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance` WHERE `race_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_raceInstance_raceId;
        }
        
        /// <summary>
        /// SELECT `id`,`race_id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceInstance()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`race_id`,`npc_group_id`,`date`,`time`,`clock_time`,`race_number` FROM `race_instance`;");
        }
        
        // SQL statements for race/race_bgm
        
        /// <summary>
        /// SELECT `race_type`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceBgm()
        {
            if (_selectQuery_masterRaceBgm == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceBgm = connection.PreparedQuery("SELECT `race_type`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceBgm;
        }
        
        /// <summary>
        /// SELECT `id`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm` WHERE `race_type`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceBgm_RaceType()
        {
            if (_indexedSelectQuery_raceBgm_raceType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceBgm_raceType = connection.PreparedQuery("SELECT `id`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm` WHERE `race_type`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_raceBgm_raceType;
        }
        
        /// <summary>
        /// SELECT `id`,`race_type`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceBgm()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`race_type`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm`;");
        }
        
        // SQL statements for race/race_bgm_pattern
        
        /// <summary>
        /// SELECT `bgm_time_1`,`bgm_cue_name_1`,`bgm_cuesheet_name_1`,`bgm_time_2`,`bgm_cue_name_2`,`bgm_cuesheet_name_2`,`bgm_time_3`,`bgm_cue_name_3`,`bgm_cuesheet_name_3`,`bgm_time_4`,`bgm_cue_name_4`,`bgm_cuesheet_name_4`,`bgm_time_5`,`bgm_cue_name_5`,`bgm_cuesheet_name_5`,`bgm_time_6`,`bgm_cue_name_6`,`bgm_cuesheet_name_6`,`bgm_time_7`,`bgm_cue_name_7`,`bgm_cuesheet_name_7`,`bgm_time_8`,`bgm_cue_name_8`,`bgm_cuesheet_name_8`,`bgm_time_9`,`bgm_cue_name_9`,`bgm_cuesheet_name_9`,`bgm_time_10`,`bgm_cue_name_10`,`bgm_cuesheet_name_10`,`bgm_time_11`,`bgm_cue_name_11`,`bgm_cuesheet_name_11`,`bgm_time_12`,`bgm_cue_name_12`,`bgm_cuesheet_name_12`,`bgm_time_13`,`bgm_cue_name_13`,`bgm_cuesheet_name_13`,`bgm_time_14`,`bgm_cue_name_14`,`bgm_cuesheet_name_14`,`bgm_time_15`,`bgm_cue_name_15`,`bgm_cuesheet_name_15`,`bgm_time_16`,`bgm_cue_name_16`,`bgm_cuesheet_name_16`,`bgm_time_17`,`bgm_cue_name_17`,`bgm_cuesheet_name_17`,`bgm_time_18`,`bgm_cue_name_18`,`bgm_cuesheet_name_18`,`bgm_time_19`,`bgm_cue_name_19`,`bgm_cuesheet_name_19`,`bgm_time_20`,`bgm_cue_name_20`,`bgm_cuesheet_name_20`,`bgm_time_21`,`bgm_cue_name_21`,`bgm_cuesheet_name_21`,`bgm_time_22`,`bgm_cue_name_22`,`bgm_cuesheet_name_22`,`bgm_time_23`,`bgm_cue_name_23`,`bgm_cuesheet_name_23`,`bgm_time_24`,`bgm_cue_name_24`,`bgm_cuesheet_name_24`,`bgm_time_25`,`bgm_cue_name_25`,`bgm_cuesheet_name_25`,`bgm_time_26`,`bgm_cue_name_26`,`bgm_cuesheet_name_26`,`bgm_time_27`,`bgm_cue_name_27`,`bgm_cuesheet_name_27`,`bgm_time_28`,`bgm_cue_name_28`,`bgm_cuesheet_name_28`,`bgm_time_29`,`bgm_cue_name_29`,`bgm_cuesheet_name_29`,`bgm_time_30`,`bgm_cue_name_30`,`bgm_cuesheet_name_30`,`bgm_trigger_time_ago`,`bgm_trigger_cue_name`,`bgm_trigger_cuesheet_name`,`bgm_skip_trigger_cue_name`,`bgm_skip_trigger_cuesheet_name` FROM `race_bgm_pattern` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceBgmPattern()
        {
            if (_selectQuery_masterRaceBgmPattern == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceBgmPattern = connection.PreparedQuery("SELECT `bgm_time_1`,`bgm_cue_name_1`,`bgm_cuesheet_name_1`,`bgm_time_2`,`bgm_cue_name_2`,`bgm_cuesheet_name_2`,`bgm_time_3`,`bgm_cue_name_3`,`bgm_cuesheet_name_3`,`bgm_time_4`,`bgm_cue_name_4`,`bgm_cuesheet_name_4`,`bgm_time_5`,`bgm_cue_name_5`,`bgm_cuesheet_name_5`,`bgm_time_6`,`bgm_cue_name_6`,`bgm_cuesheet_name_6`,`bgm_time_7`,`bgm_cue_name_7`,`bgm_cuesheet_name_7`,`bgm_time_8`,`bgm_cue_name_8`,`bgm_cuesheet_name_8`,`bgm_time_9`,`bgm_cue_name_9`,`bgm_cuesheet_name_9`,`bgm_time_10`,`bgm_cue_name_10`,`bgm_cuesheet_name_10`,`bgm_time_11`,`bgm_cue_name_11`,`bgm_cuesheet_name_11`,`bgm_time_12`,`bgm_cue_name_12`,`bgm_cuesheet_name_12`,`bgm_time_13`,`bgm_cue_name_13`,`bgm_cuesheet_name_13`,`bgm_time_14`,`bgm_cue_name_14`,`bgm_cuesheet_name_14`,`bgm_time_15`,`bgm_cue_name_15`,`bgm_cuesheet_name_15`,`bgm_time_16`,`bgm_cue_name_16`,`bgm_cuesheet_name_16`,`bgm_time_17`,`bgm_cue_name_17`,`bgm_cuesheet_name_17`,`bgm_time_18`,`bgm_cue_name_18`,`bgm_cuesheet_name_18`,`bgm_time_19`,`bgm_cue_name_19`,`bgm_cuesheet_name_19`,`bgm_time_20`,`bgm_cue_name_20`,`bgm_cuesheet_name_20`,`bgm_time_21`,`bgm_cue_name_21`,`bgm_cuesheet_name_21`,`bgm_time_22`,`bgm_cue_name_22`,`bgm_cuesheet_name_22`,`bgm_time_23`,`bgm_cue_name_23`,`bgm_cuesheet_name_23`,`bgm_time_24`,`bgm_cue_name_24`,`bgm_cuesheet_name_24`,`bgm_time_25`,`bgm_cue_name_25`,`bgm_cuesheet_name_25`,`bgm_time_26`,`bgm_cue_name_26`,`bgm_cuesheet_name_26`,`bgm_time_27`,`bgm_cue_name_27`,`bgm_cuesheet_name_27`,`bgm_time_28`,`bgm_cue_name_28`,`bgm_cuesheet_name_28`,`bgm_time_29`,`bgm_cue_name_29`,`bgm_cuesheet_name_29`,`bgm_time_30`,`bgm_cue_name_30`,`bgm_cuesheet_name_30`,`bgm_trigger_time_ago`,`bgm_trigger_cue_name`,`bgm_trigger_cuesheet_name`,`bgm_skip_trigger_cue_name`,`bgm_skip_trigger_cuesheet_name` FROM `race_bgm_pattern` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceBgmPattern;
        }
        
        /// <summary>
        /// SELECT `id`,`bgm_time_1`,`bgm_cue_name_1`,`bgm_cuesheet_name_1`,`bgm_time_2`,`bgm_cue_name_2`,`bgm_cuesheet_name_2`,`bgm_time_3`,`bgm_cue_name_3`,`bgm_cuesheet_name_3`,`bgm_time_4`,`bgm_cue_name_4`,`bgm_cuesheet_name_4`,`bgm_time_5`,`bgm_cue_name_5`,`bgm_cuesheet_name_5`,`bgm_time_6`,`bgm_cue_name_6`,`bgm_cuesheet_name_6`,`bgm_time_7`,`bgm_cue_name_7`,`bgm_cuesheet_name_7`,`bgm_time_8`,`bgm_cue_name_8`,`bgm_cuesheet_name_8`,`bgm_time_9`,`bgm_cue_name_9`,`bgm_cuesheet_name_9`,`bgm_time_10`,`bgm_cue_name_10`,`bgm_cuesheet_name_10`,`bgm_time_11`,`bgm_cue_name_11`,`bgm_cuesheet_name_11`,`bgm_time_12`,`bgm_cue_name_12`,`bgm_cuesheet_name_12`,`bgm_time_13`,`bgm_cue_name_13`,`bgm_cuesheet_name_13`,`bgm_time_14`,`bgm_cue_name_14`,`bgm_cuesheet_name_14`,`bgm_time_15`,`bgm_cue_name_15`,`bgm_cuesheet_name_15`,`bgm_time_16`,`bgm_cue_name_16`,`bgm_cuesheet_name_16`,`bgm_time_17`,`bgm_cue_name_17`,`bgm_cuesheet_name_17`,`bgm_time_18`,`bgm_cue_name_18`,`bgm_cuesheet_name_18`,`bgm_time_19`,`bgm_cue_name_19`,`bgm_cuesheet_name_19`,`bgm_time_20`,`bgm_cue_name_20`,`bgm_cuesheet_name_20`,`bgm_time_21`,`bgm_cue_name_21`,`bgm_cuesheet_name_21`,`bgm_time_22`,`bgm_cue_name_22`,`bgm_cuesheet_name_22`,`bgm_time_23`,`bgm_cue_name_23`,`bgm_cuesheet_name_23`,`bgm_time_24`,`bgm_cue_name_24`,`bgm_cuesheet_name_24`,`bgm_time_25`,`bgm_cue_name_25`,`bgm_cuesheet_name_25`,`bgm_time_26`,`bgm_cue_name_26`,`bgm_cuesheet_name_26`,`bgm_time_27`,`bgm_cue_name_27`,`bgm_cuesheet_name_27`,`bgm_time_28`,`bgm_cue_name_28`,`bgm_cuesheet_name_28`,`bgm_time_29`,`bgm_cue_name_29`,`bgm_cuesheet_name_29`,`bgm_time_30`,`bgm_cue_name_30`,`bgm_cuesheet_name_30`,`bgm_trigger_time_ago`,`bgm_trigger_cue_name`,`bgm_trigger_cuesheet_name`,`bgm_skip_trigger_cue_name`,`bgm_skip_trigger_cuesheet_name` FROM `race_bgm_pattern`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceBgmPattern()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`bgm_time_1`,`bgm_cue_name_1`,`bgm_cuesheet_name_1`,`bgm_time_2`,`bgm_cue_name_2`,`bgm_cuesheet_name_2`,`bgm_time_3`,`bgm_cue_name_3`,`bgm_cuesheet_name_3`,`bgm_time_4`,`bgm_cue_name_4`,`bgm_cuesheet_name_4`,`bgm_time_5`,`bgm_cue_name_5`,`bgm_cuesheet_name_5`,`bgm_time_6`,`bgm_cue_name_6`,`bgm_cuesheet_name_6`,`bgm_time_7`,`bgm_cue_name_7`,`bgm_cuesheet_name_7`,`bgm_time_8`,`bgm_cue_name_8`,`bgm_cuesheet_name_8`,`bgm_time_9`,`bgm_cue_name_9`,`bgm_cuesheet_name_9`,`bgm_time_10`,`bgm_cue_name_10`,`bgm_cuesheet_name_10`,`bgm_time_11`,`bgm_cue_name_11`,`bgm_cuesheet_name_11`,`bgm_time_12`,`bgm_cue_name_12`,`bgm_cuesheet_name_12`,`bgm_time_13`,`bgm_cue_name_13`,`bgm_cuesheet_name_13`,`bgm_time_14`,`bgm_cue_name_14`,`bgm_cuesheet_name_14`,`bgm_time_15`,`bgm_cue_name_15`,`bgm_cuesheet_name_15`,`bgm_time_16`,`bgm_cue_name_16`,`bgm_cuesheet_name_16`,`bgm_time_17`,`bgm_cue_name_17`,`bgm_cuesheet_name_17`,`bgm_time_18`,`bgm_cue_name_18`,`bgm_cuesheet_name_18`,`bgm_time_19`,`bgm_cue_name_19`,`bgm_cuesheet_name_19`,`bgm_time_20`,`bgm_cue_name_20`,`bgm_cuesheet_name_20`,`bgm_time_21`,`bgm_cue_name_21`,`bgm_cuesheet_name_21`,`bgm_time_22`,`bgm_cue_name_22`,`bgm_cuesheet_name_22`,`bgm_time_23`,`bgm_cue_name_23`,`bgm_cuesheet_name_23`,`bgm_time_24`,`bgm_cue_name_24`,`bgm_cuesheet_name_24`,`bgm_time_25`,`bgm_cue_name_25`,`bgm_cuesheet_name_25`,`bgm_time_26`,`bgm_cue_name_26`,`bgm_cuesheet_name_26`,`bgm_time_27`,`bgm_cue_name_27`,`bgm_cuesheet_name_27`,`bgm_time_28`,`bgm_cue_name_28`,`bgm_cuesheet_name_28`,`bgm_time_29`,`bgm_cue_name_29`,`bgm_cuesheet_name_29`,`bgm_time_30`,`bgm_cue_name_30`,`bgm_cuesheet_name_30`,`bgm_trigger_time_ago`,`bgm_trigger_cue_name`,`bgm_trigger_cuesheet_name`,`bgm_skip_trigger_cue_name`,`bgm_skip_trigger_cuesheet_name` FROM `race_bgm_pattern`;");
        }
        
        // SQL statements for race/race_bgm_cutin
        
        /// <summary>
        /// SELECT `card_id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceBgmCutin()
        {
            if (_selectQuery_masterRaceBgmCutin == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceBgmCutin = connection.PreparedQuery("SELECT `card_id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceBgmCutin;
        }
        
        /// <summary>
        /// SELECT `id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `card_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceBgmCutin_CardId()
        {
            if (_indexedSelectQuery_raceBgmCutin_cardId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceBgmCutin_cardId = connection.PreparedQuery("SELECT `id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `card_id`=?;");
            }
        
            return _indexedSelectQuery_raceBgmCutin_cardId;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `bgm_group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceBgmCutin_BgmGroupId()
        {
            if (_indexedSelectQuery_raceBgmCutin_bgmGroupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceBgmCutin_bgmGroupId = connection.PreparedQuery("SELECT `id`,`card_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `bgm_group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_raceBgmCutin_bgmGroupId;
        }
        
        /// <summary>
        /// SELECT `id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `card_id`=? AND `bgm_group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceBgmCutin_CardId_BgmGroupId()
        {
            if (_indexedSelectQuery_raceBgmCutin_cardId_bgmGroupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceBgmCutin_cardId_bgmGroupId = connection.PreparedQuery("SELECT `id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin` WHERE `card_id`=? AND `bgm_group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_raceBgmCutin_cardId_bgmGroupId;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceBgmCutin()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`card_id`,`bgm_group_id`,`fadeout_time`,`exclusive_control`,`short_cutin_bgm_cue_name`,`short_cutin_bgm_cuesheet_name`,`long_cutin_bgm_cue_name`,`long_cutin_bgm_cuesheet_name` FROM `race_bgm_cutin`;");
        }
        
        // SQL statements for race/race_jikkyo_base
        
        /// <summary>
        /// SELECT `mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceJikkyoBase()
        {
            if (_selectQuery_masterRaceJikkyoBase == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceJikkyoBase = connection.PreparedQuery("SELECT `mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceJikkyoBase;
        }
        
        /// <summary>
        /// SELECT `id`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base` WHERE `mode`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceJikkyoBase_Mode()
        {
            if (_indexedSelectQuery_raceJikkyoBase_mode == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceJikkyoBase_mode = connection.PreparedQuery("SELECT `id`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base` WHERE `mode`=?;");
            }
        
            return _indexedSelectQuery_raceJikkyoBase_mode;
        }
        
        /// <summary>
        /// SELECT `id`,`mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceJikkyoBase()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base`;");
        }
        
        // SQL statements for race/race_jikkyo_base_venus
        
        /// <summary>
        /// SELECT `mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`disable_reuse` FROM `race_jikkyo_base_venus` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceJikkyoBaseVenus()
        {
            if (_selectQuery_masterRaceJikkyoBaseVenus == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceJikkyoBaseVenus = connection.PreparedQuery("SELECT `mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`disable_reuse` FROM `race_jikkyo_base_venus` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceJikkyoBaseVenus;
        }
        
        /// <summary>
        /// SELECT `id`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`disable_reuse` FROM `race_jikkyo_base_venus` WHERE `mode`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceJikkyoBaseVenus_Mode()
        {
            if (_indexedSelectQuery_raceJikkyoBaseVenus_mode == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceJikkyoBaseVenus_mode = connection.PreparedQuery("SELECT `id`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`disable_reuse` FROM `race_jikkyo_base_venus` WHERE `mode`=?;");
            }
        
            return _indexedSelectQuery_raceJikkyoBaseVenus_mode;
        }
        
        /// <summary>
        /// SELECT `id`,`mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`disable_reuse` FROM `race_jikkyo_base_venus`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceJikkyoBaseVenus()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`disable_reuse` FROM `race_jikkyo_base_venus`;");
        }
        
        // SQL statements for race/race_jikkyo_comment
        
        /// <summary>
        /// SELECT `group_id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceJikkyoComment()
        {
            if (_selectQuery_masterRaceJikkyoComment == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceJikkyoComment = connection.PreparedQuery("SELECT `group_id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceJikkyoComment;
        }
        
        /// <summary>
        /// SELECT `id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment` WHERE `group_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceJikkyoComment_GroupId()
        {
            if (_indexedSelectQuery_raceJikkyoComment_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceJikkyoComment_groupId = connection.PreparedQuery("SELECT `id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment` WHERE `group_id`=?;");
            }
        
            return _indexedSelectQuery_raceJikkyoComment_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`message`,`voice`,`per`,`omit_tag` FROM `race_jikkyo_comment` WHERE `message_group`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceJikkyoComment_MessageGroup()
        {
            if (_indexedSelectQuery_raceJikkyoComment_messageGroup == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceJikkyoComment_messageGroup = connection.PreparedQuery("SELECT `id`,`group_id`,`message`,`voice`,`per`,`omit_tag` FROM `race_jikkyo_comment` WHERE `message_group`=?;");
            }
        
            return _indexedSelectQuery_raceJikkyoComment_messageGroup;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceJikkyoComment()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group_id`,`message`,`voice`,`per`,`message_group`,`omit_tag` FROM `race_jikkyo_comment`;");
        }
        
        // SQL statements for race/race_jikkyo_cue
        
        /// <summary>
        /// SELECT `condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name` FROM `race_jikkyo_cue` WHERE `id`=? AND `cuesheet_type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceJikkyoCue()
        {
            if (_selectQuery_masterRaceJikkyoCue == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceJikkyoCue = connection.PreparedQuery("SELECT `condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name` FROM `race_jikkyo_cue` WHERE `id`=? AND `cuesheet_type`=?;");
            }
        
            return _selectQuery_masterRaceJikkyoCue;
        }
        
        /// <summary>
        /// SELECT `id`,`condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name` FROM `race_jikkyo_cue` WHERE `cuesheet_type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceJikkyoCue_CuesheetType()
        {
            if (_indexedSelectQuery_raceJikkyoCue_cuesheetType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceJikkyoCue_cuesheetType = connection.PreparedQuery("SELECT `id`,`condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name` FROM `race_jikkyo_cue` WHERE `cuesheet_type`=?;");
            }
        
            return _indexedSelectQuery_raceJikkyoCue_cuesheetType;
        }
        
        /// <summary>
        /// SELECT `id`,`condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name`,`cuesheet_type` FROM `race_jikkyo_cue`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceJikkyoCue()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`condition_type_1`,`condition_value_1`,`condition_type_2`,`condition_value_2`,`condition_type_3`,`condition_value_3`,`cuesheet_name`,`cuesheet_type` FROM `race_jikkyo_cue`;");
        }
        
        // SQL statements for race/race_jikkyo_message
        
        /// <summary>
        /// SELECT `group_id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceJikkyoMessage()
        {
            if (_selectQuery_masterRaceJikkyoMessage == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceJikkyoMessage = connection.PreparedQuery("SELECT `group_id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceJikkyoMessage;
        }
        
        /// <summary>
        /// SELECT `id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `group_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceJikkyoMessage_GroupId()
        {
            if (_indexedSelectQuery_raceJikkyoMessage_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceJikkyoMessage_groupId = connection.PreparedQuery("SELECT `id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `group_id`=?;");
            }
        
            return _indexedSelectQuery_raceJikkyoMessage_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`message`,`voice`,`per`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `comment_group`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceJikkyoMessage_CommentGroup()
        {
            if (_indexedSelectQuery_raceJikkyoMessage_commentGroup == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceJikkyoMessage_commentGroup = connection.PreparedQuery("SELECT `id`,`group_id`,`message`,`voice`,`per`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `comment_group`=?;");
            }
        
            return _indexedSelectQuery_raceJikkyoMessage_commentGroup;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceJikkyoMessage()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group_id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message`;");
        }
        
        // SQL statements for race/race_jikkyo_race
        
        /// <summary>
        /// SELECT `cue_id` FROM `race_jikkyo_race` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceJikkyoRace()
        {
            if (_selectQuery_masterRaceJikkyoRace == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceJikkyoRace = connection.PreparedQuery("SELECT `cue_id` FROM `race_jikkyo_race` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceJikkyoRace;
        }
        
        /// <summary>
        /// SELECT `id`,`cue_id` FROM `race_jikkyo_race`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceJikkyoRace()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`cue_id` FROM `race_jikkyo_race`;");
        }
        
        // SQL statements for race/race_jikkyo_trigger
        
        /// <summary>
        /// SELECT `command`,`inequality`,`horse1`,`horse2`,`param1`,`param2` FROM `race_jikkyo_trigger` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceJikkyoTrigger()
        {
            if (_selectQuery_masterRaceJikkyoTrigger == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceJikkyoTrigger = connection.PreparedQuery("SELECT `command`,`inequality`,`horse1`,`horse2`,`param1`,`param2` FROM `race_jikkyo_trigger` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceJikkyoTrigger;
        }
        
        /// <summary>
        /// SELECT `id`,`command`,`inequality`,`horse1`,`horse2`,`param1`,`param2` FROM `race_jikkyo_trigger`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceJikkyoTrigger()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`command`,`inequality`,`horse1`,`horse2`,`param1`,`param2` FROM `race_jikkyo_trigger`;");
        }
        
        // SQL statements for race/race_player_camera
        
        /// <summary>
        /// SELECT `priority`,`prefab_name`,`category` FROM `race_player_camera` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RacePlayerCamera()
        {
            if (_selectQuery_masterRacePlayerCamera == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRacePlayerCamera = connection.PreparedQuery("SELECT `priority`,`prefab_name`,`category` FROM `race_player_camera` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRacePlayerCamera;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`category` FROM `race_player_camera` WHERE `prefab_name`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RacePlayerCamera_PrefabName()
        {
            if (_indexedSelectQuery_racePlayerCamera_prefabName == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_racePlayerCamera_prefabName = connection.PreparedQuery("SELECT `id`,`priority`,`category` FROM `race_player_camera` WHERE `prefab_name`=?;");
            }
        
            return _indexedSelectQuery_racePlayerCamera_prefabName;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`prefab_name` FROM `race_player_camera` WHERE `category`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RacePlayerCamera_Category()
        {
            if (_indexedSelectQuery_racePlayerCamera_category == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_racePlayerCamera_category = connection.PreparedQuery("SELECT `id`,`priority`,`prefab_name` FROM `race_player_camera` WHERE `category`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_racePlayerCamera_category;
        }
        
        /// <summary>
        /// SELECT `id`,`priority`,`prefab_name`,`category` FROM `race_player_camera`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RacePlayerCamera()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`priority`,`prefab_name`,`category` FROM `race_player_camera`;");
        }
        
        // SQL statements for race/race_track
        
        /// <summary>
        /// SELECT `initial_lane_type`,`enable_half_gate`,`horse_num_gate_variation`,`turf_vision_type`,`footsmoke_color_type`,`area`,`flag_type`,`gate_panel_type`,`gate_lamp_type`,`board_condition_type`,`result_board_type` FROM `race_track` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceTrack()
        {
            if (_selectQuery_masterRaceTrack == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceTrack = connection.PreparedQuery("SELECT `initial_lane_type`,`enable_half_gate`,`horse_num_gate_variation`,`turf_vision_type`,`footsmoke_color_type`,`area`,`flag_type`,`gate_panel_type`,`gate_lamp_type`,`board_condition_type`,`result_board_type` FROM `race_track` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceTrack;
        }
        
        /// <summary>
        /// SELECT `id`,`initial_lane_type`,`enable_half_gate`,`horse_num_gate_variation`,`turf_vision_type`,`footsmoke_color_type`,`area`,`flag_type`,`gate_panel_type`,`gate_lamp_type`,`board_condition_type`,`result_board_type` FROM `race_track`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceTrack()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`initial_lane_type`,`enable_half_gate`,`horse_num_gate_variation`,`turf_vision_type`,`footsmoke_color_type`,`area`,`flag_type`,`gate_panel_type`,`gate_lamp_type`,`board_condition_type`,`result_board_type` FROM `race_track`;");
        }
        
        // SQL statements for race/race_condition
        
        /// <summary>
        /// SELECT `area`,`season`,`weather`,`ground`,`rate` FROM `race_condition` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceCondition()
        {
            if (_selectQuery_masterRaceCondition == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceCondition = connection.PreparedQuery("SELECT `area`,`season`,`weather`,`ground`,`rate` FROM `race_condition` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceCondition;
        }
        
        /// <summary>
        /// SELECT `id`,`area`,`weather`,`rate` FROM `race_condition` WHERE `season`=? AND `ground`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceCondition_Season_Ground()
        {
            if (_indexedSelectQuery_raceCondition_season_ground == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceCondition_season_ground = connection.PreparedQuery("SELECT `id`,`area`,`weather`,`rate` FROM `race_condition` WHERE `season`=? AND `ground`=?;");
            }
        
            return _indexedSelectQuery_raceCondition_season_ground;
        }
        
        /// <summary>
        /// SELECT `id`,`area`,`ground`,`rate` FROM `race_condition` WHERE `season`=? AND `weather`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceCondition_Season_Weather()
        {
            if (_indexedSelectQuery_raceCondition_season_weather == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceCondition_season_weather = connection.PreparedQuery("SELECT `id`,`area`,`ground`,`rate` FROM `race_condition` WHERE `season`=? AND `weather`=?;");
            }
        
            return _indexedSelectQuery_raceCondition_season_weather;
        }
        
        /// <summary>
        /// SELECT `id`,`area`,`season`,`rate` FROM `race_condition` WHERE `weather`=? AND `ground`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceCondition_Weather_Ground()
        {
            if (_indexedSelectQuery_raceCondition_weather_ground == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceCondition_weather_ground = connection.PreparedQuery("SELECT `id`,`area`,`season`,`rate` FROM `race_condition` WHERE `weather`=? AND `ground`=?;");
            }
        
            return _indexedSelectQuery_raceCondition_weather_ground;
        }
        
        /// <summary>
        /// SELECT `id`,`area`,`season`,`rate` FROM `race_condition` WHERE `ground`=? AND `weather`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceCondition_Ground_Weather()
        {
            if (_indexedSelectQuery_raceCondition_ground_weather == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceCondition_ground_weather = connection.PreparedQuery("SELECT `id`,`area`,`season`,`rate` FROM `race_condition` WHERE `ground`=? AND `weather`=?;");
            }
        
            return _indexedSelectQuery_raceCondition_ground_weather;
        }
        
        /// <summary>
        /// SELECT `id`,`area`,`season`,`weather`,`ground`,`rate` FROM `race_condition`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceCondition()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`area`,`season`,`weather`,`ground`,`rate` FROM `race_condition`;");
        }
        
        // SQL statements for race/race_trophy
        
        /// <summary>
        /// SELECT `trophy_id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceTrophy()
        {
            if (_selectQuery_masterRaceTrophy == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceTrophy = connection.PreparedQuery("SELECT `trophy_id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceTrophy;
        }
        
        /// <summary>
        /// SELECT `id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `trophy_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceTrophy_TrophyId()
        {
            if (_indexedSelectQuery_raceTrophy_trophyId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceTrophy_trophyId = connection.PreparedQuery("SELECT `id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `trophy_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_raceTrophy_trophyId;
        }
        
        /// <summary>
        /// SELECT `id`,`trophy_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `race_instance_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RaceTrophy_RaceInstanceId()
        {
            if (_indexedSelectQuery_raceTrophy_raceInstanceId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_raceTrophy_raceInstanceId = connection.PreparedQuery("SELECT `id`,`trophy_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy` WHERE `race_instance_id`=?;");
            }
        
            return _indexedSelectQuery_raceTrophy_raceInstanceId;
        }
        
        /// <summary>
        /// SELECT `id`,`trophy_id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceTrophy()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`trophy_id`,`race_instance_id`,`original_flag`,`disp_order`,`size`,`event_type`,`start_date`,`display_end_date` FROM `race_trophy`;");
        }
        
        // SQL statements for race/race_overrun_pattern
        
        /// <summary>
        /// SELECT `finish_order_0_type`,`finish_order_1_type`,`finish_order_2_type`,`finish_order_3_type`,`finish_order_4_type`,`finish_order_lose_type` FROM `race_overrun_pattern` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RaceOverrunPattern()
        {
            if (_selectQuery_masterRaceOverrunPattern == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRaceOverrunPattern = connection.PreparedQuery("SELECT `finish_order_0_type`,`finish_order_1_type`,`finish_order_2_type`,`finish_order_3_type`,`finish_order_4_type`,`finish_order_lose_type` FROM `race_overrun_pattern` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRaceOverrunPattern;
        }
        
        /// <summary>
        /// SELECT `id`,`finish_order_0_type`,`finish_order_1_type`,`finish_order_2_type`,`finish_order_3_type`,`finish_order_4_type`,`finish_order_lose_type` FROM `race_overrun_pattern`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RaceOverrunPattern()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`finish_order_0_type`,`finish_order_1_type`,`finish_order_2_type`,`finish_order_3_type`,`finish_order_4_type`,`finish_order_lose_type` FROM `race_overrun_pattern`;");
        }
        
        // SQL statements for race/mob_data
        
        /// <summary>
        /// SELECT `chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`chara_height`,`chara_bust_size`,`socks`,`default_personality`,`race_personality`,`race_running_type`,`sex`,`dress_id`,`dress_color_id`,`use_live`,`hair_cutoff`,`attachment_model_id`,`capture_type`,`mayu_visible_type` FROM `mob_data` WHERE `mob_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_MobData()
        {
            if (_selectQuery_masterMobData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterMobData = connection.PreparedQuery("SELECT `chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`chara_height`,`chara_bust_size`,`socks`,`default_personality`,`race_personality`,`race_running_type`,`sex`,`dress_id`,`dress_color_id`,`use_live`,`hair_cutoff`,`attachment_model_id`,`capture_type`,`mayu_visible_type` FROM `mob_data` WHERE `mob_id`=?;");
            }
        
            return _selectQuery_masterMobData;
        }
        
        /// <summary>
        /// SELECT `mob_id`,`chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`chara_height`,`chara_bust_size`,`socks`,`default_personality`,`race_personality`,`race_running_type`,`sex`,`dress_id`,`dress_color_id`,`use_live`,`hair_cutoff`,`attachment_model_id`,`capture_type`,`mayu_visible_type` FROM `mob_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_MobData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `mob_id`,`chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`chara_height`,`chara_bust_size`,`socks`,`default_personality`,`race_personality`,`race_running_type`,`sex`,`dress_id`,`dress_color_id`,`use_live`,`hair_cutoff`,`attachment_model_id`,`capture_type`,`mayu_visible_type` FROM `mob_data`;");
        }
        
        // SQL statements for race/mob_dress_color_set
        
        /// <summary>
        /// SELECT `color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `mob_dress_color_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_MobDressColorSet()
        {
            if (_selectQuery_masterMobDressColorSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterMobDressColorSet = connection.PreparedQuery("SELECT `color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `mob_dress_color_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterMobDressColorSet;
        }
        
        /// <summary>
        /// SELECT `id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `mob_dress_color_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_MobDressColorSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `mob_dress_color_set`;");
        }
        
        // SQL statements for race/mob_hair_color_set
        
        /// <summary>
        /// SELECT `hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2`,`tail_color_r1`,`tail_color_r2`,`tail_color_g1`,`tail_color_g2`,`tail_color_b1`,`tail_color_b2`,`tail_toon_color_r1`,`tail_toon_color_r2`,`tail_toon_color_g1`,`tail_toon_color_g2`,`tail_toon_color_b1`,`tail_toon_color_b2` FROM `mob_hair_color_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_MobHairColorSet()
        {
            if (_selectQuery_masterMobHairColorSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterMobHairColorSet = connection.PreparedQuery("SELECT `hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2`,`tail_color_r1`,`tail_color_r2`,`tail_color_g1`,`tail_color_g2`,`tail_color_b1`,`tail_color_b2`,`tail_toon_color_r1`,`tail_toon_color_r2`,`tail_toon_color_g1`,`tail_toon_color_g2`,`tail_toon_color_b1`,`tail_toon_color_b2` FROM `mob_hair_color_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterMobHairColorSet;
        }
        
        /// <summary>
        /// SELECT `id`,`hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2`,`tail_color_r1`,`tail_color_r2`,`tail_color_g1`,`tail_color_g2`,`tail_color_b1`,`tail_color_b2`,`tail_toon_color_r1`,`tail_toon_color_r2`,`tail_toon_color_g1`,`tail_toon_color_g2`,`tail_toon_color_b1`,`tail_toon_color_b2` FROM `mob_hair_color_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_MobHairColorSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2`,`tail_color_r1`,`tail_color_r2`,`tail_color_g1`,`tail_color_g2`,`tail_color_b1`,`tail_color_b2`,`tail_toon_color_r1`,`tail_toon_color_r2`,`tail_toon_color_g1`,`tail_toon_color_g2`,`tail_toon_color_b1`,`tail_toon_color_b2` FROM `mob_hair_color_set`;");
        }
        
        // SQL statements for race/audience_data
        
        /// <summary>
        /// SELECT `chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`prop_id_1`,`prop_attach_node_name_type_1`,`prop_id_2`,`prop_attach_node_name_type_2`,`chara_height`,`shape`,`chara_bust_size`,`dress_id`,`dress_color_id` FROM `audience_data` WHERE `audience_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_AudienceData()
        {
            if (_selectQuery_masterAudienceData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterAudienceData = connection.PreparedQuery("SELECT `chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`prop_id_1`,`prop_attach_node_name_type_1`,`prop_id_2`,`prop_attach_node_name_type_2`,`chara_height`,`shape`,`chara_bust_size`,`dress_id`,`dress_color_id` FROM `audience_data` WHERE `audience_id`=?;");
            }
        
            return _selectQuery_masterAudienceData;
        }
        
        /// <summary>
        /// SELECT `audience_id`,`chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`prop_id_1`,`prop_attach_node_name_type_1`,`prop_id_2`,`prop_attach_node_name_type_2`,`chara_height`,`shape`,`chara_bust_size`,`dress_id`,`dress_color_id` FROM `audience_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_AudienceData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `audience_id`,`chara_face_model`,`chara_skin_color`,`chara_hair_model`,`chara_hair_color`,`prop_id_1`,`prop_attach_node_name_type_1`,`prop_id_2`,`prop_attach_node_name_type_2`,`chara_height`,`shape`,`chara_bust_size`,`dress_id`,`dress_color_id` FROM `audience_data`;");
        }
        
        // SQL statements for race/audience_dress_color_set
        
        /// <summary>
        /// SELECT `color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `audience_dress_color_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_AudienceDressColorSet()
        {
            if (_selectQuery_masterAudienceDressColorSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterAudienceDressColorSet = connection.PreparedQuery("SELECT `color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `audience_dress_color_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterAudienceDressColorSet;
        }
        
        /// <summary>
        /// SELECT `id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `audience_dress_color_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_AudienceDressColorSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`color_r1`,`color_r2`,`color_g1`,`color_g2`,`color_b1`,`color_b2`,`toon_color_r1`,`toon_color_r2`,`toon_color_g1`,`toon_color_g2`,`toon_color_b1`,`toon_color_b2` FROM `audience_dress_color_set`;");
        }
        
        // SQL statements for race/audience_hair_color_set
        
        /// <summary>
        /// SELECT `hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2` FROM `audience_hair_color_set` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_AudienceHairColorSet()
        {
            if (_selectQuery_masterAudienceHairColorSet == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterAudienceHairColorSet = connection.PreparedQuery("SELECT `hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2` FROM `audience_hair_color_set` WHERE `id`=?;");
            }
        
            return _selectQuery_masterAudienceHairColorSet;
        }
        
        /// <summary>
        /// SELECT `id`,`hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2` FROM `audience_hair_color_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_AudienceHairColorSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`hair_color_r1`,`hair_color_r2`,`hair_color_g1`,`hair_color_g2`,`hair_color_b1`,`hair_color_b2`,`hair_toon_color_r1`,`hair_toon_color_r2`,`hair_toon_color_g1`,`hair_toon_color_g2`,`hair_toon_color_b1`,`hair_toon_color_b2`,`mayu_color_r1`,`mayu_color_r2`,`mayu_color_g1`,`mayu_color_g2`,`mayu_color_b1`,`mayu_color_b2`,`mayu_toon_color_r1`,`mayu_toon_color_r2`,`mayu_toon_color_g1`,`mayu_toon_color_g2`,`mayu_toon_color_b1`,`mayu_toon_color_b2`,`eye_color_r1`,`eye_color_r2`,`eye_color_g1`,`eye_color_g2`,`eye_color_b1`,`eye_color_b2` FROM `audience_hair_color_set`;");
        }
        
        // SQL statements for race/audience_impostor
        
        /// <summary>
        /// SELECT `scene_type`,`audience_group_id`,`group_id`,`pattern_1`,`pattern_2`,`pattern_3`,`pattern_4`,`pattern_5`,`pattern_6`,`pattern_7`,`pattern_8`,`pattern_9`,`pattern_10` FROM `audience_impostor` WHERE `season`=? AND `weather`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_AudienceImpostor_Season_Weather()
        {
            if (_indexedSelectQuery_audienceImpostor_season_weather == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_audienceImpostor_season_weather = connection.PreparedQuery("SELECT `scene_type`,`audience_group_id`,`group_id`,`pattern_1`,`pattern_2`,`pattern_3`,`pattern_4`,`pattern_5`,`pattern_6`,`pattern_7`,`pattern_8`,`pattern_9`,`pattern_10` FROM `audience_impostor` WHERE `season`=? AND `weather`=?;");
            }
        
            return _indexedSelectQuery_audienceImpostor_season_weather;
        }
        
        /// <summary>
        /// SELECT `scene_type`,`audience_group_id`,`season`,`weather`,`group_id`,`pattern_1`,`pattern_2`,`pattern_3`,`pattern_4`,`pattern_5`,`pattern_6`,`pattern_7`,`pattern_8`,`pattern_9`,`pattern_10` FROM `audience_impostor`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_AudienceImpostor()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `scene_type`,`audience_group_id`,`season`,`weather`,`group_id`,`pattern_1`,`pattern_2`,`pattern_3`,`pattern_4`,`pattern_5`,`pattern_6`,`pattern_7`,`pattern_8`,`pattern_9`,`pattern_10` FROM `audience_impostor`;");
        }
        
        // SQL statements for race/audience_impostor_lottery
        
        /// <summary>
        /// SELECT `asset_id`,`odds` FROM `audience_impostor_lottery` WHERE `group_id`=? AND `pattern`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_AudienceImpostorLottery_GroupId_Pattern()
        {
            if (_indexedSelectQuery_audienceImpostorLottery_groupId_pattern == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_audienceImpostorLottery_groupId_pattern = connection.PreparedQuery("SELECT `asset_id`,`odds` FROM `audience_impostor_lottery` WHERE `group_id`=? AND `pattern`=?;");
            }
        
            return _indexedSelectQuery_audienceImpostorLottery_groupId_pattern;
        }
        
        /// <summary>
        /// SELECT `group_id`,`pattern`,`asset_id`,`odds` FROM `audience_impostor_lottery`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_AudienceImpostorLottery()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `group_id`,`pattern`,`asset_id`,`odds` FROM `audience_impostor_lottery`;");
        }
        
        // SQL statements for race/audience_impostor_color_set
        
        /// <summary>
        /// SELECT `color`,`odds` FROM `audience_impostor_color_set` WHERE `color_group_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_AudienceImpostorColorSet_ColorGroupId()
        {
            if (_indexedSelectQuery_audienceImpostorColorSet_colorGroupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_audienceImpostorColorSet_colorGroupId = connection.PreparedQuery("SELECT `color`,`odds` FROM `audience_impostor_color_set` WHERE `color_group_id`=?;");
            }
        
            return _indexedSelectQuery_audienceImpostorColorSet_colorGroupId;
        }
        
        /// <summary>
        /// SELECT `color_group_id`,`color`,`odds` FROM `audience_impostor_color_set`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_AudienceImpostorColorSet()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `color_group_id`,`color`,`odds` FROM `audience_impostor_color_set`;");
        }
    }
}
#endif
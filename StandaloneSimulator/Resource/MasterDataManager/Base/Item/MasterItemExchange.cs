// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/item_exchange
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:item_exchange_top_id], [:condition_type], [:pay_item_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterItemExchange : AbstractMasterData
    {
        public const string TABLE_NAME = "item_exchange";

        MasterItemDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ItemExchange> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ItemExchange>> _dictionaryWithItemExchangeTopId = null;
        private Dictionary<int, List<ItemExchange>> _dictionaryWithConditionType = null;
        private Dictionary<int, List<ItemExchange>> _dictionaryWithPayItemId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, ItemExchange> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterItemExchange");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterItemExchange(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ItemExchange>();
            _dictionaryWithItemExchangeTopId = new Dictionary<int, List<ItemExchange>>();
            _dictionaryWithConditionType = new Dictionary<int, List<ItemExchange>>();
            _dictionaryWithPayItemId = new Dictionary<int, List<ItemExchange>>();
            _db = db;
        }


        public ItemExchange Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterItemExchange");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterItemExchange", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ItemExchange _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ItemExchange();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchange");
                return null;
            }

            // SELECT `item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            ItemExchange orm = null;

            if (query.Step())
            {
                int itemExchangeTopId   = (int)query.GetInt(0);
                int dispOrder           = (int)query.GetInt(1);
                int conditionType       = (int)query.GetInt(2);
                int conditionValue1     = (int)query.GetInt(3);
                int conditionValue2     = (int)query.GetInt(4);
                int changeItemLimitType = (int)query.GetInt(5);
                int changeItemLimitNum  = (int)query.GetInt(6);
                int changeItemCategory  = (int)query.GetInt(7);
                int changeItemId        = (int)query.GetInt(8);
                int changeItemNum       = (int)query.GetInt(9);
                int additionalPieceNum  = (int)query.GetInt(10);
                int payItemCategory     = (int)query.GetInt(11);
                int payItemId           = (int)query.GetInt(12);
                int payItemNum          = (int)query.GetInt(13);
                int priceChangeGroupId  = (int)query.GetInt(14);
                long startDate          = (long)query.GetLong(15);
                long endDate            = (long)query.GetLong(16);

                orm = new ItemExchange(id, itemExchangeTopId, dispOrder, conditionType, conditionValue1, conditionValue2, changeItemLimitType, changeItemLimitNum, changeItemCategory, changeItemId, changeItemNum, additionalPieceNum, payItemCategory, payItemId, payItemNum, priceChangeGroupId, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public ItemExchange GetWithItemExchangeTopIdOrderByIdAsc(int itemExchangeTopId)
        {
            ItemExchange orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithItemExchangeTopIdOrderByIdAsc(itemExchangeTopId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", itemExchangeTopId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ItemExchange _SelectWithItemExchangeTopIdOrderByIdAsc(int itemExchangeTopId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchange_ItemExchangeTopId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchange");
                return null;
            }

            // SELECT `id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `item_exchange_top_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, itemExchangeTopId)) { return null; }

            ItemExchange orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithItemExchangeTopIdOrderByIdAsc(query, itemExchangeTopId);
            }

            query.Reset();

            return orm;
        }

        public List<ItemExchange> GetListWithItemExchangeTopIdOrderByIdAsc(int itemExchangeTopId)
        {
            int key = (int)itemExchangeTopId;

            if (!_dictionaryWithItemExchangeTopId.ContainsKey(key)) {
                _dictionaryWithItemExchangeTopId.Add(key, _ListSelectWithItemExchangeTopIdOrderByIdAsc(itemExchangeTopId));
            }

            return _dictionaryWithItemExchangeTopId[key];
        }

        public List<ItemExchange> MaybeListWithItemExchangeTopIdOrderByIdAsc(int itemExchangeTopId)
        {
            List<ItemExchange> list = GetListWithItemExchangeTopIdOrderByIdAsc(itemExchangeTopId);
            return list.Count > 0 ? list : null;
        }

        private List<ItemExchange> _ListSelectWithItemExchangeTopIdOrderByIdAsc(int itemExchangeTopId)
        {
            List<ItemExchange> _list = new List<ItemExchange>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchange_ItemExchangeTopId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchange");
                return null;
            }

            // SELECT `id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `item_exchange_top_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, itemExchangeTopId)) { return null; }

            while (query.Step()) {
                ItemExchange orm = _CreateOrmByQueryResultWithItemExchangeTopIdOrderByIdAsc(query, itemExchangeTopId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemExchange _CreateOrmByQueryResultWithItemExchangeTopIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int itemExchangeTopId)
        {
            int id                  = (int)query.GetInt(0);
            int dispOrder           = (int)query.GetInt(1);
            int conditionType       = (int)query.GetInt(2);
            int conditionValue1     = (int)query.GetInt(3);
            int conditionValue2     = (int)query.GetInt(4);
            int changeItemLimitType = (int)query.GetInt(5);
            int changeItemLimitNum  = (int)query.GetInt(6);
            int changeItemCategory  = (int)query.GetInt(7);
            int changeItemId        = (int)query.GetInt(8);
            int changeItemNum       = (int)query.GetInt(9);
            int additionalPieceNum  = (int)query.GetInt(10);
            int payItemCategory     = (int)query.GetInt(11);
            int payItemId           = (int)query.GetInt(12);
            int payItemNum          = (int)query.GetInt(13);
            int priceChangeGroupId  = (int)query.GetInt(14);
            long startDate          = (long)query.GetLong(15);
            long endDate            = (long)query.GetLong(16);

            return new ItemExchange(id, itemExchangeTopId, dispOrder, conditionType, conditionValue1, conditionValue2, changeItemLimitType, changeItemLimitNum, changeItemCategory, changeItemId, changeItemNum, additionalPieceNum, payItemCategory, payItemId, payItemNum, priceChangeGroupId, startDate, endDate);
        }

        public ItemExchange GetWithConditionTypeOrderByIdAsc(int conditionType)
        {
            ItemExchange orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionTypeOrderByIdAsc(conditionType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ItemExchange _SelectWithConditionTypeOrderByIdAsc(int conditionType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchange_ConditionType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchange");
                return null;
            }

            // SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `condition_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            ItemExchange orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionTypeOrderByIdAsc(query, conditionType);
            }

            query.Reset();

            return orm;
        }

        public List<ItemExchange> GetListWithConditionTypeOrderByIdAsc(int conditionType)
        {
            int key = (int)conditionType;

            if (!_dictionaryWithConditionType.ContainsKey(key)) {
                _dictionaryWithConditionType.Add(key, _ListSelectWithConditionTypeOrderByIdAsc(conditionType));
            }

            return _dictionaryWithConditionType[key];
        }

        public List<ItemExchange> MaybeListWithConditionTypeOrderByIdAsc(int conditionType)
        {
            List<ItemExchange> list = GetListWithConditionTypeOrderByIdAsc(conditionType);
            return list.Count > 0 ? list : null;
        }

        private List<ItemExchange> _ListSelectWithConditionTypeOrderByIdAsc(int conditionType)
        {
            List<ItemExchange> _list = new List<ItemExchange>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchange_ConditionType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchange");
                return null;
            }

            // SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `condition_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, conditionType)) { return null; }

            while (query.Step()) {
                ItemExchange orm = _CreateOrmByQueryResultWithConditionTypeOrderByIdAsc(query, conditionType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemExchange _CreateOrmByQueryResultWithConditionTypeOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int conditionType)
        {
            int id                  = (int)query.GetInt(0);
            int itemExchangeTopId   = (int)query.GetInt(1);
            int dispOrder           = (int)query.GetInt(2);
            int conditionValue1     = (int)query.GetInt(3);
            int conditionValue2     = (int)query.GetInt(4);
            int changeItemLimitType = (int)query.GetInt(5);
            int changeItemLimitNum  = (int)query.GetInt(6);
            int changeItemCategory  = (int)query.GetInt(7);
            int changeItemId        = (int)query.GetInt(8);
            int changeItemNum       = (int)query.GetInt(9);
            int additionalPieceNum  = (int)query.GetInt(10);
            int payItemCategory     = (int)query.GetInt(11);
            int payItemId           = (int)query.GetInt(12);
            int payItemNum          = (int)query.GetInt(13);
            int priceChangeGroupId  = (int)query.GetInt(14);
            long startDate          = (long)query.GetLong(15);
            long endDate            = (long)query.GetLong(16);

            return new ItemExchange(id, itemExchangeTopId, dispOrder, conditionType, conditionValue1, conditionValue2, changeItemLimitType, changeItemLimitNum, changeItemCategory, changeItemId, changeItemNum, additionalPieceNum, payItemCategory, payItemId, payItemNum, priceChangeGroupId, startDate, endDate);
        }

        public ItemExchange GetWithPayItemIdOrderByIdAsc(int payItemId)
        {
            ItemExchange orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithPayItemIdOrderByIdAsc(payItemId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", payItemId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ItemExchange _SelectWithPayItemIdOrderByIdAsc(int payItemId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchange_PayItemId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchange");
                return null;
            }

            // SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `pay_item_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, payItemId)) { return null; }

            ItemExchange orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithPayItemIdOrderByIdAsc(query, payItemId);
            }

            query.Reset();

            return orm;
        }

        public List<ItemExchange> GetListWithPayItemIdOrderByIdAsc(int payItemId)
        {
            int key = (int)payItemId;

            if (!_dictionaryWithPayItemId.ContainsKey(key)) {
                _dictionaryWithPayItemId.Add(key, _ListSelectWithPayItemIdOrderByIdAsc(payItemId));
            }

            return _dictionaryWithPayItemId[key];
        }

        public List<ItemExchange> MaybeListWithPayItemIdOrderByIdAsc(int payItemId)
        {
            List<ItemExchange> list = GetListWithPayItemIdOrderByIdAsc(payItemId);
            return list.Count > 0 ? list : null;
        }

        private List<ItemExchange> _ListSelectWithPayItemIdOrderByIdAsc(int payItemId)
        {
            List<ItemExchange> _list = new List<ItemExchange>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchange_PayItemId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchange");
                return null;
            }

            // SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `pay_item_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, payItemId)) { return null; }

            while (query.Step()) {
                ItemExchange orm = _CreateOrmByQueryResultWithPayItemIdOrderByIdAsc(query, payItemId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemExchange _CreateOrmByQueryResultWithPayItemIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int payItemId)
        {
            int id                  = (int)query.GetInt(0);
            int itemExchangeTopId   = (int)query.GetInt(1);
            int dispOrder           = (int)query.GetInt(2);
            int conditionType       = (int)query.GetInt(3);
            int conditionValue1     = (int)query.GetInt(4);
            int conditionValue2     = (int)query.GetInt(5);
            int changeItemLimitType = (int)query.GetInt(6);
            int changeItemLimitNum  = (int)query.GetInt(7);
            int changeItemCategory  = (int)query.GetInt(8);
            int changeItemId        = (int)query.GetInt(9);
            int changeItemNum       = (int)query.GetInt(10);
            int additionalPieceNum  = (int)query.GetInt(11);
            int payItemCategory     = (int)query.GetInt(12);
            int payItemNum          = (int)query.GetInt(13);
            int priceChangeGroupId  = (int)query.GetInt(14);
            long startDate          = (long)query.GetLong(15);
            long endDate            = (long)query.GetLong(16);

            return new ItemExchange(id, itemExchangeTopId, dispOrder, conditionType, conditionValue1, conditionValue2, changeItemLimitType, changeItemLimitNum, changeItemCategory, changeItemId, changeItemNum, additionalPieceNum, payItemCategory, payItemId, payItemNum, priceChangeGroupId, startDate, endDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithItemExchangeTopId.Clear();
            _dictionaryWithConditionType.Clear();
            _dictionaryWithPayItemId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ItemExchange()) {
                while (query.Step()) {
                    int id                  = (int)query.GetInt(0);
                    int itemExchangeTopId   = (int)query.GetInt(1);
                    int dispOrder           = (int)query.GetInt(2);
                    int conditionType       = (int)query.GetInt(3);
                    int conditionValue1     = (int)query.GetInt(4);
                    int conditionValue2     = (int)query.GetInt(5);
                    int changeItemLimitType = (int)query.GetInt(6);
                    int changeItemLimitNum  = (int)query.GetInt(7);
                    int changeItemCategory  = (int)query.GetInt(8);
                    int changeItemId        = (int)query.GetInt(9);
                    int changeItemNum       = (int)query.GetInt(10);
                    int additionalPieceNum  = (int)query.GetInt(11);
                    int payItemCategory     = (int)query.GetInt(12);
                    int payItemId           = (int)query.GetInt(13);
                    int payItemNum          = (int)query.GetInt(14);
                    int priceChangeGroupId  = (int)query.GetInt(15);
                    long startDate          = (long)query.GetLong(16);
                    long endDate            = (long)query.GetLong(17);

                    int key = (int)id;
                    ItemExchange orm = new ItemExchange(id, itemExchangeTopId, dispOrder, conditionType, conditionValue1, conditionValue2, changeItemLimitType, changeItemLimitNum, changeItemCategory, changeItemId, changeItemNum, additionalPieceNum, payItemCategory, payItemId, payItemNum, priceChangeGroupId, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class ItemExchange
        {
            /// <summary>
            /// id
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary> (CSV column: item_exchange_top_id) </summary>
            public readonly int ItemExchangeTopId;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: condition_value_1) </summary>
            public readonly int ConditionValue1;
            /// <summary> (CSV column: condition_value_2) </summary>
            public readonly int ConditionValue2;
            /// <summary> (CSV column: change_item_limit_type) </summary>
            public readonly int ChangeItemLimitType;
            /// <summary> (CSV column: change_item_limit_num) </summary>
            public readonly int ChangeItemLimitNum;
            /// <summary> (CSV column: change_item_category) </summary>
            public readonly int ChangeItemCategory;
            /// <summary> (CSV column: change_item_id) </summary>
            public readonly int ChangeItemId;
            /// <summary> (CSV column: change_item_num) </summary>
            public readonly int ChangeItemNum;
            /// <summary> (CSV column: additional_piece_num) </summary>
            public readonly int AdditionalPieceNum;
            /// <summary> (CSV column: pay_item_category) </summary>
            public readonly int PayItemCategory;
            /// <summary> (CSV column: pay_item_id) </summary>
            public readonly int PayItemId;
            /// <summary> (CSV column: pay_item_num) </summary>
            public readonly int PayItemNum;
            /// <summary> (CSV column: price_change_group_id) </summary>
            public readonly int PriceChangeGroupId;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ItemExchange(int id = 0, int itemExchangeTopId = 0, int dispOrder = 0, int conditionType = 0, int conditionValue1 = 0, int conditionValue2 = 0, int changeItemLimitType = 0, int changeItemLimitNum = 0, int changeItemCategory = 0, int changeItemId = 0, int changeItemNum = 0, int additionalPieceNum = 0, int payItemCategory = 0, int payItemId = 0, int payItemNum = 0, int priceChangeGroupId = 0, long startDate = 0, long endDate = 0)
            {
                this.Id                  = id;
                this.ItemExchangeTopId   = itemExchangeTopId;
                this.DispOrder           = dispOrder;
                this.ConditionType       = conditionType;
                this.ConditionValue1     = conditionValue1;
                this.ConditionValue2     = conditionValue2;
                this.ChangeItemLimitType = changeItemLimitType;
                this.ChangeItemLimitNum  = changeItemLimitNum;
                this.ChangeItemCategory  = changeItemCategory;
                this.ChangeItemId        = changeItemId;
                this.ChangeItemNum       = changeItemNum;
                this.AdditionalPieceNum  = additionalPieceNum;
                this.PayItemCategory     = payItemCategory;
                this.PayItemId           = payItemId;
                this.PayItemNum          = payItemNum;
                this.PriceChangeGroupId  = priceChangeGroupId;
                this.StartDate           = startDate;
                this.EndDate             = endDate;
            }
        }
    }
}
#endif

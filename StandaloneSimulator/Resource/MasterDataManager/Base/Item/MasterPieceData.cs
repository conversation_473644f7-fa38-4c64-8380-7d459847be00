// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/piece_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterPieceData : AbstractMasterData
    {
        public const string TABLE_NAME = "piece_data";

        MasterItemDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, PieceData> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, PieceData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterPieceData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterPieceData(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, PieceData>();
            _db = db;
        }


        public PieceData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterPieceData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterPieceData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private PieceData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_PieceData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for PieceData");
                return null;
            }

            // SELECT `item_place_id`,`start_date`,`end_date` FROM `piece_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            PieceData orm = null;

            if (query.Step())
            {
                int itemPlaceId  = (int)query.GetInt(0);
                string startDate = query.GetText(1);
                string endDate   = query.GetText(2);

                orm = new PieceData(id, itemPlaceId, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_PieceData()) {
                while (query.Step()) {
                    int id           = (int)query.GetInt(0);
                    int itemPlaceId  = (int)query.GetInt(1);
                    string startDate = query.GetText(2);
                    string endDate   = query.GetText(3);

                    int key = (int)id;
                    PieceData orm = new PieceData(id, itemPlaceId, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class PieceData
        {
            /// <summary>
            /// id
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary> (CSV column: item_place_id) </summary>
            public readonly int ItemPlaceId;
            /// <summary> (CSV column: start_date) </summary>
            public readonly string StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly string EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public PieceData(int id = 0, int itemPlaceId = 0, string startDate = "", string endDate = "")
            {
                this.Id          = id;
                this.ItemPlaceId = itemPlaceId;
                this.StartDate   = startDate;
                this.EndDate     = endDate;
            }
        }
    }
}
#endif

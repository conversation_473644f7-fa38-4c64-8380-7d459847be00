// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/item_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:item_category], [:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterItemData : AbstractMasterData
    {
        public const string TABLE_NAME = "item_data";

        MasterItemDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ItemData> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ItemData>> _dictionaryWithItemCategory = null;
        private Dictionary<int, List<ItemData>> _dictionaryWithGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, ItemData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterItemData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterItemData(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ItemData>();
            _dictionaryWithItemCategory = new Dictionary<int, List<ItemData>>();
            _dictionaryWithGroupId = new Dictionary<int, List<ItemData>>();
            _db = db;
        }


        public ItemData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterItemData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterItemData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ItemData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ItemData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemData");
                return null;
            }

            // SELECT `item_category`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            ItemData orm = null;

            if (query.Step())
            {
                int itemCategory  = (int)query.GetInt(0);
                int groupId       = (int)query.GetInt(1);
                int effectType1   = (int)query.GetInt(2);
                int effectTarget1 = (int)query.GetInt(3);
                int effectValue1  = (int)query.GetInt(4);
                int effectType2   = (int)query.GetInt(5);
                int effectTarget2 = (int)query.GetInt(6);
                int effectValue2  = (int)query.GetInt(7);
                int addValue1     = (int)query.GetInt(8);
                int addValue2     = (int)query.GetInt(9);
                int addValue3     = (int)query.GetInt(10);
                int limitNum      = (int)query.GetInt(11);
                int sort          = (int)query.GetInt(12);
                int rare          = (int)query.GetInt(13);
                int enableRequest = (int)query.GetInt(14);
                int requestReward = (int)query.GetInt(15);
                int itemPlaceId   = (int)query.GetInt(16);
                int sellItemId    = (int)query.GetInt(17);
                int sellPrice     = (int)query.GetInt(18);
                string startDate  = query.GetText(19);
                string endDate    = query.GetText(20);

                orm = new ItemData(id, itemCategory, groupId, effectType1, effectTarget1, effectValue1, effectType2, effectTarget2, effectValue2, addValue1, addValue2, addValue3, limitNum, sort, rare, enableRequest, requestReward, itemPlaceId, sellItemId, sellPrice, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public ItemData GetWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            ItemData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithItemCategoryOrderByIdAsc(itemCategory);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", itemCategory));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ItemData _SelectWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemData_ItemCategory();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemData");
                return null;
            }

            // SELECT `id`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `item_category`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, itemCategory)) { return null; }

            ItemData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithItemCategoryOrderByIdAsc(query, itemCategory);
            }

            query.Reset();

            return orm;
        }

        public List<ItemData> GetListWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            int key = (int)itemCategory;

            if (!_dictionaryWithItemCategory.ContainsKey(key)) {
                _dictionaryWithItemCategory.Add(key, _ListSelectWithItemCategoryOrderByIdAsc(itemCategory));
            }

            return _dictionaryWithItemCategory[key];
        }

        public List<ItemData> MaybeListWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            List<ItemData> list = GetListWithItemCategoryOrderByIdAsc(itemCategory);
            return list.Count > 0 ? list : null;
        }

        private List<ItemData> _ListSelectWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            List<ItemData> _list = new List<ItemData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemData_ItemCategory();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemData");
                return null;
            }

            // SELECT `id`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `item_category`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, itemCategory)) { return null; }

            while (query.Step()) {
                ItemData orm = _CreateOrmByQueryResultWithItemCategoryOrderByIdAsc(query, itemCategory);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemData _CreateOrmByQueryResultWithItemCategoryOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int itemCategory)
        {
            int id            = (int)query.GetInt(0);
            int groupId       = (int)query.GetInt(1);
            int effectType1   = (int)query.GetInt(2);
            int effectTarget1 = (int)query.GetInt(3);
            int effectValue1  = (int)query.GetInt(4);
            int effectType2   = (int)query.GetInt(5);
            int effectTarget2 = (int)query.GetInt(6);
            int effectValue2  = (int)query.GetInt(7);
            int addValue1     = (int)query.GetInt(8);
            int addValue2     = (int)query.GetInt(9);
            int addValue3     = (int)query.GetInt(10);
            int limitNum      = (int)query.GetInt(11);
            int sort          = (int)query.GetInt(12);
            int rare          = (int)query.GetInt(13);
            int enableRequest = (int)query.GetInt(14);
            int requestReward = (int)query.GetInt(15);
            int itemPlaceId   = (int)query.GetInt(16);
            int sellItemId    = (int)query.GetInt(17);
            int sellPrice     = (int)query.GetInt(18);
            string startDate  = query.GetText(19);
            string endDate    = query.GetText(20);

            return new ItemData(id, itemCategory, groupId, effectType1, effectTarget1, effectValue1, effectType2, effectTarget2, effectValue2, addValue1, addValue2, addValue3, limitNum, sort, rare, enableRequest, requestReward, itemPlaceId, sellItemId, sellPrice, startDate, endDate);
        }

        public ItemData GetWithGroupId(int groupId)
        {
            ItemData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupId(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ItemData _SelectWithGroupId(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemData_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemData");
                return null;
            }

            // SELECT `id`,`item_category`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            ItemData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupId(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<ItemData> GetListWithGroupId(int groupId)
        {
            int key = (int)groupId;

            if (!_dictionaryWithGroupId.ContainsKey(key)) {
                _dictionaryWithGroupId.Add(key, _ListSelectWithGroupId(groupId));
            }

            return _dictionaryWithGroupId[key];
        }

        public List<ItemData> MaybeListWithGroupId(int groupId)
        {
            List<ItemData> list = GetListWithGroupId(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<ItemData> _ListSelectWithGroupId(int groupId)
        {
            List<ItemData> _list = new List<ItemData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemData_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemData");
                return null;
            }

            // SELECT `id`,`item_category`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                ItemData orm = _CreateOrmByQueryResultWithGroupId(query, groupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemData _CreateOrmByQueryResultWithGroupId(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id            = (int)query.GetInt(0);
            int itemCategory  = (int)query.GetInt(1);
            int effectType1   = (int)query.GetInt(2);
            int effectTarget1 = (int)query.GetInt(3);
            int effectValue1  = (int)query.GetInt(4);
            int effectType2   = (int)query.GetInt(5);
            int effectTarget2 = (int)query.GetInt(6);
            int effectValue2  = (int)query.GetInt(7);
            int addValue1     = (int)query.GetInt(8);
            int addValue2     = (int)query.GetInt(9);
            int addValue3     = (int)query.GetInt(10);
            int limitNum      = (int)query.GetInt(11);
            int sort          = (int)query.GetInt(12);
            int rare          = (int)query.GetInt(13);
            int enableRequest = (int)query.GetInt(14);
            int requestReward = (int)query.GetInt(15);
            int itemPlaceId   = (int)query.GetInt(16);
            int sellItemId    = (int)query.GetInt(17);
            int sellPrice     = (int)query.GetInt(18);
            string startDate  = query.GetText(19);
            string endDate    = query.GetText(20);

            return new ItemData(id, itemCategory, groupId, effectType1, effectTarget1, effectValue1, effectType2, effectTarget2, effectValue2, addValue1, addValue2, addValue3, limitNum, sort, rare, enableRequest, requestReward, itemPlaceId, sellItemId, sellPrice, startDate, endDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithItemCategory.Clear();
            _dictionaryWithGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ItemData()) {
                while (query.Step()) {
                    int id            = (int)query.GetInt(0);
                    int itemCategory  = (int)query.GetInt(1);
                    int groupId       = (int)query.GetInt(2);
                    int effectType1   = (int)query.GetInt(3);
                    int effectTarget1 = (int)query.GetInt(4);
                    int effectValue1  = (int)query.GetInt(5);
                    int effectType2   = (int)query.GetInt(6);
                    int effectTarget2 = (int)query.GetInt(7);
                    int effectValue2  = (int)query.GetInt(8);
                    int addValue1     = (int)query.GetInt(9);
                    int addValue2     = (int)query.GetInt(10);
                    int addValue3     = (int)query.GetInt(11);
                    int limitNum      = (int)query.GetInt(12);
                    int sort          = (int)query.GetInt(13);
                    int rare          = (int)query.GetInt(14);
                    int enableRequest = (int)query.GetInt(15);
                    int requestReward = (int)query.GetInt(16);
                    int itemPlaceId   = (int)query.GetInt(17);
                    int sellItemId    = (int)query.GetInt(18);
                    int sellPrice     = (int)query.GetInt(19);
                    string startDate  = query.GetText(20);
                    string endDate    = query.GetText(21);

                    int key = (int)id;
                    ItemData orm = new ItemData(id, itemCategory, groupId, effectType1, effectTarget1, effectValue1, effectType2, effectTarget2, effectValue2, addValue1, addValue2, addValue3, limitNum, sort, rare, enableRequest, requestReward, itemPlaceId, sellItemId, sellPrice, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class ItemData
        {
            /// <summary>
            /// id
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary> (CSV column: item_category) </summary>
            public readonly int ItemCategory;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: effect_type_1) </summary>
            public readonly int EffectType1;
            /// <summary> (CSV column: effect_target_1) </summary>
            public readonly int EffectTarget1;
            /// <summary> (CSV column: effect_value_1) </summary>
            public readonly int EffectValue1;
            /// <summary> (CSV column: effect_type_2) </summary>
            public readonly int EffectType2;
            /// <summary> (CSV column: effect_target_2) </summary>
            public readonly int EffectTarget2;
            /// <summary> (CSV column: effect_value_2) </summary>
            public readonly int EffectValue2;
            /// <summary> (CSV column: add_value_1) </summary>
            public readonly int AddValue1;
            /// <summary> (CSV column: add_value_2) </summary>
            public readonly int AddValue2;
            /// <summary> (CSV column: add_value_3) </summary>
            public readonly int AddValue3;
            /// <summary> (CSV column: limit_num) </summary>
            public readonly int LimitNum;
            /// <summary> (CSV column: sort) </summary>
            public readonly int Sort;
            /// <summary> (CSV column: rare) </summary>
            public readonly int Rare;
            /// <summary> (CSV column: enable_request) </summary>
            public readonly int EnableRequest;
            /// <summary> (CSV column: request_reward) </summary>
            public readonly int RequestReward;
            /// <summary> (CSV column: item_place_id) </summary>
            public readonly int ItemPlaceId;
            /// <summary> (CSV column: sell_item_id) </summary>
            public readonly int SellItemId;
            /// <summary> (CSV column: sell_price) </summary>
            public readonly int SellPrice;
            /// <summary> (CSV column: start_date) </summary>
            public readonly string StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly string EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ItemData(int id = 0, int itemCategory = 0, int groupId = 0, int effectType1 = 0, int effectTarget1 = 0, int effectValue1 = 0, int effectType2 = 0, int effectTarget2 = 0, int effectValue2 = 0, int addValue1 = 0, int addValue2 = 0, int addValue3 = 0, int limitNum = 0, int sort = 0, int rare = 0, int enableRequest = 0, int requestReward = 0, int itemPlaceId = 0, int sellItemId = 0, int sellPrice = 0, string startDate = "", string endDate = "")
            {
                this.Id            = id;
                this.ItemCategory  = itemCategory;
                this.GroupId       = groupId;
                this.EffectType1   = effectType1;
                this.EffectTarget1 = effectTarget1;
                this.EffectValue1  = effectValue1;
                this.EffectType2   = effectType2;
                this.EffectTarget2 = effectTarget2;
                this.EffectValue2  = effectValue2;
                this.AddValue1     = addValue1;
                this.AddValue2     = addValue2;
                this.AddValue3     = addValue3;
                this.LimitNum      = limitNum;
                this.Sort          = sort;
                this.Rare          = rare;
                this.EnableRequest = enableRequest;
                this.RequestReward = requestReward;
                this.ItemPlaceId   = itemPlaceId;
                this.SellItemId    = sellItemId;
                this.SellPrice     = sellPrice;
                this.StartDate     = startDate;
                this.EndDate       = endDate;
            }
        }
    }
}
#endif

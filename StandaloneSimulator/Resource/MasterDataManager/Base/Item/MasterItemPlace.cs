// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/item_place
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterItemPlace : AbstractMasterData
    {
        public const string TABLE_NAME = "item_place";

        MasterItemDatabase _db = null;


        // cache dictionary
        private Dictionary<int, ItemPlace> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ItemPlace>> _dictionaryWithId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterItemPlace(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ItemPlace>();
            _dictionaryWithId = new Dictionary<int, List<ItemPlace>>();
            _db = db;
        }



        public ItemPlace GetWithIdOrderByTransitionTypeAsc(int id)
        {
            ItemPlace orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithIdOrderByTransitionTypeAsc(id);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", id));
                }
            }

            return orm;
        }

        private ItemPlace _SelectWithIdOrderByTransitionTypeAsc(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemPlace_Id();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemPlace");
                return null;
            }

            // SELECT `transition_type`,`transition_value`,`start_date` FROM `item_place` WHERE `id`=? ORDER BY `transition_type` ASC;
            if (!query.BindInt(1, id)) { return null; }

            ItemPlace orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithIdOrderByTransitionTypeAsc(query, id);
            }

            query.Reset();

            return orm;
        }

        public List<ItemPlace> GetListWithIdOrderByTransitionTypeAsc(int id)
        {
            int key = (int)id;

            if (!_dictionaryWithId.ContainsKey(key)) {
                _dictionaryWithId.Add(key, _ListSelectWithIdOrderByTransitionTypeAsc(id));
            }

            return _dictionaryWithId[key];
        }

        public List<ItemPlace> MaybeListWithIdOrderByTransitionTypeAsc(int id)
        {
            List<ItemPlace> list = GetListWithIdOrderByTransitionTypeAsc(id);
            return list.Count > 0 ? list : null;
        }

        private List<ItemPlace> _ListSelectWithIdOrderByTransitionTypeAsc(int id)
        {
            List<ItemPlace> _list = new List<ItemPlace>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemPlace_Id();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemPlace");
                return null;
            }

            // SELECT `transition_type`,`transition_value`,`start_date` FROM `item_place` WHERE `id`=? ORDER BY `transition_type` ASC;
            if (!query.BindInt(1, id)) { return null; }

            while (query.Step()) {
                ItemPlace orm = _CreateOrmByQueryResultWithIdOrderByTransitionTypeAsc(query, id);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemPlace _CreateOrmByQueryResultWithIdOrderByTransitionTypeAsc(LibNative.Sqlite3.PreparedQuery query, int id)
        {
            int transitionType  = (int)query.GetInt(0);
            int transitionValue = (int)query.GetInt(1);
            long startDate      = (long)query.GetLong(2);

            return new ItemPlace(id, transitionType, transitionValue, startDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithId.Clear();
        }

        public sealed partial class ItemPlace
        {
            /// <summary>
            /// id
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary> (CSV column: transition_type) </summary>
            public readonly int TransitionType;
            /// <summary> (CSV column: transition_value) </summary>
            public readonly int TransitionValue;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ItemPlace(int id = 0, int transitionType = 0, int transitionValue = 0, long startDate = 0)
            {
                this.Id              = id;
                this.TransitionType  = transitionType;
                this.TransitionValue = transitionValue;
                this.StartDate       = startDate;
            }
        }
    }
}
#endif

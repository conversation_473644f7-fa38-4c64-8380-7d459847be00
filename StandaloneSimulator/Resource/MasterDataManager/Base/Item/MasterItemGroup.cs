// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/item_group
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterItemGroup : AbstractMasterData
    {
        public const string TABLE_NAME = "item_group";

        MasterItemDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ItemGroup> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ItemGroup>> _dictionaryWithGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, ItemGroup> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterItemGroup");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterItemGroup(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ItemGroup>();
            _dictionaryWithGroupId = new Dictionary<int, List<ItemGroup>>();
            _db = db;
        }


        public ItemGroup Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterItemGroup");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterItemGroup", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ItemGroup _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ItemGroup();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemGroup");
                return null;
            }

            // SELECT `group_id` FROM `item_group` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            ItemGroup orm = null;

            if (query.Step())
            {
                int groupId = (int)query.GetInt(0);

                orm = new ItemGroup(id, groupId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public ItemGroup GetWithGroupIdOrderByIdAsc(int groupId)
        {
            ItemGroup orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupIdOrderByIdAsc(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ItemGroup _SelectWithGroupIdOrderByIdAsc(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemGroup_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemGroup");
                return null;
            }

            // SELECT `id` FROM `item_group` WHERE `group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            ItemGroup orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<ItemGroup> GetListWithGroupIdOrderByIdAsc(int groupId)
        {
            int key = (int)groupId;

            if (!_dictionaryWithGroupId.ContainsKey(key)) {
                _dictionaryWithGroupId.Add(key, _ListSelectWithGroupIdOrderByIdAsc(groupId));
            }

            return _dictionaryWithGroupId[key];
        }

        public List<ItemGroup> MaybeListWithGroupIdOrderByIdAsc(int groupId)
        {
            List<ItemGroup> list = GetListWithGroupIdOrderByIdAsc(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<ItemGroup> _ListSelectWithGroupIdOrderByIdAsc(int groupId)
        {
            List<ItemGroup> _list = new List<ItemGroup>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemGroup_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemGroup");
                return null;
            }

            // SELECT `id` FROM `item_group` WHERE `group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                ItemGroup orm = _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(query, groupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemGroup _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id = (int)query.GetInt(0);

            return new ItemGroup(id, groupId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ItemGroup()) {
                while (query.Step()) {
                    int id      = (int)query.GetInt(0);
                    int groupId = (int)query.GetInt(1);

                    int key = (int)id;
                    ItemGroup orm = new ItemGroup(id, groupId);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class ItemGroup
        {
            /// <summary>
            /// id
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary>
            /// group_id
            /// (CSV column: group_id)
            /// </summary>
            public readonly int GroupId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ItemGroup(int id = 0, int groupId = 0)
            {
                this.Id      = id;
                this.GroupId = groupId;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/exchange_ticket_detail
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:card_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterExchangeTicketDetail : AbstractMasterData
    {
        public const string TABLE_NAME = "exchange_ticket_detail";

        MasterItemDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ExchangeTicketDetail> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ExchangeTicketDetail>> _dictionaryWithCardType = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterExchangeTicketDetail(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ExchangeTicketDetail>();
            _dictionaryWithCardType = new Dictionary<int, List<ExchangeTicketDetail>>();
            _db = db;
        }


        public ExchangeTicketDetail Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterExchangeTicketDetail");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterExchangeTicketDetail", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ExchangeTicketDetail _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ExchangeTicketDetail();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ExchangeTicketDetail");
                return null;
            }

            // SELECT `card_type`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            ExchangeTicketDetail orm = null;

            if (query.Step())
            {
                byte cardType          = (byte)query.GetInt(0);
                int cardId             = (int)query.GetInt(1);
                int additionalPieceNum = (int)query.GetInt(2);
                long startDate         = (long)query.GetLong(3);
                long endDate           = (long)query.GetLong(4);

                orm = new ExchangeTicketDetail(id, cardType, cardId, additionalPieceNum, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public ExchangeTicketDetail GetWithCardTypeOrderByIdAsc(byte cardType)
        {
            ExchangeTicketDetail orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCardTypeOrderByIdAsc(cardType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", cardType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ExchangeTicketDetail _SelectWithCardTypeOrderByIdAsc(byte cardType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ExchangeTicketDetail_CardType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ExchangeTicketDetail");
                return null;
            }

            // SELECT `id`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail` WHERE `card_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, cardType)) { return null; }

            ExchangeTicketDetail orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCardTypeOrderByIdAsc(query, cardType);
            }

            query.Reset();

            return orm;
        }

        public List<ExchangeTicketDetail> GetListWithCardTypeOrderByIdAsc(byte cardType)
        {
            int key = (int)((byte)cardType);

            if (!_dictionaryWithCardType.ContainsKey(key)) {
                _dictionaryWithCardType.Add(key, _ListSelectWithCardTypeOrderByIdAsc(cardType));
            }

            return _dictionaryWithCardType[key];
        }

        public List<ExchangeTicketDetail> MaybeListWithCardTypeOrderByIdAsc(byte cardType)
        {
            List<ExchangeTicketDetail> list = GetListWithCardTypeOrderByIdAsc(cardType);
            return list.Count > 0 ? list : null;
        }

        private List<ExchangeTicketDetail> _ListSelectWithCardTypeOrderByIdAsc(byte cardType)
        {
            List<ExchangeTicketDetail> _list = new List<ExchangeTicketDetail>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ExchangeTicketDetail_CardType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ExchangeTicketDetail");
                return null;
            }

            // SELECT `id`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail` WHERE `card_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, cardType)) { return null; }

            while (query.Step()) {
                ExchangeTicketDetail orm = _CreateOrmByQueryResultWithCardTypeOrderByIdAsc(query, cardType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ExchangeTicketDetail _CreateOrmByQueryResultWithCardTypeOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, byte cardType)
        {
            int id                 = (int)query.GetInt(0);
            int cardId             = (int)query.GetInt(1);
            int additionalPieceNum = (int)query.GetInt(2);
            long startDate         = (long)query.GetLong(3);
            long endDate           = (long)query.GetLong(4);

            return new ExchangeTicketDetail(id, cardType, cardId, additionalPieceNum, startDate, endDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCardType.Clear();
        }

        public sealed partial class ExchangeTicketDetail
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary>
            /// 
            /// (CSV column: card_type)
            /// (enum eCardType)
            /// </summary>
            public readonly byte CardType;
            /// <summary> (CSV column: card_id) </summary>
            public readonly int CardId;
            /// <summary> (CSV column: additional_piece_num) </summary>
            public readonly int AdditionalPieceNum;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ExchangeTicketDetail(int id = 0, byte cardType = 0, int cardId = 0, int additionalPieceNum = 0, long startDate = 0, long endDate = 0)
            {
                this.Id                 = id;
                this.CardType           = cardType;
                this.CardId             = cardId;
                this.AdditionalPieceNum = additionalPieceNum;
                this.StartDate          = startDate;
                this.EndDate            = endDate;
            }
        }
    }
}
#endif

// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/item_exchange_top
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:item_top_category]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterItemExchangeTop : AbstractMasterData
    {
        public const string TABLE_NAME = "item_exchange_top";

        MasterItemDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, ItemExchangeTop> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ItemExchangeTop>> _dictionaryWithItemTopCategory = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, ItemExchangeTop> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterItemExchangeTop");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterItemExchangeTop(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, ItemExchangeTop>();
            _dictionaryWithItemTopCategory = new Dictionary<int, List<ItemExchangeTop>>();
            _db = db;
        }


        public ItemExchangeTop Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterItemExchangeTop");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterItemExchangeTop", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private ItemExchangeTop _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ItemExchangeTop();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchangeTop");
                return null;
            }

            // SELECT `item_exchange_disp_order`,`item_exchange_type`,`item_top_category`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            ItemExchangeTop orm = null;

            if (query.Step())
            {
                int itemExchangeDispOrder = (int)query.GetInt(0);
                int itemExchangeType      = (int)query.GetInt(1);
                byte itemTopCategory      = (byte)query.GetInt(2);
                int isAnnivShopTop        = (int)query.GetInt(3);
                int shopEnterTrigger      = (int)query.GetInt(4);
                long startDate            = (long)query.GetLong(5);
                long endDate              = (long)query.GetLong(6);

                orm = new ItemExchangeTop(id, itemExchangeDispOrder, itemExchangeType, itemTopCategory, isAnnivShopTop, shopEnterTrigger, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public ItemExchangeTop GetWithItemTopCategoryOrderByItemExchangeDispOrderAsc(byte itemTopCategory)
        {
            ItemExchangeTop orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithItemTopCategoryOrderByItemExchangeDispOrderAsc(itemTopCategory);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", itemTopCategory));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ItemExchangeTop _SelectWithItemTopCategoryOrderByItemExchangeDispOrderAsc(byte itemTopCategory)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchangeTop_ItemTopCategory();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchangeTop");
                return null;
            }

            // SELECT `id`,`item_exchange_disp_order`,`item_exchange_type`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top` WHERE `item_top_category`=? ORDER BY `item_exchange_disp_order` ASC;
            if (!query.BindInt(1, itemTopCategory)) { return null; }

            ItemExchangeTop orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithItemTopCategoryOrderByItemExchangeDispOrderAsc(query, itemTopCategory);
            }

            query.Reset();

            return orm;
        }

        public List<ItemExchangeTop> GetListWithItemTopCategoryOrderByItemExchangeDispOrderAsc(byte itemTopCategory)
        {
            int key = (int)((byte)itemTopCategory);

            if (!_dictionaryWithItemTopCategory.ContainsKey(key)) {
                _dictionaryWithItemTopCategory.Add(key, _ListSelectWithItemTopCategoryOrderByItemExchangeDispOrderAsc(itemTopCategory));
            }

            return _dictionaryWithItemTopCategory[key];
        }

        public List<ItemExchangeTop> MaybeListWithItemTopCategoryOrderByItemExchangeDispOrderAsc(byte itemTopCategory)
        {
            List<ItemExchangeTop> list = GetListWithItemTopCategoryOrderByItemExchangeDispOrderAsc(itemTopCategory);
            return list.Count > 0 ? list : null;
        }

        private List<ItemExchangeTop> _ListSelectWithItemTopCategoryOrderByItemExchangeDispOrderAsc(byte itemTopCategory)
        {
            List<ItemExchangeTop> _list = new List<ItemExchangeTop>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ItemExchangeTop_ItemTopCategory();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ItemExchangeTop");
                return null;
            }

            // SELECT `id`,`item_exchange_disp_order`,`item_exchange_type`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top` WHERE `item_top_category`=? ORDER BY `item_exchange_disp_order` ASC;
            if (!query.BindInt(1, itemTopCategory)) { return null; }

            while (query.Step()) {
                ItemExchangeTop orm = _CreateOrmByQueryResultWithItemTopCategoryOrderByItemExchangeDispOrderAsc(query, itemTopCategory);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ItemExchangeTop _CreateOrmByQueryResultWithItemTopCategoryOrderByItemExchangeDispOrderAsc(LibNative.Sqlite3.PreparedQuery query, byte itemTopCategory)
        {
            int id                    = (int)query.GetInt(0);
            int itemExchangeDispOrder = (int)query.GetInt(1);
            int itemExchangeType      = (int)query.GetInt(2);
            int isAnnivShopTop        = (int)query.GetInt(3);
            int shopEnterTrigger      = (int)query.GetInt(4);
            long startDate            = (long)query.GetLong(5);
            long endDate              = (long)query.GetLong(6);

            return new ItemExchangeTop(id, itemExchangeDispOrder, itemExchangeType, itemTopCategory, isAnnivShopTop, shopEnterTrigger, startDate, endDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithItemTopCategory.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ItemExchangeTop()) {
                while (query.Step()) {
                    int id                    = (int)query.GetInt(0);
                    int itemExchangeDispOrder = (int)query.GetInt(1);
                    int itemExchangeType      = (int)query.GetInt(2);
                    byte itemTopCategory      = (byte)query.GetInt(3);
                    int isAnnivShopTop        = (int)query.GetInt(4);
                    int shopEnterTrigger      = (int)query.GetInt(5);
                    long startDate            = (long)query.GetLong(6);
                    long endDate              = (long)query.GetLong(7);

                    int key = (int)id;
                    ItemExchangeTop orm = new ItemExchangeTop(id, itemExchangeDispOrder, itemExchangeType, itemTopCategory, isAnnivShopTop, shopEnterTrigger, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class ItemExchangeTop
        {
            /// <summary>
            /// id
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary> (CSV column: item_exchange_disp_order) </summary>
            public readonly int ItemExchangeDispOrder;
            /// <summary> (CSV column: item_exchange_type) </summary>
            public readonly int ItemExchangeType;
            /// <summary>
            /// ショップカテゴリ
            /// (CSV column: item_top_category)
            /// (enum eCategory)
            /// </summary>
            public readonly byte ItemTopCategory;
            /// <summary> (CSV column: is_anniv_shop_top) </summary>
            public readonly int IsAnnivShopTop;
            /// <summary> (CSV column: shop_enter_trigger) </summary>
            public readonly int ShopEnterTrigger;
            /// <summary>
            /// 開始期間(通常カテゴリ以外でのみ使用)
            /// (CSV column: start_date)
            /// </summary>
            public readonly long StartDate;
            /// <summary>
            /// 終了期間(通常カテゴリ以外でのみ使用)
            /// (CSV column: end_date)
            /// </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ItemExchangeTop(int id = 0, int itemExchangeDispOrder = 0, int itemExchangeType = 0, byte itemTopCategory = 0, int isAnnivShopTop = 0, int shopEnterTrigger = 0, long startDate = 0, long endDate = 0)
            {
                this.Id                    = id;
                this.ItemExchangeDispOrder = itemExchangeDispOrder;
                this.ItemExchangeType      = itemExchangeType;
                this.ItemTopCategory       = itemTopCategory;
                this.IsAnnivShopTop        = isAnnivShopTop;
                this.ShopEnterTrigger      = shopEnterTrigger;
                this.StartDate             = startDate;
                this.EndDate               = endDate;
            }
        }
    }
}
#endif

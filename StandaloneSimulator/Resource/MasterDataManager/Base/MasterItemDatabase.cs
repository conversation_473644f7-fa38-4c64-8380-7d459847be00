// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public class MasterItemDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterItemData masterItemData                         { get; private set; }
        public MasterItemExchange masterItemExchange                 { get; private set; }
        public MasterItemExchangeTop masterItemExchangeTop           { get; private set; }
        public MasterPieceData masterPieceData                       { get; private set; }
        public MasterItemGroup masterItemGroup                       { get; private set; }
        public MasterItemPlace masterItemPlace                       { get; private set; }
        public MasterPriceChange masterPriceChange                   { get; private set; }
        public MasterExchangeTicketDetail masterExchangeTicketDetail { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterItemData             = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterItemExchange         = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterItemExchangeTop      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterPieceData            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterItemGroup            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterPriceChange          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterExchangeTicketDetail = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemData_itemCategory           = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemData_groupId                = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemExchange_itemExchangeTopId  = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemExchange_conditionType      = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemExchange_payItemId          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemExchangeTop_itemTopCategory = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemGroup_groupId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_itemPlace_id                    = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_priceChange_groupId             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_exchangeTicketDetail_cardType   = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterItemDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterItemData             = new MasterItemData(this);
            this.masterItemExchange         = new MasterItemExchange(this);
            this.masterItemExchangeTop      = new MasterItemExchangeTop(this);
            this.masterPieceData            = new MasterPieceData(this);
            this.masterItemGroup            = new MasterItemGroup(this);
            this.masterItemPlace            = new MasterItemPlace(this);
            this.masterPriceChange          = new MasterPriceChange(this);
            this.masterExchangeTicketDetail = new MasterExchangeTicketDetail(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet item database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterItemData != null) { _selectQuery_masterItemData.Dispose(); _selectQuery_masterItemData = null; }
            if (_indexedSelectQuery_itemData_itemCategory != null) { _indexedSelectQuery_itemData_itemCategory.Dispose(); _indexedSelectQuery_itemData_itemCategory = null; }
            if (_indexedSelectQuery_itemData_groupId != null) { _indexedSelectQuery_itemData_groupId.Dispose(); _indexedSelectQuery_itemData_groupId = null; }
            if (_selectQuery_masterItemExchange != null) { _selectQuery_masterItemExchange.Dispose(); _selectQuery_masterItemExchange = null; }
            if (_indexedSelectQuery_itemExchange_itemExchangeTopId != null) { _indexedSelectQuery_itemExchange_itemExchangeTopId.Dispose(); _indexedSelectQuery_itemExchange_itemExchangeTopId = null; }
            if (_indexedSelectQuery_itemExchange_conditionType != null) { _indexedSelectQuery_itemExchange_conditionType.Dispose(); _indexedSelectQuery_itemExchange_conditionType = null; }
            if (_indexedSelectQuery_itemExchange_payItemId != null) { _indexedSelectQuery_itemExchange_payItemId.Dispose(); _indexedSelectQuery_itemExchange_payItemId = null; }
            if (_selectQuery_masterItemExchangeTop != null) { _selectQuery_masterItemExchangeTop.Dispose(); _selectQuery_masterItemExchangeTop = null; }
            if (_indexedSelectQuery_itemExchangeTop_itemTopCategory != null) { _indexedSelectQuery_itemExchangeTop_itemTopCategory.Dispose(); _indexedSelectQuery_itemExchangeTop_itemTopCategory = null; }
            if (_selectQuery_masterPieceData != null) { _selectQuery_masterPieceData.Dispose(); _selectQuery_masterPieceData = null; }
            if (_selectQuery_masterItemGroup != null) { _selectQuery_masterItemGroup.Dispose(); _selectQuery_masterItemGroup = null; }
            if (_indexedSelectQuery_itemGroup_groupId != null) { _indexedSelectQuery_itemGroup_groupId.Dispose(); _indexedSelectQuery_itemGroup_groupId = null; }
            if (_indexedSelectQuery_itemPlace_id != null) { _indexedSelectQuery_itemPlace_id.Dispose(); _indexedSelectQuery_itemPlace_id = null; }
            if (_selectQuery_masterPriceChange != null) { _selectQuery_masterPriceChange.Dispose(); _selectQuery_masterPriceChange = null; }
            if (_indexedSelectQuery_priceChange_groupId != null) { _indexedSelectQuery_priceChange_groupId.Dispose(); _indexedSelectQuery_priceChange_groupId = null; }
            if (_selectQuery_masterExchangeTicketDetail != null) { _selectQuery_masterExchangeTicketDetail.Dispose(); _selectQuery_masterExchangeTicketDetail = null; }
            if (_indexedSelectQuery_exchangeTicketDetail_cardType != null) { _indexedSelectQuery_exchangeTicketDetail_cardType.Dispose(); _indexedSelectQuery_exchangeTicketDetail_cardType = null; }
            if (this.masterItemData != null) { this.masterItemData.Unload(); }
            if (this.masterItemExchange != null) { this.masterItemExchange.Unload(); }
            if (this.masterItemExchangeTop != null) { this.masterItemExchangeTop.Unload(); }
            if (this.masterPieceData != null) { this.masterPieceData.Unload(); }
            if (this.masterItemGroup != null) { this.masterItemGroup.Unload(); }
            if (this.masterItemPlace != null) { this.masterItemPlace.Unload(); }
            if (this.masterPriceChange != null) { this.masterPriceChange.Unload(); }
            if (this.masterExchangeTicketDetail != null) { this.masterExchangeTicketDetail.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for item/item_data
        
        /// <summary>
        /// SELECT `item_category`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ItemData()
        {
            if (_selectQuery_masterItemData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterItemData = connection.PreparedQuery("SELECT `item_category`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterItemData;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `item_category`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemData_ItemCategory()
        {
            if (_indexedSelectQuery_itemData_itemCategory == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemData_itemCategory = connection.PreparedQuery("SELECT `id`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `item_category`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_itemData_itemCategory;
        }
        
        /// <summary>
        /// SELECT `id`,`item_category`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `group_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemData_GroupId()
        {
            if (_indexedSelectQuery_itemData_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemData_groupId = connection.PreparedQuery("SELECT `id`,`item_category`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data` WHERE `group_id`=?;");
            }
        
            return _indexedSelectQuery_itemData_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`item_category`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ItemData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`item_category`,`group_id`,`effect_type_1`,`effect_target_1`,`effect_value_1`,`effect_type_2`,`effect_target_2`,`effect_value_2`,`add_value_1`,`add_value_2`,`add_value_3`,`limit_num`,`sort`,`rare`,`enable_request`,`request_reward`,`item_place_id`,`sell_item_id`,`sell_price`,`start_date`,`end_date` FROM `item_data`;");
        }
        
        // SQL statements for item/item_exchange
        
        /// <summary>
        /// SELECT `item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ItemExchange()
        {
            if (_selectQuery_masterItemExchange == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterItemExchange = connection.PreparedQuery("SELECT `item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `id`=?;");
            }
        
            return _selectQuery_masterItemExchange;
        }
        
        /// <summary>
        /// SELECT `id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `item_exchange_top_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemExchange_ItemExchangeTopId()
        {
            if (_indexedSelectQuery_itemExchange_itemExchangeTopId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemExchange_itemExchangeTopId = connection.PreparedQuery("SELECT `id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `item_exchange_top_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_itemExchange_itemExchangeTopId;
        }
        
        /// <summary>
        /// SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `condition_type`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemExchange_ConditionType()
        {
            if (_indexedSelectQuery_itemExchange_conditionType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemExchange_conditionType = connection.PreparedQuery("SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `condition_type`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_itemExchange_conditionType;
        }
        
        /// <summary>
        /// SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `pay_item_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemExchange_PayItemId()
        {
            if (_indexedSelectQuery_itemExchange_payItemId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemExchange_payItemId = connection.PreparedQuery("SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange` WHERE `pay_item_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_itemExchange_payItemId;
        }
        
        /// <summary>
        /// SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ItemExchange()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`item_exchange_top_id`,`disp_order`,`condition_type`,`condition_value_1`,`condition_value_2`,`change_item_limit_type`,`change_item_limit_num`,`change_item_category`,`change_item_id`,`change_item_num`,`additional_piece_num`,`pay_item_category`,`pay_item_id`,`pay_item_num`,`price_change_group_id`,`start_date`,`end_date` FROM `item_exchange`;");
        }
        
        // SQL statements for item/item_exchange_top
        
        /// <summary>
        /// SELECT `item_exchange_disp_order`,`item_exchange_type`,`item_top_category`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ItemExchangeTop()
        {
            if (_selectQuery_masterItemExchangeTop == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterItemExchangeTop = connection.PreparedQuery("SELECT `item_exchange_disp_order`,`item_exchange_type`,`item_top_category`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top` WHERE `id`=?;");
            }
        
            return _selectQuery_masterItemExchangeTop;
        }
        
        /// <summary>
        /// SELECT `id`,`item_exchange_disp_order`,`item_exchange_type`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top` WHERE `item_top_category`=? ORDER BY `item_exchange_disp_order` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemExchangeTop_ItemTopCategory()
        {
            if (_indexedSelectQuery_itemExchangeTop_itemTopCategory == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemExchangeTop_itemTopCategory = connection.PreparedQuery("SELECT `id`,`item_exchange_disp_order`,`item_exchange_type`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top` WHERE `item_top_category`=? ORDER BY `item_exchange_disp_order` ASC;");
            }
        
            return _indexedSelectQuery_itemExchangeTop_itemTopCategory;
        }
        
        /// <summary>
        /// SELECT `id`,`item_exchange_disp_order`,`item_exchange_type`,`item_top_category`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ItemExchangeTop()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`item_exchange_disp_order`,`item_exchange_type`,`item_top_category`,`is_anniv_shop_top`,`shop_enter_trigger`,`start_date`,`end_date` FROM `item_exchange_top`;");
        }
        
        // SQL statements for item/piece_data
        
        /// <summary>
        /// SELECT `item_place_id`,`start_date`,`end_date` FROM `piece_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_PieceData()
        {
            if (_selectQuery_masterPieceData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterPieceData = connection.PreparedQuery("SELECT `item_place_id`,`start_date`,`end_date` FROM `piece_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterPieceData;
        }
        
        /// <summary>
        /// SELECT `id`,`item_place_id`,`start_date`,`end_date` FROM `piece_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_PieceData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`item_place_id`,`start_date`,`end_date` FROM `piece_data`;");
        }
        
        // SQL statements for item/item_group
        
        /// <summary>
        /// SELECT `group_id` FROM `item_group` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ItemGroup()
        {
            if (_selectQuery_masterItemGroup == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterItemGroup = connection.PreparedQuery("SELECT `group_id` FROM `item_group` WHERE `id`=?;");
            }
        
            return _selectQuery_masterItemGroup;
        }
        
        /// <summary>
        /// SELECT `id` FROM `item_group` WHERE `group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemGroup_GroupId()
        {
            if (_indexedSelectQuery_itemGroup_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemGroup_groupId = connection.PreparedQuery("SELECT `id` FROM `item_group` WHERE `group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_itemGroup_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id` FROM `item_group`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ItemGroup()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group_id` FROM `item_group`;");
        }
        
        // SQL statements for item/item_place
        
        /// <summary>
        /// SELECT `transition_type`,`transition_value`,`start_date` FROM `item_place` WHERE `id`=? ORDER BY `transition_type` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ItemPlace_Id()
        {
            if (_indexedSelectQuery_itemPlace_id == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_itemPlace_id = connection.PreparedQuery("SELECT `transition_type`,`transition_value`,`start_date` FROM `item_place` WHERE `id`=? ORDER BY `transition_type` ASC;");
            }
        
            return _indexedSelectQuery_itemPlace_id;
        }
        
        /// <summary>
        /// SELECT `id`,`transition_type`,`transition_value`,`start_date` FROM `item_place`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ItemPlace()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`transition_type`,`transition_value`,`start_date` FROM `item_place`;");
        }
        
        // SQL statements for item/price_change
        
        /// <summary>
        /// SELECT `group_id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_PriceChange()
        {
            if (_selectQuery_masterPriceChange == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterPriceChange = connection.PreparedQuery("SELECT `group_id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change` WHERE `id`=?;");
            }
        
            return _selectQuery_masterPriceChange;
        }
        
        /// <summary>
        /// SELECT `id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change` WHERE `group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_PriceChange_GroupId()
        {
            if (_indexedSelectQuery_priceChange_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_priceChange_groupId = connection.PreparedQuery("SELECT `id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change` WHERE `group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_priceChange_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_PriceChange()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group_id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change`;");
        }
        
        // SQL statements for item/exchange_ticket_detail
        
        /// <summary>
        /// SELECT `card_type`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ExchangeTicketDetail()
        {
            if (_selectQuery_masterExchangeTicketDetail == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterExchangeTicketDetail = connection.PreparedQuery("SELECT `card_type`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail` WHERE `id`=?;");
            }
        
            return _selectQuery_masterExchangeTicketDetail;
        }
        
        /// <summary>
        /// SELECT `id`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail` WHERE `card_type`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ExchangeTicketDetail_CardType()
        {
            if (_indexedSelectQuery_exchangeTicketDetail_cardType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_exchangeTicketDetail_cardType = connection.PreparedQuery("SELECT `id`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail` WHERE `card_type`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_exchangeTicketDetail_cardType;
        }
        
        /// <summary>
        /// SELECT `id`,`card_type`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ExchangeTicketDetail()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`card_type`,`card_id`,`additional_piece_num`,`start_date`,`end_date` FROM `exchange_ticket_detail`;");
        }
    }
}
#endif
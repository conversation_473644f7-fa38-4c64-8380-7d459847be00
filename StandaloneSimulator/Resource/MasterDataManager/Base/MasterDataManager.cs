
// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: manager
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public abstract class AbstractMasterDatabase
    {
        public abstract void Unload();
        public abstract LibNative.Sqlite3.Query Query(string sql);
    }

    public abstract class AbstractMasterData
    {
        protected AbstractMasterData(AbstractMasterDatabase db) { }
        protected AbstractMasterData(ArrayList list) { }

        public abstract void Unload();
    }

    public partial class MasterDataManager : Singleton<MasterDataManager>
    {
        private Dictionary<string, AbstractMasterDatabase> _databases = new Dictionary<string, AbstractMasterDatabase>();

        private LibNative.Sqlite3.Connection _masterConnection = null;

        // group: card
        public MasterCardData masterCardData                                         { get; private set; }
        public MasterCardRarityData masterCardRarityData                             { get; private set; }
        public MasterCardTalentUpgrade masterCardTalentUpgrade                       { get; private set; }
        public MasterSupportCardData masterSupportCardData                           { get; private set; }
        public MasterSupportCardLimit masterSupportCardLimit                         { get; private set; }
        public MasterSupportCardLevel masterSupportCardLevel                         { get; private set; }
        public MasterSupportCardEffectTable masterSupportCardEffectTable             { get; private set; }
        public MasterSupportCardUniqueEffect masterSupportCardUniqueEffect           { get; private set; }
        public MasterSupportCardEffectFilter masterSupportCardEffectFilter           { get; private set; }
        public MasterSupportCardEffectFilterGroup masterSupportCardEffectFilterGroup { get; private set; }
        public MasterSupportCardTeamScoreBonus masterSupportCardTeamScoreBonus       { get; private set; }
        public MasterCharaData masterCharaData                                       { get; private set; }
        public MasterCharaDataGroup masterCharaDataGroup                             { get; private set; }
        public MasterCharaType masterCharaType                                       { get; private set; }
        public MasterCharaCategoryMotion masterCharaCategoryMotion                   { get; private set; }
        public MasterCharaMotionSet masterCharaMotionSet                             { get; private set; }
        public MasterCharacterPropAnimation masterCharacterPropAnimation             { get; private set; }
        public MasterDressData masterDressData                                       { get; private set; }
        public MasterLoveRank masterLoveRank                                         { get; private set; }
        public MasterNeedPieceNumData masterNeedPieceNumData                         { get; private set; }
        public MasterSkillData masterSkillData                                       { get; private set; }
        public MasterSkillSet masterSkillSet                                         { get; private set; }
        public MasterAvailableSkillSet masterAvailableSkillSet                       { get; private set; }
        public MasterSkillExp masterSkillExp                                         { get; private set; }
        public MasterSkillLevelValue masterSkillLevelValue                           { get; private set; }
        public MasterSkillUpgradeDescription masterSkillUpgradeDescription           { get; private set; }
        public MasterSkillUpgradeSpeciality masterSkillUpgradeSpeciality             { get; private set; }
        public MasterSkillUpgradeCondition masterSkillUpgradeCondition               { get; private set; }
        public MasterSkillUpScenarioCondition masterSkillUpScenarioCondition         { get; private set; }
        public MasterCharacterSystemText masterCharacterSystemText                   { get; private set; }
        public MasterCharacterSystemLottery masterCharacterSystemLottery             { get; private set; }
        public MasterFaceTypeData masterFaceTypeData                                 { get; private set; }
        public MasterFacialMouthChange masterFacialMouthChange                       { get; private set; }
        public MasterCharaMotionAct masterCharaMotionAct                             { get; private set; }
        public MasterRandomEarTailMotion masterRandomEarTailMotion                   { get; private set; }
        public MasterNickname masterNickname                                         { get; private set; }
        public MasterSupportCardLimitBreak masterSupportCardLimitBreak               { get; private set; }
        public MasterCardTalentHintUpgrade masterCardTalentHintUpgrade               { get; private set; }
        public MasterSupportCardGroup masterSupportCardGroup                         { get; private set; }
        public MasterCharaDressColorSetDefault masterCharaDressColorSetDefault       { get; private set; }
        public MasterCharaDressColorSet masterCharaDressColorSet                     { get; private set; }
        // group: item
        public MasterItemData masterItemData                                         { get; private set; }
        public MasterItemExchange masterItemExchange                                 { get; private set; }
        public MasterItemExchangeTop masterItemExchangeTop                           { get; private set; }
        public MasterPieceData masterPieceData                                       { get; private set; }
        public MasterItemGroup masterItemGroup                                       { get; private set; }
        public MasterItemPlace masterItemPlace                                       { get; private set; }
        public MasterPriceChange masterPriceChange                                   { get; private set; }
        public MasterExchangeTicketDetail masterExchangeTicketDetail                 { get; private set; }
        // group: race
        public MasterRace masterRace                                                 { get; private set; }
        public MasterRaceMotivationRate masterRaceMotivationRate                     { get; private set; }
        public MasterRaceProperDistanceRate masterRaceProperDistanceRate             { get; private set; }
        public MasterRaceProperRunningstyleRate masterRaceProperRunningstyleRate     { get; private set; }
        public MasterRaceProperGroundRate masterRaceProperGroundRate                 { get; private set; }
        public MasterRaceBibColor masterRaceBibColor                                 { get; private set; }
        public MasterRaceCourseSet masterRaceCourseSet                               { get; private set; }
        public MasterRaceSingleModeTeamStatus masterRaceSingleModeTeamStatus         { get; private set; }
        public MasterRaceCourseSetStatus masterRaceCourseSetStatus                   { get; private set; }
        public MasterRaceEnvDefine masterRaceEnvDefine                               { get; private set; }
        public MasterRaceFenceSet masterRaceFenceSet                                 { get; private set; }
        public MasterRaceInstance masterRaceInstance                                 { get; private set; }
        public MasterRaceBgm masterRaceBgm                                           { get; private set; }
        public MasterRaceBgmPattern masterRaceBgmPattern                             { get; private set; }
        public MasterRaceBgmCutin masterRaceBgmCutin                                 { get; private set; }
        public MasterRaceJikkyoBase masterRaceJikkyoBase                             { get; private set; }
        public MasterRaceJikkyoBaseVenus masterRaceJikkyoBaseVenus                   { get; private set; }
        public MasterRaceJikkyoComment masterRaceJikkyoComment                       { get; private set; }
        public MasterRaceJikkyoCue masterRaceJikkyoCue                               { get; private set; }
        public MasterRaceJikkyoMessage masterRaceJikkyoMessage                       { get; private set; }
        public MasterRaceJikkyoRace masterRaceJikkyoRace                             { get; private set; }
        public MasterRaceJikkyoTrigger masterRaceJikkyoTrigger                       { get; private set; }
        public MasterRacePlayerCamera masterRacePlayerCamera                         { get; private set; }
        public MasterRaceTrack masterRaceTrack                                       { get; private set; }
        public MasterRaceCondition masterRaceCondition                               { get; private set; }
        public MasterRaceTrophy masterRaceTrophy                                     { get; private set; }
        public MasterRaceOverrunPattern masterRaceOverrunPattern                     { get; private set; }
        public MasterMobData masterMobData                                           { get; private set; }
        public MasterMobDressColorSet masterMobDressColorSet                         { get; private set; }
        public MasterMobHairColorSet masterMobHairColorSet                           { get; private set; }
        public MasterAudienceData masterAudienceData                                 { get; private set; }
        public MasterAudienceDressColorSet masterAudienceDressColorSet               { get; private set; }
        public MasterAudienceHairColorSet masterAudienceHairColorSet                 { get; private set; }
        public MasterAudienceImpostor masterAudienceImpostor                         { get; private set; }
        public MasterAudienceImpostorLottery masterAudienceImpostorLottery           { get; private set; }
        public MasterAudienceImpostorColorSet masterAudienceImpostorColorSet         { get; private set; }
        // group: team_stadium
        public MasterTeamStadiumRawScore masterTeamStadiumRawScore                   { get; private set; }
        public MasterTeamStadiumScoreBonus masterTeamStadiumScoreBonus               { get; private set; }
        public MasterTeamStadium masterTeamStadium                                   { get; private set; }
        public MasterTeamStadiumClass masterTeamStadiumClass                         { get; private set; }
        public MasterTeamStadiumClassReward masterTeamStadiumClassReward             { get; private set; }
        public MasterTeamStadiumRank masterTeamStadiumRank                           { get; private set; }
        public MasterTeamStadiumEvaluationRate masterTeamStadiumEvaluationRate       { get; private set; }
        public MasterTeamStadiumStandMotion masterTeamStadiumStandMotion             { get; private set; }
        public MasterTeamStadiumRaceResultMotion masterTeamStadiumRaceResultMotion   { get; private set; }
        public MasterTeamStadiumBgm masterTeamStadiumBgm                             { get; private set; }
        public MasterTeamStadiumSupportText masterTeamStadiumSupportText             { get; private set; }
        // group: challenge_match
        public MasterChallengeMatchData masterChallengeMatchData                     { get; private set; }
        public MasterChallengeMatchRace masterChallengeMatchRace                     { get; private set; }
        public MasterChallengeMatchBossNpc masterChallengeMatchBossNpc               { get; private set; }
        public MasterChallengeMatchRawPoint masterChallengeMatchRawPoint             { get; private set; }

        #region Override
        protected override void OnFinalize()
        {
            base.OnFinalize();
            ForceResetDatabases();
        }
        #endregion

        protected string[] GetDatabaseAssetList()
        {
            return new string[] {
                "master_card.mdb",
                "master_item.mdb",
                "master_race.mdb",
                "master_team_stadium.mdb",
                "master_challenge_match.mdb"
            };
        }

        protected HashSet<string> GetKnownMasterGroups()
        {
            return new HashSet<string> {
                "master_card",
                "master_item",
                "master_race",
                "master_team_stadium",
                "master_challenge_match"
            };
        }

        protected bool SetupMasterGroup(string groupName)
        {
            bool result = true;

            if (_masterConnection == null) {
                OpenMasterConnection();
            }

            switch (groupName) {
            case "master_card":
                if (_masterConnection != null) {
                    MasterCardDatabase db = new MasterCardDatabase(_masterConnection);
                    this.masterCardData                     = db.masterCardData;
                        this.masterCardRarityData               = db.masterCardRarityData;
                        this.masterCardTalentUpgrade            = db.masterCardTalentUpgrade;
                        this.masterSupportCardData              = db.masterSupportCardData;
                        this.masterSupportCardLimit             = db.masterSupportCardLimit;
                        this.masterSupportCardLevel             = db.masterSupportCardLevel;
                        this.masterSupportCardEffectTable       = db.masterSupportCardEffectTable;
                        this.masterSupportCardUniqueEffect      = db.masterSupportCardUniqueEffect;
                        this.masterSupportCardEffectFilter      = db.masterSupportCardEffectFilter;
                        this.masterSupportCardEffectFilterGroup = db.masterSupportCardEffectFilterGroup;
                        this.masterSupportCardTeamScoreBonus    = db.masterSupportCardTeamScoreBonus;
                        this.masterCharaData                    = db.masterCharaData;
                        this.masterCharaDataGroup               = db.masterCharaDataGroup;
                        this.masterCharaType                    = db.masterCharaType;
                        this.masterCharaCategoryMotion          = db.masterCharaCategoryMotion;
                        this.masterCharaMotionSet               = db.masterCharaMotionSet;
                        this.masterCharacterPropAnimation       = db.masterCharacterPropAnimation;
                        this.masterDressData                    = db.masterDressData;
                        this.masterLoveRank                     = db.masterLoveRank;
                        this.masterNeedPieceNumData             = db.masterNeedPieceNumData;
                        this.masterSkillData                    = db.masterSkillData;
                        this.masterSkillSet                     = db.masterSkillSet;
                        this.masterAvailableSkillSet            = db.masterAvailableSkillSet;
                        this.masterSkillExp                     = db.masterSkillExp;
                        this.masterSkillLevelValue              = db.masterSkillLevelValue;
                        this.masterSkillUpgradeDescription      = db.masterSkillUpgradeDescription;
                        this.masterSkillUpgradeSpeciality       = db.masterSkillUpgradeSpeciality;
                        this.masterSkillUpgradeCondition        = db.masterSkillUpgradeCondition;
                        this.masterSkillUpScenarioCondition     = db.masterSkillUpScenarioCondition;
                        this.masterCharacterSystemText          = db.masterCharacterSystemText;
                        this.masterCharacterSystemLottery       = db.masterCharacterSystemLottery;
                        this.masterFaceTypeData                 = db.masterFaceTypeData;
                        this.masterFacialMouthChange            = db.masterFacialMouthChange;
                        this.masterCharaMotionAct               = db.masterCharaMotionAct;
                        this.masterRandomEarTailMotion          = db.masterRandomEarTailMotion;
                        this.masterNickname                     = db.masterNickname;
                        this.masterSupportCardLimitBreak        = db.masterSupportCardLimitBreak;
                        this.masterCardTalentHintUpgrade        = db.masterCardTalentHintUpgrade;
                        this.masterSupportCardGroup             = db.masterSupportCardGroup;
                        this.masterCharaDressColorSetDefault    = db.masterCharaDressColorSetDefault;
                        this.masterCharaDressColorSet           = db.masterCharaDressColorSet;
                    _databases.Add("master_card", db);
                }
                break;
            case "master_item":
                if (_masterConnection != null) {
                    MasterItemDatabase db = new MasterItemDatabase(_masterConnection);
                    this.masterItemData             = db.masterItemData;
                        this.masterItemExchange         = db.masterItemExchange;
                        this.masterItemExchangeTop      = db.masterItemExchangeTop;
                        this.masterPieceData            = db.masterPieceData;
                        this.masterItemGroup            = db.masterItemGroup;
                        this.masterItemPlace            = db.masterItemPlace;
                        this.masterPriceChange          = db.masterPriceChange;
                        this.masterExchangeTicketDetail = db.masterExchangeTicketDetail;
                    _databases.Add("master_item", db);
                }
                break;
            case "master_race":
                if (_masterConnection != null) {
                    MasterRaceDatabase db = new MasterRaceDatabase(_masterConnection);
                    this.masterRace                       = db.masterRace;
                        this.masterRaceMotivationRate         = db.masterRaceMotivationRate;
                        this.masterRaceProperDistanceRate     = db.masterRaceProperDistanceRate;
                        this.masterRaceProperRunningstyleRate = db.masterRaceProperRunningstyleRate;
                        this.masterRaceProperGroundRate       = db.masterRaceProperGroundRate;
                        this.masterRaceBibColor               = db.masterRaceBibColor;
                        this.masterRaceCourseSet              = db.masterRaceCourseSet;
                        this.masterRaceSingleModeTeamStatus   = db.masterRaceSingleModeTeamStatus;
                        this.masterRaceCourseSetStatus        = db.masterRaceCourseSetStatus;
                        this.masterRaceEnvDefine              = db.masterRaceEnvDefine;
                        this.masterRaceFenceSet               = db.masterRaceFenceSet;
                        this.masterRaceInstance               = db.masterRaceInstance;
                        this.masterRaceBgm                    = db.masterRaceBgm;
                        this.masterRaceBgmPattern             = db.masterRaceBgmPattern;
                        this.masterRaceBgmCutin               = db.masterRaceBgmCutin;
                        this.masterRaceJikkyoBase             = db.masterRaceJikkyoBase;
                        this.masterRaceJikkyoBaseVenus        = db.masterRaceJikkyoBaseVenus;
                        this.masterRaceJikkyoComment          = db.masterRaceJikkyoComment;
                        this.masterRaceJikkyoCue              = db.masterRaceJikkyoCue;
                        this.masterRaceJikkyoMessage          = db.masterRaceJikkyoMessage;
                        this.masterRaceJikkyoRace             = db.masterRaceJikkyoRace;
                        this.masterRaceJikkyoTrigger          = db.masterRaceJikkyoTrigger;
                        this.masterRacePlayerCamera           = db.masterRacePlayerCamera;
                        this.masterRaceTrack                  = db.masterRaceTrack;
                        this.masterRaceCondition              = db.masterRaceCondition;
                        this.masterRaceTrophy                 = db.masterRaceTrophy;
                        this.masterRaceOverrunPattern         = db.masterRaceOverrunPattern;
                        this.masterMobData                    = db.masterMobData;
                        this.masterMobDressColorSet           = db.masterMobDressColorSet;
                        this.masterMobHairColorSet            = db.masterMobHairColorSet;
                        this.masterAudienceData               = db.masterAudienceData;
                        this.masterAudienceDressColorSet      = db.masterAudienceDressColorSet;
                        this.masterAudienceHairColorSet       = db.masterAudienceHairColorSet;
                        this.masterAudienceImpostor           = db.masterAudienceImpostor;
                        this.masterAudienceImpostorLottery    = db.masterAudienceImpostorLottery;
                        this.masterAudienceImpostorColorSet   = db.masterAudienceImpostorColorSet;
                    _databases.Add("master_race", db);
                }
                break;
            case "master_team_stadium":
                if (_masterConnection != null) {
                    MasterTeamStadiumDatabase db = new MasterTeamStadiumDatabase(_masterConnection);
                    this.masterTeamStadiumRawScore         = db.masterTeamStadiumRawScore;
                        this.masterTeamStadiumScoreBonus       = db.masterTeamStadiumScoreBonus;
                        this.masterTeamStadium                 = db.masterTeamStadium;
                        this.masterTeamStadiumClass            = db.masterTeamStadiumClass;
                        this.masterTeamStadiumClassReward      = db.masterTeamStadiumClassReward;
                        this.masterTeamStadiumRank             = db.masterTeamStadiumRank;
                        this.masterTeamStadiumEvaluationRate   = db.masterTeamStadiumEvaluationRate;
                        this.masterTeamStadiumStandMotion      = db.masterTeamStadiumStandMotion;
                        this.masterTeamStadiumRaceResultMotion = db.masterTeamStadiumRaceResultMotion;
                        this.masterTeamStadiumBgm              = db.masterTeamStadiumBgm;
                        this.masterTeamStadiumSupportText      = db.masterTeamStadiumSupportText;
                    _databases.Add("master_team_stadium", db);
                }
                break;
            case "master_challenge_match":
                if (_masterConnection != null) {
                    MasterChallengeMatchDatabase db = new MasterChallengeMatchDatabase(_masterConnection);
                    this.masterChallengeMatchData     = db.masterChallengeMatchData;
                        this.masterChallengeMatchRace     = db.masterChallengeMatchRace;
                        this.masterChallengeMatchBossNpc  = db.masterChallengeMatchBossNpc;
                        this.masterChallengeMatchRawPoint = db.masterChallengeMatchRawPoint;
                    _databases.Add("master_challenge_match", db);
                }
                break;
            default:
                Debug.LogError("Unknown database group: " + groupName);
                result = false;
                break;
            }

            return result;
        }

        public void UnloadDatabases()
        {
            foreach (KeyValuePair<string, AbstractMasterDatabase> pair in _databases)
            {
                pair.Value.Unload();
            }

            if (_masterConnection != null)
            {
                Debug.Log("DB Close: master.mdb");
                _masterConnection.CloseDB();
            }
        }

        // must be called on software reset
        public void ForceResetDatabases()
        {
            UnloadDatabases();
            _databases.Clear();
            _masterConnection = null;
        }
    }
}
#endif
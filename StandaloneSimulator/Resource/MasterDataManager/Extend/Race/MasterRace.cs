#if STANDALONE_SIMULATOR
namespace StandaloneSimulator
{
    public partial class MasterRace
    {
        public partial class Race
        {
            /// <summary>
            /// 補間済みCourseSet
            /// クライアントはクライアント側のMasterが読まれることに注意
            /// </summary>
            public int CourseSet
            {
                get
                {
                    var courseSet = _courseSet;
                    // 【先バレ防止対応】上書き用データがある場合は上書きをする
                    // 現在は「RatingRaceで、CourseSetInfoに記載がある場合」に対応する
                    {
                        // レート戦か？
                        if (Group == (int)Gallop.RaceDefine.RaceGroup.RatingRace)
                        {
                            // レースシミュレーター：レースシミュレーターWorkがあるか？
                            if (StandaloneSimulator.WorkRaceSimulatorDataManager.HasInstance())
                            {
                                // Workに保存された上書き用データに、合致するIDがあれば、上書きした値を渡す
                                if (StandaloneSimulator.WorkRaceSimulatorDataManager.Instance.OverwriteCourseSetDict.TryGetValue(Id, out var value))
                                {
                                    courseSet = value;
                                }
                            }
                        }
                    }
                    return courseSet;
                }
            }
        }
    }
}
#endif
#if STANDALONE_SIMULATOR
using System.Collections;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public partial class MasterSkillData
    {
        public partial class SkillData
        {
            private bool _tagIdsCached = false; //リスト作成済みフラグ(リストがnull許容なので)
            private List<int> _tagIdList = null;

            /// <summary>
            /// tag_idカラムの文字列をint配列で取得。
            /// </summary>
            /// <returns>カラムが空の場合はnullを返却。</returns>
            public List<int> GetTagIds()
            {
                //既に計算済みならキャッシュしたリストを返す
                if (_tagIdsCached) return _tagIdList;

                var tagIdStr = this.TagId;
                if (string.IsNullOrEmpty(tagIdStr))
                {
                    _tagIdsCached = true;
                    return null;
                }

                const char SEPARATOR = '/';
                var tagIds = tagIdStr.Split(SEPARATOR);

                _tagIdList = new List<int>(tagIds.Length);
                for (int i = 0; i < tagIds.Length; ++i)
                {
                    int id = 0;
                    if (int.TryParse(tagIds[i], out id))
                    {
                        _tagIdList.Add(id);
                    }
                    else
                    {
                        Debug.LogError(string.Format("intへのパースに失敗しました。skillId={0}, tagId={1}", this.Id, tagIds[i]));
                    }
                }

                _tagIdsCached = true;
                return _tagIdList;
            }
        }
    }
}
#endif

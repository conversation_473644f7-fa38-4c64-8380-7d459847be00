#if STANDALONE_SIMULATOR
//#define USE_PROGRESS_UI
using System;
using System.Collections;
using System.IO;
//using Cute.AssetBundle;
using System.ComponentModel;

namespace StandaloneSimulator
{
    public partial class MasterDataManager
    {
        private bool _isCompleteSetup = false;

        public bool IsCompleteSetup
        {
            get { return _isCompleteSetup; }
        }

        /// <summary>
        /// マスターデータ初期化
        /// </summary>
        /// <returns></returns>
        public void SetupMasterData(Action onFail = null)
        {
            // プリロードが必要なマスターデータをキャッシュ
            LoadMasterData();

            _isCompleteSetup = true;
        }

        /// <summary>
        /// db open
        /// </summary>
        public void OpenMasterConnection()
        {
            if (_masterConnection != null) { return; }

            var masterFilePath = RaceSimulateResource.Instance.MasterDataDirPath + "master.mdb";
            if(!File.Exists(masterFilePath))
            {
                Debug.LogError($"MDB file not found. masterFilePath={masterFilePath}");
            }

            _masterConnection = new LibNative.Sqlite3.Connection();
            _masterConnection.Open(masterFilePath);
        }

        private void LoadMasterData()
        {
            // マスター情報を初期化
            ForceResetDatabases();

            foreach (string group in GetKnownMasterGroups())
            {
                SetupMasterGroup(group);
            }
        }
    }
}
#endif

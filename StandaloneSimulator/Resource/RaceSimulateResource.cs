#if STANDALONE_SIMULATOR
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Caching.Memory;


namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レースシミュレートで使用するリソース読み込み。
    /// </summary>
    //-------------------------------------------------------------------
    public class RaceSimulateResource : Singleton<RaceSimulateResource>
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>リソースフォルダの場所。</summary>
#if PUBLISHED
        private const string RESOURCE_DIR = "../../resource/{0}/RaceSimulator/";
#else
        private const string RESOURCE_DIR = "../../../resource/{0}/RaceSimulator/";
#endif
#if STANDALONE_SIMULATOR_LOCAL
        /// <summary>ローカル環境におけるリソースフォルダの場所。</summary>
        private const string LOCAL_RESOURCE_DIR = "../../resource/_GallopResources/Bundle/Resources/";
#endif

        /// <summary>ast_race_paramdefine.assetのパス。</summary>
        private const string PARAM_DEFINE_FILENAME = "Race/RaceMain/ast_race_paramdefine.asset";
        /// <summary>コースパスのパス。</summary>
        private const string RACE_LANE_BASE_PATH = "Race/Course/{0:0000}/pos/an_pos_race{1:0000}_00_{2:0000}_{3:00}_{4}_{5}.asset";
        /// <summary>オーバーランコースパスのパス。</summary>
        private const string OVER_RUN_BASE_PATH = "Race/Course/{0:0000}/pos/an_pos_race{1:0000}_01_0000_{2:00}_0_{3}.asset";
        /// <summary>コースイベントのパス。</summary>
        private const string COURSE_PARAM_BASE_PATH = "Race/CourseEventParam/" + "{0:00000}/pfb_prm_race{0:00000}.prefab";

        /// <summary> キャッシュキーの元となるテンプレート </summary>
        private const string CACHE_KEY_BASE_TEMPLATE = "{0}:{1}:{2}";

        /// <summary>ast_race_paramdefine.assetのキャッシュキー。</summary>
        private const string PARAM_DEFINE_CACHE_KEY = "RaceParamDefine";
        /// <summary>コースパスのキャッシュキー。</summary>
        private const string RACE_LANE_CACHE_KEY = "CoursePath:{0}_{1}_{2}_{3}_{4}";
        /// <summary>オーバーランコースパスのキャッシュキー。</summary>
        private const string OVER_RUN_CACHE_KEY = "OverRunCoursePath:{0}_{1}_{2}";
        /// <summary>コースイベントのキャッシュキー。</summary>
        private const string COURSE_PARAM_CACHE_KEY = "CourseParam:{0}";

        /// <summary>キャッシュの持続時間(秒)</summary>
        private const int CACHE_TTL_SECOND = 3600;

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public Gallop.RaceParamDefine ParamDefine { get; private set; }
        public Gallop.CourseLaneAnim CoursePath { get; private set; }
        public Gallop.CourseLaneAnim OverRunCoursePath { get; private set; }
        public CourseParamTable CourseParamTable { get; private set; }

        private string _resourceVersion;
        public string ResourceDirPath { get; private set; }
        public string MasterDataDirPath  { get; private set; }

        private IMemoryCache? _memoryCache;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// 初期化。
        /// </summary>
        /// <param name="resourceVersion"></param>
        /// <param name="memoryCache">メモリキャッシュする場合に指定。指定しない場合はメモリキャッシュを行わない</param>
        public void Init(string resourceVersion, IMemoryCache? memoryCache = null)
        {
            _resourceVersion = resourceVersion;
            if (memoryCache != null)
            {
                _memoryCache = memoryCache;
            }

            // RESOURCE_DIRを絶対パスに変換する。
            var assemblyFilePathName = System.Reflection.Assembly.GetEntryAssembly().Location;
            var assemblyFilePath = Path.GetDirectoryName(assemblyFilePathName);
            var basePath = new Uri(assemblyFilePath + "/");
            var resourcePath = new Uri(basePath, string.Format(RESOURCE_DIR, _resourceVersion));
            
#if STANDALONE_SIMULATOR_LOCAL
            ResourceDirPath = (new Uri(basePath, LOCAL_RESOURCE_DIR)).LocalPath;
#else
            ResourceDirPath = resourcePath.LocalPath;
#endif
            MasterDataDirPath = resourcePath.LocalPath;
        }

        #region paramdefine
        /// <summary>
        /// ast_race_paramdefine.assetの読み込み。
        /// </summary>
        public void LoadRaceParamDefine()
        {
            ParamDefine = LoadWithCache<Gallop.RaceParamDefine>(GetRaceParamDefineCacheKey(), ResourceDirPath + PARAM_DEFINE_FILENAME);
#if CYG_DEBUG
            Debug.Log($"Load paramdefine yaml.:{EntryPoint._sw.ElapsedMilliseconds}ms");
#endif
        }
        #endregion

        #region コースパス
        /// <summary>
        /// レースのコースパスのパス取得。
        /// </summary>
        private static string GetCoursePathPath(
            int raceTrackId,
            int distance,
            Gallop.RaceDefine.GroundType ground,
            Gallop.RaceDefine.CourseAround around,
            Gallop.RaceDefine.RaceType raceType)
        {
            var pathType = GetCoursePatyType(raceType);
            return string.Format(RACE_LANE_BASE_PATH, raceTrackId, raceTrackId, distance, GetGroundPathInt(ground), GetAroundPathInt(around), (int)pathType);
        }

        /// <summary>
        /// オーバーランのコースパスのパス取得。
        /// </summary>
        public static string GetOverRunCoursePathPath(
            int raceTrackId,
            Gallop.RaceDefine.GroundType ground,
            Gallop.RaceDefine.RaceType raceType)
        {
            var pathType = GetCoursePatyType(raceType);
            return string.Format(OVER_RUN_BASE_PATH, raceTrackId, raceTrackId, GetGroundPathInt(ground), (int)pathType);
        }

        /// <summary>
        /// 競馬場リソースパス構築に使用する地面Int値取得。
        /// </summary>
        private static int GetGroundPathInt(Gallop.RaceDefine.GroundType ground)
        {
            // enumが1始まりなので-1して返却する。また、引数は1以上である。
            Debug.Assert(ground > 0);
            return (int)(ground - 1);
        }

        /// <summary>
        /// 競馬場リソースパス構築に使用する内回り/外回りInt値取得。
        /// </summary>
        private static int GetAroundPathInt(Gallop.RaceDefine.CourseAround around)
        {
            // enumが1始まりなので-1して返却する。また、引数は1以上である。
            Debug.Assert(around > 0);
            return (int)(around - 1);
        }

        /// <summary>
        /// コースパス構築に使用するコースパス種類取得。
        /// </summary>
        private static Gallop.RaceDefine.CoursePathType GetCoursePatyType(Gallop.RaceDefine.RaceType raceType)
        {
            if (raceType == Gallop.RaceDefine.RaceType.Story)
            {
                return Gallop.RaceDefine.CoursePathType.StoryRace;
            }
            else
            {
                return Gallop.RaceDefine.CoursePathType.NormalRace;
            }
        }

        /// <summary>
        /// コースパスの読み込み。
        /// </summary>
        public void LoadCousePath(
            int raceTrackId,
            int distance,
            Gallop.RaceDefine.GroundType ground,
            Gallop.RaceDefine.CourseAround around,
            Gallop.RaceDefine.RaceType raceType)
        {
            var path = GetCoursePathPath(raceTrackId, distance, ground, around, raceType);
            var key = GetCoursePathCacheKey(raceTrackId, distance, ground, around, raceType);
            CoursePath = LoadWithCache<Gallop.CourseLaneAnim>(key , ResourceDirPath + path);
#if CYG_DEBUG
            Debug.Log($"Load coursepath yaml.:{EntryPoint._sw.ElapsedMilliseconds}ms");
#endif
        }

        /// <summary>
        /// オーバーランコースパスの読み込み。
        /// </summary>
        public void LoadOverRunCousePath(
            int raceTrackId,
            Gallop.RaceDefine.GroundType ground,
            Gallop.RaceDefine.RaceType raceType)
        {
            var path = GetOverRunCoursePathPath(raceTrackId, ground, raceType);
            var key = GetOverRunCoursePathKey(raceTrackId, ground, raceType);
            OverRunCoursePath = LoadWithCache<Gallop.CourseLaneAnim>(key, ResourceDirPath + path);
#if CYG_DEBUG
            Debug.Log($"Load overrun yaml.:{EntryPoint._sw.ElapsedMilliseconds}ms");
#endif
        }
        #endregion

        #region コースイベント
        /// <summary>
        /// コースイベントパラメータパス取得。
        /// </summary>
        public static string GetCourseParamPath(int courseSetId)
        {
            return string.Format(COURSE_PARAM_BASE_PATH, courseSetId);
        }

        /// <summary>
        /// コースイベントの読み込み。
        /// </summary>
        public void LoadCourseEvent(int courseSetId)
        {
            var path = GetCourseParamPath(courseSetId);
            CourseParamTable = LoadWithCache<CourseParamTable>(GetCourseParamKey(courseSetId), ResourceDirPath + path);
#if CYG_DEBUG
            Debug.Log($"Load courseevent yaml.:{EntryPoint._sw.ElapsedMilliseconds}ms");
#endif
        }
        #endregion

        #region キャッシュ関連

        /// <summary>
        /// キャッシュキーを構築する
        /// </summary>
        /// <param name="identify">対象のリソースを一意に絞り込む識別子</param>
        private string BuildCacheKey(string identify)
        {
            return string.Format(CACHE_KEY_BASE_TEMPLATE, _resourceVersion, GetType().Name, identify);
        }

        /// <summary>
        /// ast_race_paramdefine.assetのキャッシュキーを取得
        /// </summary>
        private string GetRaceParamDefineCacheKey()
        {
            return BuildCacheKey(PARAM_DEFINE_CACHE_KEY);
        }

        /// <summary>
        /// レースのコースパスのキャッシュキーを取得
        /// </summary>
        private string GetCoursePathCacheKey(int raceTrackId, int distance, Gallop.RaceDefine.GroundType ground,
            Gallop.RaceDefine.CourseAround around, Gallop.RaceDefine.RaceType raceType)
        {
            return BuildCacheKey(string.Format(RACE_LANE_CACHE_KEY, raceTrackId, distance, ground, around, raceType));
        }

        /// <summary>
        /// オーバーランのコースパスのキャッシュキーを取得
        /// </summary>
        private string GetOverRunCoursePathKey(int raceTrackId, Gallop.RaceDefine.GroundType ground,
            Gallop.RaceDefine.RaceType raceType)
        {
            return BuildCacheKey(string.Format(OVER_RUN_CACHE_KEY, raceTrackId, ground, raceType));
        }

        /// <summary>
        /// コースイベントパラメータのキャッシュキーを取得
        /// </summary>
        private string GetCourseParamKey(int courseSetId)
        {
            return BuildCacheKey(string.Format(COURSE_PARAM_CACHE_KEY, courseSetId));
        }

        /// <summary>
        /// キャッシュを用いてYamlを読み込む
        /// </summary>
        /// <param name="key">キャッシュキー</param>
        /// <param name="path">Yamlのファイルパス</param>
        /// <typeparam name="T">読み込むクラス</typeparam>
        private T LoadWithCache<T>(string key, string path) where T : IYamlLoadable
        {
            if (_memoryCache == null)
            {
                return YamlLoadHelper.LoadYaml<T>(path);
            }

            if (_memoryCache.TryGetValue<T>(key, out var cachedValue))
            {
                return cachedValue;
            }

            var result = YamlLoadHelper.LoadYaml<T>(path);
            var cacheEntryOptions = new MemoryCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromSeconds(CACHE_TTL_SECOND));
            _memoryCache.Set(key, result, cacheEntryOptions);

            return result;
        }
        #endregion
    }
}
#endif

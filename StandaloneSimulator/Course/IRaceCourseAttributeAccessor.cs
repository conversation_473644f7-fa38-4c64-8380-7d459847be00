using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中のコース上属性アクセッサーインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IRaceCourseAttributeAccessor
    {
        /// <summary>
        /// 直線取得。
        /// </summary>
        List<CourseStraight> GetStraightList();
        /// <summary>
        /// 直線種類指定で直線取得。
        /// </summary>
        List<CourseStraight> GetStraightListByType(Gallop.RaceDefine.StraightFrontType type);
        
        /// <summary>
        /// コーナー取得。
        /// </summary>
        List<CourseCorner> GetCornerList();
        /// <summary>
        /// 坂取得。
        /// </summary>
        List<CourseSlope> GetSlopeList();
    }
}

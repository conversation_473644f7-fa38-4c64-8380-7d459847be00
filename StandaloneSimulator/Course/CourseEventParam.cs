using System;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// コースイベント管理。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class CourseEventParam
    {
        ///////////////////////////////////////////////////////////////// 
        // 変数。
        ///////////////////////////////////////////////////////////////// 
        public List<CourseParam> CourseEventList { get; private set; }
        public CourseParam[] CourseEventCorner { get; private set; }
        public CourseParam[] CourseEventStraight { get; private set; }
        public CourseParam[] CourseEventSlope { get; private set; }

        /// <summary>
        /// レーン移動イベントの位置、もしくは第１コーナーの位置。どちらも無ければ0。
        /// </summary>
        public float FirstMoveLanePointDistance { get; private set; }

        /// <summary>
        /// レーン移動イベント、もしくは第１コーナーイベントが存在するかどうか。
        /// </summary>
        public bool IsEnableFirstMoveLane { get { return FirstMoveLanePointDistance > 0; } }

        /// <summary>
        /// レース中、インに寄るかどうか。
        /// </summary>
        public bool IsEmptyInMove { get; private set; }
        
        /// <summary> 最終直線のイベントがあるかどうか </summary>
        public bool IsExistLastStraightEvent { get; private set; } = false;

        ///////////////////////////////////////////////////////////////// 
        // 関数。
        ///////////////////////////////////////////////////////////////// 
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        //---------------------------------------------------------------
        public CourseEventParam()
        {
            CourseEventList = new List<CourseParam>();

            FirstMoveLanePointDistance  = 0;
            IsEmptyInMove               = true;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// コースイベント管理リスト構築。
        /// </summary>
        //---------------------------------------------------------------
        public void InitCourseEvent(CourseParamTable courseParamTable)
        {
            if( null != courseParamTable )
            {
                int num = courseParamTable.courseParams.Length;
                for ( int i = 0 ; i < num ; i++ )
                {
                    var param = courseParamTable.courseParams[i];
                    _AddCourseEvent(param);
                }

                // スタート地点からの距離で昇順にソート。
                CourseEventList.Sort( ( a, b ) => 
                {
                    if( a._distance < b._distance )
                    {
                        return -1;
                    }
                    else if( a._distance > b._distance )
                    {
                        return 1;
                    }
                    else
                    {
                        return 0;
                    }
                } );

                // 頻繁にアクセスするイベントをキャッシュ。
                _CacheEvent();
                _CacheFirstMoveLanePoint();
                CacheIsExistLastStraightEvent();
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// コーナーイベントのキャッシュ。
        /// </summary>
        //---------------------------------------------------------------
        private void _CacheEvent()
        {
            CourseEventCorner = CourseEventList.Where( e => CourseParamType.Corner == e._paramType ).ToArray();
            CourseEventStraight = CourseEventList.Where( e => CourseParamType.Straight == e._paramType ).ToArray();
            CourseEventSlope = CourseEventList.Where(e => CourseParamType.Slope == e._paramType).ToArray();
        }

        //---------------------------------------------------------------
        /// <summary>
        /// コースイベントによる強制レーン移動情報のキャッシュ。
        /// </summary>
        //---------------------------------------------------------------
        private void _CacheFirstMoveLanePoint()
        {
            // レーン移動イベントが打たれていれば、イベント地点とイン/アウトどっちによるかをキャッシュ。
            var laneMovePoint = CourseEventList.FirstOrDefault( e => e._paramType == CourseParamType.MoveLanePoint );
            if( null != laneMovePoint )
            {
                if( laneMovePoint.Values.Length == CourseParam.MOVELANEPOINT_VALUE_NUM )
                {
                    FirstMoveLanePointDistance  = laneMovePoint._distance;
                    IsEmptyInMove               = laneMovePoint.Values[CourseParam.MOVELANEPOINT_VALUE_INDEX_IN_OR_OUT ] == 1;
                    return;
                }
            }
            
            // レーン移動イベントが無ければ、最初のコーナーをレーン移動イベントと見なし、インに寄る。
            var firstCornerDistance = CalcFirstCornerDistance();
            if( firstCornerDistance > 0 )
            {
                FirstMoveLanePointDistance  = firstCornerDistance;
                IsEmptyInMove               = true;
                return;
            }

            // レーン移動イベントも無く、コーナーイベントも無ければ、距離経過による強制レーン移動は発動しないものとする。
            FirstMoveLanePointDistance  = 0;
            IsEmptyInMove               = false;
        }
        
        /// <summary>
        /// 最終直線用のイベントパラメータが設定されているかどうかの情報をキャッシュ
        /// </summary>
        private void CacheIsExistLastStraightEvent()
        {
            IsExistLastStraightEvent = false;
            for (int i = 0, cnt = CourseEventStraight.Length; i < cnt; i++)
            {
                // そもそも最終直線用のイベントパラメータが設定されていない
                if (CourseEventStraight[i].Values.Length < CourseParam.STRAIGHT_VALUE_INDEX_IS_LAST + 1)
                {
                    continue;
                }
                if (CourseEventStraight[i].Values[CourseParam.STRAIGHT_VALUE_INDEX_IS_LAST] == 1)
                {
                    IsExistLastStraightEvent = true;
                    return;
                }
            }

            // 対応漏れの可能性があるのでワーニングを出す
            Debug.LogWarning("最終直線が設定されているコースイベントが見つかりませんでした\n既存の最終直線判定を行います");
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// コースイベント単体を管理リストに登録。
        /// </summary>
        //---------------------------------------------------------------
        private void _AddCourseEvent(CourseParam courseEvent)
        {
            var courceEv = CourseEventList.Find( item => item == courseEvent );
            if( courceEv == null )
            {
                CourseEventList.Add(courseEvent);
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 最初のコーナーまでの距離計算。
        /// </summary>
        //---------------------------------------------------------------
        public float CalcFirstCornerDistance()
        {
            if( null == CourseEventCorner || CourseEventCorner.Length == 0 )
            {
                return 0.0f;
            }
            var firstCorner = CourseEventCorner[0];
            if( null == firstCorner )
            {
                return 0.0f;
            }
            return firstCorner._distance;
        }
    };
};

namespace StandaloneSimulator
{
#if GALLOP
    // サウンド再生用
    //-------------------------------------------------------------------
    /// <summary>
    /// コースイベント管理。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class CourseEventParam
    {
        private const float SECOND_BGM_DISTANCE_NULL = -1;
        
        /// <summary>
        /// １曲目を停止する位置。
        /// </summary>
        public float FirstBgmStopDistance { get; private set; } = SECOND_BGM_DISTANCE_NULL;
        /// <summary>
        /// ２曲目を再生開始する位置。
        /// </summary>
        public float SecondBgmPlayDistance { get; private set; } = SECOND_BGM_DISTANCE_NULL;

        /// <summary>
        /// 歓声（RaceResidentCrowd）を強制発火させる位置
        /// レース展開によるRaceResidentCrowdController.CrowdModeの更新と、このDictに記載のある距離とで先に条件を達成した方が処理される
        /// </summary>
        public Dictionary<Gallop.RaceResidentCrowdController.CrowdMode, float /*Distance*/> RaceResidentCrowdDistanceDict { get; private set; } = new Dictionary<Gallop.RaceResidentCrowdController.CrowdMode, float>();

        /// <summary>
        /// GALLOP用InitCourseEvent関数
        /// </summary>
        /// <param name="courseParamTable"></param>
        /// <param name="bootMode"></param>
        public void InitCourseEvent(CourseParamTable courseParamTable, Gallop.RaceMainView.Mode bootMode)
        {
            InitCourseEvent(courseParamTable);

            CacheFirstBgmControl(bootMode);
            CacheSecondBgmControl(bootMode);

            CacheEventCrowdModeControl();
        }

        /// <summary>
        /// コースイベントによる１曲目停止位置をキャッシュ。
        /// </summary>
        private void CacheFirstBgmControl(Gallop.RaceMainView.Mode bootMode)
        {
            FirstBgmStopDistance = SECOND_BGM_DISTANCE_NULL;

            var targetparamType = Gallop.RaceUtil.GetRaceLandScapeSettingData(bootMode)
                ? CourseParamType.FirstBgmLandscape
                : CourseParamType.FirstBgm;
            var bgmStopPoint = CourseEventList.FirstOrDefault(e => e._paramType == targetparamType);
            if (bgmStopPoint == null)
            {
                return;
            }

            FirstBgmStopDistance = bgmStopPoint._distance;
        }
        
        /// <summary>
        /// コースイベントによる２曲目再生位置をキャッシュ。
        /// </summary>
        private void CacheSecondBgmControl(Gallop.RaceMainView.Mode bootMode)
        {
            SecondBgmPlayDistance = SECOND_BGM_DISTANCE_NULL;

            var targetparamType = Gallop.RaceUtil.GetRaceLandScapeSettingData(bootMode)
                ? CourseParamType.SecondBgmLandscape
                : CourseParamType.SecondBgm;
            var bgmPlayPoint = CourseEventList.FirstOrDefault(e => e._paramType == targetparamType);
            if (bgmPlayPoint == null)
            {
                return;
            }

            SecondBgmPlayDistance = bgmPlayPoint._distance;
        }
        
        /// <summary>
        /// 歓声強制開始距離をキャッシュ。
        /// </summary>
        private void CacheEventCrowdModeControl()
        {
            RaceResidentCrowdDistanceDict.Clear();

            var targetparamType = CourseParamType.CrowdMode;
            var crowdModePointList = CourseEventList.Where(e => e._paramType == targetparamType).ToList();
            var crowdModePointListCount = crowdModePointList.Count;
            if (crowdModePointListCount <= 0)
            {
                return;
            }

            for (int i = 0; i < crowdModePointListCount; i++)
            {
                var crowdModePoint = crowdModePointList[i];
                
                // 定義チェック
                var crowedModeValue = crowdModePoint._values[0];
                if (!Enum.IsDefined(typeof(Gallop.RaceResidentCrowdController.CrowdMode), crowedModeValue))
                {
                    Debug.LogError($"未定義のEnum値がCourseParamに設定されています: {crowedModeValue}");
                    return;
                }
            
                // 設定したいCrowdModeと距離をセットで保存
                RaceResidentCrowdDistanceDict.Add((Gallop.RaceResidentCrowdController.CrowdMode)crowedModeValue, crowdModePoint._distance);
            }
        }
    }
#endif
}
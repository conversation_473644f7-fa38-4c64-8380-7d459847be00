#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Numerics;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 競馬場レーンアニメーション管理。
    /// </summary>
    //-------------------------------------------------------------------
    public class CourseLane
    {
        ///////////////////////////////////////////////////////////////// 
        // 定数。
        ///////////////////////////////////////////////////////////////// 
        private const float GOAL_DIR_CALC_OFFSET = 10.0f;

        public static readonly Vector3 ROTATION_FIX_RIGHT = new Vector3(0.0f, 180.0f, 0);
        public static readonly Vector3 ROTATION_FIX_LEFT = RaceUtilMath.VECTOR3_ZERO;

        ///////////////////////////////////////////////////////////////// 
        // 変数。
        ///////////////////////////////////////////////////////////////// 
        private Gallop.CourseLaneAnim CourseLaneAnim { get; set; }

        private Gallop.CourseLaneAnim CourseOverRunLaneAnim { get; set; }
        public int OverRunDistance
        {
            get
            {
                if(CourseOverRunLaneAnim == null)
                {
                    Debug.LogWarning("オーバーランのコースパスが読み込まれる前にアクセスしています");
                    return 0;
                }
                return CourseOverRunLaneAnim.Distance;
            }
        }

        public Vector3 StartPosition { get { return _startPosition; } }
        public Quaternion StartRotation { get { return _startRotation; } }

        public bool CourseOverRunLaneEnable { get{ return null != CourseLaneAnim && null != CourseOverRunLaneAnim; } }

        private float[] _courceBankAngle = new float[0];

        private Vector3 _goalPos = RaceUtilMath.VECTOR3_ZERO;
        private Vector3 _goalDir = RaceUtilMath.VECTOR3_ZERO;
        private Quaternion _goalRot = RaceUtilMath.QUATERNION_IDENTITY;

        private Vector3 _rotationFix = RaceUtilMath.VECTOR3_ZERO;
        private Quaternion _rotationFixRot = RaceUtilMath.QUATERNION_IDENTITY;
        private Quaternion _invRotationFixRot = RaceUtilMath.QUATERNION_IDENTITY;

        private Vector3 _startPosition;
        private Quaternion _startRotation;

        public Quaternion InvRotationFix { get { return _invRotationFixRot; } }

        ///////////////////////////////////////////////////////////////// 
        // 関数。
        ///////////////////////////////////////////////////////////////// 
        //---------------------------------------------------------------
        /// <summary>
        /// 初期化。
        /// </summary>
        //---------------------------------------------------------------
        public void Init(bool isCourseRight, Gallop.CourseLaneAnim laneAnim, Gallop.CourseLaneAnim overRunAnim, int courseDistance)
        {
            _InitRotationFix( isCourseRight );
            _InitLane( laneAnim );
            _InitOverRunLane( overRunAnim );
            _InitGoal(courseDistance);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 解放。
        /// </summary>
        //---------------------------------------------------------------
        public void Exit()
        {
        }

        private void _InitRotationFix( bool isCourseRight )
        {
            // 右回りの時はyを180度反転させる。
            // これは右回りの時はX+が競馬場内側を向いている(Unityが左手座標系のため)のを外側へ向かせるための処理。
            // 但しこのコースパスのままキャラを走らせるとZ+が進行方向と逆を向くため、キャラの向きはRaceModelControllerで逆Quaternionで補正される。

            if ( isCourseRight )
            {
                _rotationFix = ROTATION_FIX_RIGHT;
            } 
            else
            {
                _rotationFix = ROTATION_FIX_LEFT;
            }

            _rotationFixRot = RaceUtilMath.QuaternionFromEuler( _rotationFix );
            _invRotationFixRot = Quaternion.Inverse(_rotationFixRot);
        }

        private void _InitLane(Gallop.CourseLaneAnim courseLaneAnim)
        {
            if (courseLaneAnim == null)
            {
                Debug.LogError("Laneアニメが存在しません");
                //Debug.LogError("Laneアニメが存在しません [" + laneName + "]");
                return;
            }

            //開始位置を控えておく(GateInなどで使用する)
            courseLaneAnim.Sample(out _startPosition, out _startRotation, 0.0f);
            CourseLaneAnim = courseLaneAnim;
        }

        private void _InitOverRunLane(Gallop.CourseLaneAnim courseOverRunLaneAnim)
        {
            if (courseOverRunLaneAnim == null)
            {
                Debug.LogError("RoundLaneアニメが存在しません");
                //Debug.LogError("RoundLaneアニメが存在しません [" + laneName + "]");
                return;
            }

            CourseOverRunLaneAnim = courseOverRunLaneAnim;
        }

        private void _InitGoal(int courseDistance)
        {
            Vector3 lanePosition;
            Quaternion laneRotation;

            // ゴール前10m地点
            GetLaneTransform( courseDistance - GOAL_DIR_CALC_OFFSET , out lanePosition , out laneRotation);

            // ゴール地点
            GetLaneTransform( courseDistance , out _goalPos , out _goalRot );

            // ゴール後1mあたりの移動ベクトル
            _goalDir = (_goalPos - lanePosition ) * 0.1f;
        }

        public void GetLaneTransform( float distance, out Vector3 position, out Quaternion rotation )
        {
            if(distance <= CourseLaneAnim.Distance)
            {
                // 現在の距離がコースパスの何フレーム目を参照するかを算出。
                float time = CourseLaneAnim.Distance2Time(distance);

                CourseLaneAnim.Sample(out position, out rotation, time);
                rotation = rotation * _rotationFixRot;
            }
            else
            {
                // ゴール後は周回用アニメデータを使用
                if( !CourseOverRunLaneEnable )
                {
                    // データが存在しない場合、ゴールした後はゴールの方向に走り続ける
                    float overDistance = distance - CourseLaneAnim.Distance;
                    position = _goalPos + _goalDir * overDistance;
                    rotation = _goalRot;
                    return;
                }

                // オーバーラン始まってからの距離を算出。
                float overRunDistance = distance - CourseLaneAnim.Distance;
                overRunDistance %= CourseOverRunLaneAnim.Distance;

                // 現在の距離がオーバーランコースパスの何フレーム目を参照するかを算出。
                float time = CourseOverRunLaneAnim.Distance2Time(overRunDistance);

                CourseOverRunLaneAnim.Sample(out position, out rotation, time);
                rotation = rotation * _rotationFixRot;
            }
        }
    };
};
#endif

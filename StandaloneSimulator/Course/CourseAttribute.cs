namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// コース上の属性。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ICourseAttribute
    {
        /// <summary>
        /// 開始距離。
        /// </summary>
        float StartDistance { get; }
        /// <summary>
        /// 終了距離。
        /// </summary>
        float EndDistance { get; }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// コース上の属性：直線。
    /// </summary>
    //-------------------------------------------------------------------
    public class CourseStraight : ICourseAttribute
    {
        public const int FRONT_TYPE_NUMBER_NULL = -1;
        
        /// <summary>
        /// 開始距離。
        /// </summary>
        public float StartDistance { get; set; }
        /// <summary>
        /// 終了距離。
        /// </summary>
        public float EndDistance { get; set; }
        /// <summary>
        /// 長さ。（終了距離 - 開始距離）
        /// </summary>
        public float Range { get; set; }
        
        public readonly Gallop.RaceDefine.StraightFrontType frontType;
        public readonly int FrontTypeNumber = FRONT_TYPE_NUMBER_NULL;

        /// <summary> 最終直線かどうか  </summary>
        private readonly bool _isLast = false;
        public bool IsLast => _isLast;

        public CourseStraight(float start, float end, Gallop.RaceDefine.StraightFrontType type, int typeNumber, bool isLast)
        {
            StartDistance = start;
            EndDistance = end;
            frontType = type;
            FrontTypeNumber = typeNumber;
            _isLast = isLast;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// コース上の属性：コーナー。
    /// </summary>
    //-------------------------------------------------------------------
    public class CourseCorner : ICourseAttribute
    {
        public float StartDistance { get; set; }
        public float EndDistance { get; private set; }
        public readonly int cornerNumber = 0;
        public bool IsFinalCorner { get; private set; }

        public CourseCorner(int number, float start, float end, bool isFinalCorner)
        {
            cornerNumber = number;
            StartDistance = start;
            EndDistance = end;
            IsFinalCorner = isFinalCorner;
            Debug.Assert( start < end, string.Format( "コーナー始点の方が終点よりも遠くに設定されています。start={0}, end={1}", start, end ) );
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// コース上の属性：坂。
    /// </summary>
    //-------------------------------------------------------------------
    public class CourseSlope : ICourseAttribute
    {
        public float StartDistance { get; set; }
        public float EndDistance { get; set; }
        public Gallop.RaceDefine.SlopeType SlopeType = Gallop.RaceDefine.SlopeType.Null;

        public CourseSlope(Gallop.RaceDefine.SlopeType type, float start, float end)
        {
            SlopeType = type;
            StartDistance = start;
            EndDistance = end;
            Debug.Assert( start < end, string.Format( "坂始点の方が終点よりも遠くに設定されています。start={0}, end={1}", start, end ) );
        }
    }
}

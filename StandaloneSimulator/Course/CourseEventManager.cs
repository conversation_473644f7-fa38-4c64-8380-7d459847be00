using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// コースイベントパラメータ管理。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class CourseEventManager
    {
        private CourseEventParam _eventParam = null;
        private float _firstCornerDistance = 0.0f;

        //---------------------------------------------------------------
        public void Init(CourseParamTable courseParamTable)
        {
            // コース上イベントパラメータ初期化。
            _eventParam = new CourseEventParam();
            _eventParam.InitCourseEvent(courseParamTable);
            _firstCornerDistance = _eventParam.CalcFirstCornerDistance();
        }

        //---------------------------------------------------------------
        public List<CourseParam> GetCourseEventList()
        { 
            return _eventParam.CourseEventList; 
        }
        
        //---------------------------------------------------------------
        public CourseParam[] GetCourseEventCorner()
        { 
            return _eventParam.CourseEventCorner; 
        }

        //---------------------------------------------------------------
        public CourseParam[] GetCourseEventStraight()
        { 
            return _eventParam.CourseEventStraight; 
        }

        //---------------------------------------------------------------
        public CourseParam[] GetCourseEventSlope()
        { 
            return _eventParam.CourseEventSlope; 
        }
        
        //---------------------------------------------------------------
        public float GetFirstCornerDistance()
        {
            return _firstCornerDistance;
        }

        //---------------------------------------------------------------
        public float FirstMoveLanePointDistance => _eventParam.FirstMoveLanePointDistance;

        //---------------------------------------------------------------
        public bool IsEnableFirstMoveLane => _eventParam.IsEnableFirstMoveLane;

        //---------------------------------------------------------------
        public bool IsFirstMoveLaneIsIn => _eventParam.IsEmptyInMove;
        
        //---------------------------------------------------------------
        public bool IsExistLastStraightEvent => _eventParam.IsExistLastStraightEvent;
    }
}

#if GALLOP
namespace StandaloneSimulator
{
    // サウンド再生用
    //-------------------------------------------------------------------
    /// <summary>
    /// コースイベントパラメータ管理。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class CourseEventManager
    {
        // GALLOP名前空間用
        public void Init(CourseParamTable courseParamTable, Gallop.RaceMainView.Mode bootMode)
        {
            // コース上イベントパラメータ初期化。
            _eventParam = new CourseEventParam();
            _eventParam.InitCourseEvent(courseParamTable, bootMode);
            _firstCornerDistance = _eventParam.CalcFirstCornerDistance();
        }
        
        //---------------------------------------------------------------
        public float FirstBgmStopDistance => _eventParam.FirstBgmStopDistance;
        public float SecondBgmPlayDistance => _eventParam.SecondBgmPlayDistance;

        public Dictionary<Gallop.RaceResidentCrowdController.CrowdMode, float /*Distance*/>
            RaceResidentCrowdDistanceDict => _eventParam.RaceResidentCrowdDistanceDict;
    }

}
#endif
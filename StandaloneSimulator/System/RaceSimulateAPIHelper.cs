#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;

namespace StandaloneSimulator
{
    public class RaceSimulateAPIHelper
    {
        /// <summary>
        /// プレイヤーのスタートダッシュ成功/失敗をサーバーに送る時の値定義。
        /// </summary>
        public enum StartDashState
        {
            None = 0, // 成功でも失敗でもない。
            BadStart, // 失敗。
            GoodStart, // 成功。
        }
        
        public class CharaResultDesc
        {
            /// <summary>後着との馬身差。サーバーへ送るためにfloatをBASHIN_INT2FLOAT_ACCURACY倍した値。</summary>
            public int BashinDiffFromBehind;
            /// <summary>１着との馬身差。サーバーへ送るためにfloatをBASHIN_INT2FLOAT_ACCURACY倍した値。</summary>
            public int BashinDiffFromTop;
            /// <summary>レアリティ別のスキル発動回数。</summary>
            public int[] SkillActivateCountByRarityArray;
            /// <summary>興奮したかどうか。1 or 0</summary>
            public int IsExcitement;
            /// <summary>レース開始後一定距離からトップでゴールしたか。1 or 0</summary>
            public int IsRunningAlone;
            /// <summary>最終直線開始時の順位。1~</summary>
            public int LastStraightLineRank;
            /// <summary>スタートダッシュの結果。</summary>
            public int StartDashState;
        }
        
        public static CharaResultDesc CreateSingleModeCharaResultDesc(IHorseRaceInfoSimulate horse, IHorseRaceInfoSimulate[] allHorseArray)
        {
            // 後着のキャラとの着タイム差分を取得。
            float finishDiffTimeBehind = GetFinishDiffTimeBehind(
                horse.FinishOrder, 
                allHorseArray);
            float diffDistance = RaceUtil.DiffTime2Distance(finishDiffTimeBehind);
            float diffBashin = RaceUtil.Distance2BashinFloat(diffDistance);

            // １位のキャラとの着タイム差分を取得。
            float finishDiffTimeTop = GetFinishDiffTimeTop(
                horse.FinishOrder, 
                horse.FinishTimeScaled, 
                allHorseArray);
            float diffDistanceFromTop = RaceUtil.DiffTime2Distance(finishDiffTimeTop);
            float diffBashinFromTop = RaceUtil.Distance2BashinFloat(diffDistanceFromTop);

            // レース開始後一定距離からトップでゴールしたか。
            bool isRunningAlone = RaceUtil.CheckRunningAloneSimulate(horse);

        #if CYG_DEBUG
            if (RaceSimulateDebugger.ForceBashinDiffBehind > 0)
            {
                diffBashin = RaceSimulateDebugger.ForceBashinDiffBehind;
            }
            if (RaceSimulateDebugger.ForceBashinDiffFromTop > 0)
            {
                diffBashinFromTop = RaceSimulateDebugger.ForceBashinDiffFromTop;
            }

            if (RaceSimulateDebugger.FixBashinDiffBehindDic.TryGetValue(horse.HorseIndex, out float fixDiffBehind))
            {
                diffBashin = fixDiffBehind;
            }
            if (RaceSimulateDebugger.FixBashinDiffFromTopDic.TryGetValue(horse.HorseIndex, out float fixDiffFromTop))
            {
                diffBashinFromTop = fixDiffFromTop;
            }
        #endif
            
            var retDesc = new CharaResultDesc()
            {
                BashinDiffFromBehind = RaceUtil.BashinFloat2Int(diffBashin),
                BashinDiffFromTop = RaceUtil.BashinFloat2Int(diffBashinFromTop),
                SkillActivateCountByRarityArray = horse.GetActivateSkillCountByRarityArray(),
                IsExcitement = horse.TemptationCount > 0 ? 1 : 0, // 興奮したかどうかを0/1で返す。
                IsRunningAlone = isRunningAlone ? 1 : 0, // レース開始後○○ｍ以降ずっと１位でそのままゴールしたかを0/1で返す。
                LastStraightLineRank = horse.LastStraightOrder + 1, // サーバーへ渡すのは1~の値で。
                StartDashState = (int)GetStartDashState(horse),
            };
            
            return retDesc;
        }
        
        /// <summary>
        /// キャラのスタートダッシュ状態をサーバーに送る整数値に変換。
        /// </summary>
        private static StartDashState GetStartDashState(IHorseRaceInfoSimulate horse)
        {
            if (horse.IsGoodStart)
            {
                return StartDashState.GoodStart;
            }
            else if (horse.IsBadStart)
            {
                return StartDashState.BadStart;
            }
            else
            {
                return StartDashState.None;
            }
        }
        
        /// <summary>
        /// 後着のキャラとの着タイム差を取得。
        /// </summary>
        /// <param name="selfFinishOrder">着順。0~</param>
        /// <returns>符号無し。自分が最後尾なら0。</returns>
        private static float GetFinishDiffTimeBehind(int selfFinishOrder, IHorseRaceInfoSimulate[] allHorseArray)
        {
            // 自分が最後尾なら0を返却。
            if (selfFinishOrder >= allHorseArray.Length - 1)
            {
                return 0;
            }

            // まず自分の後着のキャラを取得。
            int behindFinishOrder = selfFinishOrder + 1;
            var behindHorse = allHorseArray.FirstOrDefault(h => h.FinishOrder == behindFinishOrder);
            if (behindHorse == null)
            {
                return 0;
            }
            
            // そいつの前のキャラとの着タイム差分が、求める値である。
            return behindHorse.FinishTimeDiffFromPrevHorse;
        }

        
        /// <summary>
        /// １着のキャラとの着タイム差を取得。
        /// </summary>
        /// <param name="selfFinishOrder">着順。0~</param>
        /// <param name="selfFinishTime">着タイム。</param>
        /// <returns>符号無し。自分が１着なら0。</returns>
        private static float GetFinishDiffTimeTop(int selfFinishOrder, float selfFinishTime, IHorseRaceInfoSimulate[] allHorseArray)
        {
            // 自分が１着なら0を返却。
            if (selfFinishOrder == 0)
            {
                return 0;
            }

            // まず１着のキャラを取得。
            var topHorse = allHorseArray.FirstOrDefault(h => h.FinishOrder == 0);
            if (topHorse == null)
            {
                return 0;
            }
            
            // そいつとの着タイム差分が、求める値である。
            float retDiff = selfFinishTime - topHorse.FinishTimeScaled;
            Debug.Assert(retDiff >= 0);
            return retDiff;
        }
    }
}
#endif

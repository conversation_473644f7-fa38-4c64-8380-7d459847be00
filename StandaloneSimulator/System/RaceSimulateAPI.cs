#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using Gallop;
using MessagePack;

namespace StandaloneSimulator
{
    /*
     * ここに定義するシリアライズの順番を変更するときは、以下の書類も更新すること。
     * https://xxxxxxxxxx/pages/viewpage.action?pageId=121814068
     */

    //-------------------------------------------------------------------
    /// <summary>
    /// レース計算リクエスト。
    /// </summary>
    /// <remarks>
    /// ・シミュレーターサーバー→スタンドアローンレースシミュレーターへの計算リクエストに使用する。
    /// ・変数定義とシリアライズの順は一致させること。（可読性のため）
    /// </remarks>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class RaceSimulateRequest
    {
        public string resource_ver; // シミュレートに使用するリソースバージョン。00000000形式の８桁。
        public uint unlock_flags; // 機能時限解放用ビットフラグ。
        public int race_instance_id; // race_instance.csvのid。               
        public int random_seed; // 乱数シード。
        public int season; // 季節。GameDefine.BgSeason
        public int weather; // 天候。RaceDefine.Weather
        public int ground_condition; // 馬場状態。RaceDefine.GroundCondition
        public int race_horse_num; // RaceHorseDataArrayの要素数。
        public RaceHorseSimulateData[] race_horse_data_array; // 出走するキャラ。
        public int race_type; // シミュレートするレース種類。RaceDefine.RaceType
        public int self_evaluate; // チーム競技場の自チームの評価値。
        public int opponent_evaluate; // チーム競技場の対戦相手評価値。
        public int score_calc_team_id; // スコア計算を行うTeamId。
        public int support_card_bonus; // サポートカードによるボーナス。
        public int challenge_match_difficulty; // チャレンジマッチの難易度。
        public CourseSetInfoShared[] course_set_info_array; // 【先バレ防止用】上書き用CourseSetInfo
#if CYG_DEBUG
        public StandAloneSimulateDebugParam debug_param;
    #endif

        public void DeserializePostProcess()
        {
            foreach (var horse in race_horse_data_array)
            {
                horse.DeserializePostProcess();
            }
        #if CYG_DEBUG
            if (debug_param == null)
            {
                return;
            }
            // サーバーからはfloat値で渡されるが値的にはintとしての大きさなのでint → floatへの変換と同じことをやる
            var fixBashinDiffBehindArray = debug_param.fix_bashin_diff_behind_array;
            if (fixBashinDiffBehindArray != null)
            {
                for (int i = 0; i < fixBashinDiffBehindArray.Length; ++i)
                {
                    fixBashinDiffBehindArray[i].bashin_diff_behind =
                        RaceUtil.BashinServerVal2ClientVal(fixBashinDiffBehindArray[i].bashin_diff_behind);
                }
            }

            var fixBashinDiffFromTopArray = debug_param.fix_bashin_diff_from_top_array;
            if (fixBashinDiffFromTopArray != null)
            {
                for (int i = 0; i < fixBashinDiffFromTopArray.Length; ++i)
                {
                    fixBashinDiffFromTopArray[i].bashin_diff_from_top =
                        RaceUtil.BashinServerVal2ClientVal(fixBashinDiffFromTopArray[i].bashin_diff_from_top);
                }
            }
        #endif
        }
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// リクエストに使用するキャラ１体を表すモデル。
    /// </summary>
    /// <remarks>
    /// ・シミュレーターサーバー→スタンドアローンレースシミュレーターへの計算リクエストに使用する。
    /// ・変数定義とシリアライズの順は一致させること。（可読性のため）
    /// ・パラメータの内容はRaceHorseDataに準拠する。
    /// </remarks>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    [Serializable]
    public class RaceHorseSimulateData
    {
        // ※変数を追加したら、ToRaceHorseData()にも処理を追加すること。
        public long viewer_id;
        public int single_mode_chara_id;
        public int card_id;
        public int chara_id;
        public int running_style;
        public int skill_num;
        public RaceHorseSkillData[] skill_array;
        public int speed;
        public int stamina;
        public int pow;
        public int guts;
        public int wiz;
        public int final_grade;
        public int popularity;
        public int[] popularity_mark_rank_array;
        public int proper_distance_short;
        public int proper_distance_mile;
        public int proper_distance_middle;
        public int proper_distance_long;
        public int proper_running_style_nige;
        public int proper_running_style_senko;
        public int proper_running_style_sashi;
        public int proper_running_style_oikomi;
        public int proper_ground_turf;
        public int proper_ground_dirt;
        public int motivation;
        public int frame_order;
        public int team_id;
        public int team_member_id;
        public int item_num; 
        public int[] item_id_array;
        public int team_rank;
        public int single_mode_win_count;
        public int fan_count;
        public int scenario_data_num;
        public RaceHorseScenarioData[] scenario_data_array;
    #if GALLOP
        public int mob_id;
        public int npc_type;
    #endif
    #if CYG_DEBUG && UNITY_EDITOR
        [IgnoreMember]
        // 【デバッグ用】枠番ランダムにおけるソート基準index
        // MessagePackには含めない
        public int dbg_base_horse_index;
    #endif

        public RaceHorseSimulateData()
        {
        }
    #if GALLOP
        public RaceHorseSimulateData(Gallop.RaceHorseData raceHorseData)
        {
            viewer_id = raceHorseData.viewer_id;
            single_mode_chara_id = raceHorseData.single_mode_chara_id;
            card_id = raceHorseData.card_id;
            chara_id = raceHorseData.chara_id;
            running_style = raceHorseData.running_style;
            speed = raceHorseData.speed;
            stamina = raceHorseData.stamina;
            pow = raceHorseData.pow;
            guts = raceHorseData.guts;
            wiz = raceHorseData.wiz;
            final_grade = raceHorseData.final_grade;

            skill_num = raceHorseData.skill_array.Length;
            skill_array = raceHorseData.skill_array.Select(x => new RaceHorseSkillData(x)).ToArray();

            // 人気は0~で保持したいので-1する。
            popularity = raceHorseData.popularity - 1;
            popularity_mark_rank_array = raceHorseData.popularity_mark_rank_array.Select(popularity => popularity - 1).ToArray();

            proper_distance_short = raceHorseData.proper_distance_short;
            proper_distance_mile = raceHorseData.proper_distance_mile;
            proper_distance_middle = raceHorseData.proper_distance_middle;
            proper_distance_long = raceHorseData.proper_distance_long;
            proper_running_style_nige = raceHorseData.proper_running_style_nige;
            proper_running_style_senko = raceHorseData.proper_running_style_senko;
            proper_running_style_sashi = raceHorseData.proper_running_style_sashi;
            proper_running_style_oikomi = raceHorseData.proper_running_style_oikomi;
            proper_ground_turf = raceHorseData.proper_ground_turf;
            proper_ground_dirt = raceHorseData.proper_ground_dirt;
            motivation = raceHorseData.motivation;
            frame_order = raceHorseData.frame_order;
            team_id = raceHorseData.team_id;
            team_member_id = raceHorseData.team_member_id;
            item_id_array = new int[raceHorseData.item_id_array.Length];
            Array.Copy(raceHorseData.item_id_array, item_id_array, item_id_array.Length);
            mob_id = raceHorseData.mob_id;
            npc_type = raceHorseData.npc_type;
            team_rank = raceHorseData.team_rank;
            single_mode_win_count = raceHorseData.single_mode_win_count;
            fan_count = raceHorseData.fan_count;
            scenario_data_num = raceHorseData.scenario_data_array?.Length ?? 0;     // scenario_data_arrayはnullが有り得ることに注意
            scenario_data_array = raceHorseData.scenario_data_array?.Select(x => new RaceHorseScenarioData(x)).ToArray();
        }
    #endif

        public void DeserializePostProcess()
        {
            // 人気は0~で保持したいので-1する。
            popularity--;
            Debug.Assert(popularity >= 0, $"Popularityが0を下回っている {popularity}");

            for (int i = 0; i < popularity_mark_rank_array.Length; ++i)
            {
                popularity_mark_rank_array[i]--;
                Debug.Assert(popularity_mark_rank_array[i] >= 0, $"PopularityMarkRankArray[i]が0を下回っている {popularity_mark_rank_array[i]}");
            }
        }

        //---------------------------------------------------------------
        #if GALLOP
        public Gallop.RaceHorseData ToRaceHorseData()
        {
            var horseData = new Gallop.RaceHorseData();
            horseData.viewer_id = viewer_id;
            horseData.single_mode_chara_id = single_mode_chara_id;
            horseData.card_id = card_id;
            horseData.chara_id = chara_id;
            horseData.running_style = running_style;
            horseData.speed = speed;
            horseData.stamina = stamina;
            horseData.pow = pow;
            horseData.guts = guts;
            horseData.wiz = wiz;
            horseData.final_grade = final_grade;

            horseData.skill_array = new Gallop.SkillData[skill_array.Length];
            for (int i = 0; i < horseData.skill_array.Length; i++)
            {
                horseData.skill_array[i] = new SkillData()
                {
                    skill_id = skill_array[i].skill_id,
                    level = skill_array[i].level,
                };
            }

            horseData.popularity = popularity;
            horseData.popularity_mark_rank_array = new int[popularity_mark_rank_array.Length];
            Array.Copy(popularity_mark_rank_array, horseData.popularity_mark_rank_array, popularity_mark_rank_array.Length);
            horseData.proper_distance_short = proper_distance_short;
            horseData.proper_distance_mile = proper_distance_mile;
            horseData.proper_distance_middle = proper_distance_middle;
            horseData.proper_distance_long = proper_distance_long;
            horseData.proper_running_style_nige = proper_running_style_nige;
            horseData.proper_running_style_senko = proper_running_style_senko;
            horseData.proper_running_style_sashi = proper_running_style_sashi;
            horseData.proper_running_style_oikomi = proper_running_style_oikomi;
            horseData.proper_ground_turf = proper_ground_turf;
            horseData.proper_ground_dirt = proper_ground_dirt;
            horseData.motivation = motivation;
            horseData.frame_order = frame_order;
            horseData.team_id = team_id;
            horseData.team_member_id = team_member_id;
            horseData.item_id_array = new int[item_id_array.Length];
            Array.Copy(item_id_array, horseData.item_id_array, item_id_array.Length);
            horseData.team_rank = team_rank;
            horseData.single_mode_win_count = single_mode_win_count;
            horseData.fan_count = fan_count;
            horseData.scenario_data_array = new ScenarioData[scenario_data_array.Length];
            for (int i = 0; i < horseData.scenario_data_array.Length; i++)
            {
                horseData.scenario_data_array[i] = new ScenarioData()
                {
                    type = scenario_data_array[i].type,
                    value = scenario_data_array[i].value,
                };
            }
 
            // シミュレートには使用しないが、nullを許容しないつくりにしてあるためサイズ0の配列を入れておく。
            horseData.win_saddle_id_array = new int[0];
            horseData.race_result_array = new Gallop.RaceHorseDataRaceResult[0];
            
            return horseData;
        }
        #endif
    }

    [MessagePackObject(true)]
    public class RaceHorseSkillData
    {
        public int skill_id;
        public int level;
        
        public RaceHorseSkillData()
        {
        }
    #if GALLOP
        public RaceHorseSkillData(Gallop.SkillData skillData)
        {
            skill_id = skillData.skill_id;
            level = skillData.level;
        }
    #endif
    }
    
    [MessagePackObject(true)]
    public class RaceHorseScenarioData
    {
        public int type;
        public int value;
        
        public RaceHorseScenarioData()
        {
        }
#if GALLOP
        public RaceHorseScenarioData(Gallop.ScenarioData scenarioData)
        {
            type = scenarioData.type;
            value = scenarioData.value;
        }
#endif
    }

#if CYG_DEBUG
    //-------------------------------------------------------------------
    /// <summary>
    /// シミュレートリクエストに付与されてくるデバッグ情報(.NET用)
    /// </summary>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class StandAloneSimulateDebugParam
    {
        /// <summary>
        /// 着順固定情報。
        /// </summary>
        [MessagePackObject(true)]
        public class FixFinishOrder
        {
            public int frame_order; // この馬番のキャラを、
            public int finish_order; // この着順に固定する。1~。
        }

        /// <summary>
        /// 後着との馬身差固定情報。
        /// </summary>
        [MessagePackObject(true)]
        public class FixBashinDiffBehind
        {
            public int frame_order; // この馬番のキャラの
            public float bashin_diff_behind; // 後着との馬身差をこの値に固定する。
        }

        /// <summary>
        /// １着との馬身差固定情報。
        /// </summary>
        [MessagePackObject(true)]
        public class FixBashinDiffFromTop
        {
            public int frame_order; // この馬番のキャラの
            public float bashin_diff_from_top; // １着との馬身差をこの値に固定する。
        }

        public FixFinishOrder[] debug_result_rank = new FixFinishOrder[0];
        public FixBashinDiffBehind[] fix_bashin_diff_behind_array = new FixBashinDiffBehind[0];
        public FixBashinDiffFromTop[] fix_bashin_diff_from_top_array = new FixBashinDiffFromTop[0];
        public int is_record_interval_simulate = 0;

    }
#endif

    //-------------------------------------------------------------------
    /// <summary>
    /// レース計算レスポンス。
    /// </summary>
    /// <remarks>
    /// ・スタンドアローンシミュレーター→シミュレーターサーバーへの計算結果レスポンスに使用する。
    /// ・変数定義とシリアライズの順は一致させること。（可読性のため）
    /// </remarks>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class RaceSimulateResponse
    {
        public int race_scenario_version { get; set; }
        public int race_scenario_byte { get; set; }                    // RaceScenarioCompressedのバイト数。
        public byte[] race_scenario_compressed { get; set; }           // GZip圧縮済みbyte配列。
        public int result_horse_num { get; set; }                      // ResultHorseArrayの配列サイズ。
        public RaceResultHorseData[] result_horse_array { get; set; }  // キャラ数分のレース結果。
        public int result_team_num { get; set; } // ResultTeamに返却用の値が入っているなら1、そうでないなら0。
        public TeamResult result_team { get; set; } // チームのレース結果。
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// レスポンスに使用するキャラ１体を表すモデル。
    /// </summary>
    /// <remarks>
    /// ・スタンドアローンシミュレーター→シミュレーターサーバーへの計算結果レスポンスに使用する。
    /// ・変数定義とシリアライズの順は一致させること。（可読性のため）
    /// </remarks>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class RaceResultHorseData
    {
        public long viewer_id { get; set; }
        public int single_mode_chara_id { get; set; }
        public int finish_order { get; set; }         // 着順。1~。
        public int time { get; set; }          // 着タイム。秒を10,000倍した数値。
        public int finish_time_raw { get; set; }       // 着タイムの実時間。秒を10,000倍した数値。

        public HorseScore score { get; set; } // このキャラがこのレースで獲得したスコア。

        public int challenge_match_point_total { get; set; } // このキャラがチャレンジマッチで獲得したポイント合計。ボーナス込みの値。
        public int challenge_match_point_bonus_total { get; set; } // このキャラがチャレンジマッチで獲得したボーナス合計。
        public ChallengeMatchHorsePoint challenge_match_point { get; set; } // このキャラがチャレンジマッチで獲得したポイントの内訳。

        public int bashin_diff_from_behind { get; set; }
        public int bashin_diff_from_top { get; set; }
        public int[] skill_activate_count_array { get; set; }
        public int is_excitement { get; set; }
        public int is_running_alone { get; set; }
        public int last_straight_line_rank { get; set; }
        public int start_dash_state { get; set; }

#if STANDALONE_SIMULATOR
        public RaceResultHorseData() { }
        public RaceResultHorseData(IHorseRaceInfoSimulate info, IHorseRaceInfoSimulate[] allHorseArray)
        {
            viewer_id = info.ViewerId;
            single_mode_chara_id = info.SingleModeCharaId;
            finish_order = info.FinishOrder + 1; // 順位は0~なので+1して渡す。
            time = RaceUtil.FinishTimeFloat2Int(info.FinishTimeScaled);
            finish_time_raw = RaceUtil.FinishTimeFloat2Int(info.FinishTimeRaw);

            {
                var scoreList = info.ScoreList;
                score = new HorseScore();
                score.score_array = new ScoreData[scoreList.Count];
                int scoreIndex = 0;
                foreach (var scoreByType in scoreList)
                {
                    score.score_array[scoreIndex] = new ScoreData(
                        scoreByType.raw_score_id, 
                        scoreByType.num,
                        scoreByType.score,
                        scoreByType.bonus_array);
                    scoreIndex++;
                }
            }
            
            {
                var challengeMatchPointList = info.ChallengeMatchPointList;
                challenge_match_point = new ChallengeMatchHorsePoint();
                challenge_match_point.challenge_match_point_array = new ChallengeMatchPointData[challengeMatchPointList.Count];
                for (int i = 0; i < challenge_match_point.challenge_match_point_array.Length; i++)
                {
                    var from = challengeMatchPointList[i];
                    challenge_match_point.challenge_match_point_array[i] = new ChallengeMatchPointData(
                        from.raw_point_id,
                        from.num,
                        from.point,
                        from.point_bonus);
                }
                challenge_match_point_total = challenge_match_point.challenge_match_point_array.Sum(s => s.point);
                challenge_match_point_bonus_total = challenge_match_point.challenge_match_point_array.Sum(s => s.point_bonus);
            }

            var singleResultDesc = RaceSimulateAPIHelper.CreateSingleModeCharaResultDesc(info, allHorseArray);
            bashin_diff_from_behind = singleResultDesc.BashinDiffFromBehind;
            bashin_diff_from_top = singleResultDesc.BashinDiffFromTop;
            skill_activate_count_array = singleResultDesc.SkillActivateCountByRarityArray;
            is_excitement = singleResultDesc.IsExcitement;
            is_running_alone = singleResultDesc.IsRunningAlone;
            last_straight_line_rank = singleResultDesc.LastStraightLineRank;
            start_dash_state = singleResultDesc.StartDashState;
        }
#endif
    }
}
#endif
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ボーダータイム計算機。
    /// </summary>
    //-------------------------------------------------------------------
    public static class RaceBorderTimeCalculator
    {
        public static float CalcBorderTime(int courseDistance, float endAfterPhaseDistance, float baseSpeed, float lastSpurtSpeed)
        {
            // 終盤以前の距離を計算。
            float endBeforePhaseDistance = courseDistance - endAfterPhaseDistance;
            Debug.Assert(endBeforePhaseDistance > 0);

            // 終盤以前はBaseSpeedで走る時間、終盤以降はラストスパート想定速度で走る時間をそれぞれ計算。
            float endBeforeBorderTime = endBeforePhaseDistance / baseSpeed;
            float endAfterBorderTime = endAfterPhaseDistance / lastSpurtSpeed;

            // これを足したものがレース全体のボーダータイムとなる。
            return endBeforeBorderTime + endAfterBorderTime;
        }
    }
}

using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using MessagePack;

namespace StandaloneSimulator
{
    /*
     * ここに定義するシリアライズの順番を変更するときは、以下の書類も更新すること。
     * https://xxxxxxxxxx/pages/viewpage.action?pageId=121814068
     */

    //-------------------------------------------------------------------
    /// <summary>
    /// チーム競技場：ボーナスによって加算されているスコア。
    /// </summary>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class ScoreBonusData
    {
        public int score_bonus_id { get; set; }
        public int bonus_score { get; set; }

#if GALLOP
        public string BonusName => Gallop.TextUtil.GetMasterText(Gallop.MasterString.Category.TeamStadiumScoreBonusName, score_bonus_id);
        
        public ScoreBonusData(Gallop.TeamStadiumBonusData response) : this(response.score_bonus_id, response.bonus_score)
        {
        }
#endif
        public ScoreBonusData(ScoreBonusData from) : this(from.score_bonus_id, from.bonus_score)
        {
        }
        public ScoreBonusData(int score_bonus_id, int bonus_score)
        {
            this.score_bonus_id = score_bonus_id;
            this.bonus_score = bonus_score;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// チーム競技場：獲得したスコア１件当たりのデータ。
    /// </summary>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class ScoreData
    {
        public int raw_score_id { get; set; } // team_stadium_raw_score.csvのidです。
        public int num { get; set; } // このスコアを獲得した回数。
        public int score { get; set; } // 獲得したスコア。
        public int bonus_num { get; set; } // bonus_arrayの配列長
        public ScoreBonusData[] bonus_array { get; set; } = new ScoreBonusData[0];

    #if GALLOP
        public ScoreData(Gallop.TeamStadiumScoreData score) 
            : this(
                score.raw_score_id, 
                score.num, 
                score.score,
                score.bonus_array)
        {
        }
        private ScoreData(int raw_score_id, int num, int score, Gallop.TeamStadiumBonusData[] bonus_array)
        {
            this.raw_score_id = raw_score_id;
            this.score = score;
            this.num = num;
            this.bonus_num = bonus_array.Length;
            this.bonus_array = new ScoreBonusData[this.bonus_num];
            for (int i = 0; i < this.bonus_num; i++)
            {
                this.bonus_array[i] = new ScoreBonusData(bonus_array[i]);
            }
        }
    #endif
        public ScoreData(int raw_score_id) : this(raw_score_id, 0, 0, new ScoreBonusData[]{})
        {
        }
        public ScoreData(int raw_score_id, int num, int score, ScoreBonusData[] bonus_array)
        {
            this.raw_score_id = raw_score_id;
            this.score = score;
            this.num = num;
            this.bonus_num = bonus_array.Length;
            this.bonus_array = bonus_array.ToArray();
        }

        public void Add(int score)
        {
            this.score += score;
            num++;
        }
        
        public void AddArray(ScoreBonusData bonusData)
        {
            bonus_num++;
            var tempList = bonus_array.ToList();
            tempList.Add(bonusData);
            bonus_array = tempList.ToArray();
        }
        
        public int ApplyBonus(int rawScore)
        {
            int score = rawScore + SumAllBonus();
            return score;
        }

        private int SumAllBonus()
        {
            return bonus_array.Sum(b => b.bonus_score);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// チーム競技場：１キャラがこのレースで獲得したスコア。
    /// </summary>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class HorseScore
    {
        public ScoreData[] score_array { get; set; } // 獲得したスコア。
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// チーム競技場：チームの成績。チームごとに必要。
    /// </summary>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class TeamResult
    {
        public int team_id { get; set; }
        public int team_score_num { get; set; } // TeamScoreArrayの要素数。
        public ScoreData[] team_score_array { get; set; } // このチームとして獲得したスコア。
        public int team_total_score { get; set; } // このチームが獲得した合計スコア。（全キャラのHorseScore.ScoreArray合計 + TeamResult.TeamScoreArray合計に等しいが、一応別で送る）
    }
}
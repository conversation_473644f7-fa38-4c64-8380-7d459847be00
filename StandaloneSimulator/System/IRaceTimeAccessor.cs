namespace StandaloneSimulator
{
    /// <summary>
    /// レース中の時間アクセッサーインターフェース。
    /// </summary>
    public interface IRaceTimeAccessor
    {
        /// <summary>
        /// 出走してからの経過時間。
        /// </summary>
        float AccumulateTimeSinceStart { get; }

        /// <summary> 現在の経過フレーム数 </summary>
        int CurrentFrame { get; }
#if GALLOP
        // レース再生用
        
        /// <summary>
        /// レース中のdelta時間。
        /// </summary>
        /// <return>スキルカットイン中は0が返る。</return>
        float ElapsedTime { get; }

        /// <summary>
        /// レース中のdelta時間のスケール。
        /// </summary>
        float MainDeltaTimeScale { get; }

        /// <summary>
        /// スキルカットイン中のdelta時間。
        /// </summary>
        float SkillCutInElapsedTime { get; }
#endif
    }
}

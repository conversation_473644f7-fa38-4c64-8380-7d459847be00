using System;

namespace StandaloneSimulator
{
    /// <summary>
    /// レース用乱数生成器。
    /// </summary>
    public class RaceRandom : IRaceRandomGenerator
    {
        private System.Random _random;

        public RaceRandom()
        {
            _random = new System.Random();
        }

        public RaceRandom( int seed )
        {
            _random = new System.Random( seed );
        }

        public int GetRandom( int min, int max )
        {
            if( null == _random )
            {
                Debug.LogError( "乱数生成器が初期化されていません。" );
                return 0;
            }
            return _random.Next( min, max );
        }

        public int GetRandom( int max )
        {
            return GetRandom( 0, max );
        }

        public float GetRandom( float min, float max )
        {
            if( null == _random )
            {
                Debug.LogError( "乱数生成器が初期化されていません。" );
                return 0;
            }
            float random = (float)_random.NextDouble();
            float randomWithOffset = random * Math.Abs( max - min ) + min;
            // 四則演算によってmin/maxから僅かに外れる可能性があるので、一応clamp。
            return RaceUtilMath.Clamp( randomWithOffset, min, max );
        }

        public float GetRandom( float max )
        {
            return GetRandom( 0.0f, max );
        }

    }
}

using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レースシミュレート中に発生したイベントを登録するインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IRaceEventRecorder
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// スキル発動イベント登録。
        /// </summary>
        /// <param name="horseRaceInfo">発動したキャラ。</param>
        /// <param name="skillId">発動したスキルId。</param>
        /// <param name="detailIndex">スキルの中で効果が発現したdetailIndex。</param>
        /// <param name="targetFlags">スキルが効果を及ぼしたHorseIndexのビットフラグ。</param>
        /// <param name="curTime">発動した時間。（レース開始からの経過時間）</param>
        /// <param name="continueTimeInt">効果継続時間。</param>
        void AddSkillEvent(
            IHorseRaceInfoSimulate horseRaceInfo,
            int skillID,
            int detailIndex,
            int targetFlags,
            float curTime,
            int continueTimeInt,
            Gallop.SkillDefine.ActivateType activateType
        );
#endif 
#if GALLOP
        /// <summary>
        /// スキル発動イベント登録。
        /// </summary>
        /// <param name="horseRaceInfo">発動したキャラ。</param>
        /// <param name="skillID">発動したスキルId。</param>
        /// <param name="detailIndex">スキルの中で効果が発現したdetailIndex。</param>
        /// <param name="targetFlags">スキルが効果を及ぼしたHorseIndexのビットフラグ。</param>
        /// <param name="curTime">発動した時間。（レース開始からの経過時間）</param>
        /// <param name="continueTimeInt">効果継続時間。</param>
        /// <param name="activateType">
        /// スキル発動タイプ
        /// スキル効果によってはスキルプレートなどは表示したいけど固有スキル発動演出は再生したくないというパターンがあるので渡している
        /// </param>
        void AddSkillEvent(
            Gallop.IHorseRaceInfo horseRaceInfo,
            int skillID,
            int detailIndex,
            int targetFlags,
            float curTime,
            int continueTimeInt,
            Gallop.SkillDefine.ActivateType activateType
        );
#endif

        /// <summary>
        /// 競技場レース展開スコア獲得イベント登録。
        /// </summary>
        /// <param name="horseIndex">スコア獲得したキャラ。</param>
        /// <param name="rawScoreId">team_stadium_raw_score.csvのid。</param>
        /// <param name="addScore">獲得したスコア。</param>
        /// <param name="curTime">獲得した時間。（レース開始からの経過時間）</param>
        void AddScoreEvent(int horseIndex, int rawScoreId, int addScore, float curTime);
        
        /// <summary>
        /// チャレンジマッチポイント獲得イベント登録。
        /// </summary>
        /// <param name="horseIndex">スコア獲得したキャラ。</param>
        /// <param name="rawScoreId">challenge_match_raw_score.csvのid。</param>
        /// <param name="addScore">獲得したスコア。</param>
        /// <param name="curTime">獲得した時間。（レース開始からの経過時間）</param>
        void AddChallengeMatchPointEvent(int horseIndex, int rawScoreId, int addScore, float curTime);

        /// <summary>
        /// 競り合い（ハナの奪い合い）イベント登録。
        /// </summary>
        void AddCompeteTopEvent(int horseIndex, float curTime);
        
        /// <summary>
        /// 競り合い（叩き合い）イベント登録。
        /// </summary>
        void AddCompeteFightEvent(int horseIndex, float curTime);
        
        /// <summary>
        /// 足溜め解放イベント登録
        /// </summary>
        /// <param name="horseIndex"></param>
        /// <param name="curTime"></param>
        void AddReleaseConservePowerEvent(int horseIndex, float curTime);
        
        /// <summary>
        /// 上限突破パラメータスタミナイベント登録
        /// </summary>
        /// <param name="horseIndex"></param>
        /// <param name="curTime"></param>
        void AddStaminaLimitBreakBuffEvent(int horseIndex, float curTime);
        
        /// <summary>
        /// スパート前位置取り勝負イベント登録
        /// </summary>
        void AddCompeteBeforeSpurtEvent(int horseIndex, float curTime);
        
        /// <summary>
        /// 持久力温存状態イベント登録
        /// </summary>
        void AddStaminaKeepEvent(int horseIndex, float curTime);
        
        /// <summary>
        /// リード確保イベント登録
        /// </summary>
        void AddSecureLeadEvent(int horseIndex, float curTime);
    }
}

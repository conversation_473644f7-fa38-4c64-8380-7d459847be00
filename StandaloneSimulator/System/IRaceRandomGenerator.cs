using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中の乱数生成器インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IRaceRandomGenerator
    {
        /// <summary>
        /// 整数範囲指定乱数取得。
        /// </summary>
        /// <param name="min">最小値。(含む)</param>
        /// <param name="max">最大値。(含まない)</param>
        int GetRandom( int min, int max );
        /// <summary>
        /// 整数範囲指定乱数取得。0~指定最大値の範囲内。
        /// </summary>
        /// <param name="max">最大値。(含まない)</param>
        int GetRandom( int max );

        /// <summary>
        /// 実数範囲指定乱数取得。
        /// </summary>
        /// <param name="min">最小値。(含む)</param>
        /// <param name="max">最大値。(含む)</param>
        float GetRandom( float min, float max );
        /// <summary>
        /// 実数範囲指定乱数取得。0~指定最大値の範囲内。
        /// </summary>
        /// <param name="max">最大値。(含む)</param>
        float GetRandom( float max );
    }
}
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース展開定義計算クラス。
    /// </summary>
    /// <seealso cref="Test.TestRacePhaseCalculator"/>
    //-------------------------------------------------------------------
    public class RacePhaseCalculator
    {
        /// <summary>各Phaseの区間数。</summary>
        public const int PHASE_START_SECTION = 4; // 1/24~4/24が序盤。
        public const int PHASE_MIDDLE_SECTION = 12; // 5/24~16/24が中盤。
        public const int PHASE_END_SECTION = 4; // 17/24~20/24が終盤。
        public const int PHASE_LAST_SECTION = 4; // 21/24~24/24がラスト。
        
        /// <summary>中盤が始まる距離。</summary>
        public float PhaseMiddleStartDistance { get; private set; }
        /// <summary>終盤が始まる距離。</summary>
        public float PhaseEndStartDistance { get; private set; }
        /// <summary>ラストが始まる距離。</summary>
        public float PhaseLastStartDistance { get; private set; }

        private float _courseDistance = 0;
        private bool _isInitialized = false;

        //---------------------------------------------------------------
        /// <summary>
        /// 初期化。各関数アクセス前に呼び出しておく必要がある。
        /// </summary>
        //---------------------------------------------------------------
        public void Init(float courseDistance, float sectionDistance)
        {
            Debug.Assert((PHASE_START_SECTION + PHASE_MIDDLE_SECTION + PHASE_END_SECTION + PHASE_LAST_SECTION) == Gallop.RaceDefine.COURSE_SECTION_NUM);
            
            _courseDistance = courseDistance;

            PhaseMiddleStartDistance = (sectionDistance * PHASE_START_SECTION);
            PhaseEndStartDistance = (sectionDistance * (PHASE_START_SECTION + PHASE_MIDDLE_SECTION));
            PhaseLastStartDistance = (sectionDistance * (PHASE_START_SECTION + PHASE_MIDDLE_SECTION + PHASE_END_SECTION));
            
            _isInitialized = true;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 距離からフェーズ取得。
        /// </summary>
        //---------------------------------------------------------------
        public Gallop.RaceDefine.HorsePhase GetPhaseByDistance(float distance)
        {
            Debug.Assert( _isInitialized, "RacePhaseCalculatorが初期化されていません" );

            var phase = Gallop.RaceDefine.HorsePhase.Start;

            // 中盤開始距離より前なら序盤。
            if (distance < PhaseMiddleStartDistance)
            {
                phase = Gallop.RaceDefine.HorsePhase.Start;
            }
            // 終盤開始距離より前なら中盤。
            else if (distance < PhaseEndStartDistance)
            {
                phase = Gallop.RaceDefine.HorsePhase.MiddleRun;
            }
            // ラストスパート開始距離より前なら終盤。
            else if (distance < PhaseLastStartDistance)
            {
                phase = Gallop.RaceDefine.HorsePhase.End;
            }
            // コース距離を超えていればゴール済み。
            else if (distance >= _courseDistance)
            {
                phase = Gallop.RaceDefine.HorsePhase.Finished;
            }
            // ラストスパート。
            else
            {
                phase = Gallop.RaceDefine.HorsePhase.LastSpurt;
            }

            return phase;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 指定フェーズの開始距離、終了距離取得。
        /// </summary>
        /// <param name="phase">取得するフェーズ。</param>
        /// <param name="startDis">開始距離。この値を含む。</param>
        /// <param name="endDis">終了距離。この値を含まない。</param>
        /// <remarks>
        /// 指定フェーズの距離は、【startDis <= 距離 < endDis】となる。
        /// </remarks>
        //---------------------------------------------------------------
        public void GetPhaseDistance(Gallop.RaceDefine.HorsePhase phase, out float startDis, out float endDis)
        {
            Debug.Assert( _isInitialized, "RacePhaseCalculatorが初期化されていません" );

            startDis = 0.0f;
            endDis = 0.0f;
            switch (phase)
            {
                case Gallop.RaceDefine.HorsePhase.Start:
                    startDis = 0.0f;
                    endDis = PhaseMiddleStartDistance;
                    break;
                case Gallop.RaceDefine.HorsePhase.MiddleRun:
                    startDis = PhaseMiddleStartDistance;
                    endDis = PhaseEndStartDistance;
                    break;
                case Gallop.RaceDefine.HorsePhase.End:
                    startDis = PhaseEndStartDistance;
                    endDis = PhaseLastStartDistance;
                    break;
                case Gallop.RaceDefine.HorsePhase.LastSpurt:
                    startDis = PhaseLastStartDistance;
                    endDis = _courseDistance;
                    break;
                case Gallop.RaceDefine.HorsePhase.Finished:
                    startDis = _courseDistance;
                    endDis = float.MaxValue;
                    break;
            }
        }

    }
}
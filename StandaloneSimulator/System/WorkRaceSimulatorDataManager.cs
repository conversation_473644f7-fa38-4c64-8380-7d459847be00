#if STANDALONE_SIMULATOR
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /// <summary>
    /// レースシミュレーターのWork的存在
    /// サーバーからのリクエストを保持しておきたいような場合に使用する
    /// </summary>
    public class WorkRaceSimulatorDataManager : Singleton<WorkRaceSimulatorDataManager>
    {
#region Member

        /// <summary>
        /// 【先バレ防止用】race.csv補間用のCourseSetInfo
        /// race.csvの[id - course_set]に対して上書きするような形で使用する
        /// </summary>
        private Dictionary<int, int> _overwriteCourseSetDict = new Dictionary<int, int>();
        public Dictionary<int, int> OverwriteCourseSetDict => _overwriteCourseSetDict;
        
#endregion
        
        
#region Method

        /// <summary>
        /// CourseSetInfoをDict形式に変換して格納
        /// </summary>
        /// <param name="courseSetInfoArray"></param>
        public void Apply(Gallop.CourseSetInfoShared[] courseSetInfoArray)
        {
            if (courseSetInfoArray == null)
            {
                return;
            }
                    
            _overwriteCourseSetDict.Clear();

            for (int i = 0; i < courseSetInfoArray.Length; i++)
            {
                // race_idをkey、course_setをvalueとしてDict登録しておく
                var courseSetInfo = courseSetInfoArray[i];
                var key = courseSetInfo.race_id;
                var value = courseSetInfo.course_set;
                if (_overwriteCourseSetDict.ContainsKey(key))
                {
                    Debug.LogError("Key重複しています！");
                    continue;
                }
                        
                _overwriteCourseSetDict.Add(key, value);
            }
        }

#endregion
    }
}
#endif
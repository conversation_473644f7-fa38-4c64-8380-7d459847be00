#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public class RaceInfo
    {
        private const int HALF_GATE_ENTRY_NUM = 9;
        public const int DEFAULT_RACE_INSTANCE_ID = 101001;   //主にデバッグで使用するデフォルトレースID(日本ダービー)

        /// <summary>
        /// レース種類。
        /// </summary>
        public Gallop.RaceDefine.RaceType RaceType { get; private set; }

        /// <summary>
        /// 乱数シード
        /// </summary>
        public int RandomSeed { get; set; }

        /// <summary>
        /// レースインスタンスID
        /// </summary>
        public int RaceInstanceId => _raceInstanceMaster.Id;

        /// <summary>
        /// レースID
        /// </summary>
        public int RaceId => RaceMaster.Id;

        /// <summary>
        /// シングルレースプログラムId。シングルレースでのみ有効。
        /// </summary>
        public int SingleRaceProgramId { get; }

        /// <summary>
        /// 対戦相手の評価値。
        /// </summary>
        public int OpponentEvaluate { get; }

        /// <summary>
        /// 自チームの評価値。
        /// </summary>
        public int SelfEvaluate { get; }
        
        /// <summary>
        /// サポートカードのレベルによるスコアボーナス。
        /// </summary>
        public int SupportCardScoreBonus { get; }

        /// <summary>
        /// スコア計算が必要なチームId。
        /// </summary>
        public int ScoreCalcTeamId { get; }

        /// <summary>
        /// レースグレード
        /// </summary>
        public Gallop.RaceDefine.Grade Grade => (Gallop.RaceDefine.Grade)(int)RaceMaster.Grade;

        /// <summary>
        /// レースのコース情報(距離、内外回り、芝・ダートなど)
        /// </summary>
        public IRaceCourseSetAccessor RaceCourseSet { get; private set; }

        /// <summary>
        /// 競馬場Master
        /// </summary>
        public IRaceTrackAccessor RaceTrack { get; private set; }

        /// <summary>
        /// 競馬場ID
        /// </summary>
        public int RaceTrackId => RaceCourseSet.RaceTrackId;

        /// <summary>
        /// 初期レーン計算方法。
        /// </summary>
        public HorseInitialLaneCalculator.InitialLaneType InitialLaneType { get; private set; }

        /// <summary>
        /// 内回り/外回り
        /// </summary>
        public Gallop.RaceDefine.CourseAround Inout => (Gallop.RaceDefine.CourseAround)RaceCourseSet.Inout;

        /// <summary>
        /// 左回り/右回り
        /// </summary>
        public Gallop.RaceDefine.Rotation Rotation => (Gallop.RaceDefine.Rotation) RaceCourseSet.Turn;
        
        /// <summary>
        /// 左向き直線を左回り、右向き直線を右回りと扱うRotation。
        /// </summary>
        public Gallop.RaceDefine.Rotation RotationCategory { get; private set; }

        /// <summary>
        /// 芝/ダート
        /// </summary>
        public Gallop.RaceDefine.GroundType GroundType => (Gallop.RaceDefine.GroundType)RaceCourseSet.Ground;

        /// <summary>
        /// 距離
        /// </summary>
        public int CourseDistance => RaceCourseSet.Distance;

        /// <summary>
        /// コース距離の１区間の距離。
        /// </summary>
        public float CourseSectionDistance { get; private set; }

        /// <summary>
        /// 距離区分。
        /// </summary>
        public Gallop.RaceDefine.CourseDistanceType CourseDistanceType { get; private set; }

        /// <summary>
        /// ハロン数。
        /// </summary>
        public int CourseFurlongNum { get; private set; }

        /// <summary>
        /// ハーフゲートを使用するかどうか。
        /// </summary>
        public bool IsHalfGate { get; private set; }
        
        public Gallop.RaceDefine.GroundCondition GroundCondition { get; private set; }
        public Gallop.RaceDefine.Weather Weather { get; set; }
        public Gallop.RaceDefine.Season Season { get; set; }
        public Gallop.RaceDefine.Time Time { get; set; }

        // 基礎スピード(2000mで20.0m/s 1000m毎に1.0増減する)
        private float _baseSpeed = -1;
        public float BaseSpeed
        {
            get
            {
                Debug.Assert(!RaceUtilMath.Approximately(_baseSpeed, -1));
                return _baseSpeed;
            }

            set
            {
                _baseSpeed = value;
            }
        }

        /// <summary>
        /// このレースのボーダータイム。
        /// </summary>
        /// <remarks>
        /// 実時間をRaceParamDefine.FinishTimeCoef倍したもの。
        /// </remarks>
        public float BorderTimeScaled { get; private set; }

        /// <summary>
        /// チャレンジマッチ/マンスリーマッチの難易度。
        /// </summary>
        public Gallop.RaceDefine.Difficulty RaceDifficulty { get; private set; }

        /// <summary>
        /// 出走キャラ数。
        /// </summary>
        public int NumRaceHorses { get; private set; }

        /// <summary>
        /// 枠番最大値。
        /// </summary>
        public int PostNumberMax { get; private set; }

        /// <summary>
        /// 出走するキャラ配列。馬番で昇順にソート済み。
        /// </summary>
        public RaceHorseSimulateData[] RaceHorse { get; private set; }

        // このレースでの最下位の順位。
        public int LastOrder
        {
            get
            {
                Debug.Assert(NumRaceHorses > 0);
                return NumRaceHorses - 1;
            }
        }

        // レースマスタ
        private readonly IRaceAccessor _raceMaster = null;
        public IRaceAccessor RaceMaster => _raceMaster;

        // レースインスタンスマスタ
        private readonly IRaceInstanceAccessor _raceInstanceMaster = null;
        public IRaceInstanceAccessor RaceInstanceMaster => _raceInstanceMaster;


        /// <summary>
        /// レース再生に必要なシミュレーション結果を保持しているかどうか。
        /// </summary>
        /// <returns></returns>
        public bool HasRaceSimData()
        {
            return SimData != null || !string.IsNullOrEmpty(SimDataBase64);
        }

        /// <summary>
        /// レースシミュレーション結果。※Base64エンコードされた文字列。RaceSimulateDataにデコードする前の状態。
        /// </summary>
        public string SimDataBase64 { get; private set; }
        /// <summary>
        /// レースシミュレーション結果。
        /// </summary>
        /// <remarks>
        /// ・サーバー計算：RaceInializer.LoadRaceDataでRaceInfo生成時に設定される。
        /// ・ローカル計算：RaceMainViewでローカルシミュレート終了後に設定される。
        /// </remarks>
        public RaceSimulateData SimData { get; private set; }

#if CYG_DEBUG && UNITY_EDITOR
        public List<CourseSlope> SlopeList { get; set; }
        public List<CourseCorner> Corners { get; set; }
#endif

        /// <summary>
        /// レーン距離最小値。※0固定。
        /// </summary>
        public static float LaneDistanceMin { get { return 0; } }
        /// <summary>
        /// レーン距離最大値。コースセットによって定義される。
        /// </summary>
        public float LaneDistanceMax { get; private set; }

        /// <summary>
        /// 機能時限解放のビットフラグ。
        /// </summary>
        public uint UnlockFlags { get; }

        /// <summary>
        /// 機能解放されているか。
        /// </summary>
        public bool IsUnlock(Gallop.RaceDefine.UnlockFlag flag)
        {
            return (UnlockFlags & (uint)flag) != 0;
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public RaceInfo(RaceInitializer.LoadRaceInfo loadRaceInfo)
        {
            RaceType = loadRaceInfo.RaceType;
            UnlockFlags = loadRaceInfo.UnlockFlags;
            RaceDifficulty = loadRaceInfo.RaceDifficulty;

            _raceInstanceMaster = MasterManager.Instance.MasterRaceInstance.Get(loadRaceInfo.RaceInstanceId);
            if (null == _raceInstanceMaster)
            {
                throw new System.Exception("不正なRaceInstanceIdです。Id=" + loadRaceInfo.RaceInstanceId);
            }
            _raceMaster = MasterManager.Instance.MasterRace.Get(_raceInstanceMaster.RaceId);
            if (null == _raceMaster)
            {
                throw new System.Exception("不正なRaceIdです。Id=" + _raceInstanceMaster.RaceId);
            }
            RaceCourseSet = MasterManager.Instance.MasterRaceCourseSet.Get(RaceMaster.CourseSet);
            if (RaceCourseSet == null)
            {
                throw new System.Exception("不正なRaceMaster.courseSetです。Id=" + RaceMaster.CourseSet);
            }
            RaceTrack = MasterManager.Instance.MasterRaceTrack.Get(RaceCourseSet.RaceTrackId);
            if (RaceTrack == null)
            {
                throw new System.Exception("不正なRaceCourseSetMaster.RaceTrackIdです。Id=" + RaceCourseSet.RaceTrackId);
            }

            RandomSeed = loadRaceInfo.RandomSeed;
            CourseSectionDistance = (float)CourseDistance / Gallop.RaceDefine.COURSE_SECTION_NUM;
            InitialLaneType = (HorseInitialLaneCalculator.InitialLaneType)RaceTrack.InitialLaneType;
            RotationCategory = RaceUtil.GetCourseRotationCategory(Rotation);
            CourseFurlongNum = (int)(CourseDistance * Gallop.RaceDefine.ONE_FURONG_DISTANCE_INV);
            CourseDistanceType = RaceUtil.CalcDistanceType(CourseDistance);
            LaneDistanceMax = RaceUtilMath.MasterInt2Float(RaceCourseSet.FloatLaneMax);
            GroundCondition = loadRaceInfo.GroundCondition;
            Weather = loadRaceInfo.Weather;
            Season = loadRaceInfo.Season;
            Time = (Gallop.RaceDefine.Time)_raceInstanceMaster.Time;

            SingleRaceProgramId = loadRaceInfo.SingleRaceProgramId;
            OpponentEvaluate = loadRaceInfo.OpponentEvaluate;
            SelfEvaluate = loadRaceInfo.SelfEvaluate;
            SupportCardScoreBonus = loadRaceInfo.SupportCardScoreBonus;
            ScoreCalcTeamId = loadRaceInfo.ScoreCalcTeamId;
            
            // 序盤/中盤/終盤/ラストの区間初期化。
            InitRacePhase();
            
            SetupHorseData(loadRaceInfo.SortedRaceHorseDataArray);
        }
        
        
        #region <レース展開定義>
        private RacePhaseCalculator _phaseCalculator = new RacePhaseCalculator();
        public RacePhaseCalculator PhaseCalc => _phaseCalculator;
        private void InitRacePhase()
        {
            _phaseCalculator.Init(CourseDistance, CourseSectionDistance);
        }

        public Gallop.RaceDefine.HorsePhase GetPhaseByDistance(float distance)
        {
            return _phaseCalculator.GetPhaseByDistance(distance);
        }
        public void GetPhaseDistance(Gallop.RaceDefine.HorsePhase phase, out float startDis, out float endDis)
        {
            _phaseCalculator.GetPhaseDistance(phase, out startDis, out endDis);
        }

        public float PhaseMiddleStartDistance => _phaseCalculator.PhaseMiddleStartDistance;
        public float PhaseEndStartDistance => _phaseCalculator.PhaseEndStartDistance;
        public float PhaseLastStartDistance => _phaseCalculator.PhaseLastStartDistance;
        #endregion <レース展開定義>

        public void SetupHorseData(RaceHorseSimulateData[] horseDataArray)
        {
            RaceHorse = horseDataArray;
            NumRaceHorses = RaceHorse.Length;

#if CYG_DEBUG
            int fixFirstHorseIndex = -1;
            int fixLastHorseIndex = -1;
            foreach (var fixFinishOrder in RaceSimulateDebugger.FixFinishOrderDic)
            {
                if (fixFinishOrder.Key == RaceSimulateDebugger.FixFinishOrderTarget.Null)
                {
                    continue;
                }
                if (fixFinishOrder.Value == RaceSimulateDebugger.FIX_FINISH_ORDER_NULL)
                {
                    continue;
                }
                
                // 上書きするキャラ。
                int targetHorseIndex = fixFinishOrder.Key - RaceSimulateDebugger.FixFinishOrderTarget.HorseIndex_00;
                if(targetHorseIndex < 0)
                {
                    Debug.LogWarning("プレイヤーのキャラが見つからなかったため、着順固定ができませんでした。");
                    continue;
                }

                // 着順１位に固定するキャラのHorseIndexを控える。
                if (fixFinishOrder.Value == 0)
                {
                    fixFirstHorseIndex = targetHorseIndex;
                }
                // 着順最下位に固定するキャラのHorseIndexを控える。
                else if (fixFinishOrder.Value == RaceSimulateDebugger.FIX_FINISH_ORDER_LAST)
                {
                    fixLastHorseIndex = targetHorseIndex;
                }
            }
            
            for (int i = 0; i < RaceHorse.Length; ++i)
            {
                var raceHorseData = RaceHorse[i];
                if (RaceSimulateDebugger.IsNeedFixFinishOrder())
                {
                    // 着順１位に固定するキャラがいる場合、１位のキャラのステータスを高く、それ以外を低く設定する。
                    if (fixFirstHorseIndex >= 0)
                    {
                         SetFixFinishOrderStatus(raceHorseData, i == fixFirstHorseIndex);
                    }
                    // 着順最下位に固定するキャラがいる場合、最下位のキャラのステータスを低く、それ以外を高く設定する。
                    if (fixLastHorseIndex >= 0)
                    {
                        SetFixFinishOrderStatus(raceHorseData, i != fixLastHorseIndex);
                    }
                }
            }
#endif
            // 出走人数を元にハーフゲート使用するかどうか決める。
            CheckUseHalfGate();
            
            // 出走人数を元に枠番最大値計算。
            PostNumberMax = HorsePostNumberCalculator.GetPostNumberMax(NumRaceHorses);
            // 人気順のHorseIndex配列初期化。
            InitHorseIndexByPopularity();
        }

#if CYG_DEBUG
        public static void SetFixFinishOrderStatus(RaceHorseSimulateData raceHorseData, bool isMax)
        {
            const int STATUS_MAX = 1200;
            const int STATUS_MIN = 100;
            const int PROPER_MAX = (int)Gallop.RaceDefine.ProperGrade.S;
            const int PROPER_MIN = (int)Gallop.RaceDefine.ProperGrade.G;
            const int RUNNING_STYLE_MAX = (int)Gallop.RaceDefine.RunningStyle.Nige; 
            const int RUNNING_STYLE_MIN = (int)Gallop.RaceDefine.RunningStyle.Oikomi;
    
            int status = isMax ? STATUS_MAX : STATUS_MIN;
            int proper = isMax ? PROPER_MAX : PROPER_MIN;
            int runningStyle = isMax ? RUNNING_STYLE_MAX : RUNNING_STYLE_MIN;
    
            raceHorseData.speed = status;
            raceHorseData.stamina = status;
            raceHorseData.pow = status;
            raceHorseData.guts = status;
            raceHorseData.wiz = status;
            raceHorseData.proper_ground_turf = proper;
            raceHorseData.proper_ground_dirt = proper;
            raceHorseData.proper_distance_short = proper;
            raceHorseData.proper_distance_mile = proper;
            raceHorseData.proper_distance_middle = proper;
            raceHorseData.proper_distance_long = proper;
            raceHorseData.running_style = runningStyle;
        }
#endif

        /// <summary>
        /// 人気順のHorseIndex配列初期化。
        /// </summary>
        private void InitHorseIndexByPopularity()
        {
            HorseIndexByPopularity = new int[RaceHorse.Length];
            for (int horseIndex = 0; horseIndex < RaceHorse.Length; ++horseIndex)
            {
                var horse = RaceHorse[horseIndex];
                HorseIndexByPopularity[horse.popularity] = horseIndex;
            }
        }
        
        private void CheckUseHalfGate()
        {
            IsHalfGate = (NumRaceHorses <= HALF_GATE_ENTRY_NUM) && (RaceTrack.EnableHalfGate == 1);
        }
        
    #if CYG_DEBUG
        /// <summary>
        /// InitHorseIndexByPopularityをデバッグ用途で外部公開。
        /// </summary>
        public void DbgInitHorseIndexByPopularity()
        {
            InitHorseIndexByPopularity();
        }
    #endif

        // 着順順のHorseIndex。
        public int[] HorseIndexByFinishOrder { get; set; }

        // 人気順HorseIndex配列。※先頭が最も人気が高い。
        public int[] HorseIndexByPopularity { get; set; }

        public void InitBaseSpeed(Gallop.RaceParamDefine paramDefine)
        {
            // 基礎スピード(2000mで20.0m/s 1000m毎に1.0増減する)
            BaseSpeed = paramDefine.raceBaseSpeedBaseSpeed - ((CourseDistance - paramDefine.raceBaseSpeedBaseDistance) / paramDefine.raceBaseSpeedDistanceRate);
        }

        /// <summary>
        /// このレースのボーダータイムの計算。
        /// </summary>
        /// <param name="speedParam"></param>
        public void InitBorderTime(Gallop.RaceParamDefine.SpeedParam speedParam, Gallop.RaceParamDefine.RaceGlobalParam globalParam)
        {
            GetPhaseDistance(Gallop.RaceDefine.HorsePhase.End, out float endStart, out float _);
            GetPhaseDistance(Gallop.RaceDefine.HorsePhase.LastSpurt, out float _, out float lastEnd);
            float endAfterPhaseDistance = lastEnd - endStart;
            Debug.Assert(endAfterPhaseDistance > 0);

            BorderTimeScaled = RaceBorderTimeCalculator.CalcBorderTime(
                CourseDistance,
                endAfterPhaseDistance,
                BaseSpeed,
                speedParam.ExpectLastSpurtSpeed);

            BorderTimeScaled *= globalParam.FinishTimeCoef;
        }
    }
}
#endif
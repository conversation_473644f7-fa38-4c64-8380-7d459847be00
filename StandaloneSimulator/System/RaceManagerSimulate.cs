#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Numerics;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Math = System.Math;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース管理：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public class RaceManagerSimulate :
        Singleton<RaceManagerSimulate>,
        IRaceCourseAttributeAccessor,
        IRaceTimeAccessor,
        IRaceRandomGenerator,
        IRaceEventRecorder
    {
        //-------------------------------------------------------------------
        // 定義。
        //-------------------------------------------------------------------
        /// <summary>シミュレーション間隔秒</summary>
        public const float SIMULATE_ELAPSED_TIME = 0.0333f * 2.0f;
        /// <summary>シミュレーション"記録"間隔秒</summary>
        public const float SIMULATE_RECORD_ELAPSED_TIME = 1.0f;
        /// <summary>出走直後～この秒数までは記録精度高める</summary>
        public const float SIMULATE_HIGH_ACCURACY_THRESHOLD = 1.0f;
        /// <summary>ゴール前後この距離内にいるときは記録頻度を上げる。</summary>
        private const float GOAL_NEAR_DISTANCE = 25;

        //-------------------------------------------------------------------
        // 変数。
        //-------------------------------------------------------------------
        /// <summary>このレースのRaceInfo</summary>
        private RaceInfo _raceInfo;
        public RaceInfo RaceInfo => _raceInfo;
        /// <summary>コースイベント管理</summary>
        private CourseEventManager _courseEventManager = null;
        /// <summary>キャラ管理</summary>
        private IRaceHorseAccessor _horseManager = null;
        /// <summary>イベント管理</summary>
        public RaceEventManager EventManager { get; private set; }
        /// <summary>出走からの経過時間</summary>
        public float AccumulateTimeSinceStart { get; private set; }

        /// <summary> 経過フレーム数 </summary>
        public int CurrentFrame { get; private set; } = 0;
#if GALLOP
        // これらはインターフェースの都合で実装だけしているが使用しない
        public float ElapsedTime { get; }
        public float MainDeltaTimeScale { get; }
        public float SkillCutInElapsedTime { get; }
#endif

        /// <summary>乱数生成器</summary>
        private RaceRandom _randomGenerator = null;
        /// <summary>ゴールしていないキャラの最大距離と最小距離の差</summary>
        public float DistanceDiffMax { get; private set; }

        /// <summary>ParamDefine</summary>
        public Gallop.RaceParamDefine ParamDefine { get; private set; }

        /// <summary>このコースに存在する直線</summary>
        private List<CourseStraight> _straightList = null;
        /// <summary>直線の種類で直線を取得するためのdictionary</summary>
        private Dictionary<Gallop.RaceDefine.StraightFrontType, List<CourseStraight>> _straightDic;
        /// <summary>このコースに存在するコーナー</summary>
        private List<CourseCorner> _cornerList = null;
        /// <summary>このコースに存在する坂</summary>
        private List<CourseSlope> _slopeList = null;
        /// <summary>コースパス</summary>
        public CourseLane CourseLane { get; private set; }

        /// <summary>コースを進んだ距離(_distanceの差分)を3D上の距離に変換するための係数</summary>
        public float CourseDistance2World { get; private set; } = 1.0f;

        /// <summary>シミュレート結果記録</summary>
        private RaceSimulateWriter _simulateWriter = null;
        
        /// <summary>シミュレート記録間隔秒</summary>
        private float ReplayRecordIntervalTime
        {
            get
            {
#if CYG_DEBUG
                // レースシミュレーターツールでシミュレーションする場合は、シミュレーション間隔と同じ精度で記録する。
                if (RaceSimulateDebugger.IsDebugSimulationOneTime || RaceSimulateDebugger.IsRecordIntervalSimulate)
                {
                    return SIMULATE_ELAPSED_TIME;
                }
#endif

                /*
                 * 以下のケースでのみシミュレートの記録頻度を上げる（シミュレーション精度と同じにする）。
                 * 記録頻度を上げるということはレースシナリオのサイズが膨れることになるので、必要なケース以外記録頻度は上げてはいけない。
                 * 
                 * ▼1
                 * IsStartNear()：出走直後の僅かな時間。
                 * 
                 * 出走遅延によりスタート直後に足が滑るバグ対応（出走遅延が入っている場合、スタート地点に留まっているのをリプレイで忠実に再現するための処理）。
                 *
                 * ▼2
                 * #60496
                 * IsGoalNear()：ユーザーキャラがゴール前後にいる場合。
                 * ゴール付近で速度差のある２人が競っている場合、
                 * レースの着順と、レースを再生したときの見た目が異なる（着順で負けている方が先行しているように見えてしまう）場合がある件の対応。
                 * （対象をユーザーキャラだけにしているのは、NPC同士が競っているのは注視されないと考えられるため）
                 */
                if (IsStartNear() || IsGoalNear())
                {
                    return SIMULATE_ELAPSED_TIME;
                }
                else
                {
                    return SIMULATE_RECORD_ELAPSED_TIME;
                }
            }
        }

        //-------------------------------------------------------------------
        // 関数。
        //-------------------------------------------------------------------
        
        //-------------------------------------------------------------------
        protected override void OnInitialize()
        {
        }
        
        //-------------------------------------------------------------------
        protected override void OnFinalize()
        {
            if (null != CourseLane)
            {
                CourseLane.Exit();
                CourseLane = null;
            }
            if (EventManager != null)
            {
                EventManager.Release();
                EventManager = null;
            }
            if (_horseManager != null)
            {
                _horseManager.Release();
                _horseManager = null;
            }
        }

        /// <summary>
        /// 初期化。
        /// </summary>
        public void Init(
            RaceInfo raceInfo, 
            CourseParamTable courseParamTable,
            Gallop.RaceParamDefine paramDefine, 
            Gallop.CourseLaneAnim courseLane, 
            Gallop.CourseLaneAnim overRunLane)
        {
            _raceInfo = raceInfo;
            ParamDefine = paramDefine;

            // コースイベント初期化。
            CreateCourseEvent(courseParamTable);

            // レース基礎速度初期化。
            InitRaceBaseSpeed();

            // 乱数初期化。
            InitRandomNumber();

            // レーンアニメーション初期化。
            InitCourseLane(courseLane, overRunLane);

            // レースの直線リストキャッシュ。
            CacheStraights();
            // レースのコーナーリストキャッシュ。
            CacheCorners();
            // レースの坂リストキャッシュ。
            CacheSlopes();
            
            // キャラ管理初期化。
            InitHorseManager();

            //イベントマネージャ作成
            InitEventManager();

            // コース上距離1を3D空間上の距離に投影するための係数を求める。
            InitCourseDistance2World();
        }

        /// <summary>
        /// スタート直後かどうか。
        /// </summary>
        private bool IsStartNear()
        {
            return AccumulateTimeSinceStart < SIMULATE_HIGH_ACCURACY_THRESHOLD;
        }
        
        /// <summary>
        /// ユーザーのキャラがゴール付近にいるかどうか。
        /// </summary>
        private bool IsGoalNear()
        {
            var allHorse = GetHorseRaceInfos();
            for (int i = 0; i < allHorse.Length; i++)
            {
                var horse = allHorse[i];
                
                // NPCの僅差の結果は気にする必要無いので無視。
                if (horse.ViewerId == 0)
                {
                    continue;
                }

                // ゴール前後一定範囲内にいるキャラがいればtrue返却。
                if (Math.Abs(_raceInfo.CourseDistance - horse.GetDistance()) <= GOAL_NEAR_DISTANCE)
                {
                    return true;
                }
            }

            // ゴール付近にいない。
            return false;
        }
        
        //-------------------------------------------------------------------
        public RaceSimulateData GetSimulateData()
        {
            return _simulateWriter.SimulateData;
        }
        
        /// <summary>
        /// 乱数取得。
        /// </summary>
        /// <remarks>
        /// RaceManagerの管理クラス(RaceManager/RaceHorseManager系/HorseRaceInfo系/Skill系/)以外からはこの乱数を使用しないでください。
        /// </remarks>
        public int GetRandom(int min, int max)
        {
            return _randomGenerator.GetRandom(min, max);
        }
        public int GetRandom(int max)
        {
            return _randomGenerator.GetRandom(0, max);
        }
        public float GetRandom(float min, float max)
        {
            return _randomGenerator.GetRandom(min, max);
        }
        public float GetRandom(float max)
        {
            return _randomGenerator.GetRandom(0.0f, max);
        }
        
        #region <生成>
        /// <summary>
        /// レースベース速度初期化。
        /// </summary>
        private void InitRaceBaseSpeed()
        {
            Debug.Assert(null != ParamDefine);
            _raceInfo.InitBaseSpeed(ParamDefine);
            _raceInfo.InitBorderTime(ParamDefine.Speed, ParamDefine.Global);
        }

        /// <summary>
        /// 乱数初期化。
        /// </summary>
        private void InitRandomNumber()
        {
            _randomGenerator = new RaceRandom(_raceInfo.RandomSeed);
        }

        /// <summary>
        /// キャラ管理初期化。
        /// </summary>
        private void InitHorseManager()
        {
            _horseManager = new RaceHorseManagerSimulate();
            _horseManager.Init(_raceInfo, ParamDefine);
        }

        /// <summary>
        /// 出走前のキャラ初期化。
        /// </summary>
        private void InitBeforeStart()
        {
            _horseManager.InitBeforeStart();
        }

        
        /// <summary>
        /// レースイベント管理生成。
        /// </summary>
        private void InitEventManager()
        {
            //イベントマネージャ作成
            EventManager = new RaceEventManager(_horseManager, _courseEventManager);

            // 地面素材変更イベント登録。
            // EventManager.Add(RaceEvent.EventParam, EventGroundTypeChange);
            EventManager.Add(RaceEvent.EventParam, EventStraightChange);
            EventManager.Add(RaceEvent.EventParam, EventCornerChange);
            EventManager.Add(RaceEvent.EventParam, EventLaneMaxChange);
            EventManager.Add(RaceEvent.EventParam, EventSlopeChange);
        }

        /// <summary>
        /// レーン管理生成/初期化。
        /// </summary>
        private void InitCourseLane(Gallop.CourseLaneAnim courseLane, Gallop.CourseLaneAnim overRunLane)
        {
            var isNeedRotationFix = _raceInfo.RotationCategory != Gallop.RaceDefine.Rotation.Left;
            CourseLane = new CourseLane();
            CourseLane.Init(isNeedRotationFix, courseLane, overRunLane, _raceInfo.CourseDistance);
        }

        //-------------------------------------------------------------------
        private void CreateCourseEvent(CourseParamTable courseParamTable)
        {
            _courseEventManager = new CourseEventManager();
            _courseEventManager.Init(courseParamTable);
        }
        #endregion <生成>

        #region <レーン>
        /// <summary>
        /// 指定Distanceでのレーン3D座標/回転取得。
        /// </summary>
        /// <param name="distance">コース距離。</param>
        /// <param name="position">座標。※LaneDistanceは考慮されていない。</param>
        /// <param name="rotation">回転。</param>
        public void GetLaneTransform(float distance, out Vector3 position, out Quaternion rotation)
        {
            CourseLane.GetLaneTransform(distance, out position, out rotation);
        }

        /// <summary>
        /// コース上距離1を3D空間上の距離に変換するための係数を求める。
        /// </summary>
        private void InitCourseDistance2World()
        {
            Vector3 posLaneOrigin, posWorld1, posWorld2;
            Quaternion rot;
            RaceUtil.CalcWorldTransform(0.0f, 0.0f, out posLaneOrigin, out posWorld1, out rot);
            RaceUtil.CalcWorldTransform(1.0f, 0.0f, out posLaneOrigin, out posWorld2, out rot);
            CourseDistance2World = Vector3.Distance(posWorld1, posWorld2);
        }
        #endregion <レーン>

        #region レースイベント
        /// <summary>
        /// レースイベント：直線切り替え。
        /// </summary>
        private void EventStraightChange(IHorseRaceInfoSimulate horse1, int param = 0, int[] values = null)
        {
            if (param != (int)CourseParamType.Straight)
            {
                return;
            }
            if (horse1 == null)
            {
                Debug.LogError("EventStraightChange horse1 == null.");
                return;
            }
            if (values == null)
            {
                Debug.LogError("EventStraightChange values == null.");
                return;
            }

            if (!RaceUtil.GetStraightParam(values, out var isStart, out var frontType, out var isLast))
            {
                return;
            }

            bool isStraightPrev = horse1.IsStraight;
            horse1.IsStraight = isStart;
            horse1.CurStraightFrontType = frontType;
            horse1.IsLastStraightCourseEvent = isLast;

            horse1.CurStraightTypeNumber = CourseStraight.FRONT_TYPE_NUMBER_NULL;

            // 直線に入った時の処理。
            if (isStart)
            {
                // 直線番号を取得。
                foreach (var straight in _straightList)
                {
                    if (straight.frontType != frontType)
                    {
                        continue;
                    }

                    if (straight.StartDistance > horse1.GetDistance() || straight.EndDistance < horse1.GetDistance())
                    {
                        continue;
                    }

                    horse1.CurStraightTypeNumber = straight.FrontTypeNumber;
                    break;
                }

                // コーナーは強制解除。
                horse1.CurCorner = Gallop.RaceDefine.CORNER_NULL;

                // 最後の直線開始時の順位を保存。
                if (!isStraightPrev)
                {
                    if (horse1.IsStraightLast && !horse1.IsLastStraightOrderInitialized)
                    {
                        horse1.LastStraightOrder = horse1.CurOrder;
                    }
                }
            }
        }

        /// <summary>
        /// レースイベント：コーナー切り替え
        /// </summary>
        private void EventCornerChange(IHorseRaceInfoSimulate horse1, int param = 0, int[] values = null)
        {
            if (param != (int)CourseParamType.Corner)
            {
                return;
            }
            if (horse1 == null)
            {
                Debug.LogError("EventLaneMaxChange horse1 == null.");
                return;
            }
            if (values == null)
            {
                Debug.LogError("EventLaneMaxChange values == null.");
                return;
            }
            if (values.Length < CourseParam.CORNER_VALUE_NUM)
            {
                Debug.LogError("EventCornerChange values.Lengthが不正。length=" + values.Length);
                return;
            }

            int cornerNo = values[CourseParam.CORNER_VALUE_INDEX_NO];
            var corner = GetCorner(cornerNo, horse1.GetDistance());
            
            // 最終コーナーかどうか判定。
            horse1.IsFinalCorner = corner != null ? corner.IsFinalCorner : false;

            horse1.IsCornerEventPassed = true;

            // コーナーに入ったら直線状態は強制解除。
            horse1.IsStraight = false;

            // 現在通過中のコーナー番号更新。
            horse1.CurCorner = cornerNo;

            // 現在通過中のコーナーの終了地点更新。
            var cornerEndDistance = (null != corner) ? corner.EndDistance : horse1.GetDistance() + Gallop.RaceDefine.CORNER_DISTANCE_DEFAULT;
            horse1.CornerEndDistance = cornerEndDistance;
        }

        /// <summary>
        /// レースイベント：坂切り替え
        /// </summary>
        private void EventSlopeChange(IHorseRaceInfoSimulate horse1, int param = 0, int[] values = null)
        {
            if (param != (int)CourseParamType.Slope)
            {
                return;
            }
            if (horse1 == null)
            {
                Debug.LogError("EventSlopeChange horse1 == null.");
                return;
            }
            if (values == null)
            {
                Debug.LogError("EventSlopeChange values == null.");
                return;
            }

            if (!CourseParam.GetSlopeValue(values, out float slopePer, out var slopeType, out _))
            {
                return;
            }
            
            var slope = GetSlope(horse1.GetDistance(), slopeType);
            if (slope != null)
            {
                horse1.SlopeType = slopeType;
                horse1.SlopePer = slopePer;
                horse1.SlopeEndDistance = slope.EndDistance;
            }
            else
            {
                // slopeが取得できないのはスキップ後のhorse1.GetDistance()で坂の開始～終了地点を超えている時。坂状態はクリアしておく。
                horse1.SlopeType = Gallop.RaceDefine.SlopeType.Null;
                horse1.SlopePer = 0;
            }
        }

        private CourseSlope GetSlope(float distance, Gallop.RaceDefine.SlopeType slopeType)
        {
            for(int i = 0; i < _slopeList.Count; ++i)
            {
                var slope = _slopeList[i];
                if (slope.SlopeType != slopeType)
                {
                    continue;
                }

                if (distance >= slope.StartDistance && 
                    distance <= slope.EndDistance)
                {
                    return slope;
                }
            }

            return null;
        }
        
        /// <summary>
        /// レースイベント：レーン最大値変化。
        /// </summary>
        private static void EventLaneMaxChange(IHorseRaceInfoSimulate horse1, int param = 0, int[] values = null)
        {
            if (param != (int)CourseParamType.LaneMaxChange)
            {
                return;
            }
        #if CYG_DEBUG
            // レーン移動無効の時はレーン最大値可変させない。
            if(!RaceSimulateDebugger.IsEnableAIMoveLane)
            {
                return;
            }
        #endif
            if (horse1 == null)
            {
                Debug.LogError("EventLaneMaxChange horse1 == null.");
                return;
            }
            if (values == null)
            {
                Debug.LogError("EventLaneMaxChange values == null.");
                return;
            }
            if (values.Length < CourseParam.LANEMAXCHANGE_VALUE_NUM)
            {
                Debug.LogError("EventLaneMaxChange values.Lengthが不正。length=" + values.Length);
                return;
            }

            horse1.LaneDistanceMax = (float)values[CourseParam.LANEMAXCHANGE_VALUE_INDEX_LANEMAX] / 10000.0f;
        }
        #endregion

        #region イベント記録
        //-------------------------------------------------------------------
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public void AddSkillEvent(
            IHorseRaceInfoSimulate horseRaceInfo, 
            int skillID, 
            int detailIndex, 
            int targetFlags, 
            float curTime, 
            int continueTimeInt,
            Gallop.SkillDefine.ActivateType activateType)
        {
            _simulateWriter.AddEvent(
                curTime,
                SimulateEventType.Skill,
                horseRaceInfo.HorseIndex,
                skillID,
                continueTimeInt,
                detailIndex,
                targetFlags,
                (int)activateType);
        }
#endif
#if GALLOP
        public void AddSkillEvent(
            Gallop.IHorseRaceInfo horseRaceInfo, 
            int skillID, 
            int detailIndex, 
            int targetFlags, 
            float curTime, 
            int continueTimeInt,
            Gallop.SkillDefine.ActivateType activateType)
        {
            // 参照されない
            Debug.LogError("意図しない参照が発生しています");
        }
#endif
        
        //-------------------------------------------------------------------
        public void AddScoreEvent(int horseIndex, int rawScoreId, int addScore, float curTime)
        {
            _simulateWriter.AddEvent(
                curTime,
                SimulateEventType.Score,
                horseIndex,
                rawScoreId,
                addScore);
        }
        
        //-------------------------------------------------------------------
        public void AddChallengeMatchPointEvent(int horseIndex, int rawScoreId, int addScore, float curTime)
        {
            _simulateWriter.AddEvent(
                curTime,
                SimulateEventType.ChallengeMatchPoint,
                horseIndex,
                rawScoreId,
                addScore);
        }
        
        //-------------------------------------------------------------------
        public void AddCompeteTopEvent(int horseIndex, float curTime)
        {
            _simulateWriter.AddEvent(
                curTime,
                SimulateEventType.CompeteTop,
                horseIndex);
        }
        
        //-------------------------------------------------------------------
        public void AddCompeteFightEvent(int horseIndex, float curTime)
        {
            _simulateWriter.AddEvent(
                curTime,
                SimulateEventType.CompeteFight,
                horseIndex);
        }
        
        //-------------------------------------------------------------------
        public void AddReleaseConservePowerEvent(int horseIndex, float curTime)
        {

            if (_simulateWriter != null)
            {
                _simulateWriter.AddEvent(curTime, SimulateEventType.ReleaseConservePower, horseIndex);
            }
        }
        //-------------------------------------------------------------------
        public void AddStaminaLimitBreakBuffEvent(int horseIndex, float curTime)
        {
            if (_simulateWriter != null)
            {
                _simulateWriter.AddEvent(curTime, SimulateEventType.StaminaLimitBreakBuff, horseIndex);
            }
        }
        
        //-------------------------------------------------------------------
        public void AddCompeteBeforeSpurtEvent(int horseIndex, float curTime)
        {
            if (_simulateWriter != null)
            {
                _simulateWriter.AddEvent(curTime, SimulateEventType.CompeteBeforeSpurt, horseIndex);
            }
        }
        //-------------------------------------------------------------------
        public void AddStaminaKeepEvent(int horseIndex, float curTime)
        {
            if (_simulateWriter != null)
            {
                _simulateWriter.AddEvent(curTime, SimulateEventType.StaminaKeep, horseIndex);
            }
        }
        //-------------------------------------------------------------------
        public void AddSecureLeadEvent(int horseIndex, float curTime)
        {
            if (_simulateWriter != null)
            {
                _simulateWriter.AddEvent(curTime, SimulateEventType.SecureLead, horseIndex);
            }
        }
        #endregion

        #region 更新
        /// <summary>
        /// シミュレート。
        /// </summary>
        public void Simulate(bool isUseFrameDebugData)
        {
            _simulateWriter = new RaceSimulateWriter();
            _simulateWriter.StartRecord(GetHorseNumber());
            {
                // シミュレーション間隔秒。
                float replayElapse = SIMULATE_ELAPSED_TIME;
                // シミュレーション記録間隔秒。
                float replayRecordInterval = ReplayRecordIntervalTime;

                // 出走開始前のキャラ初期化。
                InitBeforeStart();

                // 0秒目(スタート地点)のデータを記録。
                UpdateHorseDatas(0, isFirstUpdate: true);
                _simulateWriter.RecordRaceInit(_horseManager, isUseFrameDebugData);

                // レースの記録。
                bool isSimulationFinished = false;
                while (!isSimulationFinished)
                {
                    // シミュレーションはelapse間隔で行う。
                    AccumulateTimeSinceStart += replayElapse;
                    CurrentFrame++;
                    _simulateWriter.AddCurrentTime(replayElapse);
                    isSimulationFinished = UpdateHorseDatas(replayElapse);

                    // イベントの更新
                    EventManager.UpdateSimulate();

                    // シミュレーション記録は一定の間隔(ReplayRecordIntervalTime)で行う。
                    // #16318 : 全員ゴール時も記録しないと、最後尾でゴールしたキャラの最後のフレームが記録されない。→スキップ時のRaceSimulateReader.GetTimeByDistance(最後尾キャラ,コース距離)が失敗する。
                    replayRecordInterval -= replayElapse;
                    if (replayRecordInterval <= 0.0f + float.Epsilon || isSimulationFinished)
                    {
                        _simulateWriter.Record(_horseManager, isUseFrameDebugData);
                        replayRecordInterval = ReplayRecordIntervalTime;
                    }
                }
            }
            _simulateWriter.EndRecord(_horseManager);
        }

        /// <summary>
        /// キャラ更新。
        /// </summary>
        /// <returns>レース終了(オーバーラン込で)したらtrue。</returns>
        private bool UpdateHorseDatas(float elapsedTime, bool isFirstUpdate = false)
        {
            // 初回Updateの時にフラグを上げるので別メソッドを呼んでいます(_horseManager.UpdateHorseはどちらでも呼ばれます)
            var isAllFinished = (isFirstUpdate) ?
                _horseManager.UpdateFirstHorse() : _horseManager.UpdateHorse(elapsedTime);

            // 先頭キャラと最後尾キャラのdistance差最大値更新。
            if (!isAllFinished)
            {
                UpdateDistanceDiffMax();
            }

            return isAllFinished;
        }

        /// <summary>
        /// 先頭キャラと最後尾キャラのdistance差最大値更新。
        /// </summary>
        private void UpdateDistanceDiffMax()
        {
            // ゴールしていないキャラのdistance最大値/最小値を調べ、差分を計算する。
            // 単純に先頭キャラと最後尾キャラを取得してdistance差分を取ると、
            // 先頭キャラがゴール後、２位のキャラと最後尾キャラとのdistance差分が最大になる可能性があり、
            // ２位キャラがゴール後、…というケースが想定されるため、素直に未ゴールのdistanceを収集する。

            float minDistance = float.MaxValue;
            float maxDistance = float.MinValue;

            var allHorses = GetHorseRaceInfos();
            foreach (var horse in allHorses)
            {
                if (horse.IsFinished())
                {
                    continue;
                }

                float distance = horse.GetDistance();
                if (distance < minDistance)
                {
                    minDistance = distance;
                }
                if (distance > maxDistance)
                {
                    maxDistance = distance;
                }
            }

            float diffDistance = maxDistance - minDistance;
            if (diffDistance > DistanceDiffMax)
            {
                DistanceDiffMax = diffDistance;
            }
        }
        #endregion

        #region <HorseIndexアクセス>
        //---------------------------------------------------------------
        public int GetFirstHorseIndex()
        {
            return _horseManager.GetFirstHorseIndex();
        }

        //---------------------------------------------------------------
        public int GetLastHorseIndex()
        {
            return _horseManager.GetLastHorseIndex();
        }

        //---------------------------------------------------------------
        public int GetHorseIndexByOrder(int order)
        {
            return _horseManager.GetHorseIndexByOrder(order);
        }

        //---------------------------------------------------------------
        public int GetHorseIndexByFinishOrder(int order)
        {
            return _horseManager.GetHorseIndexByFinishOrder(order);
        }

        //---------------------------------------------------------------
        public int GetHorseIndexByPopularity(int popularityOrder)
        {
            return _horseManager.GetHorseIndexByPopularity(popularityOrder);
        }
        #endregion <HorseIndexアクセス>

        #region <HorseInfoアクセス>
        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetHorseRaceInfos()
        {
            return _horseManager.GetHorseRaceInfos();
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseInfo(int horseArrayIndex)
        {
            return _horseManager.GetHorseInfo(horseArrayIndex);
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetFirstHorseInfo()
        {
            return _horseManager.GetFirstHorseInfo();
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetLastHorseInfo()
        {
            return _horseManager.GetLastHorseInfo();
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseInfoByOrder(int order)
        {
            return _horseManager.GetHorseInfoByOrder(order);
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseInfoByFinishOrder(int finishOrder)
        {
            return _horseManager.GetHorseInfoByFinishOrder(finishOrder);
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseByPopularity(int popularity)
        {
            return _horseManager.GetHorseByPopularity(popularity);
        }
        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetTeamMemberHorseInfoArray(int teamId)
        {
            return _horseManager.GetTeamMemberHorseInfoArray(teamId);
        }
        public List<HorseTeamInfo> GetTeamInfoList()
        {
            return _horseManager.GetTeamInfoList();
        }
        #endregion <HorseInfoアクセス>

        #region <キャラアクセスUtility>
        //---------------------------------------------------------------
        public int GetHorseNumber()
        {
            return _horseManager.GetHorseNumber();
        }

        public int LastOrder => _horseManager.LastOrder; 

        //---------------------------------------------------------------

        public int GetRunningStyleCount(Gallop.RaceDefine.RunningStyle style)
        {
            return _horseManager.GetRunningStyleCount(style);
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetRunningStyleHorses(Gallop.RaceDefine.RunningStyle style)
        {
            return _horseManager.GetRunningStyleHorses(style);
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetRunningStyleOrderHorse(Gallop.RaceDefine.RunningStyle runningStyle, int order)
        {
            return _horseManager.GetRunningStyleOrderHorse(runningStyle, order);
        }
        
        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetRunningStyleTopOrderHorse(Gallop.RaceDefine.RunningStyle runningStyle)
        {
            return _horseManager.GetRunningStyleTopOrderHorse(runningStyle);
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetRunningStyleLastOrderHorse(Gallop.RaceDefine.RunningStyle runningStyle)
        {
            return _horseManager.GetRunningStyleLastOrderHorse(runningStyle);
        }

        //---------------------------------------------------------------
        public Gallop.RaceDefine.RunningStyle GetMostForwardRunningStyle()
        {
            return _horseManager.GetMostForwardRunningStyle();
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetMostForwardRunningStyleHorseArray()
        {
            return _horseManager.GetMostForwardRunningStyleHorseArray();
        }

        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetRunningStyleTemptationHorse(Gallop.RaceDefine.RunningStyle runningStyle)
        {
            return _horseManager.GetRunningStyleTemptationHorse(runningStyle);
        }
        
        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetTemptationHorseList()
        {
            return _horseManager.GetTemptationHorseList();
        }
        
        //---------------------------------------------------------------
        public Dictionary<IHorseRaceInfoSimulate, List<ISkillDetail>> GetPrevActivateSkillHorseDict()
        {
            return _horseManager.GetPrevActivateSkillHorseDict();
        }
        
        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetActivateHealSkillHorseHistoryList()
        {
            return _horseManager.GetActivateHealSkillHorseHistoryList();
        }
        
        //---------------------------------------------------------------
        public bool CheckOrderValid(int order)
        {
            return _horseManager.CheckOrderValid(order);
        }

        //---------------------------------------------------------------
        public bool CheckFinishOrderValid(int order)
        {
            return _horseManager.CheckFinishOrderValid(order);
        }

        //---------------------------------------------------------------
        public bool CheckPopularityValid(int order)
        {
            return _horseManager.CheckPopularityValid(order);
        }
        #endregion <キャラアクセスUtility>

        #region コースイベント
        public CourseParam[] GetCourseEventCorner()
        {
            return _courseEventManager.GetCourseEventCorner();
        }

        public CourseParam[] GetCourseEventStraight()
        {
            return _courseEventManager.GetCourseEventStraight();
        }

        public bool IsEnableFirstMoveLane => _courseEventManager.IsEnableFirstMoveLane;

        //---------------------------------------------------------------
        public float FirstMoveLanePointDistance => _courseEventManager.FirstMoveLanePointDistance;

        //---------------------------------------------------------------
        public bool IsFirstMoveLaneIsIn => _courseEventManager.IsFirstMoveLaneIsIn;
        //---------------------------------------------------------------
        public bool IsExistLastStraightEvent => _courseEventManager.IsExistLastStraightEvent;
        
        //---------------------------------------------------------------
        public List<CourseStraight> GetStraightList() { return _straightList; }

        //---------------------------------------------------------------
        public List<CourseStraight> GetStraightListByType(Gallop.RaceDefine.StraightFrontType type)
        {
            _straightDic.TryGetValue(type, out var retList);
            return retList;
        }

        //---------------------------------------------------------------
        public List<CourseCorner> GetCornerList() { return _cornerList; }
        //---------------------------------------------------------------
        public List<CourseSlope> GetSlopeList() { return _slopeList; }

        /// <summary>
        /// コーナー番号と現在距離から、キャッシュしたコーナー情報を取得。
        /// </summary>
        private CourseCorner GetCorner(int cornerNo, float distance)
        {
            if (null == _cornerList)
            {
                return null;
            }
            
            // コーナー番号が一致し、現在距離がコーナーの始点～終点に収まっているコーナー情報が求めるものである。
            int index = _cornerList.FindIndex((c) => c.cornerNumber == cornerNo && c.StartDistance <= distance && c.EndDistance >= distance);
            if (index < 0)
                return null;

            return _cornerList[index];
        }

        /// <summary>
        /// 現在のレースの直線リストキャッシュ。
        /// </summary>
        private void CacheStraights()
        {
            var straights = GetCourseEventStraight();
            if (null == straights)
            {
                Debug.Assert(false);
                return;
            }

            // このレースに存在する直線をリスト化。
            _straightList = new List<CourseStraight>();
            {
                CourseStraight curStraight = null;
                int frontCnt = 1;
                int acrossFrontCnt = 1;
                int falseStraightCnt = 1;
                foreach (var straightEvent in straights)
                {
                    if (!RaceUtil.GetStraightParam(straightEvent.Values, out var isStart, out var frontType, out var isLast))
                    {
                        return;
                    }

                    if (isStart)
                    {
                        Debug.Assert(null == curStraight);

                        // この直線タイプは何本目か？
                        int typeNumber = 0;
                        switch (frontType)
                        {
                            case Gallop.RaceDefine.StraightFrontType.Front:
                                typeNumber = frontCnt++;
                                break;
                            case Gallop.RaceDefine.StraightFrontType.AcrossFront:
                                typeNumber = acrossFrontCnt++;
                                break;
                            case Gallop.RaceDefine.StraightFrontType.FalseStraight:
                                typeNumber = falseStraightCnt++;
                                break;
                        }
                        
                        curStraight = new CourseStraight(
                            straightEvent._distance, 
                            -1, 
                            frontType, 
                            typeNumber,
                            isLast
                        );
                    }
                    else
                    {
                        if (null == curStraight)
                        {
                            Debug.LogError(string.Format("直線終了イベントの次に、再度直線終了イベントが打たれています。dist={0}", straightEvent._distance));
                            continue;
                        }
                        curStraight.EndDistance = straightEvent._distance;
                        curStraight.Range = curStraight.EndDistance - curStraight.StartDistance; 
                        _straightList.Add(curStraight);

                        // 次の直線に備えて(isStartに入ることを想定して)nullクリア。
                        curStraight = null;
                    }
                }
            }

            // 直線の種類ごとに保持。
            _straightDic = new Dictionary<Gallop.RaceDefine.StraightFrontType, List<CourseStraight>>(2)
            {
                { Gallop.RaceDefine.StraightFrontType.Front, _straightList.Where(s => s.frontType == Gallop.RaceDefine.StraightFrontType.Front).ToList() },
                { Gallop.RaceDefine.StraightFrontType.AcrossFront, _straightList.Where(s => s.frontType == Gallop.RaceDefine.StraightFrontType.AcrossFront).ToList() },
                { Gallop.RaceDefine.StraightFrontType.FalseStraight, _straightList.Where(s => s.frontType == Gallop.RaceDefine.StraightFrontType.FalseStraight).ToList() },
            };
        }

        /// <summary>
        /// 現在のレースのコーナーリストキャッシュ。
        /// </summary>
        private void CacheCorners()
        {
            // このレースに存在するコーナーをリスト化。
            var corners = GetCourseEventCorner();
            _cornerList = RaceUtil.CalcCornerList(corners, _raceInfo.CourseDistance);
#if CYG_DEBUG && UNITY_EDITOR
            _raceInfo.Corners = _cornerList;
#endif
        }

        /// <summary>
        /// 現在のレースの坂リストキャッシュ。
        /// </summary>
        private void CacheSlopes()
        {
            // このレースに存在する坂をリスト化。
            _slopeList = RaceUtil.CalcSlopeList(_courseEventManager.GetCourseEventSlope());
#if CYG_DEBUG && UNITY_EDITOR
            _raceInfo.SlopeList = _slopeList;
#endif
        }
        #endregion
    }
}
#endif

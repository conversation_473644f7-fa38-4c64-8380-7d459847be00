#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
using System.Collections.Generic;
using System.Linq;
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース初期化関連処理。
    /// </summary>
    //-------------------------------------------------------------------
    public class RaceInitializer
    {
        //---------------------------------------------------------------
        /// <summary>
        /// レースをロードする時に必要なパラメータ
        /// </summary>
        //---------------------------------------------------------------
        public class LoadRaceInfo
        {
            /// <summary>
            /// LoadRaceInfo生成用引数の構造体。
            /// </summary>
            public struct BuildParam
            {
                public int raceInstanceId;
                public RaceHorseSimulateData[] raceHorseDataList;
                public int randomSeed;
                public Gallop.RaceDefine.RaceType raceType;
                public Gallop.RaceDefine.Season season;
                public Gallop.RaceDefine.Weather weather;
                public Gallop.RaceDefine.GroundCondition groundCondition;
                public int raceProgramId; // single_mode_program.csvのId。
                public int OpponentEvaluate; // 対戦相手の評価値。
                public int SelfEvaluate; // 自チームの評価値。
                public int SupportCardScoreBonus; // サポートカードのレベルによるスコアボーナス。
                public int ScoreCalcTeamId; // スコアを計算するチームId。
                public Gallop.RaceDefine.Difficulty RaceDifficulty; // チャレンジマッチ/マンスリーマッチ難易度。
                public uint UnlockFlags; // 機能時限解放のビットフラグ。
                public Gallop.CourseSetInfoShared[] OverwriteCourseSetInfoArray;   // 【先バレ防止用】上書き用CourseSetInfo
#if CYG_DEBUG
                public int dbgTryCount { get; set; }
#endif

                /// <summary>
                /// コンストラクタ
                /// </summary>
                /// <param name="raceInstanceId">race_instance.csvのデータを指すId。</param>
                /// <param name="raceHorseDataList">出走するキャラデータ。</param>
                /// <param name="randomSeed">乱数シード。</param>
                /// <param name="raceType">レースタイプ指定。</param>
                /// <param name="season">季節。大抵の場合RaceUtil.GetSeason(raceInstanceId)で取得した値を渡すことになる。</param>
                /// <param name="weather">天気。</param>
                /// <param name="groundCondition">馬場状態。</param>
                /// <param name="simDataBase64">計算済みのbase64エンコードされたレースシナリオ。空文字列を渡してRaceMainViewに遷移した場合、レースのローカルシミュレートが行われる。</param>
                /// <param name="charaIdArray">キャラIdを外部から指定する場合はここで。nullであればRaceHorseDataのカードIdから決定される。</param>
                public BuildParam(
                    int raceInstanceId,
                    RaceHorseSimulateData[] raceHorseDataList,
                    int randomSeed,
                    Gallop.RaceDefine.RaceType raceType,
                    Gallop.RaceDefine.Season season,
                    Gallop.RaceDefine.Weather weather,
                    Gallop.RaceDefine.GroundCondition groundCondition,
                    int raceProgramId = 0,
                    int opponentEvaluate = 0,
                    int scoreCalcTeamId = Gallop.RaceDefine.TEAM_ID_NULL,
                    int supportCardScoreBonus = 0,
                    int selfEvaluate = 0,
                    Gallop.RaceDefine.Difficulty raceDifficulty = Gallop.RaceDefine.Difficulty.Easy,
                    uint unlockFlags = 0,
                    Gallop.CourseSetInfoShared[] overwriteCourseSetInfoArray = null)
                {
                    this.raceInstanceId = raceInstanceId;
                    this.raceHorseDataList = raceHorseDataList;
                    this.randomSeed = randomSeed;
                    this.raceType = raceType;
                    this.season = season;
                    this.weather = weather;
                    this.groundCondition = groundCondition;
                    this.raceProgramId = raceProgramId;
                    this.OpponentEvaluate = opponentEvaluate;
                    this.SelfEvaluate = selfEvaluate;
                    this.SupportCardScoreBonus = supportCardScoreBonus;
                    this.ScoreCalcTeamId = scoreCalcTeamId;
                    this.RaceDifficulty = raceDifficulty;
                    this.UnlockFlags = unlockFlags;
                    this.OverwriteCourseSetInfoArray = overwriteCourseSetInfoArray;
#if CYG_DEBUG
                    dbgTryCount = 0;
#endif
                }
            };

            /// <summary>
            /// レース情報を紐付けるためのID
            /// </summary>
            public int RaceInstanceId { get; private set; }

            /// <summary>
            /// シングルレースプログラムId。シングルレースでのみ有効。
            /// </summary>
            public int SingleRaceProgramId { get; private set; }

            /// <summary>
            /// 出走リスト。馬番で昇順にソート済み。
            /// </summary>
            public RaceHorseSimulateData[] SortedRaceHorseDataArray { get; private set; }

            /// <summary>
            /// 乱数シード
            /// </summary>
            public int RandomSeed { get; private set; }

            /// <summary>
            /// レース種類。
            /// </summary>
            public Gallop.RaceDefine.RaceType RaceType { get; private set; }

            /// <summary>
            /// 季節
            /// </summary>
            public Gallop.RaceDefine.Season Season { get; private set; }

            /// <summary>
            /// 天候（サーバーで抽選された値）。
            /// </summary>
            public Gallop.RaceDefine.Weather Weather { get; private set; }

            /// <summary>
            /// 馬場状態（サーバーで抽選された値）。
            /// </summary>
            public Gallop.RaceDefine.GroundCondition GroundCondition { get; private set; }

            /// <summary>
            /// 対戦相手の評価値。
            /// </summary>
            public int OpponentEvaluate { get; private set; }

            /// <summary>
            /// 自チームの評価値。
            /// </summary>
            public int SelfEvaluate { get; private set; }

            /// <summary>
            /// サポートカードのレベルによるスコアボーナス。
            /// </summary>
            public int SupportCardScoreBonus { get; set; }
            
            /// <summary>
            /// スコア計算が必要なチームId。
            /// </summary>
            public int ScoreCalcTeamId { get; private set; }

            /// <summary>
            /// チャレンジマッチ/マンスリーマッチの難易度。
            /// </summary>
            public Gallop.RaceDefine.Difficulty RaceDifficulty { get; set; }

            /// <summary>
            /// 機能時限解放のビットフラグ。
            /// </summary>
            public uint UnlockFlags { get; set; }
            
            /// <summary>
            /// 【先バレ防止用】上書き用CourseSetInfo
            /// </summary>
            public Gallop.CourseSetInfoShared[] OverwriteCourseSetInfoArray { get; private set; }

#if CYG_DEBUG
            public int DbgTryCount { get; set; }
            public void DbgSetRandomSeed(int seed)
            {
                RandomSeed = seed;
            }
            public void DbgSetRaceInstanceId(int raceInstanceId)
            {
                RaceInstanceId = raceInstanceId;
            }
            public void DbgSetSeason(Gallop.RaceDefine.Season season)
            {
                Season = season;
            }
            public void DbgSetWeather(Gallop.RaceDefine.Weather weather)
            {
                Weather = weather;
            }
            public void DbgSetGroundCondition(Gallop.RaceDefine.GroundCondition groundCondition)
            {
                GroundCondition = groundCondition;
            }
#endif

            /// <summary>
            /// コンストラクタ
            /// </summary>
            public LoadRaceInfo(ref BuildParam buildParam)
            {
                Init(
                    raceInstanceId: buildParam.raceInstanceId,
                    raceHorseDataList: buildParam.raceHorseDataList,
                    randomSeed: buildParam.randomSeed,
                    raceType: buildParam.raceType,
                    season: buildParam.season,
                    weather: buildParam.weather,
                    groundCondition: buildParam.groundCondition,
                    raceProgramId: buildParam.raceProgramId,
                    opponentEvaluate: buildParam.OpponentEvaluate,
                    supportCardScoreBonus: buildParam.SupportCardScoreBonus,
                    scoreCalcTeamId: buildParam.ScoreCalcTeamId,
                    selfEvaluate: buildParam.SelfEvaluate,
                    raceDifficulty: buildParam.RaceDifficulty,
                    unlockFlags: buildParam.UnlockFlags,
                    overwriteCourseSetInfoArray: buildParam.OverwriteCourseSetInfoArray);
#if CYG_DEBUG
                DbgTryCount = buildParam.dbgTryCount;
#endif
            }

            private void Init(
                int raceInstanceId,
                RaceHorseSimulateData[] raceHorseDataList,
                int randomSeed,
                Gallop.RaceDefine.RaceType raceType,
                Gallop.RaceDefine.Season season,
                Gallop.RaceDefine.Weather weather,
                Gallop.RaceDefine.GroundCondition groundCondition,
                int raceProgramId,
                int opponentEvaluate,
                int supportCardScoreBonus,
                int scoreCalcTeamId,
                int selfEvaluate,
                Gallop.RaceDefine.Difficulty raceDifficulty,
                uint unlockFlags,
                Gallop.CourseSetInfoShared[] overwriteCourseSetInfoArray)
            {
                SortedRaceHorseDataArray = raceHorseDataList;
                RaceUtil.SortRaceHorseDataAsc(SortedRaceHorseDataArray);

                RaceInstanceId = raceInstanceId;
                RandomSeed = randomSeed;
                RaceType = raceType;
                Season = season;
                Weather = weather;
                GroundCondition = groundCondition;
                SingleRaceProgramId = raceProgramId;
                OpponentEvaluate = opponentEvaluate;
                SupportCardScoreBonus = supportCardScoreBonus;
                ScoreCalcTeamId = scoreCalcTeamId;
                SelfEvaluate = selfEvaluate;
                RaceDifficulty = raceDifficulty;
                UnlockFlags = unlockFlags;
                OverwriteCourseSetInfoArray = overwriteCourseSetInfoArray;
            }
        }
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        //---------------------------------------------------------------
        /// <summary>
        /// レースデータの作成、RaceScene遷移より前に呼ばれる
        /// </summary>
        //---------------------------------------------------------------
        public static RaceInfo CreateRaceInfo(LoadRaceInfo loadRaceInfo)
        {
            return new RaceInfo(loadRaceInfo);
        }
#endif
    }
}
#endif
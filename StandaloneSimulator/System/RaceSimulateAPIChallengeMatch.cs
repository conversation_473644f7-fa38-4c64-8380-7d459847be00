using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using MessagePack;

namespace StandaloneSimulator
{
    /*
     * ここに定義するシリアライズの順番を変更するときは、以下の書類も更新すること。
     * https://xxxxxxxxxx/pages/viewpage.action?pageId=121814068
     */

    //-------------------------------------------------------------------
    /// <summary>
    /// チャレンジマッチ：獲得したポイント１件当たりのデータ。
    /// </summary>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class ChallengeMatchPointData
    {
        public int raw_point_id { get; set; } // challenge_match_raw_point.csvのidです。
        public int num { get; set; } // このポイントを獲得した回数。
        public int point { get; set; } // 獲得したポイント。ボーナス込みの値。
        public int point_bonus { get; set; } // ボーナス。

        public int RawPoint => point - point_bonus;
        public bool HasBonus => point_bonus > 0;

        public ChallengeMatchPointData(int raw_point_id) : this(raw_point_id, 0, 0, 0)
        {
        }
        public ChallengeMatchPointData(int raw_point_id, int num, int point, int bonus)
        {
            this.raw_point_id = raw_point_id;
            this.num = num;
            this.point = point;
            this.point_bonus = bonus;
        }

        public void Add(int point)
        {
            this.point += point;
            num++;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// チャレンジマッチ：１キャラがこのレースで獲得したポイント。
    /// </summary>
    //-------------------------------------------------------------------
    [MessagePackObject(true)]
    public class ChallengeMatchHorsePoint
    {
        public ChallengeMatchPointData[] challenge_match_point_array { get; set; } // 獲得したポイント。
    }
}

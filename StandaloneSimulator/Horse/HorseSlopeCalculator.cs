using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 坂/勾配関連の計算処理。
    /// </summary>
    /// <remarks>
    /// 勾配角度及び%について
    /// ・上り坂：正の値（+3%など）
    /// ・下り坂：負の値（-3%など）
    /// </remarks>
    //-------------------------------------------------------------------
    public static class HorseSlopeCalculator
    {
        /// <summary>
        /// オイラー角Xを勾配角度に変換。
        /// </summary>
        /// <param name="eulerX">オイラー角。-360 ~ 360の範囲内の値を渡す。</param>
        /// <returns>-180 ~ 180の範囲で返却。</returns>
        /// <seealso cref="Test.TestHorseSlopeCalculator.TestClampEulerX2SlopeAngle(float, float)"/>
        public static float ClampEulerX2SlopeAngle( float eulerX )
        {
            if( eulerX < -360.0f || eulerX > 360.0f )
            {
                Debug.LogError( string.Format( "eulerXが不正。eulerX={0}", eulerX ) );
                return 0;
            }

            float retAngle = eulerX;

            // -180 ~ 180に収める。
            if( retAngle > 180.0f )
            {
                retAngle -= 360.0f;
            }
            if( retAngle < -180.0f )
            {
                retAngle += 360.0f;
            }

            // オイラー角Xと勾配角度は符号が逆。
            return -retAngle;
        }

        /// <summary>
        /// 勾配角度から坂種類取得。
        /// </summary>
        public static Gallop.RaceDefine.SlopeType SlopePer2SlopeType(float slopePer)
        {
            if (slopePer > 0)
            {
                return Gallop.RaceDefine.SlopeType.Up;
            }
            if (slopePer < 0)
            {
                return Gallop.RaceDefine.SlopeType.Down;
            }
            return Gallop.RaceDefine.SlopeType.Null;
        }

        /// <summary>
        /// 上り坂での速度加算値計算。
        /// </summary>
        /// <param name="pow">パワー。</param>
        /// <param name="slopePer">勾配%。</param>
        /// <seealso cref="Test.TestHorseSlopeCalculator.TestCalcUpSlopeAddSpeed(float, float, float)"/>
        public static float CalcUpSlopeAddSpeed( Gallop.RaceParamDefine.SlopeParam paramDefine, float pow, float slopePer )
        {
            if( slopePer <= 0.0f )
            {
                Debug.LogError( "勾配%に下り坂もしくは0が指定されている。slopePer=" + slopePer );
                return 0.0f;
            }

            float retAdd = -(paramDefine.UpSlopeAddSpeedVal1 / pow * Math.Abs(slopePer));
            // 正の値にならないように上限は0とする。
            if( retAdd > 0 )
            {
                retAdd = 0;
            }
            return retAdd;
        }

        /// <summary>
        /// 下り坂での速度加算値計算。
        /// </summary>
        /// <param name="slopePer">勾配%。</param>
        /// <seealso cref="Test.TestHorseSlopeCalculator.TestCalcDownSlopeAddSpeed(float, float)"/>
        public static float CalcDownSlopeAddSpeed( Gallop.RaceParamDefine.SlopeParam paramDefine, float slopePer )
        {
            if( slopePer >= 0.0f )
            {
                Debug.LogError( "勾配%に上り坂もしくは0が指定されている。slopePer=" + slopePer );
                return 0.0f;
            }

            float retAdd = paramDefine.DownSlopeAddSpeedVal1 + ( Math.Abs( slopePer ) / paramDefine.DownSlopeAddSpeedVal2 );
            // 負の値にならないように下限は0とする。
            if( retAdd < 0 )
            {
                retAdd = 0;
            }
            return retAdd;
        }

        /// <summary>
        /// 下り坂での速度加算モード開始確率。
        /// </summary>
        /// <param name="wiz">賢さ。</param>
        /// <returns>抽選確率を百分率で返却。</returns>
        /// <seealso cref="Test.TestHorseSlopeCalculator.TestCalcDownSlopeAccelStartPer(float, float)"/>
        public static float CalcDownSlopeAccelStartPer( Gallop.RaceParamDefine.SlopeParam paramDefine, float wiz )
        {
            float per = wiz * paramDefine.DownSlopeAddSpeedStartWizRate;
            if( per > 100 )
            {
                per = 100;
            }
            return per;
        }
        /// <summary>
        /// 下り坂での速度加算モード終了確率。
        /// </summary>
        /// <returns>抽選確率を百分率で返却。</returns>
        public static float CalcDownSlopeAccelEndPer( Gallop.RaceParamDefine.SlopeParam paramDefine )
        {
            return paramDefine.DownSlopeAddSpeedEndPer;
        }
    }
}

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算実処理インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ITargetLaneStrategy
    {
        /// <summary>
        /// 狙いたいレーン計算。
        /// </summary>
        /// <returns>計算結果のレーンを返却。</returns>
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        float SearchSimulate();
#endif
#if GALLOP
        float Search();
#endif
        
    }
}

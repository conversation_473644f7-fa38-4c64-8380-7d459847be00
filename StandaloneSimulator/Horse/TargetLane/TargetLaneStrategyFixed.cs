#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算実処理：スキルにより移動先が固定されている時。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class TargetLaneStrategyFixed : TargetLaneStrategyBase
    {
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public TargetLaneStrategyFixed( 
            IHorseRaceInfoSimulate ownerHorse, 
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine ) : base( ownerHorse, ownerAI, ownerLaneCalc, paramDefine )
        {
        }

        //---------------------------------------------------------------
        public override float SearchSimulate()
        {
            var targetLane = _ownerHorseSimulate.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TargetLane, 0);
            Debug.Assert(targetLane >= 0.0f & targetLane <= 1.5f); // レーンの最大幅は可変だが、最大でも1.5。

        #if false // 2019/04/18 レーン直値に変更して様子を見る。
            // スキルで指定される値はレーン最大値に対する比率のため、レーン直値に変換する。
            targetLane *= _ownerHorse.LaneDistanceMax;
        #endif

            if (OwnerLaneSimulate > targetLane)
            {
                if (_ownerAI.IsEnableInMove)
                {
                    return AddLaneByDirectionSimulate( true, targetLane );
                }
            }
            else if (OwnerLaneSimulate < targetLane)
            {
                if (_ownerAI.IsEnableOutMove)
                {
                    return AddLaneByDirectionSimulate( false, targetLane );
                }
            }

            return OwnerLaneSimulate;
        }
        
#if GALLOP
        public override float Search()
        {
            // 参照されることはない
            Debug.LogError("意図しない参照が発生しています");
            return 0f;
        }
#endif
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算実処理：通常時。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class TargetLaneStrategyNormal : TargetLaneStrategyBase
    {
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public TargetLaneStrategyNormal( 
            IHorseRaceInfoSimulate ownerHorse, 
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine ) : base( ownerHorse, ownerAI, ownerLaneCalc, paramDefine )
        {
        }

        //---------------------------------------------------------------
        public override float SearchSimulate()
        {
            // Hp切れて失速しているときはレーン移動しない。
            if (_ownerHorseSimulate.GetHp() <= 0)
            {
                return OwnerLaneSimulate;
            }

            // ペースダウンする場合、ある程度外に回る。
            if (_ownerAI.PositionKeepMode == Gallop.RaceDefine.PositionKeepMode.PaseDown)
            {
                return _paramDefine.PositionKeep.PositionKeepPaseDownTargetLane;
            }

            // ラストコーナー明けでアウト側に広がる。
            if (_ownerLaneCalc.IsUseExtraMove)
            {
                if (OwnerLaneSimulate < _ownerLaneCalc.ExtraMoveLane)
                {
                    if (_ownerAI.IsEnableOutMove)
                    {
                        return AddLaneByDirectionSimulate( false, _ownerLaneCalc.ExtraMoveLane );
                    }
                }
            }
            // 強制レーン移動イベント通過後、中盤まで。
            else if (OwnerPhaseSimulate <= Gallop.RaceDefine.HorsePhase.MiddleRun)
            {
                // インががら空きの時だけインに寄る
                if (_ownerAI.IsEnableInMove)
                {
                    return AddLaneByDirectionSimulate( true, OwnerLaneSimulate - MOVE_LANE_DIRECTION_DELTA );
                }
                // 中盤では、インを誰かにブロックされていて、サイドブロック幅よりちょい内側に入っているなら、外へ移動する。
                // ※序盤でもこの処理を入れると、出走直後サイドブロック状態のために広がってしまう。
                else if (OwnerPhaseSimulate == Gallop.RaceDefine.HorsePhase.MiddleRun)
                {
                    var inBlock = _ownerAI.GetBlockHorseIn();
                    if (null != inBlock)
                    {
                        // サイドブロック幅よりちょい内側の範囲内に近付いているなら、外へ。
                        // ※サイドブロック幅で判定しないのは、サイドブロックの外に出る→インへ近づいてサイドブロックになる→サイドブロックの外に出る…が繰り返さないようにするため。
                        const float MOVE_OUT_LANE_ADD = Gallop.RaceDefine.HorseLane_Quarters;
                        if (inBlock.laneGapAbs < (_paramDefine.Block.SideBlockLaneGap - MOVE_OUT_LANE_ADD))
                        {
                            // 移動先は「インをブロックしているキャラのレーン＋サイドブロック幅」のレーンまで。
                            float moveOutDestLane = inBlock.infoSimulate.GetLaneDistance() + _paramDefine.Block.SideBlockLaneGap;
                            return AddLaneByDirectionSimulate( false, moveOutDestLane );
                        }
                    }
                }
            }
            return OwnerLaneSimulate;
        }
        
#if GALLOP
        public override float Search()
        {
            // 参照されることはない
            Debug.LogError("意図しない参照が発生しています");
            return 0f;
        }
#endif
        
    }
}
#endif

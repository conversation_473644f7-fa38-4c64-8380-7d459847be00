using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算実処理：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class TargetLaneStrategyBase : ITargetLaneStrategy
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        protected const float MOVE_LANE_DIRECTION_DELTA = 0.05f;
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        // シミュレーター用
        protected readonly IHorseRaceInfoSimulate _ownerHorseSimulate;
        protected float OwnerDistanceSimulate { get { return _ownerHorseSimulate.GetDistance(); } }
        protected float OwnerLaneSimulate { get { return _ownerHorseSimulate.GetLaneDistance(); } }
        protected Gallop.RaceDefine.HorsePhase OwnerPhaseSimulate { get { return _ownerHorseSimulate.GetPhase(); } }
#endif
#if GALLOP
        // レース再生用（オーバーランなど）
        protected readonly Gallop.IHorseRaceInfo _ownerHorse;
        protected float OwnerDistance { get { return _ownerHorse.GetDistance(); } }
        protected float OwnerLane { get { return _ownerHorse.GetLaneDistance(); } }
        protected Gallop.RaceDefine.HorsePhase OwnerPhase { get { return (Gallop.RaceDefine.HorsePhase)_ownerHorse.GetPhase(); } }
#endif
        protected readonly IHorseRaceAI _ownerAI;
        protected readonly IHorseTargetLaneCalculator _ownerLaneCalc;
        protected readonly Gallop.RaceParamDefine _paramDefine;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        // シミュレーター用
        public TargetLaneStrategyBase( 
            IHorseRaceInfoSimulate ownerHorse, 
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine )
        {
            _ownerHorseSimulate = ownerHorse;
            _ownerAI = ownerAI;
            _ownerLaneCalc = ownerLaneCalc;
            _paramDefine = paramDefine;
        }
        
        public abstract float SearchSimulate();
        
        protected float AddLaneByDirectionSimulate(bool isIn, float targetLane)
        {
            float newTargetLane = 0.0f;
            if (isIn)
            {
                newTargetLane = OwnerLaneSimulate - MOVE_LANE_DIRECTION_DELTA;
                if (newTargetLane < targetLane)
                {
                    newTargetLane = targetLane;
                }
            }
            else
            {
                newTargetLane = OwnerLaneSimulate + MOVE_LANE_DIRECTION_DELTA;
                if (newTargetLane > targetLane)
                {
                    newTargetLane = targetLane;
                }
            }
            return newTargetLane;
        }
#endif
#if GALLOP
        // レース再生用（オーバーランなど）
        public TargetLaneStrategyBase( 
            Gallop.IHorseRaceInfo ownerHorse,
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine )
        {
            _ownerHorse = ownerHorse;
            _ownerAI = ownerAI;
            _ownerLaneCalc = ownerLaneCalc;
            _paramDefine = paramDefine;
        }
        
        public abstract float Search();
        
        protected float AddLaneByDirection(bool isIn, float targetLane)
        {
            float newTargetLane = 0.0f;
            if (isIn)
            {
                newTargetLane = OwnerLane - MOVE_LANE_DIRECTION_DELTA;
                if (newTargetLane < targetLane)
                {
                    newTargetLane = targetLane;
                }
            }
            else
            {
                newTargetLane = OwnerLane + MOVE_LANE_DIRECTION_DELTA;
                if (newTargetLane > targetLane)
                {
                    newTargetLane = targetLane;
                }
            }
            return newTargetLane;
        }
#endif
    }
}

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算実処理: Nullオブジェクト
    /// </summary>
    //-------------------------------------------------------------------
    public class TargetLaneStrategyNull : TargetLaneStrategyBase
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public TargetLaneStrategyNull(
            IHorseRaceInfoSimulate ownerHorse,
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine) : base(ownerHorse, ownerAI, ownerLaneCalc, paramDefine) { }

        /// <summary> 狙いたいレーン位置を計算する </summary>
        /// <remarks> 0を返すと内埒に寄ってしまうので現在のレーン位置を返すことで横移動を行わない形にする </remarks>
        public override float SearchSimulate() { return OwnerLaneSimulate; }
#endif
#if GALLOP
        public TargetLaneStrategyNull( 
            Gallop.IHorseRaceInfo ownerHorse,
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine ) : base(ownerHorse, ownerAI, ownerLaneCalc, paramDefine) { }

        /// <summary> 狙いたいレーン位置を計算する </summary>
        /// <remarks> 0を返すと内埒に寄ってしまうので現在のレーン位置を返すことで横移動を行わない形にする </remarks>
        public override float Search() { return OwnerLane; }
#endif

    }
}
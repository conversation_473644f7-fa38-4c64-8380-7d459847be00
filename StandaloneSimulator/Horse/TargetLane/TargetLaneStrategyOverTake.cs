using System;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算実処理：追い越し中。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class TargetLaneStrategyOverTake : TargetLaneStrategyBase
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly List<float> _overtakeLaneCandidate = new List<float>(8);

        /// <summary> 外埒沿い配置のレースかどうか。これがtrueだとインを優先して追い抜きしなくなる </summary>
        private bool _isOuterPlaceRace = false;
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public TargetLaneStrategyOverTake( 
            IHorseRaceInfoSimulate ownerHorse, 
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine,
            bool isOuterPlaceRace) : base( ownerHorse, ownerAI, ownerLaneCalc, paramDefine )
        {
            _isOuterPlaceRace = isOuterPlaceRace;
        }
#endif
#if GALLOP
        public TargetLaneStrategyOverTake( 
            Gallop.IHorseRaceInfo ownerHorse, 
            IHorseRaceAI ownerAI,
            IHorseTargetLaneCalculator ownerLaneCalc,
            Gallop.RaceParamDefine paramDefine,
            bool isOuterPlaceRace) : base( ownerHorse, ownerAI, ownerLaneCalc, paramDefine )
        {
            _isOuterPlaceRace = isOuterPlaceRace;
        }
#endif
        

        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public override float SearchSimulate()
        {
            // 追い越し対象がいない=クールダウン中は現状のレーンを維持。
            if (!_ownerAI.IsOverTakeOrCoolDown())
            {
                return OwnerLaneSimulate;
            }

            _overtakeLaneCandidate.Clear();

            //-----------------------------------------------------------
            // 追い越し対象達を追い抜くためのレーン候補を収集。
            //-----------------------------------------------------------
            float overtakeNearDistance = float.MaxValue;
            const float NEAR_LANEDISTANCE = Gallop.RaceDefine.HorseLane_Two; // 追い越し対象に隣接しているとみなすレーン幅。
            const float OVERTAKE_LANE_CHECK_DIST_OVER = 0.5f;
            const float OVERTAKE_LANE_SPACE = Gallop.RaceDefine.HorseLane_OneAndOneQuarters * 0.8f;    // 追い越し相手を追い越すとき、このレーン離れて追い越す。
            const float OVERTAKE_LANE_CANDIDATE_RANGE = OVERTAKE_LANE_SPACE * 0.8f; // 追い越し相手を追い越すときに、OVERTAKE_LANE_SPACEのイン/アウトにこの空白が無いと追い越しレーンの対象に入れない。

            {
                var overtakeHorse = _ownerAI.GetOverTakeHorse();
                for (int i = 0; i < overtakeHorse.Count; ++i)
                {
                    var horse = overtakeHorse[i];
                    var inHorse = horse.infoSimulate;
                    var outHorse = horse.infoSimulate;

                    // 追い抜き対象の中で最も近いキャラのDistanceを保存。
                    if (overtakeNearDistance > horse.infoSimulate.GetDistance())
                    {
                        overtakeNearDistance = horse.infoSimulate.GetDistance();
                    }

                    // 追い抜き対象に隣接する最も内側のキャラ、及びそのキャラを内側から抜かすときのレーンを取得。
                    inHorse = CalcInHorseRecursivelySimulate(inHorse, NEAR_LANEDISTANCE);
                    float inLane = inHorse.GetLaneDistance() - OVERTAKE_LANE_SPACE;

                    // 追い抜き対象に隣接する最も外側のキャラ、及びそのキャラを外側から抜かすときのレーンを取得。
                    outHorse = CalcOutHorseRecursivelySimulate(outHorse, NEAR_LANEDISTANCE);
                    float outLane = outHorse.GetLaneDistance() + OVERTAKE_LANE_SPACE;

                    // 内側から抜かすときのレーンが移動可能なものかどうか。
                    if (inLane >= _ownerHorseSimulate.LaneDistanceMin)
                    {
                        // 自分のDistanceからinHorseのDistance+alphaまでの間に、inLane上にキャラがいないなら、追い抜きレーン候補に加える。
                        if (!CheckHorseExistSimulate(_ownerAI.VisibleHorses,
                                                OwnerDistanceSimulate,
                                                inHorse.GetDistance() + OVERTAKE_LANE_CHECK_DIST_OVER,
                                                inLane - OVERTAKE_LANE_CANDIDATE_RANGE,
                                                inLane + OVERTAKE_LANE_CANDIDATE_RANGE))
                        {
                            _overtakeLaneCandidate.Add(inLane);
                        }
                    }

                    // 外側から抜かすときのレーンが移動可能なものかどうか。
                    if (outLane <= _ownerHorseSimulate.LaneDistanceMax)
                    {
                        // 自分のDistanceからoutHorseのDistance+alphaまでの間に、outLane上にキャラがいないなら、追い抜きレーン候補に加える。
                        if (!CheckHorseExistSimulate(_ownerAI.VisibleHorses,
                                                OwnerDistanceSimulate,
                                                outHorse.GetDistance() + OVERTAKE_LANE_CHECK_DIST_OVER,
                                                outLane - OVERTAKE_LANE_CANDIDATE_RANGE,
                                                outLane + OVERTAKE_LANE_CANDIDATE_RANGE))
                        {
                            _overtakeLaneCandidate.Add(outLane);
                        }
                    }
                }
            }

            //-----------------------------------------------------------
            // 中盤までは、自分の内側から追い抜けるならそのレーンを候補に加える。
            // 終盤以降は、自分の現在レーンで追い抜けるなら、そのレーンを候補に加える。
            //-----------------------------------------------------------
            const float OVERTAKE_OWNERLANE_TARGET_ADD_DIST = 3.0f;
            const float OVERTAKE_INMOVE_LANEOFFSET = Gallop.RaceDefine.HorseLane_One;
            if (OwnerPhaseSimulate <= Gallop.RaceDefine.HorsePhase.MiddleRun &&
                _ownerAI.IsEnableInMove &&
                OwnerLaneSimulate > OVERTAKE_INMOVE_LANEOFFSET)
            {
                if (!CheckHorseExistSimulate(_ownerAI.VisibleHorses,
                                        OwnerDistanceSimulate,
                                        overtakeNearDistance + OVERTAKE_OWNERLANE_TARGET_ADD_DIST,
                                        OwnerLaneSimulate - OVERTAKE_LANE_CANDIDATE_RANGE - OVERTAKE_INMOVE_LANEOFFSET,
                                        OwnerLaneSimulate + OVERTAKE_LANE_CANDIDATE_RANGE - OVERTAKE_INMOVE_LANEOFFSET))
                {
                    _overtakeLaneCandidate.Add(OwnerLaneSimulate - OVERTAKE_INMOVE_LANEOFFSET);
                }
            }
            else
            {
                if (!CheckHorseExistSimulate(_ownerAI.VisibleHorses,
                                        OwnerDistanceSimulate,
                                        overtakeNearDistance + OVERTAKE_OWNERLANE_TARGET_ADD_DIST,
                                        OwnerLaneSimulate - OVERTAKE_LANE_CANDIDATE_RANGE,
                                        OwnerLaneSimulate + OVERTAKE_LANE_CANDIDATE_RANGE))
                {
                    _overtakeLaneCandidate.Add(OwnerLaneSimulate);
                }
            }

            //-----------------------------------------------------------
            // どのレーンで追い抜くかを調べる。
            // 基本的には、現在レーンから一番近い、移動可能なレーンを追い抜きレーンとして選出する。
            // スキルやレースの展開により、イン/アウトどちらかから抜くことを優先したい場合、
            // 距離に対して重み付けすることで優先度を実現している。
            //-----------------------------------------------------------
            float ownerLane = OwnerLaneSimulate;
            float nearOvertakeLane = ownerLane;
            float laneGapAbsToNear = float.MaxValue;
            for (int i = 0; i < _overtakeLaneCandidate.Count; ++i)
            {
                var lane = _overtakeLaneCandidate[i];

                // 現在レーンから指定レーンへの距離を調べる。
                // スキルなどにより距離への重み付けも行う。
                // ※ここでいう重みとは、実際のレーン距離に対する係数として扱うため、大きいほど優先度は低くなる。
                float laneGap = ownerLane - lane;
                float laneGapCoefByPhase = CalcLaneGapCoefByPhaseSimulate(laneGap);
                float laneGapCoefBySkill = CalcLaneGapCoefBySkillSimulate(laneGap);
                float laneGapAbs = Math.Abs(laneGap * laneGapCoefByPhase * laneGapCoefBySkill);

                // 現在レーンから一番近いレーンでなければ対象外。
                if (laneGapAbs >= laneGapAbsToNear)
                {
                    continue;
                }

                // 物理的に指定レーンへは移動できない。
                if (!CheckEnableMoveLaneSimulate(lane))
                {
                    continue;
                }

                nearOvertakeLane = lane;
                laneGapAbsToNear = laneGapAbs;
            }

            //-----------------------------------------------------------
            // ラストコーナー明けでアウトに広がりたい場合、
            // 追い越しで今のレーンより外に行く＆アウトに広がる先が追い越しレーンよりもアウト、であれば、
            // アウトに広がる方を優先する。
            //-----------------------------------------------------------
            if (_ownerLaneCalc.IsUseExtraMove)
            {
                // アウトから追い抜く。
                if (OwnerLaneSimulate <= nearOvertakeLane)
                {
                    // 追い抜きレーンよりもアウトに広がりたい。
                    if (nearOvertakeLane < _ownerLaneCalc.ExtraMoveLane)
                    {
                        // 広がる先のレーンへ移動可能なら。
                        if (CheckEnableMoveLaneSimulate(_ownerLaneCalc.ExtraMoveLane))
                        {
                            // 目標レーンの上書き。
                            nearOvertakeLane = _ownerLaneCalc.ExtraMoveLane;
                        }
                    }
                }
            }
            else if( _ownerLaneCalc.IsTargetLaneFixed() )
            {
                // アウトから追い抜く。
                if (OwnerLaneSimulate <= nearOvertakeLane)
                {
                    // 追い抜きレーンよりもアウトに広がりたい。
                    var targetLane = _ownerHorseSimulate.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TargetLane, 0);
                    if (nearOvertakeLane < targetLane)
                    {
                        // 広がる先のレーンへ移動可能なら。
                        if (CheckEnableMoveLaneSimulate(targetLane))
                        {
                            // 目標レーンの上書き。
                            nearOvertakeLane = targetLane;
                        }
                    }
                }
            }

            //_ownerLaneCalc.TargetLane = nearOvertakeLane;

            return nearOvertakeLane;
        }

        /// <summary>
        /// レース展開による追い抜きレーンの重みを取得。
        /// </summary>
        /// <returns>laneGapに対する係数を重みとして返却。大きいほど優先度の低いレーンとして扱う。</returns>
        private float CalcLaneGapCoefByPhaseSimulate(float laneGap)
        {
            const float PHASE_START_COEF = 100.0f;
            const float PHASE_DEFAULT_COEF = 1.0f;

            // 外埒沿いに走るレースではインを優先とかはしなくする
            if (_isOuterPlaceRace)
            {
                return PHASE_DEFAULT_COEF;
            }

            // 序盤。
            if (OwnerPhaseSimulate == Gallop.RaceDefine.HorsePhase.Start)
            {
                // 必ずインから抜きたい。
                if (laneGap > 0)
                {
                    return PHASE_START_COEF;
                }
            }
            // 終盤以降。
            else if (OwnerPhaseSimulate >= Gallop.RaceDefine.HorsePhase.End)
            {
                // イン/アウト双方に重み付け。※終盤以降インから抜く傾向付けをしたいという要望のため。
                return laneGap > 0 ? _paramDefine.overTakeOutLaneCoefEnd : _paramDefine.overTakeInLaneCoefEnd;
            }

            return PHASE_DEFAULT_COEF;
        }

        /// <summary>
        /// スキル効果による追い抜きレーンの重みを取得。
        /// </summary>
        /// <returns>laneGapに対する係数を重みとして返却。大きいほど優先度の低いレーンとして扱う。</returns>
        private float CalcLaneGapCoefBySkillSimulate(float laneGap)
        {
            const float SKILL_INOUT_COEF = 1.5f;
            const float SKILL_DEFAULT_COEF = 1.0f;

            // ※※※ 先に判定を行うスキルほど、優先度が高い。 ※※※
            bool isForceOvertakeIn = _ownerHorseSimulate.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.ForceOvertakeIn, false);
            bool isForceOvertakeOut = _ownerHorseSimulate.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.ForceOvertakeOut, false);

            //-----------------------------------------------------------
            // イン優先/アウト優先。
            //-----------------------------------------------------------
            if (isForceOvertakeIn != isForceOvertakeOut)
            {
                // イン優先。
                if (isForceOvertakeIn)
                {
                    // アウト側への回避には重み付けを行う。
                    if (laneGap > 0)
                    {
                        return SKILL_INOUT_COEF;
                    }
                }
                // アウト優先。
                if (isForceOvertakeOut)
                {
                    // イン側への回避には重み付けを行う。
                    if (laneGap < 0)
                    {
                        return SKILL_INOUT_COEF;
                    }
                }
            }

            return SKILL_DEFAULT_COEF;
        }

        /// <summary>
        /// 指定のレーンへ移動可能かどうか。
        /// </summary>
        private bool CheckEnableMoveLaneSimulate(float lane)
        {
            if (lane < RaceInfo.LaneDistanceMin || lane > _ownerHorseSimulate.LaneDistanceMax)
            {
                return false;
            }

            float laneGap = lane - OwnerLaneSimulate;
            if (laneGap < 0)
            {
                if (!_ownerAI.IsEnableInMove || !_ownerAI.CheckLaneSpace(laneGap))
                {
                    return false;
                }
            }
            else if (laneGap > 0)
            {
                if (!_ownerAI.IsEnableOutMove || !_ownerAI.CheckLaneSpace(laneGap))
                {
                    return false;
                }
            }

            return true;
        }

        private static bool CheckHorseExistSimulate(Gallop.AroundHorse[] horses, float distanceMin, float distanceMax, float laneDistanceMin, float laneDistanceMax)
        {
            foreach (var horse in horses)
            {
                if (null == horse)
                {
                    continue;
                }

                var horseInfo = horse.infoSimulate;

                float horseDistance = horseInfo.GetDistance();
                float horseLaneDistance = horseInfo.GetLaneDistance();

                if (horseDistance < distanceMin ||
                    horseDistance > distanceMax)
                {
                    continue;
                }
                if (horseLaneDistance < laneDistanceMin ||
                    horseLaneDistance > laneDistanceMax)
                {
                    continue;
                }
                return true;
            }

            return false;
        }

        private static IHorseRaceInfoSimulate CalcInHorseSimulate(IHorseRaceInfoSimulate horse, float laneGap)
        {
            if (null == horse)
            {
                return null;
            }

            const float NEAR_DISTANCE_GAP_ABS = /*GameDefine.HorseLength_One*/Gallop.RaceDefine.HorseLength_Three;

            float minLane = horse.GetLaneDistance();
            IHorseRaceInfoSimulate minHorse = null;

            var arounds = horse.GetAroundHorses();
            for (int i = 0; i < arounds.Length; ++i)
            {
                var around = arounds[i];

                // 相手が後ろにいるか、一定距離以上離れているなら無視。
                if (around.distanceGap < 0 || around.distanceGap > NEAR_DISTANCE_GAP_ABS)
                {
                    continue;
                }
                // 外側にいるなら無視。
                if (around.laneGap >= 0)
                {
                    continue;
                }
                // 指定レーン距離以上離れているなら無視。
                if (around.laneGapAbs >= laneGap)
                {
                    continue;
                }
                // 最も内側と判定されているレーンより外にいるなら無視。
                if (minLane <= around.infoSimulate.GetLaneDistance())
                {
                    continue;
                }

                minLane = around.infoSimulate.GetLaneDistance();
                minHorse = around.infoSimulate;
            }

            return minHorse;
        }
        private static IHorseRaceInfoSimulate CalcOutHorseSimulate(IHorseRaceInfoSimulate horse, float laneGap)
        {
            if (null == horse)
            {
                return null;
            }

            const float NEAR_DISTANCE_GAP_ABS = /*GameDefine.HorseLength_One*/Gallop.RaceDefine.HorseLength_Three;

            float maxLane = horse.GetLaneDistance();
            IHorseRaceInfoSimulate maxHorse = null;

            var arounds = horse.GetAroundHorses();
            foreach (var around in arounds)
            {
                // 相手が後ろにいるか、一定距離以上離れているなら無視。
                if (around.distanceGap < 0 || around.distanceGap > NEAR_DISTANCE_GAP_ABS)
                {
                    continue;
                }
                // 内側にいるなら無視。
                if (around.laneGap <= 0)
                {
                    continue;
                }
                // 指定レーン距離以上離れているなら無視。
                if (around.laneGapAbs >= laneGap)
                {
                    continue;
                }
                // 最も外側と判定されているレーンより内にいるなら無視。
                if (maxLane >= around.infoSimulate.GetLaneDistance())
                {
                    continue;
                }

                maxLane = around.infoSimulate.GetLaneDistance();
                maxHorse = around.infoSimulate;
            }

            return maxHorse;
        }
        private IHorseRaceInfoSimulate CalcInHorseRecursivelySimulate(IHorseRaceInfoSimulate horse, float laneGap)
        {
            var inHorse = CalcInHorseSimulate(horse, laneGap);

            // horseより内側のキャラはいない。
            if (null == inHorse)
            {
                return horse;
            }

            // inHorseよりさらに内側のキャラを検索。
            return CalcInHorseRecursivelySimulate(inHorse, laneGap);
        }
        private IHorseRaceInfoSimulate CalcOutHorseRecursivelySimulate(IHorseRaceInfoSimulate horse, float laneGap)
        {
            var outHorse = CalcOutHorseSimulate(horse, laneGap);

            // horseより外側のキャラはいない。
            if (null == outHorse)
            {
                return horse;
            }

            // outHorseよりさらに外側のキャラを検索。
            return CalcOutHorseRecursivelySimulate(outHorse, laneGap);
        }
#endif
#if GALLOP
        public override float Search()
        {
            // 追い越し対象がいない=クールダウン中は現状のレーンを維持。
            if (!_ownerAI.IsOverTakeOrCoolDown())
            {
                return OwnerLane;
            }

            _overtakeLaneCandidate.Clear();

            //-----------------------------------------------------------
            // 追い越し対象達を追い抜くためのレーン候補を収集。
            //-----------------------------------------------------------
            float overtakeNearDistance = float.MaxValue;
            const float NEAR_LANEDISTANCE = Gallop.RaceDefine.HorseLane_Two; // 追い越し対象に隣接しているとみなすレーン幅。
            const float OVERTAKE_LANE_CHECK_DIST_OVER = 0.5f;
            const float OVERTAKE_LANE_SPACE = Gallop.RaceDefine.HorseLane_OneAndOneQuarters * 0.8f;    // 追い越し相手を追い越すとき、このレーン離れて追い越す。
            const float OVERTAKE_LANE_CANDIDATE_RANGE = OVERTAKE_LANE_SPACE * 0.8f; // 追い越し相手を追い越すときに、OVERTAKE_LANE_SPACEのイン/アウトにこの空白が無いと追い越しレーンの対象に入れない。

            {
                var overtakeHorse = _ownerAI.GetOverTakeHorse();
                for (int i = 0; i < overtakeHorse.Count; ++i)
                {
                    var horse = overtakeHorse[i];
                    var inHorse = horse.info;
                    var outHorse = horse.info;

                    // 追い抜き対象の中で最も近いキャラのDistanceを保存。
                    if (overtakeNearDistance > horse.info.GetDistance())
                    {
                        overtakeNearDistance = horse.info.GetDistance();
                    }

                    // 追い抜き対象に隣接する最も内側のキャラ、及びそのキャラを内側から抜かすときのレーンを取得。
                    inHorse = CalcInHorseRecursively(inHorse, NEAR_LANEDISTANCE);
                    float inLane = inHorse.GetLaneDistance() - OVERTAKE_LANE_SPACE;

                    // 追い抜き対象に隣接する最も外側のキャラ、及びそのキャラを外側から抜かすときのレーンを取得。
                    outHorse = CalcOutHorseRecursively(outHorse, NEAR_LANEDISTANCE);
                    float outLane = outHorse.GetLaneDistance() + OVERTAKE_LANE_SPACE;

                    // 内側から抜かすときのレーンが移動可能なものかどうか。
                    if (inLane >= _ownerHorse.LaneDistanceMin)
                    {
                        // 自分のDistanceからinHorseのDistance+alphaまでの間に、inLane上にキャラがいないなら、追い抜きレーン候補に加える。
                        if (!CheckHorseExist(_ownerAI.VisibleHorses,
                                                OwnerDistance,
                                                inHorse.GetDistance() + OVERTAKE_LANE_CHECK_DIST_OVER,
                                                inLane - OVERTAKE_LANE_CANDIDATE_RANGE,
                                                inLane + OVERTAKE_LANE_CANDIDATE_RANGE))
                        {
                            _overtakeLaneCandidate.Add(inLane);
                        }
                    }

                    // 外側から抜かすときのレーンが移動可能なものかどうか。
                    if (outLane <= _ownerHorse.LaneDistanceMax)
                    {
                        // 自分のDistanceからoutHorseのDistance+alphaまでの間に、outLane上にキャラがいないなら、追い抜きレーン候補に加える。
                        if (!CheckHorseExist(_ownerAI.VisibleHorses,
                                                OwnerDistance,
                                                outHorse.GetDistance() + OVERTAKE_LANE_CHECK_DIST_OVER,
                                                outLane - OVERTAKE_LANE_CANDIDATE_RANGE,
                                                outLane + OVERTAKE_LANE_CANDIDATE_RANGE))
                        {
                            _overtakeLaneCandidate.Add(outLane);
                        }
                    }
                }
            }

            //-----------------------------------------------------------
            // 中盤までは、自分の内側から追い抜けるならそのレーンを候補に加える。
            // 終盤以降は、自分の現在レーンで追い抜けるなら、そのレーンを候補に加える。
            //-----------------------------------------------------------
            const float OVERTAKE_OWNERLANE_TARGET_ADD_DIST = 3.0f;
            const float OVERTAKE_INMOVE_LANEOFFSET = Gallop.RaceDefine.HorseLane_One;
            if (OwnerPhase <= Gallop.RaceDefine.HorsePhase.MiddleRun &&
                _ownerAI.IsEnableInMove &&
                OwnerLane > OVERTAKE_INMOVE_LANEOFFSET)
            {
                if (!CheckHorseExist(_ownerAI.VisibleHorses,
                                        OwnerDistance,
                                        overtakeNearDistance + OVERTAKE_OWNERLANE_TARGET_ADD_DIST,
                                        OwnerLane - OVERTAKE_LANE_CANDIDATE_RANGE - OVERTAKE_INMOVE_LANEOFFSET,
                                        OwnerLane + OVERTAKE_LANE_CANDIDATE_RANGE - OVERTAKE_INMOVE_LANEOFFSET))
                {
                    _overtakeLaneCandidate.Add(OwnerLane - OVERTAKE_INMOVE_LANEOFFSET);
                }
            }
            else
            {
                if (!CheckHorseExist(_ownerAI.VisibleHorses,
                                        OwnerDistance,
                                        overtakeNearDistance + OVERTAKE_OWNERLANE_TARGET_ADD_DIST,
                                        OwnerLane - OVERTAKE_LANE_CANDIDATE_RANGE,
                                        OwnerLane + OVERTAKE_LANE_CANDIDATE_RANGE))
                {
                    _overtakeLaneCandidate.Add(OwnerLane);
                }
            }

            //-----------------------------------------------------------
            // どのレーンで追い抜くかを調べる。
            // 基本的には、現在レーンから一番近い、移動可能なレーンを追い抜きレーンとして選出する。
            // スキルやレースの展開により、イン/アウトどちらかから抜くことを優先したい場合、
            // 距離に対して重み付けすることで優先度を実現している。
            //-----------------------------------------------------------
            float ownerLane = OwnerLane;
            float nearOvertakeLane = ownerLane;
            float laneGapAbsToNear = float.MaxValue;
            for (int i = 0; i < _overtakeLaneCandidate.Count; ++i)
            {
                var lane = _overtakeLaneCandidate[i];

                // 現在レーンから指定レーンへの距離を調べる。
                // スキルなどにより距離への重み付けも行う。
                // ※ここでいう重みとは、実際のレーン距離に対する係数として扱うため、大きいほど優先度は低くなる。
                float laneGap = ownerLane - lane;
                float laneGapCoefByPhase = CalcLaneGapCoefByPhase(laneGap);
                float laneGapCoefBySkill = CalcLaneGapCoefBySkill(laneGap);
                float laneGapAbs = Math.Abs(laneGap * laneGapCoefByPhase * laneGapCoefBySkill);

                // 現在レーンから一番近いレーンでなければ対象外。
                if (laneGapAbs >= laneGapAbsToNear)
                {
                    continue;
                }

                // 物理的に指定レーンへは移動できない。
                if (!CheckEnableMoveLane(lane))
                {
                    continue;
                }

                nearOvertakeLane = lane;
                laneGapAbsToNear = laneGapAbs;
            }

            //-----------------------------------------------------------
            // ラストコーナー明けでアウトに広がりたい場合、
            // 追い越しで今のレーンより外に行く＆アウトに広がる先が追い越しレーンよりもアウト、であれば、
            // アウトに広がる方を優先する。
            //-----------------------------------------------------------
            if (_ownerLaneCalc.IsUseExtraMove)
            {
                // アウトから追い抜く。
                if (OwnerLane <= nearOvertakeLane)
                {
                    // 追い抜きレーンよりもアウトに広がりたい。
                    if (nearOvertakeLane < _ownerLaneCalc.ExtraMoveLane)
                    {
                        // 広がる先のレーンへ移動可能なら。
                        if (CheckEnableMoveLane(_ownerLaneCalc.ExtraMoveLane))
                        {
                            // 目標レーンの上書き。
                            nearOvertakeLane = _ownerLaneCalc.ExtraMoveLane;
                        }
                    }
                }
            }
            else if( _ownerLaneCalc.IsTargetLaneFixed() )
            {
                // アウトから追い抜く。
                if (OwnerLane <= nearOvertakeLane)
                {
                    // 追い抜きレーンよりもアウトに広がりたい。
                    var targetLane = _ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TargetLane, 0);
                    if (nearOvertakeLane < targetLane)
                    {
                        // 広がる先のレーンへ移動可能なら。
                        if (CheckEnableMoveLane(targetLane))
                        {
                            // 目標レーンの上書き。
                            nearOvertakeLane = targetLane;
                        }
                    }
                }
            }

            //_ownerLaneCalc.TargetLane = nearOvertakeLane;

            return nearOvertakeLane;
        }
        /// <summary>
        /// レース展開による追い抜きレーンの重みを取得。
        /// </summary>
        /// <returns>laneGapに対する係数を重みとして返却。大きいほど優先度の低いレーンとして扱う。</returns>
        private float CalcLaneGapCoefByPhase(float laneGap)
        {
            const float PHASE_START_COEF = 100.0f;
            const float PHASE_DEFAULT_COEF = 1.0f;

            // 序盤。
            if (OwnerPhase == Gallop.RaceDefine.HorsePhase.Start)
            {
                // 必ずインから抜きたい。
                if (laneGap > 0)
                {
                    return PHASE_START_COEF;
                }
            }
            // 終盤以降。
            else if (OwnerPhase >= Gallop.RaceDefine.HorsePhase.End)
            {
                // イン/アウト双方に重み付け。※終盤以降インから抜く傾向付けをしたいという要望のため。
                return laneGap > 0 ? _paramDefine.overTakeOutLaneCoefEnd : _paramDefine.overTakeInLaneCoefEnd;
            }

            return PHASE_DEFAULT_COEF;
        }

        /// <summary>
        /// スキル効果による追い抜きレーンの重みを取得。
        /// </summary>
        /// <returns>laneGapに対する係数を重みとして返却。大きいほど優先度の低いレーンとして扱う。</returns>
        private float CalcLaneGapCoefBySkill(float laneGap)
        {
            const float SKILL_INOUT_COEF = 1.5f;
            const float SKILL_DEFAULT_COEF = 1.0f;

            // ※※※ 先に判定を行うスキルほど、優先度が高い。 ※※※
            bool isForceOvertakeIn = _ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.ForceOvertakeIn, false);
            bool isForceOvertakeOut = _ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.ForceOvertakeOut, false);

            //-----------------------------------------------------------
            // イン優先/アウト優先。
            //-----------------------------------------------------------
            if (isForceOvertakeIn != isForceOvertakeOut)
            {
                // イン優先。
                if (isForceOvertakeIn)
                {
                    // アウト側への回避には重み付けを行う。
                    if (laneGap > 0)
                    {
                        return SKILL_INOUT_COEF;
                    }
                }
                // アウト優先。
                if (isForceOvertakeOut)
                {
                    // イン側への回避には重み付けを行う。
                    if (laneGap < 0)
                    {
                        return SKILL_INOUT_COEF;
                    }
                }
            }

            return SKILL_DEFAULT_COEF;
        }

        /// <summary>
        /// 指定のレーンへ移動可能かどうか。
        /// </summary>
        private bool CheckEnableMoveLane(float lane)
        {
            if (lane < Gallop.RaceInfo.LaneDistanceMin || lane > _ownerHorse.LaneDistanceMax)
            {
                return false;
            }

            float laneGap = lane - OwnerLane;
            if (laneGap < 0)
            {
                if (!_ownerAI.IsEnableInMove || !_ownerAI.CheckLaneSpace(laneGap))
                {
                    return false;
                }
            }
            else if (laneGap > 0)
            {
                if (!_ownerAI.IsEnableOutMove || !_ownerAI.CheckLaneSpace(laneGap))
                {
                    return false;
                }
            }

            return true;
        }

        private static bool CheckHorseExist(Gallop.AroundHorse[] horses, float distanceMin, float distanceMax, float laneDistanceMin, float laneDistanceMax)
        {
            foreach (var horse in horses)
            {
                if (null == horse)
                {
                    continue;
                }

                var horseInfo = horse.info;

                float horseDistance = horseInfo.GetDistance();
                float horseLaneDistance = horseInfo.GetLaneDistance();

                if (horseDistance < distanceMin ||
                    horseDistance > distanceMax)
                {
                    continue;
                }
                if (horseLaneDistance < laneDistanceMin ||
                    horseLaneDistance > laneDistanceMax)
                {
                    continue;
                }
                return true;
            }

            return false;
        }

        private static Gallop.IHorseRaceInfo CalcInHorse(Gallop.IHorseRaceInfo horse, float laneGap)
        {
            if (null == horse)
            {
                return null;
            }

            const float NEAR_DISTANCE_GAP_ABS = /*GameDefine.HorseLength_One*/Gallop.RaceDefine.HorseLength_Three;

            float minLane = horse.GetLaneDistance();
            Gallop.IHorseRaceInfo minHorse = null;

            var arounds = horse.GetAroundHorses();
            for (int i = 0; i < arounds.Length; ++i)
            {
                var around = arounds[i];

                // 相手が後ろにいるか、一定距離以上離れているなら無視。
                if (around.distanceGap < 0 || around.distanceGap > NEAR_DISTANCE_GAP_ABS)
                {
                    continue;
                }
                // 外側にいるなら無視。
                if (around.laneGap >= 0)
                {
                    continue;
                }
                // 指定レーン距離以上離れているなら無視。
                if (around.laneGapAbs >= laneGap)
                {
                    continue;
                }
                // 最も内側と判定されているレーンより外にいるなら無視。
                if (minLane <= around.info.GetLaneDistance())
                {
                    continue;
                }

                minLane = around.info.GetLaneDistance();
                minHorse = around.info;
            }

            return minHorse;
        }
        private static Gallop.IHorseRaceInfo CalcOutHorse(Gallop.IHorseRaceInfo horse, float laneGap)
        {
            if (null == horse)
            {
                return null;
            }

            const float NEAR_DISTANCE_GAP_ABS = /*GameDefine.HorseLength_One*/Gallop.RaceDefine.HorseLength_Three;

            float maxLane = horse.GetLaneDistance();
            Gallop.IHorseRaceInfo maxHorse = null;

            var arounds = horse.GetAroundHorses();
            foreach (var around in arounds)
            {
                // 相手が後ろにいるか、一定距離以上離れているなら無視。
                if (around.distanceGap < 0 || around.distanceGap > NEAR_DISTANCE_GAP_ABS)
                {
                    continue;
                }
                // 内側にいるなら無視。
                if (around.laneGap <= 0)
                {
                    continue;
                }
                // 指定レーン距離以上離れているなら無視。
                if (around.laneGapAbs >= laneGap)
                {
                    continue;
                }
                // 最も外側と判定されているレーンより内にいるなら無視。
                if (maxLane >= around.info.GetLaneDistance())
                {
                    continue;
                }

                maxLane = around.info.GetLaneDistance();
                maxHorse = around.info;
            }

            return maxHorse;
        }
        private Gallop.IHorseRaceInfo CalcInHorseRecursively(Gallop.IHorseRaceInfo horse, float laneGap)
        {
            var inHorse = CalcInHorse(horse, laneGap);

            // horseより内側のキャラはいない。
            if (null == inHorse)
            {
                return horse;
            }

            // inHorseよりさらに内側のキャラを検索。
            return CalcInHorseRecursively(inHorse, laneGap);
        }
        private Gallop.IHorseRaceInfo CalcOutHorseRecursively(Gallop.IHorseRaceInfo horse, float laneGap)
        {
            var outHorse = CalcOutHorse(horse, laneGap);

            // horseより外側のキャラはいない。
            if (null == outHorse)
            {
                return horse;
            }

            // outHorseよりさらに外側のキャラを検索。
            return CalcOutHorseRecursively(outHorse, laneGap);
        }

#endif
    }
}
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算処理インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseTargetLaneCalculator
    {
        /// <summary>
        /// 狙いたいレーン計算結果。
        /// </summary>
        float TargetLane { get; }

        /// <summary>
        /// レーン再検索リクエスト。
        /// </summary>
        bool IsForceCheckNextLaneOneTime { get; set; }
        /// <summary>
        /// 終盤の強制アウト移動を行うかどうか。
        /// </summary>
        bool IsUseExtraMove { get; }
        /// <summary>
        /// 終盤の強制アウト移動先レーン。
        /// </summary>
        float ExtraMoveLane { get; } 

        /// <summary>
        /// 更新。
        /// </summary>
        void Update( float deltaTime );
    
        /// <summary>
        /// 狙いたいレーンが固定されているかどうか。
        /// </summary>
        bool IsTargetLaneFixed();
    }
}

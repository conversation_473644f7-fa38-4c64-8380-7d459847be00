#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算処理：レース中。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseTargetLaneCalculatorRace : IHorseTargetLaneCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly IHorseRaceInfoSimulate _ownerHorse;
        private readonly IHorseRaceAI _ownerAI;
        private readonly Gallop.RaceParamDefine _paramDefine;
        private readonly float _firstMoveLanePointDistance;
        private readonly float _lastMoveOutStartDistance;

        private readonly ITargetLaneStrategy _calcNormal;
        private readonly ITargetLaneStrategy _calcOverTake;
        private readonly ITargetLaneStrategy _calcFixed;

        private float _extraMoveCheckDistance;
        private bool _isUseExtraMoveChecked;
        public bool IsUseExtraMove { get; private set; }
        public float ExtraMoveLane { get; private set; }

        private float OwnerDistance { get { return _ownerHorse.GetDistance(); } }
        private float OwnerLane { get { return _ownerHorse.GetLaneDistance(); } }

        private float _targetLane;
        public float TargetLane 
        { 
            get { return _targetLane; }
            set { _targetLane = RaceUtilMath.Clamp(value, _ownerHorse.LaneDistanceMin, _ownerHorse.LaneDistanceMax); }
        }

        public bool IsForceCheckNextLaneOneTime { get; set; }


        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseTargetLaneCalculatorRace(
            IHorseRaceInfoSimulate ownerHorse,
            IHorseRaceAI ownerAI,
            Gallop.RaceParamDefine paramDefine,
            float initLane,
            float firstMoveLanePointDistance,
            bool isOuterPlaceRace
        )
        {
            _ownerHorse = ownerHorse;
            _ownerAI = ownerAI;
            _paramDefine = paramDefine;
            TargetLane = initLane;
            _firstMoveLanePointDistance = firstMoveLanePointDistance;
            _lastMoveOutStartDistance = CalcLastMoveOutStartDistance();
        #if CYG_DEBUG
            _ownerHorse.DbgLastMoveOutStartDistance = _lastMoveOutStartDistance;
        #endif

            if (isOuterPlaceRace)
            {
                // 外埒沿いに走るレースの時は追い抜きとスキルによる横移動のみを許可する
                _calcNormal = new TargetLaneStrategyNull(_ownerHorse, _ownerAI, this, _paramDefine);
            }
            else
            {
                _calcNormal = new TargetLaneStrategyNormal(_ownerHorse, _ownerAI, this, _paramDefine);
            }
            _calcOverTake = new TargetLaneStrategyOverTake(_ownerHorse, _ownerAI, this, _paramDefine, isOuterPlaceRace);
            _calcFixed = new TargetLaneStrategyFixed(_ownerHorse, _ownerAI, this, _paramDefine);
        }

        //---------------------------------------------------------------
        public void Update(float deltaTime)
        {
            if (!IsCheckNextLane())
            {
                return;
            }

            TargetLane = OwnerLane;

            //-----------------------------------------------------------
            // ラストコーナー明けでアウトへ広がる先を計算。
            //-----------------------------------------------------------
            CheckLastMoveOut();

            //-----------------------------------------------------------
            // 状態別に移動先レーンを検索。
            //-----------------------------------------------------------
            // 前方一定範囲内に、自分より速度の遅い馬がいるときの回避行動。
            if (_ownerAI.IsOverTakeOrCoolDown() && IsOverTakeEnable())
            {
                TargetLane = _calcOverTake.SearchSimulate();
                return;
            }
            // スキルにより移動先レーンが指定されている。
            else if (IsTargetLaneFixed())
            {
                TargetLane = _calcFixed.SearchSimulate();
                return;
            }
            // 回避以外でのレーン移動位置検索。
            else
            {
                TargetLane = _calcNormal.SearchSimulate();
                return;
            }
        }

        //---------------------------------------------------------------
        private void CheckLastMoveOut()
        {
            // チェック済みなら不要。
            if (_isUseExtraMoveChecked)
            {
                return;
            }

            if (!IsLastMoveOutDistance())
            {
                return;
            }

            ExtraMoveLane = CalcMoveOutLane();
            IsUseExtraMove = true;
            _isUseExtraMoveChecked = true;
        }

        /// <summary>
        /// 最終コーナー以降のアウト側移動開始する距離に到達しているか。
        /// </summary>
        private bool IsLastMoveOutDistance()
        {
            // 移動開始距離が負数の場合は移動しない。
            if (_lastMoveOutStartDistance < 0)
            {
                return false;
            }
            
            return _ownerHorse.GetDistance() >= _lastMoveOutStartDistance;
        }

        /// <summary>
        /// 最終コーナー以降のアウト側移動開始する距離。
        /// </summary>
        /// <returns>-1ならアウト側移動を行わない。</returns>
        private float CalcLastMoveOutStartDistance()
        {
            var cornerList = RaceManagerSimulate.Instance.GetCornerList();
            foreach (var corner in cornerList)
            {
                if (!corner.IsFinalCorner)
                {
                    continue;
                }

                float cornerLength = corner.EndDistance - corner.StartDistance;
                float startDistance = corner.StartDistance + cornerLength * _paramDefine.LastMoveOutStartFinalCornerRate;
                return startDistance;
            }

            return -1;
        }

        /// <summary>
        /// レーン移動更新をする必要があるかチェック。
        /// </summary>
        private bool IsCheckNextLane()
        {
            // 外部からの強制レーン検索リクエスト。
            if( IsForceCheckNextLaneOneTime )
            {
                IsForceCheckNextLaneOneTime = false;
                return true;
            }

            // 追い越し処理中はレーン移動更新が必要。
            if (_ownerAI.IsOverTake())
            {
                return true;
            }

            bool isCheckNextLane = false;
            float laneGap = TargetLane - OwnerLane;

            const float LANE_NEAR_DISTANCE = Gallop.RaceDefine.HorseLane_Harf;
            if (laneGap < LANE_NEAR_DISTANCE && laneGap > -LANE_NEAR_DISTANCE)
            {
                // 予定位置近くまで移動した
                isCheckNextLane = true;
            }
            else
            {
                if (laneGap > 0.0f)
                {
                    // 外に向かう予定で移動できないなら再検索が必要。
                    if (!_ownerAI.IsEnableOutMove)
                    {
                        isCheckNextLane = true;
                    }
                }
                else
                {
                    // 内に向かう予定で移動できないなら再検索が必要。
                    if (!_ownerAI.IsEnableInMove)
                    {
                        isCheckNextLane = true;
                    }
                }
            }

            return isCheckNextLane;
        }

        /// <summary>
        /// 追い越しによるレーン移動を行えるかどうか。
        /// </summary>
        private bool IsOverTakeEnable()
        {
            if (_ownerHorse.GetPhase() <= Gallop.RaceDefine.HorsePhase.MiddleRun)
            {
                // 強制レーン移動イベント直前なら、追い越しよりもインに寄るのを優先する。
                if (IsMoveLanePointPrev())
                {
                    return false;
                }
            }
            return true;
        }

        //---------------------------------------------------------------
        public bool IsTargetLaneFixed()
        {
            var targetLane = _ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TargetLane, 0);
            return !RaceUtilMath.Approximately(targetLane, 0.0f);
        }

        /// <summary>
        /// 終盤の強制アウト移動先レーン計算。
        /// </summary>
        private float CalcMoveOutLane()
        {
            float moveOutLaneNormalizeVal = RaceUtilMath.Approximately(_paramDefine.lastMoveOutLaneNormalizeVal, 0.0f) ? 0.2f : _paramDefine.lastMoveOutLaneNormalizeVal;
            float moveOutLaneCoef = _paramDefine.lastMoveOutLaneCoef;
            float moveOutLaneAddRandom = _paramDefine.lastMoveOutLaneAddRandom;

            // 現在のレーン位置を0~1.0に正規化。外にいるキャラほど外に行くようにする。
            float laneNormalized = RaceUtilMath.Clamp(_ownerHorse.GetLaneDistance() / moveOutLaneNormalizeVal, 0.0f, 1.0f);

            // 正規化したレーン位置を元に移動先のレーンを計算。
            float retTargetLane = laneNormalized * moveOutLaneCoef;
            // ある程度ランダム幅を持たせる。
            retTargetLane += RaceManagerSimulate.Instance.GetRandom(moveOutLaneAddRandom);

            retTargetLane = RaceUtilMath.Clamp(retTargetLane, _ownerHorse.LaneDistanceMin, _ownerHorse.LaneDistanceMax);
            return retTargetLane;
        }

        /// <summary>
        /// コーナーイベントの強制レーン移動イベント手前一定範囲内にいるかどうかをチェック。
        /// </summary>
        private bool IsMoveLanePointPrev()
        {
            var moveLanePointDistance = _firstMoveLanePointDistance;
#if CYG_DEBUG
            if (moveLanePointDistance <= 0)
            {
                Debug.LogError("コースイベントで打たれたレーン移動イベントの距離が不正です。distance=" + moveLanePointDistance);
            }
#endif

            // 最初のレーン移動ポイントの手前1ハロン以内にいるならtrue。
            return 
                OwnerDistance <= moveLanePointDistance &&
                OwnerDistance >= (moveLanePointDistance - Gallop.RaceDefine.ONE_FURONG_DISTANCE);
        }
    }
}
#endif

using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 狙いたいレーン計算処理：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseTargetLaneCalculatorNull : IHorseTargetLaneCalculator
    {
        public float TargetLane 
        { 
            get { return _ownerAI.GetTargetLane(); }
        }
        public bool IsForceCheckNextLaneOneTime { get; set; }
        public bool IsUseExtraMove { get; }
        public float ExtraMoveLane { get; }
        private readonly IHorseRaceAI _ownerAI;

        //---------------------------------------------------------------
        public HorseTargetLaneCalculatorNull(IHorseRaceAI ownerAI)
        {
            _ownerAI = ownerAI;
        }
        
        //---------------------------------------------------------------
        public void Update( float deltaTime )
        {
        }

        //---------------------------------------------------------------
        public bool IsTargetLaneFixed()
        {
            return false;
        }
    }
}

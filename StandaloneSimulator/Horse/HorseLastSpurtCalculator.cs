using System.Collections.Generic;
using System.Linq;
using System;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ラストスパート計算機。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseLastSpurtCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        public struct LastSpurtCalcParam
        {
            public readonly int HorseIndex;
            public readonly float CurDistance;
            public readonly float laneDistance;
            public readonly Gallop.RaceDefine.HorsePhase phase;
            public readonly float hp;
            public readonly float speed;
            public readonly float guts;
            public readonly float wiz;
            public readonly float baseTargetSpeed;
            public readonly float minSpeed;
            public readonly float groundModifierMultiHpSub;
            public readonly float properDistanceCoef;
            public readonly float ProperRunningStyleCoef;
            public readonly int CourseDistance;
            public readonly float RaceBaseSpeed;
            public readonly Gallop.RaceDefine.RunningStyle RunningStyle;
            public readonly IRaceRandomGenerator RandomGenerator;

            public LastSpurtCalcParam(  
                int horseIndex,
                float distance, 
                float laneDistance, 
                Gallop.RaceDefine.HorsePhase phase,
                float hp, 
                float speed, 
                float guts, 
                float wiz, 
                float baseTargetSpeed,
                float minSpeed,
                float groundModifierMultiHpSub,
                float properDistanceCoef,
                float properGroundTypeCoef,
                int courseDistance,
                float raceBaseSpeed,
                Gallop.RaceDefine.RunningStyle runningStyle,
                IRaceRandomGenerator randomGenerator)
            {
                this.HorseIndex = horseIndex;
                this.CurDistance = distance;
                this.laneDistance = laneDistance;
                this.phase = phase;
                this.hp = hp;
                this.speed = speed;
                this.guts = guts;
                this.wiz = wiz;
                this.baseTargetSpeed = baseTargetSpeed;
                this.minSpeed = minSpeed;
                this.groundModifierMultiHpSub = groundModifierMultiHpSub;
                this.properDistanceCoef = properDistanceCoef;
                this.ProperRunningStyleCoef = properGroundTypeCoef;
                this.CourseDistance = courseDistance;
                this.RaceBaseSpeed = raceBaseSpeed;
                this.RunningStyle = runningStyle;
                this.RandomGenerator = randomGenerator;
            }
        }

        public const float LASTSPURT_DISTANCE_NULL = -1;

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private LastSpurtCalcParam _calcParam;
        private readonly Gallop.RaceParamDefine _paramDefine = null;
        private readonly float _lastSpurtBaseTargetSpeedAdd; // ラストスパート中、狙いたい速度に加算する値。全キャラ固定値。
        private readonly int _lastSpurtDistanceGoalBuffer;
        
        /// <summary>
        /// ラストスパートを開始する距離。計算が行われるまで(IsLastSpurtDistanceCalclulated==trueとなるまで)は0が入っている。
        /// </summary>
        public float LastSpurtStartDistance { get; private set; } = LASTSPURT_DISTANCE_NULL;
        
        /// <summary>
        /// ラストスパート狙いたい速度。
        /// </summary>
        public float LastSpurtTargetSpeed { get; private set; }

        /// <summary>
        /// 計算結果取得。
        /// </summary>
        public LastSpurtCalcResult CalcResult { get; private set; }


        private float RemainDistance
        {
            get 
            { 
                // ゴール手前ｎmまでを残り距離と見なす。
                // スパート計算時から、任意の速度で走る（つまり加速する時間を考慮しない）というざっくり計算であるため、
                // 実際に走ったときよりも消費Hpの予測は上振れする。
                // そのため、ゴール地点でHpが切れるように計算すると、Hpを使い切らないでゴールする可能性があるため、
                // 敢えてゴール少し前でHpが切れるような計算を行う。
                return Math.Max(_calcParam.CourseDistance - _calcParam.CurDistance - _lastSpurtDistanceGoalBuffer, 0);
            }
        }

    #if CYG_DEBUG && UNITY_EDITOR
        public LastSpurtParam DbgOutputParam { get; set; } = new LastSpurtParam();
    #endif

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseLastSpurtCalculator(Gallop.RaceParamDefine paramDefine, float raceBaseSpeed)
        {
            _paramDefine = paramDefine;
            _lastSpurtBaseTargetSpeedAdd = CalcLastSpurtBaseTargetSpeedAdd(raceBaseSpeed, paramDefine.lastSpurtBaseTargetSpeedAddCoef);
            _lastSpurtDistanceGoalBuffer = paramDefine.LastSpurtDistanceGoalBuffer;

            LastSpurtStartDistance = LASTSPURT_DISTANCE_NULL;
            LastSpurtTargetSpeed = raceBaseSpeed;

            CalcResult = LastSpurtCalcResult.False;
        }
        
        /// <summary>
        /// 「ラストスパート中、狙いたい速度に加算する値」の計算式
        /// </summary>
        /// <param name="raceBaseSpeed"></param>
        /// <param name="lastSpurtBaseTargetSpeedAddCoef"></param>
        /// <returns></returns>
        public static float CalcLastSpurtBaseTargetSpeedAdd(float raceBaseSpeed, float lastSpurtBaseTargetSpeedAddCoef)
        {
            return raceBaseSpeed * lastSpurtBaseTargetSpeedAddCoef;
        }

        /// <summary>
        /// 残り距離を「現在の狙いたい速度」で走り切るために必要なHpが残っているかどうかチェック。
        /// </summary>
        /// <param name="normalTargetSpeed">現在の狙いたい速度。</param>
        private bool _IsHpBelowNeedMinHp( float normalTargetSpeed )
        {
            // 指定速度で残距離を走るのに必要なHpの計算。
            float needHp = HorseHpCalculator.CalcNeedHp(
                _paramDefine.Hp,
                _calcParam.RaceBaseSpeed,
                _calcParam.groundModifierMultiHpSub, 
                _calcParam.phase, 
                _calcParam.guts, 
                normalTargetSpeed, 
                RemainDistance);

            // 【残HP】＜【最低必要HP】の時 ラストスパートをかける事ができない為、【目指す速度】で強制的にラストスパートをかける
            return ( _calcParam.hp < needHp );
        }

        /// <summary>
        /// 残り距離を「ラストスパート時の狙いたい速度」で走り切るために必要なHpが残っているかどうかチェック。
        /// </summary>
        /// <param name="lastSpurtTargetSpeed">ラストスパート時の狙いたい速度。</param>
        private bool _IsHpExceedNeedMaxHp( float lastSpurtTargetSpeed )
        {
            // 指定速度で残距離を走るのに必要なHpの計算。
            float needHp = HorseHpCalculator.CalcNeedHp(
                _paramDefine.Hp,
                _calcParam.RaceBaseSpeed,
                _calcParam.groundModifierMultiHpSub, 
                _calcParam.phase, 
                _calcParam.guts, 
                lastSpurtTargetSpeed, 
                RemainDistance );

            // 【残HP】＞【最大必要HP】の時 すぐにラストスパートの狙いたい速度でラストスパートをかける事ができる為、【ラストスパートの狙いたい速度】で強制的にラストスパートをかける
            return ( _calcParam.hp >= needHp );
        }

        /// <summary>
        /// ラストスパート開始距離計算。
        /// </summary>
        /// <param name="lastSpurtTargetSpeed">ラストスパートで狙いたい速度。</param>
        /// <param name="normalTargetSpeed">通常の狙いたい速度。GetBaseTargetSpeed(false)に相当。</param>
        /// <param name="lastSpurtStartDistanceMax">ラストスパート開始可能な最大距離。</param>
        /// <param name="retLastSpurtStartDistance">ラストスパート開始距離を返却。</param>
        /// <param name="retNeedTime">ゴールまでに掛かる予想時間を返却。</param>
        /// <returns>スパートをかけることができるならtrue。（→残りHpでは、通常の狙いたい速度でも走り切れない場合はfalseが返る）</returns>
        private bool CalcSpurtDistance( 
            float lastSpurtTargetSpeed, 
            float normalTargetSpeed, 
            float lastSpurtStartDistanceMax,
            out float retLastSpurtStartDistance,
            out float retNeedTime)
        {
            retNeedTime = float.MaxValue;
            retLastSpurtStartDistance = _calcParam.CurDistance;

            // ラストスパートを駆ける距離の算出
            // https://xxxxxxxxxx/pages/viewpage.action?pageId=16807558#id-%E6%96%B0%E3%83%AC%E3%83%BC%E3%82%B9%E3%83%AD%E3%82%B8%E3%83%83%E3%82%AF%E3%81%AE%E5%A4%89%E6%9B%B4%E7%82%B9-%E6%96%B0%E3%83%A9%E3%82%B9%E3%83%88%E3%82%B9%E3%83%91%E3%83%BC%E3%83%88%E3%82%B7%E3%82%B9%E3%83%86%E3%83%A0

            // 【残キョリ】
            float remainDistance = RemainDistance;
            // 【スパートを駆けた速度で走った時の消費HP】
            float lastSpurtSpeedDecHpPerSec = HorseHpCalculator.CalcDecHpPerSec( 
                null,
                _paramDefine.Hp,
                _calcParam.RaceBaseSpeed,
                _calcParam.groundModifierMultiHpSub, 
                _calcParam.phase, 
                _calcParam.guts, 
                lastSpurtTargetSpeed );
            // 【通常の目指す速度で走った時の消費HP】
            float normalSpeedDecHpPerSec = HorseHpCalculator.CalcDecHpPerSec( 
                null,
                _paramDefine.Hp,
                _calcParam.RaceBaseSpeed,
                _calcParam.groundModifierMultiHpSub, 
                _calcParam.phase, 
                _calcParam.guts, 
                normalTargetSpeed );

            const float DISTANCE_OFFSET = /*100*/30;
            
            // まずは、残り距離を全部スパート速度で走り切るものとして計算開始。
            float spurtRunningDistance = remainDistance;
            while(spurtRunningDistance > 0)
            {
                // スパート速度でスパート距離を走るのに必要な時間。
                float spurtNeedTime = spurtRunningDistance / lastSpurtTargetSpeed;
                // スパートをかけている間に消費するHp。
                float spurtNeedHp = lastSpurtSpeedDecHpPerSec * spurtNeedTime;
                // 通常速度で（残距離-スパート距離）を走るのに必要な時間。
                float normalNeedTime = (remainDistance - spurtRunningDistance) / normalTargetSpeed;
                // 通常速度で走っている間に消費するHp。
                float normalNeedHp = normalSpeedDecHpPerSec * normalNeedTime;

                // スパート消費Hpと通常速度消費Hpの合計が、現在Hpに収まるなら、Hp切れを起こさずに走り切れると判断する。
                if ((spurtNeedHp + normalNeedHp) <= _calcParam.hp)
                {
                    float lastSpurtStartDistance = _calcParam.CourseDistance - _lastSpurtDistanceGoalBuffer - spurtRunningDistance;

                    // スパートを始める距離が、一定の距離を超えていなければスパート可能である。
                    if (lastSpurtStartDistance < lastSpurtStartDistanceMax)
                    {
                        // ゴールまでの予想時間を返却。
                        retNeedTime = spurtNeedTime + normalNeedTime;
                        // ラストスパート開始距離を返却。
                        retLastSpurtStartDistance = lastSpurtStartDistance;

                        return true;
                    }
                }

                // スパートで走る距離を減らして再トライ。
                spurtRunningDistance -= DISTANCE_OFFSET;
            }

            // ここに来るということは、通常の狙いたい速度でも残り距離を走り切れない。
            return false;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// ラストスパート開始距離/速度計算。
        /// </summary>
        //---------------------------------------------------------------
        public void CalcLastSpurt( ref LastSpurtCalcParam calcParam )
        {
            _calcParam = calcParam;

            float curDistance = _calcParam.CurDistance;
            float baseTargetSpeedOnLastSpurt = _calcParam.baseTargetSpeed + _lastSpurtBaseTargetSpeedAdd;
            var model = new CalcBaseTargetSpeedAndStatusSpeedModel(
                baseTargetSpeedCoef: _paramDefine.baseTargetSpeedCoef,
                lastSpurtTargetSpeedCoefSqrt: _paramDefine.LastSpurtTargetSpeedCoefSqrt,
                addSpeedParamCoef: _paramDefine.addSpeedParamCoef,
                lastSpurtTargetSpeedGutsPow: _paramDefine.LastSpurtTargetSpeedGutsPow,
                lastSpurtTargetSpeedGutsCoef1: _paramDefine.LastSpurtTargetSpeedGutsCoef1,
                lastSpurtTargetSpeedGutsCoef2: _paramDefine.LastSpurtTargetSpeedGutsCoef2);
            model.SetupParameter(baseTargetSpeed: baseTargetSpeedOnLastSpurt, speed: _calcParam.speed,
                guts: _calcParam.guts, properDistanceCoef: _calcParam.properDistanceCoef);
            float lastSpurtTargetSpeedMax = CalcBaseTargetSpeedAndStatusSpeed(model);
            float lastSpurtStartDistance = curDistance;
            float lastSpurtStartDistanceMax = _calcParam.CourseDistance - _paramDefine.lastSpurtDistanceMaxFromGoal;

            //-----------------------------------------------------------
            // STEP1. ラストスパート中に出す狙いたい速度の値を減らしつつ、開始できる距離を計算。
            //-----------------------------------------------------------
            CalcResult = CalcSpurtDistanceSpeedDownRecursively( 
                lastSpurtTargetSpeedMax, 
                lastSpurtStartDistanceMax, 
                out lastSpurtStartDistance,
                out float enableLastSpurtTargetSpeed);
            bool isLastSpurtEnable = ( CalcResult & LastSpurtCalcResult.TrueFlags ) != 0;
        #if CYG_DEBUG && UNITY_EDITOR
            DbgOutputParam.Evaluate = (int) RaceUtil.LastSpurtCalcResult2EvaluateValue(CalcResult);
            DbgOutputParam.Hp = _calcParam.hp;
        #endif

            if (!isLastSpurtEnable)
            {
                // ラストスパート失敗確定。
                LastSpurtStartDistance = LASTSPURT_DISTANCE_NULL;
                return;
            }
                
            //-----------------------------------------------------------
            // STEP2. STEP1で算出された速度から一定値ずつ落としつつ、速度と開始距離を求める。
            //-----------------------------------------------------------
            if(CalcResult == LastSpurtCalcResult.True )
            {
                Debug.Assert(enableLastSpurtTargetSpeed <= lastSpurtTargetSpeedMax );

                // 計算成功したラストスパート速度から一定値ずつ速度を落としつつ、ゴールまでのおおよその所要時間を計算する。
                // 所要時間毎に、ラストスパート用のパラメータ候補（開始距離と目標速度）を出す。
                // 賢さを元にパラメータ候補の最適なものから順に抽選を行う。
                CalcSpurtDistanceSpeedDownRecursivelyNext(
                    enableLastSpurtTargetSpeed, 
                    lastSpurtStartDistanceMax, 
                    out lastSpurtStartDistance,
                    out float useLastSpurtTargetSpeed);

                LastSpurtStartDistance = lastSpurtStartDistance;
                LastSpurtTargetSpeed = useLastSpurtTargetSpeed;
            }
            else if( CalcResult == LastSpurtCalcResult.TrueExceedNeedMaxHp )
            {
                // Hpがあり余っていて、現在地点からラストスパート速度まで上げて走り切ることができる。
                // 賢さによる抽選などはスキップ。
                LastSpurtStartDistance = _calcParam.CurDistance;
                LastSpurtTargetSpeed = lastSpurtTargetSpeedMax;
            }
            else if ( CalcResult == LastSpurtCalcResult.False )
            {
                // ここに来た時点で失敗はありえない。
                Debug.Assert(false);
            }
            
            // スパートをかけるのは現在地点以降。
            if(LastSpurtStartDistance < _calcParam.CurDistance)
            {
                LastSpurtStartDistance = _calcParam.CurDistance;
            }

            Debug.Assert( 
                _calcParam.baseTargetSpeed < LastSpurtTargetSpeed,
                $"ラストスパート速度が、通常狙いたい速度を下回っている。HorseIndex={_calcParam.HorseIndex}, CalcResult={CalcResult}, 通常={_calcParam.baseTargetSpeed}, ラストスパート基準={lastSpurtTargetSpeedMax}, ラストスパート実際速度={LastSpurtTargetSpeed}");

#if CYG_DEBUG && UNITY_EDITOR
            DbgOutputParam.StartDistance = LastSpurtStartDistance;
            DbgOutputParam.TargetSpeed = LastSpurtTargetSpeed;
            DbgOutputParam.ExceedNeedHp = HorseHpCalculator.CalcNeedHp(
                _paramDefine.Hp,
                _calcParam.RaceBaseSpeed,
                _calcParam.groundModifierMultiHpSub,
                _calcParam.phase,
                _calcParam.guts,
                lastSpurtTargetSpeedMax,
                RemainDistance);
#endif
        }

        /// <summary>
        /// ベース速度から、ラストスパート中の狙いたい速度を計算。
        /// </summary>
        /// <param name="model">ベース速度。</param>
        /// <returns></returns>
        public static float CalcBaseTargetSpeedAndStatusSpeed(CalcBaseTargetSpeedAndStatusSpeedModel model)
        {
            if (!model.IsAccessible)
            {
                Debug.LogError("CalcBaseTargetSpeedAndStatusSpeedModelが初期化前です");
            }
            float retTargetSpeed = (model.BaseTargetSpeed * model.BaseTargetSpeedCoef);
            retTargetSpeed += (float)System.Math.Sqrt(model.Speed * model.LastSpurtTargetSpeedCoefSqrt) * model.ProperDistanceCoef * model.AddSpeedParamCoef;
            retTargetSpeed += (float)System.Math.Pow(model.Guts * model.LastSpurtTargetSpeedGutsCoef1, model.LastSpurtTargetSpeedGutsPow) * model.LastSpurtTargetSpeedGutsCoef2;
            return retTargetSpeed;
        }

        /// <summary>
        /// CalcBaseTargetSpeedAndStatusSpeed用モデルクラス
        /// SetupParameter()前にアクセスしないこと
        /// </summary>
        public class CalcBaseTargetSpeedAndStatusSpeedModel
        {
            // 初期化済みかどうかのフラグ
            public bool IsAccessible { get; private set; } = false;
            // 変動値（必要なときに設定）
            public float BaseTargetSpeed { get; private set; }
            public float Speed { get; private set; }
            public float Guts { get; private set; }
            
            public float ProperDistanceCoef { get; private set; }
            // 固定値（コンストラクタで設定）
            public readonly float BaseTargetSpeedCoef;
            public readonly float LastSpurtTargetSpeedCoefSqrt;
            public readonly float AddSpeedParamCoef;
            public readonly float LastSpurtTargetSpeedGutsPow;
            public readonly float LastSpurtTargetSpeedGutsCoef1;
            public readonly float LastSpurtTargetSpeedGutsCoef2;

            /// <summary>
            /// 変動値の更新をして、使えるようにする
            /// </summary>
            /// <param name="baseTargetSpeed"></param>
            /// <param name="speed"></param>
            /// <param name="guts"></param>
            public void SetupParameter(float baseTargetSpeed, float speed, float guts, float properDistanceCoef)
            {
                BaseTargetSpeed = baseTargetSpeed;
                Speed = speed;
                Guts = guts;
                ProperDistanceCoef = properDistanceCoef;
                // アクセス可能にする
                IsAccessible = true;
            }

            public CalcBaseTargetSpeedAndStatusSpeedModel(float baseTargetSpeedCoef,
                float lastSpurtTargetSpeedCoefSqrt, float addSpeedParamCoef, float lastSpurtTargetSpeedGutsPow,
                float lastSpurtTargetSpeedGutsCoef1,
                float lastSpurtTargetSpeedGutsCoef2)
            {
                BaseTargetSpeedCoef = baseTargetSpeedCoef;
                LastSpurtTargetSpeedCoefSqrt = lastSpurtTargetSpeedCoefSqrt;
                AddSpeedParamCoef = addSpeedParamCoef;
                LastSpurtTargetSpeedGutsPow = lastSpurtTargetSpeedGutsPow;
                LastSpurtTargetSpeedGutsCoef1 = lastSpurtTargetSpeedGutsCoef1;
                LastSpurtTargetSpeedGutsCoef2 = lastSpurtTargetSpeedGutsCoef2;
            }
        }

        /// <summary>
        /// ラストスパート開始距離の計算。スタミナが足りなくてラストスパートができない場合、ラストスパートで出す狙いたい速度を下げながら繰り返し計算する。
        /// </summary>
        /// <param name="lastSpurtTargetSpeed">ラストスパート中の狙いたい速度。</param>
        /// <param name="lastSpurtStartDistanceMax">ラストスパート開始距離の最大値。この値以上では開始できない。</param>
        /// <param name="retLastSpurtStartDistance">ラストスパート開始距離返却。</param>
        /// <returns>ラストスパート可能かどうか。</returns>
        private LastSpurtCalcResult CalcSpurtDistanceSpeedDownRecursively(
            float lastSpurtTargetSpeed, 
            float lastSpurtStartDistanceMax, 
            out float retLastSpurtStartDistance,
            out float retLastSpurtTargetSpeed)
        {
            // 速度低下幅が、符号無し＆０を超える値であること。
            Debug.Assert(_paramDefine.lastSpurtSpeedDelta > 0);

            retLastSpurtStartDistance = _calcParam.CurDistance;
            retLastSpurtTargetSpeed = _calcParam.baseTargetSpeed;

            float normalTargetSpeed = _calcParam.baseTargetSpeed;

            // 残りHPが不足しており、残り距離を現在の狙いたい速度でも走り続けられないなら、この時点で終了。
            if (_IsHpBelowNeedMinHp(normalTargetSpeed))
            {
                return LastSpurtCalcResult.FalseBelowNeedMinHp;
            }
            // 残りHPが十分にあり、残り距離をラストスパート速度で走り続けられるなら、この時点で終了。
            if (_IsHpExceedNeedMaxHp(lastSpurtTargetSpeed))
            {
#if CYG_DEBUG
                var candidateExceed = new LastSpurtCandidate(){distance = retLastSpurtStartDistance, speed = lastSpurtTargetSpeed};
                DbgCandidateList.Add(candidateExceed);
                DbgUseCandidate = candidateExceed;
#endif
                return LastSpurtCalcResult.TrueExceedNeedMaxHp;
            }

            float lastSpurtTargetSpeedBase = lastSpurtTargetSpeed;
            float lastSpurtTargetSpeedMin = Math.Min(lastSpurtTargetSpeedBase, _calcParam.baseTargetSpeed);

            var lastSpurtResult = LastSpurtCalcResult.False;
            {
                int loopCnt = 0;
                while (lastSpurtTargetSpeed >= lastSpurtTargetSpeedMin)
                {
                    bool isSccucess = CalcSpurtDistance(
                        lastSpurtTargetSpeed: lastSpurtTargetSpeed,
                        normalTargetSpeed: normalTargetSpeed,
                        lastSpurtStartDistanceMax: lastSpurtStartDistanceMax,
                        retLastSpurtStartDistance: out float lastSpurtStartDistance,
                        retNeedTime: out float needTime);

                    // この距離(lastSpurtStartDistance)からラストスパートできる。
                    if (isSccucess)
                    {
                        retLastSpurtStartDistance = lastSpurtStartDistance;
                        retLastSpurtTargetSpeed = lastSpurtTargetSpeed;
                        lastSpurtResult = LastSpurtCalcResult.True;
                        break;
                    }


                    // ラストスパートでの狙いたい速度を下げる。
                    // 下げた結果が狙いたい速度の最低値を下回りそうなら計算は終了する。
                    lastSpurtTargetSpeed -= _paramDefine.lastSpurtSpeedDelta;
                    if (lastSpurtTargetSpeed <= lastSpurtTargetSpeedMin)
                    {
                        // ラストスパートでの狙いたい速度が、最低値でも不可なら計算は失敗。
                        break;
                    }

                    if (++loopCnt > 100)
                    {
                        Debug.Assert(false, string.Format("ラストスパート速度の計算失敗。loopCnt={0} ParamDefine.lastSpurtSpeedDelta={1}", loopCnt, _paramDefine.lastSpurtSpeedDelta));
                        break;
                    }
                }
            }

            return lastSpurtResult;
        }


        /// <summary>
        /// 指定速度から、距離と速度を減らしつつ、走り切れる候補を算出し、賢さを元に候補を選ぶ。
        /// </summary>
        /// <param name="lastSpurtTargetSpeed"></param>
        /// <param name="lastSpurtStartDistanceMax"></param>
        /// <param name="retLastSpurtStartDistance"></param>
        /// <param name="retLastSpurtTargetSpeed"></param>
        private void CalcSpurtDistanceSpeedDownRecursivelyNext( 
            float lastSpurtTargetSpeed, 
            float lastSpurtStartDistanceMax, 
            out float retLastSpurtStartDistance,
            out float retLastSpurtTargetSpeed)
        {
            retLastSpurtStartDistance = _calcParam.CurDistance;
            retLastSpurtTargetSpeed = lastSpurtTargetSpeed;

            float normalTargetSpeed = _calcParam.baseTargetSpeed;

        #if CYG_DEBUG // ここに来ているということは、このケースはありえない。
            // 残りHPが不足しており、残り距離を現在の狙いたい速度でも走り続けられないなら、この時点で終了。
            if( _IsHpBelowNeedMinHp( normalTargetSpeed ) )
            {
                Debug.LogError("CalcSpurtDistanceSpeedDownRecursivelyNextで_IsHpBelowNeedMinHpがtrue.");
                return;
            }
        #endif

            float lastSpurtTargetSpeedBase = lastSpurtTargetSpeed;
            float lastSpurtTargetSpeedMin = Math.Min( lastSpurtTargetSpeedBase, _calcParam.baseTargetSpeed);
        
            // ラストスパート候補を集める。
            var candidates = new List<LastSpurtCandidate>();
            {
                int loopCnt = 0;
                while( lastSpurtTargetSpeed >= lastSpurtTargetSpeedMin )
                {
                    // lastSpurtTargetSpeedで走り切るためには、どれくらいの距離をラストスパートで走れるかを計算。
                    bool isSuccess = CalcSpurtDistance(
                        lastSpurtTargetSpeed: lastSpurtTargetSpeed,
                        normalTargetSpeed: normalTargetSpeed,
                        lastSpurtStartDistanceMax: lastSpurtStartDistanceMax,
                        retLastSpurtStartDistance: out float lastSpurtStartDistance,
                        retNeedTime: out float needTimeToGoal);

                    // lastSpurtTargetSpeedで走り切れる距離を見つけた。
                    if (isSuccess)
                    {
                        // この距離(lastSpurtStartDistance)でラストスパートできる。候補に加える。
                        candidates.Add( 
                            new LastSpurtCandidate() 
                            { 
                                distance = lastSpurtStartDistance, 
                                speed = lastSpurtTargetSpeed, 
                                needTime = needTimeToGoal, 
                            } );
                    }


                    // ラストスパートでの狙いたい速度を下げる。
                    // 下げた結果が狙いたい速度の最低値を下回りそうなら計算は終了する。
                    lastSpurtTargetSpeed -= _paramDefine.lastSpurtStep2SpeedDelta;
                    if (lastSpurtTargetSpeed <= lastSpurtTargetSpeedMin)
                    {
                        // ラストスパートでの狙いたい速度を最低値までチェックしたら計算終了。
                        break;
                    }

                    if( ++loopCnt > 100 )
                    {
                        Debug.Assert( false, string.Format( "ラストスパート速度の計算失敗。loopCnt={0} ParamDefine.lastSpurtSpeedDelta={1}", loopCnt, _paramDefine.lastSpurtSpeedDelta ) );
                        break;
                    }
                }
            }

            if( candidates.Count <= 0 )
            {
                Debug.Assert( false, "ラストスパート候補が０です" );
                return;
            }

            // かかる時間で昇順にソート。
            candidates.Sort( (lh, rh) => ( int )( ( lh.needTime - rh.needTime ) * 10000.0f ) );

            // どの候補を使うか抽選。
            LastSpurtCandidate useCandidate = null;
            {
                float wiz = _calcParam.wiz;
                float per = _paramDefine.lastSpurtStep2PerVal1 + _paramDefine.lastSpurtStep2PerVal2 * wiz;
                foreach( var candidate in candidates )
                {
                    int random = _calcParam.RandomGenerator.GetRandom(100);
                    if (random  < per)
                    {
                        // これを使う。
                        useCandidate = candidate;
                        break;
                    }
                }
                // どの候補も抽選に当たらなかったら、最後尾の要素を使う。
                if( null == useCandidate )
                {
                    useCandidate = candidates.Last();
                }
            }

            retLastSpurtTargetSpeed = useCandidate.speed;
            retLastSpurtStartDistance = useCandidate.distance;
            
        #if CYG_DEBUG
            DbgCandidateList.AddRange(candidates);
            DbgUseCandidate = useCandidate;
        #endif
        }
        
    #if CYG_DEBUG
        public List<LastSpurtCandidate> DbgCandidateList { get; private set; } = new List<LastSpurtCandidate>();
        public LastSpurtCandidate DbgUseCandidate { get; private set; }
    #endif
    }
}

#endif

// Debugデータが参照を持つため、切り出している
namespace StandaloneSimulator
{
    /// <summary>
    /// ラストスパート計算結果。
    /// </summary>
    public enum LastSpurtCalcResult
    {
        // 残り距離を、ラストスパートの狙いたい速度で走り続けるHpが残っている。
        TrueExceedNeedMaxHp = 1 << 0,
        // 残り距離を、ラストスパートの狙いたい速度を少し落とすか、開始地点を遅らせて走れるHpが残っている。
        True = 1 << 1,

        // 残り距離を、通常の狙いたい速度でも走り続けるHpが残っていない。
        FalseBelowNeedMinHp = 1 << 2,
        // 残り距離を、ラストスパートの狙いたい速度で走れるHpが残っていない。
        False = 1 << 3,

        TrueFlags = ( TrueExceedNeedMaxHp | True ),
        FalseFlags = ( FalseBelowNeedMinHp | False ),
    }
    
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
    public class LastSpurtCandidate
    {
        public float distance = 0; // ラストスパート開始距離。
        public float speed = 0; // ラストスパート中狙いたい速度。
        public float needTime = 0; // ゴールまでにかかると推測される時間。
    }
# endif
}
using System.Linq;

namespace StandaloneSimulator
{
    public static class HorsePostNumberCalculator
    {
        /// <summary>
        /// 枠番取得。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil.TestGetPostNumber(int, int, int)"/>
        public static int GetPostNumber(int horseIndex, int horseNum)
        {
            int tableIndex = horseNum - 1;
            if (tableIndex < 0 || tableIndex >= POST_NUMBER_ASSIGN_TABLE.Length)
            {
                Debug.LogWarning( "枠番計算のtableIndexが不正です。tableIndex = " + tableIndex );
                return POST_NUMBER_ERROR;
            }

            int[] table = POST_NUMBER_ASSIGN_TABLE[tableIndex];
            if (horseIndex < 0 || horseIndex >= table.Length)
            {
                Debug.LogWarning( "枠番計算に渡されたhorseIndexが不正です。HorseIndex = " + horseIndex );
                return POST_NUMBER_ERROR;
            }

            int retPostNumber = table[horseIndex];
            Debug.Assert(retPostNumber >= Gallop.RaceDefine.POST_NUMBER_MIN && retPostNumber <= Gallop.RaceDefine.POST_NUMBER_MAX);

            return retPostNumber;
        }
        
        /// <summary>
        /// 枠番最大値計算。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil.TestGetPostNumberMax(int, int)"/>
        public static int GetPostNumberMax( int horseNum )
        {
            int tableIndex = horseNum - 1;
            if (tableIndex < 0 || tableIndex >= POST_NUMBER_ASSIGN_TABLE.Length)
            {
                Debug.LogWarning( "枠番計算のtableIndexが不正です。tableIndex = " + tableIndex );
                return POST_NUMBER_ERROR;
            }

            int[] table = POST_NUMBER_ASSIGN_TABLE[tableIndex];
            return table.Max();
        }
        
#if GALLOP
       /// <summary>
       /// HorseIndexと出走馬数を元に体操服の色取得。
       /// </summary>
       /// <param name="horseIndex">馬番-1の値。[0 .. 出走馬数-1]</param>
       /// <param name="horseNum">出走馬数。</param>
       public static Gallop.ModelLoader.TrackSuitColor GetPostNumberColor( int horseIndex, int horseNum )
       {
           if( horseIndex < 0 || horseIndex >= horseNum )
           {
               Gallop.DebugUtils.Assert( false, string.Format( "horseIndex or horseNumが不正です。 horseIndex={0}, horseNum={1}", horseIndex, horseNum ) );
               return Gallop.ModelLoader.TrackSuitColor.White;
           }
           int postNumber = GetPostNumber( horseIndex, horseNum );
           return GetPostNumberColor( postNumber );
       }
       
       /// <summary>
       /// 枠番を元に体操服の色取得。
       /// </summary>
       public static Gallop.ModelLoader.TrackSuitColor GetPostNumberColor( int postNumber )
       {
           if( postNumber < Gallop.RaceDefine.POST_NUMBER_MIN )
           {
               Gallop.DebugUtils.Assert( false, string.Format( "枠番が不正です。 postNumber={0}", postNumber ) );
               return Gallop.ModelLoader.TrackSuitColor.White;
           }
           return Gallop.ModelLoader.TrackSuitColor.White + ( postNumber - 1 );
       }
#endif
        
        private const int POST_NUMBER_ERROR = 1;
        private static readonly int[][] POST_NUMBER_ASSIGN_TABLE = 
        {
            new [] { 1 },                                                          // 1頭。※出走馬数。
            new [] { 1, 2 },                                                       // 2頭。
            new [] { 1, 2, 3 },                                                    // 3頭。
            new [] { 1, 2, 3, 4 },                                                 // 4頭。
            new [] { 1, 2, 3, 4, 5 },                                              // 5頭。
            new [] { 1, 2, 3, 4, 5, 6 },                                           // 6頭。
            new [] { 1, 2, 3, 4, 5, 6, 7 },                                        // 7頭。
            new [] { 1, 2, 3, 4, 5, 6, 7, 8 },                                     // 8頭。
            new [] { 1, 2, 3, 4, 5, 6, 7, 8, 8 },                                  // 9頭。
            new [] { 1, 2, 3, 4, 5, 6, 7, 7, 8, 8 },                               // 10頭。
            new [] { 1, 2, 3, 4, 5, 6, 6, 7, 7, 8, 8 },                            // 11頭。
            new [] { 1, 2, 3, 4, 5, 5, 6, 6, 7, 7, 8, 8 },                         // 12頭。
            new [] { 1, 2, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8 },                      // 13頭。
            new [] { 1, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8 },                   // 14頭。
            new [] { 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8 },                // 15頭。
            new [] { 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8 },             // 16頭。
            new [] { 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 8 },          // 17頭。
            new [] { 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 7, 8, 8, 8 },       // 18頭。
            new [] { 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 6, 7, 7, 7, 8, 8, 8 },    // 19頭。
            new [] { 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 5, 6, 6, 6, 7, 7, 7, 8, 8, 8 }, // 20頭。
        };
    }
}

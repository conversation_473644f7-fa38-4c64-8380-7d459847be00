namespace StandaloneSimulator
{
    /// <summary>
    /// キャラの基礎ステータスアクセスインターフェース。
    /// </summary>
    /// <remarks>
    /// 育成されたステータスをやる気で補正したへのアクセスを提供する。
    /// </remarks>
    public interface IRaceParameter
    {
        /// <summary>
        /// 素の基礎ステータス：スピード。
        /// </summary>
        int RawSpeed { get; }
        /// <summary>
        /// 素の基礎ステータス：スタミナ。
        /// </summary>
        int RawStamina { get; }
        /// <summary>
        /// 素の基礎ステータス：パワー。
        /// </summary>
        int RawPow { get; }
        /// <summary>
        /// 素の基礎ステータス：根性。
        /// </summary>
        int RawGuts { get; }
        /// <summary>
        /// 素の基礎ステータス：賢さ。
        /// </summary>
        int RawWiz { get; }
        
        /// <summary>
        /// やる気補正済み基礎ステータス：スピード。
        /// </summary>
        float BaseSpeed { get; }
        /// <summary>
        /// やる気補正済み基礎ステータス：スタミナ。
        /// </summary>
        float BaseStamina { get; }
        /// <summary>
        /// やる気補正済み基礎ステータス：パワー。
        /// </summary>
        float BasePow { get; }
        /// <summary>
        /// やる気補正済み基礎ステータス：根性。
        /// </summary>
        float BaseGuts { get; }
        /// <summary>
        /// やる気補正済み基礎ステータス：賢さ。
        /// </summary>
        float BaseWiz { get; }

        /// <summary>
        /// やる気。
        /// </summary>
        Gallop.RaceDefine.Motivation Motivation { get; }

        float MotivationCoef { get; }
    }
}

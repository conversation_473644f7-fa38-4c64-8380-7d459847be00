#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース馬AI。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseRaceAISimulate : IHorseRaceAI
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        private const float AFTER_GOAL_SPEED_DOWN_TIME = 1.0f;
        private const float DOWN_SLOPE_CHECK_SEC = 1.0f;


        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>このレースのRaceInfo</summary>
        private RaceInfo _raceInfo;
        /// <summary>ParamDefine</summary>
        private Gallop.RaceParamDefine _paramDefine;
        /// <summary>このAIの所有者</summary>
        private IHorseRaceInfoSimulate _ownerHorse;
        /// <summary>キャラ管理</summary>
        private IRaceHorseAccessor _horseAccessor;
        /// <summary>所有者の現在距離</summary>
        private float OwnerDistance => _ownerHorse.GetDistance();
        /// <summary>所有者の現在レーン</summary>
        private float OwnerLane => _ownerHorse.GetLaneDistance();
        /// <summary>所有者の走法</summary>
        private Gallop.RaceDefine.RunningStyle OwnerRunningStyle => _ownerHorse.RunningStyle;
        /// <summary>所有者の特殊奏法</summary>
        private Gallop.RaceDefine.RunningStyleEx OwnerRunningStyleEx => _ownerHorse.RunningStyleEx;
        /// <summary>所有者の現在Phase</summary>
        private Gallop.RaceDefine.HorsePhase OwnerPhase => _ownerHorse.GetPhase();

        /// <summary></summary>
        private float _afterGoalTime;

        /// <summary>一度でもAroundHorseの更新が行われればtrue</summary>
        private bool _isAroundHorseInitialized = false;
        /// <summary>周囲のキャラ情報。毎フレームUpdate前のPreUpdateで更新される</summary>
        private Gallop.AroundHorse[] _aroundHorses = null;
        /// <summary>自分の前方をブロックしているキャラ</summary>
        private Gallop.AroundHorse _blockHorse = null;
        /// <summary>自分のイン側をブロックしているキャラ</summary>
        private Gallop.AroundHorse _blockHorseIn = null;
        /// <summary>自分のアウト側をブロックしているキャラ</summary>
        private Gallop.AroundHorse _blockHorseOut = null;

        /// <summary>進路上にいるキャラの数</summary>
        public int CongestionCount { get; private set; }
        /// <summary>進路上にいるキャラの数が一定数以上かどうか</summary>
        public bool IsCongestion => CongestionCount >= _paramDefine.CongestionHorseCntThreshold;
        
        //---------------------------------------------------------------
        #region <変数：ラストスパート>
        public bool IsReqCalcLastSpurt { get; set; }
        /// <summary>ラストスパート計算結果</summary>
        public LastSpurtCalcResult LastSpurtCalcResult => _lastSpurtCalcResult;
        public float FirstCalcLastSpurtTargetSpeed => _firstCalcLastSpurtTargetSpeed;
        /// <summary>ラストスパートで目指す速度</summary>
        public float LastSpurtTargetSpeed => _lastSpurtTargetSpeed;
        /// <summary>ラストスパート開始距離</summary>
        public float LastSpurtStartDistance => _lastSpurtStartDistance;
        public bool IsCalcLastSpurt => _lastSpurtCalcCount > 0;

        /// <summary>ラストスパート中かどうか</summary>
        public bool IsLastSpurt
        {
            get 
            { 
                // まだラストスパート開始距離が計算されていない。
                if (LastSpurtStartDistance <= 0)
                {
                    return false;
                }
                // ラストスパートに失敗している（ヤケクソモード）時は、ラストスパート掛けない。
                if((LastSpurtCalcResult & LastSpurtCalcResult.FalseFlags) != 0)
                {
                    return false;
                }
                return OwnerDistance >= LastSpurtStartDistance;
            }
        }
        #endregion
        
        //---------------------------------------------------------------
        #region <変数：ポジション維持機能>
        /// <summary>ポジ維持計算機</summary>
        private IHorsePositionKeepCalculator _positionKeepCalc = null;

        /// <summary>ポジ維持のモード</summary>
        public Gallop.RaceDefine.PositionKeepMode PositionKeepMode => _positionKeepCalc.PositionKeepMode;
        /// <summary>ポジ維持発動中かどうか</summary>
        public bool IsPositionKeep => _positionKeepCalc.IsPositionKeep;
        public bool IsPositionKeepSection => _positionKeepCalc.IsPositionKeepSection;
        /// <summary>ポジ維持による目指す速度倍率</summary>
        public float PositionKeepBaseTargetSpeedMultiply => _positionKeepCalc.BaseTargetSpeedMultiply;
        #endregion

        //---------------------------------------------------------------
        #region <変数：掛かり>
        /// <summary>掛かり計算機</summary>
        private IHorseTemptationCalculator _temptationCalc = null;
        /// <summary>掛かり中かどうか</summary>
        public bool IsTemptation => _temptationCalc.IsTemptation;
        /// <summary>掛かりモード</summary>
        public TemptationMode TemptationMode => _temptationCalc.Mode;
        /// <summary>このレースで掛かった回数</summary>
        public int TemptationCount => _temptationCalc.TemptationCount;
        /// <summary>掛かりを開始可能かどうか</summary>
        public bool IsTemptationStartEnable => _temptationCalc.IsTemptationStartEnable; 
        #endregion

        //---------------------------------------------------------------
        #region <変数：追い越し>
        /// <summary>追い越し計算機</summary>
        private HorseOvertakeTargetCalculator _overtakeCalc;
        /// <summary>追い越し完了後もこの時間は「追い越し状態」を維持する</summary>
        private float _overTakeCoolDownTime;
        #endregion

        //---------------------------------------------------------------
        #region <変数：レーン移動>
        /// <summary>目指すレーン</summary>
        private float _targetLane;
        /// <summary>目指すレーン計算機（使用中のものの参照をここに入れる）</summary>
        private IHorseTargetLaneCalculator _curLaneCalc;
        /// <summary>目指すレーン計算機（レース）</summary>
        private IHorseTargetLaneCalculator _targetLaneCalcRace;
        /// <summary>目指すレーン計算機（オーバーラン）</summary>
        private IHorseTargetLaneCalculator _targetLaneCalcOverRun;
        /// <summary>スタート直後の強制的にインによる処理が有効かどうか</summary>
        public bool IsForceInMoveEnable { get; private set; }
        /// <summary>イン側の空きレーンスペース</summary>
        public float InLaneSpace { get; private set; }
        /// <summary>アウト側の空きレーンスペース</summary>
        public float OutLaneSpace { get; private set; }
        /// <summary>イン側に移動可能かどうか</summary>
        public bool IsEnableInMove
        {
            get
            {
                if (null != _blockHorseIn)
                {
                    return false;
                }
                if (_ownerHorse.GetLaneDistance() <= _ownerHorse.LaneDistanceMin + 0.0001f)
                {
                    return false;
                }
                return true;
            }
        }

        /// <summary>アウト側に移動可能かどうか</summary>
        public bool IsEnableOutMove
        {
            get
            {
                if (null != _blockHorseOut)
                {
                    return false;
                }
                if (_ownerHorse.GetLaneDistance() >= _ownerHorse.LaneDistanceMax)
                {
                    return false;
                }
                return true;
            }
        }

        private float _firstMoveLanePointDistance;
        #endregion 

        //---------------------------------------------------------------
        #region <変数：速度>
        /// <summary>目指す速度基礎値計算機</summary>
        private IHorseBaseTargetSpeedCalculator _baseTargetSpeedCalc;
        
        /// <summary>目指す速度</summary>
        private float _targetSpeed;
        /// <summary>目指す速度計算機（レース）</summary>
        private IHorseTargetSpeedCalculator _targetSpeedCalcRace;
        /// <summary>目指す速度計算機（オーバーラン）</summary>
        private IHorseTargetSpeedCalculator _targetSpeedCalcOverRun;
        
        /// <summary>下り坂加速中かどうか</summary>
        public bool IsDownSlopeAccelMode { get; private set; }
        /// <summary>下り坂加速を行う抽選間隔</summary>
        private float _downSlopeAccelCheckSec = 0;
        #endregion

        //---------------------------------------------------------------
        #region <変数：視野範囲>
        /// <summary>視野範囲計算機</summary>
        private IHorseVisibleCalculator _visibleCalculator;
        /// <summary>視野範囲のキャラ。VisibleHorseCount人分までが有効</summary>
        public Gallop.AroundHorse[] VisibleHorses => _visibleCalculator.VisibleHorses;
        /// <summary>視野範囲のキャラ数</summary>
        public int VisibleHorseCount => _visibleCalculator.VisibleHorseCount; 

        /// <summary>近くのキャラ。NearHorseCount人分までが有効</summary>
        public Gallop.AroundHorse[] NearHorses { get; set; }
        /// <summary>近くのキャラ数</summary>
        public int NearHorseCount { get; set; }
        #endregion

        //---------------------------------------------------------------
        #region <変数：ラストスパート>
        /// <summary>ラストスパート計算回数。</summary>
        private int _lastSpurtCalcCount = 0;
        /// <summary>ラストスパート計算結果。</summary>
        private LastSpurtCalcResult _lastSpurtCalcResult = LastSpurtCalcResult.False;
        /// <summary> 初回計算時のラストスパート目指す速度。※計算後に値が入る </summary>
        private float _firstCalcLastSpurtTargetSpeed = 0f;
        /// <summary>ラストスパート目指す速度。※計算後に値が入る。</summary>
        private float _lastSpurtTargetSpeed = 0;
        /// <summary>ラストスパート開始距離。※計算後に値が入る。</summary>
        private float _lastSpurtStartDistance = HorseLastSpurtCalculator.LASTSPURT_DISTANCE_NULL;

        /// <summary>終盤開始時のラストスパート計算リクエスト済みかどうか</summary>
        private bool _isLastSpurtByDistanceRequested;
#if CYG_DEBUG
        private IHorsePaseMakerCalculator _dbgPaseMakerCalculator;
#endif
        #endregion

        //---------------------------------------------------------------
        #region <変数：囲まれ>
        /// <summary>囲まれ中かどうかの判定区分1</summary>
        private bool _isSurround1;
        /// <summary>囲まれ中かどうかの判定区分2</summary>
        private bool _isSurround2;
        /// <summary>囲まれ中かどうかの判定区分3</summary>
        private bool _isSurround3;
        /// <summary>囲まれ状態かどうか。_isSurround1~3の全区分がtrueなら囲まれ</summary>
        public bool IsSurrounded => _isSurround1 && _isSurround2 && _isSurround3;
        #endregion        

        //---------------------------------------------------------------
        #region <変数:上限突破パラメータパワー(足溜め)>
        private IHorseConservePowerCalculator _conservePowerCalculator;
        #endregion
        
        
        //---------------------------------------------------------------
        #region <変数:上限突破パラメータスタミナ>
        private IHorseStaminaLimitBreakBuffCalculator _staminaLimitBreakBuffCalculator;
        #endregion
        
        //---------------------------------------------------------------
        #region <変数:スパート前位置取り勝負>
        private IHorseConsumeStaminaCalculator _competeBeforeSpurtCalculator;
        #endregion
        
        //---------------------------------------------------------------
        #region <変数:リード確保>
        private IHorseConsumeStaminaCalculator _secureLeadCalculator;
        #endregion
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------

        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public HorseRaceAISimulate()
        {
            IsForceInMoveEnable = false;
        }

        //---------------------------------------------------------------
#if GALLOP
        public void Init(Gallop.IHorseRaceInfo owner, 
            Gallop.IHorseRaceInfo[] horses, 
            System.Action onTemptationStart,
            StandaloneSimulator.IHorsePaseMakerCalculator paseMakerCalculator)
        {
            // 参照されることはない
            Debug.LogError("意図しない参照が発生しています");
        }
#endif

        public void Init(
            RaceInfo raceInfo,
            IHorseRaceInfoSimulate owner, 
            IRaceHorseAccessor horseAccessor, 
            Action onTemptationStart, 
            IHorsePaseMakerCalculator paseMakerCalculator,
            float firstMoveLanePointDistance)
        {
            _raceInfo = raceInfo;
            _ownerHorse = owner;
            _horseAccessor = horseAccessor;
            _firstMoveLanePointDistance = firstMoveLanePointDistance;

            _paramDefine = _ownerHorse.ParamDefine;
            Debug.Assert(null != _paramDefine);

            // オーナー以外の馬情報を配列にセットする
            var horses = _horseAccessor.GetHorseRaceInfos();
            _aroundHorses = new Gallop.AroundHorse[_horseAccessor.GetHorseNumber() - 1];
            int idx = 0;
            for (int i = 0; i < horses.Length; i++)
            {
                if (_ownerHorse != horses[i])
                {
                    _aroundHorses[idx] = new Gallop.AroundHorse();
                    _aroundHorses[idx].infoSimulate = horses[i];
                    idx++;
                }
            }

            _positionKeepCalc = CreatePositionKeepCalculator(paseMakerCalculator);
            _temptationCalc = CreateTemptationCalculator(onTemptationStart);
            _overtakeCalc = CreateOvertakeCalculator();
            _targetLaneCalcRace = CreateTargetLaneCalculator();
            _targetLaneCalcOverRun = CreateTargetLaneCalculatorOverRun();
            _targetSpeedCalcRace = CreateTargetSpeedCalculator();
            _targetSpeedCalcOverRun = CreateTargetSpeedCalculatorOverRun();
            _visibleCalculator = CreateVisibleCalculator();
            _baseTargetSpeedCalc = CreateBaseTargetSpeedCalculator();
            // 上限突破パラメータは緑スキルなどが適用された後のパラメータを使用して初期化するためこの時点ではNullオブジェクトを生成しておく
            _conservePowerCalculator = new HorseConservePowerCalculatorNull();
            _staminaLimitBreakBuffCalculator = new HorseStaminaLimitBreakBuffCalculatorNull();
            _competeBeforeSpurtCalculator = CreateCompeteBeforeSpurtCalculator();
            _secureLeadCalculator = CreateSecureLeadCalculator();
            _targetSpeed = _ownerHorse.GetLastSpeed();
            _targetLane = _ownerHorse.GetLaneDistance();

            _curLaneCalc = _targetLaneCalcRace;

            NearHorses = new Gallop.AroundHorse[ _aroundHorses.Length ];
            NearHorseCount = 0;
#if CYG_DEBUG
            _dbgPaseMakerCalculator = paseMakerCalculator;
#endif
        }
        
        public void Release()
        {
            _aroundHorses = null;
            _positionKeepCalc = null;
            _temptationCalc = null;
            _overtakeCalc = null;
            _targetLaneCalcRace = null;
            _targetLaneCalcOverRun = null;
            _targetSpeedCalcRace = null;
            _targetSpeedCalcOverRun = null;
            _visibleCalculator = null;
            _baseTargetSpeedCalc = null;
            _conservePowerCalculator = null;
            _staminaLimitBreakBuffCalculator = null;
        }

        //---------------------------------------------------------------
        public void LotTemptation()
        {
            _temptationCalc.LotTemptationStartEnable();
        }

        /// <summary>
        /// ポジション維持機能計算機生成。
        /// </summary>
        private IHorsePositionKeepCalculator CreatePositionKeepCalculator(IHorsePaseMakerCalculator paseMakerCalculator)
        {
#if CYG_DEBUG
            // デバッグ機能：AI機能ON/OFF：ポジション維持機能。
            if( !RaceSimulateDebugger.IsEnableAIPositionKeep )
            {
                return new HorsePositionKeepCalculatorNull();
            }
#endif
            return new HorsePositionKeepCalculator( 
                _ownerHorse, 
                _raceInfo.CourseDistance,
                Gallop.RaceDefine.COURSE_SECTION_NUM,
                _paramDefine.PositionKeep, 
                _horseAccessor, 
                RaceManagerSimulate.Instance,
                paseMakerCalculator);
        }

        /// <summary>
        /// 掛かり機能計算機生成。
        /// </summary>
        private IHorseTemptationCalculator CreateTemptationCalculator(System.Action onTemptationStart)
        {
#if CYG_DEBUG
            // デバッグ機能：AI機能ON/OFF：掛かり。
            if( !RaceSimulateDebugger.IsEnableAITemptation )
            {
                return new HorseTemptationCalculatorNull();
            }
#endif
            return new HorseTemptationCalculator( 
                _ownerHorse, 
                _paramDefine.Temptation, 
                RaceManagerSimulate.Instance,
                onTemptationStart);
        }

        /// <summary>
        /// 追い越し計算機生成。
        /// </summary>
        private HorseOvertakeTargetCalculator CreateOvertakeCalculator()
        {
            return new HorseOvertakeTargetCalculator(
                _ownerHorse,
                this,
                _paramDefine);
        }

        /// <summary>
        /// 目指すレーン計算機生成：レース中。
        /// </summary>
        private IHorseTargetLaneCalculator CreateTargetLaneCalculator()
        {
            if( !_ownerHorse.IsEmptyInMove )
            {
                return new HorseTargetLaneCalculatorNull(this);
            }
#if CYG_DEBUG
            // デバッグ機能：AI機能ON/OFF：レーン移動。
            if (!RaceSimulateDebugger.IsEnableAIMoveLane)
            {
                return new HorseTargetLaneCalculatorNull(this);
            }
#endif

            return new HorseTargetLaneCalculatorRace(
                _ownerHorse,
                this,
                _paramDefine,
                _ownerHorse.GetLaneDistance(),
                _firstMoveLanePointDistance,
                _raceInfo.RaceCourseSet.RunOutSide
            );
        }

        /// <summary>
        /// 目指すレーン計算機生成：オーバーラン中。
        /// </summary>
        private IHorseTargetLaneCalculator CreateTargetLaneCalculatorOverRun()
        {
            // オーバーラン中の行動はレース再生時に計算をするため、シミュレート段階では適当（現在レーンを維持するNullオブジェクト）でOK。
            return new HorseTargetLaneCalculatorNull(this);
        }

        /// <summary>
        /// 目指す速度計算機生成：レース中。
        /// </summary>
        private IHorseTargetSpeedCalculator CreateTargetSpeedCalculator()
        {
            return new HorseTargetSpeedCalculatorRace(
                _ownerHorse,
                this,
                _paramDefine,
                RaceManagerSimulate.Instance);
        }

        /// <summary>
        /// 目指す速度計算機生成：オーバーラン中。
        /// </summary>
        private IHorseTargetSpeedCalculator CreateTargetSpeedCalculatorOverRun()
        {
            // オーバーラン中の行動はレース再生時に計算をするため、シミュレート段階では適当（現在速度を維持するNullオブジェクト）でOK。
            return new HorseTargetSpeedCalculatorNull(this);
        }

        /// <summary>
        /// 視野範囲計算機生成。
        /// </summary>
        private IHorseVisibleCalculator CreateVisibleCalculator()
        {
            return new HorseVisibleCalculator(_aroundHorses.Length);
        }

        /// <summary>
        /// 目指す速度基礎値計算機生成。
        /// </summary>
        private IHorseBaseTargetSpeedCalculator CreateBaseTargetSpeedCalculator()
        {
            return new HorseBaseTargetSpeedCalculator(
                Gallop.RaceDefine.COURSE_SECTION_NUM,
                _raceInfo.CourseSectionDistance,
                _raceInfo.BaseSpeed);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 上限突破パラメータパワー(足溜め)計算機生成
        /// </summary>
        private IHorseConservePowerCalculator CreateConservePowerCalculator()
        {
#if CYG_DEBUG
            if (!RaceSimulateDebugger.IsEnableAIConservePower)
            {
                return new HorseConservePowerCalculatorNull();
            }
#endif

            //---------------------------------------------------------------
            // 足溜めで参照されるパワーが上限を超えていた分だけ計算機の実態を作る
            if (HorseConservePowerCalculator.CalcPower(_ownerHorse) > 0)
            {
                return new HorseConservePowerCalculator(
                    _ownerHorse, 
                    this, 
                    _raceInfo.CourseDistance,
                    _paramDefine.ConservePower, 
                    RaceManagerSimulate.Instance, 
                    RaceManagerSimulate.Instance);
            }
            
            return new HorseConservePowerCalculatorNull();
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// 上限突破パラメータスタミナ計算機生成
        /// </summary>
        private IHorseStaminaLimitBreakBuffCalculator CreateStaminaLimitBreakBuffCalculator()
        {
#if CYG_DEBUG
            if (!RaceSimulateDebugger.IsEnableAIStaminaLimitBreakBuff)
            {
                return new HorseStaminaLimitBreakBuffCalculatorNull();
            }
#endif
            //---------------------------------------------------------------
            // 参照するスタミナ値が0以上であれば計算機の実態を作る
            if (HorseStaminaLimitBreakBuffCalculator.CalcStamina(_ownerHorse) > 0)
            {
                var calculator = new HorseStaminaLimitBreakBuffCalculator(
                    _ownerHorse, _raceInfo.CourseDistance, _paramDefine.StaminaLimitBreakBuff, RaceManagerSimulate.Instance, RaceManagerSimulate.Instance
                );
                // 目指す速度の加算値が0以上であればこのまま返す
                if (calculator.RawTargetSpeed > 0f)
                {
                    return calculator;
                }
            }
            return new HorseStaminaLimitBreakBuffCalculatorNull();
        }
        //---------------------------------------------------------------
        /// <summary>
        /// スパート前位置取り勝負　計算機生成
        /// </summary>
        /// <returns></returns>
        private IHorseConsumeStaminaCalculator CreateCompeteBeforeSpurtCalculator()
        {
            IHorseConsumeStaminaCalculator calculator = new HorseCompeteBeforeSpurtCalculatorNull();
            
#if CYG_DEBUG
            if (!RaceSimulateDebugger.IsEnableAICompeteBeforeSpurt)
            {
                return calculator;
            }
#endif
            //---------------------------------------------------------------
            
            // スパート用パラメーター計算に必要な情報をまとめたモデルクラス
            var model = new HorseLastSpurtCalculator.CalcBaseTargetSpeedAndStatusSpeedModel(
                baseTargetSpeedCoef: _paramDefine.baseTargetSpeedCoef,
                lastSpurtTargetSpeedCoefSqrt: _paramDefine.LastSpurtTargetSpeedCoefSqrt,
                addSpeedParamCoef: _paramDefine.addSpeedParamCoef,
                lastSpurtTargetSpeedGutsPow: _paramDefine.LastSpurtTargetSpeedGutsPow,
                lastSpurtTargetSpeedGutsCoef1: _paramDefine.LastSpurtTargetSpeedGutsCoef1,
                lastSpurtTargetSpeedGutsCoef2: _paramDefine.LastSpurtTargetSpeedGutsCoef2);
            
            calculator = new HorseCompeteBeforeSpurtCalculator(
                _ownerHorse, this, _horseAccessor, RaceManagerSimulate.Instance, RaceManagerSimulate.Instance,
                RaceManagerSimulate.Instance,
                _paramDefine.CompeteBeforeSpurt, _paramDefine.Hp, _paramDefine.PositionKeep, model,
                _raceInfo.CourseDistance, _raceInfo.BaseSpeed, _paramDefine.lastSpurtBaseTargetSpeedAddCoef
            );
            return calculator;
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// リード確保　計算機生成
        /// </summary>
        /// <returns></returns>
        private IHorseConsumeStaminaCalculator CreateSecureLeadCalculator()
        {
            IHorseConsumeStaminaCalculator calculator = new HorseSecureLeadCalculatorNull();
            
#if CYG_DEBUG
            if (!RaceSimulateDebugger.IsEnableAISecureLead)
            {
                return calculator;
            }
#endif
            //---------------------------------------------------------------
            
            calculator = new HorseSecureLeadCalculator(_ownerHorse, _horseAccessor, RaceManagerSimulate.Instance,
                RaceManagerSimulate.Instance, RaceManagerSimulate.Instance, _paramDefine.SecureLead, _raceInfo.CourseDistance);
            return calculator;
        }
        
        
        //---------------------------------------------------------------
        public void Update(float elapsedTime)
        {
            // このUpdateは各ウマについて順位判定して距離を更新していく処理なので、相互距離に関連する処理はPostUpdateに出す必要がある（Compete系など）
            UpdatePositionOvertake( elapsedTime );
            UpdateOverTake( elapsedTime );
            UpdateTemptation( elapsedTime );
            UpdateMoveLane( elapsedTime );
            UpdateForceInMove( elapsedTime );
            UpdateLastSpurtRequestByDistance();
            UpdateLastSpurt();
            UpdateDownSlopeAccel(elapsedTime);
            // ラストスパートの判定を行ってから実行すること
            UpdateConservePower(elapsedTime);
            // 掛かりに依存するので、UpdateTemptationの後に呼び出す必要がある。
            UpdateTargetSpeed( elapsedTime );
            UpdateStaminaAdditionalEffect(elapsedTime);
#if CYG_DEBUG
            if (_dbgPaseMakerCalculator.PaseMaker != null)
            {
                int section = _ownerHorse.CalcSection();
                if (section >= _paramDefine.PositionKeep.PositionKeepStartSection &&
                    section <= _paramDefine.PositionKeep.PositionKeepEndSection)
                {
                    _ownerHorse.DbgPaseMakerHorseIndex = _dbgPaseMakerCalculator.PaseMaker.HorseIndex;
                    _ownerHorse.DbgTopHorseNotMostForwardRunningStyleCnt = _dbgPaseMakerCalculator.DbgTopHorseNotMostForwardRunningStyleCnt;
                }
                else
                {
                    _ownerHorse.DbgPaseMakerHorseIndex = Gallop.RaceDefine.HORSE_INDEX_NULL;
                    _ownerHorse.DbgTopHorseNotMostForwardRunningStyleCnt = 0;
                }
            }
            // 自身を追い抜き対象としているウマ娘情報更新
            UpdateOverTakenHorse();
#endif  
        }

        private void UpdateForceInMove( float elapsedTime )
        {
            IsForceInMoveEnable = false;

#if CYG_DEBUG
            if( !RaceSimulateDebugger.IsEnableAIForceInMove )
            {
                return;
            }
#endif

            //-----------------------------------------------------------
            // インに寄らないレースでは不要。
            //-----------------------------------------------------------
            if(!_ownerHorse.IsEmptyInMove)
            {
                return;
            }

            //-----------------------------------------------------------
            // 中盤以降では行わない。
            //-----------------------------------------------------------
            if( OwnerPhase >= Gallop.RaceDefine.HorsePhase.MiddleRun )
            {
                return;
            }

            //-----------------------------------------------------------
            // 一定レーン以上でなければ不要。
            //-----------------------------------------------------------
            if( OwnerLane < _paramDefine.forceInMoveLaneThreshold )
            {
                return;
            }

            //-----------------------------------------------------------
            // インをブロックされていなければ不要。
            //-----------------------------------------------------------
            if( _ownerHorse.IsEnableInMove )
            {
                return;
            }

            //-----------------------------------------------------------
            // 速度加算有効化。
            //-----------------------------------------------------------
            IsForceInMoveEnable = true;
        }
        
        //---------------------------------------------------------------
        public void UpdateAfterGoalTime(float deltaTime)
        {
            if (!_ownerHorse.IsFinished())
            {
                return;
            }
            float beforeTime = _afterGoalTime;
            _afterGoalTime += deltaTime;

            // ゴール後一定時間経過でオーバーラン用のレーン移動処理に切り替え。
            if( beforeTime < AFTER_GOAL_SPEED_DOWN_TIME &&
                _afterGoalTime >= AFTER_GOAL_SPEED_DOWN_TIME )
            {
                _curLaneCalc = _targetLaneCalcOverRun;
            }
        }
    
        //---------------------------------------------------------------
        private void UpdatePositionOvertake( float deltaTime )
        {
            // ゴール後は更新しない。
            if( _ownerHorse.IsFinished() )
            {
                return;
            }
            _positionKeepCalc.Update( deltaTime );
        }

        //---------------------------------------------------------------
        private void UpdateTemptation( float deltaTime )
        {
            // ゴール後は更新しない。
            if( _ownerHorse.IsFinished() )
            {
                return;
            }

            // スキルによる興奮終了時間の上書き。
            // ※GetSkillValueParamの値はデフォルト0である。
            // ※そのため即時終了させたいときは負の値(-1など)をcsvで入力してもらっている。
            if(_temptationCalc.IsTemptation)
            {
                float skillValue = _ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TemptationEndTime, 0);
                if (!RaceUtilMath.Approximately(skillValue, 0))
                {
                    _temptationCalc.SetForceEndTimeBySkill(skillValue);
                }
            }

            _temptationCalc.Update( deltaTime );
        }

        //---------------------------------------------------------------
        private void UpdateConservePower(float deltaTime)
        {
            // ゴール後は更新しない。
            if (_ownerHorse.IsFinished())
            {
                return;
            }

            _conservePowerCalculator.UpdateConservePower(deltaTime);
        }
        
        //---------------------------------------------------------------
        private void UpdateStaminaAdditionalEffect(float deltaTime)
        {
            // ゴール後は更新しない。
            if (_ownerHorse.IsFinished())
            {
                return;
            }

            _staminaLimitBreakBuffCalculator.UpdateStaminaLimitBreakBuff(deltaTime);
        }

        //---------------------------------------------------------------
        public bool IsOverRunSpeedDownMode => _afterGoalTime >= AFTER_GOAL_SPEED_DOWN_TIME;

        //---------------------------------------------------------------
        private void UpdateSurrounded()
        {
            _isSurround1 = false;
            _isSurround2 = false;
            _isSurround3 = false;
            for (int i = 0; i < _aroundHorses.Length; ++i)
            {
                var around = _aroundHorses[i];
                
                if (!_isSurround1)
                {
                    if (around.distanceGapAbs <= _paramDefine.Surrounded.DistanceGapAbs1)
                    {
                        if (around.laneGap >= 0 && around.laneGap <= _paramDefine.Surrounded.LaneGap1)
                        {
                            _isSurround1 = true;
                        }
                    }
                }

                if (!_isSurround2)
                {
                    if (around.distanceGap > 0 && around.distanceGap <= _paramDefine.Surrounded.DistanceGap2)
                    {
                        if (around.laneGapAbs <= _paramDefine.Surrounded.LaneGapAbs2)
                        {
                            _isSurround2 = true;
                        }
                    }
                }
                
                if (!_isSurround3)
                {
                    if (around.distanceGap < 0 && around.distanceGap >= -_paramDefine.Surrounded.DistanceGap2)
                    {
                        if (around.laneGapAbs <= _paramDefine.Surrounded.LaneGapAbs2)
                        {
                            _isSurround3 = true;
                        }
                    }
                }
            }
        }

        #region <ラストスパート>
        /// <summary>
        /// 一定距離に到達したらラストスパート開始計算のリクエストを出す。このリクエストは１レースに１回のみ。
        /// </summary>
        private void UpdateLastSpurtRequestByDistance()
        {
            // 既に終盤開始時のラストスパート計算リクエスト発行済みなら何もしない。
            if (_isLastSpurtByDistanceRequested)
            {
                return;
            }

            // 終盤に到達していなければラストスパート計算しない。
            if (_ownerHorse.GetPhase() < Gallop.RaceDefine.HorsePhase.End)
            {
                return;
            }

            IsReqCalcLastSpurt = true;
            _isLastSpurtByDistanceRequested = true;
        }
            
        /// <summary>
        /// ラストスパート開始リクエストが来ていたらラストスパート計算を行う。
        /// </summary>
        private void UpdateLastSpurt()
        {
        #if CYG_DEBUG
            if(!RaceSimulateDebugger.IsEnableAILastSpurt)
            {
                return;
            }
        #endif

            if (!IsReqCalcLastSpurt)
            {
                return;
            }
            
            // ラストスパート計算。
            var calcParam = new HorseLastSpurtCalculator.LastSpurtCalcParam( 
                horseIndex:_ownerHorse.HorseIndex,
                distance:OwnerDistance,
                laneDistance:OwnerLane,
                hp:_ownerHorse.GetHp(),
                phase:_ownerHorse.GetPhase(),
                speed:_ownerHorse.Speed,
                guts:_ownerHorse.Guts,
                wiz:_ownerHorse.Wiz,
                baseTargetSpeed:GetBaseTargetSpeedWithoutRandom(_ownerHorse.GetPhase()), // 終盤の目指す速度基礎値からランダム値を除いたもの。
                minSpeed:_ownerHorse.MinSpeed,
                groundModifierMultiHpSub:_ownerHorse.GroundModeifierParam.multiHpSub,
                properDistanceCoef:_ownerHorse.ProperDistanceCoefSpeed,
                properGroundTypeCoef:_ownerHorse.ProperGroundTypeCoef,
                courseDistance:_raceInfo.CourseDistance,
                raceBaseSpeed:_raceInfo.BaseSpeed,
                runningStyle:_ownerHorse.RunningStyle,
                randomGenerator:RaceManagerSimulate.Instance);
            
            var lastSpurtCalculator = new HorseLastSpurtCalculator(_paramDefine, _raceInfo.BaseSpeed);
        #if CYG_DEBUG && UNITY_EDITOR
            lastSpurtCalculator.DbgOutputParam = _ownerHorse.LastSpurtParam;
        #endif
            
            lastSpurtCalculator.CalcLastSpurt( ref calcParam );
            _lastSpurtCalcCount++;

            // 結果受け取り。１回目は必ず受け取る。
            if (_lastSpurtCalcCount <= 1)
            {
                _lastSpurtCalcResult = lastSpurtCalculator.CalcResult;
                _lastSpurtTargetSpeed = lastSpurtCalculator.LastSpurtTargetSpeed;
                _firstCalcLastSpurtTargetSpeed = _lastSpurtTargetSpeed;
                _lastSpurtStartDistance = lastSpurtCalculator.LastSpurtStartDistance;
            #if CYG_DEBUG
                _ownerHorse.DbgLastSpurtCandidateList = lastSpurtCalculator.DbgCandidateList;
                _ownerHorse.DbgLastSpurtUseCandidate = lastSpurtCalculator.DbgUseCandidate;
                _ownerHorse.DbgLastSpurtLastCalculatedSpeedQueue.Enqueue(lastSpurtCalculator.LastSpurtTargetSpeed);
            #endif
            }
            // ２回目以降はラストスパート成功の時だけ受け取る。
            else
            {
                if ((lastSpurtCalculator.CalcResult & LastSpurtCalcResult.TrueFlags) != 0)
                {
                    _lastSpurtCalcResult = lastSpurtCalculator.CalcResult;
                    _lastSpurtTargetSpeed = lastSpurtCalculator.LastSpurtTargetSpeed;

                    // #79087 １回目の計算を失敗している場合開始距離に負数が入ったままなのでここで受け取る。１回目の計算を成功している場合の開始距離は１回目の値を優先する。
                    if (_lastSpurtStartDistance < 0)
                    {
                        _lastSpurtStartDistance = lastSpurtCalculator.LastSpurtStartDistance;
                    }

                #if CYG_DEBUG
                    _ownerHorse.DbgLastSpurtCandidateList.AddRange(lastSpurtCalculator.DbgCandidateList);
                    _ownerHorse.DbgLastSpurtUseCandidate = lastSpurtCalculator.DbgUseCandidate;

                    // 目指す速度は最新のｎ件だけを保持する。
                    const int LAST_SPURT_SPEED_QUEUE_MAX = 10;
                    _ownerHorse.DbgLastSpurtLastCalculatedSpeedQueue.Enqueue(lastSpurtCalculator.LastSpurtTargetSpeed);
                    if (_ownerHorse.DbgLastSpurtLastCalculatedSpeedQueue.Count > LAST_SPURT_SPEED_QUEUE_MAX)
                    {
                        _ownerHorse.DbgLastSpurtLastCalculatedSpeedQueue.Dequeue();
                    }
                #endif
                }
            }
        #if CYG_DEBUG
            _ownerHorse.DbgLastSpurtCalcCount = _lastSpurtCalcCount;
        #endif
            
            // リクエスト解除。
            IsReqCalcLastSpurt = false;
        }

        public float GetBaseTargetSpeedWithoutRandom(Gallop.RaceDefine.HorsePhase phase)
        {
            return _baseTargetSpeedCalc.GetBaseTargetSpeedWithoutRandom(phase);
        }
        #endregion
        
        //---------------------------------------------------------------
        public void UpdateAroundHorsesParam(float deltaTime)
        {
            _isAroundHorseInitialized = true;

            _blockHorseIn = null;
            _blockHorseOut = null;
            _blockHorse = null;

            float ownerLastSpeed = _ownerHorse.GetLastSpeed();
            float ownerDistance = _ownerHorse.GetDistance();
            float ownerLane = _ownerHorse.GetLaneDistance();

            InLaneSpace = ownerLane;
            OutLaneSpace = _ownerHorse.LaneDistanceMax - ownerLane;

            // 視野情報のクリア。
            ClearVisible();
            // 近くのキャラ情報のクリア。
            ClearNearHorse();

            CongestionCount = 0;
            var paramDefine = _paramDefine;

            float minDistance = float.MaxValue;

            float visibleDistanceMax = _ownerHorse.VisibleDistance;

            for (int i = 0; i < _aroundHorses.Length; ++i)
            {
                var aroundHorse = _aroundHorses[i];
                var targetHorseInfo = aroundHorse.infoSimulate;

                // 距離差分 +なほど相手が前にいる
                aroundHorse.distanceGap = targetHorseInfo.GetDistance() - ownerDistance;
                aroundHorse.distanceGapAbs = aroundHorse.distanceGap;
                if (aroundHorse.distanceGapAbs < 0)
                {
                    aroundHorse.distanceGapAbs = -aroundHorse.distanceGapAbs;
                }

                // 横位置差分 +なほど相手が外にいる
                aroundHorse.laneGap = targetHorseInfo.GetLaneDistance() - ownerLane;
                aroundHorse.laneGapAbs = aroundHorse.laneGap;
                if (aroundHorse.laneGapAbs < 0)
                {
                    aroundHorse.laneGapAbs = -aroundHorse.laneGapAbs;
                }

                // スピード差分 +なほど自分が速い
                aroundHorse.speedGap = ownerLastSpeed - targetHorseInfo.GetLastSpeed();
                aroundHorse.speedGapAbs = aroundHorse.speedGap;
                if (aroundHorse.speedGapAbs < 0)
                {
                    aroundHorse.speedGapAbs = -aroundHorse.speedGapAbs;
                }

                // 近くのキャラかどうかチェック。
                CheckNearHorse(aroundHorse);

                // 競り合い候補かどうかチェック。
                CheckCompeteFightNear(deltaTime, aroundHorse);
                
                // サイドブロック判定に使用する距離差分以上後方に離れているキャラはチェック対象外とする。
                if (aroundHorse.distanceGap < -_paramDefine.Block.SideBlockDistanceGap)
                {
                    aroundHorse.isBack = true;
                    continue;
                }
                aroundHorse.isBack = false;

                //-------------------------------------------------------
                // ※※※　ここから先は後方のキャラはチェックされない。　※※※
                //-------------------------------------------------------

                //-------------------------------------------------------
                // 前方ブロック判定。
                //-------------------------------------------------------
                if (CheckFrontBlock(aroundHorse.distanceGap, aroundHorse.laneGapAbs))
                {
                    // 最も近いキャラでなければブロックされているとみなさない。
                    if (aroundHorse.distanceGap < minDistance)
                    {
                        minDistance = aroundHorse.distanceGap;
                        _blockHorse = aroundHorse;
                    }
                }

                //-------------------------------------------------------
                // サイドブロック判定。
                //-------------------------------------------------------
                if (CheckSideBlock(aroundHorse.distanceGapAbs, aroundHorse.laneGapAbs))
                {
                    if (aroundHorse.laneGap > 0.0f)
                    {
                        _blockHorseOut = aroundHorse;
                    }
                    else
                    {
                        _blockHorseIn = aroundHorse;
                    }
                }

                // 前後一定距離内にいるキャラの場合、内/外の空きスペース更新。
                // ※追い抜きで内/外それぞれに移動可能な範囲参照のため。
                if (aroundHorse.distanceGapAbs < _paramDefine.Block.SideBlockDistanceGap)
                {
                    // 内側の空きスペース。
                    if (aroundHorse.laneGap < 0)
                    {
                        if (InLaneSpace > aroundHorse.laneGapAbs)
                        {
                            InLaneSpace = aroundHorse.laneGapAbs;
                        }
                    }
                    // 外側の空きスペース。
                    else if (aroundHorse.laneGap > 0)
                    {
                        if (OutLaneSpace > aroundHorse.laneGapAbs)
                        {
                            OutLaneSpace = aroundHorse.laneGapAbs;
                        }
                    }
                }

                //-------------------------------------------------------
                // 進路上にいるキャラ数カウント。
                //-------------------------------------------------------
                if (aroundHorse.laneGapAbs <= paramDefine.CongestionLaneGapAbs &&
                    aroundHorse.distanceGap > 0.0f)
                {
                    ++CongestionCount;
                }

                //-------------------------------------------------------
                // 視野範囲に入っているかチェック。
                //-------------------------------------------------------
                CheckVisible(visibleDistanceMax, aroundHorse);
            }

            // 囲まれているかチェック。
            UpdateSurrounded();
        }

        private bool CheckFrontBlock(float distanceGap, float laneGapAbs)
        {
            // 自分より後方にいるなら前方ブロックではない。
            if (distanceGap <= 0.0f)
            {
                return false;
            }

            // 前方一定距離以上離れているなら前方ブロックではない。
            if (distanceGap >= _paramDefine.Block.FrontBlockDistanceGap)
            {
                return false;
            }

#if true
            // 0.0~1.0で指定。1.0に近いほど台形の短辺（自分側）が短くなる。
            const float DISTANCE_GAP_NORMALIZE_RATE = 0.6f;

            // 前方のキャラとどれくらい離れているかを、最大ParamDefine.frontBlockDistanceGapとして、0~1に正規化。
            float distanceGapNormalized = distanceGap / _paramDefine.Block.FrontBlockDistanceGap;
            distanceGapNormalized *= DISTANCE_GAP_NORMALIZE_RATE;

            // 前方のキャラと離れているほど、前方ブロックと見なすレーン幅を小さくする。
            // ・前方のキャラとのdistanceGapが0の時をレーン幅最長（台形の長辺）
            // ・前方のキャラとのdistanceGapがParamDefine.frontBlockDistanceGapの時をレーン幅最短（台形の短辺）
            // となるようにする。
            float laneGapRate = (1 - distanceGapNormalized) * _paramDefine.Block.FrontBlockLaneGap;

            // レーンが一定距離以上離れているなら前方ブロックではない。
            if (laneGapAbs > laneGapRate)
            {
                return false;
            }
#else
            // レーンが一定距離以上離れているなら前方ブロックではない。
            if( laneGapAbs > ParamDefine.frontBlockLaneGap )
            {
                return false;
            }
#endif
            return true;
        }

        private bool CheckSideBlock(float distanceGapAbs, float laneGapAbs)
        {
            // 前後で一定距離以上離れているならサイドブロックではない。
            if (distanceGapAbs >= _paramDefine.Block.SideBlockDistanceGap)
            {
                return false;
            }

            // レーンが一定距離以上離れているならサイドブロックではない。
            if (laneGapAbs >= _paramDefine.Block.SideBlockLaneGap)
            {
                return false;
            }
            return laneGapAbs < 0.0f || laneGapAbs > 0.0f;
        }

        /// <summary>
        /// 視野範囲情報のクリア。
        /// </summary>
        private void ClearVisible()
        {
            _visibleCalculator.Clear();
        }

        /// <summary>
        /// 指定のキャラが視野範囲にいるかチェック。いれば視野範囲リストに登録する。
        /// </summary>
        private void CheckVisible(float visibleDistanceMax, Gallop.AroundHorse horse)
        {
            _visibleCalculator.CheckVisible(visibleDistanceMax, horse);
        }

        private void ClearNearHorse()
        {
            NearHorseCount = 0;
            Array.Clear( NearHorses, 0, NearHorses.Length );
        }

        private void CheckNearHorse(Gallop.AroundHorse horse)
        {
            if(horse.distanceGapAbs > _paramDefine.Near.DistanceThreshold)
            {
                return;
            }
            if(horse.laneGapAbs > _paramDefine.Near.LaneDistanceThreshold)
            {
                return;
            }
            NearHorses[NearHorseCount] = horse;
            ++NearHorseCount;
        }

        //---------------------------------------------------------------
        private void CheckCompeteFightNear(float deltaTime, Gallop.AroundHorse aroundHorse)
        {
            bool isDistanceNear = aroundHorse.distanceGapAbs <= _paramDefine.CompeteFight.DistanceGap;
            bool isLaneNear = aroundHorse.laneGapAbs <= _paramDefine.CompeteFight.LaneGap;
            bool isInLastStraight = _ownerHorse.IsStraightLast || aroundHorse.infoSimulate.IsStraightLast;  
        
            // 距離・レーンがある程度離れているか、自分も相手も最終直線にいない場合、候補リストから外す。 
            if (!isDistanceNear || !isLaneNear || !isInLastStraight)
            {
                _ownerHorse.CompeteFightNearList.Remove(aroundHorse);
                aroundHorse.CompeteFightCandidateContinueTime = 0;
                return;
            }
            
            // 既に候補リストに入っているキャラは、近くにい続けている時間を加算する。
            if (_ownerHorse.CompeteFightNearList.Contains(aroundHorse))
            {
                aroundHorse.CompeteFightCandidateContinueTime += deltaTime;
            }
            else
            {
                _ownerHorse.CompeteFightNearList.Add(aroundHorse);
            }
        }
        
        //---------------------------------------------------------------
        public Gallop.AroundHorse[] GetAroundHorses()
        {
#if CYG_DEBUG
            if (!_isAroundHorseInitialized)
            {
                Debug.LogError("AourndHorseが初期化されていません");
            }
#endif
            return _aroundHorses;
        }


        //---------------------------------------------------------------
        public bool IsBlockFront()
        {
            return GetBlockHorse() != null;
        }

        //---------------------------------------------------------------
        public bool IsBlockSide()
        {
            return GetBlockHorseIn() != null || GetBlockHorseOut() != null;
        }

        //---------------------------------------------------------------
        public Gallop.AroundHorse GetBlockHorse() { return _blockHorse; }
        //---------------------------------------------------------------
        public Gallop.AroundHorse GetBlockHorseIn() { return _blockHorseIn; }
        //---------------------------------------------------------------
        public Gallop.AroundHorse GetBlockHorseOut() { return _blockHorseOut; }

        //---------------------------------------------------------------
        public void CalcBaseTargetSpeed()
        {
#if CYG_DEBUG
            _baseTargetSpeedCalc.DbgOwnerHorse = _ownerHorse;
#endif
            _baseTargetSpeedCalc.CalcBaseTargetSpeedArray(
                _raceInfo.PhaseCalc,
                RaceManagerSimulate.Instance,
                _paramDefine.BasetTargetSpeed,
                OwnerRunningStyle,
                OwnerRunningStyleEx,
                _ownerHorse.Wiz,
                _ownerHorse.Speed,
                _ownerHorse.ProperDistanceCoefSpeed,
                _paramDefine.addSpeedParamCoef);
        }

        //---------------------------------------------------------------
        public float GetBaseTargetSpeed()
        {
            return _baseTargetSpeedCalc.GetBaseTargetSpeed(OwnerDistance);
        }

#if CYG_DEBUG
        public float DbgOverTakeLane { get; set; }
        public  float DbgOverTakeCoolDownTime 
        { 
            get { return _overTakeCoolDownTime; }
        }
        public List<int> DbgOverTakenHorseIndexList { get; set; }
        public float DbgInLaneSpace { get { return InLaneSpace; } }
        public float DbgOutLaneSpace { get { return OutLaneSpace; } }

        public float DbgCurDistanceDiffFromPaseMaker { get { return _positionKeepCalc.DbgCurDistanceDiffFromPaseMaker; } }
        public float DbgDistanceDiffMin { get { return _positionKeepCalc.DbgDistanceDiffMin; } }
        public float DbgDistanceDiffMax { get { return _positionKeepCalc.DbgDistanceDiffMax; } }
        public float DbgAdjustTargetDistanceDiff { get { return _positionKeepCalc.DbgAdjustTargetDistanceDiff; } }
        public float DbgPositionKeepCoolDownTime => _positionKeepCalc.DbgCoolDownTime;
        
        public float DbgConservePowerAddAccel => _conservePowerCalculator.DbgAccelValue;
        public float DbgConservePowerAddAccelRemainTime => _conservePowerCalculator.DbgRemainTime;
        public float DbgConservePower => _conservePowerCalculator.DbgAccumulateConservePowerCalculator.ConservePower;
        public float DbgConservePowerActivityTime => _conservePowerCalculator.DbgActivityTime;
        public float DbgConservePowerIncreaseCoolTime => _conservePowerCalculator.DbgAccumulateConservePowerCalculator.DbgConservePowerIncreaseCoolTime;

        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>
            DbgConservePowerIncreaseTimeDict => _conservePowerCalculator.DbgAccumulateConservePowerCalculator.DbgConservePowerIncreaseTimeDict;

        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>
            DbgConservePowerDecreaseTimeDict => _conservePowerCalculator.DbgAccumulateConservePowerCalculator.DbgConservePowerDecreaseTimeDict;

        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>
            DbgConservePowerIncreaseTimeDictPerRecord => _conservePowerCalculator.DbgAccumulateConservePowerCalculator.DbgConservePowerIncreaseTimeDictPerRecord;

        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>
            DbgConservePowerDecreaseTimeDictPerRecord => _conservePowerCalculator.DbgAccumulateConservePowerCalculator.DbgConservePowerDecreaseTimeDictPerRecord;

        public void ClearDbgConservePowerIncreaseTimeDictPerRecord()
        {
            _conservePowerCalculator.DbgAccumulateConservePowerCalculator.ClearDbgConservePowerIncreaseTimeDictPerRecord();
        }
        public void ClearDbgConservePowerDecreaseTimeDictPerRecord()
        {
            _conservePowerCalculator.DbgAccumulateConservePowerCalculator.ClearDbgConservePowerDecreaseTimeDictPerRecord();
        }
        public float DbgCompeteFightTime { get; set; }
        public float DbgStaminaLimitBreakBuffAddTargetSpeed => _staminaLimitBreakBuffCalculator.DbgAddTargetSpeed;
        public Gallop.RaceDefine.RandomTableType DbgStaminaLimitBreakBuffLotteryRandomTableType
            => _staminaLimitBreakBuffCalculator.DbgLotteryRandomTableType;
        public Dictionary<Gallop.RaceDefine.RandomTableType, float> DbgStaminaLimitBreakBuffRandomTableProbabilityDict
            => _staminaLimitBreakBuffCalculator.DbgStaminaLimitBreakBuffRandomTableProbabilityDict;
        public float DbgStaminaLimitBreakBuffRandomTableCoef => _staminaLimitBreakBuffCalculator.DbgRandomTableCoef;
        public float DbgStaminaLimitBreakBuffActivateTime => _staminaLimitBreakBuffCalculator.DbgStaminaLimitBreakBuffActivateTime;
        public float DbgStaminaLimitBreakBuffFinishTime => _staminaLimitBreakBuffCalculator.DbgStaminaLimitBreakBuffFinishTime;
        
        // スパート前位置取り勝負
        public float DbgCompeteBeforeSpurtRemainActiveSec => _competeBeforeSpurtCalculator.DbgRemainActiveSec;
        public float DbgCompeteBeforeSpurtRemainCoolDownSec => _competeBeforeSpurtCalculator.DbgRemainCoolDownSec;
        public float DbgCompeteBeforeSpurtRemainCheckIntervalSec => _competeBeforeSpurtCalculator.DbgRemainCheckIntervalSec;
        public float DbgCompeteBeforeSpurtAddSpeedValue => _competeBeforeSpurtCalculator.DbgAddSpeedValue;
        public float DbgCompeteBeforeSpurtConsumeStaminaValue => _competeBeforeSpurtCalculator.DbgConsumeStaminaValue;
        public int DbgCompeteBeforeSpurtActivateCount => _competeBeforeSpurtCalculator.DbgActivateCount;
        public int DbgCompeteBeforeSpurtConditionNearCharaNum => _competeBeforeSpurtCalculator.DbgConditionNearCharaNum;
        public int DbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle => _competeBeforeSpurtCalculator.DbgConditionNearCharaNumSameRunningStyle;
        public bool DbgCompeteBeforeSpurtIsSpeedUpMinorBonus => _competeBeforeSpurtCalculator.DbgIsSpeedUpMinorBonus;
        public int DbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum => _competeBeforeSpurtCalculator.DbgSpeedUpMinorBonusNearCharaNum;
        public int DbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum => _competeBeforeSpurtCalculator.DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum;
        
        // 持久力温存状態
        public bool DbgIsStaminaKeepState => _competeBeforeSpurtCalculator.DbgStaminaKeepCalculator.DbgIsStaminaKeepState;
        public float DbgStaminaKeepCheckIntervalSec => _competeBeforeSpurtCalculator.DbgStaminaKeepCalculator.DbgRemainCheckIntervalSec;
        public float DbgStaminaKeepNeedHp => _competeBeforeSpurtCalculator.DbgStaminaKeepCalculator.DbgNeedHp;
        public float DbgStaminaKeepNeedHpForSpurt => _competeBeforeSpurtCalculator.DbgStaminaKeepCalculator.DbgNeedHpForSpurt;
        public float DbgStaminaKeepNeedHpForMiddle => _competeBeforeSpurtCalculator.DbgStaminaKeepCalculator.DbgNeedHpForMiddle;
        public float DbgStaminaKeepNeedHpWizRand => _competeBeforeSpurtCalculator.DbgStaminaKeepCalculator.DbgNeedHpWizRand;
        
        // リード確保
        public float DbgSecureLeadRemainActiveSec => _secureLeadCalculator.DbgRemainActiveSec;
        public float DbgSecureLeadRemainCoolDownSec => _secureLeadCalculator.DbgRemainCoolDownSec;
        public float DbgSecureLeadRemainCheckIntervalSec => _secureLeadCalculator.DbgRemainCheckIntervalSec;
        public float DbgSecureLeadAddSpeedValue => _secureLeadCalculator.DbgAddSpeedValue;
        public float DbgSecureLeadConsumeStaminaValue => _secureLeadCalculator.DbgConsumeStaminaValue;
        public int DbgSecureLeadActivateCount => _secureLeadCalculator.DbgActivateCount;
        public bool DbgSecureLeadIsSpeedUpMinorBonus => _secureLeadCalculator.DbgIsSpeedUpMinorBonus;
        public int DbgSecureLeadSpeedUpMinorBonusNearCharaNum => _secureLeadCalculator.DbgSpeedUpMinorBonusNearCharaNum;
        public int DbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum => _secureLeadCalculator.DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum;

        public List<DbgOverTakeLog> DbgOrderChangeCounterUpOverTakeLogList { get; set; } = new List<DbgOverTakeLog>();
        public List<DbgOverTakeLog> DbgOrderChangeCounterDownOverTakeLogList { get; set; } = new List<DbgOverTakeLog>();
#endif

        /// <summary>
        /// 狙いたい速度更新。
        /// </summary>
        private void UpdateTargetSpeed(float deltaTime)
        {
            if (_ownerHorse.IsFinished())
            {
                UpdateTargetSpeedAfterGoal(deltaTime);
            }
            else
            {
                UpdateTargetSpeedRace(deltaTime);
            }
        }

        private void UpdateTargetSpeedRace(float deltaTime)
        {
            _targetSpeedCalcRace.Update(deltaTime);
            _targetSpeed = _targetSpeedCalcRace.TargetSpeed;
        }

        private void UpdateTargetSpeedAfterGoal(float deltaTime)
        {
            // ゴール後一定時間はゴール時の速度を維持。※急減速すると、ゴールの瞬間真後ろにいるキャラ（自分が前方ブロックしているキャラ）の現在速度も下げてしまうため。
            if (!IsOverRunSpeedDownMode)
            {
                return;
            }

            _targetSpeedCalcOverRun.Update(deltaTime);
            _targetSpeed = _targetSpeedCalcOverRun.TargetSpeed;
        }

        //---------------------------------------------------------------
        public float GetTargetSpeed()
        {
            return _targetSpeed;
        }

        #region <レーン移動>
        /// <summary>
        /// レーン移動更新。
        /// </summary>
        private void UpdateMoveLane(float deltaTime)
        {
            _curLaneCalc.Update( deltaTime );
            _targetLane = _curLaneCalc.TargetLane;
        }

        //---------------------------------------------------------------
        public bool IsForceCheckNextLaneOneTime 
        { 
            get { return _curLaneCalc.IsForceCheckNextLaneOneTime; }
            set { _curLaneCalc.IsForceCheckNextLaneOneTime = value; }
        }

        //---------------------------------------------------------------
        public float GetTargetLane()
        {
            return _targetLane;
        }
        #endregion <レーン移動>

        #region <追い越し>
        /// <summary>
        /// 追い越し更新。
        /// </summary>
        private void UpdateOverTake(float deltaTime)
        {
            _overtakeCalc.CollectOvertakeTargetSimulate();

            if( UpdateCoolDownTime( deltaTime ) )
            {
                IsForceCheckNextLaneOneTime = true;
            }
        }

        /// <summary>
        /// 追い越しクールダウン時間更新。
        /// </summary>
        /// <returns>クールダウンがON→OFFに切り替わったらtrue。</returns>
        private bool UpdateCoolDownTime( float deltaTime )
        {
            // 追い越し相手がいなくなってすぐにインに入る→また追い越し発動→…の繰り返しにより振動するのを回避するため、
            // 一定時間は追い越し状態を維持するためのクールダウン時間を使用する。
            if (_overtakeCalc.HasOverTakeTarget())
            {
                _overTakeCoolDownTime = _paramDefine.overTakeCoolDownTime;
            }
            else
            {
                if (_overTakeCoolDownTime > 0.0f)
                {
                    _overTakeCoolDownTime -= deltaTime;

                    // 追い越しが終了したらtrueを返す。
                    return _overTakeCoolDownTime <= 0.0f;
                }
            }

            return false;
        }

        //---------------------------------------------------------------
        public bool IsOverTake()
        {
            return _overtakeCalc.HasOverTakeTarget();
        }
        //---------------------------------------------------------------
        public bool IsOverTakeOrCoolDown()
        {
            return IsOverTake() || _overTakeCoolDownTime > 0;
        }
        //---------------------------------------------------------------
        public List<Gallop.AroundHorse> GetOverTakeHorse()
        {
            return _overtakeCalc.OverTakeHorseList;
        }

        public bool CheckLaneSpace(float laneGapRange)
        {
            // 内側のスペースをチェック。
            if (laneGapRange < 0)
            {
                return InLaneSpace >= -laneGapRange;
            }
            // 外側のスペースをチェック。
            else
            {
                return OutLaneSpace >= laneGapRange;
            }
        }

        #endregion <追い越し>

        #region <坂>
        private void UpdateDownSlopeAccel(float deltaTime)
        {
            if (!IsDownSlopeAccelMode)
            {
                CheckDownSlopeAccelStart(deltaTime);
            }
            else
            {
                CheckDownSlopeAccelEnd(deltaTime);
            }
        }
        private void CheckDownSlopeAccelStart(float deltaTime)
        {
            if (Gallop.RaceDefine.SlopeType.Down != _ownerHorse.SlopeType)
            {
                return;
            }

            // 一定間隔での抽選。
            if (_downSlopeAccelCheckSec > 0)
            {
                _downSlopeAccelCheckSec -= deltaTime;
                return;
            }
            _downSlopeAccelCheckSec = DOWN_SLOPE_CHECK_SEC;

            // 確率抽選。
            float per = HorseSlopeCalculator.CalcDownSlopeAccelStartPer(_paramDefine.Slope, _ownerHorse.Wiz);
            if (RaceManagerSimulate.Instance.GetRandom(100.0f) >= per)
            {
                return;
            }

            // 下り坂での加速を有効にする。
            StartDownSlopeAccel();
        }
        private void CheckDownSlopeAccelEnd(float deltaTime)
        {
            // 下り坂でなければ、加速状態を終了する。
            if (Gallop.RaceDefine.SlopeType.Down != _ownerHorse.SlopeType)
            {
                EndDownSlopeAccel();
                return;
            }

            // 一定間隔での抽選。
            if (_downSlopeAccelCheckSec > 0)
            {
                _downSlopeAccelCheckSec -= deltaTime;
                return;
            }
            _downSlopeAccelCheckSec = DOWN_SLOPE_CHECK_SEC;

            // 確率抽選。
            float per = HorseSlopeCalculator.CalcDownSlopeAccelEndPer(_paramDefine.Slope);
            if (RaceManagerSimulate.Instance.GetRandom(100.0f) >= per)
            {
                return;
            }

            // 下り坂での加速を無効にする。
            EndDownSlopeAccel();
        }

        private void StartDownSlopeAccel()
        {
            IsDownSlopeAccelMode = true;
            _downSlopeAccelCheckSec = DOWN_SLOPE_CHECK_SEC;
        }
        private void EndDownSlopeAccel()
        {
            IsDownSlopeAccelMode = false;
            _downSlopeAccelCheckSec = DOWN_SLOPE_CHECK_SEC;
        }
        #endregion
        
        
        #region <AI:上限突破パラメータパワー(足溜め)>
        //---------------------------------------------------------------
        public void UpdateActivateStatusConservePower()
        {
            _conservePowerCalculator = CreateConservePowerCalculator();
        }
        //---------------------------------------------------------------
        public void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode)
        {
            _conservePowerCalculator.IncreaseConservePower(mode);
        }

        //---------------------------------------------------------------
        public void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode)
        {
            _conservePowerCalculator.DecreaseConservePower(mode);
        }

        //---------------------------------------------------------------
        public void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList)
        {
            _conservePowerCalculator.DecreaseConservePower(modeList);
        }

        //---------------------------------------------------------------
        public float ConservePowerAddAccel => _conservePowerCalculator.AccelValue;
        #endregion
        
        #region <AI:上限突破パラメータスタミナ>
        public void UpdateActivateStatusStaminaLimitBreakBuff()
        {
            _staminaLimitBreakBuffCalculator = CreateStaminaLimitBreakBuffCalculator();
        }
        /// <summary> 上限突破パラメータスタミナによる目指す速度加算値 </summary>
        public float StaminaLimitBreakBuffAddTargetSpeed => _staminaLimitBreakBuffCalculator.TargetSpeed;
        #endregion
        
        #region スパート前位置取り勝負
        //---------------------------------------------------------------
        public void UpdateCompeteBeforeSpurtCalculator(float deltaTime)
        {
            // ゴール後は更新しない。
            if (_ownerHorse.IsFinished())
            {
                return;
            }
            _competeBeforeSpurtCalculator.UpdateStates(deltaTime);
        }
        // スピード上昇値は、効果期間中、目指す速度値に加算し続けるための値
        public float CompeteBeforeSpurtAddSpeed => _competeBeforeSpurtCalculator.AddSpeedValue;
        // 消費スタミナ量は、スピード上昇とは違い、1回スタミナを消費するだけなので、使ったら0にしておく
        public float GetCompeteBeforeSpurtConsumeStamina()
        {
            return _competeBeforeSpurtCalculator.GetAndResetConsumeStaminaValue();
        }

        #endregion
        
        #region リード確保
        //---------------------------------------------------------------
        public void UpdateSecureLeadCalculator(float deltaTime)
        {
            // ゴール後は更新しない。
            if (_ownerHorse.IsFinished())
            {
                return;
            }
            _secureLeadCalculator.UpdateStates(deltaTime);
        }
        public float SecureLeadAddSpeed => _secureLeadCalculator.AddSpeedValue;
        public float GetSecureLeadConsumeStamina()
        {
            return _secureLeadCalculator.GetAndResetConsumeStaminaValue();
        }

        #endregion
        
#if CYG_DEBUG
        /// <summary>
        /// 自身を追い抜き対象としているキャラリストを更新
        /// </summary>
        /// <returns></returns>
        public void UpdateOverTakenHorse()
        {
            if (DbgOverTakenHorseIndexList == null)
            {
                DbgOverTakenHorseIndexList = new List<int>();
            }

            DbgOverTakenHorseIndexList.Clear();
            // 自分が誰かの追い抜き対象になっているか検索。
            var allHorseArray = _horseAccessor.GetHorseRaceInfos();
            for (int i = 0; i < allHorseArray.Length; ++i)
            {
                var horse = allHorseArray[i];

                // 自分より前にいるキャラは、追い抜き対象に入っているか調べない。
                if (horse.CurOrder <= _ownerHorse.CurOrder)
                {
                    continue;
                }

                var overTakeHorseList = horse.GetOverTakeHorseList();
                if (overTakeHorseList.FindIndex(x => x.infoSimulate == _ownerHorse) >= 0)
                {
                    // 追い抜き対象になっている。
                    DbgOverTakenHorseIndexList.Add(horse.HorseIndex);
                }
            }
        }
#endif
    }
}
#endif

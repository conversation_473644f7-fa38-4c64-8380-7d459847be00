using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース馬インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseRaceAI 
    {
        //---------------------------------------------------------------
        /// <summary>
        /// 初期化。
        /// </summary>
        //---------------------------------------------------------------
#if GALLOP
        // レース再生用
        void Init(
            Gallop.IHorseRaceInfo owner, 
            Gallop.IHorseRaceInfo[] horses, 
            System.Action onTemptationStart, 
            StandaloneSimulator.IHorsePaseMakerCalculator paseMakerCalculator);
#endif
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        // シミュレート用
        void Init(
            RaceInfo raceInfo,
            IHorseRaceInfoSimulate owner,
            IRaceHorseAccessor horseAccessor,
            System.Action onTemptationStart,
            IHorsePaseMakerCalculator paseMakerCalculator,
            float firstMoveLanePointDistance);
#endif
        
        void Release();

        //---------------------------------------------------------------
        /// <summary>
        /// 更新。
        /// </summary>
        //---------------------------------------------------------------
        void Update(float elapsedTime);
        //---------------------------------------------------------------
        /// <summary>
        /// 周辺馬情報更新。
        /// </summary>
        //---------------------------------------------------------------
        void UpdateAroundHorsesParam(float deltaTime);
        void UpdateAfterGoalTime( float deltaTime );

        /// <summary>
        /// 出走前の興奮発動抽選。
        /// </summary>
        void LotTemptation();

        //---------------------------------------------------------------
        /// <summary>
        /// 視野範囲のキャラ取得。
        /// </summary>
        /// <remarks>配列は出走キャラ数分確保しているが、有効な数はVisibleHorseCountまで。</remarks>
        //---------------------------------------------------------------
        Gallop.AroundHorse[] VisibleHorses { get; }
        //---------------------------------------------------------------
        /// <summary>
        /// 視野範囲のキャラ数。
        /// </summary>
        //---------------------------------------------------------------
        int VisibleHorseCount { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// 前後左右近くのキャラ取得。
        /// </summary>
        /// <remarks>配列は出走キャラ数分確保しているが、有効な数はNearHorseCountまで。</remarks>
        //---------------------------------------------------------------
        Gallop.AroundHorse[] NearHorses { get; }
        //---------------------------------------------------------------
        /// <summary>
        /// 前後左右近くのキャラ数取得。
        /// </summary>
        //---------------------------------------------------------------
        int NearHorseCount { get; }

        /// <summary>
        /// 周りを囲まれているかどうか。
        /// </summary>
        bool IsSurrounded { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// 周辺馬情報全取得。
        /// </summary>
        /// <returns></returns>
        //---------------------------------------------------------------
        Gallop.AroundHorse[] GetAroundHorses();

        //---------------------------------------------------------------
        /// <summary>
        /// 前方進路上にいると認識しているキャラ数。
        /// </summary>
        //---------------------------------------------------------------
        int CongestionCount { get; }
        //---------------------------------------------------------------
        /// <summary>
        /// 前方進路上にキャラが一定数以上いるかどうか。
        /// </summary>
        //---------------------------------------------------------------
        bool IsCongestion { get; }


        //---------------------------------------------------------------
        /// <summary>
        /// 前をブロックされているかどうか。
        /// </summary>
        //---------------------------------------------------------------
        bool IsBlockFront();
        //---------------------------------------------------------------
        /// <summary>
        /// イン・アウト少なくとも片側をブロックされているかどうか。
        /// </summary>
        //---------------------------------------------------------------
        bool IsBlockSide();

        //---------------------------------------------------------------
        /// <summary>
        /// 自分の前方をブロックしている馬の取得。
        /// </summary>
        //---------------------------------------------------------------
        Gallop.AroundHorse GetBlockHorse();
        //---------------------------------------------------------------
        /// <summary>
        /// 自分のイン側をブロックしている馬の取得。
        /// </summary>
        //---------------------------------------------------------------
        Gallop.AroundHorse GetBlockHorseIn();
        //---------------------------------------------------------------
        /// <summary>
        /// 自分のアウト側をブロックしている馬の取得。
        /// </summary>
        //---------------------------------------------------------------
        Gallop.AroundHorse GetBlockHorseOut();

        /// <summary>
        /// イン側の空きスペース。※符号なし。
        /// </summary>
        float InLaneSpace { get; } 
        /// <summary>
        /// アウト側の空きスペース。※符号なし。
        /// </summary>
        float OutLaneSpace { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// インに移動可能かどうか。
        /// </summary>
        /// <remarks>falseを返す場合でも、インを他の馬にブロックされているとは限らない。最も埒に寄り切っている場合もfalseを返すため。</remarks>
        //---------------------------------------------------------------
        bool IsEnableInMove { get; }
        //---------------------------------------------------------------
        /// <summary>
        /// アウトに移動可能かどうか。
        /// </summary>
        /// <remarks>falseを返す場合でも、アウトを他の馬にブロックされているとは限らない。最も外に寄り切っている場合もfalseを返すため。</remarks>
        //---------------------------------------------------------------
        bool IsEnableOutMove { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// 現在距離において、目指す速度基礎値取得。
        /// </summary>
        //---------------------------------------------------------------
        float GetBaseTargetSpeed();
        
        //---------------------------------------------------------------
        /// <summary>
        /// 目指す速度基礎値取得(乱数を除いたもの)。
        /// </summary>
        //---------------------------------------------------------------
        float GetBaseTargetSpeedWithoutRandom(Gallop.RaceDefine.HorsePhase phase);
        
        /// <summary>
        /// 目指す速度基礎値計算。
        /// </summary>
        void CalcBaseTargetSpeed();

        //---------------------------------------------------------------
        /// <summary>
        /// 狙いたい速度取得。
        /// </summary>
        //---------------------------------------------------------------
        float GetTargetSpeed();
        
        //---------------------------------------------------------------
        /// <summary>
        /// 移動目標レーン距離取得。
        /// </summary>
        //---------------------------------------------------------------
        float GetTargetLane();

        /// <summary>
        /// 指定レーン移動可能なスペースがあるかどうかチェック。
        /// </summary>
        /// <param name="laneGapRange">負の数ならイン側、正の数ならアウト側のスペースをチェック。</param>
        bool CheckLaneSpace(float laneGapRange);

        /// <summary>
        /// 追い抜きを行う必要があるか取得。
        /// </summary>
        bool IsOverTake();
        bool IsOverTakeOrCoolDown();

        /// <summary>
        /// 追い抜き対象のキャラ取得。
        /// </summary>
        List<Gallop.AroundHorse> GetOverTakeHorse();

        /// <summary>
        /// 掛かり中か取得。
        /// </summary>
        bool IsTemptation { get; }
        /// <summary>
        /// 掛かりのモード取得。
        /// </summary>
        TemptationMode TemptationMode { get; }
        /// <summary>
        /// 掛かった回数。
        /// </summary>
        int TemptationCount { get; }
        /// <summary>
        /// 掛かり発動するかどうか。
        /// </summary>
        bool IsTemptationStartEnable { get; }

        /// <summary>
        /// 下り坂での加速モード中かどうか。
        /// </summary>
        bool IsDownSlopeAccelMode { get; }

        //---------------------------------------------------------------
        bool IsLastSpurt { get; }
        //---------------------------------------------------------------
        float LastSpurtStartDistance { get; }
        //---------------------------------------------------------------
        LastSpurtCalcResult LastSpurtCalcResult { get; }
        //---------------------------------------------------------------
        float FirstCalcLastSpurtTargetSpeed { get; }
        //---------------------------------------------------------------
        float LastSpurtTargetSpeed { get; }
        /// <summary>
        /// ラストスパート計算のリクエスト。
        /// </summary>
        bool IsReqCalcLastSpurt { get; set; }
        /// <summary>
        /// ラストスパート計算が1回でも行われたかどうか
        /// </summary>
        public bool IsCalcLastSpurt { get; }

        //---------------------------------------------------------------
        Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get; }
        bool IsPositionKeepSection { get; }
        bool IsPositionKeep { get; }
        float PositionKeepBaseTargetSpeedMultiply { get; }

        /// <summary>
        /// レーン再検索を行わせる。
        /// </summary>
        bool IsForceCheckNextLaneOneTime { get; set; }

        //---------------------------------------------------------------
        /// <summary>
        /// ゴール後オーバーランで減速をしている状態か。
        /// </summary>
        /// <remarks>オーバランのための減速はゴール後すぐにではなく、一定時間後からなので、その判定を行う。</remarks>
        //---------------------------------------------------------------
        bool IsOverRunSpeedDownMode { get; }

        /// <summary>
        /// 序盤、インに寄るための目指す速度加算状態かどうか。
        /// </summary>
        bool IsForceInMoveEnable { get; }

        #region  <AI:上限突破パラメータパワー(足溜め)>
        //---------------------------------------------------------------
        /// <summary>
        /// 足溜め計算機初期化
        /// </summary>
        void UpdateActivateStatusConservePower();
        //---------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント外部から増加
        /// </summary>
        /// <param name="mode"> 増加モード </param>
        void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode);
        //---------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント外部から減少
        /// </summary>
        /// <param name="mode"> 減少モード </param>
        void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode);

        //---------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント外部から減少(スキルのように同時に複数個発動した時用)
        /// </summary>
        /// <param name="modeList"> 減少モードリスト </param>
        void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList);

        //---------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントによる加速度加算値
        /// </summary>
        /// <returns></returns>
        float ConservePowerAddAccel { get; }
        #endregion
        
        #region <AI:上限突破パラメータスタミナ>
        /// <summary> 上限突破パラメータスタミナ計算機発動状況更新 </summary>
        void UpdateActivateStatusStaminaLimitBreakBuff();
        /// <summary> 上限突破パラメータスタミナによる目指す速度加算値 </summary>
        float StaminaLimitBreakBuffAddTargetSpeed { get; }
        #endregion
        
        #region  <AI:スパート前位置取り勝負>
        //---------------------------------------------------------------
        /// <summary>
        /// スパート前位置取り勝負計算機更新
        /// </summary>
        void UpdateCompeteBeforeSpurtCalculator(float deltaTime);
        //---------------------------------------------------------------
        /// <summary>
        /// スパート前位置取り勝負による速度加算値
        /// </summary>
        float CompeteBeforeSpurtAddSpeed { get; }
        /// <summary>
        /// スパート前位置取り勝負による消費スタミナ
        /// </summary>
        float GetCompeteBeforeSpurtConsumeStamina();
        #endregion
        
        #region  <AI:リード確保>
        //---------------------------------------------------------------
        /// <summary>
        /// リード確保計算機更新
        /// </summary>
        void UpdateSecureLeadCalculator(float deltaTime);
        //---------------------------------------------------------------
        /// <summary>
        /// リード確保による速度加算値
        /// </summary>
        float SecureLeadAddSpeed { get; }
        /// <summary>
        /// リード確保による消費スタミナ
        /// </summary>
        float GetSecureLeadConsumeStamina();
        #endregion

#if CYG_DEBUG
        float DbgOverTakeLane { get; set; }
        float DbgOverTakeCoolDownTime { get; }
        List<int> DbgOverTakenHorseIndexList { get; set; }
        float DbgInLaneSpace { get; }
        float DbgOutLaneSpace { get; }

        float DbgCurDistanceDiffFromPaseMaker { get; }
        float DbgDistanceDiffMin { get; }
        float DbgDistanceDiffMax { get; }
        float DbgAdjustTargetDistanceDiff { get; }
        float DbgPositionKeepCoolDownTime { get; }
        #region  <AI:上限突破パラメータパワー(足溜め)>
        float DbgConservePowerAddAccel { get; }
        float DbgConservePowerAddAccelRemainTime { get; }
        float DbgConservePower { get; }
        float DbgConservePowerActivityTime { get; }
        float DbgConservePowerIncreaseCoolTime { get; }
        Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDict { get; }
        Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDict { get; }
        Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDictPerRecord { get; }
        Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDictPerRecord { get; }
        void ClearDbgConservePowerIncreaseTimeDictPerRecord();
        void ClearDbgConservePowerDecreaseTimeDictPerRecord();
        #endregion  // <AI:上限突破パラメータパワー(足溜め)>
        
        #region  <AI:上限突破パラメータスタミナ>
        /// <summary> 目指す速度に加算される値 </summary>
        float DbgStaminaLimitBreakBuffAddTargetSpeed { get; }
        Gallop.RaceDefine.RandomTableType DbgStaminaLimitBreakBuffLotteryRandomTableType { get; }
        Dictionary<Gallop.RaceDefine.RandomTableType, float> DbgStaminaLimitBreakBuffRandomTableProbabilityDict { get; }
        float DbgStaminaLimitBreakBuffRandomTableCoef { get; }
        float DbgStaminaLimitBreakBuffActivateTime { get; }
        float DbgStaminaLimitBreakBuffFinishTime { get; }
        #endregion // <AI:上限突破パラメータスタミナ>
        
        float DbgCompeteFightTime { get; set; }
        
        #region <スパート前位置取り勝負>
        float DbgCompeteBeforeSpurtRemainActiveSec { get; }
        float DbgCompeteBeforeSpurtRemainCoolDownSec { get; }
        float DbgCompeteBeforeSpurtRemainCheckIntervalSec { get; }
        float DbgCompeteBeforeSpurtAddSpeedValue { get; }
        float DbgCompeteBeforeSpurtConsumeStaminaValue { get; }
        int DbgCompeteBeforeSpurtActivateCount { get; }
        int DbgCompeteBeforeSpurtConditionNearCharaNum { get; }
        int DbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle { get; }
        bool DbgCompeteBeforeSpurtIsSpeedUpMinorBonus { get; }
        int DbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum { get; }
        int DbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum { get; }

        bool DbgIsStaminaKeepState { get; }
        float DbgStaminaKeepCheckIntervalSec { get; }
        float DbgStaminaKeepNeedHp { get; }
        float DbgStaminaKeepNeedHpForSpurt { get; }
        float DbgStaminaKeepNeedHpForMiddle { get; }
        float DbgStaminaKeepNeedHpWizRand { get; }
        #endregion
        
        #region <リード確保>
        float DbgSecureLeadRemainActiveSec { get; }
        float DbgSecureLeadRemainCoolDownSec { get; }
        float DbgSecureLeadRemainCheckIntervalSec { get; }
        float DbgSecureLeadAddSpeedValue { get; }
        float DbgSecureLeadConsumeStaminaValue { get; }
        int DbgSecureLeadActivateCount { get; }
        bool DbgSecureLeadIsSpeedUpMinorBonus { get; }
        int DbgSecureLeadSpeedUpMinorBonusNearCharaNum { get; }
        int DbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum { get; }
        #endregion
        
        List<DbgOverTakeLog> DbgOrderChangeCounterUpOverTakeLogList { get; set; }
        List<DbgOverTakeLog> DbgOrderChangeCounterDownOverTakeLogList { get; set; }
#endif
    }
}
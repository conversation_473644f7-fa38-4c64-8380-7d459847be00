#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足溜めによる加速度減少値計算機
    /// </summary>
    /// <remarks>
    /// 現状中身は「HorseConservePowerDecreaseAccelValueCalculator」とほぼ同じだが加速度現象の方だけ時間指定したいとか要望が来たときのために別クラスで定義しておく
    /// </remarks>
    //-------------------------------------------------------------------
    public class HorseConservePowerDecreaseAccelValueCalculator : IHorseConservePowerFluctuatingValueCalculator
    {
        private readonly IHorseRaceInfoSimulate _owner = null;
        /// <summary> 足溜め減少モードごとの加速度減衰係数 </summary>
        private readonly Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, float> _conservePowerDecreaseAccelCoefDict;
        /// <summary> 足溜め減少による加速度減少値リスト(乗算は順番に掛けていく必要があるのでリストで持つ) </summary>
        private readonly List<float> _conservePowerDecreaseAccelList;
        /// <summary> 最大減衰係数 </summary>
        private readonly float _maxDecreaseCoef;

        /// <summary> 変動値(乗算) </summary>
        private float _multiplyValue;
#if CYG_DEBUG
        /// <summary> 今までの足溜め減少時間テーブル </summary>
        private readonly Dictionary<int, List<float>> _dbgConservePowerDecreaseAccelTimeDict = new Dictionary<int, List<float>>();
        /// <summary> レコード毎の足溜め減少時間テーブル </summary>
        private readonly Dictionary<int, List<float>> _dbgConservePowerDecreaseAccelTimeDictPerRecord = new Dictionary<int, List<float>>();
#endif
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="ownerAI"></param>
        /// <param name="conservePowerParam"></param>
        public HorseConservePowerDecreaseAccelValueCalculator(IHorseRaceInfoSimulate owner, Gallop.RaceParamDefine.ConservePowerParam conservePowerParam)
        {
            _owner = owner;
            _conservePowerDecreaseAccelCoefDict = new Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, float>(
                conservePowerParam.ConservePowerDecreaseAccelCoefArray?.Length ?? 0
            );
            InitializeParam(conservePowerParam);
            _conservePowerDecreaseAccelList = new List<float>();
            if (_conservePowerDecreaseAccelCoefDict.Count > 0)
            {
                _maxDecreaseCoef = _conservePowerDecreaseAccelCoefDict.OrderByDescending(coefData => coefData.Value).FirstOrDefault().Value;
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// フレームごとに実行される更新
        /// </summary>
        /// <param name="deltaTime"> 前回実行されてからの経過時間 </param>
        public void UpdateFluctuatingValue(float deltaTime)
        {
            // 減少値リストから変動値を計算しておく
            _multiplyValue = 1f;
            foreach (var decreaseValue in _conservePowerDecreaseAccelList)
            {
                _multiplyValue *= decreaseValue;
            }
            // 取り出したらリセットする
            _conservePowerDecreaseAccelList.Clear();
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 変動値反映
        /// </summary>
        /// <param name="value"> 反映させたい値 </param>
        public void ApplyFluctuatingValue(ref float value)
        {
            value *= _multiplyValue;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 外部から減少変動値を設定する
        /// </summary>
        /// <param name="rawMode"> 減少モード </param>
        public void AddFluctuatingValue(int rawMode)
        {
            var mode = (Gallop.RaceDefine.DecreaseConservePowerMode) rawMode;
            AddFluctuatingValue(mode);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 減少変動値を設定する
        /// </summary>
        /// <param name="mode"> 減少モード </param>
        private void AddFluctuatingValue(Gallop.RaceDefine.DecreaseConservePowerMode mode)
        {
            // ラストスパートなら何もしない
            if (_owner.IsLastSpurt)
            {
                return;
            }

            if (!_conservePowerDecreaseAccelCoefDict.TryGetValue(mode, out var coef))
            {
                return;
            }

            _conservePowerDecreaseAccelList.Add(coef);
        #if CYG_DEBUG
            int modeRawValue = (int) mode;
            // 減少時間記録
            if (!_dbgConservePowerDecreaseAccelTimeDict.ContainsKey(modeRawValue))
            {
                _dbgConservePowerDecreaseAccelTimeDict.Add(modeRawValue, new List<float>());
            }

            _dbgConservePowerDecreaseAccelTimeDict[modeRawValue].Add(RaceManagerSimulate.Instance.AccumulateTimeSinceStart);

            if (!_dbgConservePowerDecreaseAccelTimeDictPerRecord.ContainsKey(modeRawValue))
            {
                _dbgConservePowerDecreaseAccelTimeDictPerRecord.Add(modeRawValue, new List<float>());
            }

            _dbgConservePowerDecreaseAccelTimeDictPerRecord[modeRawValue]
                .Add(RaceManagerSimulate.Instance.AccumulateTimeSinceStart);
        #endif
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 減少分の変動値加算
        /// </summary>
        /// <remarks>
        /// スキル発動等同時に複数個発動した時用
        /// </remarks>
        /// <param name="rawModeList"> 減少モードリスト </param>
        public void AddFluctuatingValue(List<int> rawModeList)
        {
            // ラストスパートなら何もしない
            if (_owner.IsLastSpurt)
            {
                return;
            }
            if (rawModeList == null || rawModeList.Count <= 0)
            {
                return;
            }

            // 同時に条件を満たした場合係数が一番小さいものを採用する
            var modeList = rawModeList.Cast<Gallop.RaceDefine.DecreaseConservePowerMode>();
            var targetCoefMode = _conservePowerDecreaseAccelCoefDict
                .Where(coefData => modeList.Contains(coefData.Key)).OrderByDescending(coefData => coefData.Value).FirstOrDefault().Key;
            AddFluctuatingValue(targetCoefMode);
        }


        //---------------------------------------------------------------
        /// <summary>
        /// RaceParamDefineに定義されたデータを扱いやすいデータで取得する
        /// </summary>
        /// <param name="conservePowerParam"></param>
        private void InitializeParam(Gallop.RaceParamDefine.ConservePowerParam conservePowerParam)
        {
            // 足溜め減少に夜加速度減衰係数リストを取得
            if (conservePowerParam.ConservePowerDecreaseAccelCoefArray == null || conservePowerParam.ConservePowerDecreaseAccelCoefArray.Length <= 0)
            {
                Debug.LogWarning("RaceParamDefineに足溜め減算時の加速度減衰係数リスト(ConservePowerDecreaseAccelCoefArray)が定義されていません");
            }
            else
            {
                foreach (var decreaseAccelCoefData in conservePowerParam.ConservePowerDecreaseAccelCoefArray)
                {
                    if (_conservePowerDecreaseAccelCoefDict.ContainsKey(decreaseAccelCoefData.Mode))
                    {
                        Debug.LogWarning($"{decreaseAccelCoefData.Mode}が複数個定義されています");
                    }
                    else
                    {
                        _conservePowerDecreaseAccelCoefDict.Add(decreaseAccelCoefData.Mode, decreaseAccelCoefData.Coef);
                    }
                }
            }
        }

#if CYG_DEBUG
        public float DbgConservePowerIncreaseCoolTime { get { return 0f; } }
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDict => _dbgConservePowerDecreaseAccelTimeDict;
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDictPerRecord => _dbgConservePowerDecreaseAccelTimeDictPerRecord;
        public void ClearDbgConservePowerFluctuatingTimeDictPerRecord()
        {
            _dbgConservePowerDecreaseAccelTimeDictPerRecord.Clear();
        }
#endif
    }
}
#endif

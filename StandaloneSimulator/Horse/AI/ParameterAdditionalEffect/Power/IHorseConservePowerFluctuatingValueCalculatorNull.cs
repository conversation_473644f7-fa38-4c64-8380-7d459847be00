#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足を溜める変動値計算機：Nullオブジェクト
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseConservePowerFluctuatingValueCalculatorNull : IHorseConservePowerFluctuatingValueCalculator
    {
        public void UpdateFluctuatingValue(float deltaTime) { }

        public void ApplyFluctuatingValue(ref float value) { }
        public void AddFluctuatingValue(int rawMode) { }

        public void AddFluctuatingValue(List<int> rawModeList) { }
#if CYG_DEBUG
        public float DbgConservePowerIncreaseCoolTime { get; }
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDict { get { return new Dictionary<int, List<float>>(); } }
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDictPerRecord { get { return new Dictionary<int, List<float>>(); } }
        public void ClearDbgConservePowerFluctuatingTimeDictPerRecord() { }
#endif
    }
}
#endif

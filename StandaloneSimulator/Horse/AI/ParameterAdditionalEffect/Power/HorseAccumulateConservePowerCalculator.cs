#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足溜めポイント計算機
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseAccumulateConservePowerCalculator : IHorseAccumulateConservePowerCalculator
    {
        /// <summary> 足溜めポイント増加量計算機 </summary>
        private readonly IHorseConservePowerFluctuatingValueCalculator _increaseConservePowerCalculator;

        /// <summary> 足溜めポイント減少量計算機 </summary>
        private readonly IHorseConservePowerFluctuatingValueCalculator _decreaseConservePowerCalculator;

        /// <summary> 足溜めポイント </summary>
        private float _conservePower;

        /// <summary>
        /// 足溜めポイント取得
        /// </summary>
        public float ConservePower => _conservePower;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="ownerAI"></param>
        /// <param name="conservePowerParam"></param>
        public HorseAccumulateConservePowerCalculator(IHorseRaceInfoSimulate owner, IHorseRaceAI ownerAI, Gallop.RaceParamDefine.ConservePowerParam conservePowerParam)
        {
            // 足溜め増減計算機生成
            _increaseConservePowerCalculator = new HorseConservePowerFluctuatingIncreaseValueCalculator(owner, ownerAI, conservePowerParam);
            _decreaseConservePowerCalculator = new HorseConservePowerFluctuatingDecreaseValueCalculator(owner, conservePowerParam);
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント更新
        /// </summary>
        public void UpdateConservePower(float deltaTime)
        {
            // 増加量反映
            _increaseConservePowerCalculator.UpdateFluctuatingValue(deltaTime);
            _increaseConservePowerCalculator.ApplyFluctuatingValue(ref _conservePower);

            // 減少量反映
            _decreaseConservePowerCalculator.UpdateFluctuatingValue(deltaTime);
            _decreaseConservePowerCalculator.ApplyFluctuatingValue(ref _conservePower);
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント増加
        /// </summary>
        /// <param name="mode"> 増加モード </param>
        public void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode)
        {
            _increaseConservePowerCalculator.AddFluctuatingValue((int)mode);
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント減少
        /// </summary>
        /// <param name="mode"> 減少モード </param>
        public void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode)
        {
            _decreaseConservePowerCalculator.AddFluctuatingValue((int)mode);
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを減少させる
        /// </summary>
        /// <remarks>
        /// スキル発動のように同時に複数個の条件を満たしたときに使用
        /// </remarks>
        /// <param name="modeList"> 減少モードリスト </param>
        public void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList)
        {
            _decreaseConservePowerCalculator.AddFluctuatingValue(modeList.Select(mode => (int) mode).ToList());
        }

#if CYG_DEBUG
        public float DbgConservePowerIncreaseCoolTime => _increaseConservePowerCalculator.DbgConservePowerIncreaseCoolTime;

        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>
            DbgConservePowerIncreaseTimeDict => _increaseConservePowerCalculator.DbgConservePowerFluctuatingTimeDict
            .ToDictionary(
                data => (Gallop.RaceDefine.IncreaseConservePowerMode) data.Key,
                data => data.Value
            );

        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>
            DbgConservePowerIncreaseTimeDictPerRecord => _increaseConservePowerCalculator.DbgConservePowerFluctuatingTimeDictPerRecord
            .ToDictionary(
                data => (Gallop.RaceDefine.IncreaseConservePowerMode) data.Key,
                data => data.Value
            );

        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>
            DbgConservePowerDecreaseTimeDict => _decreaseConservePowerCalculator.DbgConservePowerFluctuatingTimeDict
            .ToDictionary(
                data => (Gallop.RaceDefine.DecreaseConservePowerMode) data.Key,
                data => data.Value
            );

        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>
            DbgConservePowerDecreaseTimeDictPerRecord => _decreaseConservePowerCalculator.DbgConservePowerFluctuatingTimeDictPerRecord
            .ToDictionary(
                data => (Gallop.RaceDefine.DecreaseConservePowerMode) data.Key,
                data => data.Value
            );

        public void ClearDbgConservePowerIncreaseTimeDictPerRecord()
        {
            _increaseConservePowerCalculator.ClearDbgConservePowerFluctuatingTimeDictPerRecord();
        }

        public void ClearDbgConservePowerDecreaseTimeDictPerRecord()
        {
            _decreaseConservePowerCalculator.ClearDbgConservePowerFluctuatingTimeDictPerRecord();
        }
#endif
    }
}
#endif

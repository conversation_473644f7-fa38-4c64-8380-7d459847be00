using System.Collections.Generic;
namespace StandaloneSimulator
{

    //-------------------------------------------------------------------
    /// <summary>
    /// 足を溜める解放計算機Nullオブジェクト
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseConservePowerCalculatorNull : IHorseConservePowerCalculator
    {
        public float AccelValue { get { return 0f; } }

        public void UpdateConservePower(float deltaTime) { }

        public void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode) { }

        public void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode) { }

        public void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList) { }

    #if CYG_DEBUG
        public IHorseAccumulateConservePowerCalculator DbgAccumulateConservePowerCalculator
        {
            get { return new HorseAccumulateConservePowerCalculatorNull(); }
        }

        public float DbgRemainTime { get { return 0f; } }

        public float DbgAccelValue { get { return 0f; } }

        public float DbgActivityTime { get { return 0f; } }

    #endif
    }
}

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using Gallop;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足を溜める変動値計算機: 減少
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseConservePowerFluctuatingDecreaseValueCalculator : IHorseConservePowerFluctuatingValueCalculator
    {
        private readonly IHorseRaceInfoSimulate _owner = null;
        /// <summary> 足溜め減少モードごとの溜めポイント減衰係数 </summary>
        private readonly Dictionary<RaceDefine.DecreaseConservePowerMode, float> _conservePowerDecreaseCoefDict;

        /// <summary> 足溜め減少値リスト(乗算は順番に掛けていく必要があるのでリストで持つ) </summary>
        private readonly List<float> _conservePowerDecreaseValueList;
        /// <summary> 最大減衰係数 </summary>
        private readonly float _maxDecreaseCoef;
        /// <summary> 変動値(乗算) </summary>
        private float _multiplyValue;
#if CYG_DEBUG
        /// <summary> 今までの足溜め減少時間テーブル </summary>
        private readonly Dictionary<int, List<float>> _dbgConservePowerDecreaseTimeDict = new Dictionary<int, List<float>>();
        /// <summary> レコード毎の足溜め減少時間テーブル </summary>
        private readonly Dictionary<int, List<float>> _dbgConservePowerDecreaseTimeDictPerRecord = new Dictionary<int, List<float>>();
#endif
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="ownerAI"></param>
        /// <param name="conservePowerParam"></param>
        public HorseConservePowerFluctuatingDecreaseValueCalculator(IHorseRaceInfoSimulate owner, Gallop.RaceParamDefine.ConservePowerParam conservePowerParam)
        {
            _owner = owner;
            _conservePowerDecreaseCoefDict = new Dictionary<RaceDefine.DecreaseConservePowerMode, float>(
                conservePowerParam.ConservePowerDecreaseCoefArray?.Length ?? 0
            );
            InitializeParam(conservePowerParam);
            _conservePowerDecreaseValueList = new List<float>();
            if (_conservePowerDecreaseCoefDict.Count > 0)
            {
                _maxDecreaseCoef = _conservePowerDecreaseCoefDict.OrderByDescending(coefData => coefData.Value).FirstOrDefault().Value;
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// フレームごとに実行される更新
        /// </summary>
        /// <param name="deltaTime"> 前回実行されてからの経過時間 </param>
        public void UpdateFluctuatingValue(float deltaTime)
        {
            _multiplyValue = 1f;
            foreach (var decreaseValue in _conservePowerDecreaseValueList)
            {
                _multiplyValue *= decreaseValue;
            }

            // 取り出したらリセットする
            _conservePowerDecreaseValueList.Clear();
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 変動値反映
        /// </summary>
        /// <param name="value"> 反映させたい値 </param>
        /// <param name="target"> 反映させたい対象 </param>
        public void ApplyFluctuatingValue(ref float value)
        {
            value *= _multiplyValue;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 外部から減少変動値を設定する
        /// </summary>
        /// <param name="rawMode"> 減少モード </param>
        public void AddFluctuatingValue(int rawMode)
        {
            var mode = (RaceDefine.DecreaseConservePowerMode) rawMode;
            AddFluctuatingValue(mode);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 外部から減少変動値を設定する
        /// </summary>
        /// <param name="mode"> 減少モード </param>
        public void AddFluctuatingValue(RaceDefine.DecreaseConservePowerMode mode)
        {
            // ラストスパートなら何もしない
            if (_owner.IsLastSpurt)
            {
                return;
            }

            if (!_conservePowerDecreaseCoefDict.TryGetValue(mode, out var coef))
            {
                return;
            }

            _conservePowerDecreaseValueList.Add(coef);
        #if CYG_DEBUG
            int modeRawValue = (int) mode;
            // 減少時間記録
            if (!_dbgConservePowerDecreaseTimeDict.ContainsKey(modeRawValue))
            {
                _dbgConservePowerDecreaseTimeDict.Add(modeRawValue, new List<float>());
            }

            _dbgConservePowerDecreaseTimeDict[modeRawValue].Add(RaceManagerSimulate.Instance.AccumulateTimeSinceStart);

            if (!_dbgConservePowerDecreaseTimeDictPerRecord.ContainsKey(modeRawValue))
            {
                _dbgConservePowerDecreaseTimeDictPerRecord.Add(modeRawValue, new List<float>());
            }

            _dbgConservePowerDecreaseTimeDictPerRecord[modeRawValue].Add(RaceManagerSimulate.Instance.AccumulateTimeSinceStart);
        #endif
        }


        //-------------------------------------------------------------------
        /// <summary>
        /// 減少分の変動値加算
        /// </summary>
        /// <remarks>
        /// スキル発動等同時に複数個発動した時用
        /// </remarks>
        /// <param name="modeList"> 減少モードリスト </param>
        public void AddFluctuatingValue(List<int> rawModeList)
        {
            // ラストスパートなら何もしない
            if (_owner.IsLastSpurt)
            {
                return;
            }

            if (rawModeList == null || rawModeList.Count <= 0)
            {
                return;
            }

            // 同時に条件を満たした場合係数が一番小さいものを採用する
            var modeList = rawModeList.Cast<RaceDefine.DecreaseConservePowerMode>();
            var targetCoefMode = _conservePowerDecreaseCoefDict
                .Where(coefData => modeList.Contains(coefData.Key)).OrderByDescending(coefData => coefData.Value).FirstOrDefault().Key;
            AddFluctuatingValue(targetCoefMode);
        }


        //---------------------------------------------------------------
        /// <summary>
        /// RaceParamDefineに定義されたデータを扱いやすいデータで取得する
        /// </summary>
        /// <param name="conservePowerParam"></param>
        private void InitializeParam(Gallop.RaceParamDefine.ConservePowerParam conservePowerParam)
        {
            // 足溜め減衰係数リストを取得
            if (conservePowerParam.ConservePowerDecreaseCoefArray == null || conservePowerParam.ConservePowerDecreaseCoefArray.Length <= 0)
            {
                Debug.LogWarning("RaceParamDefineに足溜め減衰係数リスト(ConservePowerDecreaseCoefArray)が定義されていません");
            }
            else
            {
                foreach (var decreaseCoefData in conservePowerParam.ConservePowerDecreaseCoefArray)
                {
                    if (_conservePowerDecreaseCoefDict.ContainsKey(decreaseCoefData.Mode))
                    {
                        Debug.LogWarning($"{decreaseCoefData.Mode}が複数個定義されています");
                    }
                    else
                    {
                        _conservePowerDecreaseCoefDict.Add(decreaseCoefData.Mode, decreaseCoefData.Coef);
                    }
                }
            }
        }

#if CYG_DEBUG
        public float DbgConservePowerIncreaseCoolTime { get { return 0f; } }
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDict => _dbgConservePowerDecreaseTimeDict;
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDictPerRecord => _dbgConservePowerDecreaseTimeDictPerRecord;
        public void ClearDbgConservePowerFluctuatingTimeDictPerRecord()
        {
            _dbgConservePowerDecreaseTimeDictPerRecord.Clear();
        }
#endif
    }
}
#endif

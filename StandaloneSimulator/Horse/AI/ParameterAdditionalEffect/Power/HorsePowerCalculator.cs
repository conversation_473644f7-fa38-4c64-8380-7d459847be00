#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using System;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足を溜める解放計算機
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseConservePowerCalculator : IHorseConservePowerCalculator
    {
        #region 定数
        /// <summary> 乗算用デフォルト係数 </summary>
        private const float MULTIPLY_DEFAULT_COEF = 1f;
        #endregion

        #region アクセサ
        private readonly IHorseRaceInfoSimulate _owner;
        /// <summary> イベント記録 </summary>
        private readonly IRaceEventRecorder _eventRecorder;
        /// <summary> レース時間アクセッサ </summary>
        private readonly IRaceTimeAccessor _timeAccessor;
        #endregion

        # region 足溜めポイント計算用
        /// <summary> ラストスパート前での足溜めポイント蓄積計算機 </summary>
        private readonly IHorseAccumulateConservePowerCalculator _conservePowerAccumulateCalculator;
        /// <summary> 足溜め消費による加速度減少量計算機 </summary>
        private readonly IHorseConservePowerFluctuatingValueCalculator _conservePowerDecreaseAccelCalculator;
        #endregion

        #region 足溜め効果時間計算用
        /// <summary> 足溜め設定値 </summary>
        private readonly Gallop.RaceParamDefine.ConservePowerParam _conservePowerParam;
        /// <summary> レース場の距離(この距離に応じて足溜め解放効果時間が決まる) </summary>
        private readonly int _courseDistance;
        /// <summary> レース場の距離帯別係数 </summary>
        private readonly float _releaseConservePowerActivityTimeDistanceTypeCoef;
        #endregion

        #region 変数
        /// <summary> 解放時の効果持続時間 </summary>
        private float _remainTime = 0f;
        /// <summary> 足溜め解放時に加算される加速度 </summary>
        private float _accelValue = 0f;
        /// <summary> 足溜め解放中かどうか </summary>
        private bool _isReleaseConservePower = false;
        #endregion

        #region プロパティ
        public float AccelValue { get { return (_isReleaseConservePower && _remainTime > 0f) ? _accelValue : 0f; } }
        #endregion

        //-------------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="ownerAI"></param>
        /// <param name="courseDistance"></param>
        /// <param name="conservePowerParam"></param>
        /// <param name="eventRecorder"></param>
        /// <param name="timeAccessor"></param>
        public HorseConservePowerCalculator(
            IHorseRaceInfoSimulate owner, IHorseRaceAI ownerAI, int courseDistance,
            Gallop.RaceParamDefine.ConservePowerParam conservePowerParam, IRaceEventRecorder eventRecorder, IRaceTimeAccessor timeAccessor
        )
        {
            _owner = owner;
            _courseDistance = courseDistance;
            _releaseConservePowerActivityTimeDistanceTypeCoef = CalcReleaseConservePowerActivityTimeDistanceTypeCoef(courseDistance, conservePowerParam);
            _conservePowerParam = conservePowerParam;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;

            _conservePowerAccumulateCalculator = new HorseAccumulateConservePowerCalculator(owner, ownerAI, conservePowerParam);
            // 足溜め消費による加速度減少量計算機作成
            _conservePowerDecreaseAccelCalculator = new HorseConservePowerDecreaseAccelValueCalculator(owner, conservePowerParam);
            // 上限突破したパワーに応じて加算される加速度を計算する
            _accelValue = CalcAccelValue();
        }

        #region 足溜めポイント増減
        //-------------------------------------------------------------------
        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="deltaTime"></param>
        public void UpdateConservePower(float deltaTime)
        {
            var conservePower = _conservePowerAccumulateCalculator.ConservePower;
            // 足溜め蓄積更新
            _conservePowerAccumulateCalculator.UpdateConservePower(deltaTime);
            // 足溜め消費による加速度減少量計算
            _conservePowerDecreaseAccelCalculator.UpdateFluctuatingValue(deltaTime);
            _conservePowerDecreaseAccelCalculator.ApplyFluctuatingValue(ref _accelValue);

            if (_isReleaseConservePower)
            {
                _remainTime -= deltaTime;
                _remainTime = Math.Max(_remainTime, 0f);
            }
            else
            {
                // 効果時間は毎フレーム計算(一定秒数以上だと足溜め効果発揮可能)
                _remainTime = CalcRemainTime();
        #if CYG_DEBUG
                // 効果時間を保持しておく
                DbgActivityTime = _remainTime;
        #endif
            }

            // 足溜め解放チェック
            CheckReleaseConservePower();
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを増加させる
        /// </summary>
        /// <param name="mode"> 増加モード </param>
        public void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode)
        {
            _conservePowerAccumulateCalculator.IncreaseConservePower(mode);
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを増加させる
        /// </summary>
        /// <param name="mode"> 減少モード </param>
        public void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode)
        {
            _conservePowerAccumulateCalculator.DecreaseConservePower(mode);
            _conservePowerDecreaseAccelCalculator.AddFluctuatingValue((int)mode);
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを減少させる
        /// </summary>
        /// <remarks>
        /// スキル発動のように同時に複数個の条件を満たしたときに使用
        /// </remarks>
        /// <param name="modeList"> 減少モードリスト </param>
        public void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList)
        {
            _conservePowerAccumulateCalculator.DecreaseConservePower(modeList);
            _conservePowerDecreaseAccelCalculator.AddFluctuatingValue(modeList.Select(mode => (int) mode).ToList());
        }
        #endregion

        #region 足溜め効果時間計算
        //---------------------------------------------------------------
        /// <summary>
        /// 足溜め効果時間計算用距離帯別係数を取得する
        /// </summary>
        /// <param name="courseDistance"> レース距離 </param>
        /// <param name="conservePowerParam"> raceParamDefine </param>
        private float CalcReleaseConservePowerActivityTimeDistanceTypeCoef(int courseDistance, Gallop.RaceParamDefine.ConservePowerParam conservePowerParam)
        {
            var courseDistanceType = RaceUtil.CalcDistanceType(courseDistance);
            var releaseConservePowerActivityTimeCoef = MULTIPLY_DEFAULT_COEF;
            // 加算時に参照するモードごとの係数をparamDefineから取得
            if (conservePowerParam.ReleaseConservePowerActivityTimeDistanceTypeCoefArray == null ||
                conservePowerParam.ReleaseConservePowerActivityTimeDistanceTypeCoefArray.Length <= 0
               )
            {
                Debug.LogWarning("RaceParamDefineに足溜め効果時間計算用距離帯別係数(ReleaseConservePowerActivityTimeDistanceTypeCoef)が定義されていません");
            }
            else
            {
                var targetDistanceTypeCoefList = conservePowerParam.ReleaseConservePowerActivityTimeDistanceTypeCoefArray
                    .Where(coefData => coefData.DistanceType == courseDistanceType);
                var targetDistanceTypeCoefCount = targetDistanceTypeCoefList.Count();
                if (targetDistanceTypeCoefCount != 1)
                {
                    Debug.LogWarning($"{courseDistanceType}が{targetDistanceTypeCoefCount}個定義されています");
                }
                else
                {
                    releaseConservePowerActivityTimeCoef = targetDistanceTypeCoefList.FirstOrDefault().Coef;
                }
            }

            return releaseConservePowerActivityTimeCoef;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 現在の足溜めポイントから効果時間を計算する
        /// <remarks> 【溜めポイント】 / (【ReleaseConservePowerActivityTimeCoef】 / SQRT(レース距離)) × 【距離帯係数】 </remarks>
        /// <returns></returns>
        private float CalcRemainTime()
        {
            return _conservePowerAccumulateCalculator.ConservePower /
                   (_conservePowerParam.ReleaseConservePowerActivityTimeCoef / (float)Math.Sqrt(_courseDistance)) *
                   _releaseConservePowerActivityTimeDistanceTypeCoef;
        }
        #endregion

        #region 足溜め加速度計算
        //---------------------------------------------------------------
        /// <summary>
        /// 足溜め解放時の加速度計算に使用する脚質＆距離帯係数を取得する
        /// </summary>
        /// <param name="courseDistance"> レース距離 </param>
        private float CalcReleaseConservePowerAccelRunningStyleAndDistanceTypeCoef(int courseDistance)
        {
            var courseDistanceType = RaceUtil.CalcDistanceType(courseDistance);
            var releaseConservePowerAccelRunningStyleAndDistanceTypeCoef = MULTIPLY_DEFAULT_COEF;
            // 加算時に参照するモードごとの係数をparamDefineから取得
            if (_conservePowerParam.ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoefArray == null ||
                _conservePowerParam.ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoefArray.Length <= 0
               )
            {
                Debug.LogWarning(
                    "RaceParamDefineに足溜め効果時間計算用距離帯別係数(ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoefArray)が定義されていません"
                );
            }
            else
            {
                var targetRunningStyleAndDistanceTypeCoefList = _conservePowerParam.ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoefArray
                    .Where(coefData => (coefData.DistanceType == courseDistanceType) && (coefData.RunningStyle == _owner.RunningStyle));
                var targetRunningStyleAndDistanceTypeCoefListCount = targetRunningStyleAndDistanceTypeCoefList.Count();
                if (targetRunningStyleAndDistanceTypeCoefListCount != 1)
                {
                    Debug.LogWarning($"{courseDistanceType}が{targetRunningStyleAndDistanceTypeCoefListCount}個定義されています");
                }
                else
                {
                    releaseConservePowerAccelRunningStyleAndDistanceTypeCoef = targetRunningStyleAndDistanceTypeCoefList.FirstOrDefault().Coef;
                }
            }

            return releaseConservePowerAccelRunningStyleAndDistanceTypeCoef;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜め解放時に加算される加速度を計算する
        /// </summary>
        /// <remarks> (【ReleaseConservePowerAccelCoef】 * (SQRT(【上限突破分パラメータ】 * 【ReleaseConservePowerDeclCoef】) ) ) * 【ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoef】</remarks>
        /// <returns></returns>
        private float CalcAccelValue()
        {
            // 脚質とコース距離帯に応じた係数を計算して乗算する
            var releaseConservePowerAccelRunningStyleAndDistanceTypeCoef = CalcReleaseConservePowerAccelRunningStyleAndDistanceTypeCoef(_courseDistance);
            return (_conservePowerParam.ReleaseConservePowerAccelCoef *
                    (float)Math.Sqrt(CalcPower(_owner) * _conservePowerParam.ReleaseConservePowerDeclCoef)) *
                   releaseConservePowerAccelRunningStyleAndDistanceTypeCoef;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 加速度計算に使用されるパワーを計算する
        /// </summary>
        /// <remarks>
        /// (RawPow - STATUS_THRESHOLD) + (Pow - RawPowにAdjustRawStatusを反映させた値)
        /// 呼び出されるタイミングによってPowの値が変わるので注意。現状は開幕発動スキルやアイテムが適用された直後に呼び出されている
        /// </remarks>
        /// <returns></returns>
        public static float CalcPower(IHorseRaceInfoSimulate owner)
        {
            return Math.Max(0f, (owner.RawPow - RaceParameter.STATUS_THRESHOLD) + (owner.Pow - RaceParameter.AdjustRawStatus(owner.RawPow)));
        }

        #endregion

        /// <summary>
        /// 足溜め解放チェック
        /// </summary>
        private void CheckReleaseConservePower()
        {
            // ラストスパートでない
            if (!_owner.IsLastSpurt)
            {
                return;
            }

            // 既に足溜め解放済み
            if (_isReleaseConservePower)
            {
                return;
            }

            if (_remainTime <= _conservePowerParam.ConservePowerReleasableActivityTimeThreshold)
            {
                return;
            }

            _isReleaseConservePower = true;
            _eventRecorder.AddReleaseConservePowerEvent(_owner.HorseIndex, _timeAccessor.AccumulateTimeSinceStart);
        }

#if CYG_DEBUG
        public IHorseAccumulateConservePowerCalculator DbgAccumulateConservePowerCalculator => _conservePowerAccumulateCalculator;
        public float DbgRemainTime => _remainTime;
        public float DbgAccelValue => _accelValue;
        public float DbgActivityTime { get; private set; }
#endif
    }
}
#endif

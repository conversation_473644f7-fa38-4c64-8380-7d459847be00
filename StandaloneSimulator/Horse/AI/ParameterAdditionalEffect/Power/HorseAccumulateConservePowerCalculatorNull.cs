using System.Collections.Generic;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足溜めポイント計算機：Nullオブジェクト
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseAccumulateConservePowerCalculatorNull : IHorseAccumulateConservePowerCalculator
    {
        public float ConservePower { get { return 0f; } }

        public void UpdateConservePower(float deltaTime) { }

        public void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode) { }

        public void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode) { }

        public void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList) { }

#if CYG_DEBUG
        public float DbgConservePowerIncreaseCoolTime { get { return 0f; } }

        /// <summary>足溜めポイント増加時間テーブル </summary>
        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDict
        {
            get
            {
                return new Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>();
            }
        }

        /// <summary> レコードごとの足溜めポイント増加時間テーブル </summary>
        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDictPerRecord
        {
            get
            {
                return new Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>();
            }
        }

        /// <summary> 足溜めポイント減少時間テーブル </summary>
        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDict
        {
            get
            {
                return new Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>();
            }
        }

        /// <summary> レコードごとの足溜めポイント減少時間テーブル </summary>
        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDictPerRecord
        {
            get
            {
                return new Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>();
            }
        }

        /// <summary> レコードごとの足溜めポイント増加時間テーブルクリア </summary>
        public void ClearDbgConservePowerIncreaseTimeDictPerRecord() { }

        /// <summary> レコードごとの足溜めポイント減少時間テーブルクリア </summary>
        public void ClearDbgConservePowerDecreaseTimeDictPerRecord() { }
#endif
    }
}

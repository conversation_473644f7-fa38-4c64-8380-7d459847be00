#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using System;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足を溜める変動値計算機: 増加
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseConservePowerFluctuatingIncreaseValueCalculator : IHorseConservePowerFluctuatingValueCalculator
    {
        private const float DEFAULT_RUNNING_COEF = 1f;
        private readonly IHorseRaceInfoSimulate _ownerHorse = null;
        private readonly IHorseRaceAI _ownerAI = null;
        private readonly Gallop.RaceParamDefine.ConservePowerParam _conservePowerParam;
        /// <summary> 溜めポイント加算時モードごとの係数 </summary>
        private readonly Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, float> _conservePowerIncreaseCoefDict;
        /// <summary> 溜めポイント加算時脚質ごとの係数 </summary>
        private readonly Dictionary<Gallop.RaceDefine.RunningStyle, float> _conservePowerRunningStyleCoefDict;
        /// <summary> 脚質による溜めポイント加算時にかかる係数 </summary>
        private readonly float _runningStyleCoef;

        /// <summary> 足溜め増加ポイントリスト </summary>
        private readonly List<float> _conservePowerIncreaseValueList;

        /// <summary> 溜めポイント加算のクールタイム </summary>
        private float _increaseConservePowerCoolTime;

        /// <summary> 1フレーム前のクールタイム明けてからの足溜め経過秒数 </summary>
        private int _prevConservePowerTimeAfterCoolTime;

        /// <summary> 変動値(加算) </summary>
        private float _addValue;

        #if CYG_DEBUG
        /// <summary> 今までの足溜め増加時間テーブル </summary>
        private readonly Dictionary<int, List<float>> _dbgConservePowerIncreaseTimeDict = new Dictionary<int, List<float>>();
        /// <summary> レコードごとの足溜め増加時間テーブル </summary>
        private readonly Dictionary<int, List<float>> _dbgConservePowerIncreaseTimeDictPerRecord = new Dictionary<int, List<float>>();
        #endif

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="ownerAI"></param>
        /// <param name="conservePowerParam"></param>
        public HorseConservePowerFluctuatingIncreaseValueCalculator(
            IHorseRaceInfoSimulate owner, IHorseRaceAI ownerAI, Gallop.RaceParamDefine.ConservePowerParam conservePowerParam
        )
        {
            _ownerHorse = owner;
            _ownerAI = ownerAI;
            _conservePowerParam = conservePowerParam;
            _conservePowerIncreaseValueList = new List<float>();
            _conservePowerIncreaseCoefDict = new Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, float>(
                conservePowerParam.ConservePowerIncreaseCoefArray?.Length ?? 0
            );
            _conservePowerRunningStyleCoefDict = new Dictionary<Gallop.RaceDefine.RunningStyle, float>(
                conservePowerParam.ConservePowerRunningStyleCoefArray?.Length ?? 0
            );
            // RaceParamDefineからデータ取り出し
            InitializeParam(conservePowerParam);

            // 脚質は途中で変わらないので保持しておく
            _conservePowerRunningStyleCoefDict.TryGetValue(_ownerHorse.RunningStyle, out _runningStyleCoef);
            // 0乗算だと問題になるのでデフォルト値は1fにする
            if (_runningStyleCoef == 0f)
            {
                _runningStyleCoef = DEFAULT_RUNNING_COEF;
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 変動値反映
        /// </summary>
        /// <param name="value"> 反映させたい値 </param>
        /// <param name="target"> 反映させたい対象 </param>
        public void ApplyFluctuatingValue(ref float value)
        {
            value += _addValue;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// フレームごとに実行される更新
        /// </summary>
        /// <param name="deltaTime"> 前回実行されてからの経過時間 </param>
        public void UpdateFluctuatingValue(float deltaTime)
        {
            _addValue = 0f;
            // ポジション維持発動可能区間でないなら足溜め加算しない
            if (!_ownerHorse.IsPositionKeepSection)
            {
                return;
            }

            _increaseConservePowerCoolTime -= deltaTime;
            // クールタイムをリセットする必要があればリセットする
            if (IsNeedResetCoolTime())
            {
                _increaseConservePowerCoolTime = _conservePowerParam.ConservePowerDefaultCoolDownTime;
                _prevConservePowerTimeAfterCoolTime = 0;
            }


            // クールタイムが0以下なら加算
            if (_increaseConservePowerCoolTime <= 0f)
            {
                // クールタイムが0秒以下になってから経過した秒数を元に足溜めポイントを加算していく
                int conservePowerSecElapsedTime = RaceUtilMath.FloorToInt(Math.Abs(_increaseConservePowerCoolTime));
                if (conservePowerSecElapsedTime > _prevConservePowerTimeAfterCoolTime)
                {
                    IncreaseConservePower();
                }
                _prevConservePowerTimeAfterCoolTime = conservePowerSecElapsedTime;
            }

            _addValue = _conservePowerIncreaseValueList.Sum() * _runningStyleCoef;
            // 取り出したら値リセット
            _conservePowerIncreaseValueList.Clear();
        }
        //---------------------------------------------------------------
        /// <summary>
        /// 外部から増加変動値を設定する
        /// </summary>
        /// <param name="rawMode"> 増加モード </param>
        public void AddFluctuatingValue(int rawMode)
        {
            var mode = (Gallop.RaceDefine.IncreaseConservePowerMode) rawMode;
            AddFluctuatingValue(mode);
        }

        private void AddFluctuatingValue(Gallop.RaceDefine.IncreaseConservePowerMode mode)
        {
            if (!_conservePowerIncreaseCoefDict.TryGetValue(mode, out var coef))
            {
                return;
            }

            _conservePowerIncreaseValueList.Add(coef);
        #if CYG_DEBUG
            var modeRawValue = (int) mode;
            // 増加時間記録
            if (!_dbgConservePowerIncreaseTimeDict.ContainsKey(modeRawValue))
            {
                _dbgConservePowerIncreaseTimeDict.Add(modeRawValue, new List<float>());
            }

            _dbgConservePowerIncreaseTimeDict[modeRawValue].Add(RaceManagerSimulate.Instance.AccumulateTimeSinceStart);

            if (!_dbgConservePowerIncreaseTimeDictPerRecord.ContainsKey(modeRawValue))
            {
                _dbgConservePowerIncreaseTimeDictPerRecord.Add(modeRawValue, new List<float>());
            }

            _dbgConservePowerIncreaseTimeDictPerRecord[modeRawValue].Add(RaceManagerSimulate.Instance.AccumulateTimeSinceStart);
        #endif
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 増加分の変動値加算
        /// </summary>
        /// <remarks>
        /// スキル発動等同時に複数個発動した時用
        /// </remarks>
        /// <param name="modeList"> 増加モードリスト </param>
        public void AddFluctuatingValue(List<int> modeList) { }

        //---------------------------------------------------------------
        /// <summary>
        /// 溜めポイント加算
        /// </summary>
        private void IncreaseConservePower()
        {
            // PaceDown中
            bool isPaceDown = _ownerHorse.IsPositionKeep && _ownerHorse.PositionKeepMode == Gallop.RaceDefine.PositionKeepMode.PaseDown;
            if (isPaceDown)
            {
                AddFluctuatingValue(Gallop.RaceDefine.IncreaseConservePowerMode.PositionKeepPaceDown);
            }

            // ポジ維持未発動
            if (!_ownerHorse.IsPositionKeep)
            {
                AddFluctuatingValue(Gallop.RaceDefine.IncreaseConservePowerMode.NotActivatePositionKeep);
            }
        }
        //---------------------------------------------------------------
        /// <summary>
        /// RaceParamDefineに定義されたデータを扱いやすいデータで取得する
        /// </summary>
        /// <param name="conservePowerParam"></param>
        private void InitializeParam(Gallop.RaceParamDefine.ConservePowerParam conservePowerParam)
        {
            // 加算時に参照するモードごとの係数をparamDefineから取得
            if (conservePowerParam.ConservePowerIncreaseCoefArray == null ||　conservePowerParam.ConservePowerIncreaseCoefArray.Length <= 0)
            {
                Debug.LogWarning("RaceParamDefineに増加時の増加モード別係数(conservePowerIncreaseCoefArray)が定義されていません");
            }
            else
            {
                foreach (var increaseCoefData in conservePowerParam.ConservePowerIncreaseCoefArray)
                {
                    if (_conservePowerIncreaseCoefDict.ContainsKey(increaseCoefData.Mode))
                    {
                        Debug.LogWarning($"{increaseCoefData.Mode}が複数個定義されています");
                    }
                    else
                    {
                        _conservePowerIncreaseCoefDict.Add(increaseCoefData.Mode, increaseCoefData.Coef);
                    }
                }
            }

            // 増加時に参照する脚質ごとの係数をparamDefineから取得
            if (conservePowerParam.ConservePowerRunningStyleCoefArray == null ||
                conservePowerParam.ConservePowerRunningStyleCoefArray.Length <= 0
            )
            {
                Debug.LogWarning("RaceParamDefineに増加時の脚質別係数(conservePowerRunningStyleCoefArray)が定義されていません");
            }
            else
            {
                foreach (var runningStyleCoefData in conservePowerParam.ConservePowerRunningStyleCoefArray)
                {
                    if (_conservePowerRunningStyleCoefDict.ContainsKey(runningStyleCoefData.RunningStyle))
                    {
                        Debug.LogWarning($"{runningStyleCoefData.RunningStyle}が複数個定義されています");
                    }
                    else
                    {
                        _conservePowerRunningStyleCoefDict.Add(runningStyleCoefData.RunningStyle, runningStyleCoefData.Coef);
                    }
                }
            }
        }
        //---------------------------------------------------------------
        /// <summary>
        /// 足溜めクールタイムをリセットするべきかどうか
        /// </summary>
        /// <returns></returns>
        private bool IsNeedResetCoolTime()
        {
            // 掛かり
            if (_ownerHorse.IsTemptation)
            {
                return true;
            }

            // 位置取り争いで速度上昇
            if (_ownerHorse.IsCompeteTop && _ownerHorse.CompeteTopAddTargetSpeed > 0f)
            {
                return true;
            }

            // ポジ維持で速度上昇中
            if (_ownerHorse.IsPositionKeep && _ownerAI.PositionKeepMode != Gallop.RaceDefine.PositionKeepMode.PaseDown)
            {
                return true;
            }

            return false;
        }

#if CYG_DEBUG
        public float DbgConservePowerIncreaseCoolTime => _increaseConservePowerCoolTime;
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDict => _dbgConservePowerIncreaseTimeDict;
        public Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDictPerRecord => _dbgConservePowerIncreaseTimeDictPerRecord;
        public void ClearDbgConservePowerFluctuatingTimeDictPerRecord()
        {
            _dbgConservePowerIncreaseTimeDictPerRecord.Clear();
        }
#endif
    }
}
#endif

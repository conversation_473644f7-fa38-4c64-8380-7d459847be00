using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足を溜める解放計算機インターフェース
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseConservePowerCalculator
    {
        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜め解放時に加算される加速度
        /// </summary>
        float AccelValue { get; }
        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント更新
        /// </summary>
        /// <param name="deltaTime"></param>
        void UpdateConservePower(float deltaTime);

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを増加させる
        /// </summary>
        /// <param name="mode"> 増加モード </param>
        void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode);

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを減少させる
        /// </summary>
        /// <param name="mode"> 減少モード </param>
        void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode);

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを減少させる
        /// </summary>
        /// <remarks>
        /// スキル発動のように同時に複数個の条件を満たしたときに使用
        /// </remarks>
        /// <param name="modeList"> 減少モードリスト </param>
        void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList);

    #if CYG_DEBUG
        /// <summary>
        /// 足溜めポイント蓄積計算機(デバッグ情報として値参照するために提供)
        /// </summary>
        IHorseAccumulateConservePowerCalculator DbgAccumulateConservePowerCalculator { get; }
        /// <summary>
        /// 効果残り時間
        /// </summary>
        float DbgRemainTime { get; }

        float DbgAccelValue { get; }

        float DbgActivityTime { get; }
#endif
    }
}

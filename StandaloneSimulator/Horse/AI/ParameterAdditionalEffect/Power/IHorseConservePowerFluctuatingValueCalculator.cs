#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足を溜める変動値計算機インターフェース
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseConservePowerFluctuatingValueCalculator
    {
        //-------------------------------------------------------------------
        /// <summary>
        /// valueに対して変動値を反映させる
        /// </summary>
        /// <param name="value"> 変動値を反映させたい値 </param>
        void ApplyFluctuatingValue(ref float value);

        //-------------------------------------------------------------------
        /// <summary>
        /// 変動値の更新
        /// </summary>
        /// <param name="deltaTime"></param>
        void UpdateFluctuatingValue(float deltaTime);

        //-------------------------------------------------------------------
        /// <summary>
        /// 変動値加算
        /// </summary>
        /// <param name="rawMode"> 増加/減少モード(引数の型の違いをなくすためintで受け取る) </param>
        void AddFluctuatingValue(int rawMode);

        //-------------------------------------------------------------------
        /// <summary>
        /// 減少分の変動値加算
        /// </summary>
        /// <remarks>
        /// スキル発動等同時に複数個発動した時用
        /// </remarks>
        /// <param name="rawModeList"> 増加/減少モードリスト(引数の型の違いをなくすためintで受け取る) </param>
        void AddFluctuatingValue(List<int> rawModeList);

#if CYG_DEBUG
        float DbgConservePowerIncreaseCoolTime { get; }
        /// <summary> 変動した時間リスト(Key : IncreaseMode/DecreaseModeをint型で扱ったもの, Value : 変動時間) </summary>
        Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDict { get; }
        /// <summary> レコード毎の変動した時間リスト(Key : IncreaseMode/DecreaseModeをint型で扱ったもの, Value : 変動時間) </summary>
        Dictionary<int, List<float>> DbgConservePowerFluctuatingTimeDictPerRecord { get; }
        /// <summary> レコード毎の変動した時間リストをクリアする(Recordごとに呼ばれることを想定) </summary>
        void ClearDbgConservePowerFluctuatingTimeDictPerRecord();
#endif
    }
}
#endif
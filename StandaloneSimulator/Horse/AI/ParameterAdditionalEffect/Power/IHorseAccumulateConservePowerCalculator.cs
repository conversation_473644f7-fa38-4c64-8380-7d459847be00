using System.Collections.Generic;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 足溜めポイント計算機インターフェース
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseAccumulateConservePowerCalculator
    {
        //-------------------------------------------------------------------
        /// <summary>
        /// 足めポイント取得
        /// </summary>
        float ConservePower { get; }

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント更新
        /// </summary>
        void UpdateConservePower(float deltaTime);

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント増加
        /// </summary>
        /// <param name="mode"> 増加モード </param>
        void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode);

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイント減少
        /// </summary>
        /// <param name="mode"> 減少モード </param>
        void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode);

        //-------------------------------------------------------------------
        /// <summary>
        /// 足溜めポイントを減少させる
        /// </summary>
        /// <remarks>
        /// スキル発動のように同時に複数個の条件を満たしたときに使用
        /// </remarks>
        /// <param name="modeList"> 減少モードリスト </param>
        void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList);

    #if CYG_DEBUG
        /// <summary> 足溜めポイント増加クールタイム </summary>
        float DbgConservePowerIncreaseCoolTime { get; }

        /// <summary> 足溜めポイント増加時間テーブル </summary>
        Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDict { get; }
        /// <summary> レコードごとの足溜めポイント増加時間テーブル </summary>
        Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDictPerRecord { get; }

        /// <summary>足溜めポイント減少時間テーブル </summary>
        Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDict { get; }

        /// <summary> レコードごとの足溜めポイント減少時間テーブル </summary>
        Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDictPerRecord { get; }

        /// <summary> レコードごとの足溜めポイント増加時間テーブルクリア </summary>
        void ClearDbgConservePowerIncreaseTimeDictPerRecord();

        /// <summary> レコードごとの足溜めポイント減少時間テーブルクリア </summary>
        void ClearDbgConservePowerDecreaseTimeDictPerRecord();
#endif
    }
}

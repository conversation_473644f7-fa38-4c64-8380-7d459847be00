#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Linq;
#if CYG_DEBUG
using System.Collections.Generic;
#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スタミナの上限突破パラメータ追加効果計算機
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseStaminaLimitBreakBuffCalculator : IHorseStaminaLimitBreakBuffCalculator
    {
        /// <summary> 発動状態 </summary>
        private enum ActivateState
        {
            None,     // 未発動
            Activate, // 発動
            Finish,   // 発動終了
        }
    #region 定数
        /// <summary> 乗算用デフォルト係数 </summary>
        private const float MULTIPLY_DEFAULT_COEF = 1f;
        /// <summary> 抽選用確率の合計(100を10000倍した数なのでこの数となっている) </summary>
        private const int LOTTERY_SUM_VALUE = 1000000;
    #endregion

    #region アクセサ
        private readonly IHorseRaceInfoSimulate _owner;
        /// <summary> イベント記録 </summary>
        private readonly IRaceEventRecorder _eventRecorder;
        /// <summary> レース時間アクセッサ </summary>
        private readonly IRaceTimeAccessor _timeAccessor;
    #endregion

    #region 変数
        /// <summary> 発動状態 </summary>
        private ActivateState _activateState = ActivateState.None;
        /// <summary> 目指す速度 </summary>
        private readonly float _targetSpeed = 0f;
        public float TargetSpeed { get { return (_activateState == ActivateState.Activate) ? _targetSpeed : 0f; } }
        /// <summary> 目指す速度の加算値を知りたいとき用のプロパティ </summary>
        public float RawTargetSpeed => _targetSpeed;
    #endregion

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="courseDistance"></param>
        /// <param name="staminaLimitBreakBuffParam"></param>
        /// <param name="eventRecorder"></param>
        /// <param name="timeAccessor"></param>
        //---------------------------------------------------------------
        public HorseStaminaLimitBreakBuffCalculator(
            IHorseRaceInfoSimulate owner,
            int courseDistance,
            Gallop.RaceParamDefine.StaminaLimitBreakBuffParam staminaLimitBreakBuffParam,
            IRaceEventRecorder eventRecorder,
            IRaceTimeAccessor timeAccessor
        )
        {
            _owner = owner;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _targetSpeed = CalcTargetSpeed(owner, courseDistance, staminaLimitBreakBuffParam);
        }

        public void UpdateStaminaLimitBreakBuff(float deltaTime)
        {
            switch (_activateState)
            {
                case ActivateState.None:
                    // 未発動の時だけ発動可能
                    CheckActivate();
                    break;
                case ActivateState.Activate:
                    // 発動中のみ継続判定
                    CheckContinuable();
                    break;
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 効果発動判定
        /// </summary>
        private void CheckActivate()
        {
            // 発動条件を満たしていない
            if (!IsActivatable())
            {
                return;
            }

            // 発動状態に遷移
            _activateState = ActivateState.Activate;
            _eventRecorder.AddStaminaLimitBreakBuffEvent(_owner.HorseIndex, _timeAccessor.AccumulateTimeSinceStart);
        #if CYG_DEBUG
            // デバッグ用に発動開始時間記録
            DbgStaminaLimitBreakBuffActivateTime = _timeAccessor.AccumulateTimeSinceStart;
        #endif
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 効果継続判定
        /// </summary>
        private void CheckContinuable()
        {
            // 効果継続なら何もする必要なし
            if (IsContinuable())
            {
                return;
            }

            // 再度発動しないよう発動済みに状態を遷移させる
            _activateState = ActivateState.Finish;
        #if CYG_DEBUG
            // デバッグ用に効果終了時間記録
            DbgStaminaLimitBreakBuffFinishTime = _timeAccessor.AccumulateTimeSinceStart;
        #endif
        }

    #region 目指す速度計算
        /// <summary>
        /// 上限突破パラメータスタミナによる目指す速度上昇の値を取得
        /// </summary>
        /// <remarks>
        /// targetSpeed = Sqrt(【スタミナ - 1200】× 500) x 【距離区分補正】 x 0.002 * 【ランダムな揺らぎ幅テーブルによる重み】
        /// 500、0.002はraceParamDefineで定義される係数です。
        /// 呼び出されるタイミングによってowner.Staminaの値が変わるので注意。現状は足の溜めと同様、開幕発動スキルやアイテムが適用された直後に呼び出されている
        /// </remarks>
        /// <param name="owner"> 対象のキャラデータ </param>
        /// <param name="courseDistance"> レース距離 </param>
        /// <param name="staminaLimitBreakBuffParam"> raceParamDefine </param>
        /// <returns></returns>
        private float CalcTargetSpeed(
            IHorseRaceInfoSimulate owner,
            int courseDistance,
            Gallop.RaceParamDefine.StaminaLimitBreakBuffParam staminaLimitBreakBuffParam
        )
        {
            // ランダム揺らぎ幅を与える
            float randomCoef = CalcRandomTableCoef(staminaLimitBreakBuffParam);
            return (float)System.Math.Sqrt(CalcStamina(owner) * staminaLimitBreakBuffParam.TargetSpeedForStaminaCoef)
                   * CalcTargetSpeedDistanceTypeCoef(courseDistance, staminaLimitBreakBuffParam)
                   * staminaLimitBreakBuffParam.TargetSpeedForWholeCoef
                   * randomCoef;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 目指す速度計算に使用されるスタミナを計算する
        /// </summary>
        /// <remarks>
        /// (RawStamina - STATUS_THRESHOLD) + (Stamina - RawStaminaにAdjustRawStatusを反映させた値)
        /// 呼び出されるタイミングによってPowの値が変わるので注意。現状は開幕発動スキルやアイテムが適用された直後に呼び出されている
        /// </remarks>
        /// <returns></returns>
        public static float CalcStamina(IHorseRaceInfoSimulate owner)
        {
            return System.Math.Max(0f, (owner.RawStamina - RaceParameter.STATUS_THRESHOLD) + (owner.Stamina - RaceParameter.AdjustRawStatus(owner.RawStamina)));
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 目指す速度 計算用 距離帯別係数を取得する
        /// </summary>
        /// <param name="courseDistance"> レース距離 </param>
        /// <param name="staminaLimitBreakBuffParam"> raceParamDefine </param>
        //---------------------------------------------------------------
        private float CalcTargetSpeedDistanceTypeCoef(
            int courseDistance,
            Gallop.RaceParamDefine.StaminaLimitBreakBuffParam staminaLimitBreakBuffParam
        )
        {
            var targetSpeedDistanceCoefArray = staminaLimitBreakBuffParam.TargetSpeedDistanceCoefArray;

            if (targetSpeedDistanceCoefArray == null || targetSpeedDistanceCoefArray.Length <= 0)
            {
                Debug.LogWarning(
                    "RaceParamDefineにパラメータ上限突破スタミナ目指す速度 計算用 距離帯別係数(TargetSpeedDistanceCoefArray)が定義されていません"
                );
                return MULTIPLY_DEFAULT_COEF;
            }

            for (int i = 0; i < targetSpeedDistanceCoefArray.Length; i++)
            {
                if (courseDistance < targetSpeedDistanceCoefArray[i].DistanceThreshold)
                {
                    return targetSpeedDistanceCoefArray[i].Multiply;
                }
            }

            Debug.LogWarning($"StaminaLimitBreakBuffParamの距離別係数に{courseDistance}を満たす係数が定義されていません");
            return MULTIPLY_DEFAULT_COEF;
        }

        /// <summary>
        /// 揺らぎ幅テーブルによる重みを計算
        /// </summary>
        /// <param name="staminaLimitBreakBuffParam"> raceParamDefine </param>
        /// <returns></returns>
        private float CalcRandomTableCoef(Gallop.RaceParamDefine.StaminaLimitBreakBuffParam staminaLimitBreakBuffParam)
        {
            var coef = MULTIPLY_DEFAULT_COEF;
            var originRandomTableArray = staminaLimitBreakBuffParam.TargetSpeedRandomTableArray;
            // データ定義チェック
            if (originRandomTableArray == null || originRandomTableArray.Length <= 0)
            {
                Debug.LogWarning(
                    "RaceParamDefineにパラメータ上限突破スタミナ目指す速度 計算用 揺らぎ幅テーブル" +
                    "(TargetSpeedRandomTableArray)が定義されていません"
                );
                return coef;
            }
            int randomTableArrayLength = originRandomTableArray.Length;

            // テーブル定義数チェック
            if (randomTableArrayLength != RaceUtilEnum.GetEnumElementCount<Gallop.RaceDefine.RandomTableType>())
            {
                Debug.LogError("上限突破パラメータスタミナの揺らぎ幅テーブルに定義されていないテーブルがあります");
                return coef;
            }

            // 元のデータをいじりたくないのでディープコピーする
            var randomTableArray = new Gallop.RaceParamDefine.StaminaLimitBreakBuffTargetSpeedRandomTable[randomTableArrayLength];
            for (int i = 0; i < randomTableArrayLength; i++)
            {
                randomTableArray[i] = new Gallop.RaceParamDefine.StaminaLimitBreakBuffTargetSpeedRandomTable();
                randomTableArray[i].Copy(originRandomTableArray[i]);
            }

            // 確率をパワーの値によって補正する
            randomTableArray = ReCalcProbability(randomTableArray, staminaLimitBreakBuffParam);
            int sumProbability = randomTableArray.Select(table => table.Probability).Sum();
            if (sumProbability!= LOTTERY_SUM_VALUE)
            {
                Debug.LogWarning($"揺らぎ幅テーブルの合計確率が{LOTTERY_SUM_VALUE}となっていません({sumProbability})");
            }
            // 浮動小数点で扱うと計算誤差が出るので10000倍した数で抽選
            var lotteryProbability = RaceManagerSimulate.Instance.GetRandom(LOTTERY_SUM_VALUE);
            sumProbability = 0;

            Gallop.RaceParamDefine.StaminaLimitBreakBuffTargetSpeedRandomTable targetRandomTable = null;
            foreach (var randomTable in randomTableArray)
            {
                sumProbability += randomTable.Probability;
                if (lotteryProbability < sumProbability)
                {
                    targetRandomTable = randomTable;
                    break;
                }
            }

            if (targetRandomTable == null)
            {
                Debug.LogError("上限突破パラメータスタミナの揺らぎ幅テーブルが見つかりませんでした");
                return coef;
            }

            var coefInt = RaceManagerSimulate.Instance.GetRandom(targetRandomTable.LowerLimit, targetRandomTable.UpperLimit);
            coef = RaceUtilMath.MasterInt2Float(coefInt);
            #if CYG_DEBUG
                DbgRandomTableCoef = coef;
                DbgLotteryRandomTableType = (Gallop.RaceDefine.RandomTableType)targetRandomTable.TableType;
            #endif

            return coef;
        }

        /// <summary>
        /// 揺れ幅テーブルの最終的な確率を求める
        /// </summary>
        /// <returns></returns>
        private Gallop.RaceParamDefine.StaminaLimitBreakBuffTargetSpeedRandomTable[] ReCalcProbability(
            Gallop.RaceParamDefine.StaminaLimitBreakBuffTargetSpeedRandomTable[] randomTableArray,
            Gallop.RaceParamDefine.StaminaLimitBreakBuffParam staminaLimitBreakBuffParam
        )
        {
            // 確率計算用のベースの係数。実際の値はraceParamDefineで入力してもらい、それに対して下記の増減用の係数を掛けて計算する
            const int HIGHER_ADD_PROBABILITY_BASE_COEF = 1;
            const int LOWER_ADD_PROBABILITY_BASE_COEF = -1;
            // パドックで表示されるパワーを元に確率を再計算する
            var overLimitRawPower = System.Math.Max(0, _owner.RawPow - RaceParameter.STATUS_THRESHOLD);
            // 上振れテーブルの最大抽選確率
            var maxHigherTableProbability = 0;
            for(int i = 0; i < randomTableArray.Length; i++)
            {
                var addProbabilityCoef = 0;
                switch (randomTableArray[i].TableType)
                {
                    case Gallop.RaceDefine.RandomTableType.Higher:
                        addProbabilityCoef = HIGHER_ADD_PROBABILITY_BASE_COEF * staminaLimitBreakBuffParam.TargetSpeedRandomTableChangeProbabilityByPower;
                        break;
                    case Gallop.RaceDefine.RandomTableType.Lower:
                        addProbabilityCoef = LOWER_ADD_PROBABILITY_BASE_COEF * staminaLimitBreakBuffParam.TargetSpeedRandomTableChangeProbabilityByPower;
                        break;
                    case Gallop.RaceDefine.RandomTableType.Normal:
                        // 上振れテーブルの抽選確率の最大値は下振れテーブルの抽選確率が0の時、つまり確率合計値 - 通常テーブルの抽選確率となる
                        maxHigherTableProbability = LOTTERY_SUM_VALUE - randomTableArray[i].Probability;
                        addProbabilityCoef = 0;
                        break;
                }

                randomTableArray[i].Probability += overLimitRawPower * addProbabilityCoef;
            }

            // 最大値、最小値を超えている可能性があるので頭止めの処理を挟む
            for (int i = 0; i < randomTableArray.Length; i++)
            {
                // 0 ~ 最大確率で頭止め
                randomTableArray[i].Probability = RaceUtilMath.Clamp(randomTableArray[i].Probability, 0, maxHigherTableProbability);
            }

        #if CYG_DEBUG
            DbgStaminaLimitBreakBuffRandomTableProbabilityDict =
                new Dictionary<Gallop.RaceDefine.RandomTableType, float>(randomTableArray.Length);
            foreach (var randomTable in randomTableArray)
            {
                DbgStaminaLimitBreakBuffRandomTableProbabilityDict[(Gallop.RaceDefine.RandomTableType)randomTable.TableType] = randomTable.Probability / 10000f;
            }
        #endif
            return randomTableArray;
        }

    #endregion 目指す速度計算

        /// <summary>
        /// 発動可能かどうか
        /// </summary>
        /// <returns></returns>
        private bool IsActivatable()
        {
            // ラストスパート中でないなら発動不可
            if (!_owner.IsLastSpurt)
            {
                return false;
            }

            // 継続条件も満たしているかチェック
            if (!IsContinuable())
            {
                return false;
            }

            if (RaceManagerSimulate.Instance.RaceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.LogicUpdate_20805))
            {
                // 初回計算時のラストスパートで目指す速度に現在の速度(デバフスキルは考慮しない)が達したら発動可能
                return _owner.GetLastSpeedWithoutDebuffSkill() >= _owner.FirstCalcLastSpurtTargetSpeed;
            }
            else
            {
                return _owner.GetLastSpeed() >= _owner.FirstCalcLastSpurtTargetSpeed;
            }
        }

        /// <summary>
        /// 効果継続可能かどうか
        /// </summary>
        /// <returns></returns>
        private bool IsContinuable () {
            // HPが0以下になったら終了(同時にラストスパートも解除されるはず)
            if (_owner.GetHp() <= 0f)
            {
                return false;
            }

            return true;
        }

    #if CYG_DEBUG
        /// <summary> 目指す速度の加算値(デバッグ用なのでisActivateのチェックは行いません) </summary>
        public float DbgAddTargetSpeed => _targetSpeed;
        /// <summary> 抽選された揺らぎ幅テーブル </summary>
        public Gallop.RaceDefine.RandomTableType DbgLotteryRandomTableType { get; private set; }
        /// <summary> 揺らぎ幅テーブルの各タイプにおける抽選確率 </summary>
        public Dictionary<Gallop.RaceDefine.RandomTableType, float> DbgStaminaLimitBreakBuffRandomTableProbabilityDict { get; private set; }
        /// <summary> 選ばれた揺らぎ幅テーブルから実際にかける重み </summary>
        public float DbgRandomTableCoef { get; private set; }
        /// <summary> 効果発動時間 </summary>
        public float DbgStaminaLimitBreakBuffActivateTime { get; private set; }
        /// <summary> 効果終了時間 </summary>
        public float DbgStaminaLimitBreakBuffFinishTime { get; private set; }
    #endif
    }
}
#endif
#if CYG_DEBUG
using System.Collections.Generic;
#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スタミナの上限突破パラメータ追加効果計算機インターフェース
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseStaminaLimitBreakBuffCalculator
    {
        //-------------------------------------------------------------------
        /// <summary>
        /// 目指す速度の上昇値
        /// </summary>
        /// <returns></returns>
        //-------------------------------------------------------------------
        float TargetSpeed { get; }

        //-------------------------------------------------------------------
        /// <summary>
        /// 発動状態の更新など
        /// </summary>
        /// <param name="deltaTime"></param>
        //-------------------------------------------------------------------
        void UpdateStaminaLimitBreakBuff(float deltaTime);

#if CYG_DEBUG
        /// <summary> 目指す速度の加算値(デバッグ用なのでisActivateのチェックは行いません) </summary>
        float DbgAddTargetSpeed { get; }
        /// <summary> 抽選された揺らぎ幅テーブル </summary>
        Gallop.RaceDefine.RandomTableType DbgLotteryRandomTableType { get; }
        /// <summary> 揺らぎ幅テーブルの各タイプにおける抽選確率 </summary>
        Dictionary<Gallop.RaceDefine.RandomTableType, float> DbgStaminaLimitBreakBuffRandomTableProbabilityDict { get; }
        /// <summary> 選ばれた揺らぎ幅テーブルから実際にかける重み </summary>
        float DbgRandomTableCoef { get; }
        /// <summary> 効果発動時間 </summary>
        float DbgStaminaLimitBreakBuffActivateTime { get; }
        /// <summary> 効果終了時間 </summary>
        float DbgStaminaLimitBreakBuffFinishTime { get; }
#endif
    }
}
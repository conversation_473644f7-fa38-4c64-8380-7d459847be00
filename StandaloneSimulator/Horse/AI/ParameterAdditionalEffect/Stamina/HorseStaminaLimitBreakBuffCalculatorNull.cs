#if CYG_DEBUG
using System.Collections.Generic;
#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スタミナの上限突破パラメータ追加効果計算機Nullオブジェクト
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseStaminaLimitBreakBuffCalculatorNull : IHorseStaminaLimitBreakBuffCalculator
    {
        public float TargetSpeed { get { return 0f; } }

        public void UpdateStaminaLimitBreakBuff(float deltaTime) { }

#if CYG_DEBUG
        /// <summary> 目指す速度の加算値(デバッグ用なのでisActivateのチェックは行いません) </summary>
        public float DbgAddTargetSpeed { get; }
        /// <summary> 抽選された揺らぎ幅テーブル </summary>
        public Gallop.RaceDefine.RandomTableType DbgLotteryRandomTableType { get; }

        /// <summary> 揺らぎ幅テーブルの各タイプにおける抽選確率 </summary>
        public Dictionary<Gallop.RaceDefine.RandomTableType, float> DbgStaminaLimitBreakBuffRandomTableProbabilityDict
        {
            get { return new Dictionary<Gallop.RaceDefine.RandomTableType, float>(); }
        }

        /// <summary> 選ばれた揺らぎ幅テーブルから実際にかける重み </summary>
        public float DbgRandomTableCoef { get; }
        /// <summary> 効果発動時間 </summary>
        public float DbgStaminaLimitBreakBuffActivateTime { get; }
        /// <summary> 効果終了時間 </summary>
        public float DbgStaminaLimitBreakBuffFinishTime { get; }
#endif
    }
}
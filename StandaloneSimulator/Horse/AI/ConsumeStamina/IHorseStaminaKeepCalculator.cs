namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 持久力温存状態　計算機インターフェース
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseStaminaKeepCalculator
    {
        // 持久力温存状態か？
        bool IsStaminaKeepState { get; }

        //-------------------------------------------------------------------
        /// <summary>
        /// 状態更新
        /// </summary>
        void UpdateStates(float deltaTime);

#if CYG_DEBUG
        /// <summary> 持久力温存状態になっているか </summary>
        bool DbgIsStaminaKeepState { get; }

        /// <summary> 算出された、必要な持久力 </summary>
        float DbgNeedHp { get; }

        /// <summary> 必要な持久力計算用：スパート用スタミナ </summary>
        float DbgNeedHpForSpurt { get; }

        /// <summary> 必要な持久力計算用：中盤用スタミナ </summary>
        float DbgNeedHpForMiddle { get; }

        /// <summary> 必要な持久力計算用：賢さによる乱数 </summary>
        float DbgNeedHpWizRand { get; }

        /// <summary> 発動チェック残り時間 </summary>
        float DbgRemainCheckIntervalSec { get; }
#endif
    }
}
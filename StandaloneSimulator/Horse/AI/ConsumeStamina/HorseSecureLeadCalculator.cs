#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// リード確保　計算機
    /// </summary>
    //-------------------------------------------------------------------

    public class HorseSecureLeadCalculator : IHorseConsumeStaminaCalculator
    {
        #region アクセサ

        private readonly IHorseRaceInfoSimulate _owner;
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly IRaceRandomGenerator _randomGenerator;

        /// <summary> イベント記録 </summary>
        private readonly IRaceEventRecorder _eventRecorder;

        /// <summary> レース時間アクセッサ </summary>
        private readonly IRaceTimeAccessor _timeAccessor;

        #endregion

        #region 変数

        /// <summary> パラメーター定義 </summary>
        private readonly Gallop.RaceParamDefine.SecureLeadParam _param;
        private readonly int _courseDistance;
        
        /// <summary> 効果発動時の、速度加算値 </summary>
        private float _addSpeedValue;
        /// <summary> 効果発動時の、持久力消費値 </summary>
        private float _consumeStaminaValue;
        /// <summary> 効果発動の残り時間 </summary>
        private float _remainActiveSec = 0f;
        /// <summary> 次の発動チェックまでの残り時間 </summary>
        private float _remainCheckIntervalSec = 0f;
        /// <summary> クールダウン終了までの残り時間 </summary>
        private float _remainCoolDownSec = 0f;
        /// <summary> 効果発動中か </summary>
        private bool _isActiveEffect = false;
        /// <summary> 効果発動回数 </summary>
        private int _activateCount = 0;

        /// <summary>
        /// セーフティリード：作戦別リードの基準値
        /// 特殊走法を考慮するために、TupleをKeyとするDictを用意
        /// </summary>
        private Dictionary<Tuple<Gallop.RaceDefine.RunningStyleEx, Gallop.RaceDefine.RunningStyle>, float> _safetyLeadRunningStyleDistanceThresholdDict = 
            new Dictionary<Tuple<Gallop.RaceDefine.RunningStyleEx, Gallop.RaceDefine.RunningStyle>, float>();
        /// <summary> セーフティリード：コース距離によって変動係数 </summary>
        private float _safetyLeadDistanceCoef;
        /// <summary> 速度上昇: 作戦別係数 </summary>
        private float _speedUpRunningStyleCoef;
        /// <summary> 速度上昇: 少数派ボーナス係数 </summary>
        private float _minorBonusRunningStyleCoef;
        /// <summary> 持久力消費: 作戦別係数 </summary>
        private float _consumeStaminaRunningStyleCoef;
        /// <summary> 持久力消費: 距離ごとの係数 </summary>
        private float _consumeStaminaCourseDistanceCoef;

        #endregion

        #region プロパティ

        public float AddSpeedValue
        {
            get { return (_isActiveEffect && _remainActiveSec > 0f) ? _addSpeedValue : 0f; }
        }
        
        /// <summary>
        /// 消費するスタミナ量を取得し、その値が有効値（0以外）なら取得後にリセットする
        /// </summary>
        /// <returns></returns>
        public float GetAndResetConsumeStaminaValue()
        {
            // 消費スタミナ量が設定されているなら、設定後最初の1回の処理だけその値を渡す
            if (_isActiveEffect && _remainActiveSec > 0f && _consumeStaminaValue != 0f)
            {
                // コピーしておく
                var value = _consumeStaminaValue;
                // 元の値はリセットする（そうしないと、毎フレームこの値だけ減ることになる）
                _consumeStaminaValue = 0f;
                return value;
            }

            return 0f;
        }

        #endregion

        //-------------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="horseAccessor"></param>
        /// <param name="randomGenerator"></param>
        /// <param name="eventRecorder"></param>
        /// <param name="timeAccessor"></param>
        /// <param name="secureLeadParam"></param>
        /// <param name="courseDistance"></param>
        public HorseSecureLeadCalculator(
            IHorseRaceInfoSimulate owner, IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator, IRaceEventRecorder eventRecorder, IRaceTimeAccessor timeAccessor,
            Gallop.RaceParamDefine.SecureLeadParam secureLeadParam, int courseDistance 
        )
        {
            _owner = owner;
            _horseAccessor = horseAccessor;
            _randomGenerator = randomGenerator;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _param = secureLeadParam;
            _courseDistance = courseDistance;

            SetupParameter();
        }

        /// <summary>
        /// 初めに、必要な数値を取得しておく
        /// </summary>
        private void SetupParameter()
        {
            // セーフティリード：作戦別リードの基準値
            {
                // 自分の作戦に対応する、相手の作戦とその閾値の情報を取得
                var opponentRunningStyleDistanceThresholdArray = _param.SecureLeadSafetyLeadRunningStyleDistanceArray?
                    .FirstOrDefault(x => x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)
                    ?.OpponentRunningStyleDistanceThresholdArray;
                if (opponentRunningStyleDistanceThresholdArray == null)
                {
                    Debug.LogWarning("ParamDefine: SecureLeadSafetyLeadRunningStyleDistanceArray: 作戦に対応する閾値が設定されていません");
                    return;
                }
                // Dictに格納しておく
                _safetyLeadRunningStyleDistanceThresholdDict.Clear();
                for (int i = 0; i < opponentRunningStyleDistanceThresholdArray.Length; i++)
                {
                    var opponent = opponentRunningStyleDistanceThresholdArray[i];
                    var runningStyle = new Tuple<Gallop.RaceDefine.RunningStyleEx, Gallop.RaceDefine.RunningStyle>(opponent.RunningStyleEx, opponent.RunningStyle);
                    _safetyLeadRunningStyleDistanceThresholdDict.Add(runningStyle, opponent.Threshold);
                }
            }
            
            // セーフティリード：作戦別リードの基準値
            {
                // (1+(コース距離+1000)*0.0002)
                _safetyLeadDistanceCoef = 1 + (_courseDistance + _param.SecureLeadSafetyLeadAddend) * _param.SecureLeadSafetyLeadCoef;
            }
            
            // 速度上昇: 係数
            {
                var speedUpRunningStyleCoef = _param.SecureLeadSpeedUpRunningStyleCoefArray?
                    .FirstOrDefault(x =>
                        x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)?.Coef;
                if (speedUpRunningStyleCoef == null)
                {
                    Debug.LogWarning("ParamDefine: SecureLeadSpeedUpRunningStyleCoef: 作戦に対応する閾値が設定されていません");
                    return;
                }
                _speedUpRunningStyleCoef = (float)speedUpRunningStyleCoef;
            }
            
            // 少数派ボーナス係数
            {
                var minorBonusRunningStyleCoef = _param.SecureLeadMinorBonusRunningStyleCoefArray?
                    .FirstOrDefault(x =>
                        x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)?.Coef;
                if (minorBonusRunningStyleCoef == null)
                {
                    Debug.LogWarning("ParamDefine: SecureLeadMinorBonusRunningStyleCoef: 作戦に対応する閾値が設定されていません");
                    return;
                }

                _minorBonusRunningStyleCoef = (float)minorBonusRunningStyleCoef;
            }

            // 持久力消費: 作戦別係数
            {
                var consumeStaminaRunningStyleCoef = _param.SecureLeadConsumeStaminaRunningStyleCoefArray?
                    .FirstOrDefault(x =>
                        x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)?.Coef;
                if (consumeStaminaRunningStyleCoef == null)
                {
                    Debug.LogWarning("ParamDefine: SecureLeadConsumeStaminaRunningStyleCoef: 作戦に対応する閾値が設定されていません");
                    return;
                }

                _consumeStaminaRunningStyleCoef = (float)consumeStaminaRunningStyleCoef;
            }
            
            // 持久力消費: 距離ごとの係数
            {
                var consumeStaminaCourseDistanceCoef = _param.SecureLeadConsumeStaminaCourseDistanceCoefArray?
                    .OrderBy(x=>x.Threshold)
                    .FirstOrDefault(x => x.Threshold > _courseDistance)?.Coef;
                if (consumeStaminaCourseDistanceCoef == null)
                {
                    Debug.LogWarning("ParamDefine: SecureLeadConsumeStaminaCourseDistanceCoef: 距離に対応する閾値が設定されていません");
                    return;
                }
                _consumeStaminaCourseDistanceCoef = (float)consumeStaminaCourseDistanceCoef;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// リード確保 計算機更新
        /// キャラの相互位置関係を参照するので、Update()ではなくPostUpdate()で処理する必要がある
        /// </summary>
        public void UpdateStates(float deltaTime)
        {
            // 各種残り時間の管理
            UpdateTime(deltaTime);

            // 発動中なら、終了をチェックする
            if (_isActiveEffect)
            {
                CheckEnd();
            }
            // 発動中でないなら、開始をチェックする
            else
            {
                CheckStart();
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 時間管理の更新
        /// </summary>
        private void UpdateTime(float deltaTime)
        {
            // クールダウン中なら、残り時間を減らす
            if (_remainCoolDownSec > 0f)
            {
                _remainCoolDownSec -= deltaTime;
            }

            // 発動チェックのインターバル中だったら、残り時間を減らす
            if (_remainCheckIntervalSec > 0f)
            {
                _remainCheckIntervalSec -= deltaTime;
            }

            // 発動中なら、残り時間を減らす
            if (_remainActiveSec > 0f)
            {
                // 効果残り時間を減らす
                _remainActiveSec -= deltaTime;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 発動終了のチェック
        /// </summary>
        private void CheckEnd()
        {
            // 終了確認
            if (IsFinishedEffect())
            {
                // 残り時間をリセットし、
                ResetRemainTime();
                // クールダウンをセットし、
                _remainCoolDownSec = _param.SecureLeadCoolDownSec;
                // 効果発動を止める
                _isActiveEffect = false;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 発動条件のチェック（一定時間(CheckIntervalSec)ごとに行う）
        /// </summary>
        private void CheckStart()
        {
            // 該当区間以外では発動しない
            var currentSection = _owner.CalcSection();
            if (currentSection < _param.SecureLeadStartSection ||
                currentSection > _param.SecureLeadEndSection)
            {
                return;
            }

            // 発動チェックのインターバル中だったら、何もしない
            if (_remainCheckIntervalSec > 0f)
            {
                return;
            }

            // 条件を満たしている（クールダウン中でない && セーフティリードをとれていない）なら
            if (_remainCoolDownSec <= 0f && !IsHavingSafetyLead())
            {
                // 抽選する
                DrawLotsToActuate();
            }

            // 発動チェックのインターバルに入る
            _remainCheckIntervalSec = _param.SecureLeadCheckIntervalSec;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 発動条件を満たした場合に、発動抽選を行う
        /// </summary>
        private void DrawLotsToActuate()
        {
            // 発動率: 新規定数 * Log10(賢さ)
            var noticeOdds = _param.SecureLeadPerCoef * System.Math.Log10(_owner.Wiz);
            if (noticeOdds >= _randomGenerator.GetRandom(100f))
            {
                ActuateEffect();
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 効果を発動する
        /// </summary>
        private void ActuateEffect()
        {
            // メイン効果発動
            {
                // 速度上昇
                SpeedUp();
                // 持久力消費
                ConsumeStamina();
            }
            // 付随処理
            if (_activateCount == 0)
            {
                // 初回のみ、eventRecorderへの追加をし、状態示唆アイコンを出す
                _eventRecorder.AddSecureLeadEvent(_owner.HorseIndex, _timeAccessor.AccumulateTimeSinceStart);
            }
            // フラグ管理
            {
                _isActiveEffect = true;
                _remainActiveSec = _param.SecureLeadActiveSec;
                _activateCount++;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 効果の終了をチェック
        /// </summary>
        private bool IsFinishedEffect()
        {
            // 一定時間経過 or 自分以降の作戦のキャラ全員にセーフティリードを取れている と終了する
            bool isFinished = _remainActiveSec <= 0f || IsHavingSafetyLead();
            return isFinished;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 自分以降の作戦のキャラ全員にセーフティリードを取れているかどうかを判定する
        /// </summary>
        private bool IsHavingSafetyLead()
        {
            bool isHavingSafetyLead = true;
            // 定義: 自分以降の作戦のキャラ全員にセーフティリード（Threshold * (1+(コース距離+1000)*0.0002) m）を取れている
            // 自分以降の作戦のキャラ全員を前から順に並べる（同作戦含む）
            var afterOwnRunningStyleCharaArray = _horseAccessor.GetHorseRaceInfos()
                .Where(x => x.RunningStyle >= _owner.RunningStyle)
                .Where(x => x.HorseIndex != _owner.HorseIndex)      // 自分は除く
                .OrderByDescending(x => x.GetDistance()).ToArray();
            // 自分が逃げの場合のみ、大逃げを除く（大逃げは逃げよりも前）
            if (_owner.RunningStyle == Gallop.RaceDefine.RunningStyle.Nige && _owner.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.None)
            {
                afterOwnRunningStyleCharaArray = afterOwnRunningStyleCharaArray.Where(x => x.RunningStyleEx != Gallop.RaceDefine.RunningStyleEx.Oonige).ToArray();
            }

            // 前のキャラから順に後ろを見ていく
            var myDistance = _owner.GetDistance();
            for (int i = 0; i < afterOwnRunningStyleCharaArray.Length; i++)
            {
                var opponent = afterOwnRunningStyleCharaArray[i];
                var key = new Tuple<Gallop.RaceDefine.RunningStyleEx, Gallop.RaceDefine.RunningStyle>(opponent.RunningStyleEx, opponent.RunningStyle);
                // 対応する作戦があるかを確認する（なかった場合は対象外なのでスルーする）
                if (!_safetyLeadRunningStyleDistanceThresholdDict.TryGetValue(key, out float threshold))
                {
                    continue;
                }
                // 閾値が0(以下)なら、その相手に対しては無条件で取れている判定
                if (threshold <= 0)
                {
                    continue;
                }
                // 一定距離未満だったら、取れていない判定
                if (myDistance - opponent.GetDistance() < threshold * _safetyLeadDistanceCoef)
                {
                    isHavingSafetyLead = false;
                    break;
                }
            }

            return isHavingSafetyLead;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 各種残り時間のリセット
        /// </summary>
        private void ResetRemainTime()
        {
            _remainActiveSec = 0f;
            _remainCheckIntervalSec = 0f;
            _remainCoolDownSec = 0f;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 効果発動: 速度上昇
        /// </summary>
        private void SpeedUp()
        {
            // 計算式：(根性/2000)^(1/2)*0.03*(作戦係数)*(少数派ボーナス)
            var addSpeed = System.Math.Pow(_owner.Guts/_param.SecureLeadSpeedUpGutsDivisor, _param.SecureLeadSpeedUpGutsExponent) * _param.SecureLeadSpeedUpCoef * _speedUpRunningStyleCoef;
            // 少数派ボーナスがあれば乗算する
            if (IsGetMinorBonus())
            {
                addSpeed *= _minorBonusRunningStyleCoef;
            }

            _addSpeedValue = (float)addSpeed;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 効果発動: 速度上昇: 少数派ボーナス（同じ作戦のウマ娘が近くにいない）を得られるか
        /// </summary>
        private bool IsGetMinorBonus()
        {
            // ▼「同じ作戦のウマ娘が近くにいない」の条件
            // 前後10m以内にいる自身以外のウマ娘について、 (同じ作戦のウマ娘) * 100 /(すべてのウマ娘)<新規定数：20 or (同じ作戦のウマ娘)=0
            bool isGetMinorBonus = false;
            // 前後10m以内にいる自身以外のウマ娘
            var nearCharaArray = _owner.GetAroundHorses()
                .Where(x => x != null && x.distanceGapAbs < _param.SecureLeadMinorBonusNearDistance).ToArray();
            var nearCharaNum = nearCharaArray.Length;
            // うち、同作戦の数
            var sameRunningStyleNearCharaNum = nearCharaArray.Where(x =>
                    x.infoSimulate.RunningStyleEx == _owner.RunningStyleEx && x.infoSimulate.RunningStyle == _owner.RunningStyle)
                .ToArray().Length;
            // (同じ作戦のウマ娘) * 100 /(すべてのウマ娘)<新規定数：20 or (同じ作戦のウマ娘)=0
            if (sameRunningStyleNearCharaNum * 100 / (float)nearCharaNum < _param.SecureLeadMinorBonusPerThreshold || sameRunningStyleNearCharaNum == 0)
            {
                isGetMinorBonus = true;
            }
            
#if CYG_DEBUG
            // 算出用パラメーターの保持
            DbgIsSpeedUpMinorBonus = isGetMinorBonus;
            DbgSpeedUpMinorBonusNearCharaNum = nearCharaNum;
            DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum = sameRunningStyleNearCharaNum;
#endif

            return isGetMinorBonus;
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 効果発動: 持久力消費
        /// </summary>
        private void ConsumeStamina()
        {
            // 計算式：【消費スタミナ基礎値】*【作成毎の係数】*【距離ごとの係数】
            
            float baseUseHp = _param.SecureLeadConsumeStaminaValue;
            // 持久力を減らす
            var useHp = _consumeStaminaRunningStyleCoef * baseUseHp * _consumeStaminaCourseDistanceCoef;
            _consumeStaminaValue = -1 * useHp;
#if CYG_DEBUG
            // デバッグ用に保持
            DbgConsumeStaminaValue = _consumeStaminaValue;
#endif
        }

#if CYG_DEBUG
        public IHorseStaminaKeepCalculator DbgStaminaKeepCalculator => new HorseStaminaKeepCalculatorNull();    // 完全に不要
        public float DbgRemainActiveSec => _remainActiveSec;
        public float DbgRemainCoolDownSec => _remainCoolDownSec;
        public float DbgRemainCheckIntervalSec => _remainCheckIntervalSec;
        public float DbgAddSpeedValue => AddSpeedValue;
        public float DbgConsumeStaminaValue { get; private set; }
        public int DbgActivateCount => _activateCount;
        public int DbgConditionNearCharaNum => 0;
        public int DbgConditionNearCharaNumSameRunningStyle => 0;
        public bool DbgIsSpeedUpMinorBonus { get; private set; }
        public int DbgSpeedUpMinorBonusNearCharaNum { get; private set; }
        public int DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum { get; private set; }
#endif
    }
}
#endif
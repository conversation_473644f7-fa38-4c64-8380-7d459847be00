namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 持久力温存状態　計算機：Nullオブジェクト
    /// </summary>
    //-------------------------------------------------------------------

    public class HorseStaminaKeepCalculatorNull : IHorseStaminaKeepCalculator
    {
        public bool IsStaminaKeepState => false;
        public void UpdateStates(float deltaTime) { }
        
#if CYG_DEBUG
        public bool DbgIsStaminaKeepState => false;
        public float DbgNeedHp => 0f;
        public float DbgRemainCheckIntervalSec => 0f;
        public float DbgNeedHpForSpurt => 0f;
        public float DbgNeedHpForMiddle => 0f;
        public float DbgNeedHpWizRand => 0f;
#endif
    }
}
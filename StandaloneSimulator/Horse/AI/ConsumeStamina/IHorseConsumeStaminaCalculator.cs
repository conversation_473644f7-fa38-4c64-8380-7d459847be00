namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スタミナ消費増加仕様　計算機インターフェース
    /// スパート前位置取り勝負 + リード確保 に使用
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseConsumeStaminaCalculator
    {
        //-------------------------------------------------------------------
        /// <summary>
        /// 目指す速度に加算される値
        /// </summary>
        float AddSpeedValue { get; }
        //-------------------------------------------------------------------
        /// <summary>
        /// 消費するHP値
        /// </summary>
        float GetAndResetConsumeStaminaValue();
        //-------------------------------------------------------------------
        /// <summary>
        /// 状態更新
        /// </summary>
        void UpdateStates(float deltaTime);
        
#if CYG_DEBUG
        /// <summary>
        /// 足溜めポイント蓄積計算機(デバッグ情報として値参照するために提供)
        /// </summary>
        IHorseStaminaKeepCalculator DbgStaminaKeepCalculator { get; }
        /// <summary> 効果残り時間 </summary>
        float DbgRemainActiveSec { get; }
        /// <summary> クールダウン残り時間 </summary>
        float DbgRemainCoolDownSec { get; }
        /// <summary> 発動チェック残り時間 </summary>
        float DbgRemainCheckIntervalSec { get; }
        /// <summary> 効果量: 速度上昇 </summary>
        float DbgAddSpeedValue { get; }
        /// <summary> 効果量: 消費持久力 </summary>
        float DbgConsumeStaminaValue { get; }
        /// <summary> 発動回数 </summary>
        int DbgActivateCount { get; }
        /// <summary> 発動条件：近くにいるウマ娘の数：前方10m+後方5m </summary>
        int DbgConditionNearCharaNum { get; }
        /// <summary> 発動条件：近くにいるウマ娘の数：前方10m+後方5m：同作戦 </summary>
        int DbgConditionNearCharaNumSameRunningStyle { get; }
        /// <summary> 速度上昇：少数派ボーナス：発動したか？ </summary>
        bool DbgIsSpeedUpMinorBonus { get; }
        /// <summary> 速度上昇：少数派ボーナス：近く（前後10m）にいるウマ娘の数 </summary>
        int DbgSpeedUpMinorBonusNearCharaNum { get; }
        /// <summary> 速度上昇：少数派ボーナス：近く（前後10m）にいる同作戦ウマ娘の数 </summary>
        int DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum { get; }
#endif
    }
}
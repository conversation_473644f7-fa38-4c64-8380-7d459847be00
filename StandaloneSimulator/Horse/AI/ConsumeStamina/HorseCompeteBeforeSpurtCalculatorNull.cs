namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スパート前位置取り勝負　計算機：Nullオブジェクト
    /// </summary>
    //-------------------------------------------------------------------

    public class HorseCompeteBeforeSpurtCalculatorNull : IHorseConsumeStaminaCalculator
    {
        public float AddSpeedValue { get; }

        public float GetAndResetConsumeStaminaValue() { return 0f;}
        public void UpdateStates(float deltaTime) { }
        
#if CYG_DEBUG
        public IHorseStaminaKeepCalculator DbgStaminaKeepCalculator => new HorseStaminaKeepCalculatorNull();
        public float DbgRemainActiveSec => 0f;
        public float DbgRemainCoolDownSec => 0f;
        public float DbgRemainCheckIntervalSec => 0f;
        public float DbgAddSpeedValue => 0f;
        public float DbgConsumeStaminaValue => 0f;
        public int DbgActivateCount => 0;
        public int DbgConditionNearCharaNum => 0;
        public int DbgConditionNearCharaNumSameRunningStyle => 0;
        public bool DbgIsSpeedUpMinorBonus => false;
        public int DbgSpeedUpMinorBonusNearCharaNum => 0;
        public int DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum => 0;
#endif
    }
}
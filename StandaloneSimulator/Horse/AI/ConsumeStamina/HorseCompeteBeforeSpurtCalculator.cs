#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スパート前位置取り勝負　計算機
    /// </summary>
    //-------------------------------------------------------------------

    public class HorseCompeteBeforeSpurtCalculator : IHorseConsumeStaminaCalculator
    {
        #region アクセサ
        private readonly IHorseRaceInfoSimulate _owner;
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly IRaceRandomGenerator _randomGenerator;
        /// <summary> イベント記録 </summary>
        private readonly IRaceEventRecorder _eventRecorder;
        /// <summary> レース時間アクセッサ </summary>
        private readonly IRaceTimeAccessor _timeAccessor;
        #endregion
        
        #region 変数
        /// <summary> パラメーター定義 </summary>
        private readonly Gallop.RaceParamDefine.CompeteBeforeSpurtParam _param;
        /// <summary> パラメーター定義: レース距離による判定距離の伸縮用 </summary>
        private Gallop.RaceParamDefine.PositionKeepParam _keepParam;
        /// <summary> コース距離 </summary>
        private int _courseDistance;
        
        /// <summary> 持久力温存状態　計算機 </summary>
        private readonly IHorseStaminaKeepCalculator _staminaKeepCalculator;
        /// <summary> 効果発動時の、速度加算値 </summary>
        private float _addSpeedValue;
        /// <summary> 効果発動時の、持久力消費値 </summary>
        private float _consumeStaminaValue;
        /// <summary> 効果発動の残り時間 </summary>
        private float _remainActiveSec = 0f;
        /// <summary> 次の発動チェックまでの残り時間 </summary>
        private float _remainCheckIntervalSec = 0f;
        /// <summary> クールダウン終了までの残り時間 </summary>
        private float _remainCoolDownSec = 0f;
        /// <summary> 効果発動中か </summary>
        private bool _isActiveEffect = false;
        /// <summary> 効果発動中に、余分に持久力を消費する状態か </summary>
        private bool _isConsumeMuchHp = false;
        /// <summary> 効果発動回数 </summary>
        private int _activateCount = 0;
        
        /// <summary> 「条件A: 先頭が遠い」の閾値 </summary>
        private float _distanceFarFromTopThreshold;
        /// <summary> 速度上昇: 作戦別係数 </summary>
        private float _speedUpRunningStyleCoef;
        /// <summary> 速度上昇: 少数派ボーナス係数 </summary>
        private float _minorBonusRunningStyleCoef;
        /// <summary> 持久力消費: 作戦別係数 </summary>
        private float _consumeStaminaRunningStyleCoef;
        /// <summary> 持久力消費: 距離ごとの係数 </summary>
        private float _consumeStaminaCourseDistanceCoef;
        #endregion
        
        #region プロパティ
        public float AddSpeedValue { get { return (_isActiveEffect && _remainActiveSec > 0f) ? _addSpeedValue : 0f; } }

        /// <summary>
        /// 消費するスタミナ量を取得し、その値が有効値（0以外）なら取得後にリセットする
        /// </summary>
        /// <returns></returns>
        public float GetAndResetConsumeStaminaValue()
        {
            // 消費スタミナ量が設定されているなら、設定後最初の1回の処理だけその値を渡す
            if (_isActiveEffect && _remainActiveSec > 0f && _consumeStaminaValue != 0f)
            {
                // コピーしておく
                var value = _consumeStaminaValue;
                // 元の値はリセットする（そうしないと、毎フレームこの値だけ減ることになる）
                _consumeStaminaValue = 0f;
                return value;
            }

            return 0f;
        }
        #endregion
        
        //-------------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="ownerAI"></param>
        /// <param name="horseAccessor"></param>
        /// <param name="randomGenerator"></param>
        /// <param name="eventRecorder"></param>
        /// <param name="timeAccessor"></param>
        /// <param name="competeBeforeSpurtParam"></param>
        /// <param name="hpParam"></param>
        /// <param name="keepParam"></param>
        /// <param name="model"></param>
        /// <param name="courseDistance"></param>
        /// <param name="baseSpeed"></param>
        /// <param name="lastSpurtBaseTargetSpeedAddCoef"></param>
        public HorseCompeteBeforeSpurtCalculator(
            IHorseRaceInfoSimulate owner, IHorseRaceAI ownerAI, IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator, IRaceEventRecorder eventRecorder, IRaceTimeAccessor timeAccessor,
            Gallop.RaceParamDefine.CompeteBeforeSpurtParam competeBeforeSpurtParam, Gallop.RaceParamDefine.HpParam hpParam,
            Gallop.RaceParamDefine.PositionKeepParam keepParam, HorseLastSpurtCalculator.CalcBaseTargetSpeedAndStatusSpeedModel model,
            int courseDistance, float baseSpeed, float lastSpurtBaseTargetSpeedAddCoef
        )
        {
            _owner = owner;
            _horseAccessor = horseAccessor;
            _randomGenerator = randomGenerator;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _param = competeBeforeSpurtParam;
            _keepParam = keepParam;
            _courseDistance = courseDistance;

            _staminaKeepCalculator = new HorseStaminaKeepCalculator(owner, ownerAI, randomGenerator, eventRecorder,
                timeAccessor, competeBeforeSpurtParam, hpParam, model, courseDistance, baseSpeed, lastSpurtBaseTargetSpeedAddCoef);

            SetupParameter();
        }

        /// <summary>
        /// 初めに、必要な数値を取得しておく
        /// </summary>
        private void SetupParameter()
        {
            // 「条件A: 先頭が遠い」の閾値
            {
                var distanceFarFromTopThreshold = _param.CompeteBeforeSpurtDistanceFarFromTopThresholdArray?
                    .FirstOrDefault(x => x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)
                    ?.Threshold;
                if (distanceFarFromTopThreshold == null)
                {
                    Debug.LogWarning("ParamDefine: CompeteBeforeSpurtDistanceFarFromTopThreshold: 作戦に対応する閾値が設定されていません");
                    return;
                }
                _distanceFarFromTopThreshold = (float)distanceFarFromTopThreshold;

                // 差し以降は「レース距離による判定距離の伸縮」を適用する
                if (_owner.RunningStyle >= Gallop.RaceDefine.RunningStyle.Sashi)
                {
                    _distanceFarFromTopThreshold *= HorsePaseCalculator.CalcOffsetDistanceRate(_keepParam, _courseDistance);
                }
            }
            
            // 速度上昇: 係数
            {
                var speedUpRunningStyleCoef = _param.CompeteBeforeSpurtSpeedUpRunningStyleCoefArray?
                    .FirstOrDefault(x => x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)?.Coef;
                if (speedUpRunningStyleCoef == null)
                {
                    Debug.LogWarning("ParamDefine: CompeteBeforeSpurtSpeedUpRunningStyleCoef: 作戦に対応する閾値が設定されていません");
                    return;
                }
                _speedUpRunningStyleCoef = (float)speedUpRunningStyleCoef;
            }

            // 少数派ボーナス係数
            {
                var minorBonusRunningStyleCoef = _param.CompeteBeforeSpurtMinorBonusRunningStyleCoefArray?
                    .FirstOrDefault(x => x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)?.Coef;
                if (minorBonusRunningStyleCoef == null)
                {
                    Debug.LogWarning("ParamDefine: CompeteBeforeSpurtMinorBonusRunningStyleCoef: 作戦に対応する閾値が設定されていません");
                    return;
                }
                _minorBonusRunningStyleCoef = (float)minorBonusRunningStyleCoef;
            }
            
            // 持久力消費: 作戦別係数
            {
                var consumeStaminaRunningStyleCoef = _param.CompeteBeforeSpurtConsumeStaminaRunningStyleCoefArray?
                    .FirstOrDefault(x => x.RunningStyleEx == _owner.RunningStyleEx && x.RunningStyle == _owner.RunningStyle)?.Coef;
                if (consumeStaminaRunningStyleCoef == null)
                {
                    Debug.LogWarning("ParamDefine: CompeteBeforeSpurtConsumeStaminaRunningStyleCoef: 作戦に対応する閾値が設定されていません");
                    return;
                }
                _consumeStaminaRunningStyleCoef = (float)consumeStaminaRunningStyleCoef;
            }
            
            // 持久力消費: 距離ごとの係数
            {
                var consumeStaminaCourseDistanceCoef = _param.CompeteBeforeSpurtConsumeStaminaCourseDistanceCoefArray?
                    .OrderBy(x=>x.Threshold)
                    .FirstOrDefault(x => x.Threshold > _courseDistance)?.Coef;
                if (consumeStaminaCourseDistanceCoef == null)
                {
                    Debug.LogWarning("ParamDefine: CompeteBeforeSpurtConsumeStaminaCourseDistanceCoef: 距離に対応する閾値が設定されていません");
                    return;
                }
                _consumeStaminaCourseDistanceCoef = (float)consumeStaminaCourseDistanceCoef;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// スパート前位置取り勝負 計算機更新
        /// キャラの相互位置関係を参照するので、Update()ではなくPostUpdate()で処理する必要がある
        /// </summary>
        public void UpdateStates(float deltaTime)
        {
            // 各種残り時間の管理
            UpdateTime(deltaTime);
            
            // 持久力温存状態計算機を回す（この計算機の処理よりも先）
            _staminaKeepCalculator.UpdateStates(deltaTime);
            
            // 発動中なら、終了をチェックする
            if (_isActiveEffect)
            {
                CheckEnd();
            }
            // 発動中でないなら、開始をチェックする
            else
            {
               CheckStart();
            }
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 時間管理の更新
        /// </summary>
        private void UpdateTime(float deltaTime)
        {
            // クールダウン中なら、残り時間を減らす
            if (_remainCoolDownSec > 0f)
            {
                _remainCoolDownSec -= deltaTime;
            }
            
            // 発動チェックのインターバル中だったら、残り時間を減らす
            if (_remainCheckIntervalSec > 0f)
            {
                _remainCheckIntervalSec -= deltaTime;
            }
            
            // 発動中なら、残り時間を減らす
            if (_remainActiveSec > 0f)
            {
                // 効果残り時間を減らす
                _remainActiveSec -= deltaTime;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 発動終了のチェック
        /// </summary>
        private void CheckEnd()
        {
            // 終了確認
            if (IsFinishedEffect())
            {
                // 残り時間をリセットし、
                ResetRemainTime();
                // クールダウンをセットし、
                _remainCoolDownSec = _param.CompeteBeforeSpurtCoolDownSec;
                // 効果発動を止める
                _isActiveEffect = false;
            }
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 発動条件のチェック（一定時間(CheckIntervalSec)ごとに行う）
        /// </summary>
        private void CheckStart()
        {
            // 該当区間以外では発動しない
            var currentSection = _owner.CalcSection();
            if (currentSection < _param.CompeteBeforeSpurtStartSection ||
                currentSection > _param.CompeteBeforeSpurtEndSection)
            {
                return;
            }
            
            // 発動チェックのインターバル中だったら、何もしない
            if (_remainCheckIntervalSec > 0f)
            {
                return;
            }
            
            // 条件を満たしている（クールダウン中でない && 体力温存状態でない）なら
            if (_remainCoolDownSec <= 0f && !_staminaKeepCalculator.IsStaminaKeepState)
            {
                // 抽選する
                DrawLotsToActuate();
            }
            // 発動チェックのインターバルに入る
            _remainCheckIntervalSec = _param.CompeteBeforeSpurtCheckIntervalSec;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 発動条件を満たした場合に、発動抽選を行う
        /// </summary>
        private void DrawLotsToActuate()
        {
            // 「条件A：先頭が遠い」を通過したか？
            bool isPassedFarFromTopCondition = IsPassedFarFromTopCondition();
            // 「条件B: 近くにウマ娘が多い」を通過したか？
            bool isPassedManyCharaInNearCondition = IsPassedManyCharaInNearCondition();
            // 条件Aの抽選を満たさず条件Bの抽選を満たすと発動中余計に持久力を消費する
            bool isConsumeMuchHp = !isPassedFarFromTopCondition && isPassedManyCharaInNearCondition;
            // 一方でも通過すると発動する
            if (isPassedFarFromTopCondition || isPassedManyCharaInNearCondition)
            {
                ActuateEffect(isConsumeMuchHp);
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 効果を発動する
        /// </summary>
        private void ActuateEffect(bool isConsumeMuchHp)
        {
            // 付随処理
            if (_activateCount == 0)
            {
                // 初回のみ、eventRecorderへの追加をし、状態示唆アイコンを出す
                _eventRecorder.AddCompeteBeforeSpurtEvent(_owner.HorseIndex, _timeAccessor.AccumulateTimeSinceStart);
            }
            // フラグ管理
            {
                _isActiveEffect = true;
                _remainActiveSec = _param.CompeteBeforeSpurtActiveSec;
                _isConsumeMuchHp = isConsumeMuchHp;
                _activateCount++;
            }
            // メイン効果発動
            {
                // 速度上昇
                SpeedUp();
                // 持久力消費
                ConsumeStamina();
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 効果の終了をチェック
        /// </summary>
        private bool IsFinishedEffect()
        {
            // 一定時間経過 or 持久力温存状態になる と終了する
            bool isFinished = _remainActiveSec <= 0f || _staminaKeepCalculator.IsStaminaKeepState;
            return isFinished;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 各種残り時間のリセット
        /// </summary>
        private void ResetRemainTime()
        {
            _remainActiveSec = 0f;
            _remainCheckIntervalSec = 0f;
            _remainCoolDownSec = 0f;
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 「発動条件A: 先頭が遠い」の抽選を通過したか
        /// </summary>
        private bool IsPassedFarFromTopCondition()
        {
            // ▼発動率
            //  (先頭との距離)<=(閾値)：0(発動しない)
            //  (先頭との距離)>(閾値)：新規定数 * Log10(賢さ)^2：30~40％程度
            
            // 先頭との距離と、作戦ごとの閾値を比較する
            var distanceFromTop = _horseAccessor.GetFirstHorseInfo().GetDistance() - _owner.GetDistance();
            // 先頭が遠くなければ、通過しない
            bool isNotFarFromTop = distanceFromTop <= _distanceFarFromTopThreshold;
            if (isNotFarFromTop) 
            {
                return false;
            }
            
            // 最終的に通過したかどうか
            bool isPassed = false;     
            
            // 先頭が遠ければ、抽選する
            // 発動率: 新規定数 * Log10(賢さ)^2
            var noticeOdds = _param.CompeteBeforeSpurtDistanceFromTopPerCoef * System.Math.Pow(System.Math.Log10(_owner.Wiz), _param.CompeteBeforeSpurtDistanceFromTopPerExponent);
            if (noticeOdds >= _randomGenerator.GetRandom(100f))
            {
                isPassed = true;
            }
            
            return isPassed;
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 「発動条件B: 近くにウマが多い」の抽選を通過したか
        /// </summary>
        private bool IsPassedManyCharaInNearCondition()
        {
            // ▼発動率
            // (近くにいるウマ娘数による係数)^(1/2)*Log10(根性)：20~30%
            // 近くにいるウマ娘数による係数 = (近くにいるウマ娘数+近くにいる同作戦のウマ娘数*新規定数(同作戦ボーナス))/(出走人数)*新規定数(全体への乗算定数)
            
            // 最終的に通過したかどうか
            bool isPassed = false;
            // 「近くにいるウマ娘数による係数」を計算する
            // そのために、近くにいるウマ娘数 と 近くにいる同作戦のウマ娘数 を取得する
            var nearCharaForwardArray = _owner.GetAroundHorses().Where(x=>x is { isBack: false })
                .Where(x => x.distanceGapAbs < _param.CompeteBeforeSpurtNearDistanceForward).ToArray();
            var nearCharaBackwardArray = _owner.GetAroundHorses().Where(x =>x is { isBack: true })
                .Where(x => x.distanceGapAbs < _param.CompeteBeforeSpurtNearDistanceBackward).ToArray();
            var sameRunningStyleNearCharaForwardArray =
                nearCharaForwardArray.Where(x => x.infoSimulate.RunningStyleEx == _owner.RunningStyleEx && x.infoSimulate.RunningStyle == _owner.RunningStyle).ToArray();
            var sameRunningStyleNearCharaBackwardArray =
                nearCharaBackwardArray.Where(x => x.infoSimulate.RunningStyleEx == _owner.RunningStyleEx && x.infoSimulate.RunningStyle == _owner.RunningStyle).ToArray();
            var nearCharaNum = nearCharaForwardArray.Length + nearCharaBackwardArray.Length;    // 近くにいるウマ娘数
            var sameRunningStyleNearCharaNum =
                sameRunningStyleNearCharaForwardArray.Length + sameRunningStyleNearCharaBackwardArray.Length;   // 近くにいる同作戦のウマ娘数
            
            // 近くにいるウマ娘数による係数 = (近くにいるウマ娘数+近くにいる同作戦のウマ娘数*新規定数(同作戦ボーナス))/(出走人数)*新規定数(全体への乗算定数)
            var nearCharaNumCoef = (nearCharaNum + sameRunningStyleNearCharaNum * _param.CompeteBeforeSpurtSameRunnnigStyleCoefOfNearCharaNumCoef) 
                / (float)_horseAccessor.GetHorseNumber() * _param.CompeteBeforeSpurtCoefOfNearCharaNumCoef;
            
            // それをもとに抽選する
            // 発動率: (近くにいるウマ娘数による係数)^(1/2)*Log10(根性)
            var noticeOdds = System.Math.Pow(nearCharaNumCoef, _param.CompeteBeforeSpurtNearDistancePerExponent) * System.Math.Log10(_owner.Guts);
            if (noticeOdds >= _randomGenerator.GetRandom(100f))
            {
                isPassed = true;
            }
            
#if CYG_DEBUG
            // 算出用パラメーターの保持
            DbgConditionNearCharaNum = nearCharaNum;
            DbgConditionNearCharaNumSameRunningStyle = sameRunningStyleNearCharaNum;
#endif

            return isPassed;
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 効果発動: 速度上昇
        /// </summary>
        private void SpeedUp()
        {
            // 計算式：((パワー/3000)^(1/2)*2+(根性/3000)^(1/5))*0.1*(作戦係数)*(少数派ボーナス)
            
            // (パワー/3000)^(1/2)*2
            var powMember = System.Math.Pow(_owner.Pow / _param.CompeteBeforeSpurtSpeedUpPowDivisor,
                _param.CompeteBeforeSpurtSpeedUpPowExponent) * _param.CompeteBeforeSpurtSpeedUpPowCoef;
            // (根性/3000)^(1/5)
            var gutsMember = System.Math.Pow(_owner.Guts / _param.CompeteBeforeSpurtSpeedUpGutsDivisor,
                _param.CompeteBeforeSpurtSpeedUpGutsExponent);
            var addSpeed = (powMember + gutsMember) * _param.CompeteBeforeSpurtSpeedUpCoef * _speedUpRunningStyleCoef;
            // 少数派ボーナスがあれば乗算する
            if (IsGetMinorBonus())
            {
                addSpeed *= _minorBonusRunningStyleCoef;
            }

            _addSpeedValue = (float)addSpeed;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 効果発動: 速度上昇: 少数派ボーナス（同じ作戦のウマ娘が近くにいない）を得られるか
        /// </summary>
        private bool IsGetMinorBonus()
        {
            // ▼「同じ作戦のウマ娘が近くにいない」の条件
            // 前後10m以内にいる自身以外のウマ娘について、 (同じ作戦のウマ娘) * 100 /(すべてのウマ娘)<新規定数：20 or (同じ作戦のウマ娘)=0
            bool isGetMinorBonus = false;
            // 前後10m以内にいる自身以外のウマ娘
            var nearCharaArray = _owner.GetAroundHorses()
                .Where(x => x != null && x.distanceGapAbs < _param.CompeteBeforeSpurtMinorBonusNearDistance).ToArray();
            var nearCharaNum = nearCharaArray.Length;
            // うち、同作戦の数
            var sameRunningStyleNearCharaNum = nearCharaArray.Where(x =>
                    x.infoSimulate.RunningStyleEx == _owner.RunningStyleEx && x.infoSimulate.RunningStyle == _owner.RunningStyle)
                .ToArray()
                .Length;
            // (同じ作戦のウマ娘) * 100 /(すべてのウマ娘)<新規定数：20 or (同じ作戦のウマ娘)=0
            if (sameRunningStyleNearCharaNum * 100 / (float)nearCharaNum < _param.CompeteBeforeSpurtMinorBonusPerThreshold || sameRunningStyleNearCharaNum == 0)
            {
                isGetMinorBonus = true;
            }
            
#if CYG_DEBUG
            // 算出用パラメーターの保持
            DbgIsSpeedUpMinorBonus = isGetMinorBonus;
            DbgSpeedUpMinorBonusNearCharaNum = nearCharaNum;
            DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum = sameRunningStyleNearCharaNum;
#endif

            return isGetMinorBonus;
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 効果発動: 持久力消費
        /// </summary>
        private void ConsumeStamina()
        {
            // 計算式：【消費スタミナ基礎値】*【作成毎の係数】* (1+抽選結果による増加分) *【距離ごとの係数】
            
            float baseUseHp = _param.CompeteBeforeSpurtConsumeStaminaValue;
            // 条件Bのみ満たしている場合は消費量が増える
            var rate = 1f + (_isConsumeMuchHp ? _param.CompeteBeforeSpurtConsumeStaminaValueRateAddend : 0f);
            // 持久力を減らす
            var useHp = _consumeStaminaRunningStyleCoef * baseUseHp * rate * _consumeStaminaCourseDistanceCoef;
            _consumeStaminaValue = -1 * useHp;
#if CYG_DEBUG
            // デバッグ用に保持
            DbgConsumeStaminaValue = _consumeStaminaValue;
#endif
        }

#if CYG_DEBUG
        public IHorseStaminaKeepCalculator DbgStaminaKeepCalculator => _staminaKeepCalculator;
        public float DbgRemainActiveSec => _remainActiveSec;
        public float DbgRemainCoolDownSec => _remainCoolDownSec;
        public float DbgRemainCheckIntervalSec => _remainCheckIntervalSec;
        public float DbgAddSpeedValue => AddSpeedValue;
        public float DbgConsumeStaminaValue { get; private set; }
        public int DbgActivateCount => _activateCount;
        public int DbgConditionNearCharaNum { get; private set; }
        public int DbgConditionNearCharaNumSameRunningStyle { get; private set; }
        public bool DbgIsSpeedUpMinorBonus { get; private set; }
        public int DbgSpeedUpMinorBonusNearCharaNum { get; private set; }
        public int DbgSpeedUpMinorBonusSameRunningStyleNearCharaNum { get; private set; }
#endif
    }
}
#endif
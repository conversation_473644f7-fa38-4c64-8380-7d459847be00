#if STANDALONE_SIMULATOR || UNITY_EDITOR

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 持久力温存状態　計算機
    /// </summary>
    //-------------------------------------------------------------------

    public class HorseStaminaKeepCalculator : IHorseStaminaKeepCalculator
    {
        #region アクセサ
        private readonly IHorseRaceInfoSimulate _owner;
        private readonly IHorseRaceAI _ownerAI;
        private readonly IRaceRandomGenerator _randomGenerator;
        /// <summary> イベント記録 </summary>
        private readonly IRaceEventRecorder _eventRecorder;
        /// <summary> レース時間アクセッサ </summary>
        private readonly IRaceTimeAccessor _timeAccessor;
        #endregion
        
        #region 変数

        /// <summary> パラメーター定義 </summary>
        private readonly Gallop.RaceParamDefine.CompeteBeforeSpurtParam _competeBeforeSpurtParam;
        private readonly Gallop.RaceParamDefine.HpParam _hpParam;
        private readonly HorseLastSpurtCalculator.CalcBaseTargetSpeedAndStatusSpeedModel _model;
        private readonly float _lastSpurtBaseTargetSpeedAddCoef;
        private readonly float _raceBaseSpeed;
        private readonly int _courseDistance;
        
        /// <summary> この計算機が稼働可能範囲に入り、有効化（初期化）されたか </summary>
        private bool _isActivated;
        /// <summary> 持久力温存状態か </summary>
        private bool _isStaminaKeepState = false;
        /// <summary> 持久力温存状態開始チェックインターバルの残り時間 </summary>
        private float _checkIntervalSec;
        /// <summary> 必要な持久力（HP） スパート用スタミナ </summary>
        private float _needHpForLastSpurt;
        /// <summary> 必要な持久力（HP） 中盤用スタミナ（100%） </summary>
        private float _needHpForMiddlePhase;
        /// <summary> スタート地点から「終盤」開始区画までの距離（序盤+中盤） </summary>
        private float _distanceFromStartToFinal;
        /// <summary> 発動区間の開始位置から「終盤」開始区画までの距離 </summary>
        private float _distanceFromActivateToFinal;
        /// <summary> 賢さによる乱数 </summary>
        private float _wizRand;
        /// <summary> 回復スキルの使用回数 </summary>
        private int _usedHealSkillCount;
        /// <summary> 効果発動回数 </summary>
        private int _activateCount = 0;
        #endregion
        
        //-------------------------------------------------------------------
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="ownerAI"></param>
        /// <param name="randomGenerator"></param>
        /// <param name="eventRecorder"></param>
        /// <param name="timeAccessor"></param>
        /// <param name="competeBeforeSpurtParam"></param>
        /// <param name="hpParam"></param>
        /// <param name="model"></param>
        /// <param name="courseDistance"></param>
        /// <param name="raceBaseSpeed"></param>
        /// <param name="lastSpurtBaseTargetSpeedAddCoef"></param>
        public HorseStaminaKeepCalculator(
            IHorseRaceInfoSimulate owner, IHorseRaceAI ownerAI, IRaceRandomGenerator randomGenerator,
            IRaceEventRecorder eventRecorder, IRaceTimeAccessor timeAccessor,
            Gallop.RaceParamDefine.CompeteBeforeSpurtParam competeBeforeSpurtParam, Gallop.RaceParamDefine.HpParam hpParam,
            HorseLastSpurtCalculator.CalcBaseTargetSpeedAndStatusSpeedModel model, int courseDistance, float raceBaseSpeed,
            float lastSpurtBaseTargetSpeedAddCoef
        )
        {
            _owner = owner;
            _ownerAI = ownerAI;
            _randomGenerator = randomGenerator;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _competeBeforeSpurtParam = competeBeforeSpurtParam;
            _lastSpurtBaseTargetSpeedAddCoef = lastSpurtBaseTargetSpeedAddCoef;
            _hpParam = hpParam;
            _model = model;
            _courseDistance = courseDistance;
            _raceBaseSpeed = raceBaseSpeed;
        }

        public bool IsStaminaKeepState => _isStaminaKeepState;

        //-------------------------------------------------------------------
        /// <summary>
        /// 持久力温存状態 計算機更新
        /// </summary>
        public void UpdateStates(float deltaTime)
        {
            // 各種残り時間の管理
            UpdateTime(deltaTime);
            
            // 発動中なら、終了をチェックする
            if (_isStaminaKeepState)
            {
                CheckEnd();
            }
            // 発動中でないなら、開始をチェックする
            else
            {
                CheckStart();
            }
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 時間管理の更新
        /// </summary>
        private void UpdateTime(float deltaTime)
        {
            // 持久力温存状態開始チェックのインターバル中なら、残り時間を減らす
            if (_checkIntervalSec > 0f)
            {
                _checkIntervalSec -= deltaTime;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 発動終了のチェック
        /// </summary>
        private void CheckEnd()
        {
            // 持久力を回復するスキルを使用すると「持久力温存状態」は解除される
            if (_usedHealSkillCount < _owner.GetActivateHealSkillCount())
            {
                DeactivateStaminaKeepState();
            }
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 持久力温存状態開始チェックのチェックをする
        /// </summary>
        private void CheckStart()
        {
            // 該当区間以外では発動しない
            var currentSection = _owner.CalcSection();
            if (currentSection < _competeBeforeSpurtParam.CompeteBeforeSpurtStartSection ||
                currentSection > _competeBeforeSpurtParam.CompeteBeforeSpurtEndSection)
            {
                return;
            }

            // 該当区間に入ったとき、一度だけ有効化処理を走らせる
            if (!_isActivated)
            {
                ActivateCalculator();
            }
            
            // 持久力温存状態開始チェックのインターバルが終わっていたら、持久力温存状態開始チェックを行う
            if (_checkIntervalSec <= 0f)
            {
                CheckActivateStaminaKeepState();
                
                // チェックのインターバルに入る
                _checkIntervalSec = _competeBeforeSpurtParam.CompeteBeforeSpurtStaminaKeepCheckIntervalSec;
            }
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 持久力温存状態開始チェックをする
        /// </summary>
        private void CheckActivateStaminaKeepState()
        {
            // 現在地点における、必要HP
            var needHp = CalcNeedHp();

            // 現在のHPが必要な持久力を下回っていた場合、
            if (_owner.GetHp() < needHp)
            {
                // 賢さに応じた値で気づき「持久力温存状態」になる
                var noticeOdds = _competeBeforeSpurtParam.CompeteBeforeSpurtStaminaKeepCheckNoticePerCoef * System.Math.Log10(_owner.Wiz);
                if (noticeOdds >= _randomGenerator.GetRandom(100f))
                {
                    ActivateStaminaKeepState();
                }
            }
        }

        /// <summary>
        /// 各持久力温存状態チェック地点で算出する、その後に必要なHP
        /// </summary>
        /// <returns></returns>
        private float CalcNeedHp()
        {
            // 「発動区間の開始位置から終盤までの距離」のうちの、未通過距離
            var currentRemainDistanceMiddle = _distanceFromStartToFinal - _owner.GetDistance();
            // 「発動区間の開始位置から終盤までの距離」のうち、どれくらいが未通過距離として残っているか(0~1)
            var remainRateOfMiddle = currentRemainDistanceMiddle / _distanceFromActivateToFinal;
            if (remainRateOfMiddle < 0 || remainRateOfMiddle > 1)
            {
                Debug.LogError("持久力温存状態チェックの発動位置が不正です");
                remainRateOfMiddle = 1;
            }

            // 必要な持久力 = ((スパート用スタミナ)+(中盤用スタミナ))*(賢さによる乱数)
            var needHp = (_needHpForLastSpurt + _needHpForMiddlePhase * remainRateOfMiddle) * _wizRand;
#if CYG_DEBUG
            // 算出用パラメーターの保持
            DbgNeedHp = needHp;
            DbgNeedHpForMiddle = _needHpForMiddlePhase * remainRateOfMiddle;
#endif
            return needHp;
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 持久力温存状態になる
        /// </summary>
        private void ActivateStaminaKeepState()
        {
            // 効果発動
            if (_activateCount == 0)
            {
                // 初回のみ、eventRecorderへの追加をし、状態示唆アイコンを出す
                _eventRecorder.AddStaminaKeepEvent(_owner.HorseIndex, _timeAccessor.AccumulateTimeSinceStart);
            }
            // フラグ管理
            {
                _isStaminaKeepState = true;
                // この状態での回復スキル使用回数を覚えておく（増えたら持久力温存状態を解除）
                _usedHealSkillCount = _owner.GetActivateHealSkillCount();
                _activateCount++;
            }
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 持久力温存状態を終了する
        /// </summary>
        private void DeactivateStaminaKeepState()
        {
            _isStaminaKeepState = false;
            ResetRemainTime();
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// この計算機自体を有効化する
        /// </summary>
        private void ActivateCalculator()
        {
            // 有効化されたタイミングで、必要な持久力の計算に必要な値を取得しておく
            InitNeedHp();
            _isActivated = true;
        }
        
        //-------------------------------------------------------------------
        /// <summary>
        /// 持久力温存状態における「必要な持久力」の計算に必要な値を取得
        /// </summary>
        private void InitNeedHp()
        {
            // スタート地点から「終盤」開始区画までの距離（序盤+中盤）
            _distanceFromStartToFinal = RaceManagerSimulate.Instance.RaceInfo.CourseSectionDistance * (Gallop.RaceDefine.COURSE_SECTION_NUM_START + Gallop.RaceDefine.COURSE_SECTION_NUM_MIDDLE);
            // 発動区間の開始位置から「終盤」開始区画までの距離（開始位置なので、Sectionは-1）// 発動区間の開始位置から「終盤（スパート開始可能）区画」（発動区間の終了位置）までの距離（開始位置なので、Sectionは-1）
            _distanceFromActivateToFinal = _distanceFromStartToFinal - RaceManagerSimulate.Instance.RaceInfo.CourseSectionDistance * (_competeBeforeSpurtParam.CompeteBeforeSpurtStartSection - 1);
            // 「終盤」開始区画からゴール地点までの距離
            var distanceFromFinalToGoal = _courseDistance - _distanceFromStartToFinal;
            
            // 賢さによる乱数　((賢さ/1200)^(1/5))*(0.9~1.1の乱数)
            _wizRand = (float)System.Math.Pow(_owner.Wiz / _competeBeforeSpurtParam.CompeteBeforeSpurtStaminaKeepWizDivisor,  _competeBeforeSpurtParam.CompeteBeforeSpurtStaminaKeepWizExponent) 
                       * _randomGenerator.GetRandom( _competeBeforeSpurtParam.CompeteBeforeSpurtStaminaKeepRandomRangeMin,  _competeBeforeSpurtParam.CompeteBeforeSpurtStaminaKeepRandomRangeMax);
            
            // スパート用スタミナ (終盤突入時からゴールまで出せる最高速で走りきれるスタミナ)
            var lastSpurtTargetSpeed = CalculateLastSpurtTargetSpeed();     // スパート前位置取り勝負の発動時に想定している、ラストスパートで目指す速度
            _needHpForLastSpurt = HorseHpCalculator.CalcNeedHp(
                _hpParam,
                _raceBaseSpeed,                 
                _owner.GroundModeifierParam.multiHpSub,
                Gallop.RaceDefine.HorsePhase.End,        // 終盤
                _owner.Guts,
                lastSpurtTargetSpeed,
                distanceFromFinalToGoal);
            
            // 中盤用スタミナ (計算地点~終盤までに基準速度で必要なスタミナ量)
            _needHpForMiddlePhase = HorseHpCalculator.CalcNeedHp(
                _hpParam,
                _raceBaseSpeed,
                _owner.GroundModeifierParam.multiHpSub,
                _owner.GetPhase(), 
                _owner.Guts, 
                _raceBaseSpeed, 
                _distanceFromActivateToFinal);
        }

        /// <summary>
        /// スパート前位置取り勝負の発動時に想定している、ラストスパートで目指す速度を計算
        /// </summary>
        /// <returns></returns>
        private float CalculateLastSpurtTargetSpeed()
        {
            // ラストスパートで目指す速度 = CalcBaseTargetSpeedAndStatusSpeed(【（ラストスパートの）目指す速度基礎値】+【コースごとの基本速度】* lastSpurtBaseTargetSpeedAddCoef)
            
            // 【（ラストスパートの）目指す速度基礎値】(乱数を除いたもの)
            var baseTargetSpeed = _ownerAI.GetBaseTargetSpeedWithoutRandom(Gallop.RaceDefine.HorsePhase.End);
            // 【コースごとの基本速度】* lastSpurtBaseTargetSpeedAddCoef
            var lastSpurtBaseTargetSpeedAdd = HorseLastSpurtCalculator.CalcLastSpurtBaseTargetSpeedAdd(_raceBaseSpeed, _lastSpurtBaseTargetSpeedAddCoef);
            // 【（ラストスパートの）目指す速度基礎値】+【コースごとの基本速度】* lastSpurtBaseTargetSpeedAddCoef
            var baseTargetSpeedOnLastSpurt = baseTargetSpeed + lastSpurtBaseTargetSpeedAdd;
            // モデルの変動パラメーターはこの時点の値を入れる
            _model.SetupParameter(baseTargetSpeedOnLastSpurt, _owner.Speed, _owner.Guts, _owner.ProperDistanceCoefSpeed);
            // ラストスパートで目指す速度
            float lastSpurtTargetSpeed = HorseLastSpurtCalculator.CalcBaseTargetSpeedAndStatusSpeed(_model);

            return lastSpurtTargetSpeed;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 各種残り時間のリセット
        /// </summary>
        private void ResetRemainTime()
        {
            _checkIntervalSec = 0f;
        }
        
#if CYG_DEBUG
        public bool DbgIsStaminaKeepState => _isStaminaKeepState;
        public float DbgNeedHp { get; private set; }
        public float DbgRemainCheckIntervalSec => _checkIntervalSec;
        public float DbgNeedHpForSpurt => _needHpForLastSpurt;
        public float DbgNeedHpForMiddle { get; private set; }
        public float DbgNeedHpWizRand => _wizRand;
#endif
    }
}
#endif
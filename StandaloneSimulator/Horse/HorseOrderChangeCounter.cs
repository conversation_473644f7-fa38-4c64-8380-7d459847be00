namespace StandaloneSimulator
{
#if CYG_DEBUG
    //-------------------------------------------------------------------
    /// <summary>
    /// 追い抜き・追い越しの記録用クラス
    /// </summary>
    //-------------------------------------------------------------------
    public class DbgOverTakeLog
    {
        public readonly int HorseIndex;
        public readonly float LogTime;

        public DbgOverTakeLog(int horseIndex, float logTime)
        {
            HorseIndex = horseIndex;
            LogTime = logTime;
        }
    }
#endif
    
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの順位変動カウンターインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseOrderChangeCounter
    {
        int ChangeCountPhaseStart { get; }
        int ChangeCountPhaseMiddle { get; }
        int ChangeCountPhaseEndAfter { get; }
        int ChangeCountCorner { get; }
        int ChangeCountCornerPhaseEndAfter { get; }
        int ChangeCountLastSpurt { get; }
        int ChangeCountDistance1 { get; }
        int ChangeCountFinalCornerAfter { get; }
        int ChangeCountLaterHalf { get; }
        bool IsOrderChangePerFrame { get; }

        void Update(float deltaTime);
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの順位変動カウンター：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseOrderChangeCounterNull : IHorseOrderChangeCounter
    {
        public int ChangeCountPhaseStart { get { return 0; } }
        public int ChangeCountPhaseMiddle { get { return 0; } }
        public int ChangeCountPhaseEndAfter { get { return 0; } }
        public int ChangeCountCorner { get { return 0; } }
        public int ChangeCountCornerPhaseEndAfter => 0;
        public int ChangeCountLastSpurt { get { return 0; } }
        public int ChangeCountDistance1 => 0;
        public int ChangeCountFinalCornerAfter => 0;
        public int ChangeCountLaterHalf => 0;
        public bool IsOrderChangePerFrame => false;

        public void Update(float deltaTime) { }
    }
#if STANDALONE_SIMULATOR || UNITY_EDITOR

    /// <summary>
    /// モデルクラス
    /// </summary>
    public class HorseOrderChangeCounterModel
    {
        public readonly IHorseRaceInfoSimulate Owner = null;
        public readonly IRaceHorseAccessor HorseAccessor = null;
        public readonly int NumRaceHorse = 0;
        public readonly float CountCoolDownTime = 0;
        public readonly int CourseDistance = 0;

        public HorseOrderChangeCounterModel(
            IHorseRaceInfoSimulate owner,
            IRaceHorseAccessor horseAccessor,
            int numRaceHorse,
            float countCoolDownTime,
            int courseDistance)
        {
            Owner = owner;
            HorseAccessor = horseAccessor;
            NumRaceHorse = numRaceHorse;
            CountCoolDownTime = countCoolDownTime;
            CourseDistance = courseDistance;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの順位変動カウンター：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class HorseOrderChangeCounterBase : IHorseOrderChangeCounter
    {
        /// <summary>一定距離からの順位変動をカウントするときの距離。#32102。</summary>
        private const float DISTANCE_1 = 200;

        protected readonly IHorseRaceInfoSimulate _owner = null;
        protected readonly IRaceHorseAccessor _horseAccessor = null;
        protected readonly int _numRaceHorse = 0;
        protected float[] _countTargetCoolDown = new float[Gallop.RaceDefine.RACE_HORSE_MAX];
        private readonly float _countCoolDownTime = 0;
        
        /// <summary>
        /// レース距離
        /// </summary>
        private float _courseDistance = 0;

        /// <summary>
        /// 区間：Startでの順位変動回数。
        /// </summary>
        public int ChangeCountPhaseStart { get; protected set; }
        /// <summary>
        /// 区間：Middleでの順位変動回数。
        /// </summary>
        public int ChangeCountPhaseMiddle { get; protected set; }
        /// <summary>
        /// 区間：End/LastSpurtでの順位変動回数。
        /// </summary>
        public int ChangeCountPhaseEndAfter { get; protected set; }
        /// <summary>
        /// コーナー通過中の順位変動回数。
        /// </summary>
        public int ChangeCountCorner { get; protected set; }
        public int ChangeCountCornerPhaseEndAfter { get; protected set; }
        /// <summary>
        /// ラストスパート発動中の順位変動回数。
        /// </summary>
        public int ChangeCountLastSpurt { get; protected set; }

        public int ChangeCountDistance1 { get; protected set; }

        public int ChangeCountFinalCornerAfter { get; protected set; }
        
        /// <summary>
        /// 後半50%以降の順位変動回数。
        /// </summary>
        public int ChangeCountLaterHalf { get; protected set; }

        /// <summary> 1フレーム前に比べて順位が変動したかどうか </summary>
        public bool IsOrderChangePerFrame { get; protected set; }
        
#if CYG_DEBUG
        protected float _currentTime = 0f;
#endif
        
        //---------------------------------------------------------------
        public HorseOrderChangeCounterBase(HorseOrderChangeCounterModel model)
        {
            _owner = model.Owner;
            _horseAccessor = model.HorseAccessor;
            _numRaceHorse = model.NumRaceHorse;

            _courseDistance = model.CourseDistance;

            ChangeCountPhaseStart = 0;
            ChangeCountPhaseMiddle = 0;
            ChangeCountPhaseEndAfter = 0;
            ChangeCountCorner = 0;
            ChangeCountCornerPhaseEndAfter = 0;
            ChangeCountLastSpurt = 0;
            ChangeCountDistance1 = 0;
            ChangeCountFinalCornerAfter = 0;
            ChangeCountLaterHalf = 0;
            IsOrderChangePerFrame = false;
            _InitCountTargetCoolDown();

            _countCoolDownTime = model.CountCoolDownTime/*owner.ParamDefine.overTakeCountCoolDownTime*/;
            
#if CYG_DEBUG
            _currentTime = 0f;
            _owner.DbgOrderChangeCounterUpOverTakeLogList?.Clear();
            _owner.DbgOrderChangeCounterDownOverTakeLogList?.Clear();
#endif
        }

        //---------------------------------------------------------------
        public void Update(float deltaTime)
        {
            // #64477 ゴール後に順位が入れ替わったのがカウントされないように。
            if (_owner.IsFinished())
            {
                return;
            }
            
#if CYG_DEBUG
            _currentTime += deltaTime;
#endif
            
            UpdateCountTargetCoolDown(deltaTime);
            if (CheckChange())
            {
                CountUpPhase();
                CountUpCorner();
                CountUpLastSpurt();
                CountUpDistance1();
                CountUpFinalCornerAfter();
                CountUpLaterHalf();

                OnChangeOrder();
            }
        }

        //---------------------------------------------------------------
        protected abstract void OnChangeOrder();
        
        //---------------------------------------------------------------
        private void CountUpPhase()
        {
            var phase = _owner.GetPhase();
            switch (phase)
            {
                case Gallop.RaceDefine.HorsePhase.Start:
                    ++ChangeCountPhaseStart;
                    break;
                case Gallop.RaceDefine.HorsePhase.MiddleRun:
                    ++ChangeCountPhaseMiddle;
                    break;
                case Gallop.RaceDefine.HorsePhase.End:
                case Gallop.RaceDefine.HorsePhase.LastSpurt:
                    ++ChangeCountPhaseEndAfter;
                    break;
            }
        }

        //---------------------------------------------------------------
        private void CountUpCorner()
        {
            if (_owner.CurCorner == Gallop.RaceDefine.CORNER_NULL)
            {
                return;
            }
            ++ChangeCountCorner;
            
            switch (_owner.GetPhase())
            {
                case Gallop.RaceDefine.HorsePhase.End:
                case Gallop.RaceDefine.HorsePhase.LastSpurt:
                    ++ChangeCountCornerPhaseEndAfter;
                    break;
            }
        }

        //---------------------------------------------------------------
        private void CountUpLastSpurt()
        {
            if (!_owner.IsLastSpurt)
            {
                return;
            }
            ++ChangeCountLastSpurt;
        }

        //---------------------------------------------------------------
        private void CountUpDistance1()
        {
            if (_owner.GetDistance() < DISTANCE_1)
            {
                return;
            }
            ++ChangeCountDistance1;
        }

        //---------------------------------------------------------------
        private void CountUpFinalCornerAfter()
        {
            if (!_owner.IsFinalCorner)
            {
                return;
            }
            ++ChangeCountFinalCornerAfter;
        }
        
        //---------------------------------------------------------------
        private void CountUpLaterHalf()
        {
            if (_owner.GetDistance() < _courseDistance * 0.5f)
            {
                return;
            }

            ++ChangeCountLaterHalf;
        }

        //---------------------------------------------------------------
        protected abstract bool CheckChange();

        //---------------------------------------------------------------
        protected void _InitCountTargetCoolDown()
        {
            System.Array.Clear(_countTargetCoolDown, 0, 0);
        }

        //---------------------------------------------------------------
        protected void _SetCountTargetCoolDown(int order)
        {
            var horseIndex = _horseAccessor.GetHorseIndexByOrder(order);
            _countTargetCoolDown[horseIndex] = _countCoolDownTime;
        }

        //---------------------------------------------------------------
        protected bool _IsEnableCountTarget(int order)
        {
            var horseIndex = _horseAccessor.GetHorseIndexByOrder(order);
            return _countTargetCoolDown[horseIndex] <= 0.0f;
        }

        //---------------------------------------------------------------
        protected void UpdateCountTargetCoolDown(float deltaTime)
        {
            for (int i = 0, cnt = _countTargetCoolDown.Length; i < cnt; ++i)
            {
                _countTargetCoolDown[i] -= deltaTime;
                if (_countTargetCoolDown[i] < 0)
                {
                    _countTargetCoolDown[i] = 0;
                }
            }
        }
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの順位変動カウンター：順位上昇。
    /// </summary>
    /// <seealso cref="Test.TestHorseOrderChangeCounterUp"/>
    //-------------------------------------------------------------------
    public class HorseOrderChangeCounterUp : HorseOrderChangeCounterBase
    {
        //---------------------------------------------------------------
        public HorseOrderChangeCounterUp(HorseOrderChangeCounterModel model) : base(model)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckChange()
        {
            IsOrderChangePerFrame = false;
            // 順位が上がっていないなら処理不要。
            if (_owner.PrevOrder <= _owner.CurOrder)
            {
                return IsOrderChangePerFrame;
            }

            int behindOrder = _owner.CurOrder + 1;

            // 一つ後ろのキャラを追い越したばかりなのでカウントしない。
            // ※同じキャラと競っている時、互いに順位変動回数がカウントアップされまくる問題の対策として、
            // ※同じキャラに対していは一定時間順位変動をカウントしないための処理。
            if (!_IsEnableCountTarget(behindOrder))
            {
                return IsOrderChangePerFrame;
            }

            // 追い越したとみなしたキャラを登録。
            _SetCountTargetCoolDown(behindOrder);
            IsOrderChangePerFrame = true;

#if CYG_DEBUG
            _owner.DbgOrderChangeCounterUpOverTakeLogList.Add(new DbgOverTakeLog(_horseAccessor.GetHorseIndexByOrder(behindOrder), _currentTime));
#endif
            
            return IsOrderChangePerFrame;
        }
        
        //---------------------------------------------------------------
        protected override void OnChangeOrder()
        {
            _owner.OverTakeTargetHaveNoOrderUpContinueTime = 0;
        }
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの順位変動カウンター：順位下降。
    /// </summary>
    /// <seealso cref="Test.TestHorseOrderChangeCounterDown"/>
    //-------------------------------------------------------------------
    public class HorseOrderChangeCounterDown : HorseOrderChangeCounterBase
    {
        //---------------------------------------------------------------
        public HorseOrderChangeCounterDown(HorseOrderChangeCounterModel model) : base(model)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckChange()
        {
            IsOrderChangePerFrame = false;
            // 順位が下がっていないなら処理不要。
            if (_owner.PrevOrder >= _owner.CurOrder)
            {
                return IsOrderChangePerFrame;
            }

            int infrontOrder = _owner.CurOrder - 1;

            // 一つ前のキャラに追い越されたばかりなのでカウントしない。
            // ※同じキャラと競っている時、互いに順位変動回数がカウントアップされまくる問題の対策として、
            // ※同じキャラに対していは一定時間順位変動をカウントしないための処理。
            if (!_IsEnableCountTarget(infrontOrder))
            {
                return IsOrderChangePerFrame;
            }

            // 追い越されたとみなしたキャラを登録。
            _SetCountTargetCoolDown(infrontOrder);
            IsOrderChangePerFrame = true;
            
#if CYG_DEBUG
            _owner.DbgOrderChangeCounterDownOverTakeLogList.Add(new DbgOverTakeLog(_horseAccessor.GetHorseIndexByOrder(infrontOrder), _currentTime));
#endif
            
            return IsOrderChangePerFrame;
        }
        
        //---------------------------------------------------------------
        protected override void OnChangeOrder()
        {
            _owner.OverTakeTargetHaveNoOrderDownContinueTime = 0;
        }
    };
#endif
}

using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 進行距離に基づくキャラの順位計算器。
    /// </summary>
    /// <seealso cref="Test.TestHorseOrderCalculator"/>
    //-------------------------------------------------------------------
    public class HorseOrderCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        private struct CurOrderInfo
        {
            
            // memo: Editor上からはSTANDALONEとGALLOPの両方が読まれうるので、こういう書き方をしている
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            public IHorseRaceInfoSimulate horseSimulate;
#endif
#if GALLOP
            public Gallop.IHorseRaceInfo horse;
#endif
            public float distance;
        }


        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private CurOrderInfo[] _horseArrayByOrder;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        private IHorseRaceInfoSimulate[] _horseArrayByIndexSimulate;
#endif
#if GALLOP
        private Gallop.IHorseRaceInfo[] _horseArrayByIndex;
#endif

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// ソート関数。
        /// </summary>
        /// <remarks>順位が高い順に昇順にソート。</remarks>
        private static void SortCurOrderSimulate(CurOrderInfo[] pairArray)
        {
            Array.Sort(pairArray,
                delegate (CurOrderInfo x, CurOrderInfo y)
                {
                    int compResult = y.distance.CompareTo(x.distance);
                    if( 0 != compResult )
                    {
                        return compResult;
                    }
                    // 距離が同一であった場合、第２ソートキーをHorseIndex(馬番)にする。
                    return x.horseSimulate.HorseIndex.CompareTo(y.horseSimulate.HorseIndex);
                }
            );
        }
        
        /// <summary>
        /// 初期化。
        /// </summary>
        public void Init( IHorseRaceInfoSimulate[] horseArray )
        {
            _horseArrayByIndexSimulate = horseArray;
            _horseArrayByOrder = new CurOrderInfo[ horseArray.Length ];
            for( int i = 0; i < _horseArrayByOrder.Length; ++i )
            {
                _horseArrayByOrder[i].horseSimulate = horseArray[i];
                _horseArrayByOrder[i].distance = 0;
            }
        }
        
        /// <summary>
        /// 順位計算。
        /// </summary>
        public void UpdateHorseOrderSimulate()
        {
            // 現在の進行距離をバッファに詰める。
            for (int i = 0; i < _horseArrayByOrder.Length; i++)
            {
                _horseArrayByOrder[i].distance = _horseArrayByOrder[i].horseSimulate.GetDistance();
            }

            // 順位で昇順ソート。
            SortCurOrderSimulate(_horseArrayByOrder);

            // 直前の順位、現在の順位を書き込み。
            for (int i = 0; i < _horseArrayByOrder.Length; i++)
            {
                var horse = _horseArrayByIndexSimulate[ _horseArrayByOrder[i].horseSimulate.HorseIndex ];
                horse.PrevOrder = horse.CurOrder;
                horse.CurOrder = i;
            }
        }

        /// <summary>
        /// 指定順位のHorseIndex取得。
        /// </summary>
        private IHorseRaceInfoSimulate GetHorseByOrderSimulate( int order )
        {
            if( order < 0 || order >= _horseArrayByOrder.Length )
            {
                return null;
            }
            return _horseArrayByOrder[ order ].horseSimulate;
        }

        /// <summary>
        /// 指定順位のHorseIndex取得。
        /// </summary>
        public int GetHorseIndexByOrderSimulate( int order )
        {
            IHorseRaceInfoSimulate horse = GetHorseByOrderSimulate( order );
            return null != horse ? horse.HorseIndex : 0;
        }
#endif
#if GALLOP
        /// <summary>
        /// ソート関数。
        /// </summary>
        /// <remarks>順位が高い順に昇順にソート。</remarks>
        private static void SortCurOrder(CurOrderInfo[] pairArray)
        {
            Array.Sort(pairArray,
                delegate (CurOrderInfo x, CurOrderInfo y)
                {
                    int compResult = y.distance.CompareTo(x.distance);
                    if( 0 != compResult )
                    {
                        return compResult;
                    }
                    // 距離が同一であった場合、第２ソートキーをHorseIndex(馬番)にする。
                    return x.horse.HorseIndex.CompareTo(y.horse.HorseIndex);
                }
            );
        }
        
        /// <summary>
        /// 初期化。
        /// </summary>
        public void Init( Gallop.IHorseRaceInfo[] horseArray )
        {
            _horseArrayByIndex = horseArray;
            _horseArrayByOrder = new CurOrderInfo[ horseArray.Length ];
            for( int i = 0; i < _horseArrayByOrder.Length; ++i )
            {
                _horseArrayByOrder[i].horse = horseArray[i];
                _horseArrayByOrder[i].distance = 0;
            }
        }
        
        /// <summary>
        /// 順位計算。
        /// </summary>
        public void UpdateHorseOrder()
        {
            // 現在の進行距離をバッファに詰める。
            for (int i = 0; i < _horseArrayByOrder.Length; i++)
            {
                _horseArrayByOrder[i].distance = _horseArrayByOrder[i].horse.GetDistance();
            }

            // 順位で昇順ソート。
            SortCurOrder(_horseArrayByOrder);

            // 直前の順位、現在の順位を書き込み。
            for (int i = 0; i < _horseArrayByOrder.Length; i++)
            {
                var horse = _horseArrayByIndex[ _horseArrayByOrder[i].horse.HorseIndex ];
                horse.PrevOrder = horse.CurOrder;
                horse.CurOrder = i;
            }
        }
        
        /// <summary>
        /// 指定順位のHorseIndex取得。
        /// </summary>
        private Gallop.IHorseRaceInfo GetHorseByOrder( int order )
        {
            if( order < 0 || order >= _horseArrayByOrder.Length )
            {
                return null;
            }
            return _horseArrayByOrder[ order ].horse;
        }
        
        /// <summary>
        /// 指定順位のHorseIndex取得。
        /// </summary>
        public int GetHorseIndexByOrder( int order )
        {
            Gallop.IHorseRaceInfo horse = GetHorseByOrder( order );
            return null != horse ? horse.HorseIndex : 0;
        }
#endif
    }
}


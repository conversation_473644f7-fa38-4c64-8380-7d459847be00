namespace StandaloneSimulator 
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 視野範囲計算機インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseVisibleCalculator
    {
        /// <summary>
        /// 視野範囲にいるキャラ。0 ~ VisibleHorseCount-1 までが有効な要素です。
        /// </summary>
        Gallop.AroundHorse[] VisibleHorses { get; }
        /// <summary>
        /// 視野範囲にいるキャラ数。
        /// </summary>
        int VisibleHorseCount { get; }

        /// <summary>
        /// 指定キャラが視野範囲かどうかを調べる。視野範囲に入っているなら、VisibleHorsesに加える。
        /// </summary>
        void CheckVisible(float visibleDistanceMax, Gallop.AroundHorse horse);
        /// <summary>
        /// 視野範囲として認識しているキャラのクリア。
        /// </summary>
        void Clear();
    }
}

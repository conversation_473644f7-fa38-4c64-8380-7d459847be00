using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 視野範囲計算機：実体。
    /// </summary>
    /// <seealso cref="Test.TestHorseVisibleCalculator"/>
    //-------------------------------------------------------------------
    public sealed class HorseVisibleCalculator : IHorseVisibleCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        // 台形の上底の長さ。
        public const float VISIBLE_LANEDISTANCE_MIN = Gallop.RaceDefine.HorseLane2DistanceCoef * 2.0f;
        // 台形の下底の長さ。
        public const float VISIBLE_LANEDISTANCE_MAX = 0.75f;

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public Gallop.AroundHorse[] VisibleHorses { get; private set; }
        public int VisibleHorseCount { get; private set; }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        /// <param name="visibleDistanceMax">視野範囲の最長距離。</param>
        public HorseVisibleCalculator(int horseNum)
        {
            VisibleHorses = new Gallop.AroundHorse[horseNum];
            VisibleHorseCount = 0;
        }


        public void CheckVisible(float visibleDistanceMax, Gallop.AroundHorse horse)
        {
            if (!IsVisible(visibleDistanceMax, horse))
            {
                return;
            }
            VisibleHorses[VisibleHorseCount] = horse;
            ++VisibleHorseCount;
        }

        public void Clear()
        {
            VisibleHorseCount = 0;
            System.Array.Clear(VisibleHorses, 0, VisibleHorses.Length);
        }


        /// <summary>
        /// 指定キャラが視野範囲内かどうか。
        /// </summary>
        private bool IsVisible(float visibleDistanceMax, Gallop.AroundHorse horse)
        {
            // レーン距離が視野範囲内か。
            if(!CalcVisibleLaneDistance(visibleDistanceMax, horse.distanceGap, out float laneDistAbs))
            {
                return false;
            }
            if (horse.laneGapAbs > laneDistAbs)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 指定距離離れているときの、横の視野範囲（レーン距離半径）を計算。
        /// </summary>
        /// <param name="distanceGap">この距離離れている時、</param>
        /// <param name="retLaneDistanceAbs">左右にこのレーンだけ見えている。※返却。</param>
        /// <returns>計算の成否。falseの場合は確実に視野範囲から外れている。その場合retLaneDistanceは0が返る。</returns>
        private bool CalcVisibleLaneDistance(float visibleDistanceMax, float distanceGap, out float retLaneDistanceAbs)
        {
            retLaneDistanceAbs = 0;

            // 前方視野範囲が０なら横も０。
            if (visibleDistanceMax <= 0)
            {
                return false;
            }

            // horseとの距離差分が視野範囲の上限下限の範囲外の場合、計算は省く。
            if (distanceGap < 0 ||
                distanceGap > visibleDistanceMax)
            {
                return false;
            }

            float distanceGapRate = distanceGap / visibleDistanceMax;
            float laneDist = VISIBLE_LANEDISTANCE_MIN + (VISIBLE_LANEDISTANCE_MAX - VISIBLE_LANEDISTANCE_MIN) * distanceGapRate;

            // laneDistは自分の左右合わせての長さなので半分にする。
            laneDist *= 0.5f;
            retLaneDistanceAbs = laneDist;

            return true;
        }
    }
}

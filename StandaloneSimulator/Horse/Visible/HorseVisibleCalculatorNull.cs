#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 視野範囲計算機：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseVisibleCalculatorNull : IHorseVisibleCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public Gallop.AroundHorse[] VisibleHorses { get; private set; }
        public int VisibleHorseCount { get { return 0; } }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseVisibleCalculatorNull()
        {
            VisibleHorses = new Gallop.AroundHorse[0];
        }

        public void CheckVisible(float visibleDistanceMax, Gallop.AroundHorse horse)
        {
        }

        public void Clear()
        {
        }
    }
}
#endif

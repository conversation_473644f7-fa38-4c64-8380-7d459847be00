namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの敗因情報構築クラス。
    /// </summary>
    //-------------------------------------------------------------------
    public static partial class HorseDefeatCalculator
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        private const int WIN_FINISHORDER_THRESHOLD = 0;

        //---------------------------------------------------------------
        /// <summary>
        /// 敗因計算。
        /// </summary>
        /// <returns>RaceDefine.DefeatTypeのビットフラグの組み合わせ。</returns>
        //---------------------------------------------------------------
        public static Gallop.RaceDefine.DefeatType CalcDefeat(IHorseRaceInfoSimulate horse, IRaceHorseAccessor horseAccessor, Gallop.RaceParamDefine.DefeatParam defeatParam)
        {
            //-----------------------------------------------------------
            // 勝利した。
            //-----------------------------------------------------------
            if( horse.FinishOrder <= WIN_FINISHORDER_THRESHOLD )
            {
                return Gallop.RaceDefine.DefeatType.Win;
            }
            //-----------------------------------------------------------
            // 惜敗した。
            // 着順が２位で、かつ着差が５馬身以内。
            //-----------------------------------------------------------
            else if( horse.FinishOrder <= (defeatParam.DefeatLoseFinishOrder - 1) ) // defeatLoseFinishOrderは1~で入力されているので、-1する。
            {
                var diffDistance = RaceUtil.DiffTime2Distance( horse.FinishTimeDiffFromPrevHorse );
                var diffBashin = RaceUtil.Distance2Bashin( diffDistance );
                if( diffBashin <= defeatParam.DefeatLoseBashinDiff )
                {
                    return Gallop.RaceDefine.DefeatType.Lose;
                }
            }

            //-----------------------------------------------------------
            // 距離適性が一定値未満。
            //-----------------------------------------------------------
            if(horse.ActiveProperDistance < defeatParam.DefeatProperDistance)
            {
                return Gallop.RaceDefine.DefeatType.ProperDistance;
            }

            //-----------------------------------------------------------
            // 馬場適正が一定値未満。
            //-----------------------------------------------------------
            if (horse.ActiveProperGroundType < defeatParam.DefeatProperGround)
            {
                return Gallop.RaceDefine.DefeatType.ProperGround;
            }

            //-----------------------------------------------------------
            // 同じ走法のキャラ数が一定数以上いる。
            //-----------------------------------------------------------
            {
                int cnt = horseAccessor.GetRunningStyleCount( horse.RunningStyle );
                int borderCnt = RaceUtil.GetIntThresholdByPer( horseAccessor.GetHorseNumber(), defeatParam.DefeatSameRunningStylePer );
                if( cnt > borderCnt )
                {
                    return Gallop.RaceDefine.DefeatType.RunningStyleMany;
                }
            }

            //-----------------------------------------------------------
            // Hpが切れた。
            //-----------------------------------------------------------
            if( horse.IsHpEmptyOnRace )
            {
                //-------------------------------------------------------
                // 掛かった回数。
                //-------------------------------------------------------
                if( horse.TemptationCount >= defeatParam.DefeatTemptaionCount )
                {
                    return Gallop.RaceDefine.DefeatType.Temptaion;
                }

                //-------------------------------------------------------
                // 根性の順位。
                //-------------------------------------------------------
                int borderOrder = RaceUtil.GetIntThresholdByPer( horseAccessor.GetHorseNumber(), defeatParam.DefeatGutsOrderPer );
                if( horse.GutsOrder+1 >= borderOrder )
                {
                    return Gallop.RaceDefine.DefeatType.GutsOrder;
                }

                //-------------------------------------------------------
                // スタミナが足りていない。
                //-------------------------------------------------------
                return Gallop.RaceDefine.DefeatType.Stamina;
            }

            //-----------------------------------------------------------
            // ラストスパートに失敗したか。
            //-----------------------------------------------------------
            if( ( horse.LastSpurtCalcResult & LastSpurtCalcResult.FalseFlags ) != 0 )
            {
                return Gallop.RaceDefine.DefeatType.LastSpurtFalse;
            }

            //-----------------------------------------------------------
            // ラストスパートに成功したが狙いたい速度を落としたか。
            //-----------------------------------------------------------
            if(horse.LastSpurtCalcResult == LastSpurtCalcResult.True)
            {
                return Gallop.RaceDefine.DefeatType.LastSpurtTargetSpeedDec;
            }

            //-----------------------------------------------------------
            // やる気。
            //-----------------------------------------------------------
            if (horse.Motivation <= defeatParam.DefeatMotivation)
            {
                return Gallop.RaceDefine.DefeatType.Motivation;
            }

            //-----------------------------------------------------------
            // １～３位のキャラに比べて特性所持数が少ない。
            //-----------------------------------------------------------
            int selfPassiveSkillNum = horse.SkillManager.GetSkills().Length;
            {
                int topHorsesPassiveNumAvg = CalcTopPassiveSkillNumAverage(horseAccessor, horse.HorseIndex);
                if( selfPassiveSkillNum < topHorsesPassiveNumAvg )
                {
                    return Gallop.RaceDefine.DefeatType.PassiveSkillNum;
                }
            }

            //-----------------------------------------------------------
            // 一定時間前方ブロックされていた。
            //-----------------------------------------------------------
            if( horse.BlockFrontAccumulateTime >= defeatParam.DefeatFrontBlockAccumulateTime )
            {
                return Gallop.RaceDefine.DefeatType.BlockFrontTime;
            }

            //-----------------------------------------------------------
            // 全ての敗因に当てはまらなかった場合、速度が足りないとみなす。
            //-----------------------------------------------------------
            return Gallop.RaceDefine.DefeatType.Speed;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// １～３位のキャラの平均特性所持数計算。
        /// </summary>
        /// <param name="exceptHorseIndex">このキャラはカウントから除外する。</param>
        //---------------------------------------------------------------
        public static int CalcTopPassiveSkillNumAverage(IRaceHorseAccessor horseAccessor, int exceptHorseIndex)
        {
            const int TOP_HORSE_NUM = 3;

            int checkedHorseNum = 0;
            int allSkillNum = 0;
            for( int i = 0; i < TOP_HORSE_NUM; ++i )
            {
                var horse = horseAccessor.GetHorseInfoByFinishOrder( i );
                if( horse.HorseIndex == exceptHorseIndex )
                {
                    continue;
                }

                allSkillNum += horse.SkillManager.GetSkills().Length;
                ++checkedHorseNum;
            }

            if( 0 == checkedHorseNum )
            {
                return 0;
            }

            // 小数第１位四捨五入して端数切捨て。
            return (int)( (float)allSkillNum / checkedHorseNum + 0.5f );
        }
#endif
    }
}

namespace StandaloneSimulator
{
    public static partial class HorseDefeatCalculator
    {
#if GALLOP && CYG_DEBUG
        // デバコマ参照用に切り出し
        public static int CalcTopPassiveSkillNumAverage(Gallop.IRaceHorseAccessor horseAccessor, int exceptHorseIndex)
        {
            const int TOP_HORSE_NUM = 3;

            int checkedHorseNum = 0;
            int allSkillNum = 0;
            for( int i = 0; i < TOP_HORSE_NUM; ++i )
            {
                var horse = horseAccessor.GetHorseInfoByFinishOrder( i );
                if( horse.HorseIndex == exceptHorseIndex )
                {
                    continue;
                }

                allSkillNum += horse.SkillManager.GetSkills().Length;
                ++checkedHorseNum;
            }

            if( 0 == checkedHorseNum )
            {
                return 0;
            }

            // 小数第１位四捨五入して端数切捨て。
            return (int)( (float)allSkillNum / checkedHorseNum + 0.5f );
        }
#endif
    }
}
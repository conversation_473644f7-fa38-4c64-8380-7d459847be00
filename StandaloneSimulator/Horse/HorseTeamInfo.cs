using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// チーム競技場の１チームの情報。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseTeamInfo
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>
        /// チームId。
        /// </summary>
        public int TeamId { get; private set; }
        /// <summary>
        /// チームメンバー。
        /// </summary>
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public IHorseRaceInfoSimulate[] TeamMemberArraySimulate { get; private set; }
#endif
#if GALLOP
        public Gallop.IHorseRaceInfo[] TeamMemberArray { get; private set; }
#endif

        /// <summary>
        /// チームとして獲得したスコア。
        /// </summary>
        public List<ScoreData> ScoreList => _scoreCalc.ScoreList;

        private readonly TeamScoreCalculator _scoreCalc;
        private readonly int _selfEvaluate;
        private readonly int _opponentEvaluate;
        private readonly int _supportCardScoreBonus;
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public HorseTeamInfo(int teamId, IHorseRaceInfoSimulate[] teamMemberArraySimulate, int selfEvaluate, int opponentEvaluate, int supportCardScoreBonus)
        {
            _selfEvaluate = selfEvaluate;
            _opponentEvaluate = opponentEvaluate;
            _supportCardScoreBonus = supportCardScoreBonus;
            TeamId = teamId;
            TeamMemberArraySimulate = teamMemberArraySimulate;

            _scoreCalc = new TeamScoreCalculator(teamMemberArraySimulate);
        }
#endif
#if GALLOP
        public HorseTeamInfo(int teamId, Gallop.IHorseRaceInfo[] teamMemberArray)
        {
            TeamId = teamId;
            TeamMemberArray = teamMemberArray;

            _scoreCalc = new TeamScoreCalculator(teamMemberArray);
        }
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// チームスコア計算に影響するアイテム配列取得。
        /// </summary>
        private int[] GetItemIdArray()
        {
            // エースの所持しているアイテムをチームスコアに影響させる。
            var ace = TeamMemberArraySimulate.FirstOrDefault(h => h.IsTeamAce);
            if (ace != null)
            {
                return ace.ItemIdArray;
            }
            else
            {
                return new int[0];
            }
        }

        /// <summary>
        /// 全員ゴール時の処理。
        /// </summary>
        public void OnAllFinish()
        {
            var itemIdArray = GetItemIdArray();

            _scoreCalc.OnAllFinish(
                _selfEvaluate,
                _opponentEvaluate, 
                itemIdArray,
                _supportCardScoreBonus);
        }

        /// <summary>
        /// チームメンバーの獲得スコアと、チームとして獲得したスコアの合計計算。
        /// </summary>
        public int CalcMemberAndTeamScoreTotal()
        {
            // チームメンバー個人が獲得したスコアの合計。
            int memberScoreTotal = TeamMemberArraySimulate.Sum(h => h.CalcScoreTotal());
            
            // チームとして獲得したスコアの合計。
            int teamScoreTotal = _scoreCalc.CalcTotal();

            return memberScoreTotal + teamScoreTotal;
        }
#endif
    }
}

using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /// <summary>
    /// キャラの基礎ステータス実体。
    /// </summary>
    /// <remarks>
    /// 育成されたステータスをやる気で補正したへのアクセスを提供する。
    /// </remarks>
    /// <seealso cref="Test.TestRaceParameter"/>
    public class RaceParameter : IRaceParameter
    {
        /// <summary> 素パラが丸められる上限値 </summary>
        public const int STATUS_THRESHOLD = 1200;
        
        /// <summary>
        /// 素のパラメータ
        /// </summary>
        public int RawSpeed { get; private set; }
        public int RawStamina { get; private set; }
        public int RawPow { get; private set; }
        public int RawGuts { get; private set; }
        public int RawWiz { get; private set; }
        
        /// <summary>
        /// やる気補正済みの基礎ステータス。
        /// </summary>
        public float BaseSpeed { get; private set; }
        public float BaseStamina { get; private set; }
        public float BasePow { get; private set; }
        public float BaseGuts { get; private set; }
        public float BaseWiz { get; private set; }
        public Gallop.RaceDefine.Motivation Motivation { get; private set; }
        public float MotivationCoef { get; private set; }

        public RaceParameter(
            int speed, 
            int stamina, 
            int pow, 
            int guts, 
            int wiz, 
            Gallop.RaceDefine.Motivation motivation,
            int singleModeTeamRank)
        {
            // 素のパラメータを保持しておく
            RawSpeed = speed;
            RawStamina = stamina;
            RawPow = pow;
            RawGuts = guts;
            RawWiz = wiz;
            
            float rawSpeed = speed;
            float rawStamina = stamina;
            float rawPow = pow;
            float rawGuts = guts;
            float rawWiz = wiz;

            // シミュレーターとクライアント両方からの参照がありうるので、↓のような特殊なチェックが必要
            bool isExistRaceInfo = false;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            isExistRaceInfo |= (RaceManagerSimulate.HasInstance() && RaceManagerSimulate.Instance.RaceInfo != null);
#endif
#if GALLOP
            isExistRaceInfo |= Gallop.RaceManager.RaceInfo != null;
#endif
            // #91196 一定値以上の素パラを小さく扱う処理
            if (isExistRaceInfo)
            {
                rawSpeed = AdjustRawStatus(rawSpeed);
                rawStamina = AdjustRawStatus(rawStamina);
                rawPow = AdjustRawStatus(rawPow);
                rawGuts = AdjustRawStatus(rawGuts);
                rawWiz = AdjustRawStatus(rawWiz);
            }

            // 育成チームランクによる基礎ステータスボーナス。
            // アオハル杯以外ではRaceHorseData.team_rankの値は0が渡ってくるので、ここではRaceTypeで「アオハル杯かどうか」のチェックは行わない。 
            int teamRankBonus = GetTeamRankStatusBonus(singleModeTeamRank);

            MotivationCoef = GetMotivationCoef(motivation);
            BaseSpeed = AdjustByMotivation( rawSpeed + teamRankBonus, MotivationCoef);
            BaseStamina = AdjustByMotivation(rawStamina + teamRankBonus, MotivationCoef);
            BasePow = AdjustByMotivation(rawPow + teamRankBonus, MotivationCoef);
            BaseGuts = AdjustByMotivation(rawGuts + teamRankBonus, MotivationCoef);
            BaseWiz = AdjustByMotivation(rawWiz + teamRankBonus, MotivationCoef);
            Motivation = motivation;
        }
        
        /// <summary>
        /// 素パラが閾値を超えていたらその分補正して返す。
        /// </summary>
        public static float AdjustRawStatus(float rawStatus)
        {
            const float COEF = 0.5f;
            
            // 素パラが閾値以下なら補正の必要なし。
            if (rawStatus <= STATUS_THRESHOLD)
            {
                return rawStatus;
            }

            // 素パラのうち閾値を超えている部分に係数を掛けて返す。
            // ex. 素パラ1300,閾値1200,係数0.5 => 1200 + (100 * 0.5) = 1250 
            float adjustTarget = rawStatus - STATUS_THRESHOLD;
            float adjusted = STATUS_THRESHOLD + (adjustTarget * COEF);
            return adjusted;
        }
        
        /// <summary>
        /// チーム対抗戦のチームランクボーナス取得。
        /// </summary>
        private static int GetTeamRankStatusBonus(int singleModeTeamRank)
        {
            if (singleModeTeamRank == 0)
            {
                return 0;
            }
            
            // 返り値
            int addStatus = 0;
            
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            var masterTeamStatusSimulate = RaceUtil.GetTeamStatusBonus(singleModeTeamRank);
            if (masterTeamStatusSimulate != null)
            {
                addStatus = masterTeamStatusSimulate.AddStatus;
            }
#endif
#if GALLOP
            var masterTeamStatus = Gallop.RaceUtil.GetTeamStatusBonus(singleModeTeamRank);
            if (masterTeamStatus != null)
            {
                addStatus = masterTeamStatus.AddStatus;
            }
#endif
            return addStatus;
        }

        /// <summary>
        /// 基礎ステータスをやる気で補正。
        /// </summary>
        private static float AdjustByMotivation(float baseParam, float motivationCoef)
        {
            float retAdjusted = baseParam;

            // やる気による基礎ステータス補正。
            retAdjusted *= motivationCoef;
            bool isRemoveStatusLimit = false;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
            isRemoveStatusLimit =
                RaceManagerSimulate.HasInstance() &&
                RaceManagerSimulate.Instance.RaceInfo != null &&
                RaceManagerSimulate.Instance.RaceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit);
#elif GALLOP
            isRemoveStatusLimit =
                Gallop.RaceManager.RaceInfo != null &&
                Gallop.RaceManager.RaceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit);
#endif
            if (isRemoveStatusLimit)
            {
                retAdjusted = StandaloneSimulator.RaceUtilMath.Max(retAdjusted, Gallop.RaceDefine.STATUS_MIN);
            }
            else
            {
                retAdjusted = StandaloneSimulator.RaceUtilMath.Clamp(retAdjusted, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
            }

            return retAdjusted;
        }

        /// <summary>
        /// やる気による基礎ステータス補正係数取得。
        /// </summary>
        public static float GetMotivationCoef(Gallop.RaceDefine.Motivation motivation)
        {
#if CYG_DEBUG && UNITY_EDITOR
            // ユニットテストでRaceParameter生成時に、テストによってはMasterDataManagerの生成・初期化が行われないため、ここで処理抜ける。
            // 製品コードでここを通過するときにMasterDataManagerのインスタンスは必ず存在しているため、#ifでエラー処理を書いておいて問題無いはず。

            // UNITY_EDITORではMasterManagerへのアクセスOKなので分岐不要
            if (!MasterManager.HasInstance())
            {
                return 1;
            }
#endif
            
            // シミュレーターとクライアント両方からの参照がありうるので、↓のような特殊なチェックが必要
            bool isNotExistMasterDataManager = false;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
            isNotExistMasterDataManager |= !MasterManager.HasInstance();
#endif
#if GALLOP
            isNotExistMasterDataManager |= !Gallop.MasterDataManager.HasInstance();
#endif
            if (isNotExistMasterDataManager)
            {
                Debug.LogWarning("MasterDataManagerが存在しないためやる気補正値が取得できません");
                return 1;
            }

            int motivationRate = 1;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
            if (MasterManager.HasInstance())
            {
                var masterRaceMotivationSimulate = MasterManager.Instance.MasterRaceMotivationRate.Get((int) motivation);
                if (masterRaceMotivationSimulate != null)
                {
                    motivationRate = masterRaceMotivationSimulate.MotivationRate;
                }
                else
                {
                    Debug.LogWarning($"やる気({motivation})の補正値がrace_motivation_rate.csvに登録されていないため、やる気補正値が取得できていない可能性があります");
                }
            }
#endif
#if GALLOP
            if (Gallop.MasterDataManager.HasInstance())
            {
                var masterRaceMotivation = Gallop.MasterDataManager.Instance.masterRaceMotivationRate.Get((int) motivation);
                if (masterRaceMotivation != null)
                {
                    motivationRate = masterRaceMotivation.MotivationRate;
                }
                else
                {
                    Debug.LogWarning($"やる気({motivation})の補正値がrace_motivation_rate.csvに登録されていないため、やる気補正値が取得できていない可能性があります");
                }
            }
#endif
            
            return RaceUtilMath.MasterInt2Float(motivationRate);
        }
    }
}

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 目指す速度基礎値計算インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseBaseTargetSpeedCalculator
    {
        /// <summary>
        /// 指定距離での目指す速度基礎値取得。
        /// </summary>
        /// <remarks>
        /// 事前にCalcBaseTargetSpeedArrayで計算しておくこと。
        /// </remarks>
        float GetBaseTargetSpeed(float distance);

        /// <summary>
        /// 指定フェーズでのランダム値を含まない目指す速度基礎値取得。
        /// </summary>
        float GetBaseTargetSpeedWithoutRandom(Gallop.RaceDefine.HorsePhase phase);
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// 目指す速度基礎値計算。
        /// </summary>
        void CalcBaseTargetSpeedArray(
            RacePhaseCalculator phaseCalculator,
            IRaceRandomGenerator randomGenerator,
            Gallop.RaceParamDefine.BaseTargetSpeedParam paramDefine,
            Gallop.RaceDefine.RunningStyle runningStyle,
            Gallop.RaceDefine.RunningStyleEx runningStyleEx,
            float wiz,
            float speed,
            float properDistanceCoef,
            float addSpeedParamCoef);
        
    #if CYG_DEBUG
        IHorseRaceInfoSimulate DbgOwnerHorse { get; set; }
    #endif
#endif
    }
}

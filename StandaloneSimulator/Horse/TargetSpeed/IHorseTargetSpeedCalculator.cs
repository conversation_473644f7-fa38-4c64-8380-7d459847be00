namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 最終的な目指す速度計算処理インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseTargetSpeedCalculator
    {
        /// <summary>
        /// 最終的な目指す速度の基礎値(スキルによる上昇が入っていない値)
        /// </summary>
        float BaseTargetSpeed { get; }
        /// <summary>
        /// 最終的な目指す速度計算結果。
        /// </summary>
        float TargetSpeed { get; }

        /// <summary>
        /// 更新。
        /// </summary>
        void Update(float deltaTime);
    }
}

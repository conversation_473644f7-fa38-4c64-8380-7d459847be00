#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 最終的な目指す速度計算：レース中。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseTargetSpeedCalculatorRace : IHorseTargetSpeedCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public float BaseTargetSpeed { get; private set; }
        public float TargetSpeed { get; private set; }

        private readonly IHorseRaceInfoSimulate _owner;
        private readonly IHorseRaceAI _ownerAI;
        private readonly Gallop.RaceParamDefine _paramDefine;
        private readonly float _lastSpurtCoef;
        private readonly float _forceInMoveAddTargetSpeed;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseTargetSpeedCalculatorRace(
            IHorseRaceInfoSimulate owner,
            IHorseRaceAI ownerAI,
            Gallop.RaceParamDefine paramDefine,
            IRaceRandomGenerator randomGenerator)
        {
            _owner = owner;
            _ownerAI = ownerAI;
            _paramDefine = paramDefine;
            _forceInMoveAddTargetSpeed = CalcForceInMoveTargetSpeedAdd(randomGenerator);
        }

        public void Update(float deltaTime)
        {
            // 目指す速度基礎値。
            float targetSpeed = GetBaseTargetSpeed();
            BaseTargetSpeed = targetSpeed;
        #if CYG_DEBUG
            _owner.DbgBaseTargetSpeed = targetSpeed;
            _owner.DbgPositionKeepBaseTargetSpeedMultiply = 1;
            _owner.DbgForceInMoveAddTargetSpeed = 0;
            _owner.DbgSkillAddTargetSpeed = 0;
            _owner.DbgSlopeAddTargetSpeed = 0;
        #endif

            // ポジション維持による狙いたい速度基礎値乗算。
            // ※この処理は、GetBaseTargetSpeed()で得た基礎値に対して行うこと。
            if (_ownerAI.IsPositionKeep)
            {
                targetSpeed *= _ownerAI.PositionKeepBaseTargetSpeedMultiply;
            #if CYG_DEBUG
                _owner.DbgPositionKeepBaseTargetSpeedMultiply = _ownerAI.PositionKeepBaseTargetSpeedMultiply;
            #endif
            }

            // 序盤インに寄るための加減速。
            if (_ownerAI.IsForceInMoveEnable)
            {
                targetSpeed += _forceInMoveAddTargetSpeed;
            #if CYG_DEBUG
                _owner.DbgForceInMoveAddTargetSpeed = _forceInMoveAddTargetSpeed;
            #endif
            }

            // スパート前位置取り勝負による速度上昇
            targetSpeed += _ownerAI.CompeteBeforeSpurtAddSpeed;
            
            // リード確保による速度上昇
            targetSpeed += _ownerAI.SecureLeadAddSpeed;
            
            // スキルによる狙いたい速度加算。
            float prevTargetSpeed = targetSpeed;
            targetSpeed = _owner.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TargetSpeed, targetSpeed);
            if (!RaceUtilMath.Approximately(_owner.GetLaneDistance(), _owner.PrevLaneDistance))
            {
                // TargetSpeedOnMoveLaneはレーン移動中のみ加算される。
                if (_owner.HasModifier(Gallop.SkillDefine.SkillModifierParam.TargetSpeedOnMoveLane))
                {
                    float add = CalcAddTargetSpeedOnLaneMove(_owner.Pow, _paramDefine.Skill);
                    targetSpeed += add;
                }
            }
        #if CYG_DEBUG
            _owner.DbgSkillAddTargetSpeed = targetSpeed - prevTargetSpeed;
        #endif

            // 坂補正。
            {
                float slopeAddSpeed = 0.0f;
                if (Gallop.RaceDefine.SlopeType.Up == _owner.SlopeType)
                {
                    slopeAddSpeed = HorseSlopeCalculator.CalcUpSlopeAddSpeed(
                        _paramDefine.Slope, 
                        _owner.Pow, 
                        _owner.SlopePer);
                }
                else if (Gallop.RaceDefine.SlopeType.Down == _owner.SlopeType)
                {
                    // 下り坂の場合、速度加算モードになっているかどうか。
                    if (_owner.IsDownSlopeAccelMode)
                    {
                        slopeAddSpeed = HorseSlopeCalculator.CalcDownSlopeAddSpeed(
                            _paramDefine.Slope, 
                            _owner.SlopePer);
                    }
                }
                targetSpeed += slopeAddSpeed;
            #if CYG_DEBUG
                _owner.DbgSlopeAddTargetSpeed = slopeAddSpeed;
            #endif
            }
            
            // 競り合い（叩き合い）。
            if (_owner.IsCompeteFight)
            {
                targetSpeed += _owner.CompeteFightAddTargetSpeed;
            }
            // 競り合い（ハナ奪い合い）。
            if (_owner.IsCompeteTop)
            {
                targetSpeed += _owner.CompeteTopAddTargetSpeed;
            }
            // 上限突破パラメータスタミナ
            targetSpeed += _owner.StaminaLimitBreakBuffAddTargetSpeed;

            // HPが切れていたら、最低速度まで落とす。
            if (_owner.GetHp() <= 0.0f)
            {
                targetSpeed = _owner.MinSpeed;
            }

        #if CYG_DEBUG
            // デバッグ機能：現在速度上書き。
            var overrideInfo = RaceSimulateDebugger.GetOverrideInfo(
                _owner.HorseIndex, 
                RaceManagerSimulate.Instance.AccumulateTimeSinceStart);
            if (overrideInfo != null)
            {
                if (overrideInfo.IsOverrideSpeed)
                {
                    targetSpeed = overrideInfo.Speed;
                }
            }
        #endif

            TargetSpeed = RaceUtilMath.Clamp(targetSpeed, _paramDefine.Speed.TargetSpeedMin, Gallop.RaceDefine.MAX_SPEED);
        }
        
        /// <summary>
        /// レーン移動速度上昇時、目指す速度に加算される値の計算。
        /// </summary>
        private float CalcAddTargetSpeedOnLaneMove(float pow, Gallop.RaceParamDefine.SkillParam skillParam)
        {
            return (float)Math.Pow(pow * skillParam.LaneMoveAddParam1, skillParam.LaneMoveAddParam2);
        }

        /// <summary>
        /// 目指す速度基礎値取得。ラストスパート中であれば、ラストスパートの目指す速度を基礎値として返す。
        /// </summary>
        private float GetBaseTargetSpeed()
        {
            float targetSpeed;

            // ラストスパート中は、ラストスパートの目指す速度を基礎値として扱う。
            if (_owner.IsLastSpurt)
            {
                targetSpeed = _owner.LastSpurtTargetSpeed;
#if CYG_DEBUG
                _owner.DbgLastSpurtTargetSpeed = targetSpeed;
#endif
            }
            // ラストスパートでなければ、目指す速度基礎値を使う。
            else
            {
                targetSpeed = _ownerAI.GetBaseTargetSpeed();
            }

            return targetSpeed;
        }

        /// <summary>
        /// 序盤、強制的にインに寄るための狙いたい速度に加算する値取得。
        /// </summary>
        private float CalcForceInMoveTargetSpeedAdd(IRaceRandomGenerator randomGenerator)
        {
            float addBase = 0;
            switch (_owner.RunningStyle)
            {
                case Gallop.RaceDefine.RunningStyle.Nige: addBase = _paramDefine.forceInMoveTargetSpeedAddRunningOut; break;
                case Gallop.RaceDefine.RunningStyle.Sashi: addBase = _paramDefine.forceInMoveTargetSpeedAddInsertion; break;
                case Gallop.RaceDefine.RunningStyle.Senko: addBase = _paramDefine.forceInMoveTargetSpeedAddProceding; break;
                case Gallop.RaceDefine.RunningStyle.Oikomi: addBase = _paramDefine.forceInMoveTargetSpeedAddChasing; break;
            }
            if (RaceUtilMath.Approximately(addBase, 0.0f))
            {
                return 0;
            }

            // 固定値の符号が+なら0 ~ +forceInMoveTargetSpeedAddRandomRangeまででランダム。
            // 固定値の符号が-なら-forceInMoveTargetSpeedAddRandomRange ~ 0まででランダム。
            float randomMin = addBase > 0 ? 0 : -_paramDefine.forceInMoveTargetSpeedAddRandomRange;
            float randomMax = addBase > 0 ? _paramDefine.forceInMoveTargetSpeedAddRandomRange : 0;
            return addBase + randomGenerator.GetRandom(randomMin, randomMax);
        }
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 目指す速度基礎値計算。レース開始前に一度だけ計算し、以降不変の値。
    /// </summary>
    /// <remarks>
    /// 「逃げ」は序盤から飛ばし気味、「追い」は序盤～中盤抑えつつ終盤以降でペース上げる、などの計算。
    /// </remarks>
    /// <seealso cref="Test.TestHorseBaseTargetSpeedCalculator"/>
    //-------------------------------------------------------------------
    public class HorseBaseTargetSpeedCalculator : IHorseBaseTargetSpeedCalculator
    {
        //---------------------------------------------------------------
        // 定義。        
        //---------------------------------------------------------------
        private class BaseSpeedInfo
        {
            public readonly Gallop.RaceDefine.HorsePhase Phase;
            private readonly float _baseTargetSpeed;
            private readonly float _baseTargetSpeedRandom;

            /// <summary>
            /// 目指す速度基礎値。
            /// </summary>
            public float BaseTargetSpeed
            {
                get { return _baseTargetSpeed + _baseTargetSpeedRandom; }
            }

            /// <summary>
            /// 目指す速度基礎値から、ランダム値を除いたもの。
            /// </summary>
            public float BaseTargetSpeedWithoutRandom
            {
                get { return _baseTargetSpeed; }
            }

            public BaseSpeedInfo(Gallop.RaceDefine.HorsePhase phase, float baseTargetSpeed, float random)
            {
                Phase = phase;
                _baseTargetSpeed = baseTargetSpeed;
                _baseTargetSpeedRandom = random;
            }
        }


        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly float _sectionChangeDistance;          // 目指す速度基礎値を、この距離ごとに計算しておく。
        private readonly BaseSpeedInfo[] _baseTargetSpeedArray; // 計算済みの目指す速度基礎値。
        private readonly float _raceBaseSpeed;                  // レース基本速度。
        private bool _isInitialized;

#if CYG_DEBUG
        public IHorseRaceInfoSimulate DbgOwnerHorse { get; set; }
#endif

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseBaseTargetSpeedCalculator(
            int sectionNum,
            float sectionChangeDistance,
            float raceBaseSpeed )
        {
            _baseTargetSpeedArray = new BaseSpeedInfo[sectionNum];
            _sectionChangeDistance = sectionChangeDistance;
            _raceBaseSpeed = raceBaseSpeed;
        }

        //---------------------------------------------------------------
        public float GetBaseTargetSpeed(float distance)
        {
            if (!_isInitialized)
            {
                Debug.LogWarning("目指す速度基礎値が初期化されていません");
                return _raceBaseSpeed;
            }

            int index = Distance2Index(distance);
            var retBaseTargetSpeed = _baseTargetSpeedArray[index];
            return retBaseTargetSpeed.BaseTargetSpeed;
        }

        //---------------------------------------------------------------
        public float GetBaseTargetSpeedWithoutRandom(Gallop.RaceDefine.HorsePhase phase)
        {
            if (!_isInitialized)
            {
                Debug.LogWarning("目指す速度基礎値が初期化されていません");
                return _raceBaseSpeed;
            }

            // 指定Phaseで最初に見つかったデータを採用する。
            // ランダム要素を含まないBaseTargetSpeedWithoutRandomの場合、
            // 同じフェーズであれば全て同じ値であるため。
            var info = _baseTargetSpeedArray.FirstOrDefault(i => i.Phase == phase);
            if(info == null)
            {
                Debug.LogWarning($"指定フェーズの目指す速度基礎値が登録されていません。phase={phase}");
                return _raceBaseSpeed;
            }

            return info.BaseTargetSpeedWithoutRandom;
        }

        //---------------------------------------------------------------
        private int Distance2Index(float distance)
        {
            int index = (int)(distance / _sectionChangeDistance);
            index = RaceUtilMath.Clamp(index, 0, _baseTargetSpeedArray.Length - 1);
            return index;
        }

        //---------------------------------------------------------------
        public void CalcBaseTargetSpeedArray(
            RacePhaseCalculator phaseCalculator,
            IRaceRandomGenerator randomGenerator,
            Gallop.RaceParamDefine.BaseTargetSpeedParam paramDefine,
            Gallop.RaceDefine.RunningStyle runningStyle,
            Gallop.RaceDefine.RunningStyleEx runningStyleEx,
            float wiz,
            float speed,
            float properDistanceCoef,
            float addSpeedParamCoef)
        {
            // 走法ごとにペース配分テーブルの参照を決める。
            Gallop.RaceParamDefine.PhaseBaseTargetSpeedPer baseTargetSpeedPer;
            if (runningStyleEx != Gallop.RaceDefine.RunningStyleEx.None)
            {
                int perIndex = (int)(runningStyleEx - 1);
                if (perIndex < 0 || perIndex >= paramDefine.PhaseBaseTargetSpeedPerExArray.Length)
                {
                    Debug.LogWarning("走法が不正です。perIndex:" + perIndex);
                    return;
                }
                baseTargetSpeedPer = paramDefine.PhaseBaseTargetSpeedPerExArray[perIndex];
            }
            else
            {
                int perIndex = (int)(runningStyle - 1);
                if (perIndex < 0 || perIndex >= paramDefine.PhaseBaseTargetSpeedPerArray.Length)
                {
                    Debug.LogWarning("走法が不正です。perIndex:" + perIndex);
                    return;
                }
                baseTargetSpeedPer = paramDefine.PhaseBaseTargetSpeedPerArray[perIndex];
            }

            // ランダム幅の最小・最大を計算。
            float randomMin = CalcMinusRandomRange(wiz, paramDefine);
            float randomMax = CalcPlusRandomRange(wiz, paramDefine);
#if CYG_DEBUG
            if (DbgOwnerHorse != null)
            {
                DbgOwnerHorse.DbgBaseTargetSpeedRandomMin = randomMin;
                DbgOwnerHorse.DbgBaseTargetSpeedRandomMax = randomMax;
            }
#endif
            
            // 終盤以降の目指す速度に加える。
            float phaseEndAdd = CalcPhaseEndAdd(speed, properDistanceCoef, paramDefine, addSpeedParamCoef);

            for (int i = 0; i < _baseTargetSpeedArray.Length; i++)
            {
                // 百分率での比率。
                float per = 0.0f;

                //---------------------------------------------------
                // 現在のフェーズでのペース配分取得。
                //---------------------------------------------------
                float distance = i * _sectionChangeDistance;
                var phase = phaseCalculator.GetPhaseByDistance(distance);
                float phaseEndAddCur = 0;
                switch (phase)
                {
                    case Gallop.RaceDefine.HorsePhase.Start: 
                        per = baseTargetSpeedPer.Start; 
                        break;
                    case Gallop.RaceDefine.HorsePhase.MiddleRun: 
                        per = baseTargetSpeedPer.Middle; 
                        break;
                    case Gallop.RaceDefine.HorsePhase.End: 
                        per = baseTargetSpeedPer.End;
                        phaseEndAddCur = phaseEndAdd; // 終盤以降目指す速度に加算する。
                        break;
                    case Gallop.RaceDefine.HorsePhase.LastSpurt: 
                        per = baseTargetSpeedPer.Last; 
                        phaseEndAddCur = phaseEndAdd; // 終盤以降目指す速度に加算する。
                        break;
                }

                //---------------------------------------------------
                // 賢さに応じてペース配分にランダム値を加える。
                //---------------------------------------------------
                float randomPer = CalcRandom(
                    randomGenerator,
                    randomMin, 
                    randomMax);

                //---------------------------------------------------
                // RaceBaseSpeedにペース配分(0.0 ... 1.0)を乗算した速度が各区分における狙いたい速度である。
                //---------------------------------------------------
                _baseTargetSpeedArray[i] = new BaseSpeedInfo(
                    phase,
                    _raceBaseSpeed * (per * 0.01f) + phaseEndAddCur,
                    _raceBaseSpeed * (randomPer * 0.01f));
            }

            _isInitialized = true;
        }

        /// <summary>
        /// 終盤以降の目指す速度に加算する値を計算。
        /// </summary>
        private float CalcPhaseEndAdd(float speed, float properDistance, Gallop.RaceParamDefine.BaseTargetSpeedParam param, float addSpeedParamCoef)
        {
            float add = (float)Math.Sqrt(speed * param.PhaseEndBaseTargetSpeedCoef) * properDistance * addSpeedParamCoef;
            return add;
        }

        /// <summary>
        /// ペース配分に含めるランダム値を計算。
        /// </summary>
        /// <returns>百分率でのランダム値を返却。</returns>
        private float CalcRandom(
            IRaceRandomGenerator randomGenerator, 
            float randomMin,
            float randomMax)
        {
        #if CYG_DEBUG
            // デバッグ機能：AI機能ON/OFF：ペース配分のランダム。
            if (!RaceSimulateDebugger.IsEnableAICalcPhaseHpRandom)
            {
                return 0;
            }
        #endif
            return randomGenerator.GetRandom(randomMin, randomMax);
        }

        private static float CalcMinusRandomRange(float wiz, Gallop.RaceParamDefine.BaseTargetSpeedParam param)
        {
            return param.BaseTargetSpeedRandomMinusVal1 + (wiz / param.BaseTargetSpeedRandomMinusVal2) * (float)Math.Log10(wiz * 0.1f);
        }
        private static float CalcPlusRandomRange(float wiz, Gallop.RaceParamDefine.BaseTargetSpeedParam param)
        {
            return (wiz / param.BaseTargetSpeedRandomPlusVal1) * (float)Math.Log10(wiz * 0.1f);
        }
    }
}

#endif

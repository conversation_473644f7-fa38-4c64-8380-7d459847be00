namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 目指す速度基礎値計算Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseBaseTargetSpeedCalculatorNull : IHorseBaseTargetSpeedCalculator
    {
        public float GetBaseTargetSpeed(float distance)
        {
            return 0;
        }

        //---------------------------------------------------------------
        public float GetBaseTargetSpeedWithoutRandom(Gallop.RaceDefine.HorsePhase phase)
        {
            return 0;
        }
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        //---------------------------------------------------------------
        public void CalcBaseTargetSpeedArray(
            RacePhaseCalculator phaseCalculator,
            IRaceRandomGenerator randomGenerator,
            Gallop.RaceParamDefine.BaseTargetSpeedParam paramDefine,
            Gallop.RaceDefine.RunningStyle runningStyle,
            Gallop.RaceDefine.RunningStyleEx runningStyleEx,
            float wiz,
            float speed,
            float properDistanceCoef,
            float addSpeedParamCoef)
        {
        }
        
    #if CYG_DEBUG
        public IHorseRaceInfoSimulate DbgOwnerHorse { get; set; } = null;
    #endif
#endif
    }
}

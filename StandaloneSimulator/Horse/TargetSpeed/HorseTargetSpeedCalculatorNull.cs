#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 最終的な目指す速度計算：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed partial class HorseTargetSpeedCalculatorNull : IHorseTargetSpeedCalculator
    {
        private IHorseRaceAI _ownerAI;
        
        public HorseTargetSpeedCalculatorNull(IHorseRaceAI ownerAI)
        {
            _ownerAI = ownerAI;
        }
    }
}
#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 最終的な目指す速度計算：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed partial class HorseTargetSpeedCalculatorNull : IHorseTargetSpeedCalculator
    {
        // リプレイ用
        public HorseTargetSpeedCalculatorNull()
        {
            
        }
        
        /// <summary> 目指す速度の基礎値 </summary>
        /// <remarks> シミュレートでしか使用しないのでNullオブジェクトでは0fを返す </remarks>
        public float BaseTargetSpeed { get { return 0f; } }

        public float TargetSpeed
        {
            get { return BaseTargetSpeed; }
        }

        public void Update(float deltaTime)
        {
        }
    }
}

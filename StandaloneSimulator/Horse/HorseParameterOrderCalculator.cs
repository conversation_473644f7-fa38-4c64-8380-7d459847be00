using System;
using System.Linq;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ステータスの順位計算機。
    /// </summary>
    /// <seealso cref="Test.TestHorseParameterOrderCalculator"/>
    //-------------------------------------------------------------------
    public static partial class HorseParameterOrderCalculator
    {
        /// <summary>
        /// 根性順位計算。
        /// </summary>
        public static void CalcGutsOrder(IHorseRaceInfoSimulate[] allHorseArray)
        {
            ApplyOrder(allHorseArray, (h) => h.Guts, (h, order) => h.GutsOrder = order);
        }
        
        /// <summary>
        /// 賢さ順位計算。
        /// </summary>
        public static void CalcWizOrder(IHorseRaceInfoSimulate[] allHorseArray)
        {
            ApplyOrder(allHorseArray, (h) => h.Wiz, (h, order) => h.WizOrder = order);
        }
        
        //---------------------------------------------------------------
        private static void ApplyOrder(
            IHorseRaceInfoSimulate[] allHorseArray, 
            Func<IHorseRaceInfoSimulate, float> getter, 
            Action<IHorseRaceInfoSimulate, int> orderSetter)
        {
            // 配列の中身ソートするため、ワークに退避する。
            var sortTmpHorseList = allHorseArray.ToList();
            
            // ステータスで昇順にソート。
            sortTmpHorseList.Sort((a, b) =>
            {
                // ソートキーとなるステータス。
                float statusA = getter(a);
                float statusB = getter(b);
            
                // ステータス同一の場合、馬番の若いほうが上位となる。※同一順位にはしない。
                if(RaceUtilMath.Approximately(statusA, statusB))
                {
                    return a.HorseIndex - b.HorseIndex;
                }

                return statusA > statusB ? -1 : 1;
            });

            // 順位を振っていく。
            for (int i = 0; i < sortTmpHorseList.Count; ++i)
            {
                orderSetter(sortTmpHorseList[i], i);
            }
        }
    }
}
#endif

#if GALLOP
namespace StandaloneSimulator
{
    public static partial class HorseParameterOrderCalculator
    {
        /// <summary>
        /// 根性順位計算。
        /// </summary>
        public static void CalcGutsOrder(Gallop.IHorseRaceInfo[] allHorseArray)
        {
            ApplyOrder(allHorseArray, (h) => h.Guts, (h, order) => h.GutsOrder = order);
        }
        
        /// <summary>
        /// 賢さ順位計算。
        /// </summary>
        public static void CalcWizOrder(Gallop.IHorseRaceInfo[] allHorseArray)
        {
            ApplyOrder(allHorseArray, (h) => h.Wiz, (h, order) => h.WizOrder = order);
        }
        
        //---------------------------------------------------------------
        private static void ApplyOrder(
            Gallop.IHorseRaceInfo[] allHorseArray, 
            Func<Gallop.IHorseRaceInfo, float> getter, 
            Action<Gallop.IHorseRaceInfo, int> orderSetter)
        {
            // 配列の中身ソートするため、ワークに退避する。
            var sortTmpHorseList = allHorseArray.ToList();
            
            // ステータスで昇順にソート。
            sortTmpHorseList.Sort((a, b) =>
            {
                // ソートキーとなるステータス。
                float statusA = getter(a);
                float statusB = getter(b);
            
                // ステータス同一の場合、馬番の若いほうが上位となる。※同一順位にはしない。
                if(RaceUtilMath.Approximately(statusA, statusB))
                {
                    return a.HorseIndex - b.HorseIndex;
                }

                return statusA > statusB ? -1 : 1;
            });

            // 順位を振っていく。
            for (int i = 0; i < sortTmpHorseList.Count; ++i)
            {
                orderSetter(sortTmpHorseList[i], i);
            }
        }
    }
}
#endif

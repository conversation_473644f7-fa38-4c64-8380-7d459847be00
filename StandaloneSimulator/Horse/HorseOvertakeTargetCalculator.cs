using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの追い越し対象計算処理。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseOvertakeTargetCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public List<Gallop.AroundHorse> OverTakeHorseList { get; private set; }
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        private readonly IHorseRaceInfoSimulate _ownerHorseSimulate;
#endif
#if GALLOP
        private readonly Gallop.IHorseRaceInfo _ownerHorse;
#endif
        private readonly IHorseRaceAI _ownerAI;
        private readonly Gallop.RaceParamDefine _paramDefine;


        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public HorseOvertakeTargetCalculator(
            IHorseRaceInfoSimulate ownerHorseSimulate,
            IHorseRaceAI ownerAI, 
            Gallop.RaceParamDefine paramDefine)
        {
            OverTakeHorseList = new List<Gallop.AroundHorse>(Gallop.RaceDefine.RACE_HORSE_MAX);

            _ownerHorseSimulate = ownerHorseSimulate;
            _ownerAI = ownerAI;
            _paramDefine = paramDefine;
        }
#endif
#if GALLOP
        public HorseOvertakeTargetCalculator(
            Gallop.IHorseRaceInfo ownerHorse,
            IHorseRaceAI ownerAI,
            Gallop.RaceParamDefine paramDefine)
        {
            OverTakeHorseList = new List<Gallop.AroundHorse>(Gallop.RaceDefine.RACE_HORSE_MAX);

            _ownerHorse = ownerHorse;
            _ownerAI = ownerAI;
            _paramDefine = paramDefine;
        }
#endif
        /// <summary>
        /// 追い越し対象を保持しているかどうか。
        /// </summary>
        public bool HasOverTakeTarget()
        {
            return OverTakeHorseList.Count > 0;
        }
        
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// 追い越し対象の収集。
        /// </summary>
        public void CollectOvertakeTargetSimulate()
        {
            // 視野範囲内のキャラの中で追い越し対象のキャラをリストに加える。
            // その後、追い越し対象から外れたキャラをRemoveAllでリストから外す。
            // ※一度視野範囲に入って追い越し対象となってから、追い越し処理中に視野から外れても、追い越し対象として維持し続けるため。
            for (int i = 0; i < _ownerAI.VisibleHorseCount; ++i)
            {
                var horse = _ownerAI.VisibleHorses[i];
                if (!CheckOvertakeTargetSimulate(horse))
                {
                    continue;
                }

                if (OverTakeHorseList.Contains(horse))
                {
                    continue;
                }

                OverTakeHorseList.Add(horse);
            }

            OverTakeHorseList.RemoveAll(h => !CheckOvertakeTargetSimulate(h));

            // 自分の前をブロックしているキャラも、回避のために追い越し対象にする。
            // ※自分をブロックしている馬は自分より速度が速いので、ここで強制的にリストに追加する必要がある。
            var frontBlock = _ownerAI.GetBlockHorse();
            if (frontBlock != null)
            {
                if (!OverTakeHorseList.Contains(frontBlock))
                {
                    OverTakeHorseList.Add(frontBlock);
                }
            }
        }
        
        /// <summary>
        /// 指定キャラを追い越し対象とできるかどうか。
        /// </summary>
        private bool CheckOvertakeTargetSimulate(Gallop.AroundHorse frontHorse)
        {
            // 相手より一定距離前に出たら、その相手は追い越し対象から外す。
            if (frontHorse.distanceGap < -Gallop.RaceDefine.OVERTAKE_END_DISTANCEGAP)
            {
                return false;
            }

            float selfTargetSpeedWithSkill = _ownerHorseSimulate.GetTargetSpeedWithSkill();
            float frontHorseTargetSpeedWithSkill = frontHorse.infoSimulate.GetTargetSpeedWithSkill();
            float frontHorseLastSpeed = frontHorse.infoSimulate.GetLastSpeed();

            //-----------------------------------------------------------
            // チェック対象が、自分の前方をブロックしている時。
            //-----------------------------------------------------------
            if (_ownerHorseSimulate.GetBlockHorseFront() == frontHorse.infoSimulate)
            {
                // 相手より自分が早く走ろうとしていないなら追い越し不要。
                if (frontHorseLastSpeed >= selfTargetSpeedWithSkill)
                {
                    return false;
                }
            }
            //-----------------------------------------------------------
            // チェック対象が、自分の前方をブロックしていない。
            //-----------------------------------------------------------
            else
            {
                const float OVERTAKE_DISTANCEGAP_MAX = 20.0f;
                const float OVERTAKE_DISTANCEGAP_MIN = 1.0f;

                // 相手が近すぎるか、遠すぎるなら追い越し不要。
                if (frontHorse.distanceGap < OVERTAKE_DISTANCEGAP_MIN ||
                    frontHorse.distanceGap > OVERTAKE_DISTANCEGAP_MAX)
                {
                    return false;
                }

                // 自分の方が遅いなら追い越し不要。
                if (frontHorse.speedGap < 0)
                {
                    return false;
                }

                float distGapPerSpeedGap = frontHorse.distanceGap / frontHorse.speedGap;
                if (distGapPerSpeedGap > _paramDefine.overTakeDistPerSpeed)
                {
                    return false;
                }

                if (null != frontHorse.infoSimulate.GetBlockHorseFront())
                {
                    // 相手が前方ブロックされている場合、相手は現在速度以上出せない。
                    // 相手の現在速度を、自分の【狙いたい速度＋スキルによる現在速度加算】が上回っていたら追い越し対象とする。
                    if (frontHorseLastSpeed >= selfTargetSpeedWithSkill)
                    {
                        return false;
                    }
                }
                else
                {
                    // 相手が前方ブロックされていない場合、相手は狙いたい速度まで速度を上げる可能性が高い。
                    // 相手の狙いたい速度を、自分の【狙いたい速度＋スキルによる現在速度加算】が上回っていたら追い越し対象とする。
                    if (frontHorseTargetSpeedWithSkill >= selfTargetSpeedWithSkill)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
#endif
#if GALLOP
        /// <summary>
        /// 追い越し対象の収集。
        /// </summary>
        public void CollectOvertakeTarget()
        {
            // 視野範囲内のキャラの中で追い越し対象のキャラをリストに加える。
            // その後、追い越し対象から外れたキャラをRemoveAllでリストから外す。
            // ※一度視野範囲に入って追い越し対象となってから、追い越し処理中に視野から外れても、追い越し対象として維持し続けるため。
            for (int i = 0; i < _ownerAI.VisibleHorseCount; ++i)
            {
                var horse = _ownerAI.VisibleHorses[i];
                if (!CheckOvertakeTarget(horse))
                {
                    continue;
                }

                if (OverTakeHorseList.Contains(horse))
                {
                    continue;
                }

                OverTakeHorseList.Add(horse);
            }

            OverTakeHorseList.RemoveAll(h => !CheckOvertakeTarget(h));

            // 自分の前をブロックしているキャラも、回避のために追い越し対象にする。
            // ※自分をブロックしている馬は自分より速度が速いので、ここで強制的にリストに追加する必要がある。
            var frontBlock = _ownerAI.GetBlockHorse();
            if (frontBlock != null)
            {
                if (!OverTakeHorseList.Contains(frontBlock))
                {
                    OverTakeHorseList.Add(frontBlock);
                }
            }
        }
        
        /// <summary>
        /// 指定キャラを追い越し対象とできるかどうか。
        /// </summary>
        private bool CheckOvertakeTarget(Gallop.AroundHorse frontHorse)
        {
            // 相手より一定距離前に出たら、その相手は追い越し対象から外す。
            if (frontHorse.distanceGap < -Gallop.RaceDefine.OVERTAKE_END_DISTANCEGAP)
            {
                return false;
            }

            float selfTargetSpeedWithSkill = _ownerHorse.GetTargetSpeedWithSkill();
            float frontHorseTargetSpeedWithSkill = frontHorse.info.GetTargetSpeedWithSkill();
            float frontHorseLastSpeed = frontHorse.info.GetLastSpeed();

            //-----------------------------------------------------------
            // チェック対象が、自分の前方をブロックしている時。
            //-----------------------------------------------------------
            if (_ownerHorse.GetBlockHorseFront() == frontHorse.info)
            {
                // 相手より自分が早く走ろうとしていないなら追い越し不要。
                if (frontHorseLastSpeed >= selfTargetSpeedWithSkill)
                {
                    return false;
                }
            }
            //-----------------------------------------------------------
            // チェック対象が、自分の前方をブロックしていない。
            //-----------------------------------------------------------
            else
            {
                const float OVERTAKE_DISTANCEGAP_MAX = 20.0f;
                const float OVERTAKE_DISTANCEGAP_MIN = 1.0f;

                // 相手が近すぎるか、遠すぎるなら追い越し不要。
                if (frontHorse.distanceGap < OVERTAKE_DISTANCEGAP_MIN ||
                    frontHorse.distanceGap > OVERTAKE_DISTANCEGAP_MAX)
                {
                    return false;
                }

                // 自分の方が遅いなら追い越し不要。
                if (frontHorse.speedGap < 0)
                {
                    return false;
                }

                float distGapPerSpeedGap = frontHorse.distanceGap / frontHorse.speedGap;
                if (distGapPerSpeedGap > _paramDefine.overTakeDistPerSpeed)
                {
                    return false;
                }

                if (null != frontHorse.info.GetBlockHorseFront())
                {
                    // 相手が前方ブロックされている場合、相手は現在速度以上出せない。
                    // 相手の現在速度を、自分の【狙いたい速度＋スキルによる現在速度加算】が上回っていたら追い越し対象とする。
                    if (frontHorseLastSpeed >= selfTargetSpeedWithSkill)
                    {
                        return false;
                    }
                }
                else
                {
                    // 相手が前方ブロックされていない場合、相手は狙いたい速度まで速度を上げる可能性が高い。
                    // 相手の狙いたい速度を、自分の【狙いたい速度＋スキルによる現在速度加算】が上回っていたら追い越し対象とする。
                    if (frontHorseTargetSpeedWithSkill >= selfTargetSpeedWithSkill)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
#endif
    }
}


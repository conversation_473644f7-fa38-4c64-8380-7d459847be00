namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース開始時のレーン計算機。
    /// </summary>
    /// <seealso cref="Test.TestHorseInitialLaneCalculator"/>
    //-------------------------------------------------------------------
    public static class HorseInitialLaneCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>
        /// 初期レーン計算方法。
        /// </summary>
        public enum InitialLaneType
        {
            ExtraSpaceAfter9 = 1,        // 9番と10番の間にゲートの境目があるので余分にずらす。
            Equidistant,                 // 1番~20番まで等間隔。
            ExtraSpaceAfter14,           // 14番と15番の間にゲートの境目があるので余分にずらす。
            ExtraSpaceAfter8,            // 8番と9番の間にゲートの境目があるので余分にずらす。
        }

        private const int LANE_EXTRA_SPACE_AFTER_8_INDEX = 7;
        public const float LANE_EXTRA_SPACE_AFTER_8 = 0.6f / 18.0f;    // スタートゲートの8番と9番間の距離をlaneDistanceに換算

        private const int LANE_EXTRA_SPACE_AFTER_9_INDEX = 8;
        public const float LANE_EXTRA_SPACE_AFTER_9 = 0.6f / 18.0f;    // スタートゲートの９番と１０番間の距離をlaneDistanceに換算

        private const int LANE_EXTRA_SPACE_AFTER_14_INDEX = 13;
        public const float LANE_EXTRA_SPACE_AFTER_14 = 1.86f / 18.0f;  // スタートゲートの１４番と１５番間の距離をlaneDistanceに換算

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// 初期レーン計算。
        /// </summary>
        /// <param name="horseIndex"> 馬番 </param>
        /// <param name="type"> 初期レーン計算方法 </param>
        /// <param name="isHalfGate"> ハーフゲートかどうか </param>
        /// <param name="maxLaneDistance"> 最大レーン距離 </param>
        /// <param name="horseNum"> 出走人数 </param>
        /// <param name="isOuterPlaceRace"> 外埒側から詰めるかどうか </param>
        /// <returns></returns>
        public static float CalcInitialLane(int horseIndex, InitialLaneType type, bool isHalfGate, float maxLaneDistance, int horseNum, bool isOuterPlaceRace)
        {
            float lane = horseIndex * Gallop.RaceDefine.HorseLane_One;

            if (isOuterPlaceRace) {
                // 馬番の大きいキャラから外埒(maxLaneDistance)から順に配置する
                lane = maxLaneDistance - (horseNum - horseIndex) * Gallop.RaceDefine.HorseLane_One;
            }

            // 9番と10番の間にゲートとゲートの境目があるので、余分にずらす。
            switch (type)
            {
                // フルゲートの場合、8番と9番の間にゲートとゲートの境目があるので、余分にずらす。
                case InitialLaneType.ExtraSpaceAfter8:
                    if (!isHalfGate && horseIndex > LANE_EXTRA_SPACE_AFTER_8_INDEX)
                    {
                        lane += LANE_EXTRA_SPACE_AFTER_8;
                    }

                    break;

                // 9番と10番の間にゲートとゲートの境目があるので、余分にずらす。
                case InitialLaneType.ExtraSpaceAfter9:
                    if (isOuterPlaceRace)
                    {
                        // 外埒から配置しているので外埒側から数えて9番目以降のキャラを内埒側にオフセット分ずらす
                        // horseIndexで比較しているので(horseNum - 1)して最大の馬番から数える必要がある
                        if (horseIndex < (horseNum - 1) - LANE_EXTRA_SPACE_AFTER_9_INDEX)
                        {
                            lane -= LANE_EXTRA_SPACE_AFTER_9;
                        }
                    }
                    else
                    {
                        if (horseIndex > LANE_EXTRA_SPACE_AFTER_9_INDEX)
                        {
                            lane += LANE_EXTRA_SPACE_AFTER_9;
                        }
                    }
                    break;

                // 14番と15番の間にゲートとゲートの境目があるので、余分にずらす。
                case InitialLaneType.ExtraSpaceAfter14:
                    if (horseIndex > LANE_EXTRA_SPACE_AFTER_14_INDEX)
                    {
                        lane += LANE_EXTRA_SPACE_AFTER_14;
                    }
                    break;

            }
            return lane;
        }
    }
}
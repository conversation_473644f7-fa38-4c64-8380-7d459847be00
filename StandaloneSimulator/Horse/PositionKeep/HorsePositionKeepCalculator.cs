#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorsePositionKeepCalculator : IHorsePositionKeepCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly IHorseRaceInfoSimulate _ownerHorse = null;
        private readonly float _checkStartDistance;
        private readonly float _checkEndDistance;
        private readonly int _courseDistance;
        private readonly Gallop.RaceParamDefine.PositionKeepParam _keepParam;
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly IRaceRandomGenerator _randomGenerator;
        private /*readonly*/ IPositionKeepModeCalculator _keepModeCalc;
        private readonly IHorsePaseMakerCalculator _paseMakerCalculator;
        private bool _isTemptationPrev;

        private IPositionKeepStrategy _curStrategy;
        private IPositionKeepStrategy[] _strategyArray;

        public bool IsPositionKeepSection
        {
            get
            {
                return _ownerHorse.GetDistance() >= _checkStartDistance && _ownerHorse.GetDistance() < _checkEndDistance;
            }
        }
        
        public bool IsPositionKeep => PositionKeepMode != Gallop.RaceDefine.PositionKeepMode.Null;
        public Gallop.RaceDefine.PositionKeepMode PositionKeepMode => _curStrategy.PositionKeepMode;
        public float BaseTargetSpeedMultiply => _curStrategy.BaseTargetSpeedMultiply; 

        private float _coolDownSec;
        private float _checkStartIntervalSec;


        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorsePositionKeepCalculator(
            IHorseRaceInfoSimulate owner,
            int courseDistance,
            int sectionNum,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator,
            IHorsePaseMakerCalculator paseMakerCalculator)
        {
            _ownerHorse = owner;
            _keepParam = keepParam;
            _horseAccessor = horseAccessor;
            _randomGenerator = randomGenerator;
            _courseDistance = courseDistance;
            _paseMakerCalculator = paseMakerCalculator;

            // 発動チェックを行う開始・終了区間。
            float sectionDistance = courseDistance / sectionNum;
            _checkStartDistance = sectionDistance * (_keepParam.PositionKeepStartSection - 1); // 1始まりで入力されているため-1。
            _checkEndDistance = sectionDistance * _keepParam.PositionKeepEndSection;

            // 発動するポジション維持モード計算機の生成。
            InitKeepModeCalcByDefault();

            InitStrategy(courseDistance, sectionNum);
        }

        private void InitKeepModeCalcByDefault()
        {
            _keepModeCalc = CreateKeepModeCalculator(_ownerHorse.RunningStyle, false);
        }

        private void InitKeepModeCalcByTemptation(Gallop.RaceDefine.RunningStyle style)
        {
            _keepModeCalc = CreateKeepModeCalculator(style, true);
        }

        /// <summary>
        /// ポジション維持機能実処理群の初期化。
        /// </summary>
        private void InitStrategy(int courseDistance, int sectionNum)
        {
            _strategyArray = new IPositionKeepStrategy[RaceUtilEnum.GetEnumElementCount<Gallop.RaceDefine.PositionKeepMode>()];
            _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.Null] = new PositionKeepStrategyNull();
            _strategyArray[(int) Gallop.RaceDefine.PositionKeepMode.SpeedUp] = new PositionKeepStrategySpeedUp(
                _ownerHorse,
                _keepParam,
                _randomGenerator,
                _horseAccessor,
                courseDistance,
                sectionNum,
                _ownerHorse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige
                    ? _keepParam.PositionKeepSpeedUpBaseTargetSpeedMultiplyOonige
                    : _keepParam.PositionKeepSpeedUpBaseTargetSpeedMultiply,
                _paseMakerCalculator);
            _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.PaseUp] = new PositionKeepStrategyPaseUp(
                _ownerHorse,
                _keepParam,
                _randomGenerator,
                _horseAccessor,
                courseDistance,
                sectionNum,
                _keepParam.PositionKeepPaseUpBaseTargetSpeedMultiply,
                _paseMakerCalculator);
            _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.PaseUpEx] = new PositionKeepStrategyPaseUpEx(
                _ownerHorse,
                _keepParam,
                _randomGenerator,
                _horseAccessor,
                courseDistance,
                sectionNum,
                _keepParam.PositionKeepPaseUpExBaseTargetSpeedMultiply,
                _paseMakerCalculator);
            _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.PaseDown] = new PositionKeepStrategyPaseDown(
                _ownerHorse,
                _keepParam,
                _randomGenerator,
                _horseAccessor,
                courseDistance,
                sectionNum,
                _paseMakerCalculator);
            _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.Overtake] = new PositionKeepStrategyOvertake(
                _ownerHorse,
                _keepParam,
                _randomGenerator,
                courseDistance,
                sectionNum,
                _ownerHorse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige 
                    ? _keepParam.PositionKeepOvertakeBaseTargetSpeedMultiplyOonige
                    : _keepParam.PositionKeepOvertakeBaseTargetSpeedMultiply,
                _horseAccessor);

            _curStrategy = _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.Null];
        }

        public void Update(float deltaTime)
        {
            CheckTemptation();

            if (IsPositionKeep)
            {
                _curStrategy.Update(deltaTime);
                CheckEnd(deltaTime);
            }
            else
            {
                CheckStart(deltaTime);
            }
            
        #if CYG_DEBUG
            DbgUpdateDistanceDiff();
        #endif
        }

#if CYG_DEBUG
        private void DbgUpdateDistanceDiff()
        {
            float ownerDistance = _ownerHorse.GetDistance();
            
            _ownerHorse.DbgDistanceDiffOnPositionKeepCalcArray = new float[_horseAccessor.GetHorseNumber()];
            for (int i = 0; i < _ownerHorse.DbgDistanceDiffOnPositionKeepCalcArray.Length; i++)
            {
                var horse = _horseAccessor.GetHorseInfo(i);
                _ownerHorse.DbgDistanceDiffOnPositionKeepCalcArray[i] = horse.GetDistance() - ownerDistance;
            }
        }
#endif

        /// <summary>
        /// ポジション維持機能開始更新。
        /// </summary>
        private void CheckStart(float deltaTime)
        {
            // 発動する区間ではない。
            if (!IsPositionKeepSection)
            {
                return;
            }

            // 前回発動後のクールダウン中なら処理不要。
            if (UpdateCoolDown(deltaTime))
            {
                return;
            }

            // 前回の開始チェックから一定時間経過していなければ処理不要。
            if(UpdateCheckStartInterval(deltaTime))
            {
                return;
            }
            _checkStartIntervalSec = _keepParam.PositionKeepCheckIntervalSec;

            // ポジション維持のどの機能を発動するか選定、そしてその機能が発動できるかを試みる。
            var strategy = GetStrategy(_keepModeCalc.CalcKeepMode());
            if (strategy.TryStart(!_ownerHorse.IsTemptation))
            {
                // 実処理切替。
                _curStrategy = strategy;

                // ポジション維持を発動した回数カウント。
                ++_ownerHorse.PositionKeepCount;
            }
        }

        /// <summary>
        /// 開始更新のチェック間隔時間更新。
        /// </summary>
        /// <param name="deltaTime"></param>
        /// <returns>まだ前回チェックから一定時間経過していなければtrue。</returns>
        private bool UpdateCheckStartInterval(float deltaTime)
        {
            if (_checkStartIntervalSec > 0)
            {
                _checkStartIntervalSec -= deltaTime;
            }
            return _checkStartIntervalSec > 0;
        }

        /// <summary>
        /// クールダウン時間更新。
        /// </summary>
        /// <returns>まだクールダウンが残っているならtrue。</returns>
        private bool UpdateCoolDown(float deltaTime)
        {
            if(_coolDownSec > 0)
            {
                _coolDownSec -= deltaTime;
            }
            return _coolDownSec > 0;
        }

        /// <summary>
        /// ポジション維持機能終了チェック。
        /// </summary>
        private void CheckEnd(float deltaTime)
        {
            // 発動中の機能が終了条件を満たしたら終了。
            if (_curStrategy.CheckEnd(deltaTime))
            {
                Clear();
                return;
            }
        }

        /// <summary>
        /// ポジション維持機能終了。
        /// </summary>
        private void Clear()
        {
            _curStrategy.Clear();
            _curStrategy = _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.Null];
            _coolDownSec = _keepParam.PositionKeepCoolDownSec;
        }

        /// <summary>
        /// ポジション維持モード計算機生成。
        /// </summary>
        /// <returns></returns>
        private IPositionKeepModeCalculator CreateKeepModeCalculator(Gallop.RaceDefine.RunningStyle style, bool isForceSpeedUp)
        {
            switch (style)
            {
                case Gallop.RaceDefine.RunningStyle.Nige:
                    return new PositionKeepModeCalculatorNige(
                        _ownerHorse,
                        isForceSpeedUp,
                        _horseAccessor);

                default:
                    return new PositionKeepModeCalculatorPaseAdjust(
                        _keepParam, 
                        _courseDistance, 
                        _ownerHorse,
                        style,
                        _horseAccessor,
                        _paseMakerCalculator);
            }
        }

        /// <summary>
        /// ポジション維持機能実処理オブジェクト取得。
        /// </summary>
        private IPositionKeepStrategy GetStrategy(Gallop.RaceDefine.PositionKeepMode mode)
        {
            if (mode < 0 || (int)mode >= _strategyArray.Length)
            {
                return _strategyArray[(int)Gallop.RaceDefine.PositionKeepMode.Null];
            }
            return _strategyArray[(int)mode];
        }

        /// <summary>
        /// 走法の変更をStragety群に通知。
        /// </summary>
        /// <param name="style"></param>
        private void NotifyChangeRunningStyle(Gallop.RaceDefine.RunningStyle style)
        {
            foreach (var strategy in _strategyArray)
            {
                strategy.ChangeRunningStyle(style);
            }
        }

        /// <summary>
        /// 掛かりON/OFFによるポジション維持モードの上書き更新。
        /// </summary>
        private void CheckTemptation()
        {
            // 掛かりのON/OFFが行われていなければ処理不要。
            if (_ownerHorse.IsTemptation == _isTemptationPrev)
            {
                return;
            }
            _isTemptationPrev = _ownerHorse.IsTemptation;

            // 今のポジ維持終了。
            Clear();

            // クールダウンやチェック間隔リセット。
            _coolDownSec = 0;
            _checkStartIntervalSec = 0;

            // 掛かりONになったら掛かりモードによって別の走法のペース配分になったり、加速したり。
            if (_ownerHorse.IsTemptation)
            {
                // 現在の掛かりモードによって採らされる別の走法。
                var temptationRunningStyle = GetTemptationRunningStyle();

                // 新しい走法を元に、ポジション維持モード選定モジュールの選び直し。
                InitKeepModeCalcByTemptation(temptationRunningStyle);
                // Strategy群に新しい走法通知。
                NotifyChangeRunningStyle(temptationRunningStyle);
            }
            // 掛かりOFFになったらモード切替。
            else
            {
                // 本来の自分の走法を元に、ポジション維持モード選定モジュールの選び直し。
                InitKeepModeCalcByDefault();
                // Strategy群に本来の自分の走法への切り替え通知。
                NotifyChangeRunningStyle(_ownerHorse.RunningStyle);
            }
        }

        private Gallop.RaceDefine.RunningStyle GetTemptationRunningStyle()
        {
            // 掛かりモードによって、変更後の走法が決まる。
            switch (_ownerHorse.TemptationMode)
            {
                case TemptationMode.Boost:
                    return Gallop.RaceDefine.RunningStyle.Nige;
                case TemptationMode.PositionNige:
                    return Gallop.RaceDefine.RunningStyle.Nige;
                case TemptationMode.PositionSenko:
                    return Gallop.RaceDefine.RunningStyle.Senko;
                case TemptationMode.PositionSashi:
                    return Gallop.RaceDefine.RunningStyle.Sashi;
                default:
                    Debug.LogWarning("予期しない掛かりモードです。mode=" + _ownerHorse.TemptationMode);
                    return Gallop.RaceDefine.RunningStyle.Nige;
            }
        }

#if CYG_DEBUG
        public float DbgCurDistanceDiffFromPaseMaker { get { return _curStrategy.DbgCurDistanceDiffFromPaseMaker; } }
        public float DbgDistanceDiffMin { get { return _curStrategy.DbgDistanceDiffMin; } }
        public float DbgDistanceDiffMax { get { return _curStrategy.DbgDistanceDiffMax; } }
        public float DbgAdjustTargetDistanceDiff { get { return _curStrategy.DbgAdjustTargetDistanceDiff; } }
        public float DbgCoolDownTime => _coolDownSec;
#endif
    }
}
#endif

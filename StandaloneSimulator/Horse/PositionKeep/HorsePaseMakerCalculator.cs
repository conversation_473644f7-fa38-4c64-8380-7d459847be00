namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持のペースメーカー計算機インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorsePaseMakerCalculator
    {
        void Update();
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        IHorseRaceInfoSimulate PaseMaker { get; }
#endif
#if CYG_DEBUG
        int DbgTopHorseNotMostForwardRunningStyleCnt { get; }
#endif
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持のペースメーカー計算機Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorsePaseMakerCalculatorNull : IHorsePaseMakerCalculator
    {
        public HorsePaseMakerCalculatorNull() { }   // リプレイ用nullオブジェクトの場合のみ許容（アクセスされないので）
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public HorsePaseMakerCalculatorNull(IRaceHorseAccessor horseAccessor) { _horseAccessor = horseAccessor; }
        public IHorseRaceInfoSimulate PaseMaker
        {
            get
            {
                // nullアクセスを防止するため、適当に先頭のキャラを代入しておく。
                if (_defaultPaseMaker == null)
                {
                    _defaultPaseMaker = _horseAccessor.GetHorseInfo(0);
                }

                return _defaultPaseMaker;
            }
        }
        private readonly IRaceHorseAccessor _horseAccessor;
        private IHorseRaceInfoSimulate _defaultPaseMaker;
#endif
        public void Update() {}
#if CYG_DEBUG
        public int DbgTopHorseNotMostForwardRunningStyleCnt { get; }
#endif
    }
    
#if STANDALONE_SIMULATOR || UNITY_EDITOR
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持のペースメーカー計算機。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorsePaseMakerCalculator : IHorsePaseMakerCalculator
    {
        /// <summary>レース中キャラへのアクセッサ。</summary>
        private readonly IRaceHorseAccessor _horseAccessor;
        /// <summary>ParamDefine</summary>
        private readonly Gallop.RaceParamDefine.PositionKeepParam _positionKeepParam;
        /// <summary>最後にペースメーカー変更チェックをした区間。</summary>
        private int _lastCalculatedSection;
        /// <summary>ペースメーカーの初期値済みかどうか。</summary>
        private bool _isPaseMakerInitialized;
        /// <summary>先頭キャラが最も前の走法ではなかった回数。ペースメーカー変更チェックのタイミングでカウントする。</summary>
        private int _topHorseNotMostForwardRunningStyleCnt;

        private IHorseRaceInfoSimulate _paseMaker;

        public IHorseRaceInfoSimulate PaseMaker
        {
            get
            {
                Debug.Assert(_isPaseMakerInitialized, "初期化前に_paseMakerにアクセスしている"); 
                return _paseMaker;
            }
            private set
            {
                _paseMaker = value;
            }
        }

#if CYG_DEBUG
        public int DbgTopHorseNotMostForwardRunningStyleCnt => _topHorseNotMostForwardRunningStyleCnt;
#endif
        
        public HorsePaseMakerCalculator(IRaceHorseAccessor horseAccessor, Gallop.RaceParamDefine.PositionKeepParam positionKeepParam)
        {
            _horseAccessor = horseAccessor;
            _positionKeepParam = positionKeepParam;
            _lastCalculatedSection = -1;
            _isPaseMakerInitialized = false;
        }

        /// <summary>
        /// 更新。
        /// </summary>
        public void Update()
        {
            if (!CheckCalcSection())
            {
                // 現在のペースメーカーより前に、前の走法のキャラがいるなら、それを新しいペースメーカーにする。
                // これはCheckCalcSectionの条件を満たしていないフレームは毎回実行する。
                ChangePaseMakerIfForwardRunningStyleExist();
                return;
            }

            // ペースメーカーの初期化済みかどうかで変える。
            if (!_isPaseMakerInitialized)
            {
                // 初期値は最も前の走法の１位のキャラ。
                PaseMaker = GetMostForwardRunningStyleTopHorse();

                // 初期値計算は以降不要。
                _isPaseMakerInitialized = true;
            }
            else
            {
                // ペースメーカー変更の必要があれば再計算。
                if (CheckNeedCalc())
                {
                    PaseMaker = CalcPaseMaker();
                }
                // 「現在のペースメーカーの走法」と「先頭キャラの走法」と「このレース中最も前の走法」が一致するなら、先頭キャラをペースメーカーにする。
                else if (CheckPaseMakerAndTopRunningStyleMostForward())
                {
                    PaseMaker = GetTopHorse();
                }
            }
        }

        /// <summary>
        /// 現在のペースメーカーより前に、前の走法のキャラがいるなら、それを新しいペースメーカーにする。
        /// </summary>
        private void ChangePaseMakerIfForwardRunningStyleExist()
        {
            // ペースメーカーより前に、前の走法のキャラがいるか調べる。
            int checkStartOrder = PaseMaker.CurOrder - 1;
            for (int checkOrder = checkStartOrder; checkOrder >= 0; checkOrder--)
            {
                var checkHorse = _horseAccessor.GetHorseInfoByOrder(checkOrder);
                if (RaceUtil.IsForwardRunningStyle(checkHorse.RunningStyle, PaseMaker.RunningStyle))
                {
                    PaseMaker = checkHorse;
                    _topHorseNotMostForwardRunningStyleCnt = 0;
                    break;
                }
            }
        }

        /// <summary>
        /// 「現在のペースメーカーの走法」と「先頭キャラの走法」と「このレース中最も前の走法」が一致するか。
        /// </summary>
        private bool CheckPaseMakerAndTopRunningStyleMostForward()
        {
            if (PaseMaker == null)
            {
                return false;
            }

            // 「ペースメーカーの走法」と「先頭キャラの走法」が一致するか。
            var topHorse = GetTopHorse();
            if (PaseMaker.RunningStyle != topHorse.RunningStyle)
            {
                return false;
            }
            
            // 「ペースメーカーの走法」と「最も前の走法」が一致するか。
            return PaseMaker.RunningStyle == _horseAccessor.GetMostForwardRunningStyle();
        }

        /// <summary>
        /// 先頭キャラ取得。
        /// </summary>
        private IHorseRaceInfoSimulate GetTopHorse()
        {
            var topHorse = _horseAccessor.GetHorseInfoByOrder(HorseRaceInfoSimulate.ORDER_1ST_NO);
            return topHorse;
        }


        /// <summary>
        /// 先頭キャラがペースメーカー変更する区間に入ったかどうか。変更区間に入った場合、_lastCalculatedSectionも更新される。
        /// </summary>
        private bool CheckCalcSection()
        {
            var topHorse = GetTopHorse();
            int section = topHorse.CalcSection();

            if (section < _positionKeepParam.PositionKeepStartSection ||
                section > _positionKeepParam.PositionKeepEndSection)
            {
                return false;
            }

            if (section <= _lastCalculatedSection)
            {
                return false;
            }
            
            // 最後に計算した区間を保存。
            _lastCalculatedSection = section;
            return true;
        }

        /// <summary>
        /// このレースで最も前の走法の中で１位のキャラを取得。
        /// </summary>
        private IHorseRaceInfoSimulate GetMostForwardRunningStyleTopHorse()
        {
            // 一番前の走法のキャラの中で、一位のキャラを取得。
            var mostForwardStyle = _horseAccessor.GetMostForwardRunningStyle();
            var topHorse = _horseAccessor.GetRunningStyleTopOrderHorse(mostForwardStyle);
            return topHorse;
        }
        
        /// <summary>
        /// ペースメーカー取得。
        /// </summary>
        private IHorseRaceInfoSimulate CalcPaseMaker()
        {
            var topHorse = GetTopHorse();
            switch (topHorse.RunningStyle)
            {
                // 逃げが先頭の場合はここに来ないはず。
                case Gallop.RaceDefine.RunningStyle.Nige:
                    Debug.LogWarning($"先頭が逃げの場合、CalcPaseMakerが呼び出されることは想定していない");
                    break;
                
                // 先行が先頭の場合はそのキャラが基準。
                case Gallop.RaceDefine.RunningStyle.Senko:
                    return topHorse;

                // 差し・追いが先頭の場合は、一定距離内にいる自分より前の走法のキャラを基準にする。いない場合は先頭のキャラが基準。
                case Gallop.RaceDefine.RunningStyle.Sashi:
                case Gallop.RaceDefine.RunningStyle.Oikomi:
                {
                    var nearForwardStyleHorse = GetForwardRunningStyleInRange(topHorse, _positionKeepParam.PaseMakerChangeForwardRunningStyleRange);
                    return nearForwardStyleHorse != null ? nearForwardStyleHorse : topHorse;
                }
            }
            
            return topHorse;
        }
        
        /// <summary>
        /// horseの後ろrange(m)以内に、horseより前の走法のキャラがいるかどうか。
        /// </summary>
        private IHorseRaceInfoSimulate GetForwardRunningStyleInRange(IHorseRaceInfoSimulate horse, float range)
        {
            int order = horse.CurOrder;
            int checkNum = _horseAccessor.GetHorseNumber() - order;
            for (int i = 1; i < checkNum; i++)
            {
                int checkOrder = order + i;
                var checkHorse = _horseAccessor.GetHorseInfoByOrder(checkOrder);
                if (checkHorse == null)
                {
                    break;
                }

                if (!RaceUtil.IsForwardRunningStyle(checkHorse.RunningStyle, horse.RunningStyle))
                {
                    continue;
                }
                
                float distanceDiff = horse.GetDistance() - checkHorse.GetDistance();
                if (distanceDiff <= range)
                {
                    return checkHorse;
                }
            }

            return null;
        }
        
        private bool CheckNeedCalc()
        {
            var topHorse = GetTopHorse();
            
            // チェックの際に先頭キャラが最も前の走法ではなかった回数を数える。
            if (topHorse.RunningStyle != _horseAccessor.GetMostForwardRunningStyle())
            {
                ++_topHorseNotMostForwardRunningStyleCnt;
            }

            // 先頭のキャラの選択走法が一番前の走法ではない 且つ 先頭のキャラと一番前の走法のキャラの1位が一定の距離以上離れている(距離差は要調整)
            {
                if (topHorse.RunningStyle != _horseAccessor.GetMostForwardRunningStyle())
                {
                    var mostForwardRunningStyleHorseArray = _horseAccessor.GetMostForwardRunningStyleHorseArray();
                    var mostForwardTopHorse = RaceUtil.GetHorseByOrder(0, mostForwardRunningStyleHorseArray);

                    float distanceDiff = topHorse.GetDistance() - mostForwardTopHorse.GetDistance();
                    if (distanceDiff >= _positionKeepParam.PaseMakerChangeMostForwardStyleRange)
                    {
                        return true;
                    }
                }
            }

            // 判定地点でのチェックで、2回連続で先頭のキャラの走法が一番前を走る走法では無かったら、ペースメーカーの変更が必要。
            {
                if (_topHorseNotMostForwardRunningStyleCnt >= _positionKeepParam.PaseMakerChangeTopHorseNotMostForwardStyleCount)
                {
                    _topHorseNotMostForwardRunningStyleCnt = 0;
                    return true;
                }
            }
            
            return false;
        }
    }
#endif
}


#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class PositionKeepStrategyNull : IPositionKeepStrategy
    {
        public Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get { return Gallop.RaceDefine.PositionKeepMode.Null; } }
        public float BaseTargetSpeedMultiply { get; }
        public void Update(float deltaTime) { }
        public bool TryStart(bool isRot) { return false; }
        public bool CheckEnd(float deltaTime) { return true; }
        public void Clear() { }
        public void ChangeRunningStyle(Gallop.RaceDefine.RunningStyle style) { }
#if CYG_DEBUG
        public float DbgCurDistanceDiffFromPaseMaker { get; }
        public float DbgDistanceDiffMin { get; }
        public float DbgDistanceDiffMax { get; }
        public float DbgAdjustTargetDistanceDiff { get; }
        public float DbgAdjustTargetMax { get; }
#endif
    }
}

#endif

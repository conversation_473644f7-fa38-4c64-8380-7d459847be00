#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：ペース調整の基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class PositionKeepStrategyPaseBase : PositionKeepStrategyBase
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        protected readonly IRaceRandomGenerator _randomGenerator;
        protected readonly IRaceHorseAccessor _horseAccessor;
        private readonly int _courseDistance;
        private float _distanceDiffMin;
        private float _distanceDiffMax;
        protected float _paseAdjustTargetDistanceDiff;
        private Gallop.RaceDefine.RunningStyle _runningStyle;
        protected readonly IHorsePaseMakerCalculator _paseMakerCalculator;

#if CYG_DEBUG
        protected float _dbgCurDistanceDiffFromPaseMaker;
        public override float DbgCurDistanceDiffFromPaseMaker { get { return _dbgCurDistanceDiffFromPaseMaker; } }
        public override float DbgDistanceDiffMin { get { return _distanceDiffMin; } }
        public override float DbgDistanceDiffMax { get { return _distanceDiffMax; } }
        public override float DbgAdjustTargetDistanceDiff { get { return _paseAdjustTargetDistanceDiff; } }
#endif


        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepStrategyPaseBase(
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            IRaceRandomGenerator randomGenerator,
            IRaceHorseAccessor horseAccessor,
            int courseDistance,
            int sectionNum,
            IHorsePaseMakerCalculator paseMakerCalculator)
            : base(ownerHorse, keepParam, courseDistance, sectionNum)
        {
            _randomGenerator = randomGenerator;
            _horseAccessor = horseAccessor;
            _courseDistance = courseDistance;
            _paseMakerCalculator = paseMakerCalculator;
            ChangeRunningStyle(_ownerHorse.RunningStyle);
        }

        //---------------------------------------------------------------
        protected void InitPaseAdjustTargetDistance(bool isDiffMaxCoef)
        {
            // 現在の走法でのペース配分的に、適正距離を計算。
            HorsePaseCalculator.CalcOffsetDistance(
                _keepParam,
                _courseDistance,
                _runningStyle,
                out _distanceDiffMin,
                out _distanceDiffMax);

            // 最大値をPositionKeepPaseUpDownDistanceDiffMaxCoefで補正。
            if (isDiffMaxCoef)
            {
                Debug.Assert(_keepParam.PositionKeepPaseUpDownDistanceDiffMaxCoef >= 0 && _keepParam.PositionKeepPaseUpDownDistanceDiffMaxCoef <= 1, $"PositionKeepPaseUpDownDistanceDiffMaxCoefが0~1に収まっていない. {_keepParam.PositionKeepPaseUpDownDistanceDiffMaxCoef}");
                _distanceDiffMax = RaceUtilMath.Lerp(_distanceDiffMin, _distanceDiffMax, _keepParam.PositionKeepPaseUpDownDistanceDiffMaxCoef);
            }

            // 適正距離の中から１点をランダムに選ぶ。
            _paseAdjustTargetDistanceDiff = _randomGenerator.GetRandom(_distanceDiffMin, _distanceDiffMax);
        }

        //---------------------------------------------------------------
        protected float CalcPaseMakerDistanceDiff()
        {
            // 基準キャラとの距離差が、目指す距離差の範囲内に入ったらペース調整は完了と見なす。
            var paseMaker = _paseMakerCalculator.PaseMaker;
            float distanceDiff = paseMaker.GetDistance() - _ownerHorse.GetDistance();
#if CYG_DEBUG
            _dbgCurDistanceDiffFromPaseMaker = distanceDiff;
#endif
            return distanceDiff;
        }

        //---------------------------------------------------------------
        public override void ChangeRunningStyle(Gallop.RaceDefine.RunningStyle style)
        {
            _runningStyle = style;
        }
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持モードの計算機：ペース調整（逃げ以外用）。
    /// </summary>
    //-------------------------------------------------------------------
    public class PositionKeepModeCalculatorPaseAdjust : IPositionKeepModeCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly Gallop.RaceParamDefine.PositionKeepParam _keepParam;
        private readonly int _courseDistance;
        private readonly IHorseRaceInfoSimulate _owner;
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly IHorsePaseMakerCalculator _paseMakerCalc;
        private Gallop.RaceDefine.RunningStyle _runningStyle;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepModeCalculatorPaseAdjust(
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            int courseDistance,
            IHorseRaceInfoSimulate owner,
            Gallop.RaceDefine.RunningStyle style,
            IRaceHorseAccessor horseAccessor,
            IHorsePaseMakerCalculator paseMakerCalc)
        {
            _keepParam = keepParam;
            _courseDistance = courseDistance;
            _owner = owner;
            _horseAccessor = horseAccessor;
            _runningStyle = style;
            _paseMakerCalc = paseMakerCalc;
        }

        public Gallop.RaceDefine.PositionKeepMode CalcKeepMode()
        {
            var paseMaker = _paseMakerCalc.PaseMaker;
            
            // ペースメーカーが自分より前にいて、さらに自分より後ろの走法ならそいつを抜かすためのモードになる。
            if(paseMaker.CurOrder < _owner.CurOrder && 
               RaceUtil.IsBackwardRunningStyle(paseMaker.RunningStyle, _owner.RunningStyle))
            {
                return Gallop.RaceDefine.PositionKeepMode.PaseUpEx;
            }
            
            var pase = HorsePaseCalculator.CalcPase(
                _keepParam,
                _courseDistance,
                _owner.GetDistance(),
                paseMaker.GetDistance(),
                _runningStyle);

            if (pase == HorsePaseCalculator.Pase.TooFast)
            {
            #if true
                // 自分がペースメーカーになっていたらペースダウンしない。
                if (paseMaker == _owner)
            #else
                // #52633 自分がトップにいて、自分より前の走法を選んでいるキャラがいない場合はペース調整の必要無し。
                if (topHorse == _owner && IsMostForwardRunningStyle())
            #endif
                {
                    return Gallop.RaceDefine.PositionKeepMode.Null;
                }
                // 早すぎるのでペースダウン。
                return Gallop.RaceDefine.PositionKeepMode.PaseDown;
            }
            else if (pase == HorsePaseCalculator.Pase.TooSlow)
            {
                // 遅すぎるのでペースアップ。
                return Gallop.RaceDefine.PositionKeepMode.PaseUp;
            }
            else
            {
                return Gallop.RaceDefine.PositionKeepMode.Null;
            }
        }

        /// <summary>
        /// 自分が一番前の走法か。
        /// </summary>
        private bool IsMostForwardRunningStyle()
        {
            var mostForwardStyle = _horseAccessor.GetMostForwardRunningStyle();
            return mostForwardStyle == _runningStyle;
        }
    }
}

#endif

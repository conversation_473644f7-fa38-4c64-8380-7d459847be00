#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持モードの計算機インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IPositionKeepModeCalculator
    {
        Gallop.RaceDefine.PositionKeepMode CalcKeepMode();
    }
}

#endif

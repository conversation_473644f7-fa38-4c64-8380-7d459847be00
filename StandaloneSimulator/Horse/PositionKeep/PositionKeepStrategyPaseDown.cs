#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：ペースダウン。
    /// </summary>
    //-------------------------------------------------------------------
    public class PositionKeepStrategyPaseDown : PositionKeepStrategyPaseBase
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public override Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get { return Gallop.RaceDefine.PositionKeepMode.PaseDown; } }
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepStrategyPaseDown(
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            IRaceRandomGenerator randomGenerator,
            IRaceHorseAccessor horseAccessor,
            int courseDistance,
            int sectionNum,
            IHorsePaseMakerCalculator paseMakerCalculator)
            : base(ownerHorse, keepParam, randomGenerator, horseAccessor, courseDistance, sectionNum, paseMakerCalculator)
        {
            BaseTargetSpeedMultiply = GetBaseTargetSpeedMultiply(ownerHorse.GetPhase());
        }

        /// <summary>
        /// 目指す速度の倍率を取得。PaseDownは現在Phaseによって倍率が分岐する。
        /// </summary>
        private float GetBaseTargetSpeedMultiply(Gallop.RaceDefine.HorsePhase phase)
        {
            return phase == Gallop.RaceDefine.HorsePhase.MiddleRun
                ? _keepParam.PositionKeepPaseDownBaseTargetSpeedMultiplyPhaseMiddle
                : _keepParam.PositionKeepPaseDownBaseTargetSpeedMultiply;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
        }

        //---------------------------------------------------------------
        public override bool TryStart(bool isRot)
        {
            // 特定のスキル効果が発動しているときはそちらを優先するため、発動させない。
            if(HasSpeedUpSkillValue())
            {
                return false;
            }
            
            // 時限解放チェック
            if (RaceManagerSimulate.Instance.RaceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.LogicUpdate_20805))
            {
                // 自分より前を走ろうとする走法のキャラがいない場合は発動させない
                if (!IsExistForwardRunningStyleHorse())
                {
                    return false;
                }
            }

            // ここまできたら発動。
            _endDistance = CalcEndDistance();

            // ペース調整によって目指す距離差を計算。
            // 中盤でのペース調整は距離差の最大値を補正する。
            bool isUseDiffMaxCoef = _ownerHorse.GetPhase() == Gallop.RaceDefine.HorsePhase.MiddleRun;
            InitPaseAdjustTargetDistance(isUseDiffMaxCoef);

            // 目指す速度補正係数はPhaseによって値が異なるため、発動時に改めて求める。
            BaseTargetSpeedMultiply = GetBaseTargetSpeedMultiply(_ownerHorse.GetPhase());
            
            return true;
        }

        //---------------------------------------------------------------
        public override bool CheckEnd(float deltaTime)
        {
            // 自分がペースメーカーになったら直ちに終了。
            if (_paseMakerCalculator.PaseMaker == _ownerHorse)
            {
                return true;
            }
            
            // 特定のスキル効果が発動しているときはそちらを優先するため、終了させる。
            if (HasSpeedUpSkillValue())
            {
                return true;
            }

            // 一定距離走行したら終了。
            if (CheckEndDistance())
            {
                return true;
            }

            // 本来の位置取りまで後退できたら終了。
            if (CheckPaseAdjustEnd())
            {
                return true;
            }
            return false;
        }

        //---------------------------------------------------------------
        private bool CheckPaseAdjustEnd()
        {
            // ペースメーカーとの距離差が、目指す距離差以上離れたらペースダウン終了可能。
            float distanceDiff = CalcPaseMakerDistanceDiff();
#if CYG_DEBUG
            _dbgCurDistanceDiffFromPaseMaker = distanceDiff;
#endif
            return distanceDiff >= _paseAdjustTargetDistanceDiff;
        }

        //---------------------------------------------------------------
        public override void Clear()
        {
        }

        /// <summary>
        /// 自分よりも前脚質のウマがいるか？
        /// </summary>
        /// <returns></returns>
        private bool IsExistForwardRunningStyleHorse()
        {
            bool isExist = false;
            var myRunningStyle = _ownerHorse.RunningStyle;
            for (int i = 0; i < _horseAccessor.GetHorseNumber(); i++)
            {
                var horse = _horseAccessor.GetHorseInfoByOrder(i);
                if (horse == null)
                {
                    break;
                }

                if (RaceUtil.IsForwardRunningStyle(horse.RunningStyle, myRunningStyle))
                {
                    isExist = true;
                    break;
                }
            }

            return isExist;
        }

        private bool HasSpeedUpSkillValue()
        {
            if (_ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.CurrentSpeed, 0) > 0)
            {
                return true;
            }
            if (_ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TargetSpeed, 0) > 0)
            {
                return true;
            }
            return false;
        }
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：狙いたい速度に一定値を加算。
    /// </summary>
    //-------------------------------------------------------------------
    public class PositionKeepStrategySpeedUp : PositionKeepStrategyBase
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly IRaceRandomGenerator _randomGenerator;
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly IHorsePaseMakerCalculator _paseMakerCalculator;
        public override Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get { return Gallop.RaceDefine.PositionKeepMode.SpeedUp; } }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepStrategySpeedUp(
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            IRaceRandomGenerator randomGenerator,
            IRaceHorseAccessor horseAccessor,
            int courseDistance,
            int sectionNum,
            float baseTargetSpeedMultiply,
            IHorsePaseMakerCalculator paseMakerCalculator)
            : base(ownerHorse, keepParam, courseDistance, sectionNum)
        {
            _randomGenerator = randomGenerator;
            _horseAccessor = horseAccessor;
            BaseTargetSpeedMultiply = baseTargetSpeedMultiply;
            _paseMakerCalculator = paseMakerCalculator;
        }

        public override void Update(float deltaTime)
        {
        }

        public override bool TryStart(bool isRot)
        {
            // ２位との距離が一定値以上離れていたら、発動しない。
            if(CheckDistanceDiffBehind())
            {
                return false;
            }

            // 確率抽選。
            if(isRot)
            {
                float per = CalcStartPer(_ownerHorse.Wiz, _keepParam);
                if (_randomGenerator.GetRandom(100.0f) >= per)
                {
                    return false;
                }
            }

            // 発動する。
            _endDistance = CalcEndDistance();
            return true;
        }

        /// <summary>
        /// ２位との距離差が一定値以上離れているかどうか。
        /// </summary>
        private bool CheckDistanceDiffBehind()
        {
            var behindHorse = _horseAccessor.GetHorseInfoByOrder(_ownerHorse.CurOrder + 1);
            if(behindHorse == null)
            {
                return true;
            }

            float distanceDiff = _ownerHorse.GetDistance() - behindHorse.GetDistance();
            return distanceDiff >= GetSpeedUpEndDistanceDiff();
        }

        /// <summary>
        /// ２位との距離差がこれ以上離れるとポジ維持終了する、の距離を取得。
        /// </summary>
        private float GetSpeedUpEndDistanceDiff()
        {
            if (_ownerHorse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige)
            {
                return _keepParam.PositionKeepSpeedUpEndDistanceDiffOonige;
            }
            else
            {
                // 自分がペースメーカーかつ逃げでは無い場合、PositionKeepSpeedUpEndDistanceDiffを距離として参照する。
                var paseMaker = _paseMakerCalculator.PaseMaker;
                if (_ownerHorse == paseMaker && _ownerHorse.RunningStyle != Gallop.RaceDefine.RunningStyle.Nige)
                {
                    return _keepParam.PositionKeepSpeedUpEndDistanceDiff;
                }
                
                // 逃げが一人の場合とそうでない場合で、距離を変える。
                var nigeHorseArray = _horseAccessor.GetRunningStyleHorses(Gallop.RaceDefine.RunningStyle.Nige);
                int nigeCount = nigeHorseArray.Count(x => x.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.None);
                if (nigeCount == 1)
                {
                    return _keepParam.PositionKeepSpeedUpEndDistanceDiffOne;
                }
                else
                {
                    return _keepParam.PositionKeepSpeedUpEndDistanceDiff;
                }
            }
        }
        
        
        //---------------------------------------------------------------
        private float CalcStartPer(float wiz, Gallop.RaceParamDefine.PositionKeepParam keepParam)
        {
            float positionKeepSpeedUpStartPerVal1 = _ownerHorse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige 
                ? keepParam.PositionKeepSpeedUpStartPerVal1Oonige
                : keepParam.PositionKeepSpeedUpStartPerVal1;
            float per = positionKeepSpeedUpStartPerVal1 * (float)Math.Log10(wiz * 0.1f);
            return per;
        }

        //---------------------------------------------------------------
        public override bool CheckEnd(float deltaTime)
        {
            // ２位との距離が一定値以上離れていたら、終了する。
            if(CheckDistanceDiffBehind())
            {
                return true;
            }

            // 発動後一定距離進んだら、終了する。
            if(CheckEndDistance())
            {
                return true;
            }
            return false;
        }

        //---------------------------------------------------------------
        public override void Clear()
        {
        }

        //---------------------------------------------------------------
        public override void ChangeRunningStyle(Gallop.RaceDefine.RunningStyle style)
        {
        }
    }
}
#endif

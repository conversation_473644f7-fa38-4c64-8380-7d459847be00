namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorsePositionKeepCalculator
    {
        /// <summary>
        /// ポジション維持発動可能区間にいるかどうか
        /// </summary>
        bool IsPositionKeepSection { get; }
        
        /// <summary>
        /// ポジション維持機能発動中かどうか。
        /// </summary>
        bool IsPositionKeep { get; }

        /// <summary>
        /// 発動中のポジション維持機能モード。
        /// </summary>
        Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get; }

        /// <summary>
        /// 目指す速度基礎値の補正係数。TargetSpeedMode==Multiplyならこの値が係数。
        /// </summary>
        float BaseTargetSpeedMultiply { get; }

        /// <summary>
        /// 更新。
        /// </summary>
        void Update(float deltaTime);

    #if CYG_DEBUG
        float DbgCurDistanceDiffFromPaseMaker { get; }
        float DbgDistanceDiffMin { get; }
        float DbgDistanceDiffMax { get; }
        float DbgAdjustTargetDistanceDiff { get; }
        float DbgCoolDownTime { get; }
    #endif
    }
}

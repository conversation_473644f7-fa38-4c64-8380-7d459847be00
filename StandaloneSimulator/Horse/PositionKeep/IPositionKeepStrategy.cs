#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持実処理インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IPositionKeepStrategy
    {
        /// <summary>
        /// ポジション維持モード。
        /// </summary>
        Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get; }

        /// <summary>
        /// 目指す速度基礎値の補正係数。TargetSpeedMode==Multiplyならこの値が係数。
        /// </summary>
        float BaseTargetSpeedMultiply { get; }

        /// <summary>
        /// 更新。
        /// </summary>
        void Update(float deltaTime);
        /// <summary>
        /// 発動チェック及び発動。
        /// </summary>
        /// <param name="isLot">trueにすると発動チェック時の確率抽選をスキップ(必ずOKに)する。</param>
        /// <returns>発動したらtrueを返す。</returns>
        bool TryStart(bool isLot);
        /// <summary>
        /// 終了チェック及び終了。
        /// </summary>
        /// <returns>終了したらtrueを返す。</returns>
        bool CheckEnd(float deltaTime);
        /// <summary>
        /// 強制終了。
        /// </summary>
        void Clear();

        /// <summary>
        /// ペース計算などに使用する走法変更。
        /// </summary>
        void ChangeRunningStyle(Gallop.RaceDefine.RunningStyle style);

#if CYG_DEBUG
        float DbgCurDistanceDiffFromPaseMaker { get; }
        float DbgDistanceDiffMin { get; }
        float DbgDistanceDiffMax { get; }
        float DbgAdjustTargetDistanceDiff { get; }
#endif
    }
}

#endif

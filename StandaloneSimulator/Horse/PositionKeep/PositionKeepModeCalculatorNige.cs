#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持モードの計算機：逃げ用。
    /// </summary>
    /// <seealso cref="Test.TestPositionKeepModeCalculatorPaseAdjust"/>
    //-------------------------------------------------------------------
    public class PositionKeepModeCalculatorNige : IPositionKeepModeCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly IHorseRaceInfoSimulate _owner;
        private readonly bool _isForceSpeedUp;
        private readonly IRaceHorseAccessor _horseAccessor;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepModeCalculatorNige(
            IHorseRaceInfoSimulate owner,
            bool isForceSpeedUp,
            IRaceHorseAccessor horseAccessor)
        {
            _owner = owner;
            _isForceSpeedUp = isForceSpeedUp;
            _horseAccessor = horseAccessor;
        }

        public Gallop.RaceDefine.PositionKeepMode CalcKeepMode()
        {
            if(_isForceSpeedUp)
            {
                return Gallop.RaceDefine.PositionKeepMode.SpeedUp;
            }

            if(RaceUtil.IsExistBackwardRunningStyle(_owner.CurOrder, _owner.RunningStyle, _horseAccessor))
            {
                // 自分より前に、後ろの走法のキャラがいる場合、そいつを抜かすためのモードになる。
                return Gallop.RaceDefine.PositionKeepMode.PaseUpEx;
            }
            else if (_owner.CurOrder == 0)
            {
                // １位になっているときは加速。
                return Gallop.RaceDefine.PositionKeepMode.SpeedUp;
            }
            else
            {
                // 逃げ以外の走法よりも前に出ようとする。
                return Gallop.RaceDefine.PositionKeepMode.Overtake;
            }
        }
    }
}
#endif

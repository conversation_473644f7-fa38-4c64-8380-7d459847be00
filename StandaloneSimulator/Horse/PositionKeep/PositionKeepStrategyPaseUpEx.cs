#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：自分より前に、後ろの走法のキャラがいる場合のペースアップ。
    /// </summary>
    //-------------------------------------------------------------------
    public class PositionKeepStrategyPaseUpEx : PositionKeepStrategyPaseBase
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public override Gallop.RaceDefine.PositionKeepMode PositionKeepMode => Gallop.RaceDefine.PositionKeepMode.PaseUpEx;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepStrategyPaseUpEx(
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            IRaceRandomGenerator randomGenerator,
            IRaceHorseAccessor horseAccessor,
            int courseDistance,
            int sectionNum,
            float baseTargetSpeedMultiply,
            IHorsePaseMakerCalculator paseMakerCalculator)
            : base(ownerHorse, keepParam, randomGenerator, horseAccessor, courseDistance, sectionNum, paseMakerCalculator)
        {
            BaseTargetSpeedMultiply = baseTargetSpeedMultiply;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
        }

        //---------------------------------------------------------------
        public override bool TryStart(bool isRot)
        {
            _endDistance = CalcEndDistance();
            return true;
        }

        //---------------------------------------------------------------
        public override bool CheckEnd(float deltaTime)
        {
            // 一定距離走行したら終了。
            if (CheckEndDistance())
            {
                return true;
            }

            // 本来の位置取りまで追いついたら終了。
            if (CheckPaseAdjustEnd())
            {
                return true;
            }

            return false;
        }

        //---------------------------------------------------------------
        private bool CheckPaseAdjustEnd()
        {
            // 自分より前に、後ろの走法のキャラがいるか調べる。いるなら継続するのでfalseを返す。
            return !RaceUtil.IsExistBackwardRunningStyle(_ownerHorse.CurOrder, _ownerHorse.RunningStyle, _horseAccessor);
        }

        //---------------------------------------------------------------
        public override void Clear()
        {
        }
    }
}
#endif

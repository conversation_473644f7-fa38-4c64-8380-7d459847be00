#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class PositionKeepStrategyBase : IPositionKeepStrategy
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        protected readonly IHorseRaceInfoSimulate _ownerHorse;
        protected readonly Gallop.RaceParamDefine.PositionKeepParam _keepParam;
        protected readonly float _continueDistance;
        protected float _endDistance;
#if CYG_DEBUG
        public virtual float DbgCurDistanceDiffFromPaseMaker { get; }
        public virtual float DbgDistanceDiffMin { get; }
        public virtual float DbgDistanceDiffMax { get; }
        public virtual float DbgAdjustTargetDistanceDiff { get; }
#endif

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepStrategyBase(
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            int courseDistance,
            int sectionNum)
        {
            _ownerHorse = ownerHorse;
            _keepParam = keepParam;

            int continueSection = ownerHorse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.None
                ? _keepParam.PositionKeepContinueSection
                : GetPositionKeepContinueSectionEx(ownerHorse.RunningStyleEx);
            _continueDistance = (courseDistance / sectionNum) * continueSection;
        }

        private int GetPositionKeepContinueSectionEx(Gallop.RaceDefine.RunningStyleEx runningStyleEx)
        {
            int index = (int)runningStyleEx - 1;
            if (index < 0 || index >= _keepParam.PositionKeepContinueSectionExIntArray.Length)
            {
                Debug.LogWarning($"特殊走法がPositionKeepContinueSectionExArrayに登録されていない runningStyleEx={runningStyleEx}");
                return 0;
            }

            return _keepParam.PositionKeepContinueSectionExIntArray[index];
        }

        public abstract Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get; }
        public float BaseTargetSpeedMultiply { get; protected set; } = 1;
        public abstract void Update(float deltaTime);
        public abstract bool TryStart(bool isRot);
        public abstract bool CheckEnd(float deltaTime);
        public abstract void Clear();
        public abstract void ChangeRunningStyle(Gallop.RaceDefine.RunningStyle style);

        /// <summary>
        /// 発動中のポジション維持機能を終了する距離を計算。
        /// </summary>
        protected float CalcEndDistance()
        {
            return _ownerHorse.GetDistance() + _continueDistance;
        }

        /// <summary>
        /// 発動中のポジション維持機能を終了する距離に達しているかどうか。
        /// </summary>
        /// <returns></returns>
        protected bool CheckEndDistance()
        {
            return _ownerHorse.GetDistance() >= _endDistance;
        }

    }
}

#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：同じ走法内の１位に追いつく。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class PositionKeepStrategyOvertake : PositionKeepStrategyBase
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly IRaceRandomGenerator _randomGenerator;
        private readonly IRaceHorseAccessor _horseAccessor;

        public override Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get { return Gallop.RaceDefine.PositionKeepMode.Overtake; } }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepStrategyOvertake(
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            IRaceRandomGenerator randomGenerator,
            int courseDistance,
            int sectionNum,
            float addTargetSpeed,
            IRaceHorseAccessor horseAccessor)
            : base(ownerHorse, keepParam, courseDistance, sectionNum)
        {
            _randomGenerator = randomGenerator;
            _horseAccessor = horseAccessor;
            BaseTargetSpeedMultiply = addTargetSpeed;
            ChangeRunningStyle(ownerHorse.RunningStyle);
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
        }

        //---------------------------------------------------------------
        public override bool TryStart(bool isRot)
        {
            // 確率抽選。
            if(isRot)
            {
                float per = CalcStartPer(_ownerHorse.Wiz, _keepParam);
                if (_randomGenerator.GetRandom(100.0f) >= per)
                {
                    return false;
                }
            }

            // ここまで来たら発動。
            _endDistance = CalcEndDistance();
            return true;
        }

        //---------------------------------------------------------------
        private float CalcStartPer(float wiz, Gallop.RaceParamDefine.PositionKeepParam keepParam)
        {
            float positionKeepOvertakeStartPerVal1 = _ownerHorse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige
                ? keepParam.PositionKeepOvertakeStartPerVal1Oonige
                : keepParam.PositionKeepOvertakeStartPerVal1;
            float per = (positionKeepOvertakeStartPerVal1 * (float)Math.Log10(wiz * 0.1f));
            return per;
        }

        //---------------------------------------------------------------
        public override bool CheckEnd(float deltaTime)
        {
            // 一定距離進んだら終了。
            if (CheckEndDistance())
            {
                return true;
            }

            // 一位になり、二位のキャラと一定距離以上差がついているなら終了。
            if (CheckDistanceDiffBehindHorse())
            {
                return true;
            }
            
            // 何も条件を満たしていなければ継続。
            return false;
        }

        /// <summary>
        /// 一位になり、二位のキャラと一定距離以上差がついているかどうか。
        /// </summary>
        private bool CheckDistanceDiffBehindHorse()
        {
            // 自分が一位でなければ条件を満たさない。
            if (_ownerHorse.CurOrder > 0)
            {
                return false;
            }

            // 二位のキャラ取得。失敗したら自分以外レースにいないということなので条件満たした扱いにする。
            var behindHorse =  _horseAccessor.GetHorseInfoByOrder(1);
            if (behindHorse == null)
            {
                return true;
            }

            // 後ろのキャラとの差が一定距離離れていないと終了しない。
            float distDiff = _ownerHorse.GetDistance() - behindHorse.GetDistance();
            float positionKeepOvertakeEndDistanceDiff = _ownerHorse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige
                ? _keepParam.PositionKeepOvertakeEndDistanceDiffOonige
                : _keepParam.PositionKeepOvertakeEndDistanceDiff;
            if (distDiff < positionKeepOvertakeEndDistanceDiff)
            {
                return false;
            }

            return true;
        }

        //---------------------------------------------------------------
        public override void Clear()
        {
        }

        //---------------------------------------------------------------
        public override void ChangeRunningStyle(Gallop.RaceDefine.RunningStyle style)
        {
            // 何もしない。
        }
    }
}
#endif

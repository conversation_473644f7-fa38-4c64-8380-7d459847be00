#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ペースの適性を計算。
    /// </summary>
    /// <seealso cref="Test.TestHorsePaseCalculator"/>
    //-------------------------------------------------------------------
    public static class HorsePaseCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>
        /// ペース。
        /// </summary>
        public enum Pase
        {
            Match,      // ちょうどいい。
            TooFast,    // 早すぎ。
            TooSlow,    // 遅すぎ。
        }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// 現在のペースを計算。
        /// </summary>
        /// <param name="distance">自分の距離。</param>
        /// <param name="distanceTop">１位のキャラの距離。</param>
        /// <param name="style">自分の走法。</param>
        public static Pase CalcPase(
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            int courseDistance,
            float distance,
            float distanceTop,
            Gallop.RaceDefine.RunningStyle style)
        {
            // 指定走法での１位からの距離差の適正値を求める。
            CalcOffsetDistance(
                keepParam,
                courseDistance,
                style,
                out float distanceDiffMin,
                out float distanceDiffMax);

            // 実際の距離差と、適正値を比べる。
            // ここで、自身がトップorトップ横並びの場合distanceDiffは0になるが、
            // その場合は「距離が詰まりすぎ」と見なす。
            float distanceDiff = distanceTop - distance;

            // 距離が詰まりすぎ。
            if (distanceDiff < distanceDiffMin)
            {
                return Pase.TooFast;
            }
            // 距離が離れすぎ。
            else if (distanceDiff > distanceDiffMax)
            {
                return Pase.TooSlow;
            }
            // 適正な距離にいる。
            else
            {
                return Pase.Match;
            }
        }

        /// <summary>
        /// 指定走法での１位からの適正距離の範囲を求める。
        /// </summary>
        /// <param name="retDistanceDiffMin">１位からの距離差が、この値以上かつ</param>
        /// <param name="retDistanceDiffMax">この値以下なら適正な位置にいる。</param>
        public static void CalcOffsetDistance(
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            int courseDistance,
            Gallop.RaceDefine.RunningStyle style,
            out float retDistanceDiffMin,
            out float retDistanceDiffMax)
        {
            float rate = CalcOffsetDistanceRate(keepParam, courseDistance);
            retDistanceDiffMin = GetDistanceDiffMin(keepParam, rate, style);
            retDistanceDiffMax = GetDistanceDiffMax(keepParam, rate, style);
        }
        
        /// <summary>
        /// レース距離による判定距離の伸縮における、倍率を計算
        /// </summary>
        /// <param name="keepParam"></param>
        /// <param name="courseDistance"></param>
        /// <returns></returns>
        public static float CalcOffsetDistanceRate(
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            int courseDistance)
        {
            float rate = 1 + (float)(courseDistance - keepParam.PositionKeepPaseBaseDistance) * keepParam.PositionKeepPaseBaseDistanceCoef;
            if (rate < 1)
            {
                rate = 1;
            }

            return rate;
        }

        private static float GetDistanceDiffMin(
            Gallop.RaceParamDefine.PositionKeepParam keepParam, 
            float rate, 
            Gallop.RaceDefine.RunningStyle style)
        {
            switch (style)
            {
                // 逃げはペース調整使用しないので0固定で。
                case Gallop.RaceDefine.RunningStyle.Nige: return 0;

                // 先行にはrateを適用しない仕様。
                case Gallop.RaceDefine.RunningStyle.Senko: return keepParam.PositionKeepPaseUpDownDistanceDiffMinSenko;
                case Gallop.RaceDefine.RunningStyle.Sashi: return rate * keepParam.PositionKeepPaseUpDownDistanceDiffMinSashi;
                case Gallop.RaceDefine.RunningStyle.Oikomi: return rate * keepParam.PositionKeepPaseUpDownDistanceDiffMinOikomi;
                default:
                    Debug.LogWarning($"予期しない走法です。style={style}");
                    return 0;
            }
        }

        private static float GetDistanceDiffMax(
            Gallop.RaceParamDefine.PositionKeepParam keepParam, 
            float rate, 
            Gallop.RaceDefine.RunningStyle style)
        {
            // コース距離比率（rate）によって適正範囲の最大値を補正する。
            switch (style)
            {
                // 逃げはペース調整使用しないので0固定で。
                case Gallop.RaceDefine.RunningStyle.Nige: return 0;

                case Gallop.RaceDefine.RunningStyle.Senko: return rate * keepParam.PositionKeepPaseUpDownDistanceDiffMaxSenko;
                case Gallop.RaceDefine.RunningStyle.Sashi: return rate * keepParam.PositionKeepPaseUpDownDistanceDiffMaxSashi;
                case Gallop.RaceDefine.RunningStyle.Oikomi: return rate * keepParam.PositionKeepPaseUpDownDistanceDiffMaxOikomi;
                default:
                    Debug.LogWarning($"予期しない走法です。style={style}");
                    return 0;
            }
        }
    }
}

#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorsePositionKeepCalculatorNull : IHorsePositionKeepCalculator
    {
        public bool IsPositionKeep { get; }
        public bool IsPositionKeepSection { get; }
        public Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get; }
        public float BaseTargetSpeedMultiply { get; }
        public void Update(float deltaTime) { }

    #if CYG_DEBUG
        public float DbgCurDistanceDiffFromPaseMaker { get; }
        public float DbgDistanceDiffMin { get; }
        public float DbgDistanceDiffMax { get; }
        public float DbgAdjustTargetDistanceDiff { get; }
        public float DbgAdjustTargetMax { get; }
        public float DbgCoolDownTime { get; }
    #endif
    }
}

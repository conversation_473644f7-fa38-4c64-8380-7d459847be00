#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //---------------------------------------------------------------
    /// <summary>
    /// コースセットによる基礎ステータス補正係数の計算処理。
    /// </summary>
    /// <seealso cref="Test.TestCourseSetStatusAdjuster"/>
    //---------------------------------------------------------------
    public static class CourseSetStatusAdjuster
    {
        //-----------------------------------------------------------
        // 定義。
        //-----------------------------------------------------------
        /// <summary>
        /// 補正対象のステータス。
        /// </summary>
        /// <remarks>
        /// race_course_set_status.csvに入力される値。
        /// </remarks>
        public enum TargetStatusType
        {
            Null = 0, // 補正無し。
            Speed = 1,
            Stamina,
            Pow,
            Guts,
            Wiz,
        }

        //-----------------------------------------------------------
        // 関数。
        //-----------------------------------------------------------
        /// <summary>
        /// 補正係数計算。
        /// </summary>
        /// <param name="param">基礎ステータス。</param>
        public static float CalcSpeedCoef(
            IRaceParameter param,
            IRaceCourseSetStatusAccessor masterCourseSetStatus,
            Gallop.RaceParamDefine.CourseSetAdjustParam courseSetAdjustParam)
        {
            if(masterCourseSetStatus == null)
            {
                // Masterがnullということは補正不要ということなのでデフォルト値で返却。
                return 1.0f;
            }

            return CalcSpeedCoefInternal(
                param,
                courseSetAdjustParam,
                (TargetStatusType)masterCourseSetStatus.TargetStatus1,
                (TargetStatusType)masterCourseSetStatus.TargetStatus2);
        }

        private static float CalcSpeedCoefInternal(
            IRaceParameter param,
            Gallop.RaceParamDefine.CourseSetAdjustParam courseSetAdjustParam,
            params TargetStatusType[] targetArray)
        {
            // チェック対象ステータスの数。ex.スピードとスタミナをチェックするなら2。
            int validAdjustNum = targetArray.Count(t => t != TargetStatusType.Null);
            if (validAdjustNum <= 0)
            {
                return 1.0f;
            }

            float retCoef = 1.0f;
            for (int i = 0; i < targetArray.Length; ++i)
            {
                var type = targetArray[i];
                if(type == TargetStatusType.Null)
                {
                    continue;
                }

                retCoef += CalcCoef(type, param, courseSetAdjustParam) / validAdjustNum;
            }

            return retCoef;
        }

        private static float CalcCoef(
            TargetStatusType targetStatusType,
            IRaceParameter param,
            Gallop.RaceParamDefine.CourseSetAdjustParam courseSetAdjustParam)
        {
            switch (targetStatusType)
            {
                case TargetStatusType.Speed: return GetCoefByStatus(courseSetAdjustParam, param.BaseSpeed);
                case TargetStatusType.Stamina: return GetCoefByStatus(courseSetAdjustParam, param.BaseStamina);
                case TargetStatusType.Pow: return GetCoefByStatus(courseSetAdjustParam, param.BasePow);
                case TargetStatusType.Guts: return GetCoefByStatus(courseSetAdjustParam, param.BaseGuts);
                case TargetStatusType.Wiz: return GetCoefByStatus(courseSetAdjustParam, param.BaseWiz);
                default:
                    Debug.LogWarning("チェック対象のステータスタイプが不正です。targetStatusType=" + targetStatusType);
                    return 0;
            }
        }

        /// <summary>
        /// ステータスの値域から補正係数を取得。
        /// </summary>
        /// <returns>
        /// 1.0fに加算して補正する値。
        /// 例：speed = speed x 1.0f + 戻り値。
        /// </returns>
        private static float GetCoefByStatus(Gallop.RaceParamDefine.CourseSetAdjustParam courseSetAdjustParam, float status)
        {
            foreach (var coefAndThreshold in courseSetAdjustParam.CoefAndThresholdArray)
            {
                if (status <= coefAndThreshold.StatusThreshold)
                {
                    return coefAndThreshold.Coef;
                }
            }
            return 0;
        }
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラの出走時出遅れ計算機。
    /// </summary>
    /// <seealso cref="Test.TestHorseDelayCalculator"/>
    //-------------------------------------------------------------------
    public static partial class HorseDelayCalculator
    {
        /// <summary>
        /// 遅延時間計算結果。
        /// </summary>
        public struct DelayResult
        {
            /// <summary>遅延時間。</summary>
            public float DelayTime;
        #if CYG_DEBUG
            /// <summary>スキル効果での補正前の遅延時間。</summary>
            public float DbgDelayTimeBase;
        #endif
        }
        
        /// <summary>
        /// 出遅れ時間計算。
        /// </summary>
        /// <param name="randomGenerator">乱数生成器。</param>
        /// <param name="delayTimeMax">出遅れ時間最大値。</param>
        /// <param name="scale">出遅れ時間に乗算する倍率。</param>
        /// <param name="fix">出遅れ時間を乱数生成ではなくこの値で固定する場合、0超過の値を指定する。scaleはこの値にも乗算される。</param>
        public static DelayResult CalcDelayTime(IRaceRandomGenerator randomGenerator, float delayTimeMax, float scale, float fix)
        {
            var res = new DelayResult();
            
            // 固定値0超過なら、出遅れ時間が固定されていると見なす。
            if (fix > 0)
            {
                res.DelayTime = fix;
            }
            // 固定出ないなら乱数から乱数で出す。
            else
            {
                res.DelayTime = randomGenerator.GetRandom( delayTimeMax );
            }
        #if CYG_DEBUG
            res.DbgDelayTimeBase = res.DelayTime;
        #endif
            
            // 倍率は固定値にも影響させるため、ここで計算。 
            res.DelayTime *= scale;
            return res;
        }
    }
}
#endif

namespace StandaloneSimulator
{
    public static partial class HorseDelayCalculator
    {
        private const float GOOD_START_DELAY_RATE = 0.2f;
        private const float BAD_START_DELAY_RATE = 0.8f;

        /// <summary>
        /// 出遅れ時間が好スタートかどうか。
        /// </summary>
        /// <param name="delayTime">出遅れ時間。</param>
        /// <param name="delayTimeMax">出遅れ時間最大値。</param>
        public static bool IsGoodStart( float delayTime, float delayTimeMax )
        {
            return delayTime < _GetGoodStartDelayTime( delayTimeMax );
        }

        /// <summary>
        /// 出遅れ時間が悪スタートかどうか。
        /// </summary>
        /// <param name="delayTime">出遅れ時間。</param>
        /// <param name="delayTimeMax">出遅れ時間最大値。</param>
        public static bool IsBadStart( float delayTime, float delayTimeMax )
        {
            return delayTime > _GetBadStartDelayTime( delayTimeMax );
        }

        private static float _GetGoodStartDelayTime(float startDelayMax)
        {
            return startDelayMax * GOOD_START_DELAY_RATE;
        }

        private static float _GetBadStartDelayTime(float startDelayMax)
        {
            return startDelayMax * BAD_START_DELAY_RATE;
        }
    }
}
#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Numerics;
using System.Collections.Generic;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中馬制御インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseRaceInfoSimulate : IRaceParameter
    {
        #region <パラメータ>
        /// <summary>
        /// RaceHorseManagerでのキャラ管理番号。0~出走キャラ数-1。
        /// </summary>
        int HorseIndex { get; }
        /// <summary>
        /// カードId。
        /// </summary>
        int CardId { get; }
        /// <summary>
        /// キャラId。
        /// </summary>
        int CharaId { get; }
        /// <summary>
        /// キャラ名。
        /// </summary>
        string CharaName { get; }
        /// <summary>
        /// ビューワーId。ユーザーのキャラでなければ0。
        /// </summary>
        long ViewerId { get; }
        /// <summary>
        /// 育成済みキャラ識別Id。
        /// </summary>
        int SingleModeCharaId { get; }
        /// <summary>
        /// 育成レース勝利回数。
        /// </summary>
        int SingleModeWinCount { get; }
        /// <summary>
        /// 育成ランク。
        /// </summary>
        int FinalGrade { get; }
        /// <summary>
        /// ファン数。
        /// </summary>
        int FanCount { get; }
        /// <summary>
        /// シナリオ専用データ配列
        /// </summary>
        public RaceHorseScenarioData[] ScenarioDataArray { get; }
#if GALLOP
        /// <summary>
        /// モブId。
        /// </summary>
        int MobId { get; }
#endif
        /// <summary>
        /// 枠番。
        /// </summary>
        int PostNumber { get; }

        /// <summary>
        /// 人気。0 ~ 出走馬数-1。
        /// </summary>
        int Popularity { get; }

        /// <summary>
        /// 人気計算中間値。左の印のランク。0が1位。
        /// </summary>
        int PopularityRankLeft { get; }
        /// <summary>
        /// 人気計算中間値。中央の印のランク。0が1位。
        /// </summary>
        int PopularityRankCenter { get; }
        /// <summary>
        /// 人気計算中間値。右の印のランク。0が1位。
        /// </summary>
        int PopularityRankRight { get; }

        /// <summary>
        /// Hp取得。
        /// </summary>
        float GetHp();
        /// <summary>
        /// Hp最大値取得。
        /// </summary>
        float GetMaxHp();
        /// <summary>
        /// Hp設定。
        /// </summary>
        void SetHp(float hp);
        /// <summary>
        /// Hp加算。
        /// </summary>
        void AddHp(float addHp);
        /// <summary>
        /// 最大Hpに対する現在Hp比率取得。
        /// </summary>
        float GetHpPer();

        /// <summary>
        /// パラメータ管理オブジェクト取得。
        /// </summary>
        Gallop.RaceParamDefine ParamDefine { get; }

        /// <summary>
        /// 現在のフェイズ取得。
        /// </summary>
        /// <returns></returns>
        Gallop.RaceDefine.HorsePhase GetPhase();

        /// <summary>
        /// 現在のレーン。
        /// </summary>
        Gallop.RaceDefine.LaneType Lane { get; }

        /// <summary>
        /// 馬場状態補正。
        /// </summary>
        Gallop.RaceDefine.GroundModifierParam GroundModeifierParam { get; }

        /// <summary>
        /// ステータス：スピード。
        /// </summary>
        /// <remarks>基礎ステータスにスキル効果値や馬場状態などの補正が入った値。</remarks>
        float Speed { get; }
        /// <summary>
        /// ステータス：スタミナ。
        /// </summary>
        /// <remarks>基礎ステータスにスキル効果値や馬場状態などの補正が入った値。</remarks>
        float Stamina { get; }
        /// <summary>
        /// ステータス：パワー。
        /// </summary>
        /// <remarks>基礎ステータスにスキル効果値や馬場状態などの補正が入った値。</remarks>
        float Pow { get; }
        /// <summary>
        /// ステータス：根性。
        /// </summary>
        /// <remarks>基礎ステータスにスキル効果値や馬場状態などの補正が入った値。</remarks>
        float Guts { get; }
        /// <summary>
        /// ステータス：賢さ。
        /// </summary>
        /// <remarks>基礎ステータスにスキル効果値や馬場状態などの補正が入った値。</remarks>
        float Wiz { get; }

        /// <summary>
        /// 根性相対順位。0が1位。
        /// </summary>
        int GutsOrder { get; set; }
        /// <summary>
        /// 賢さ相対順位。0が1位。
        /// </summary>
        int WizOrder { get; set; }

        /// <summary>
        /// スタートダッシュ中かどうか。
        /// </summary>
        /// <remarks>
        /// スタートダッシュ：出走直後～レースの最低速度に達するまで急加速する期間。
        /// </remarks>
        bool IsStartDash { get; }

        /// <summary>
        /// 競技場モードチームId。0は無所属。
        /// </summary>
        int TeamId { get; }

        /// <summary>
        /// 競技場モードチーム内メンバーId。1がエース。
        /// </summary>
        int TeamMemberId { get; }

        /// <summary>
        /// 競技場モードのチーム内エースかどうか。
        /// </summary>
        bool IsTeamAce { get; }

        /// <summary>
        /// 私用したアイテム。
        /// </summary>
        int[] ItemIdArray { get; }

        #endregion <パラメータ>

        #region <初期化>
        /// <summary>
        /// RecalcParamsでパラメータ初期化済みかどうか。
        /// </summary>
        bool IsParamInitialized { get; }

        /// <summary>
        /// スキル/馬場/馬場状態などを反映した基本５パラメータ(Speed/Stamina/Pow/Guts/Wiz)を元にステータス計算。
        /// </summary>
        /// <remarks>HP/SPなどのパラメータ最大値・現在値はリセットされる。</remarks>
        void RecalcParams();
        void RecalcParamsPost();

        /// <summary>
        /// スキル生成/初期化。
        /// </summary>
        void CreateSkill();

        /// <summary>
        /// AI生成/初期化。
        /// </summary>
        void CreateAI(IHorseRaceInfoSimulate[] horseRaceInfos, IHorsePaseMakerCalculator paseMakerCalculator);

        /// <summary>
        /// 初回Updateを行ったかどうか
        /// </summary>
        bool IsCompleteFirstUpdate { get; }

        /// <summary>
        /// 初回Updateを行ったかどうかのフラグを更新する
        /// </summary>
        /// <param name="isCompleteFirstUpdate"> 初回Updateを行ったかどうか </param>
        public void SetUpdateFirst(bool isCompleteFirstUpdate);
        #endregion <初期化>

        #region <更新>
        /// <summary>
        /// 更新前更新。
        /// </summary>
        void PreUpdate(float deltaTime);

        /// <summary>
        /// 更新。
        /// </summary>
        /// <returns>ゴール済みかどうか。</returns>
        bool Update(float deltaTime);

        //---------------------------------------------------------------
        /// <summary>
        /// 馬更(強制ゴール版)。
        /// </summary>
        /// <returns></returns>
        //---------------------------------------------------------------
        void UpdateForceGoal(float deltaTime);

        /// <summary>
        /// 更新後更新。
        /// </summary>
        /// <remarks>
        /// 全キャラのUpdateが行われて位置が確定した後に呼ばれる。
        /// </remarks>
        void PostUpdate(float deltaTime);
        
        /// <summary>
        /// 順位確定後更新。
        /// </summary>
        /// <remarks>
        /// 全キャラのUpdateが行われて位置が確定し、PostUpdateが行われ、順位も確定した後に呼ばれる。
        /// </remarks>
        void PostUpdateAfterOrderCalculation(float deltaTime);
        
        /// <summary>
        /// 順位変動回数更新。
        /// </summary>
        void UpdateOrderChangeCount(float deltaTime);

        /// <summary>
        /// 現在のdistance/laneDistanceを元に3D座標/回転を更新。
        /// </summary>
        void UpdateWorldTransformByDistance();
            
        /// <summary>
        /// 全員ゴールに到達したときに一度だけ呼び出される。
        /// </summary>
        void OnAllFinish();

        /// <summary>
        /// レース中にスキップが行われたときに呼び出される。
        /// </summary>
        /// <remarks>
        /// レース終盤前→終盤、終盤以降→リザルト演出までの両方のスキップで呼び出される。
        /// </remarks>
        void OnSkip();
        #endregion <更新>

        #region <AI：基本>
        /// <summary>
        /// インが空いている時、インに寄るかどうか。
        /// </summary>
        /// <remarks>
        /// ※新潟1000はカーブが無いためインに寄らない。
        /// </remarks>
        bool IsEmptyInMove { get; }

        /// <summary>
        /// 出走時の出遅れ時間計算。
        /// </summary>
        /// <param name="max">出遅れ最大秒。</param>
        void CalcDelayTime(float max);
        /// <summary>
        /// 出走時の出遅れ時間。
        /// </summary>
        /// <remarks>
        /// ゲートオープン後にカウントダウンされ、０になったら実際に走り出せる。
        /// </remarks>
        float DelayTime { get; }
        /// <summary>
        /// 出走時の出遅れ時間。
        /// </summary>
        /// <remarks>
        /// この時間はカウントダウンされない。このレース何秒遅れて出走するかを出走後も取得できる。
        /// </remarks>
        float DelayTimeSaved { get; }
#if CYG_DEBUG
        /// <summary>
        /// 出走時の出遅れ時間のスキル効果が反映される前の値。
        /// </summary>
        float DbgDelayTimeSavedBase { get; }
#endif
        
        /// <summary>
        /// 好スタートかどうか。
        /// </summary>
        bool IsGoodStart { get; }
        /// <summary>
        /// 悪スタートかどうか。
        /// </summary>
        bool IsBadStart { get; }

        /// <summary>
        /// レース中に一度でもHpが0になったかどうか。
        /// </summary>
        bool IsHpEmptyOnRace { get; }

        /// <summary>
        /// 追い抜き処理中かどうか。
        /// </summary>
        bool IsOvertake();

        /// <summary>
        /// 追い抜き対象取得。
        /// </summary>
        List<Gallop.AroundHorse> GetOverTakeHorseList();

        /// <summary>
        /// 他の誰かの追い抜き対象になっている継続時間。
        /// </summary>
        float OverTakeTargetContinueTime { get; }

        /// <summary>
        /// 自分が追い抜き対象を持っている時に順位が上がっていない継続時間。
        /// </summary>
        float OverTakeTargetHaveNoOrderUpContinueTime { get; set; }
        /// <summary>
        /// 自分が追い抜き対象を持っている時に順位が下がっていない継続時間。
        /// </summary>
        float OverTakeTargetHaveNoOrderDownContinueTime { get; set; }

        /// <summary>
        /// 詰まり中かどうか。
        /// </summary>
        /// <remarks>リプレイ中だけ有効。</remarks>
        bool IsClog { get; }

        /// <summary>
        /// 敗因分析情報構築。
        /// </summary>
        void CreateDefeat();
        /// <summary>
        /// 敗因取得。
        /// </summary>
        Gallop.RaceDefine.DefeatType Defeat { get; }

        /// <summary>
        /// レーン再検索を行わせる。
        /// </summary>
        bool IsForceCheckNextLaneOneTime { get; set; }

        /// <summary>
        /// 序盤、インに寄るための目指す速度加算状態かどうか。
        /// </summary>
        bool IsForceInMoveEnable { get; }
        
        /// <summary>
        /// 座標取得。
        /// </summary>
        Vector3 GetPosition();
        #endregion <AI：基本>

        #region <AI：ブロック>
        /// <summary>
        /// インに移動可能かどうか。
        /// </summary>
        /// <remarks>falseを返す場合でも、インを他の馬にブロックされているとは限らない。最も埒に寄り切っている場合もfalseを返すため。</remarks>
        bool IsEnableInMove { get; }
        /// <summary>
        /// アウトに移動可能かどうか。
        /// </summary>
        /// <remarks>falseを返す場合でも、アウトを他の馬にブロックされているとは限らない。最も外に寄り切っている場合もfalseを返すため。</remarks>
        bool IsEnableOutMove { get; }

        /// <summary>
        /// 前・イン・アウト全てブロックされているかどうか。
        /// </summary>
        bool IsBlockAll();
        /// <summary>
        /// 前をブロックされているかどうか。
        /// </summary>
        bool IsBlockFront();
        /// <summary>
        /// イン・アウト少なくとも片側をブロックされているかどうか。
        /// </summary>
        bool IsBlockSide();
        /// <summary>
        /// 前をブロックしている馬取得。
        /// </summary>
        IHorseRaceInfoSimulate GetBlockHorseFront();
        /// <summary>
        /// インをブロックしている馬取得。
        /// </summary>
        /// <remarks>IsBlockIn()がtrueを返しても、nullが返却されることがあり得ます。※LaneDistanceがLANEDISTANCE_MINに近い値（内ラチ沿い）で、実質それ以上インに行けないケース。</remarks>
        IHorseRaceInfoSimulate GetBlockHorseIn();
        /// <summary>
        /// アウトをブロックしている馬取得。
        /// </summary>
        /// <remarks>IsBlockOut()がtrueを返しても、nullが返却されることがあり得ます。※LaneDistanceがLANEDISTANCE_MAXに近い値で、実質それ以上アウトに行けないケース。</remarks>
        IHorseRaceInfoSimulate GetBlockHorseOut();

        /// <summary>
        /// 前をブロックされている累積時間。
        /// </summary>
        float BlockFrontAccumulateTime { get; }
        /// <summary>
        /// 前orサイドをブロックされている累積時間。
        /// </summary>
        float BlockFrontOrSideAccumulateTime { get; }

        /// <summary>
        /// 前をブロックされている継続時間。
        /// </summary>
        float BlockFrontContinueTime { get; }
        /// <summary>
        /// サイドをブロックされている継続時間。
        /// </summary>
        float BlockSideContinueTime { get; }
        /// <summary>
        /// 前をHp0のキャラにブロックされている継続時間。
        /// </summary>
        float BlockFrontHpEmptyContinueTime { get; }
        /// <summary>
        /// サイドをブロックされていた最大時間
        /// </summary>
        Dictionary<Gallop.RaceDefine.HorsePhase, float> BlockSideMaxContinueTimeByPhase { get; }
        #endregion <AI：ブロック>
            
        #region <AI：視野>
        /// <summary>
        /// 視野範囲（前方方向距離）
        /// </summary>
        float VisibleDistance { get; }
            
        /// <summary>
        /// 視野範囲内のキャラ。配列要素数は固定。(VisibleHorseCount-1)要素までに有効な値が入る。
        /// </summary>
        Gallop.AroundHorse[] VisibleHorses { get; }
        /// <summary>
        /// 視野範囲内のキャラ数。
        /// </summary>
        int VisibleHorseCount { get; }
        #endregion <AI：視野>

        #region <AI：至近キャラ>
        /// <summary>
        /// 他のキャラ情報。
        /// </summary>
        /// <returns></returns>
        Gallop.AroundHorse[] GetAroundHorses();

        /// <summary>
        /// 前後左右近くのキャラ取得。
        /// </summary>
        /// <remarks>配列は出走キャラ数分確保しているが、有効な数はNearHorseCountまで。</remarks>
        Gallop.AroundHorse[] NearHorses { get; }
        /// <summary>
        /// 前後左右近くのキャラ数取得。
        /// </summary>
        int NearHorseCount { get; }
        /// <summary>
        /// NearHorseCountが一定数(4)以上ある継続時間。
        /// </summary>
        float NearHorseAroundAccumulateTime { get; }
        /// <summary>
        /// 前方混雑で本来の狙いたい速度が出せていない時間。
        /// </summary>
        float CongestionTime { get; }

        /// <summary>
        /// 周りを囲まれているかどうか。
        /// </summary>
        bool IsSurrounded { get; }

        /// <summary>
        /// 順位が一つ下のキャラとｎm以内にいる継続時間。
        /// </summary>
        /// <remarks>自分の順位が変わるとリセットされる。</remarks>
        float BehindHorseNearContinueTime { get; }
        /// <summary>
        /// 順位が一つ上のキャラとｎm以内にいる継続時間。
        /// </summary>
        /// <remarks>自分の順位が変わるとリセットされる。</remarks>
        float InfrontHorseNearContinueTime { get; }

        /// <summary>
        /// 順位が一つ下のキャラとｎm以内かつレーン１ｍ以内にいる継続時間。
        /// </summary>
        /// <remarks>自分の順位が変わるとリセットされる。</remarks>
        float BehindHorseNearLaneContinueTime { get; }
        /// <summary>
        /// 順位が一つ上のキャラとｎm以内かつレーン１ｍ以内にいる継続時間。
        /// </summary>
        /// <remarks>自分の順位が変わるとリセットされる。</remarks>
        float InfrontHorseNearLaneContinueTime { get; }

        /// <summary>
        /// 順位が一つ下のキャラとｎm以内かつレーン１ｍ以内にいる継続時間。距離・レーンのパラメータセット指定でカウントしている版。
        /// </summary>
        /// <remarks>自分の順位が変わるとリセットされる。</remarks>
        float GetBehindHorseNearLaneContinueTime(int paramSet);
        
        /// <summary>
        /// レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値。
        /// </summary>
        float TopLeadMaxAmount { get; }
        
        #endregion <AI：至近キャラ>

        #region <AI：興奮>
        /// <summary>
        /// 出走前の興奮発動抽選。
        /// </summary>
        void LotTemptation();

        /// <summary>
        /// 興奮中かどうか。
        /// </summary>
        bool IsTemptation { get; }
        /// <summary>
        /// 興奮のモード。
        /// </summary>
        TemptationMode TemptationMode { get; }
        /// <summary>
        /// 興奮した回数。
        /// </summary>
        int TemptationCount { get; }
        /// <summary>
        /// このレース中、興奮発動するかどうか。
        /// </summary>
        bool IsTemptationStartEnable { get; }
        #endregion <AI：興奮>

        #region <AI：ポジション維持機能>
        /// <summary>
        /// ポジション維持区間にいるかどうか
        /// </summary>
        bool IsPositionKeepSection { get; }
        /// <summary>
        /// ポジション維持機能発動中かどうか。
        /// </summary>
        bool IsPositionKeep { get; }
        /// <summary>
        /// 発動中のポジション維持機能のモード。
        /// </summary>
        Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get; }
        /// <summary>
        /// ポジション維持機能の発動回数。
        /// </summary>
        int PositionKeepCount { get; set; }
        #endregion <AI：ポジション維持機能>

        #region <AI:競り合い>
        /// <summary>
        /// 競り合い（叩き合い）中かどうか。 
        /// </summary>
        bool IsCompeteFight { get; set; }
        /// <summary>
        /// 競り合い（叩き合い）発動回数。
        /// </summary>
        int CompeteFightCount { get; set; }
        /// <summary>
        /// 競り合い（叩き合い）所属グループ。
        /// </summary>
        int CompeteFightGroup { get; set; }
        /// <summary>
        /// 競り合い（叩き合い）による目指す速度加算値。
        /// </summary>
        float CompeteFightAddTargetSpeed { get; set; }
        /// <summary>
        /// 競り合い（叩き合い）による加速度加算値。
        /// </summary>
        float CompeteFightAddAccel { get; set; }
        /// <summary>
        /// 競り合い（叩き合い）対象となる付近のキャラリスト。
        /// </summary>
        List<Gallop.AroundHorse> CompeteFightNearList { get; }

        /// <summary>
        /// 競り合い（ハナ奪い合い）中かどうか。
        /// </summary>
        bool IsCompeteTop { get; set; }
        /// <summary>
        /// 競り合い（ハナ奪い合い）継続時間。
        /// </summary>
        float CompeteTopRemainTime { get; set; }
        /// <summary>
        /// 競り合い（ハナ奪い合い）の発動回数。
        /// </summary>
        int CompeteTopCount { get; set; }
        /// <summary>
        /// 競り合い（ハナ奪い合い）の所属グループ。
        /// </summary>
        int CompeteTopGroup { get; set; }
        /// <summary>
        /// 競り合い（ハナ奪い合い）による目指す速度加算値。
        /// </summary>
        float CompeteTopAddTargetSpeed { get; set; }
        #endregion

        #region <AI:上限突破パラメータパワー(足溜め)>
        /// <summary>
        /// 足溜め計算機発動状況更新
        /// </summary>
        void UpdateActivateStatusConservePower();
        /// <summary>
        /// 外部から足溜めポイント増加
        /// </summary>
        void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode);

        /// <summary>
        /// 外部から足溜めポイント減少
        /// </summary>
        void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode);
        /// <summary>
        /// 外部から足溜めポイント減少(スキルなど同時に複数の条件が満たされた時用)
        /// </summary>
        void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> mode);

        /// <summary>
        /// 足溜めポイントによる加速度加算値
        /// </summary>
        float ConservePowerAddAccel { get; }
        #endregion
        
        #region <AI:上限突破パラメータスタミナ>
        /// <summary> 上限突破パラメータスタミナ計算機発動状況更新 </summary>
        void UpdateActivateStatusStaminaLimitBreakBuff();
        /// <summary> 上限突破パラメータスタミナによる目指す速度加算値 </summary>
        float StaminaLimitBreakBuffAddTargetSpeed { get; }
        #endregion
        
        #region <坂>
        /// 現在地の坂の種類。
        /// </summary>
        Gallop.RaceDefine.SlopeType SlopeType { get; set; }
        /// <summary>
        /// 坂の勾配%。単位は百分率。
        /// </summary>
        float SlopePer { get; set; }
        /// <summary>
        /// 下り坂での加速モード中かどうか。
        /// </summary>
        bool IsDownSlopeAccelMode { get; }
        /// <summary>
        /// 現在通過中の坂が終了する距離。
        /// </summary>
        float SlopeEndDistance { get; set; }
        #endregion

        #region <速度>
        /// <summary>
        /// 最低速度。
        /// </summary>
        float MinSpeed { get; }

        /// <summary>
        /// 現在速度取得。
        /// </summary>
        float GetLastSpeed();
        /// <summary>
        /// 現在速度設定。
        /// </summary>
        void SetLastSpeed(float spd);

        /// <summary>
        /// スタートダッシュ終了判定で使用している現在速度を取得
        /// </summary>
        /// <returns></returns>
        float GetLastSpeedWithoutDebuffSkill();

        /// <summary>
        /// 現在のレーン移動速度取得。
        /// </summary>
        float GetCurLaneMoveSpeed();
        /// <summary>
        /// 現在のレーン移動速度。自力の移動速度のみ。スキル効果分は含まない。
        /// </summary>
        float LastSelfLaneMoveSpeedPerSec { get; }

        /// <summary>
        /// 狙いたい速度。
        /// </summary>
        float GetTargetSpeed();

        /// <summary>
        /// 狙いたい速度。スキル効果値加算した値。
        /// </summary>
        float GetTargetSpeedWithSkill();

        /// <summary>
        /// 狙いたいレーン。
        /// </summary>
        float GetTargetLane();

        /// <summary>
        /// 同じレーン方向（インorアウト）に移動し続けている秒数。
        /// </summary>
        float MoveLaneContinueTime { get; }
        /// <summary>
        /// レーン移動している方向。
        /// </summary>
        Gallop.RaceDefine.LaneDirection MoveLaneContinueDirection { get; }
        #endregion

        #region <適性>
        /// <summary>
        /// 今回のレースでの距離適性。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ActiveProperDistance { get; }
        /// <summary>
        /// 今回のレースでの馬場適正。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ActiveProperGroundType { get; }

        /// <summary>
        /// 距離適性によるラストスパート目指す速度補正値/終盤以降目指す速度補正値。
        /// </summary>
        float ProperDistanceCoefSpeed { get; }
        /// <summary>
        /// 距離適性による加速度補正値。
        /// </summary>
        float ProperDistanceCoefPow { get; }
        /// <summary>
        /// 走法適正による加速度補正値。
        /// </summary>
        float ProperRunningStyleCoef { get; }
        /// <summary>
        /// 馬場適正による賢さ補正値。
        /// </summary>
        float ProperGroundTypeCoef { get; }
        #endregion

        #region <順位>
        /// <summary>
        /// 前回のフレームでの順位。0が1位。
        /// </summary>
        int PrevOrder { get; set; }
        /// <summary>
        /// 現在の順位。0が1位。
        /// </summary>
        int CurOrder { get; set; }
        /// <summary>
        /// ラストスパート開始時の順位。0が1位。
        /// </summary>
        int LastSpurtOrder { get; set; }
        /// <summary>
        /// 最終直線開始時の順位。
        /// </summary>
        int LastStraightOrder { get; set; }
        /// <summary>
        /// 最終直線開始時の順位初期化済みかどうか。
        /// </summary>
        bool IsLastStraightOrderInitialized { get; }

        /// <summary>
        /// 着順。0が1位。
        /// </summary>
        int FinishOrder { get; set; }
        /// <summary>
        /// 着タイム。（UIでユーザーに表示したりサーバーに保存する着タイムはこちら）
        /// </summary>
        /// <remarks>
        /// 実時間にRaceParamDefine.FinishTimeCoefを掛けた値。
        /// </remarks>
        float FinishTimeScaled { get; set; }
        /// <summary>
        /// FinishTimeScaledのdouble精度版。着順を計算するときだけ使用する。
        /// </summary>
        double FinishTimeScaledForFinishOrderCalc { get; set; }
        /// <summary>
        /// 着タイム実時間。
        /// </summary>
        /// <remarks>
        /// 実時間が欲しい時だけこの値を使う。
        /// </remarks>
        float FinishTimeRaw { get; set; }
        /// <summary>
        /// 前の着順のキャラとの着差時間。
        /// </summary>
        float FinishTimeDiffFromPrevHorse { get; set; }

        /// <summary>
        /// 最後尾にいた時間の累計時間。
        /// </summary>
        float LastOrderTime { get; set; }

        /// <summary>
        /// 順位上昇回数。
        /// </summary>
        int CurOrderUpCountPhaseStart { get; }
        int CurOrderUpCountPhaseMiddle { get; }
        int CurOrderUpCountPhaseEndAfter { get; }
        int CurOrderUpCountCorner { get; }
        int CurOrderUpCountCornerPhaseEndAfter { get; }
        int CurOrderUpCountLastSpurt { get; }
        int CurOrderUpCountDistance1 { get; }
        int CurOrderUpCountFinalCornerAfter { get; }
        int CurOrderUpCountLaterHalf { get; }
        /// <summary> 1フレーム前と比べて順位が上昇したかどうか(クールダウンタイム込み) </summary>
        bool IsCurOrderUp { get; }

        /// <summary>
        /// 順位下降回数。
        /// </summary>
        int CurOrderDownCountPhaseStart { get; }
        int CurOrderDownCountPhaseMiddle { get; }
        int CurOrderDownCountPhaseEndAfter { get; }
        int CurOrderDownCountCorner { get; }
        int CurOrderDownCountCornerPhaseEndAfter { get; }
        int CurOrderDownCountLastSpurt { get; }
        int CurOrderDownCountDistance1 { get; }
        int CurOrderDownCountFinalCornerAfter { get; }
        int CurOrderDownCountLaterHalf { get; }
        /// <summary> 1フレーム前と比べて順位が下降したかどうか(クールダウンタイム込み) </summary>
        bool IsCurOrderDown { get; }

        /// <summary>
        /// レース開始ｎ秒後～現在まで、指定の順位％以内をキープしているかどうか。
        /// </summary>
        bool IsOrderInContinue(SkillDefine.OrderInType type);
        /// <summary>
        /// レース開始ｎ秒後～現在まで、指定の順位％以下をキープしているかどうか。
        /// </summary>
        bool IsOrderOutContinue(SkillDefine.OrderOutType type);
        #endregion <順位>

        #region <走法>
        /// <summary>
        /// 走法取得。
        /// </summary>
        Gallop.RaceDefine.RunningStyle RunningStyle { get; }

        /// <summary>
        /// 特殊走法。
        /// </summary>
        Gallop.RaceDefine.RunningStyleEx RunningStyleEx { get; }
        #endregion <走法>

        #region <距離>
        /// <summary>
        /// 距離取得。
        /// </summary>
        float GetDistance();
        /// <summary>
        /// 距離設定。
        /// </summary>
        void SetDistance(float distance);
        /// <summary>
        /// 距離加算。
        /// </summary>
        void AddDistance(float realMovedDistance);

        /// <summary>
        /// 直前のフレームのレーン距離。
        /// </summary>
        float PrevLaneDistance { get; }

        /// <summary>
        /// レーン距離取得。
        /// </summary>
        float GetLaneDistance();
        /// <summary>
        /// レーン距離設定。
        /// </summary>
        void SetLaneDistance(float lanePosition);

        /// <summary>
        /// レーン距離最低値。
        /// </summary>
        float LaneDistanceMin { get; set; }
        /// <summary>
        /// レーン距離最大値。
        /// </summary>
        float LaneDistanceMax { get; set; }

        /// <summary>
        /// 馬群全体でのレーン比率の百分率。0 ~ 100
        /// </summary>
        float LaneDistancePerInAllHorses { get; set; }

        /// <summary>
        /// 自分が今いる区間を計算。
        /// </summary>
        /// <returns>区間を1~で返す。</returns>
        int CalcSection();

        /// <summary>
        /// ゴール済みかどうか。
        /// </summary>
        bool IsFinished();

        /// <summary>
        /// 最終コーナーを通過したかどうか。
        /// </summary>
        /// <remarks>最終コーナーに入った時点でtrueになる。</remarks>
        bool IsFinalCorner { get; set; }

        /// <summary>
        /// 最終コーナー終了時の順位。最終コーナーを抜けていなければRaceDefine.FINAL_CORNER_END_ORDER_NULLが入っている。
        /// </summary>
        int FinalCornerEndOrder { get; }
        /// <summary>
        /// 最終コーナー終了時の順位が初期化済みかどうか。
        /// </summary>
        bool IsFinalCornerEndOrderInitialized { get; }

        /// <summary>
        /// 現在コーナー通過中かどうか。
        /// </summary>
        bool IsCorner { get; }
        /// <summary>
        /// 現在通過中のコーナー。コーナーにいない場合はRaceUtil.CORNER_NULL。
        /// </summary>
        int CurCorner { get; set; }
        /// <summary>
        /// 現在通過中のコーナーが終了する距離。
        /// </summary>
        float CornerEndDistance { get; set; }
        /// <summary>
        /// 一度でもコーナーイベントを通過したかどうか。
        /// </summary>
        bool IsCornerEventPassed { get; set; }

        /// <summary>
        /// 現在通過中の直線。直線にいない場合は0。
        /// </summary>
        Gallop.RaceDefine.StraightFrontType CurStraightFrontType { get; set; }
        /// <summary>
        /// 現在通過中の直線の番号。観客側、向正面"それぞれ"で何本目の直線か。1~。直線にいない場合は-1。
        /// </summary>
        int CurStraightTypeNumber { get; set; }

        /// <summary>
        /// 最初の直線にいるかどうか。
        /// </summary>
        bool IsStraightFirst { get; }
        /// <summary>
        /// 最後の直線にいるかどうか。
        /// </summary>
        bool IsStraightLast { get; }
        /// <summary> コースイベントで設定された最終直線にいるかどうか </summary>
        bool IsLastStraightCourseEvent { get; set; }
        /// <summary>
        /// 直線にいるかどうか。
        /// </summary>
        bool IsStraight { get; set; }
        #endregion <距離>

        #region <ラストスパート>
        /// <summary>
        /// ラストスパート中かどうか。
        /// </summary>
        bool IsLastSpurt { get; }
        /// <summary>
        /// ラストスパート開始距離。
        /// </summary>
        float LastSpurtStartDistance { get; }

        /// <summary>
        /// ラストスパート計算結果。
        /// </summary>
        LastSpurtCalcResult LastSpurtCalcResult { get; }
        /// <summary>
        /// 初回計算時のラストスパートによる目指す速度
        /// </summary>
        float FirstCalcLastSpurtTargetSpeed { get; }
        /// <summary>
        /// ラストスパート中の狙いたい速度。
        /// </summary>
        float LastSpurtTargetSpeed { get; }
        /// <summary>
        /// ラストスパート計算のリクエスト。
        /// </summary>
        bool IsReqCalcLastSpurt { get; set; }
        /// <summary>
        /// ラストスパート計算が1回でも行われたかどうか
        /// </summary>
        bool IsCalcLastSpurt { get; }
        #endregion <ラストスパート>

        #region <スキル>
        /// <summary>
        /// スキルのレース通しての発動回数取得。
        /// </summary>
        int GetActivateSkillCount();
        /// <summary>
        /// スキルのPhaseごとの発動回数取得。
        /// </summary>
        int GetActivateSkillCountByPhase(Gallop.RaceDefine.HorsePhase phase);
        /// <summary>
        /// スキルのPhaseごとの発動回数加算。
        /// </summary>
        void AddActivateSkillCountByPhase(Gallop.RaceDefine.HorsePhase phase, int add);

        /// <summary>
        /// スキルのレアリティごとの発動回数配列取得。
        /// </summary>
        int[] GetActivateSkillCountByRarityArray();
        /// <summary>
        /// スキルのレアリティごとの発動回数取得。
        /// </summary>
        int GetActivateSkillCountByRarity(SkillDefine.SkillRarity rarity);
        /// <summary>
        /// スキルのレアリティごとの発動回数加算。
        /// </summary>
        void AddActivateSkillCountByRarity(SkillDefine.SkillRarity rarity, int add);

        /// <summary>
        /// 速度スキルの発動回数取得。
        /// </summary>
        int GetActivateSpeedSkillCount();

        /// <summary>
        /// 速度スキルの発動回数加算。
        /// </summary>
        void AddActivateSpeedSkillCount();
        
        /// <summary>
        /// 回復スキルの発動回数取得。
        /// </summary>
        int GetActivateHealSkillCount();
        /// <summary>
        /// 回復スキルの発動回数加算。
        /// </summary>
        void AddActivateHealSkillCount();
        
        /// <summary>
        /// 特定の評価タグを持つスキルの発動回数取得。
        /// </summary>
        int GetActivateSpecificTagGroupSkillCount();
        /// <summary>
        ///特定の評価タグを持つスキルの発動回数加算。
        /// </summary>
        void AddActivateSpecificTagGroupSkillCount();
        
        /// <summary>
        /// 特定のSkillAbilityTypeを持つスキルの発動回数取得。
        /// </summary>
        int GetActivateSpecificSkillAbilityTypeGroupSkillCount();
        /// <summary>
        ///特定のSkillAbilityTypeを持つスキルの発動回数加算。
        /// </summary>
        void AddActivateSpecificSkillAbilityTypeGroupSkillCount();
        
        /// <summary>
        /// スキルの距離ごとの発動回数取得
        /// </summary>
        int GetActivateSkillCountByDistance(HorseRaceInfoSimulate.DistanceDivision distanceDivision);

        /// <summary>
        /// スキルの距離ごとの発動回数加算
        /// </summary>
        void AddActivateSkillCountByDistance(float distance, int add);
        
        /// <summary>
        /// スキル効果量を適用。
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        float ApplyModifier(SkillDefine.SkillModifierParam type, float value);
        /// <summary>
        /// スキル効果量を適用。
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        bool ApplyModifier(SkillDefine.SkillModifierParam type, bool value);
        /// <summary>
        /// スキル効果量Modifier追加。
        /// </summary>
        void AddModifier(SkillDefine.SkillModifierParam type, ISkillParamModifier modifier);

        /// <summary>
        /// スキル効果量AddModifier生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        SkillParamModifierAdd CreateSkillParamAddModifier(
                float value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        );
        /// <summary>
        /// スキル効果量SetModifier生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        SkillParamModifierSet CreateSkillParamSetModifier(
                float value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        );
        /// <summary>
        /// スキル効果量SetModifier生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        SkillParamModifierSet CreateSkillParamSetModifier(
                bool value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        );

        /// <summary>
        /// スキル効果量MultiplyModifier生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        SkillParamModifierMultiply CreateSkillParamMultiplyModifier(
                float value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        );

        /// <summary>
        /// 指定のスキル効果量を所持しているかどうか。
        /// </summary>
        bool HasModifier(SkillDefine.SkillModifierParam type);
        /// <summary>
        /// スキル効果値の削除。
        /// </summary>
        void RemoveModifier(SkillDefine.SkillModifierParam type);
        /// <summary>
        /// スキル効果量継続時間更新。
        /// </summary>
        void UpdateModifier(float deltaTime);
        /// <summary>
        /// デバフ無効化するかどうか。
        /// </summary>
        bool IsDebuffCancel();
        /// <summary>
        /// HpRateDemeritの効果量取得
        /// </summary>
        float GetHpRateDemeritValue();
        
    #if CYG_DEBUG
        List<ISkillParamModifier> DbgGetModifier(SkillDefine.SkillModifierParam type);
        List<ISkillParamModifier> DbgGetCancelledModifier(SkillDefine.SkillModifierParam type);
        void AddCancelledModifier(SkillDefine.SkillModifierParam type, ISkillParamModifier modifier);
        SkillModifierReceiver DbgGetModifierReceiver { get; }
    #endif
        
        /// <summary>
        /// スキル初期化済みかどうか。
        /// </summary>
        bool IsSkillInitialized { get; }

        /// <summary>
        /// 発動中のスキル。
        /// </summary>
        List<ISkillDetail> CurerntActiveSkills { get; }

        /// <summary>
        /// スキル管理オブジェクト取得。
        /// </summary>
        SkillManagerSimulate SkillManager { get; }

        /// <summary>
        /// 所持スキルクリア。
        /// </summary>
        void ClearSkills();

        /// <summary>
        /// スキル更新。1番目に実行されるグループ
        /// </summary>
        void UpdateSkillFirstGroup(float elapsedTime);

        /// <summary>
        /// 1番目のスキル更新実行後に実行される処理
        /// </summary>
        void PostUpdateSkillFirstGroup();

        /// <summary>
        /// スキル更新。2番目に実行されるグループ(基本的にはこのグループで実行される)
        /// </summary>
        void UpdateSkillSecondGroup(float elapsedTime);

        /// <summary>
        /// レース開始前のスキル発動抽選。
        /// </summary>
        void LotActivateSkill();

        /// <summary>
        /// スキル発動条件チェックと起動(1番目に実行されるグループ)
        /// </summary>
        /// <remarks>
        /// 出走前に発動すべきスキルの起動に使う。LotActivateSkillが既に実行されていることを想定している。
        /// </remarks>
        void CheckSkillTriggerAndActivateFirstGroup();

        /// <summary>
        /// スキル発動条件チェックと起動(2番目に実行されるグループ)
        /// </summary>
        /// <remarks>
        /// 出走前に発動すべきスキルの起動に使う。LotActivateSkillが既に実行されていることを想定している。
        /// </remarks>
        void CheckSkillTriggerAndActivateSecondGroup();

        /// <summary>
        /// スキル取得。
        /// </summary>
        /// <returns>失敗したらnullを返却。</returns>
        SkillBase GetSkill(int skillID);
        /// <summary>
        /// 所持スキル全取得。
        /// </summary>
        SkillBase[] GetSkills();

        /// <summary>
        /// 一度でも使用したスキルId取得。
        /// </summary>
        /// <returns>重複無しのリストを返却。そのレースで閃いたスキルIdも含まれている。</returns>
        List<int> GetUsedSkillIdList();

        /// <summary>
        /// 特定スキル強制発動による発動予約スキルIDリストクリア
        /// </summary>
        void ClearReservedSpecificActivateSkillList();

        /// <summary>
        /// 特定スキル強制発動による発動予約スキルID登録
        /// </summary>
        /// <param name="skillIdArray"></param>
        void AddReservedSpecificActivateSkill(int[] skillIdArray);
        
#if UNITY_EDITOR && CYG_DEBUG
        List<SkillManagerSimulate.DbgActivatedSkillInfo> GetDbgActivatedSkillList();
#endif
            
        #endregion

        /// <summary>
        /// アイテム効果適用。
        /// </summary>
        void UseItem();

        #region チーム競技場スコア
        /// <summary>
        /// スキル発動時スコア加算。
        /// </summary>
        void AddScoreSkillActivate(SkillDefine.SkillRarity rarity, int level, int groupRate);

        /// <summary>
        /// スコア合計計算。
        /// </summary>
        int CalcScoreTotal();
        
        /// <summary>
        /// 獲得したスコア。
        /// </summary>
        /// <remarks>
        /// スコア未獲得なら空のリストを返す。
        /// </remarks>
        List<ScoreData> ScoreList { get; }
        #endregion
            
        #region チャレンジマッチポイント
        /// <summary>
        /// スキル発動時ポイント加算。
        /// </summary>
        void AddChallengeMatchPointSkillActivate(SkillDefine.SkillRarity rarity, int level, int groupRate);

        /// <summary>
        /// ポイント合計計算。
        /// </summary>
        int CalcChallengeMatchPointTotal();
    
        /// <summary>
        /// 獲得したポイント。
        /// </summary>
        /// <remarks>
        /// ポイント未獲得なら空のリストを返す。
        /// </remarks>
        List<ChallengeMatchPointData> ChallengeMatchPointList { get; }
        #endregion
            
        void Release();

        #region <デバッグ>
#if CYG_DEBUG && UNITY_EDITOR
        // 枠番ランダム用 シャッフル前の配列順記憶用Index
        public int DbgBaseHorseIndex { get; set; }
        
        /// <summary>
        /// デバッグ用ハロン統計情報取得。
        /// </summary>
        HorseFurlongData[] FurlongDatas { get; }

        LastSpurtParam LastSpurtParam { get; set; }
#endif
#if CYG_DEBUG
        bool DbgIsEnableInMove { get; }
        bool DbgIsEnableOutMove { get; }
        float DbgInLaneSpace { get; }
        float DbgOutLaneSpace { get; }

        float DbgOverTakeLane { get; }
        float DbgOverTakeCoolDownTime { get; }

        Dictionary<Gallop.RaceDefine.HorsePhase, int> DbgGetActivateSkillCount();
        float DbgBaseTargetSpeed { get; set; }
        float DbgBaseTargetSpeedRandomMin { get; set; }
        float DbgBaseTargetSpeedRandomMax { get; set; }
        float DbgCourseSetBaseSpeedCoef { get; }
        float DbgSlopeAddTargetSpeed { get; set; }
        float DbgSkillAddTargetSpeed { get; set; }
        float DbgForceInMoveAddTargetSpeed { get; set; }
        float DbgPositionKeepBaseTargetSpeedMultiply { get; set; }

        float DbgAccelPerSec { get; set; }
        float DbgDecHpPerSec { get; set; }
        float DbgAddHp { get; set; }

        float DbgLastSpurtTargetSpeed { get; set; }

        float DbgConsumeHpOnTemptation { get; }

        IHorseRaceAI DbgHorseAI { get; }

        List<ScoreBonusRate> DbgBonusRateList { get; set; }
        int DbgTeamStadiumTotalScore { get; set; }

        float DbgTemptationPerRandom { get; set; }
        float DbgTemptationPerWiz { get; set; }
        int DbgTemptationStartSection { get; set; }

        List<LastSpurtCandidate> DbgLastSpurtCandidateList { get; set; }
        LastSpurtCandidate DbgLastSpurtUseCandidate { get; set; }
        Queue<float> DbgLastSpurtLastCalculatedSpeedQueue { get; }
        int DbgLastSpurtCalcCount { get; set; }

        float DbgLastMoveOutStartDistance { get; set; }
        List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteTopStartGroupHorseList { get; set; }
        List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteTopEndGroupHorseList { get; set; }
        List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteFightStartGroupHorseList { get; set; }
        List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteFightEndGroupHorseList { get; set; }

        int DbgPaseMakerHorseIndex { get; set; }
        int DbgTopHorseNotMostForwardRunningStyleCnt { get; set; }
        List<int> DbgOverTakenHorseIndexList { get; }

        float DbgSpeedOnStart { get; }
        float DbgStaminaOnStart { get; }
        float DbgPowOnStart { get; }
        float DbgGutsOnStart { get; }
        float DbgWizOnStart { get; }
        float[] DbgDistanceDiffOnPositionKeepCalcArray { get; set; }
    #if GALLOP
        int NpcType { get; }
        int FrameOrder { get; }
    #endif
        /// <summary>
        /// 自分が追い抜きした記録。
        /// </summary>
        List<DbgOverTakeLog> DbgOrderChangeCounterUpOverTakeLogList { get;}
        /// <summary>
        /// 自分が追い越されした記録。
        /// </summary>
        List<DbgOverTakeLog> DbgOrderChangeCounterDownOverTakeLogList { get;}
#endif

    #endregion <デバッグ>
    }
}
#endif

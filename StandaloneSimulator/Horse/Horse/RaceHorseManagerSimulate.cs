#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース馬管理オブジェクト：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class RaceHorseManagerSimulate : IRaceHorseAccessor
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        private const float OVERLAP_DISTANCE_GAP_THRESHOLD = Gallop.RaceDefine.HorseLength_One * 0.4f;
        private const float OVERLAP_LANE_GAP_THRESHOLD = Gallop.RaceDefine.HorseLane_One * 0.4f;

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>このレースのRaceInfo</summary>
        private RaceInfo _raceInfo;
        /// <summary>ParamDefine</summary>
        private Gallop.RaceParamDefine _paramDefine;

        /// <summary>キャラ</summary>
        private IHorseRaceInfoSimulate[] _horseRaceInfos;
        /// <summary>キャラ数</summary>
        private int _horseNum = 0;

        /// <summary>キャラの現在順位計算機</summary>
        private HorseOrderCalculator _orderCalculator = new HorseOrderCalculator();

        /// <summary>同じ走法毎のキャラ配列</summary>
        private IHorseRaceInfoSimulate[][] _sameRunningStyleHorses = new IHorseRaceInfoSimulate[ Gallop.RaceDefine.HORSE_USABLE_RUNNING_STYLE_ARRAY.Length ][];
        /// <summary>各走法の人数</summary>
        private int[] _sameRunningStyleCount = new int[ Gallop.RaceDefine.HORSE_USABLE_RUNNING_STYLE_ARRAY.Length ];
        /// <summary>各走法でトップのキャラ</summary>
        private IHorseRaceInfoSimulate[] _runningStyleTopOrderHorses = new IHorseRaceInfoSimulate[Gallop.RaceDefine.HORSE_USABLE_RUNNING_STYLE_ARRAY.Length];
        /// <summary>各走法で最下位のキャラ</summary>
        private IHorseRaceInfoSimulate[] _runningStyleLastOrderHorses = new IHorseRaceInfoSimulate[ Gallop.RaceDefine.HORSE_USABLE_RUNNING_STYLE_ARRAY.Length ];

        /// <summary>各走法で掛かりになっているキャラ</summary>
        private List<IHorseRaceInfoSimulate>[] _runningStyleTemptationHorseArray = new List<IHorseRaceInfoSimulate>[Gallop.RaceDefine.HORSE_USABLE_RUNNING_STYLE_ARRAY.Length];
        /// <summary>IHorseRaceInfoSimulateの空リスト</summary>
        private List<IHorseRaceInfoSimulate> _emptyHorseList = new List<IHorseRaceInfoSimulate>();

        /// <summary>このレースの最も前の走法</summary>
        private Gallop.RaceDefine.RunningStyle _mostForwardRunningStyle;
        /// <summary>このレースの最も前の走法のキャラ配列</summary>
        private IHorseRaceInfoSimulate[] _mostForwardRunningStyleHorseArray;

        /// <summary>チームのリスト。無所属のチームは含まれない</summary>
        private List<HorseTeamInfo> _teamInfoList = new List<HorseTeamInfo>(); 
        /// <summary>チームメンバーの空配列</summary>
        private readonly IHorseRaceInfoSimulate[] _emptyTeamMemberArray = new IHorseRaceInfoSimulate[0];

        /// <summary>掛かっているキャラのリスト</summary>
        private List<IHorseRaceInfoSimulate> _temptationHorseList = new List<IHorseRaceInfoSimulate>(); 
        
        /// <summary> 毎フレーム更新されるスキルを発動したキャラと発動スキルのテーブル </summary>
        private Dictionary<IHorseRaceInfoSimulate, List<ISkillDetail>> _prevActivateSkillHorseDict = new Dictionary<IHorseRaceInfoSimulate, List<ISkillDetail>>();

        /// <summary> 回復スキルを発動したキャラリスト(スキル発動順に格納されていきます) </summary>
        private List<IHorseRaceInfoSimulate> _activateHealSkillHorseList = new List<IHorseRaceInfoSimulate>();

        /// <summary>ペースメーカー計算機</summary>
        private IHorsePaseMakerCalculator _paseMakerCalculator;

        /// <summary>敗因計算済みかどうか</summary>
        private bool _isDefeatChecked = false;

        /// <summary>着順でアクセスできるHorseIndex配列。ex.1位のHorseIndexは_horseIndexByFinishOrder[0]で取得できる</summary>
        private int[] _horseIndexByFinishOrder = null;
        /// <summary>着順計算済みかどうか</summary>
        private bool _isFinishOrderCalculated = false;

        /// <summary>位置取り争い（旧名称: 競り合い（ハナ奪い合い））計算機</summary>
        private IHorseCompeteTopCalculator _competeTopCalculator;
        /// <summary>追い比べ（旧名称: 競り合い（叩き合い））計算機</summary>
        private IHorseCompeteFightCalculator _competeFightCalculator;
        
        /// <summary>このレースの最下位の順位</summary>
        public int LastOrder { get; private set; }

        //---------------------------------------------------------------
        public void Init(RaceInfo raceInfo, Gallop.RaceParamDefine paramDefine)
        {
            _raceInfo = raceInfo;
            _paramDefine = paramDefine;
            _horseRaceInfos = new IHorseRaceInfoSimulate[raceInfo.NumRaceHorses];
            _horseNum = raceInfo.NumRaceHorses;
            LastOrder = _horseNum - 1;
            _paseMakerCalculator = CreatePaseMakerCalculator();

            // 馬制御の生成。
            for (int i = 0; i < raceInfo.NumRaceHorses; i++)
            {
                var desc = new HorseRaceInfoSimulate.InitDesc()
                {
                    RawHorseData = raceInfo.RaceHorse[i],
                    HorseIndex = i,
                    HorseNum = _horseNum,
                    GroundType = raceInfo.GroundType,
                    DistanceType = raceInfo.CourseDistanceType,
                    ParamDefine = paramDefine,
                    RaceInfo = raceInfo,
                    LaneDistanceMax = raceInfo.LaneDistanceMax,
                    HorseAccessor = this,
                };
                desc.IsFirstMoveLaneIsIn = RaceManagerSimulate.Instance.IsFirstMoveLaneIsIn; 
                desc.IsEnableFirstMoveLane = RaceManagerSimulate.Instance.IsEnableFirstMoveLane;
                desc.FirstMoveLanePointDistance = RaceManagerSimulate.Instance.FirstMoveLanePointDistance; 
                
                _horseRaceInfos[i] = CreateHorseRaceInfo(
                    desc, 
                    raceInfo.RaceType,
                    raceInfo.ScoreCalcTeamId);
#if CYG_DEBUG && UNITY_EDITOR
                // デバッグ用: 枠番シャッフル用に元の配列順を記録しておく
                _horseRaceInfos[i].DbgBaseHorseIndex = raceInfo.RaceHorse[i].dbg_base_horse_index;          
#endif
            }
            
            for (int i = 0; i < _runningStyleTemptationHorseArray.Length; i++)
            {
                _runningStyleTemptationHorseArray[i] = new List<IHorseRaceInfoSimulate>();
            }
            
            // 各走法ごとのキャラをキャッシュ。
            CacheSameRunningStyleHorses();
            // 一番前の走法を選んでいるキャラをキャッシュ。
            CacheMostForwardRunningStyleHorses();
            
            // _runningStyleTopOrderHorses/_runningStyleLastOrderHorsesに初期値を入れたいので更新。
            UpdateRunningStyleFirstAndLastOrderHorses();

            // スキルの生成。
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].CreateSkill();
            }

            // 馬AIの生成。
            for( int i = 0; i < _horseRaceInfos.Length; i++ )
            {
                _horseRaceInfos[i].CreateAI(_horseRaceInfos, _paseMakerCalculator);
            }

            // 順位計算機の初期化。
            _orderCalculator.Init( _horseRaceInfos );

            // チーム情報の構築。
            _teamInfoList = CreateTeamInfo(
                _horseRaceInfos, 
                selfEvaluate:_raceInfo.SelfEvaluate, 
                opponentEvaluate:_raceInfo.OpponentEvaluate, 
                supportCardScoreBonus:_raceInfo.SupportCardScoreBonus);
            
            _horseIndexByFinishOrder = new int[raceInfo.NumRaceHorses];
            _competeTopCalculator = CreateCompeteTopCalculator();
            _competeFightCalculator = CreateCompeteFightCalculator();
        }

        //---------------------------------------------------------------
        public void Release()
        {
            _competeTopCalculator = null;
            _competeFightCalculator = null;
            
            if (_horseRaceInfos != null)
            {
                for (int index = 0; index < _horseRaceInfos.Length; ++index)
                {
                    _horseRaceInfos[index].Release();
                }
                _horseRaceInfos = null;
            }
        }

        //---------------------------------------------------------------
        private IHorseCompeteTopCalculator CreateCompeteTopCalculator()
        {
#if CYG_DEBUG
            if (!RaceSimulateDebugger.IsEnableAICompeteTop)
            {
                return new HorseCompeteTopCalculatorNull();
            }            
#endif
            
            return new HorseCompeteTopCalculator(
                this,
                RaceManagerSimulate.Instance,
                RaceManagerSimulate.Instance,
                _paramDefine.CompeteTop);
        }
        
        //---------------------------------------------------------------
        private IHorseCompeteFightCalculator CreateCompeteFightCalculator()
        {
#if CYG_DEBUG
            if (!RaceSimulateDebugger.IsEnableAICompeteFight)
            {
                return new HorseCompeteFightCalculatorNull();
            }            
#endif
            
            return new HorseCompeteFightCalculator(
                this,
                RaceManagerSimulate.Instance,
                RaceManagerSimulate.Instance,
                _paramDefine.CompeteFight);
        }
        
        //---------------------------------------------------------------
        private IHorsePaseMakerCalculator CreatePaseMakerCalculator()
        {
            return new HorsePaseMakerCalculator(
                this,
                _paramDefine.PositionKeep);
        }

        //---------------------------------------------------------------
        public int GetHorseNumber()
        {
            return _horseNum;
        }

        //---------------------------------------------------------------
        public void InitStartPosition()
        {
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].UpdateWorldTransformByDistance();
            }
        }

        //---------------------------------------------------------------
        public void InitBeforeStart()
        {
            // 出走前に所持しているスキルを発動するかの抽選を行う。
            AllHorseLotActivateSkill();
            // 出走前に競馬場特性や天気特性、得意走法などの、レースを通してON/OFFが固定されるスキルを発動する。
            // このスキルが反映された状態でキャラのステータスや、キャラ間のステータス順位を計算する。
            AllHorseCheckSkillTriggerAndActivate();
            
            // アイテム効果適用。
            AllHorseUseItem();

            // アイテムやスキルなどのパラメータ反映したパワーを足溜めでは参照したいのでここで初期化している
            UpdateActivateStatusConservePower();
            UpdateActivateStatusStaminaLimitBreakBuff();
            
            //-----------------------------------------------------------
            // ここまでに出走前のスキル・アイテムの反映は終了。
            //-----------------------------------------------------------
            
            // スタート遅延時間計算。
            CalcDelayTime();
            // スキルを反映させたステータス再計算。
            RecalcParams();

            // 出走前にこのレース中興奮発動するどうかを抽選。
            // 確率は賢さに依存するため、スキル効果を反映させた最終的なステータスが計算された後(RecalcParams)のタイミングで行う必要がある。
            AllHorseLotTemptation();

            // 座標をスタートゲートに置く。
            InitStartPosition();
        }

        /// <summary>
        /// 全キャラの足溜め発動状況更新
        /// </summary>
        private void UpdateActivateStatusConservePower()
        {
            for (int i = 0; i < _horseRaceInfos.Length; ++i)
            {
                _horseRaceInfos[i].UpdateActivateStatusConservePower();
            }
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// 全キャラの上限突破パラメータスタミナ発動状況更新
        /// </summary>
        //---------------------------------------------------------------
        private void UpdateActivateStatusStaminaLimitBreakBuff()
        {
            for (int i = 0; i < _horseRaceInfos.Length; ++i)
            {
                _horseRaceInfos[i].UpdateActivateStatusStaminaLimitBreakBuff();
            }
        }
        
        //---------------------------------------------------------------
        private void AllHorseUseItem()
        {
            for (int i = 0; i < _horseRaceInfos.Length; ++i)
            {
                _horseRaceInfos[i].UseItem();
            }
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// HorseRaceInfo生成。
        /// </summary>
        //---------------------------------------------------------------
        private IHorseRaceInfoSimulate CreateHorseRaceInfo(
            HorseRaceInfoSimulate.InitDesc desc, 
            Gallop.RaceDefine.RaceType type, 
            int scoreCalcTeamId)
        {
            bool isNeedScore = (scoreCalcTeamId != Gallop.RaceDefine.TEAM_ID_NULL) && (desc.RawHorseData.team_id == scoreCalcTeamId);

            // チャレンジマッチではユーザーのウマ娘は一人だけなのでviewerIdが入っているキャラをポイント計算対象にする。
            bool isNeedChallengeMatchPoint = desc.RawHorseData.viewer_id != 0;
            
            if (type == Gallop.RaceDefine.RaceType.ChallengeMatch && isNeedChallengeMatchPoint)
            {
                return new HorseRaceInfoSimulateChallengeMatch(desc);
            }
            else if (type == Gallop.RaceDefine.RaceType.TeamStadium && isNeedScore)
            {
                return new HorseRaceInfoSimulateStadium(desc);
            }
            else
            {
                return new HorseRaceInfoSimulate(desc);
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 順位計算。
        /// </summary>
        //---------------------------------------------------------------
        private void CalcHorseOrder( float deltaTime )
        {
            _orderCalculator.UpdateHorseOrderSimulate();

            for( int i = 0; i < _horseRaceInfos.Length; ++i )
            {
                _horseRaceInfos[i].UpdateOrderChangeCount( deltaTime );
            }
        }

        /// <summary>
        /// 着順計算。
        /// </summary>
        private void CalcFinishOrder()
        {
            if( _isFinishOrderCalculated )
            {
                return;
            }

            // 着タイムから着順順のHorseIndex配列取得。
            var finishTimes = _horseRaceInfos.Select( h => h.FinishTimeScaledForFinishOrderCalc ).ToArray();
            RaceUtil.InitHorseIndexByFinishOrder( finishTimes, out _horseIndexByFinishOrder );

            // HorseRaceInfoに着順と着タイム差を設定。
            for (int i = 0 ; i<_horseIndexByFinishOrder.Length ; i++ )
            {
                var curHorse = _horseRaceInfos[ _horseIndexByFinishOrder[ i ] ];
                curHorse.FinishOrder = i;

                // 自分の前の着順キャラとの着タイム差。
                if (i != 0 )
                {
                    var prevHorse = _horseRaceInfos[ _horseIndexByFinishOrder[ i - 1 ] ];
                    float diffTime = curHorse.FinishTimeScaled - prevHorse.FinishTimeScaled;
                    curHorse.FinishTimeDiffFromPrevHorse = diffTime;
                }
            }
            
#if CYG_DEBUG
            if (RaceSimulateDebugger.IsNeedFixFinishOrder())
            {
                SwapPlayerFinishOrder();
            }
#endif

            _isFinishOrderCalculated = true;
        }

#if CYG_DEBUG        
        //---------------------------------------------------------------
        private void SwapPlayerFinishOrder()
        {
            var raceHorseDataArray = GetHorseRaceInfos();
            
            foreach (var fixFinishOrder in RaceSimulateDebugger.FixFinishOrderDic)
            {
                if (fixFinishOrder.Key == RaceSimulateDebugger.FixFinishOrderTarget.Null)
                {
                    continue;
                }
                if (fixFinishOrder.Value == RaceSimulateDebugger.FIX_FINISH_ORDER_NULL)
                {
                    continue;
                }
                
                // 交換する順位。
                int newFinishOrder = fixFinishOrder.Value;
                if (newFinishOrder == RaceSimulateDebugger.FIX_FINISH_ORDER_LAST)
                {
                    newFinishOrder = _raceInfo.LastOrder;
                }

                // 上書きするキャラ。
                int swapTargetHorseIndexTo = RaceSimulateDebugger.GetFixFinishOrderHorseIndex(fixFinishOrder.Key, raceHorseDataArray);
                if(swapTargetHorseIndexTo < 0)
                {
                    Debug.LogWarning("プレイヤーのキャラが見つからなかったため、着順固定ができませんでした。");
                    continue;
                }

                
                // このLhが着順固定したいキャラ。本来の着順/着タイムを取得。
                var horseLh = _horseRaceInfos[swapTargetHorseIndexTo];
                int finishOrderLh = horseLh.FinishOrder;
                float finishTimeRawLh = horseLh.FinishTimeRaw;
                float finishTimeScaledLh = horseLh.FinishTimeScaled;
                float finishTimeDiffFromPrevHorseLh = horseLh.FinishTimeDiffFromPrevHorse;
                if(finishOrderLh == newFinishOrder)
                {
                    // 順位が既に指定値と一致しているなら処理不要。
                    continue;
                }

                // Rhは着順固定したい順位（newFinishOrder）を本来獲得していたキャラ。
                var horseRh = _horseRaceInfos.FirstOrDefault(h => h.FinishOrder == newFinishOrder);
                float finishTimeRawRh = horseRh.FinishTimeRaw;
                float finishTimeScaledRh = horseRh.FinishTimeScaled;
                float finishTimeDiffFromPrevHorseRh = horseRh.FinishTimeDiffFromPrevHorse;

                // LhとRhの着順/着タイムを入れ替え。
                // 例：プレイヤー本来の順位が7位、上書きしたい順位が1位なら、1位と7位の着順/着タイムを入れ替える。
                horseLh.FinishOrder = newFinishOrder;
                horseLh.FinishTimeRaw = finishTimeRawRh;
                horseLh.FinishTimeScaled = finishTimeScaledRh;
                horseLh.FinishTimeDiffFromPrevHorse = finishTimeDiffFromPrevHorseRh;

                horseRh.FinishOrder = finishOrderLh;
                horseRh.FinishTimeRaw = finishTimeRawLh;
                horseRh.FinishTimeScaled = finishTimeScaledLh;
                horseRh.FinishTimeDiffFromPrevHorse = finishTimeDiffFromPrevHorseLh;
            }
        }
#endif
        
        //---------------------------------------------------------------
        public bool UpdateFirstHorse()
        {
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].SetUpdateFirst(false);
            }

            bool isAllFinished = UpdateHorse(0);

            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].SetUpdateFirst(true);
            }

            return isAllFinished;
        }

        //---------------------------------------------------------------
        public bool UpdateHorse(float deltaTime)
        {
            for ( int i = 0 ; i < _horseRaceInfos.Length ; i++ )
            {
                _horseRaceInfos[i].PreUpdate( deltaTime );
            }

            // スキル更新は2回に分けて行われる
            // 1グループ目のスキル更新(デバフ無効化スキル等他のスキルより早く処理したいスキルはここで処理される)
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].UpdateSkillFirstGroup(deltaTime);
            }
            // 1グループ目のスキル更新が行われた後に実行される処理
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].PostUpdateSkillFirstGroup();
            }

            // 2グループ目のスキル更新(大体のスキルがここで処理される)
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].UpdateSkillSecondGroup(deltaTime);
            }

            bool allFinished = true;
            for ( int i = 0 ; i < _horseRaceInfos.Length ; i++ )
            {
                allFinished &= _horseRaceInfos[i].Update( deltaTime );
            }

            // キャラ同士重ならないように配置。
            UpdateOverlap();
            
            // 競り合い更新。
            _competeTopCalculator.Update(deltaTime);
            _competeFightCalculator.Update(deltaTime);
            
            // ペースメーカー計算機更新。
            _paseMakerCalculator.Update();

            // 全キャラの更新が済んで位置が確定した後の更新。
            for ( int i = 0 ; i < _horseRaceInfos.Length ; i++ )
            {
                _horseRaceInfos[i].PostUpdate(deltaTime);
            }
            
            if( allFinished )
            {
                CalcFinishOrder();
                ClampFinishTime();
                CreateDefeat();
                CallOnAllFinish();
            }
            else
            {
                CalcHorseOrder( deltaTime );
            }
            
            // 全キャラの順位が確定した後の更新。
            for ( int i = 0 ; i < _horseRaceInfos.Length ; i++ )
            {
                _horseRaceInfos[i].PostUpdateAfterOrderCalculation(deltaTime);
            }

            // 最後尾のキャラの、最後尾にいた累計時間更新。
            UpdateLastOrderTime( deltaTime );

            // 各走法の最後尾のキャラを更新。
            UpdateRunningStyleFirstAndLastOrderHorses();

            // 各走法の興奮状態のキャラ数をカウント。
            CacheTemptationHorse();
            // このフレームでスキルを発動したキャラと発動スキルリストをキャッシュ
            CachePrevActivateSkillHorseDict();
            // 直近の回復スキル発動キャラリストを更新
            CacheActivateHealSkillHorseHistoryList();
            // 全キャラのレーン比率更新。
            UpdateLaneDistanceRate();

#if CYG_DEBUG
            DbgCheckOverlap();
#endif

            return allFinished;
        }

        private void UpdateLaneDistanceRate()
        {
            float laneMin = float.MaxValue;
            float laneMax = float.MinValue;
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                var horse = _horseRaceInfos[i];
                if (horse.IsFinished())
                {
                    continue;
                }

                float lane = horse.GetLaneDistance();
                if (lane < laneMin)
                {
                    laneMin = lane;
                }
                if (lane > laneMax)
                {
                    laneMax = lane;
                }
            }

            float laneMinMaxDiff = laneMax - laneMin;
            if (RaceUtilMath.Approximately(laneMinMaxDiff, 0))
            {
                // 除算に使うので０割発生しないように。
                laneMinMaxDiff = 1;
            }
            
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                var horse = _horseRaceInfos[i];
                if (horse.IsFinished())
                {
                    continue;
                }

                // 最も内側にいるキャラを0、最も外側にいるキャラを100とする。
                float lane = horse.GetLaneDistance();
                float laneRate = (lane - laneMin) / laneMinMaxDiff;
                horse.LaneDistancePerInAllHorses = laneRate * 100; // 百分率にするため x100。
                Debug.Assert(horse.LaneDistancePerInAllHorses >= 0 && horse.LaneDistancePerInAllHorses <= 100, $"レーン比率が0~100に収まっていない:{horse.LaneDistancePerInAllHorses}");
            }   
        }
        
        //---------------------------------------------------------------
        private void ClampFinishTime()
        {
            // １着のキャラの着タイムが、race_course_set.csvに登録されている「最遅タイムより遅い」or「最早タイムより早い」なら着タイムを改竄する。
            int topHorseIndex = _horseIndexByFinishOrder[0];
            var topHorseInfo = _horseRaceInfos[topHorseIndex];

            float finishTimeNew = topHorseInfo.FinishTimeScaled;
            bool isClamped = RaceUtil.CalcClampedFinishTime(
                ref finishTimeNew, 
                raceInstanceId:_raceInfo.RaceInstanceId,
                randomSeed:_raceInfo.RandomSeed); // お問い合わせ#0015:改竄ランダム幅を決める乱数シードはレース見るごとに変わるようにシミュレートの乱数シードを使う。

            // 改竄が不要だった場合は処理不要。
            if (!isClamped)
            {
                return;
            }
            
            // １着のキャラの着タイム改竄。
            float finishTimeAdd = finishTimeNew - topHorseInfo.FinishTimeScaled;
            topHorseInfo.FinishTimeScaled = finishTimeNew;

            // ２着以下は１着のキャラの着タイム増減と同じ量を加算していく。
            for (int i = 1; i < _horseIndexByFinishOrder.Length; ++i)
            {
                int horseIndex = _horseIndexByFinishOrder[i];
                var horseInfo = _horseRaceInfos[horseIndex];
                horseInfo.FinishTimeScaled += finishTimeAdd;
            }
        }
        
        /// <summary>
        /// キャラの重なり防止。
        /// </summary>
        private void UpdateOverlap()
        {
            // 全キャラをレーン内側から外側に向かってソート。
            var horseInfoSorted = CreateSortedByLane();

            // 最も内側のキャラは押し出しチェックが行われず、レーン再検索も不要なのでフラグを降ろしておく。
            horseInfoSorted[0].IsForceCheckNextLaneOneTime = false;

            // レーン外側にいるキャラが内側に重なりそうな位置にいる場合は、レーン外側に押し出す。
            // この処理を内側→外側に向かって行っていくことで全員の重なりを防止する。
            for( int i = 1; i < horseInfoSorted.Count; ++i )
            {
                var horseIn = horseInfoSorted[i-1];
                var horseOut = horseInfoSorted[i];

                // 距離、レーンが重なりと見なされる範囲内にあるかどうかをチェック。
                var distanceGapAbs = Math.Abs( horseOut.GetDistance() - horseIn.GetDistance() );
                var laneGapAbs = Math.Abs( horseOut.GetLaneDistance() - horseIn.GetLaneDistance() );
                if( !IsOverlap( distanceGapAbs, laneGapAbs ) )
                {
                    continue;
                }

                // このキャラは内側のキャラに重なっているため外に押し出す。
                // 今のレーン、レーン目的地は適切ではないので、再検索をリクエストする。
                horseOut.SetLaneDistance( horseIn.GetLaneDistance() + OVERLAP_LANE_GAP_THRESHOLD );
                horseOut.IsForceCheckNextLaneOneTime = true;
            }
        }

        /// <summary>
        /// レーン内側→外側に向かってソートされたリスト生成。
        /// </summary>
        private List<IHorseRaceInfoSimulate> CreateSortedByLane()
        {
            var horseInfoSorted = _horseRaceInfos.ToList();
            horseInfoSorted.Sort( (a, b) => (int)a.GetLaneDistance()*10000 - (int)b.GetLaneDistance()*10000 );
            return horseInfoSorted;
        }

        /// <summary>
        /// 距離差、レーン差が重なりと見なす範囲内かどうか。
        /// </summary>
        /// <returns>重なり範囲に入っていればtrue。</returns>
        private static bool IsOverlap( float distanceGapAbs, float laneGapAbs )
        {
            if( distanceGapAbs >= OVERLAP_DISTANCE_GAP_THRESHOLD )
            {
                return false;
            }
            if( laneGapAbs >= OVERLAP_LANE_GAP_THRESHOLD )
            {
                return false;
            }
            return true;
        }

#if CYG_DEBUG
        //---------------------------------------------------------------
        /// <summary>
        /// キャラ同士が重なっていないかチェック。
        /// </summary>
        //---------------------------------------------------------------
        private void DbgCheckOverlap()
        {
            // 全キャラをレーン内側から外側に向かってソート。
            var horseInfoSorted = CreateSortedByLane();

            for( int i = 1; i < horseInfoSorted.Count; ++i )
            {
                var horseIn = horseInfoSorted[i-1];
                var horseOut = horseInfoSorted[i];

                // 距離、レーンが重なりと見なされる範囲内にあるかどうかをチェック。
                var distanceGapAbs = Math.Abs( horseOut.GetDistance() - horseIn.GetDistance() );
                var laneGapAbs = Math.Abs( horseOut.GetLaneDistance() - horseIn.GetLaneDistance() );
                if( !IsOverlap( distanceGapAbs, laneGapAbs ) )
                {
                    continue;
                }

                Debug.LogWarning( 
                    string.Format( "[重なり検知 エンジニア日比までご連絡ください][{0}秒目]{1}番と{2}番 DistanceGap={3} LaneGap={4}",
                        RaceManagerSimulate.Instance.AccumulateTimeSinceStart,
                        horseIn.HorseIndex+1,
                        horseOut.HorseIndex+1,
                        distanceGapAbs,
                        laneGapAbs ) );
            }
        }
#endif
        
        /// <summary>
        /// 全キャラ敗因分析情報構築。
        /// </summary>
        private void CreateDefeat()
        {
            if( _isDefeatChecked )
            {
                return;
            }
            _isDefeatChecked = false;

            foreach( var horse in _horseRaceInfos )
            {
                horse.CreateDefeat();
            }
        }

        /// <summary>
        /// 全員ゴール時の通知。
        /// </summary>
        private void CallOnAllFinish()
        {
            foreach(var horse in _horseRaceInfos)
            {
                horse.OnAllFinish();
            }
            foreach(var team in _teamInfoList)
            {
                team.OnAllFinish();
            }
        }
        
        //---------------------------------------------------------------
        public void AllHorseLotTemptation()
        {
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].LotTemptation();
            }
        }

        //---------------------------------------------------------------
        private void UpdateLastOrderTime( float deltaTime )
        {
            var lastHorse = GetLastHorseInfo();
            if( null == lastHorse )
            {
                return;
            }
            lastHorse.LastOrderTime += deltaTime;
        }

        //---------------------------------------------------------------
        private void CacheSameRunningStyleHorses()
        {
            foreach (var style in Gallop.RaceDefine.HORSE_USABLE_RUNNING_STYLE_ARRAY)
            {
                int index = (int)(style - 1);
                if( index < 0 || index >= _sameRunningStyleHorses.Length )
                {
                    continue;
                }

                _sameRunningStyleHorses[index] = _horseRaceInfos.Where(h => h.RunningStyle == style).ToArray();
                _sameRunningStyleCount[index] = _sameRunningStyleHorses[index].Length;
            }
        }

        //---------------------------------------------------------------
        private void CacheMostForwardRunningStyleHorses()
        {
            foreach(var styleHorseArray in _sameRunningStyleHorses)
            {
                if(styleHorseArray != null && styleHorseArray.Length > 0)
                {
                    // ついでに一番前の走法もキャッシュ。
                    _mostForwardRunningStyle = styleHorseArray[0].RunningStyle; 
                    _mostForwardRunningStyleHorseArray = styleHorseArray;
                    return;
                }
            }

            Debug.LogWarning("一番前の走法のキャラが見つかりませんでした");
        }

        //---------------------------------------------------------------
        private void UpdateRunningStyleFirstAndLastOrderHorses()
        {
            Debug.Assert(_sameRunningStyleHorses.Length == _runningStyleLastOrderHorses.Length);
            Debug.Assert(_sameRunningStyleHorses.Length == _runningStyleTopOrderHorses.Length);

            for (int i = 0; i < _sameRunningStyleHorses.Length; ++i)
            {
                // この走法のキャラを現在順位で昇順にソート。
                System.Array.Sort(_sameRunningStyleHorses[i], (a, b) => a.CurOrder - b.CurOrder);
                
                // この走法のトップ/最後尾キャラを保存。
                _runningStyleTopOrderHorses[i] = _sameRunningStyleHorses[i].FirstOrDefault();
                _runningStyleLastOrderHorses[i] = _sameRunningStyleHorses[i].LastOrDefault();
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 掛かっているキャラに関する情報のキャッシュ。
        /// </summary>
        //---------------------------------------------------------------
        private void CacheTemptationHorse()
        {
            Debug.Assert(_sameRunningStyleHorses.Length == _runningStyleTemptationHorseArray.Length);

            _temptationHorseList.Clear();
            
            for (int styleIndex = 0; styleIndex < _sameRunningStyleHorses.Length; ++styleIndex)
            {
                _runningStyleTemptationHorseArray[styleIndex].Clear();
                
                for (int horse = 0; horse < _sameRunningStyleHorses[styleIndex].Length; ++horse)
                {
                    var horseInfo = _sameRunningStyleHorses[styleIndex][horse];
                    if (horseInfo.IsTemptation)
                    {
                        // この走法（styleIndex）を選んでいるキャラで掛かっているキャラ。
                        _runningStyleTemptationHorseArray[styleIndex].Add(horseInfo);

                        // 走法問わず掛かっているキャラはここに入れる。
                        _temptationHorseList.Add(horseInfo);
                    }
                }
            }
        }

        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetTemptationHorseList()
        {
            return _temptationHorseList;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 直近の回復スキル発動キャラリストの更新
        /// </summary>
        /// <returns></returns>
        //---------------------------------------------------------------
        private void CacheActivateHealSkillHorseHistoryList()
        {
            // スキル発動者から検索
            foreach (var activateSkillHorseData in _prevActivateSkillHorseDict)
            {
                var horsePrevActivateSkillList = activateSkillHorseData.Value;
                if (horsePrevActivateSkillList == null || horsePrevActivateSkillList.Count <= 0)
                {
                    continue;
                }

                for (int i = 0, cnt = horsePrevActivateSkillList.Count; i < cnt; i++)
                {
                    if (!horsePrevActivateSkillList[i].IsTargetSkill(Gallop.SkillDefine.SkillAbilityType.HpRate))
                    {
                        continue;
                    }

                    // もし既に発動者リストに載っているなら一旦削除して末尾に挿入する
                    if (_activateHealSkillHorseList.Contains(activateSkillHorseData.Key))
                    {
                        _activateHealSkillHorseList.Remove(activateSkillHorseData.Key);
                    }

                    _activateHealSkillHorseList.Add(activateSkillHorseData.Key);
                    // このキャラは回復スキルを発動していることが分かったのでこれ以上調べる必要なし
                    break;
                }
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// キャッシュしていたスキル発動キャラリストを返す
        /// </summary>
        /// <returns></returns>
        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetActivateHealSkillHorseHistoryList()
        {
            return _activateHealSkillHorseList;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// このフレームでのスキル発動者と発動スキルリストを更新する
        /// </summary>
        /// <returns></returns>
        //---------------------------------------------------------------
        private void CachePrevActivateSkillHorseDict()
        {
            _prevActivateSkillHorseDict.Clear();
            // スキル発動者から検索
            foreach (var horseRaceInfo in _horseRaceInfos)
            {
                var horsePrevActivateSkillList = horseRaceInfo.SkillManager.GetPrevActivateSkillDetailList();
                if (horsePrevActivateSkillList == null || horsePrevActivateSkillList.Count <= 0)
                {
                    continue;
                }

                // ここで持つリストはディープコピーにしたいのでnewでリスト生成しています
                _prevActivateSkillHorseDict.Add(horseRaceInfo, new List<ISkillDetail>(horsePrevActivateSkillList));
            }
        }

        /// <summary>
        /// キャッシュしていたスキル発動者と発動スキルのテーブルを返す
        /// </summary>
        /// <returns></returns>
        public Dictionary<IHorseRaceInfoSimulate, List<ISkillDetail>> GetPrevActivateSkillHorseDict()
        {
            return _prevActivateSkillHorseDict;
        }
        
        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetRunningStyleOrderHorse(Gallop.RaceDefine.RunningStyle runningStyle, int order)
        {
            int index = (int)runningStyle - 1;
            if (index < 0 || index >= _sameRunningStyleHorses.Length)
            {
                Debug.LogWarning("指定キャラは存在しません。走法=" + runningStyle);
                return null;
            }
            
            if (order < 0 || order >= _sameRunningStyleHorses[index].Length)
            {
                Debug.LogWarning("指定順位のキャラは存在しません。order=" + order);
                return null;
            }
            
            return _sameRunningStyleHorses[index][order];
        }
        
        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetRunningStyleTopOrderHorse(Gallop.RaceDefine.RunningStyle runningStyle)
        {
            int index = (int)runningStyle - 1;
            if (index < 0 || index >= _runningStyleTopOrderHorses.Length)
            {
                Debug.LogWarning("指定キャラは存在しません。走法=" + runningStyle);
                return null;
            }
            return _runningStyleTopOrderHorses[index];
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetRunningStyleLastOrderHorse( Gallop.RaceDefine.RunningStyle runningStyle )
        {
            int index = (int)runningStyle-1;
            if( index < 0 || index >= _runningStyleLastOrderHorses.Length )
            {
                Debug.LogWarning("指定キャラは存在しません。走法=" + runningStyle);
                return null;
            }
            return _runningStyleLastOrderHorses[ index ];
        }

        //---------------------------------------------------------------
        public Gallop.RaceDefine.RunningStyle GetMostForwardRunningStyle()
        {
            return _mostForwardRunningStyle;
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetMostForwardRunningStyleHorseArray()
        {
            return _mostForwardRunningStyleHorseArray;
        }

        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetRunningStyleTemptationHorse(Gallop.RaceDefine.RunningStyle runningStyle)
        {
            int index = (int)runningStyle - 1;
            if (index < 0 || index >= _runningStyleTemptationHorseArray.Length)
            {
                Debug.LogWarning("指定キャラは存在しません。走法=" + runningStyle);
                return _emptyHorseList;
            }
            return _runningStyleTemptationHorseArray[index];
        }

        //---------------------------------------------------------------
        private void CalcParamOrder()
        {
            HorseParameterOrderCalculator.CalcGutsOrder(_horseRaceInfos);
            HorseParameterOrderCalculator.CalcWizOrder(_horseRaceInfos);
        }

        //---------------------------------------------------------------
        public void RecalcParams()
        {
            for (int i = 0, cnt = _horseRaceInfos.Length; i < cnt; ++i)
            {
                _horseRaceInfos[i].RecalcParams();
            }

            CalcParamOrder();

            for (int i = 0, cnt = _horseRaceInfos.Length; i < cnt; ++i)
            {
                _horseRaceInfos[i].RecalcParamsPost();
            }
        }

        //---------------------------------------------------------------
        public void CalcDelayTime()
        {
            float startDelayMax = _paramDefine.startDelayMax;
            for (int i = 0, cnt = _horseRaceInfos.Length; i < cnt; ++i)
            {
                _horseRaceInfos[i].CalcDelayTime(startDelayMax);
            }
        }

        //---------------------------------------------------------------
        public void AllHorseLotActivateSkill()
        {
            for (int i = 0, cnt = _horseRaceInfos.Length; i < cnt; ++i)
            {
                _horseRaceInfos[i].LotActivateSkill();
            }
        }

        //---------------------------------------------------------------
        private void AllHorseCheckSkillTriggerAndActivate()
        {
            // グループごとに順番に判定
            // 1グループ目
            for (int i = 0, cnt = _horseRaceInfos.Length; i < cnt; ++i)
            {
                _horseRaceInfos[i].CheckSkillTriggerAndActivateFirstGroup();
            }

            // 1グループ目の後処理
            for (int i = 0, cnt = _horseRaceInfos.Length; i < cnt; ++i)
            {
                _horseRaceInfos[i].PostUpdateSkillFirstGroup();
            }

            // 2グループ目
            for (int i = 0, cnt = _horseRaceInfos.Length; i < cnt; ++i)
            {
                _horseRaceInfos[i].CheckSkillTriggerAndActivateSecondGroup();
            }
        }

        //---------------------------------------------------------------
        public void AllHorseClearSkills()
        {
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].ClearSkills();
            }
        }

        //---------------------------------------------------------------
        public int GetRunningStyleCount(Gallop.RaceDefine.RunningStyle style)
        {
            int index = (int)style-1;
            if( index < 0 || index >= _sameRunningStyleCount.Length )
            {
                Debug.LogError("指定走法キャラ人数はキャッシュされていません。走法=" + style);
                return 0;
            }
            return _sameRunningStyleCount[ index ];
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetRunningStyleHorses(Gallop.RaceDefine.RunningStyle style)
        {
            int index = (int)style-1;
            if( index < 0 || index >= _sameRunningStyleHorses.Length )
            {
                Debug.LogError("指定走法キャラはキャッシュされていません。走法=" + style);
                return new IHorseRaceInfoSimulate[0];
            }
            return _sameRunningStyleHorses[ index ];
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetHorseRaceInfos() { return _horseRaceInfos; }

        //---------------------------------------------------------------
        public int GetFirstHorseIndex()
        {
            return _orderCalculator.GetHorseIndexByOrderSimulate( 0 );
        }

        //---------------------------------------------------------------
        public int GetLastHorseIndex()
        {
            return _orderCalculator.GetHorseIndexByOrderSimulate( _horseRaceInfos.Length - 1 );
        }

        //---------------------------------------------------------------
        public int GetHorseIndexByOrder(int order)
        {
            return _orderCalculator.GetHorseIndexByOrderSimulate( order );
        }

        //---------------------------------------------------------------
        public int GetHorseIndexByFinishOrder(int finishOrder)
        {
            Debug.Assert( _isFinishOrderCalculated, "着順計算前にアクセスしている。" );

            if( !CheckFinishOrderValid( finishOrder ) )
            {
                Debug.LogError( "不正な着順指定。finishOrder=" + finishOrder );
                return 0;
            }
            return _horseIndexByFinishOrder[ finishOrder ];
        }

        //---------------------------------------------------------------
        public int GetHorseIndexByPopularity(int popularityOrder)
        {
            popularityOrder = RaceUtilMath.Clamp(popularityOrder, 0, _raceInfo.HorseIndexByPopularity.Length - 1);
            return _raceInfo.HorseIndexByPopularity[popularityOrder];
        }

        //---------------------------------------------------------------
        public bool CheckOrderValid(int order)
        {
            return (order >= 0 && order < _horseRaceInfos.Length);
        }

        //---------------------------------------------------------------
        public bool CheckFinishOrderValid(int finishOrder)
        {
            Debug.Assert( _isFinishOrderCalculated, "着順計算前にアクセスしている。" );
            return ( finishOrder >= 0 && finishOrder < _horseIndexByFinishOrder.Length );
        }

        //---------------------------------------------------------------
        public bool CheckPopularityValid(int popularityOrder)
        {
            return (popularityOrder >= 0 && popularityOrder < _raceInfo.HorseIndexByPopularity.Length);
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseInfo(int horseArrayIndex)
        {
            if (horseArrayIndex < 0 || _horseRaceInfos.Length <= horseArrayIndex)
            {
                return null;
            }
            return _horseRaceInfos[horseArrayIndex];
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetFirstHorseInfo()
        {
            return GetHorseInfo(GetFirstHorseIndex());
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetLastHorseInfo()
        {
            return GetHorseInfo(GetLastHorseIndex());
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseInfoByOrder(int order)
        {
            return GetHorseInfo(GetHorseIndexByOrder(order));
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseInfoByFinishOrder(int finishOrder)
        {
            return _horseRaceInfos[GetHorseIndexByFinishOrder(finishOrder)];
        }

        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate GetHorseByPopularity(int popularity)
        {
            return _horseRaceInfos[GetHorseIndexByPopularity(popularity)];
        }

        //---------------------------------------------------------------
        public List<HorseTeamInfo> GetTeamInfoList()
        {
            return _teamInfoList;
        }
        
        //---------------------------------------------------------------
        public IHorseRaceInfoSimulate[] GetTeamMemberHorseInfoArray(int teamId)
        {
            foreach (var teamInfo in _teamInfoList)
            {
                if (teamInfo.TeamId == teamId)
                {
                    return teamInfo.TeamMemberArraySimulate;
                }
            }
            return _emptyTeamMemberArray;
        }

        /// <summary>
        /// チーム情報の生成。
        /// </summary>
        public static List<HorseTeamInfo> CreateTeamInfo(IHorseRaceInfoSimulate[] horseInfoArray, int selfEvaluate, int opponentEvaluate, int supportCardScoreBonus)
        {
            var retList = new List<HorseTeamInfo>(2);
            var query = horseInfoArray.GroupBy(h => h.TeamId);
            foreach (var horseByTeam in query)
            {
                int teamId = horseByTeam.Key;
                // 無所属のチームはチームリストには加えない。
                if (teamId == Gallop.RaceDefine.TEAM_ID_NULL)
                {
                    continue;
                }
                
                // 編成番号で昇順に並べてからリストに格納する。
                var teamMemberArray = horseByTeam.ToArray();
                System.Array.Sort(teamMemberArray, (a, b) => { return a.TeamMemberId - b.TeamMemberId; });
                
                retList.Add(new HorseTeamInfo(
                    teamId,
                    teamMemberArray,
                    selfEvaluate:selfEvaluate,
                    opponentEvaluate:opponentEvaluate,
                    supportCardScoreBonus:supportCardScoreBonus));
            }
            return retList;
        }

        public void OnSkip()
        {
            for (int i = 0; i < _horseRaceInfos.Length; i++)
            {
                _horseRaceInfos[i].OnSkip();
            }
        }
    }
}
#endif

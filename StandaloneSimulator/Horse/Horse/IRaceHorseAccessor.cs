#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中のキャラへのアクセス用インターフェース。
    /// </summary>
    /// <remarks>ここでは取得系のアクセッサのみを提供する。副作用のある関数は提供しない。</remarks>
    //-------------------------------------------------------------------
    public interface IRaceHorseAccessor
    {
        /// <summary>
        /// 初期化。
        /// </summary>
        void Init(RaceInfo raceInfo, Gallop.RaceParamDefine paramDefine);
        /// <summary>
        /// 解放。
        /// </summary>
        void Release();
        
        /// <summary>
        /// 全キャラ出走前初期化。
        /// </summary>
        void InitBeforeStart();
        
        /// <summary>
        /// 最初のキャラ更新。
        /// </summary>
        bool UpdateFirstHorse();
        
        /// <summary>
        /// キャラ更新。
        /// </summary>
        bool UpdateHorse(float deltaTime);
        
        #region <HorseIndexアクセス>
        /// <summary>
        /// １位馬のHorseIndex取得。
        /// </summary>
        int GetFirstHorseIndex();
        /// <summary>
        /// 最下位馬のHorseIndex取得。
        /// </summary>
        int GetLastHorseIndex();
        /// <summary>
        /// 指定順位の馬のHorseIndex取得。
        /// </summary>
        int GetHorseIndexByOrder(int order);
        /// <summary>
        /// 指定着順の馬のHorseIndex取得。
        /// </summary>
        int GetHorseIndexByFinishOrder(int order);
        /// <summary>
        /// 指定人気順位の馬のHorseIndex取得。
        /// </summary>
        int GetHorseIndexByPopularity( int popularityOrder );
        #endregion <HorseIndexアクセス>
            
        #region <HorseInfoアクセス>
        /// <summary>
        /// HorseRaceInfo配列取得。
        /// </summary>
        IHorseRaceInfoSimulate[] GetHorseRaceInfos();
        /// <summary>
        /// 指定HorseIndexのHorseRaceInfo取得。
        /// </summary>
        IHorseRaceInfoSimulate GetHorseInfo(int horseArrayIndex);
        /// <summary>
        /// １位馬のHorseRaceInfo取得。
        /// </summary>
        IHorseRaceInfoSimulate GetFirstHorseInfo();
        /// <summary>
        /// 最下位馬のHorseRaceInfo取得。
        /// </summary>
        IHorseRaceInfoSimulate GetLastHorseInfo();
        /// <summary>
        /// 指定順位の馬のHorseRaceInfo取得。
        /// </summary>
        IHorseRaceInfoSimulate GetHorseInfoByOrder(int order);
        /// <summary>
        /// 指定着順の馬のHorseRaceInfo取得。
        /// </summary>
        IHorseRaceInfoSimulate GetHorseInfoByFinishOrder(int finishOrder);
        /// <summary>
        /// 指定人気順の馬のHorseRaceInfo取得。
        /// </summary>
        IHorseRaceInfoSimulate GetHorseByPopularity(int popularity);

        /// <summary>
        /// 指定チームに所属するHorseRaceInfo取得。
        /// </summary>
        /// <param name="teamId">チームId。</param>
        IHorseRaceInfoSimulate[] GetTeamMemberHorseInfoArray(int teamId);

        /// <summary>
        /// チームリスト取得。
        /// </summary>
        /// <returns>チームが存在しない場合は空リストを返す。</returns>
        List<HorseTeamInfo> GetTeamInfoList();
        #endregion <HorseInfoアクセス>

        #region <Utility>
        /// <summary>
        /// キャラ数取得。
        /// </summary>
        int GetHorseNumber();
        /// <summary>
        /// 最後尾の順位を取得。
        /// </summary>
        int LastOrder { get; }
        /// <summary>
        /// 指定の走法の馬数取得。
        /// </summary>
        int GetRunningStyleCount(Gallop.RaceDefine.RunningStyle style);
        /// <summary>
        /// 指定の走法のキャラを取得。
        /// </summary>
        IHorseRaceInfoSimulate[] GetRunningStyleHorses( Gallop.RaceDefine.RunningStyle style );

        /// <summary>
        /// 指定の走法の中での指定順位のキャラを取得。
        /// </summary>
        /// <param name="order">全体の中での順位ではなく、指定走法内での順位。</param>
        IHorseRaceInfoSimulate GetRunningStyleOrderHorse(Gallop.RaceDefine.RunningStyle runningStyle, int order);
        /// <summary>
        /// 指定の走法のトップのキャラを取得。
        /// </summary>
        /// <returns>指定走法を選択しているキャラがいない場合はnull。</returns>
        IHorseRaceInfoSimulate GetRunningStyleTopOrderHorse(Gallop.RaceDefine.RunningStyle runningStyle);
        /// <summary>
        /// 指定の走法の最後尾のキャラを取得。
        /// </summary>
        /// <returns>指定走法を選択しているキャラがいない場合はnull。</returns>
        IHorseRaceInfoSimulate GetRunningStyleLastOrderHorse( Gallop.RaceDefine.RunningStyle runningStyle );
        /// <summary>
        /// 最も前の走法を取得。
        /// </summary>
        Gallop.RaceDefine.RunningStyle GetMostForwardRunningStyle();
        /// <summary>
        /// 最も前の走法を選んでいるキャラを取得。
        /// </summary>
        /// <remarks>逃げ→先行→差し→追い、の順で調べる。</remarks>
        IHorseRaceInfoSimulate[] GetMostForwardRunningStyleHorseArray();
        /// <summary>
        /// 指定の走法の興奮状態のキャラを取得。
        /// </summary>
        List<IHorseRaceInfoSimulate> GetRunningStyleTemptationHorse(Gallop.RaceDefine.RunningStyle runningStyle);

        /// <summary>
        /// 興奮状態のキャラを取得。
        /// </summary>
        /// <returns></returns>
        List<IHorseRaceInfoSimulate> GetTemptationHorseList();

        /// <summary>
        /// 前のフレームでスキル発動したウマ娘とその発動スキルリストのテーブル
        /// </summary>
        /// <returns></returns>
        Dictionary<IHorseRaceInfoSimulate, List<ISkillDetail>> GetPrevActivateSkillHorseDict();
        
        /// <summary>
        /// 過去に回復スキルを発動したキャラを重複しないリストで発動順で取得
        /// </summary>
        /// <returns></returns>
        List<IHorseRaceInfoSimulate> GetActivateHealSkillHorseHistoryList();
        
        /// <summary>
        /// 順位が正常な範囲に収まっているかどうか。
        /// </summary>
        bool CheckOrderValid( int order );
        /// <summary>
        /// 着順が正常な範囲に収まっているかどうか。
        /// </summary>
        bool CheckFinishOrderValid( int finishOrder );
        /// <summary>
        /// 人気順が正常な範囲に収まっているかどうか。
        /// </summary>
        bool CheckPopularityValid( int popularityOrder );
        #endregion <Utility>
    }
}

#endif

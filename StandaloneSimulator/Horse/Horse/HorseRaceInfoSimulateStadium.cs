#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中馬制御：チーム競技場シミュレーション用。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseRaceInfoSimulateStadium : HorseRaceInfoSimulate
    {
        //---------------------------------------------------------------
        // 定数。
        //---------------------------------------------------------------

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private HorseScoreCalculator _scoreCalc;

        public override List<ScoreData> ScoreList => _scoreCalc.ScoreList;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseRaceInfoSimulateStadium(HorseRaceInfoSimulate.InitDesc desc) : base(desc)
        {
            _scoreCalc = new HorseScoreCalculator(
                this,
                _horseAccessor,
                RaceManagerSimulate.Instance,
                RaceManagerSimulate.Instance,
                _raceInfo.BorderTimeScaled);
        }

        //---------------------------------------------------------------
        public override void OnAllFinish()
        {
            base.OnAllFinish();

            CalcScoreOnAllFinish();
        }

        //---------------------------------------------------------------
        private void CalcScoreOnAllFinish()
        {
            //-----------------------------------------------------------
            // 全員ゴールした時のスコア加算。
            //-----------------------------------------------------------
            _scoreCalc.OnAllFinish(
                _raceInfo.SelfEvaluate,
                _raceInfo.OpponentEvaluate, 
                _raceInfo.SupportCardScoreBonus);

            //-----------------------------------------------------------
            // ここまでで展開スコアの加算は終えている。
            //-----------------------------------------------------------
        #if CYG_DEBUG
            // デバッグ情報（スコア合計値）保存用に呼び出す。
            CalcScoreTotal();
        #endif
        }

        //---------------------------------------------------------------
        public override void AddScoreSkillActivate(Gallop.SkillDefine.SkillRarity rarity, int level, int groupRate)
        {
            base.AddScoreSkillActivate(rarity, level, groupRate);
            _scoreCalc.AddScoreSkillActivate(rarity, level, groupRate);
        }
        
        //---------------------------------------------------------------
        public override int CalcScoreTotal()
        {
            int total = _scoreCalc.CalcTotal();
        #if CYG_DEBUG
            DbgTeamStadiumTotalScore = total;
        #endif
            return total;
        }

        //---------------------------------------------------------------
        public override bool Update(float deltaTime)
        {
            bool ret = base.Update(deltaTime);

            // スコア加算のために常時更新が必要なもの。
            _scoreCalc.Update(deltaTime);
            
            return ret;
        }
        
        //---------------------------------------------------------------
        protected override void OnTemptationStart()
        {
            base.OnTemptationStart();

            // 興奮開始時のスコア加算。
            _scoreCalc.AddScoreTemptationStart();
        }
        
        //---------------------------------------------------------------
        public override void CalcDelayTime(float max)
        {
            base.CalcDelayTime(max);
            
            // スタートダッシュ成功/失敗によるスコア加算。
            _scoreCalc.AddScoreGoodStart();
        } 
    }
}

#endif

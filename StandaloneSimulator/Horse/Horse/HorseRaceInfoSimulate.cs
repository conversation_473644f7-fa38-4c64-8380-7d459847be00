#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Numerics;
using System.Linq;
using System.Collections.Generic;
using Math = System.Math;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中キャラ制御。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseRaceInfoSimulate : IHorseRaceInfoSimulate
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>
        /// 初期化パラメータ。
        /// </summary>
        public class InitDesc
        {
            public RaceHorseSimulateData RawHorseData;
            public IRaceHorseAccessor HorseAccessor;
            public int HorseIndex;
            public int HorseNum;
            public Gallop.RaceDefine.CourseDistanceType DistanceType;
            public Gallop.RaceDefine.GroundType GroundType;
            public Gallop.RaceParamDefine ParamDefine;
            public RaceInfo RaceInfo;
            public bool IsFirstMoveLaneIsIn;
            public bool IsEnableFirstMoveLane;
            public float LaneDistanceMax;
            public float FirstMoveLanePointDistance;
        }
        
        private const int MOB_CHARA_ID = 1; // ModelLoader.MOB_CHARA_ID

        private const int NEAR_HORSE_AROUND_NUM = 4;
        private const float ORDER_IN_CHECK_TIME = 5;
        private const float ORDER_OUT_CHECK_TIME = ORDER_IN_CHECK_TIME;
        
        /// <summary>1位の時のorder</summary>
        public const int ORDER_1ST_NO = 0;

        /// <summary>前方視野範囲距離の最小。</summary>
        private const float VISIBLE_DISTANCE_MIN = 0;
                
        private const float OVERRUN_LANE_MOVE_SPEED_PER_SEC = 1.0f;

        /// <summary>
        /// 距離に応じたタイプ
        /// </summary>
        public enum DistanceDivision
        {
            FirstHalf,    // 前半
            SecondHalf,   // 後半
        }
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>このレースのRaceInfo</summary>
        protected RaceInfo _raceInfo;
        /// <summary>HorseData生成の元になっているRaceHorseSimulateData</summary>
        private readonly RaceHorseSimulateData _rawHorseData = null;
        /// <summary>このキャラを制御するAI</summary>
        private IHorseRaceAI _horseRaceAI;
        /// <summary>現在のPhase</summary>
        private Gallop.RaceDefine.HorsePhase _phase = Gallop.RaceDefine.HorsePhase.Start;

        /// <summary>人気。0が１番人気。</summary>
        public int Popularity { get; private set; }
        /// <summary>人気計算中間値。左の印のランク。0が1位。</summary>
        public int PopularityRankLeft { get; private set; }
        /// <summary>人気計算中間値。中央の印のランク。0が1位。</summary>
        public int PopularityRankCenter { get; private set; }
        /// <summary>人気計算中間値。右の印のランク。0が1位。</summary>
        public int PopularityRankRight { get; private set; }

    #if CYG_DEBUG
        /// <summary>
        /// デバッグ用に外部から人気設定。引数には1~の値を渡すこと。
        /// </summary>
        public void DbgSetPopularity(int popularity) { Popularity = popularity - 1; }
        public void DbgSetPopularityMarkRank(int[] popularityMarkRank)
        {
            PopularityRankLeft = Math.Max(0, popularityMarkRank[(int)Gallop.RaceDefine.PopularityMark.Left] - 1);
            PopularityRankCenter = Math.Max(0, popularityMarkRank[(int)Gallop.RaceDefine.PopularityMark.Center] - 1);
            PopularityRankRight = Math.Max(0, popularityMarkRank[(int)Gallop.RaceDefine.PopularityMark.Right] - 1);
        }
    #endif
        
        /// <summary>チーム対抗戦のチームランク。</summary>
        public int SingleModeTeamRank => _rawHorseData.team_rank;
        /// <summary>育成レースの勝利数。</summary>
        public int SingleModeWinCount => _rawHorseData.single_mode_win_count;
        
#if GALLOP
        public int MobId => _rawHorseData.mob_id;
#endif

        /// <summary>走法取得。</summary>
        public Gallop.RaceDefine.RunningStyle RunningStyle => (Gallop.RaceDefine.RunningStyle)_rawHorseData.running_style;
        /// <summary>特殊走法初期化済みかどうか。</summary>
        private bool _isRunningStyleExInitialized;
        /// <summary>特殊走法。</summary>
        private Gallop.RaceDefine.RunningStyleEx _runningStyleEx = Gallop.RaceDefine.RunningStyleEx.None;
        public Gallop.RaceDefine.RunningStyleEx RunningStyleEx
        {
            get
            {
                Debug.Assert(_isRunningStyleExInitialized, "特殊走法に初期化前にアクセスしている");
                return _runningStyleEx;
            }
            set
            {
                _isRunningStyleExInitialized = true;
                _runningStyleEx = value;
            }
        }
        
        /// <summary>やる気で補正された基礎ステータス管理</summary>
        private readonly IRaceParameter _raceParam;

    #if CYG_DEBUG
        public float DbgSpeedOnStart { get; private set; }
        public float DbgStaminaOnStart { get; private set; }
        public float DbgPowOnStart { get; private set; }
        public float DbgGutsOnStart { get; private set; }
        public float DbgWizOnStart { get; private set; }
    #endif

        /// <summary> 所持スキル。</summary>
        public RaceHorseSkillData[] SkillArray => _rawHorseData.skill_array;
        /// <summary>所持アイテム。</summary>
        public int[] ItemIdArray => _rawHorseData.item_id_array;
        
        /// <summary>今回のレースで適用される距離適性。</summary>
        public Gallop.RaceDefine.ProperGrade ActiveProperDistance { get; private set; }
        /// <summary>今回のレースで適用される馬場適正。</summary>
        public Gallop.RaceDefine.ProperGrade ActiveProperGroundType { get; private set; }

        /// <summary>競技場モードチームId。0は無所属。</summary>
        public int TeamId => _rawHorseData.team_id;
        /// <summary>競技場モードチーム内メンバーId。1がエース。</summary>
        public int TeamMemberId => _rawHorseData.team_member_id;
        /// <summary>競技場モードのチーム内エースかどうか。</summary>
        public bool IsTeamAce => TeamMemberId == Gallop.RaceDefine.TEAM_ACE_MEMBER_ID;
        /// <summary>競技場モードの着順によって獲得したスコアの素点</summary>
        public int FinishOrderRawScore { get; set; }
        
        /// <summary>HorseIndex</summary>
        public int HorseIndex { get; private set; }

        /// <summary>カードId</summary>
        public int CardId => _rawHorseData.card_id;
        /// <summary>キャラId</summary>
        public int CharaId => _rawHorseData.chara_id;

        /// <summary>キャラ名</summary>
        public string CharaName { get; private set; } = "";
        /// <summary>ビューワーId</summary>
        public long ViewerId => _rawHorseData.viewer_id;
        /// <summary>育成済みキャラ識別Id</summary>
        public int SingleModeCharaId => _rawHorseData.single_mode_chara_id;
        /// <summary>育成ランク</summary>
        public int FinalGrade => _rawHorseData.final_grade;
        public int FanCount => _rawHorseData.fan_count;
        /// <summary>
        /// シナリオ専用データ配列
        /// </summary>
        public RaceHorseScenarioData[] ScenarioDataArray => _rawHorseData.scenario_data_array;
        /// <summary>枠番</summary>
        public int PostNumber { get; private set; }

        /// <summary>Hp</summary>
        private float _hp;
        /// <summary>最大Hp</summary>
        private float _maxHp = -1;
        /// <summary>レース中に一度でもHpが0になったかどうか。</summary>
        public bool IsHpEmptyOnRace { get; private set; }

        /// <summary>最低速度。レースの進行状況によって変動する</summary>
        public float MinSpeed { get; private set; }
        /// <summary>出走直後のスタートダッシュ終了後、通常走行中の最低速度。固定値</summary>
        private readonly float _raceDefaultMinSpeed;

        /// <summary>RecalcParamsでのパラメータ初期化済みかどうか</summary>
        public bool IsParamInitialized { get; private set; }

        /// <summary>馬場状態補正値が初期化済みかどうか</summary>
        private bool _isGroundModifierParamInitialized = false;
        /// <summary>馬場状態補正値</summary>
        private Gallop.RaceDefine.GroundModifierParam _groundModifierParam = Gallop.RaceDefine.GROUND_MODIFIER_PARAM_NULL;
        public Gallop.RaceDefine.GroundModifierParam GroundModeifierParam
        {
            get
            {
                Debug.Assert(_isGroundModifierParamInitialized);
                return _groundModifierParam;
            }
        }

        /// <summary>視野範囲基礎値</summary>
        private float _baseVisibleDistance;
        
        /// <summary>CourseSetによる基礎ステスピード補正倍率</summary>
        private float _baseSpeedCoef;
        /// <summary>CourseSetによる基礎ステ補正値初期化済みかどうか</summary>
        private bool _isCourseSetStatusInitialized;

        /// <summary>BaseSpeedを馬場状態補正やCourseSetなどで補正した値。初期化時に計算されレース中は変動しない</summary>
        private float _baseSpeedAdjusted;
        /// <summary>BaseStaminaを馬場状態補正やCourseSetなどで補正した値。初期化時に計算されレース中は変動しない</summary>
        private float _baseStaminaAdjusted;
        /// <summary>BasePowを馬場状態補正やCourseSetなどで補正した値。初期化時に計算されレース中は変動しない</summary>
        private float _basePowAdjusted;
        /// <summary>BaseGutsを馬場状態補正やCourseSetなどで補正した値。初期化時に計算されレース中は変動しない</summary>
        private float _baseGutsAdjusted;
        /// <summary>BaseWizを馬場状態補正やCourseSetなどで補正した値。初期化時に計算されレース中は変動しない</summary>
        private float _baseWizAdjusted;
        /// <summary>_base***Adjustedが初期化済みかどうか</summary>
        private bool _isBaseStatusAdjusted;

        /// <summary>現在速度(= _lastSelfSpeed + スキル増加分)</summary>
        private float _lastSpeed = 0.0f;
        /// <summary>現在の自力速度</summary>
        private float _lastSelfSpeed = 0;
        /// <summary> _lastSpeed のデバフスキルによる速度減少を考慮しない_lastSpeed </summary>
        /// <remarks> スタートダッシュ終了判定やスタミナ勝負の発動条件判定で使用する現在速度 </remarks>
        private float _lastSpeedWithoutDebuffSkill = 0.0f;
        /// <summary>現在のレーン移動速度</summary>
        private float _laneMoveSpeed = 0.0f;

        /// <summary>レーン移動し続けている時間</summary>
        public float MoveLaneContinueTime { get; private set; }
        /// <summary>レーン移動している方向</summary>
        public Gallop.RaceDefine.LaneDirection MoveLaneContinueDirection { get; private set; }

        /// <summary>最終コーナー通過済みかどうか。最終コーナーに入ったら以降true。最終コーナーがないコースではfalseのまま</summary>
        public bool IsFinalCorner { get; set; }

        /// <summary>現在速度がこの値に達したらスタートダッシュの急加速を止める。</summary>
        private float StartDashSpeedThreshold => _raceDefaultMinSpeed; 
        
        /// <summary>前フレームの_laneDistance</summary>
        public float PrevLaneDistance { get; private set; }
        /// <summary>現在のレーン</summary>
        private float _laneDistance;
        /// <summary>前フレームの距離</summary>
        private float _prevDistance = 0;
        // UpdateTopLeadMaxAmount()内のパラメーターをキャッシュ
        private readonly (float StartDistance, float EndDistance) _distanceTupleForTopLeadMaxAmountCheck;
        /// <summary>現在の距離</summary>
        private float _distance;

        /// <summary>前フレームの3D座標</summary>
        private Vector3 _prevPosition = RaceUtilMath.VECTOR3_ZERO;
        /// <summary>現在の3D座標</summary>
        private Vector3 _position = RaceUtilMath.VECTOR3_ZERO;

        /// <summary>現在の坂</summary>
        public Gallop.RaceDefine.SlopeType SlopeType { get; set; }
        /// <summary>坂の傾斜度</summary>
        public float SlopePer { get; set; }
        /// <summary>下り坂加速状態かどうか</summary>
        public bool IsDownSlopeAccelMode => _horseRaceAI.IsDownSlopeAccelMode;
        /// <summary>現在の坂が終わる距離</summary>
        public float SlopeEndDistance { get; set; }

        /// <summary>加速度計算機</summary>
        private HorseAccelCalculator _accelCalc;

        /// <summary>出遅れ時間。この値が0になったら動き始める</summary>
        public float DelayTime { get; private set; }
        /// <summary>出遅れ時間。保存用なので減少しない</summary>
        public float DelayTimeSaved { get; private set; }
    #if CYG_DEBUG
        public float DbgDelayTimeSavedBase { get; private set; }
    #endif

        /// <summary>距離適性によるスピード補正値</summary>
        public float ProperDistanceCoefSpeed { get; private set; }
        /// <summary>距離適性によるパワー補正値</summary>
        public float ProperDistanceCoefPow { get; private set; }
        /// <summary>走法適正による各種補正値</summary>
        public float ProperRunningStyleCoef { get; private set; }
        /// <summary>馬場適正による各種補正値</summary>
        public float ProperGroundTypeCoef { get; private set; }

        /// <summary>前回のフレームでの順位。0が1位。</summary>
        public int PrevOrder { get; set; }
        /// <summary>現在の順位。0が1位。</summary>
        public int CurOrder { get; set; }
        /// <summary>ラストスパート開始時の順位。0が1位。</summary>
        public int LastSpurtOrder { get; set; }
        /// <summary>最終直線開始時の順位。</summary>
        public int LastStraightOrder { get; set; }
        /// <summary>最終直線開始時の順位初期化済みかどうか</summary>
        public bool IsLastStraightOrderInitialized => LastStraightOrder != Gallop.RaceDefine.FINISH_ORDER_NULL;
        /// <summary>最終コーナー終了時の順位。最終コーナーを抜けていなければRaceDefine.FINAL_CORNER_END_ORDER_NULLが入っている</summary>
        public int FinalCornerEndOrder { get; private set; } = Gallop.RaceDefine.FINAL_CORNER_END_ORDER_NULL;
        /// <summary>最終コーナー終了時の順位が初期化済みかどうか</summary>
        public bool IsFinalCornerEndOrderInitialized => FinalCornerEndOrder != Gallop.RaceDefine.FINAL_CORNER_END_ORDER_NULL;
        /// <summary>最後尾にいた時間の累計時間</summary>
        public float LastOrderTime { get; set; }

        /// <summary>順位上昇計算機</summary>
        private IHorseOrderChangeCounter _orderChangeCounterUp;
        /// <summary>順位下降計算機</summary>
        private IHorseOrderChangeCounter _orderChangeCounterDown;

        public int CurOrderUpCountPhaseStart { get { return _orderChangeCounterUp.ChangeCountPhaseStart; } }
        public int CurOrderUpCountPhaseMiddle { get { return _orderChangeCounterUp.ChangeCountPhaseMiddle; } }
        public int CurOrderUpCountPhaseEndAfter { get { return _orderChangeCounterUp.ChangeCountPhaseEndAfter; } }
        public int CurOrderUpCountCorner { get { return _orderChangeCounterUp.ChangeCountCorner; } }
        public int CurOrderUpCountCornerPhaseEndAfter { get { return _orderChangeCounterUp.ChangeCountCornerPhaseEndAfter; } }
        public int CurOrderUpCountLastSpurt { get { return _orderChangeCounterUp.ChangeCountLastSpurt; } }
        public int CurOrderUpCountDistance1 { get { return _orderChangeCounterUp.ChangeCountDistance1; } }
        public int CurOrderUpCountFinalCornerAfter => _orderChangeCounterUp.ChangeCountFinalCornerAfter;
        public int CurOrderUpCountLaterHalf => _orderChangeCounterUp.ChangeCountLaterHalf;
        /// <summary> 1フレーム前と比べて順位が上昇したかどうか(クールダウンタイム込み) </summary>
        public bool IsCurOrderUp => _orderChangeCounterUp.IsOrderChangePerFrame;

        public int CurOrderDownCountPhaseStart { get { return _orderChangeCounterDown.ChangeCountPhaseStart; } }
        public int CurOrderDownCountPhaseMiddle { get { return _orderChangeCounterDown.ChangeCountPhaseMiddle; } }
        public int CurOrderDownCountPhaseEndAfter { get { return _orderChangeCounterDown.ChangeCountPhaseEndAfter; } }
        public int CurOrderDownCountCorner { get { return _orderChangeCounterDown.ChangeCountCorner; } }
        public int CurOrderDownCountCornerPhaseEndAfter { get { return _orderChangeCounterDown.ChangeCountCornerPhaseEndAfter; } }
        public int CurOrderDownCountLastSpurt { get { return _orderChangeCounterDown.ChangeCountLastSpurt; } }
        public int CurOrderDownCountDistance1 { get { return _orderChangeCounterDown.ChangeCountDistance1; } }
        public int CurOrderDownCountFinalCornerAfter => _orderChangeCounterDown.ChangeCountFinalCornerAfter;
        public int CurOrderDownCountLaterHalf => _orderChangeCounterDown.ChangeCountLaterHalf;
        /// <summary> 1フレーム前と比べて順位が下降したかどうか(クールダウンタイム込み) </summary>
        public bool IsCurOrderDown => _orderChangeCounterDown.IsOrderChangePerFrame;

        /// <summary>ｎ位以上になり続けているかどうかのフラグ</summary>
        private bool[] IsOrderInContinueArray = new bool[RaceUtilEnum.GetEnumElementCount<SkillDefine.OrderInType>()];
        /// <summary>ｎ位以下になり続けているかどうかのフラグ</summary>
        private bool[] IsOrderOutContinueArray = new bool[RaceUtilEnum.GetEnumElementCount<SkillDefine.OrderOutType>()];
        
        /// <summary>前フレームでの経過時間</summary>
        private float _prevElapsedTime = 0.0f;
        
        /// <summary>ゴール済みかどうか</summary>
        private bool _isFinished = false;

        /// <summary>スキル管理</summary>
        public SkillManagerSimulate SkillManager { get; private set; }
        /// <summary>スキル初期化済みかどうか</summary>
        public bool IsSkillInitialized { get; private set; }
        /// <summary>スキル効果受信機</summary>
        private readonly SkillModifierReceiver SkillModifierReceiver;

        /// <summary>Phase毎のスキル発動回数</summary>
        private Dictionary<Gallop.RaceDefine.HorsePhase, int> _activateSkillCountByPhase = new Dictionary<Gallop.RaceDefine.HorsePhase, int>();
        /// <summary>レアリティごとのスキル発動回数</summary>
        private int[] _activateSkillCountByRarity = new int[RaceUtilEnum.GetEnumElementCount<SkillDefine.SkillRarity>()];
        /// <summary>回復スキル発動回数</summary>
        private int _activateHealSkillCount;
        /// <summary>速度スキル発動回数</summary>
        private int _activateSpeedSkillCount;

        /// <summary> 特定の評価タググループのスキルを発動した回数(現状は1グループ想定) </summary>
        private int _activateSpecificTagGroupSkillCount;

        /// <summary> 特定のSkillAbilityTypeのスキルを発動した回数(現状は1グループ想定) </summary>
        private int _activateSpecificSkillAbilityTypeGroupSkillCount;
        
        /// <summary> 距離に応じたスキル発動回数テーブル </summary>
        private Dictionary<DistanceDivision, int> _activateSkillCountByDistance = new Dictionary<DistanceDivision, int>();
        
        /// <summary>ParamDefine</summary>
        public Gallop.RaceParamDefine ParamDefine { get; private set; }

        /// <summary>インが空いている時、インに寄るかどうか。</summary>
        public bool IsEmptyInMove { get; private set; }

        /// <summary>ラストスパート済みかどうか</summary>
        private bool _isLastSpurtPrev = false;
        
        /// <summary>着順。0~</summary>
        public int FinishOrder { get; set; }
        /// <summary>着タイム。FinishTimeRawを定数倍したもの。</summary>
        public float FinishTimeScaled { get; set; }
        /// <summary> FinishTimeScaledのdouble精度版。着順を計算するときだけ使用する。 </summary>
        public double FinishTimeScaledForFinishOrderCalc { get; set; }
        /// <summary>着タイム実時間。</summary>
        public float FinishTimeRaw { get; set; }
        /// <summary>前の着順のキャラとの着差時間。FinishTimeScaledの差分。</summary>
        public float FinishTimeDiffFromPrevHorse { get; set; }

        /// <summary>好スタートかどうか</summary>
        public bool IsGoodStart { get; private set; }
        /// <summary>悪スタートかどうか</summary>
        public bool IsBadStart { get; private set; }

        protected readonly IRaceHorseAccessor _horseAccessor;

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        //---------------------------------------------------------------
        public HorseRaceInfoSimulate(InitDesc desc)
        {
            _raceInfo = desc.RaceInfo;
            _rawHorseData = desc.RawHorseData;
            _horseAccessor = desc.HorseAccessor;
            HorseIndex = desc.HorseIndex;
            PostNumber = HorsePostNumberCalculator.GetPostNumber(HorseIndex, desc.HorseNum);
            
            ParamDefine = desc.ParamDefine;
            InitRunningStyleEx(_rawHorseData.skill_array);
            InitPopularity();

            // 今回のレースで適用される適正の決定。
            ActiveProperDistance = CalcProperGradeDistance(
                (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_distance_short,
                (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_distance_mile,
                (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_distance_middle,
                (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_distance_long,
                desc.DistanceType);
            ActiveProperGroundType = CalcProperGradeGroundType(
                (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_ground_turf,
                (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_ground_dirt,
                desc.GroundType);
            
            // 基礎ステータスの補正。
            _raceParam = new RaceParameter(
                _rawHorseData.speed,
                _rawHorseData.stamina,
                _rawHorseData.pow,
                _rawHorseData.guts,
                _rawHorseData.wiz,
                (Gallop.RaceDefine.Motivation)_rawHorseData.motivation,
                _rawHorseData.team_rank);
            
            IsCompeteTop = false;
            CompeteTopCount = 0;
            CompeteTopGroup = Gallop.RaceDefine.COMPETE_GROUP_NULL;
            CompeteTopAddTargetSpeed = 0;
            
            IsCompeteFight = false;
            CompeteFightCount = 0;
            CompeteFightGroup = Gallop.RaceDefine.COMPETE_GROUP_NULL;
            CompeteFightAddTargetSpeed = 0;
            CompeteFightAddAccel = 0;
            CompeteFightNearList = new List<Gallop.AroundHorse>();
            
            _baseVisibleDistance = ParamDefine.visibleDistance;
            _raceDefaultMinSpeed = _raceInfo.BaseSpeed * ParamDefine.Speed.MinSpeedRate;

            IsEmptyInMove = desc.IsFirstMoveLaneIsIn && desc.IsEnableFirstMoveLane;
            LaneDistanceMin = RaceInfo.LaneDistanceMin;
            LaneDistanceMax = desc.LaneDistanceMax;
            _firstMoveLanePointDistance = desc.FirstMoveLanePointDistance;

            PrevOrder = Gallop.RaceDefine.FINISH_ORDER_NULL;
            CurOrder = Gallop.RaceDefine.CUR_ORDER_DEFAULT;
            LastSpurtOrder = Gallop.RaceDefine.FINISH_ORDER_NULL;
            LastStraightOrder = Gallop.RaceDefine.FINISH_ORDER_NULL;

            SkillModifierReceiver = new SkillModifierReceiver();

            IsGoodStart = false;
            IsBadStart = false;

            SlopeType = Gallop.RaceDefine.SlopeType.Null;
            SlopePer = 0;
            SlopeEndDistance = 0;

            _distance = 0.0f;

            LastOrderTime = 0;

            _laneDistance = HorseInitialLaneCalculator.CalcInitialLane(
                HorseIndex,
                _raceInfo.InitialLaneType,
                _raceInfo.IsHalfGate,
                _raceInfo.LaneDistanceMax,
                _raceInfo.NumRaceHorses,
                _raceInfo.RaceCourseSet.RunOutSide
            );
            PrevLaneDistance = _laneDistance;

            CurCorner = Gallop.RaceDefine.CORNER_NULL;
            CornerEndDistance = 0;

            _orderChangeCounterUp = _CreateOrderChangeCounterUp();
            _orderChangeCounterDown = _CreateOrderChangeCounterDown();

            _InitActivateSkillCountByPhase();
            InitBlockSideMaxContinueTimeByPhase();

            InitProperDistance();
            InitProperRunningStyle( RunningStyle );
            InitProperGroundType();

            PositionKeepCount = 0;
            IsHpEmptyOnRace = false;

            IsClog = false;

            IsStartDash = true;

            _accelCalc = new HorseAccelCalculator(this, ParamDefine);

            // _baseSpeedAdjustedに正式な値が入る前に値を取得したい場合があるのでデフォルトの値自体は入れておく
            if (_raceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
            {
                _baseSpeedAdjusted = RaceUtilMath.Max(BaseSpeed, Gallop.RaceDefine.STATUS_MIN);
                _baseStaminaAdjusted = RaceUtilMath.Max(BaseStamina, Gallop.RaceDefine.STATUS_MIN);
                _basePowAdjusted = RaceUtilMath.Max(BasePow, Gallop.RaceDefine.STATUS_MIN);
                _baseGutsAdjusted = RaceUtilMath.Max(BaseGuts, Gallop.RaceDefine.STATUS_MIN);
                _baseWizAdjusted = RaceUtilMath.Max(BaseWiz, Gallop.RaceDefine.STATUS_MIN);
            }
            else
            {
                _baseSpeedAdjusted = RaceUtilMath.Clamp(BaseSpeed, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _baseStaminaAdjusted = RaceUtilMath.Clamp(BaseStamina, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _basePowAdjusted = RaceUtilMath.Clamp(BasePow, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _baseGutsAdjusted = RaceUtilMath.Clamp(BaseGuts, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _baseWizAdjusted = RaceUtilMath.Clamp(BaseWiz, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
            }
            
#if CYG_DEBUG
#if UNITY_EDITOR
            LastSpurtParam = new LastSpurtParam();
            _furlongStat = new HorseFurlongStat(_raceInfo.CourseFurlongNum);
#endif
            DbgDistanceDiffOnPositionKeepCalcArray = new float[desc.HorseNum];
#endif
            FinishOrder = Gallop.RaceDefine.FINISH_ORDER_NULL;
            FinishTimeScaled = Gallop.RaceDefine.FINISH_TIME_NULL;
            FinishTimeScaledForFinishOrderCalc = Gallop.RaceDefine.FINISH_TIME_NULL;
            FinishTimeRaw = Gallop.RaceDefine.FINISH_TIME_NULL;
            FinishTimeDiffFromPrevHorse = Gallop.RaceDefine.FINISH_DIFF_TIME_NULL;

            for (int i = 0; i < IsOrderInContinueArray.Length; i++)
            {
                IsOrderInContinueArray[i] = true;
            }
            for (int i = 0; i < IsOrderOutContinueArray.Length; i++)
            {
                IsOrderOutContinueArray[i] = true;
            }

            _behindHorseNearLaneContinueTimeArray = new float[ParamDefine.Skill.BehindNearParamArray.Length];
            
            // UpdateTopLeadMaxAmount()内のパラメーターはレース中固定なので、初期値の場合に1回だけ値を計算しておけばよい
            var courseDistancePerHundred = _raceInfo.CourseDistance / 100f;
            _distanceTupleForTopLeadMaxAmountCheck = (
                courseDistancePerHundred * ParamDefine.Skill.TopLeadAmountStartCourseRatePer,
                courseDistancePerHundred * ParamDefine.Skill.TopLeadAmountEndCourseRatePer);
            
        #if GALLOP
            var charaCommonData = Gallop.MasterDataUtil.MasterCharaAndMobAccessor.Create(CharaId, MobId);
            CharaName = charaCommonData.Name;
        #endif
        }

        private static Gallop.RaceDefine.ProperGrade CalcProperGradeDistance(
            Gallop.RaceDefine.ProperGrade properDistanceShort,
            Gallop.RaceDefine.ProperGrade properDistanceMile,
            Gallop.RaceDefine.ProperGrade properDistanceMiddle,
            Gallop.RaceDefine.ProperGrade properDistanceLong,
            Gallop.RaceDefine.CourseDistanceType distanceType)
        {
            switch (distanceType)
            {
                case Gallop.RaceDefine.CourseDistanceType.Short: return properDistanceShort;
                case Gallop.RaceDefine.CourseDistanceType.Mile: return properDistanceMile;
                case Gallop.RaceDefine.CourseDistanceType.Middle: return properDistanceMiddle;
                case Gallop.RaceDefine.CourseDistanceType.Long: return properDistanceLong;
                default:
                    Debug.LogWarning("不正な距離区分です。type=" + distanceType);
                    return Gallop.RaceDefine.ProperGrade.Null;
            }
        }
        
        private static Gallop.RaceDefine.ProperGrade CalcProperGradeGroundType(
            Gallop.RaceDefine.ProperGrade properGroundTurf, 
            Gallop.RaceDefine.ProperGrade properGroundDirt, 
            Gallop.RaceDefine.GroundType groundType)
        {
            switch(groundType)
            {
                case Gallop.RaceDefine.GroundType.Turf: return properGroundTurf;
                case Gallop.RaceDefine.GroundType.Dirt: return properGroundDirt;
                default:
                    Debug.LogWarning( "不正な地面種類です。type=" + groundType );
                    return Gallop.RaceDefine.ProperGrade.Null;
            }
        }
        
        /// <summary>
        /// 特殊走法初期化。
        /// </summary>
        private void InitRunningStyleEx(RaceHorseSkillData[] skillDataArray)
        {
            // 走法が「逃げ」で、スキル「大逃げ」を所持しているなら、特殊走法として「大逃げ」になる。
            if (RunningStyle == Gallop.RaceDefine.RunningStyle.Nige && RaceUtil.HasAbility(SkillDefine.SkillAbilityType.RunningStyleExOonige, skillDataArray))
            {
                RunningStyleEx = Gallop.RaceDefine.RunningStyleEx.Oonige;
            }
            else
            {
                RunningStyleEx = Gallop.RaceDefine.RunningStyleEx.None;
            }
        }
        
        /// <summary>
        /// 人気初期化。
        /// </summary>
        private void InitPopularity()
        {
            Popularity = _rawHorseData.popularity;
            if (_rawHorseData.popularity_mark_rank_array == null)
            {
                _rawHorseData.popularity_mark_rank_array = new int[RaceUtilEnum.GetEnumElementCount<Gallop.RaceDefine.PopularityMark>()];
                Debug.LogWarning($"RaceHorseData.popularity_mark_rank_arrayがnullです。人気を参照しない箇所でもpopularity_mark_rank_arrayはsize={_rawHorseData.popularity_mark_rank_array.Length}の配列を期待しています");
                
                PopularityRankLeft = 0;
                PopularityRankCenter = 0;
                PopularityRankRight = 0;
            }
            else
            {
                PopularityRankLeft = Math.Max(0, _rawHorseData.popularity_mark_rank_array[(int)Gallop.RaceDefine.PopularityMark.Left]);
                PopularityRankCenter = Math.Max(0, _rawHorseData.popularity_mark_rank_array[(int)Gallop.RaceDefine.PopularityMark.Center]);
                PopularityRankRight = Math.Max(0, _rawHorseData.popularity_mark_rank_array[(int)Gallop.RaceDefine.PopularityMark.Right]);
            }
            
            // 人気は0 ~ 出走人数-1になっているはず。
            Debug.Assert(Popularity >= 0 && Popularity < _raceInfo.NumRaceHorses);
            Debug.Assert(PopularityRankLeft >= 0 && PopularityRankLeft < _raceInfo.NumRaceHorses);
            Debug.Assert(PopularityRankCenter >= 0 && PopularityRankCenter < _raceInfo.NumRaceHorses);
            Debug.Assert(PopularityRankRight >= 0 && PopularityRankRight < _raceInfo.NumRaceHorses);
        }
        
        //---------------------------------------------------------------
        public virtual void CalcDelayTime(float max)
        {
            float delayTimeScale = ApplyModifier(SkillDefine.SkillModifierParam.StartDelayScale, 1);
            float delayTimeFix = ApplyModifier(SkillDefine.SkillModifierParam.StartDelayFix, 0);
            var delayTime = HorseDelayCalculator.CalcDelayTime(RaceManagerSimulate.Instance, max, delayTimeScale, delayTimeFix);

#if CYG_DEBUG
            DbgDelayTimeSavedBase = delayTime.DbgDelayTimeBase;
            // デバッグ機能：AI機能ON/OFF：スタート遅延。
            if (!RaceSimulateDebugger.IsEnableAIStartDelay)
            {
                delayTime.DelayTime = 0;
            }
#endif

            DelayTime = delayTime.DelayTime;
            DelayTimeSaved = DelayTime;
            IsGoodStart = HorseDelayCalculator.IsGoodStart(DelayTime, max);
            IsBadStart = HorseDelayCalculator.IsBadStart(DelayTime, max);
        }

        //---------------------------------------------------------------
        private void InitProperDistance()
        {
            var masterProperDistance = MasterManager.Instance.MasterRaceProperDistanceRate.Get((int)ActiveProperDistance);
            ProperDistanceCoefSpeed = masterProperDistance != null 
                ? RaceUtilMath.MasterInt2Float(masterProperDistance.ProperRateSpeed) 
                : 1;
            ProperDistanceCoefPow = masterProperDistance != null 
                ? RaceUtilMath.MasterInt2Float(masterProperDistance.ProperRatePower) 
                : 1;
        }

        //---------------------------------------------------------------
        private void InitProperRunningStyle( Gallop.RaceDefine.RunningStyle runningStyle )
        {
            // 今回のレースでの走法適正はHorseData生成時には決定できないことがある（パドック遷移時）ため、ここで決める。
            var properRunningStyleGrade = Gallop.RaceDefine.ProperGrade.Null;
            switch( runningStyle )
            {
                case Gallop.RaceDefine.RunningStyle.Nige:      properRunningStyleGrade = (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_running_style_nige;     break;
                case Gallop.RaceDefine.RunningStyle.Senko:     properRunningStyleGrade = (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_running_style_senko;    break;
                case Gallop.RaceDefine.RunningStyle.Sashi:     properRunningStyleGrade = (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_running_style_sashi;    break;
                case Gallop.RaceDefine.RunningStyle.Oikomi:    properRunningStyleGrade = (Gallop.RaceDefine.ProperGrade)_rawHorseData.proper_running_style_oikomi;   break;
            }

            var masterProperRunningStyle = MasterManager.Instance.MasterRaceProperRunningStyleRate.Get((int)properRunningStyleGrade);
            ProperRunningStyleCoef = masterProperRunningStyle != null 
                ? RaceUtilMath.MasterInt2Float(masterProperRunningStyle.ProperRate) 
                : 1;
        }

        //---------------------------------------------------------------
        private void InitProperGroundType()
        {
            var masterProperGround = MasterManager.Instance.MasterRaceProperGroundRate.Get((int)ActiveProperGroundType);
            ProperGroundTypeCoef = masterProperGround != null 
                ? RaceUtilMath.MasterInt2Float(masterProperGround.ProperRate) 
                : 1;
        }

        //---------------------------------------------------------------
        private void UpdateCornerEnd()
        {
            // 今はコーナーにいないので処理不要。
            if (CurCorner == Gallop.RaceDefine.CORNER_NULL)
            {
                return;
            }
            // コーナー終了距離に達していない。
            if (GetDistance() <= CornerEndDistance)
            {
                return;
            }
            // コーナー終了。
            CurCorner = Gallop.RaceDefine.CORNER_NULL;
        }

        //---------------------------------------------------------------
        private void UpdateSlope()
        {
#if CYG_DEBUG
            if(!RaceSimulateDebugger.IsEnableAISlope)
            {
                return;
            }
#endif
            UpdateSlopeEnd();
        }

        //---------------------------------------------------------------
        private void UpdateSlopeEnd()
        {
            // 今は坂にいないので処理不要。
            if (SlopeType == Gallop.RaceDefine.SlopeType.Null)
            {
                return;
            }
            // 坂終了距離に達していない。
            if (GetDistance() <= SlopeEndDistance)
            {
                return;
            }
            // 坂終了。
            SlopeType = Gallop.RaceDefine.SlopeType.Null;
            SlopePer = 0;
        }

        #region <初期化>
        //---------------------------------------------------------------
        /// <summary>
        /// 馬場状態によるパラメータ補正値初期化。
        /// </summary>
        //---------------------------------------------------------------
        private void InitGroundConditionParam()
        {
            // 馬場種類で参照するテーブルを変える。
            Gallop.RaceDefine.GroundModifierParam[] modifierParams = null;
            switch (_raceInfo.GroundType)
            {
                case Gallop.RaceDefine.GroundType.Turf:
                    modifierParams = ParamDefine._groundModifierParamTurf;
                    break;
                case Gallop.RaceDefine.GroundType.Dirt:
                    modifierParams = ParamDefine._groundModifierParamDirt;
                    break;
                default:
                    Debug.Assert(false, "馬場種類が不正です。groundType=" + (int)_raceInfo.GroundType);
                    return;
            }

            // 馬場状態でテーブル内で参照するデータを決定。
            int groundConditionIndex = (int)_raceInfo.GroundCondition - 1;// enum GroundConditionは1始まりなので-1。
            if (groundConditionIndex < 0 || (int)groundConditionIndex >= modifierParams.Length)
            {
                Debug.Assert(false, "馬場状態が不正です。groundCondition=" + groundConditionIndex);
                return;
            }
            _groundModifierParam = modifierParams[(int)groundConditionIndex];

            _isGroundModifierParamInitialized = true;
        }

        //---------------------------------------------------------------
        private void InitCourseSetStatus()
        {
            var masterCourseSetStatus = _raceInfo.RaceCourseSet.CourseSetStatusId != 0 
                ? MasterManager.Instance.MasterRaceCourseSetStatus.Get(_raceInfo.RaceCourseSet.CourseSetStatusId)
                : null;
            _baseSpeedCoef = CourseSetStatusAdjuster.CalcSpeedCoef(
                this,
                masterCourseSetStatus,
                ParamDefine.CourseSetAdjust);

            _isCourseSetStatusInitialized = true;
        }

        //---------------------------------------------------------------
        private void InitBaseStatusAdjust()
        {
            // コースセットによるパラメータ補正初期化。
            InitCourseSetStatus();

            // 馬場状態によるパラメータ補正値初期化。
            InitGroundConditionParam();

            Debug.Assert(
                IsSkillInitialized &&
                _isGroundModifierParamInitialized &&
                _isCourseSetStatusInitialized);

            GetRaceTypeAdjust(
                addSpeed: out float addSpeed,
                addStamina: out float addStamina,
                addPow: out float addPow,
                addGuts: out float addGuts,
                addWiz: out float addWiz);

        #if CYG_DEBUG
            DbgSpeedOnStart = BaseSpeed + addSpeed;
            DbgStaminaOnStart = BaseStamina + addStamina;
            DbgPowOnStart = BasePow + addPow;
            DbgGutsOnStart = BaseGuts + addGuts;
            DbgWizOnStart = BaseWiz + addWiz;
        #endif

            // レース中ではここで計算した値にスキルによる効果を上乗せした値を参照する。※スキル効果値は動的に変化するのでここで計算できない。
            // ここでの値はあくまでも中間値。
            if (_raceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
            {
                _baseSpeedAdjusted = RaceUtilMath.Max(BaseSpeed * _baseSpeedCoef + _groundModifierParam.addSpeed + addSpeed, Gallop.RaceDefine.STATUS_MIN);
                _baseStaminaAdjusted = RaceUtilMath.Max(BaseStamina + addStamina, Gallop.RaceDefine.STATUS_MIN);
                _basePowAdjusted = RaceUtilMath.Max(BasePow + _groundModifierParam.addPower + addPow, Gallop.RaceDefine.STATUS_MIN);
                _baseGutsAdjusted = RaceUtilMath.Max(BaseGuts + addGuts, Gallop.RaceDefine.STATUS_MIN);
                _baseWizAdjusted = RaceUtilMath.Max(BaseWiz * ProperRunningStyleCoef + addWiz, Gallop.RaceDefine.STATUS_MIN);
            }
            else
            {
                _baseSpeedAdjusted = RaceUtilMath.Clamp(BaseSpeed * _baseSpeedCoef + _groundModifierParam.addSpeed + addSpeed, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _baseStaminaAdjusted = RaceUtilMath.Clamp(BaseStamina + addStamina, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _basePowAdjusted = RaceUtilMath.Clamp(BasePow + _groundModifierParam.addPower + addPow, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _baseGutsAdjusted = RaceUtilMath.Clamp(BaseGuts + addGuts, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                _baseWizAdjusted = RaceUtilMath.Clamp(BaseWiz * ProperRunningStyleCoef + addWiz, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
            }

            _isBaseStatusAdjusted = true;
        }

        /// <summary>
        /// レースタイプによる基礎ステータスの加算値取得。
        /// </summary>
        private void GetRaceTypeAdjust(out float addSpeed, out float addStamina, out float addPow, out float addGuts, out float addWiz)
        {
            var raceType = _raceInfo.RaceType;
            
            addSpeed = 0;
            addStamina = 0;
            addPow = 0;
            addGuts = 0;
            addWiz = 0;
        
            //-----------------------------------------------------------
            // 育成レースは全キャラの全ステータスにバフが入る。
            // 育成のシナリオ追加でRaceTypeが追加されることがあるので、その時はここで条件を追加する。
            //-----------------------------------------------------------
            bool isSingleMode =
                raceType == Gallop.RaceDefine.RaceType.Single ||
                raceType == Gallop.RaceDefine.RaceType.SingleModeScenarioTeamRace;
        #if CYG_DEBUG
            if (RaceSimulateDebugger.IsSingleModeAddStatus)
            {
                isSingleMode = true;
            }
        #endif
            if (isSingleMode)
            {
                addSpeed += ParamDefine.Global.SingleModeAddSpeed;
                addStamina += ParamDefine.Global.SingleModeAddStamina;
                addPow += ParamDefine.Global.SingleModeAddPow;
                addGuts += ParamDefine.Global.SingleModeAddGuts;
                addWiz += ParamDefine.Global.SingleModeAddWiz;
            }
        }

        //---------------------------------------------------------------
        public void RecalcParams()
        {
            Debug.Assert(!IsParamInitialized, "RecalcParamsが複数回呼ばれています。初期化フローに問題があります。");

            // 基礎ステータスをコースセットや馬場状態で補正。
            InitBaseStatusAdjust();

            // HP
            _hp = HorseHpCalculator.CalcInitialHp(
                ParamDefine.Hp, 
                _raceInfo.CourseDistance,
                Stamina, 
                RunningStyle,
                RunningStyleEx);
            _maxHp = _hp;

            // 最低速度。
            UpdateMinSpeed();

            // 初速は全員同じ固定値。
            SetLastSpeed(ParamDefine.StartSpeed);

            // 目指す速度基礎値計算。
            _horseRaceAI.CalcBaseTargetSpeed();

            // 最低速度、初速が正しい値になっているか。
            Debug.Assert(RaceUtilMath.Approximately(MinSpeed, ParamDefine.StartSpeed));
            Debug.Assert(RaceUtilMath.Approximately(GetLastSpeed(), ParamDefine.StartSpeed));

            IsParamInitialized = true;
        }

        /// <summary>
        /// アイテム効果適用。
        /// </summary>
        public void UseItem()
        {
            var itemIdArray = _rawHorseData.item_id_array;
            if (itemIdArray == null)
            {
                return;
            }
            
            foreach (var itemId in itemIdArray)
            {
                var masterItem = MasterManager.Instance.MasterItemData.Get(itemId);
                switch ((Gallop.RaceDefine.ItemEffectType)masterItem.EffectType1)
                {
                    case Gallop.RaceDefine.ItemEffectType.StartDashSuccess:
                    {
                        // デバフではない
                        var modifier = CreateSkillParamSetModifier(
                            0, SkillParamModifierBase.TIME_INFINITE, SkillDefine.SkillModifierParam.StartDelayScale, false
                        );
#if CYG_DEBUG
                        modifier.AppendDbgItem(HorseIndex, itemId);
#endif
                        AddModifier(SkillDefine.SkillModifierParam.StartDelayScale, modifier);
                    }
                        break;
                        
                    case Gallop.RaceDefine.ItemEffectType.DebuffCancel:
                    {
                        // デバフではない
                        var modifier = CreateSkillParamSetModifier(
                            true, SkillParamModifierBase.TIME_INFINITE, SkillDefine.SkillModifierParam.DebuffCancel,
                            false
                        );
#if CYG_DEBUG
                        modifier.AppendDbgItem(HorseIndex, itemId);
#endif
                        AddModifier(SkillDefine.SkillModifierParam.DebuffCancel, modifier);
                    }
                        break;
                }
            }
        }
        
        private void UpdateStartDash()
        {
            if(!IsStartDash)
            {
                return;
            }

            // 速度が通常走行できる最低速度に達したら、スタートダッシュの急加速を停止。
            // refs #144017 スタートダッシュ終了判定に使用する現在速度はデバフスキル効果を除いた値を使用する
            if(_lastSpeedWithoutDebuffSkill >= StartDashSpeedThreshold)
            {
                IsStartDash = false;
            }
        }


        //---------------------------------------------------------------
        public void RecalcParamsPost()
        {
            Debug.Assert(IsParamInitialized);
            _lastSelfSpeed = _lastSpeed;
        }

        //---------------------------------------------------------------
        public void CreateSkill()
        {
            // スキル初期化
            SkillManager = CreateSkillManager();
            AddSkills();

            IsSkillInitialized = true;
        }

        //---------------------------------------------------------------
        public void CreateAI(IHorseRaceInfoSimulate[] horseRaceInfos, IHorsePaseMakerCalculator paseMakerCalculator)
        {
            _horseRaceAI = new HorseRaceAISimulate();
            _horseRaceAI.Init(
                _raceInfo, 
                this, 
                _horseAccessor, 
                OnTemptationStart, 
                paseMakerCalculator,
                _firstMoveLanePointDistance);
        }

        //---------------------------------------------------------------
        public bool IsCompleteFirstUpdate { get; private set; }

        //---------------------------------------------------------------
        public void SetUpdateFirst(bool isCompleteFirstUpdate)
        {
            IsCompleteFirstUpdate = isCompleteFirstUpdate;
        }
        
        //---------------------------------------------------------------
        protected virtual void OnTemptationStart()
        {
        }
        
        //---------------------------------------------------------------
        private SkillManagerSimulate CreateSkillManager()
        {
            return new SkillManagerSimulate(this);
        }

        //---------------------------------------------------------------
        private IHorseOrderChangeCounter _CreateOrderChangeCounterUp()
        {
            var model = new HorseOrderChangeCounterModel(this,
                _horseAccessor,
                _raceInfo.NumRaceHorses,
                ParamDefine.overTakeCountCoolDownTime,
                _raceInfo.CourseDistance);
            return new HorseOrderChangeCounterUp(model);
        }
        
        //---------------------------------------------------------------
        private IHorseOrderChangeCounter _CreateOrderChangeCounterDown()
        {
            var model = new HorseOrderChangeCounterModel(this,
                _horseAccessor,
                _raceInfo.NumRaceHorses,
                ParamDefine.overTakeCountCoolDownTime,
                _raceInfo.CourseDistance);
            return new HorseOrderChangeCounterDown(model);
        }

        #endregion <初期化>

        public float GetHp() { return _hp; }
        public float GetMaxHp() { return _maxHp; }
        public void SetHp(float hp)
        {
#if CYG_DEBUG && UNITY_EDITOR
            // 【デバッグ機能】スタミナ無限機能がONなら体力を消費しない（クライアントシミュレートのみ）
            if (RaceSimulateDebugger.IsStaminaUnlimited)
            {
                return;
            }
#endif
            Debug.Assert(_maxHp > 0.0f);
            _hp = hp;
            _hp = RaceUtilMath.Clamp(_hp, 0.0f, _maxHp);
        }
        public void AddHp(float addHp)
        {
#if CYG_DEBUG && UNITY_EDITOR
            // 【デバッグ機能】スタミナ無限機能がONなら体力を消費しない（クライアントシミュレートのみ）
            if (RaceSimulateDebugger.IsStaminaUnlimited)
            {
                return;
            }
#endif
            SetHp(GetHp() + addHp);
        }
        public float GetHpPer() { return _hp / _maxHp; }
        
        //---------------------------------------------------------------
        public void UpdateWorldTransformByDistance()
        {
            RaceUtil.CalcWorldTransform(
                GetDistance(),
                GetLaneDistance(),
                out _,
                out Vector3 posWorld,
                out _);

            SetPosition(posWorld);
        }

        public void SetPosition(Vector3 position)
        {
            _position = position;
        }

        public Vector3 GetPosition()
        {
            return _position;
        }

        public Gallop.RaceDefine.HorsePhase GetPhase()
        {
            return _phase;
        }

        #region <更新>
        //---------------------------------------------------------------
        public void PreUpdate(float deltaTime)
        {
            Debug.Assert(IsParamInitialized);
            _horseRaceAI.UpdateAroundHorsesParam(deltaTime);
            _horseRaceAI.UpdateAfterGoalTime(deltaTime);

            // スキル効果時間更新。
            SkillModifierReceiver.UpdateModifiers(deltaTime);
            
            // コーナー終了チェック。
            UpdateCornerEnd();
        }

        //---------------------------------------------------------------
        public virtual bool Update(float deltaTime)
        {
            // 最低速度の更新。※このフレームでのSetLastSpeedはこの後に行うこと。
            UpdateMinSpeed();

            // スキルによるHp増減。
            UpdateHpBySkill();
            
            // 持久力消費の仕様によるHP消費
            UpdateHpByConsumeStamina();

            // 進行距離更新。
            UpdateDistance(deltaTime);

            // 馬群の中にいる継続時間更新。
            UpdateNearAround(deltaTime);

            // 誰かの追い抜き対象になっている継続時間。
            UpdateOvertakeTargetTime(deltaTime);

            // 追い抜き対象を所持している時に順位が上がってない/下がってない継続時間更新。
            UpdateOvertakeTargetHaveTime(deltaTime);
            
            // ブロックされている継続時間更新。
            UpdateBlockTime(deltaTime);

            // 進路上が混雑していて、かつ狙いたい速度まで速度が出せていない継続時間。
            UpdateCongestionTime(deltaTime);

            // ラストスパート開始時の順位更新。
            UpdateLastSpurtOrder();

            // 最終コーナー終了時の順位更新。
            UpdateFilanlCornerEndOrder();

            // 特定の順位以内をキープしているか更新。
            UpdateOrderInContinue();
            // 特定の順位以下をキープしているか更新。
            UpdateOrderOutContinue();
            
            // ゴールした瞬間の処理。
            CheckFinished();

            return IsFinished();
        }

        //---------------------------------------------------------------
        private void UpdateOrderInContinue()
        {
            if (RaceManagerSimulate.Instance.AccumulateTimeSinceStart < ORDER_IN_CHECK_TIME)
            {
                return;
            }

            int horseNum = _raceInfo.NumRaceHorses;
            for(int i = 0; i < IsOrderInContinueArray.Length; ++i)
            {
                // 既にfalseになっているならチェック不要。
                if (!IsOrderInContinueArray[i])
                {
                    continue;
                }
                
                int per = SkillDefine.ORDER_IN_PER[i];
                int orderThreshold = RaceUtil.GetIntThresholdByPer(horseNum, per);
                // orderThresholdは1~18なのでCurOrder+1と比較する。
                if (CurOrder+1 > orderThreshold)
                {
                    IsOrderInContinueArray[i] = false;
                }
            }
        }

        //---------------------------------------------------------------
        private void UpdateOrderOutContinue()
        {
            if (RaceManagerSimulate.Instance.AccumulateTimeSinceStart < ORDER_OUT_CHECK_TIME)
            {
                return;
            }

            int horseNum = _raceInfo.NumRaceHorses;
            for(int i = 0; i < IsOrderOutContinueArray.Length; ++i)
            {
                // 既にfalseになっているならチェック不要。
                if (!IsOrderOutContinueArray[i])
                {
                    continue;
                }
                
                int per = SkillDefine.ORDER_OUT_PER[i];
                int orderThreshold = RaceUtil.GetIntThresholdByPer(horseNum, per);
                // orderThresholdは1~18なのでCurOrder+1と比較する。
                if (CurOrder+1 < orderThreshold)
                {
                    IsOrderOutContinueArray[i] = false;
                }
            }
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// 位置更新後の処理
        /// 順位は更新前なので、順位参照は避けること
        /// </summary>
        /// <param name="deltaTime"></param>
        public void PostUpdate(float deltaTime)
        {
            // 前後のキャラと一定距離内にいる継続時間。
            UpdateInfrontHorseNearTime(deltaTime);
            UpdateBehindHorseNearTime(deltaTime);

            UpdateBehindHorseNearTimeParamSet(deltaTime);
            
            // スタミナ消費（スパート前位置取り勝負 + リード確保計算機の更新）
            // 位置関係を参照するため、PostUpdateで処理することが必要
            UpdateConsumeStaminaCalculator(deltaTime);
            
#if CYG_DEBUG
            // 追い比べ時間を計測
            DbgUpdateCompeteFightTime(deltaTime);
#endif
        }
        
        //---------------------------------------------------------------
        public void PostUpdateAfterOrderCalculation(float deltaTime)
        {
            // レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値を更新
            UpdateTopLeadMaxAmount();
        }

        //---------------------------------------------------------------
        private void UpdateHpBySkill()
        {
            float addHp = ApplyModifier(SkillDefine.SkillModifierParam.Hp, 0);
            if (RaceUtilMath.Approximately(addHp, 0.0f))
            {
                return;
            }
            AddHp(addHp);

            // 終盤以降にHp回復を行ったらラストスパート再計算。
            if (addHp > 0 && GetPhase() >= Gallop.RaceDefine.HorsePhase.End)
            {
                IsReqCalcLastSpurt = true;
            }
            
#if CYG_DEBUG
            DbgAddHp += addHp;
#endif
        }
        
#if CYG_DEBUG
        private bool _isOverrideDistance;
#endif
        
        //---------------------------------------------------------------
        // スパート前位置取り勝負とリード確保でのスタミナ消費
        private void UpdateHpByConsumeStamina()
        {
            // スタミナ消費なので、addHpはマイナス値 or 0（発動時以外）
            // スパート前位置取り勝負
            float competeBeforeSpurtAddHp = _horseRaceAI.GetCompeteBeforeSpurtConsumeStamina();
            AddHp(competeBeforeSpurtAddHp);
            // リード確保
            float secureLeadAddHp = _horseRaceAI.GetSecureLeadConsumeStamina();
            AddHp(secureLeadAddHp);
        }
        
        //---------------------------------------------------------------
        private void UpdateDistance(float deltaTime)
        {
            _prevDistance = GetDistance();
            _prevPosition = GetPosition();

            // 今回のフレームで進むコース上距離。
            float distCourseDelta = UpdateAI(deltaTime);

#if CYG_DEBUG
            // デバッグ機能：速度固定。
            if (RaceSimulateDebugger.IsFixSpeed)
            {
                distCourseDelta = RaceSimulateDebugger.FixSpeed * deltaTime;
            }
#if UNITY_EDITOR
            _furlongStat.RecordIfNeeded(this, RaceManagerSimulate.Instance.AccumulateTimeSinceStart, deltaTime, distCourseDelta);
#endif
#endif

            // 3D空間上の座標決定。
            UpdateWorldTransformByDistance();
            var posWorld = GetPosition();

            // 3D空間上を進んだ距離。※坂の上り下りによるy変化は無視。
            float distWorldDelta = Vector3.Distance(
                new Vector3(_prevPosition.X, 0, _prevPosition.Z),
                new Vector3(posWorld.X, 0, posWorld.Z));

#if CYG_DEBUG
            // デバッグ機能：AI機能ON/OFF：外周の距離差。
            if (RaceSimulateDebugger.IsEnableAICircumference)
#endif
            {
                // 外周を走る場合、【コース上を進んだ距離】よりも【3D空間上を進んだ距離】の方が大きくなる。その場合はコース上を進んだ距離を縮小する。
                float distCourse2WorldDelta = distCourseDelta * RaceManagerSimulate.Instance.CourseDistance2World;
                if (distCourse2WorldDelta < distWorldDelta)
                {
                    distCourseDelta *= (distCourse2WorldDelta / distWorldDelta);
                }
            }

            // コース上距離を進める。
            AddDistance(distCourseDelta);

        #if CYG_DEBUG
            // デバッグ機能：距離上書き。
            if(!_isOverrideDistance)
            {
                var overrideInfo = RaceSimulateDebugger.GetOverrideInfo(HorseIndex, RaceManagerSimulate.Instance.AccumulateTimeSinceStart);
                if (overrideInfo != null)
                {
                    if (overrideInfo.IsOverrideDistance)
                    {
                        SetDistance(overrideInfo.Distance);
                        _isOverrideDistance = true;
                    }
                }
            }
        #endif

            // 現在地の勾配情報更新。
            UpdateSlope();

            UpdateStartDash();
        }

        //---------------------------------------------------------------
        private void UpdateLastSpurtOrder()
        {
            // 前回ラストスパート開始済みなら、順位も計算済みである。
            if (_isLastSpurtPrev)
            {
                return;
            }

            // まだラストスパート開始していない。
            if (!IsLastSpurt)
            {
                return;
            }

            LastSpurtOrder = CurOrder;
            _isLastSpurtPrev = IsLastSpurt;
        }

        //---------------------------------------------------------------
        private void UpdateFilanlCornerEndOrder()
        {
            // 初期化済みなら処理不要。
            if (IsFinalCornerEndOrderInitialized)
            {
                return;
            }

            // まだ最終コーナーに入っていなければ処理不要。
            if (!IsFinalCorner)
            {
                return;
            }
            // 最終コーナーに入った後にまだコーナーにいるなら、最終コーナー継続中と見なして処理しない。
            if (IsCorner)
            {
                return;
            }

            // ここまで来たら最終コーナー明けて直線に入ったということなので順位を控える。
            FinalCornerEndOrder = CurOrder;
        }

        //---------------------------------------------------------------
        private void UpdateNearAround(float deltaTime)
        {
            if (NearHorseCount >= NEAR_HORSE_AROUND_NUM)
            {
                NearHorseAroundAccumulateTime += deltaTime;
            }
        }

        //---------------------------------------------------------------
        private void UpdateOvertakeTargetTime(float deltaTime)
        {
            bool isOvertakeTarget = false;
            
            // 自分が誰かの追い抜き対象になっているか検索。
            var allHorseArray = RaceManagerSimulate.Instance.GetHorseRaceInfos();
            for (int i = 0; i < allHorseArray.Length; ++i)
            {
                var horse = allHorseArray[i];
                
                // 自分より前にいるキャラは、追い抜き対象に入っているか調べない。
                if (horse.CurOrder <= CurOrder)
                {
                    continue;
                }
                
                var overTakeHorseList = horse.GetOverTakeHorseList();
                if (overTakeHorseList.FindIndex(x => x.infoSimulate == this) >= 0)
                {
                    // 追い抜き対象になっている。
                    isOvertakeTarget = true;
                    break;
                }
            }

            if (isOvertakeTarget)
            {
                OverTakeTargetContinueTime += deltaTime;
            }
            else
            {
                // 誰の追い抜き対象にもなっていなければ時間リセット。
                OverTakeTargetContinueTime = 0;
            }
        }

        //---------------------------------------------------------------
        private void UpdateOvertakeTargetHaveTime(float deltaTime)
        {
            bool hasOvertakeTarget = GetOverTakeHorseList().Count > 0;

            if (hasOvertakeTarget)
            {
                OverTakeTargetHaveNoOrderUpContinueTime += deltaTime;
                OverTakeTargetHaveNoOrderDownContinueTime += deltaTime;
            }
            else
            {
                OverTakeTargetHaveNoOrderUpContinueTime = 0;
                OverTakeTargetHaveNoOrderDownContinueTime = 0;
            }
        }
        
        //---------------------------------------------------------------
        private void UpdateBlockTime(float deltaTime)
        {
            bool isBlockFront = GetBlockHorseFront() != null;
            bool isBlockFrontHpEmpty = isBlockFront ? GetBlockHorseFront().GetHp() <= 0.0f : false;
            bool isBlockSide = GetBlockHorseIn() != null || GetBlockHorseOut() != null;

            //-----------------------------------------------------------
            // 累積時間更新。
            //-----------------------------------------------------------
            if (isBlockFront)
            {
                BlockFrontAccumulateTime += deltaTime;
            }
            if (isBlockFront || isBlockSide)
            {
                BlockFrontOrSideAccumulateTime += deltaTime;
            }

            //-----------------------------------------------------------
            // 継続時間更新。ブロック状態が外れたら継続時間はリセットする。
            //-----------------------------------------------------------
            if (isBlockFront)
            {
                BlockFrontContinueTime += deltaTime;
            }
            else
            {
                BlockFrontContinueTime = 0;
            }
            if (isBlockFrontHpEmpty)
            {
                BlockFrontHpEmptyContinueTime += deltaTime;
            }
            else
            {
                BlockFrontHpEmptyContinueTime = 0;
            }
            if (isBlockSide)
            {
                BlockSideContinueTime += deltaTime;
                // 最大継続時間更新
                var curPhase = GetPhase();
                if (BlockSideMaxContinueTimeByPhase.TryGetValue(curPhase, out var blockSideMaxContinueTimeByCurPhase))
                {
                    if (BlockSideContinueTime > blockSideMaxContinueTimeByCurPhase)
                    {
                        BlockSideMaxContinueTimeByPhase[curPhase] = BlockSideContinueTime;
                    }
                }
            }
            else
            {
                BlockSideContinueTime = 0;
            }
        }

        //---------------------------------------------------------------
        private void UpdateCongestionTime(float deltaTime)
        {
            if (!_horseRaceAI.IsCongestion)
            {
                return;
            }
            if (GetLastSpeed() >= GetTargetSpeed())
            {
                return;
            }
            CongestionTime += deltaTime;
        }

        /// <summary>
        /// 順位が一つ下のキャラと、一定時間一定距離内にいる時間を計算。
        /// </summary>
        private void UpdateBehindHorseNearTime(float deltaTime)
        {
            // 自分が最後尾ならリセット。計算不要。
            if (CurOrder == RaceManagerSimulate.Instance.LastOrder)
            {
                BehindHorseNearContinueTime = 0;
                BehindHorseNearLaneContinueTime = 0;
                return;
            }
            
            // 自分の順位が前回と変化したらリセット。
            if (CurOrder != PrevOrder)
            {
                BehindHorseNearContinueTime = 0;
                BehindHorseNearLaneContinueTime = 0;
                return;
            }
            
            // 後ろのキャラとの距離が離れたらリセット。
            var behindHorse = RaceManagerSimulate.Instance.GetHorseInfoByOrder(CurOrder+1);
            // 厳密には前フレームの順位と現在フレームの位置を参照しているので、追い越しフレームでの判定は正しくないが、
            // 効果時間の両端1フレームのみへの影響なので、PLさん相談で許容とした
            float distanceDiff = GetDistance() - behindHorse.GetDistance(); 
            if (distanceDiff > ParamDefine.Skill.BehindHorseNearDistance)
            {
                BehindHorseNearContinueTime = 0;
                BehindHorseNearLaneContinueTime = 0;
                return;
            }

            // ここまできたら一定距離内にいるのは確定なので時間加算。
            BehindHorseNearContinueTime += deltaTime;

            // ここからは一定のレーン内にいるかもチェックして時間加算。
            float laneDistanceDiff = Math.Abs(GetLaneDistance() - behindHorse.GetLaneDistance());
            if (laneDistanceDiff > ParamDefine.Skill.BehindHorseNearLaneDistance)
            {
                BehindHorseNearLaneContinueTime = 0;
                return;
            }
         
            BehindHorseNearLaneContinueTime += deltaTime;
        }

        /// <summary>
        /// 順位が一つ下のキャラと、一定時間一定距離内にいる時間のリセット。パラメータセット指定版。
        /// </summary>
        private void ResetBehindHorseNearTimeParamSet()
        {
           for (int i = 0; i < _behindHorseNearLaneContinueTimeArray.Length; i++)
            {
                _behindHorseNearLaneContinueTimeArray[i] = 0;
            }
        }
        
        /// <summary>
        /// 順位が一つ下のキャラと、一定時間一定距離内にいる時間を計算。
        /// </summary>
        private void UpdateBehindHorseNearTimeParamSet(float deltaTime)
        {
            // 自分が最後尾ならリセット。計算不要。
            if (CurOrder == RaceManagerSimulate.Instance.LastOrder)
            {
                ResetBehindHorseNearTimeParamSet();
                return;
            }
            
            // 自分の順位が前回と変化したらリセット。
            if (CurOrder != PrevOrder)
            {
                ResetBehindHorseNearTimeParamSet();
                return;
            }

            // １つ後ろのキャラとの距離差・レーン差。
            var behindHorse = RaceManagerSimulate.Instance.GetHorseInfoByOrder(CurOrder+1);
            // 厳密には前フレームの順位と現在フレームの位置を参照しているので、追い越しフレームでの判定は正しくないが、
            // 効果時間の両端1フレームのみへの影響なので、PLさん相談で許容とした
            float distanceDiff = GetDistance() - behindHorse.GetDistance(); // 自分の方が進んでいるのでabs不要。 
            float laneDistanceDiff = Math.Abs(GetLaneDistance() - behindHorse.GetLaneDistance());
            
            for (int i = 0; i < ParamDefine.Skill.BehindNearParamArray.Length; i++)
            {
                float distanceThreshold = ParamDefine.Skill.BehindNearParamArray[i].Distance;
                float laneThreshold = ParamDefine.Skill.BehindNearParamArray[i].LaneDistance;
                
                // 後ろのキャラとの距離が離れたらリセット。
                if (distanceDiff <= distanceThreshold && laneDistanceDiff <= laneThreshold)
                {
                    _behindHorseNearLaneContinueTimeArray[i] += deltaTime;
                }
                else
                {
                    _behindHorseNearLaneContinueTimeArray[i] = 0;
                }
            }
        }
        
        /// <summary>
        /// 順位が一つ上のキャラと、一定時間一定距離内にいる時間を計算。
        /// </summary>
        private void UpdateInfrontHorseNearTime(float deltaTime)
        {
            // 自分が先頭ならリセット。計算不要。
            if (CurOrder == 0)
            {
                InfrontHorseNearContinueTime = 0;
                InfrontHorseNearLaneContinueTime = 0;
                return;
            }
            
            // 自分の順位が前回と変化したらリセット。
            if (CurOrder != PrevOrder)
            {
                InfrontHorseNearContinueTime = 0;
                InfrontHorseNearLaneContinueTime = 0;
                return;
            }
            
            // 前のキャラとの距離が離れたらリセット。
            var infrontHorse = RaceManagerSimulate.Instance.GetHorseInfoByOrder(CurOrder-1);
            // 厳密には前フレームの順位と現在フレームの位置を参照しているので、追い越しフレームでの判定は正しくないが、
            // 効果時間の両端1フレームのみへの影響なので、PLさん相談で許容とした
            float distanceDiff = infrontHorse.GetDistance() - GetDistance();
            if (distanceDiff > ParamDefine.Skill.InfrontHorseNearDistance)
            {
                InfrontHorseNearContinueTime = 0;
                InfrontHorseNearLaneContinueTime = 0;
                return;
            }

            // ここまできたら一定距離内にいるのは確定なので時間加算。
            InfrontHorseNearContinueTime += deltaTime;
            
            // ここからは一定のレーン内にいるかもチェックして時間加算。
            float laneDistanceDiff = Math.Abs(GetLaneDistance() - infrontHorse.GetLaneDistance());
            if (laneDistanceDiff > ParamDefine.Skill.InfrontHorseNearLaneDistance)
            {
                InfrontHorseNearLaneContinueTime = 0;
                return;
            }
            
            InfrontHorseNearLaneContinueTime += deltaTime;
        }
        
#if CYG_DEBUG
        /// <summary>
        /// 追い比べしている時間を計算。
        /// </summary>
        private void DbgUpdateCompeteFightTime(float deltaTime)
        {
            if (IsCompeteFight)
            {
                _horseRaceAI.DbgCompeteFightTime += deltaTime;
            }
        }
#endif
        
        /// <summary>
        /// スタミナ消費増加（スパート前位置取り勝負 + リード確保）計算機の更新
        /// </summary>
        private void UpdateConsumeStaminaCalculator(float deltaTime)
        {
            _horseRaceAI.UpdateCompeteBeforeSpurtCalculator(deltaTime);
            _horseRaceAI.UpdateSecureLeadCalculator(deltaTime);
        }
        
        /// <summary>
        /// レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値を計算。
        /// </summary>
        private void UpdateTopLeadMaxAmount()
        {
            // 自分が先頭ではないなら、何もしない
            if (CurOrder != 0)
            {
                return;
            }

            // 該当区間以外は何もしない
            if (GetDistance() < _distanceTupleForTopLeadMaxAmountCheck.StartDistance || GetDistance() > _distanceTupleForTopLeadMaxAmountCheck.EndDistance)
            {
                return;
            }

            // 自分が先頭なら、2番手との距離を確認し、最大を更新しているかチェックする
            var secondHorse = RaceManagerSimulate.Instance.GetHorseInfoByOrder(CurOrder + 1);
            float distanceDiff = GetDistance() - secondHorse.GetDistance();
            TopLeadMaxAmount = System.Math.Max(distanceDiff, TopLeadMaxAmount);
        }

        //---------------------------------------------------------------
        private void CalcFinishTime()
        {
            if (FinishTimeScaled >= 0.0f)
            {
                return;
            }

            // このフレームで進んだ距離に比例した時間を足して実際ゴールした時間を得る
            float moveDistance = _distance - _prevDistance;
            Debug.Assert(moveDistance > 0);

            float rate = (_distance - _raceInfo.CourseDistance) / moveDistance;
            rate = RaceUtilMath.Clamp(rate, 0.0f, 1.0f);
            FinishTimeRaw = RaceManagerSimulate.Instance.AccumulateTimeSinceStart - _prevElapsedTime * rate;

            // 実際の着タイムに一定の係数を掛けた値を使用する。
            FinishTimeScaled = FinishTimeRaw * ParamDefine.Global.FinishTimeCoef;

            // #89057
            // FinishTimeRawが違う2キャラが、FinishTimeCoefを掛けた結果floatの有効桁数を超えてしまい同値になるケースがあった。
            // そのままにすると着順計算時にFinishTimeRawの優劣に関係なく馬番の若い方が上位になる。
            // それを避けるため、FinishTimeCoef乗算結果はdoubleで保存しておく。これは着順の判断にだけ使う。
            FinishTimeScaledForFinishOrderCalc = FinishTimeRaw * ParamDefine.Global.FinishTimeCoef;
            FinishTimeScaled = (float)FinishTimeScaledForFinishOrderCalc;
        }
        
        //---------------------------------------------------------------
        private float UpdateAI(float elapsedTime)
        {
            // 時間経過が無ければ進行距離も０。
            if (RaceUtilMath.Approximately(elapsedTime, 0.0f))
            {
                return 0.0f;
            }

            
            _laneMoveSpeed = 0.0f;

            // 出遅れ
            if (DelayTime > 0.0f)
            {
                DelayTime -= elapsedTime;
                if (DelayTime > 0.0f)
                {
                    return 0.0f;
                }
            }

            // AI更新
            _horseRaceAI.Update(elapsedTime);

            // 最大速度調整。
            float maxSpeed = CalcMaxSpeed();

            //-----------------------------------------------------------
            // Lane移動
            //-----------------------------------------------------------
            MoveLane(elapsedTime);

            //-----------------------------------------------------------
            // 自分のステータスにより出せる速度の計算。
            //-----------------------------------------------------------
            float selfSpeed = UpdateSelfSpeed(elapsedTime, maxSpeed);

            //-----------------------------------------------------------
            // 自力の速度にスキルによる速度加算。これが最終的な現在速度となる。
            //-----------------------------------------------------------
            float skillAddSpeed = ApplyModifier(SkillDefine.SkillModifierParam.CurrentSpeed, 0);
            // スキル発動中は他のスキルと同様に「ApplyModifier」から取得する
            skillAddSpeed += ApplyModifier(SkillDefine.SkillModifierParam.CurrentSpeedWithNaturalDeceleration, 0);
            float curSpeed = selfSpeed + skillAddSpeed;
            curSpeed = RaceUtilMath.Clamp(curSpeed, MinSpeed, maxSpeed);

            //-----------------------------------------------------------
            // スタートダッシュ終了判定に使用するための現在速度を計算(デバフスキル効果を反映しない)
            //-----------------------------------------------------------
            float skillAddSpeedBuffParam = ApplyModifierBuffParam(SkillDefine.SkillModifierParam.CurrentSpeed, 0);
            // スキル発動中は他のスキルと同様に「ApplyModifier」から取得する
            skillAddSpeedBuffParam += ApplyModifierBuffParam(SkillDefine.SkillModifierParam.CurrentSpeedWithNaturalDeceleration, 0);
            float curSpeedForStatDashFinishCheck = selfSpeed + skillAddSpeedBuffParam;
            curSpeedForStatDashFinishCheck = RaceUtilMath.Clamp(curSpeedForStatDashFinishCheck, MinSpeed, maxSpeed);

#if CYG_DEBUG
            if (MinSpeed > maxSpeed)
            {
                Debug.LogError(string.Format("最低速度が最高速度を上回っています。HorseIndex={0}, MinSpeed={1}, MaxSpeed={2}", HorseIndex, MinSpeed, maxSpeed));
            }
#endif

            // ※※※　これ以降はデバッグ機能以外でcurSpeed、curSpeedForStatDashFinishCheckに変更を加えないこと。　※※※

#if CYG_DEBUG
            // デバッグ機能：速度上書き。レーン上書き。
            {
                var overrideInfo = RaceSimulateDebugger.GetOverrideInfo(HorseIndex, RaceManagerSimulate.Instance.AccumulateTimeSinceStart);
                if(overrideInfo != null)
                {
                    if(overrideInfo.IsOverrideSpeed)
                    {
                        curSpeed = overrideInfo.Speed;
                        curSpeedForStatDashFinishCheck = overrideInfo.Speed;
                    }
                    if (overrideInfo.IsOverrideLane)
                    {
                        _laneDistance = overrideInfo.Lane;
                    }
                }
            }
#endif

            SetLastSpeed(curSpeed);
            SetLastSpeedWithoutDebuffSkill(curSpeedForStatDashFinishCheck);

            //-----------------------------------------------------------
            // このフレームで走った距離を計算。
            //-----------------------------------------------------------
            float currentFrameMovedDistance = curSpeed * elapsedTime;
            if (DelayTime < 0.0f)
            {
                currentFrameMovedDistance *= Math.Abs(DelayTime) / elapsedTime;
                DelayTime = 0.0f;
            }

            //-----------------------------------------------------------
            // 自力で出している速度を元にHP計算。
            //-----------------------------------------------------------
            _ConsumeHp(selfSpeed, elapsedTime);

            //-----------------------------------------------------------
            // 進んだ距離を元にPhase更新。
            //-----------------------------------------------------------
            UpdatePhase();

            _prevElapsedTime = elapsedTime;

            return currentFrameMovedDistance;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// スピードを元にHP更新。
        /// </summary>
        //---------------------------------------------------------------
        private void _ConsumeHp(float currentSpeed, float elapsedTime)
        {
            if (IsFinished())
            {
                // ゴールしたら減少させない
                return;
            }

#if CYG_DEBUG
            // デバッグ機能：AI機能ON/OFF：HP消費。
            if (!RaceSimulateDebugger.IsEnableAIConsumeHp)
            {
                return;
            }
#endif

            if (_hp > 0.0f)
            {
                float speedGapBaseSpeed = IsStartDash 
                    ? currentSpeed 
                    : _raceInfo.BaseSpeed;

                float decHpPerSec = HorseHpCalculator.CalcDecHpPerSec(
                    this,
                    ParamDefine.Hp,
                    speedGapBaseSpeed,
                    GroundModeifierParam.multiHpSub,
                    GetPhase(),
                    Guts,
                    currentSpeed);
#if CYG_DEBUG
                DbgDecHpPerSec = decHpPerSec;
#endif
                decHpPerSec *= elapsedTime;
                AddHp(-decHpPerSec);
#if CYG_DEBUG
                DbgSaveConsumeHp(decHpPerSec);
#endif
            }

            // 一度でもHpが0になった状態を記録。
            if (_hp <= 0.0f)
            {
                IsHpEmptyOnRace = true;
            }
        }


#if CYG_DEBUG
        private void DbgSaveConsumeHp(float decHp)
        {
            if(IsTemptation)
            {
                DbgConsumeHpOnTemptation += decHp;
            }
        }
#endif
        
        //---------------------------------------------------------------
        /// <summary>
        /// レーン移動。
        /// </summary>
        //---------------------------------------------------------------
        private void MoveLane(float elapsedTime)
        {
            PrevLaneDistance = _laneDistance;
            float targetLane = _horseRaceAI.GetTargetLane();
            if (RaceUtilMath.Approximately(PrevLaneDistance, targetLane))
            {
                // レーン移動しない。
                _laneDistance = RaceUtilMath.Clamp(_laneDistance, LaneDistanceMin, LaneDistanceMax);
                MoveLaneContinueTime = 0;
                return;
            }

            var prevMoveLaneDirection = MoveLaneContinueDirection;
            MoveLaneContinueDirection = PrevLaneDistance > targetLane ? Gallop.RaceDefine.LaneDirection.In : Gallop.RaceDefine.LaneDirection.Out;

            //-----------------------------------------------------------
            // 移動速度決定。
            //-----------------------------------------------------------
            // オーバーラン中のレーン移動速度(固定値)。
            if (IsFinished() && _horseRaceAI.IsOverRunSpeedDownMode)
            {
                if (PrevLaneDistance > targetLane)
                {
                    _laneMoveSpeed = -OVERRUN_LANE_MOVE_SPEED_PER_SEC * elapsedTime;
                }
                else
                {
                    _laneMoveSpeed = OVERRUN_LANE_MOVE_SPEED_PER_SEC * elapsedTime;
                }
            }
            // レース中のレーン移動速度。
            else
            {
                float speed = CalcLaneMoveSpeed(elapsedTime);
                if (PrevLaneDistance > targetLane)
                {
                    // 内に寄る場合、外側にいるほど内に寄る速度を上げてできるだけINにウマが集まるようにLaneの値を考慮する
                    _laneMoveSpeed = -speed * (1.0f + PrevLaneDistance);
                }
                else
                {
                    _laneMoveSpeed = +speed;
                }
            }
            if (RaceUtilMath.Approximately(_laneMoveSpeed, 0.0f))
            {
                return;
            }

            //-----------------------------------------------------------
            // 自分の移動先に誰かいないかチェック。誰かいる場合は移動できない。
            //-----------------------------------------------------------
            float moveTargetLane = PrevLaneDistance + _laneMoveSpeed * elapsedTime;

            // 目標地点行き過ぎチェック。
            if (PrevLaneDistance > targetLane)
            {
                if (moveTargetLane < targetLane)
                {
                    moveTargetLane = targetLane;
                }
            }
            else
            {
                if (moveTargetLane > targetLane)
                {
                    moveTargetLane = targetLane;
                }
            }
            moveTargetLane = RaceUtilMath.Clamp(moveTargetLane, LaneDistanceMin, LaneDistanceMax);

            //イン/アウト移動するのにサイドブロックされていないかどうか。
            if (PrevLaneDistance < moveTargetLane)
            {
                if (!IsEnableOutMove)
                {
                    return;
                }
            }
            else if (PrevLaneDistance > moveTargetLane)
            {
                if (!IsEnableInMove)
                {
                    return;
                }
            }

            //-----------------------------------------------------------
            // 実際の移動。
            //-----------------------------------------------------------
            _laneDistance = moveTargetLane;

            if (prevMoveLaneDirection == MoveLaneContinueDirection)
            {
                MoveLaneContinueTime += elapsedTime;
            }

            if (RaceUtilMath.Approximately(_laneDistance, targetLane))
            {
                LastSelfLaneMoveSpeedPerSec = 0; // 移動し終わったら移動速度リセット。
            }
        }

        public bool IsForceCheckNextLaneOneTime 
        { 
            get { return _horseRaceAI.IsForceCheckNextLaneOneTime; }
            set { _horseRaceAI.IsForceCheckNextLaneOneTime = value; }
        }

        public bool IsForceInMoveEnable 
        { 
            get { return _horseRaceAI.IsForceInMoveEnable; }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 自力のレーン移動速度計算。
        /// </summary>
        /// <remarks>符号無しの１秒当たりの移動速度を返す。</remarks>
        //---------------------------------------------------------------
        private float CalcLaneMoveSpeed(float deltaTime)
        {
            // レーン移動速度は以下の２つに分かれる。
            // １．自力のレーン移動速度
            // ２．スキル効果値によるレーン移動速度加算値
            // １は加速/減速が行われ、その結果に対して２のスキル効果値が上乗せされる。

            //-----------------------------------------------------------
            // 自力のレーン移動速度更新。
            //-----------------------------------------------------------
            // 目標値。
            float targetSelfLaneMoveSpeed = ParamDefine.laneMoveSpeedBase * (ParamDefine.laneMoveSpeedAdd + Pow * ParamDefine.laneMoveSpeedPowCoef);

            // 目標値の補正。
            if (GetPhase() >= Gallop.RaceDefine.HorsePhase.End)
            {
                // 終盤は全体的に広がり始めるので後ろにいるウマほど早く横移動をさせる
                // ※これをしないとスピード差があまり無い場合にきれいに並んで広がってしまう
                targetSelfLaneMoveSpeed *= 1.0f + (0.01f * CurOrder);
            }
            else
            {
                // 最初のインに寄るイベントの手前までは、アウトにいるほどレーン移動速度を上げる。
                if (GetDistance() <= _firstMoveLanePointDistance)
                {
                    targetSelfLaneMoveSpeed = RaceUtil.CalcLaneMoveSpeedBeforeFirstMoveLanePoint(this, targetSelfLaneMoveSpeed);
                }
            }

            // 現在速度→目標値への加速or減速。
            float accelPerSec = RaceUtil.CalcMoveLaneAccelPerSec(ParamDefine);
            float selfLaneMoveSpeed = LastSelfLaneMoveSpeedPerSec;
            if (selfLaneMoveSpeed < targetSelfLaneMoveSpeed)
            {
                selfLaneMoveSpeed += accelPerSec * deltaTime;
                if (selfLaneMoveSpeed > targetSelfLaneMoveSpeed)
                {
                    selfLaneMoveSpeed = targetSelfLaneMoveSpeed;
                }
            }
            else if (selfLaneMoveSpeed > targetSelfLaneMoveSpeed)
            {
                selfLaneMoveSpeed -= accelPerSec * deltaTime;
                if (selfLaneMoveSpeed < targetSelfLaneMoveSpeed)
                {
                    selfLaneMoveSpeed = targetSelfLaneMoveSpeed;
                }
            }

            LastSelfLaneMoveSpeedPerSec = selfLaneMoveSpeed;

            //-----------------------------------------------------------
            // スキルによる速度加算。
            // スキル効果値分は、自力の加速/減速後の値に丸ごと上乗せする。
            //-----------------------------------------------------------
            float retLaneMoveSpeed = ApplyModifier(SkillDefine.SkillModifierParam.LaneMoveSpeed, selfLaneMoveSpeed);

            //-----------------------------------------------------------
            // 上限/下限クリッピング。
            // スキル効果値分も含めた最終値に対してクリッピングを行う。
            //-----------------------------------------------------------
            if (retLaneMoveSpeed < Gallop.RaceDefine.LANE_MOVE_SPEED_MIN)
            {
                retLaneMoveSpeed = Gallop.RaceDefine.LANE_MOVE_SPEED_MIN;
            }
            else if (retLaneMoveSpeed > Gallop.RaceDefine.LANE_MOVE_SPEED_MAX)
            {
                // もし最大値定義を上回るようなら、スキル効果値の累積による可能性が高い。
                // 最大値に達しているということは横高速移動をしているはずなので、
                // プランナーに相談してスキル効果値の見直しか、高速移動を許容して最大値を変更する。
                Debug.LogError("レーン移動速度が最大値を上回っています。speed=" + retLaneMoveSpeed);
                retLaneMoveSpeed = Gallop.RaceDefine.LANE_MOVE_SPEED_MAX;
            }

            return retLaneMoveSpeed;
        }

        private float UpdateSelfSpeed(float deltaTime, float maxSpeed)
        {
            float selfSpeed = _lastSelfSpeed;   // 現在速度
            float targetSpeed = _horseRaceAI.GetTargetSpeed();   // ねらいたい速度

        #if CYG_DEBUG
            DbgAccelPerSec = 0;
        #endif

            //-----------------------------------------------------------
            // 自然減速スキルの場合スキル効果終了後一度だけ効果反映させる
            //-----------------------------------------------------------
            selfSpeed += ApplyOneTimeSkillModifier(SkillDefine.SkillModifierParam.CurrentSpeedWithNaturalDeceleration, 0);

            // 現在速度を狙いたい速度に合わせる。
            // 加速。
            if (targetSpeed > selfSpeed)
            {
                float accel = _accelCalc.CalcAccelPerSec();
            #if CYG_DEBUG
                DbgAccelPerSec = accel;
            #endif
                accel *= deltaTime;
                selfSpeed += accel;
                if (selfSpeed > targetSpeed)
                {
                    selfSpeed = targetSpeed;
                }
            }
            // 減速。
            else if(targetSpeed < selfSpeed)
            {
                bool isPaseDown = IsPositionKeep && PositionKeepMode == Gallop.RaceDefine.PositionKeepMode.PaseDown;
                float decl = RaceUtil.CalcDeclPerSec(GetPhase(), GetHp(), IsFinished(), isPaseDown, ParamDefine);
            #if CYG_DEBUG
                DbgAccelPerSec = -decl;
            #endif
                decl *= deltaTime;
                selfSpeed -= decl;
                if (selfSpeed < targetSpeed)
                {
                    selfSpeed = targetSpeed;
                }
            }

            // #62949 cs0988 スタートダッシュ中はスタートダッシュ終了速度に達したらそこで止める。
            if (IsStartDash)
            {
                if (selfSpeed > StartDashSpeedThreshold)
                {
                    selfSpeed = StartDashSpeedThreshold;
                }
            }

#if CYG_DEBUG
            // デバッグ機能：AI機能ON/OFF：加速減速無効。
            if (!RaceSimulateDebugger.IsEnableAISpeedAccel)
            {
                selfSpeed = targetSpeed;
            }
#endif

            // 自力で出している速度。HP消費計算にはこの値を使う。
            _lastSelfSpeed = RaceUtilMath.Clamp(selfSpeed, MinSpeed, maxSpeed);
            return _lastSelfSpeed;
        }

        //---------------------------------------------------------------
        private float CalcMaxSpeed()
        {
            float retMaxSpeed = Gallop.RaceDefine.MAX_SPEED;

            // 前方ブロックされているときの速度調整。
            var blockHorse = _horseRaceAI.GetBlockHorse();
            if (blockHorse != null)
            {
                // 前方ブロックされているとき、自分の最高速度はそのキャラの速度に合わせる。
                retMaxSpeed = RaceUtil.CalcMaxSpeedBlockSimulate(blockHorse, ParamDefine.Block);
                if (retMaxSpeed < MinSpeed)
                {
                    retMaxSpeed = MinSpeed;
                }
            }

            return retMaxSpeed;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 現在の距離を元にPhase更新。
        /// </summary>
        //---------------------------------------------------------------
        private void UpdatePhase()
        {
            _phase = _raceInfo.GetPhaseByDistance(_distance);
        }

        //---------------------------------------------------------------
        public void UpdateOrderChangeCount(float deltaTime)
        {
            _orderChangeCounterUp.Update(deltaTime);
            _orderChangeCounterDown.Update(deltaTime);
        }
        #endregion <更新>

        public void AddDistance(float realMovedDistance)
        {
            _distance += realMovedDistance;
        }

        public bool IsEnableInMove => _horseRaceAI.IsEnableInMove;
        public bool IsEnableOutMove => _horseRaceAI.IsEnableOutMove;
        public bool IsBlockAll() { return IsBlockFront() && !_horseRaceAI.IsEnableInMove && !_horseRaceAI.IsEnableOutMove; }
        public bool IsBlockFront() { return _horseRaceAI.IsBlockFront(); }
        public bool IsBlockSide() { return _horseRaceAI.IsBlockSide(); }
        public IHorseRaceInfoSimulate GetBlockHorseFront() { return (null != _horseRaceAI.GetBlockHorse()) ? _horseRaceAI.GetBlockHorse().infoSimulate : null; }
        public IHorseRaceInfoSimulate GetBlockHorseIn() { return (null != _horseRaceAI.GetBlockHorseIn()) ? _horseRaceAI.GetBlockHorseIn().infoSimulate : null; }
        public IHorseRaceInfoSimulate GetBlockHorseOut() { return (null != _horseRaceAI.GetBlockHorseOut()) ? _horseRaceAI.GetBlockHorseOut().infoSimulate : null; }
        public float BlockFrontAccumulateTime { get; private set; }
        public float BlockFrontOrSideAccumulateTime { get; private set; }
        public float BlockFrontContinueTime { get; private set; }
        public float BlockSideContinueTime { get; private set; }
        public float BlockFrontHpEmptyContinueTime { get; private set; }

        public Dictionary<Gallop.RaceDefine.HorsePhase, float> BlockSideMaxContinueTimeByPhase { get; protected set; } = new Dictionary<Gallop.RaceDefine.HorsePhase, float>();
        
        /// <summary>
        /// フェーズごとのサイドをブロックされた時間のテーブルを初期化
        /// </summary>
        private void InitBlockSideMaxContinueTimeByPhase()
        {
            BlockSideMaxContinueTimeByPhase.Clear();

            var phases = new [] { Gallop.RaceDefine.HorsePhase.Start, Gallop.RaceDefine.HorsePhase.MiddleRun, Gallop.RaceDefine.HorsePhase.End, Gallop.RaceDefine.HorsePhase.LastSpurt };
            foreach (var phase in phases)
            {
                BlockSideMaxContinueTimeByPhase.Add(phase, 0f);
            }
        }
        
        public float VisibleDistance
        {
            get
            {
                float retVisibleDistance = _baseVisibleDistance;
                retVisibleDistance = ApplyModifier(SkillDefine.SkillModifierParam.VisibleDistance, retVisibleDistance);
                retVisibleDistance = Math.Max(retVisibleDistance, VISIBLE_DISTANCE_MIN);
                return retVisibleDistance;
            }
        }
        public int VisibleHorseCount => _horseRaceAI.VisibleHorseCount;
        public Gallop.AroundHorse[] VisibleHorses => _horseRaceAI.VisibleHorses;

        public Gallop.AroundHorse[] NearHorses => _horseRaceAI.NearHorses;
        public int NearHorseCount => _horseRaceAI.NearHorseCount;
        public float NearHorseAroundAccumulateTime { get; private set; }
        public bool IsSurrounded => _horseRaceAI.IsSurrounded;
        private float[] _behindHorseNearLaneContinueTimeArray;

        public Gallop.AroundHorse[] GetAroundHorses() { return _horseRaceAI.GetAroundHorses(); }

        public float CongestionTime { get; private set; }

        public float BehindHorseNearContinueTime { get; private set; }
        public float InfrontHorseNearContinueTime { get; private set; }
        public float BehindHorseNearLaneContinueTime { get; private set; }
        public float InfrontHorseNearLaneContinueTime { get; private set; }
        public float GetBehindHorseNearLaneContinueTime(int paramSet)
        {
            if (paramSet < 0 || paramSet >= _behindHorseNearLaneContinueTimeArray.Length)
            {
                return 0;
            }

            return _behindHorseNearLaneContinueTimeArray[paramSet];
        }
        public float TopLeadMaxAmount { get; protected set; }
        
        public float GetTargetSpeed() { return _horseRaceAI.GetTargetSpeed(); }
        public float GetTargetSpeedWithSkill()
        {
            return ApplyModifier(SkillDefine.SkillModifierParam.CurrentSpeed, GetTargetSpeed());
        }
        public float GetTargetLane() { return _horseRaceAI.GetTargetLane(); }

        public void LotTemptation()
        {
            _horseRaceAI.LotTemptation();
        }

        public bool IsTemptation => _horseRaceAI.IsTemptation;
        public TemptationMode TemptationMode => _horseRaceAI.TemptationMode;
        public int TemptationCount => _horseRaceAI.TemptationCount;
        public bool IsTemptationStartEnable => _horseRaceAI.IsTemptationStartEnable;

        public bool IsOvertake() { return _horseRaceAI.IsOverTake(); }
        public List<Gallop.AroundHorse> GetOverTakeHorseList() { return _horseRaceAI.GetOverTakeHorse(); }
        public float OverTakeTargetContinueTime { get; private set; }
        public float OverTakeTargetHaveNoOrderUpContinueTime { get; set; }
        public float OverTakeTargetHaveNoOrderDownContinueTime { get; set; }

#if CYG_DEBUG
        public List<DbgOverTakeLog> DbgOrderChangeCounterUpOverTakeLogList => _horseRaceAI?.DbgOrderChangeCounterUpOverTakeLogList;
        public List<DbgOverTakeLog> DbgOrderChangeCounterDownOverTakeLogList => _horseRaceAI?.DbgOrderChangeCounterDownOverTakeLogList;
#endif
        
        public bool IsOrderInContinue(SkillDefine.OrderInType type)
        {
            return IsOrderInContinueArray[(int)type];
        }

        public bool IsOrderOutContinue(SkillDefine.OrderOutType type)
        {
            return IsOrderOutContinueArray[(int)type];
        }
        
    #if CYG_DEBUG
        public IHorseRaceAI DbgHorseAI { get { return _horseRaceAI; } }
        public List<ScoreBonusRate> DbgBonusRateList { get; set; }
        public int DbgTeamStadiumTotalScore { get; set; }

        public float DbgTemptationPerRandom { get; set; }
        public float DbgTemptationPerWiz { get; set; }
        public int DbgTemptationStartSection { get; set; }
        
        public bool DbgIsEnableInMove { get { return _horseRaceAI.IsEnableInMove; } }
        public bool DbgIsEnableOutMove { get { return _horseRaceAI.IsEnableOutMove; } }
        public float DbgInLaneSpace { get { return _horseRaceAI.DbgInLaneSpace; } }
        public float DbgOutLaneSpace { get { return _horseRaceAI.DbgOutLaneSpace; } }

        public float DbgOverTakeLane { get { return _horseRaceAI.DbgOverTakeLane; } }
        public float DbgOverTakeCoolDownTime { get { return _horseRaceAI.DbgOverTakeCoolDownTime; } }
        
        public List<int> DbgOverTakenHorseIndexList => _horseRaceAI.DbgOverTakenHorseIndexList;
        
        public float DbgBaseTargetSpeed { get; set; }
        public float DbgBaseTargetSpeedRandomMin { get; set; }
        public float DbgBaseTargetSpeedRandomMax { get; set; }
        public float DbgCourseSetBaseSpeedCoef => _baseSpeedCoef;
        public float DbgSkillAddTargetSpeed { get; set; }
        public float DbgForceInMoveAddTargetSpeed { get; set; }
        public float DbgPositionKeepBaseTargetSpeedMultiply { get; set; }

        public float DbgAccelPerSec { get; set; }
        public float DbgDecHpPerSec { get; set; }
        public float DbgAddHp { get; set; }
        
        public float DbgSlopeAddTargetSpeed { get; set; }
        public float DbgLastSpurtTargetSpeed { get; set; }

        public float DbgConsumeHpOnTemptation { get; private set; }
        public List<LastSpurtCandidate> DbgLastSpurtCandidateList { get; set; }
        public LastSpurtCandidate DbgLastSpurtUseCandidate { get; set; }
        public Queue<float> DbgLastSpurtLastCalculatedSpeedQueue { get; private set; } = new Queue<float>();
        public int DbgLastSpurtCalcCount { get; set; }

        public float DbgLastMoveOutStartDistance { get; set; } = -1;
        public List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteTopStartGroupHorseList { get; set; }
        public List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteTopEndGroupHorseList { get; set; }
        public List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteFightStartGroupHorseList { get; set; }
        public List<RaceSimulateHorseResultDebugData.CompeteGroupHorse> DbgCompeteFightEndGroupHorseList { get; set; }
        public int DbgPaseMakerHorseIndex { get; set; } = Gallop.RaceDefine.HORSE_INDEX_NULL;
        public int DbgTopHorseNotMostForwardRunningStyleCnt { get; set; }
        public float[] DbgDistanceDiffOnPositionKeepCalcArray { get; set; }
    #if GALLOP
        public int NpcType => _rawHorseData.npc_type;
        public int FrameOrder => _rawHorseData.frame_order;
    #endif
#endif
#if CYG_DEBUG && UNITY_EDITOR
        public int DbgBaseHorseIndex { get; set; }
        private HorseFurlongStat _furlongStat = null;
        public HorseFurlongData[] FurlongDatas
        {
            get { return (null != _furlongStat) ? _furlongStat.FurlongDatas : null; }
        }
        public LastSpurtParam LastSpurtParam { get; set; }
#endif
        public bool IsLastSpurt => _horseRaceAI.IsLastSpurt;
        public float LastSpurtStartDistance => _horseRaceAI.LastSpurtStartDistance;
        public bool IsCalcLastSpurt => _horseRaceAI.IsCalcLastSpurt;

        public LastSpurtCalcResult LastSpurtCalcResult => _horseRaceAI.LastSpurtCalcResult;
        public float FirstCalcLastSpurtTargetSpeed => _horseRaceAI.FirstCalcLastSpurtTargetSpeed;
        public float LastSpurtTargetSpeed => _horseRaceAI.LastSpurtTargetSpeed;
        public bool IsReqCalcLastSpurt
        {
            get { return _horseRaceAI.IsReqCalcLastSpurt; }
            set { _horseRaceAI.IsReqCalcLastSpurt = value; }
        }

        public bool IsPositionKeepSection => _horseRaceAI.IsPositionKeepSection;
        public bool IsPositionKeep => _horseRaceAI.IsPositionKeep;
        public Gallop.RaceDefine.PositionKeepMode PositionKeepMode => _horseRaceAI.PositionKeepMode;
        public int PositionKeepCount { get; set; }

        /// <summary>
        /// ゴール済みかどうか。
        /// </summary>
        public bool IsFinished()
        {
            return _isFinished;
        }

        private void CheckFinished()
        {
            if (_isFinished)
            {
                return;
            }

            int courseDistance = _raceInfo.CourseDistance;
#if CYG_DEBUG
            if(RaceSimulateDebugger.IsRecordOverRun)
            {
                courseDistance += RaceManagerSimulate.Instance.CourseLane.OverRunDistance;
            }
#endif
            if (_distance > courseDistance)
            {
                _isFinished = true;
                OnFinish();
            }
        }

        private void OnFinish()
        {
            if (FinishTimeScaled < 0.0f)
            {
                // ゴールの瞬間の時間を計算。
                CalcFinishTime();
                
                // スキルは全停止。
                SkillManager.ClearSkills();
            }
        }

        public virtual void OnAllFinish()
        {
        }

        public virtual void OnSkip()
        {
        }

        public int CalcSection()
        {
            return RaceUtil.Distance2Section(
                _raceInfo.CourseSectionDistance,
                GetDistance());
        }

        /// <summary>
        /// 最低速度の更新。
        /// </summary>
        private void UpdateMinSpeed()
        {
            // ゴール後オーバーラン中の最低速度。
            if(IsFinished())
            {
                MinSpeed = Gallop.RaceDefine.OVERRUN_SPEED_MIN;
                return;
            }

            // 出走直後、低速から急加速する間は初速を最低速度とする。
            if(IsStartDash)
            {
                MinSpeed = ParamDefine.StartSpeed;
                return;
            }

            // 通常レース走行中の最低速度。現在の根性によって少し上乗せされる。
            MinSpeed = _raceDefaultMinSpeed + (float)Math.Sqrt(Guts * ParamDefine.Speed.MinSpeedGutsCoefSqrt) * ParamDefine.Speed.MinSpeedGutsCoef;
            
            // 前方ブロックされている場合、出走直後だと相手の最低速度が_raceDefaultMinSpeedを下回るParamDefine.StartSpeedの可能性がある。その場合はそっちを最低速度にする。
            // ※_raceDefaultMinSpeedを最低速度とし続けてしまうと減速してきた相手に重なりが発生してしまう。
            var frontBlockHorse = GetBlockHorseFront();
            if (frontBlockHorse != null)
            {
                MinSpeed = Math.Min(MinSpeed, frontBlockHorse.MinSpeed);
            }
        }

        public float GetLastSpeed()
        {
            return _lastSpeed;
        }
        public void SetLastSpeed(float spd)
        {
            _lastSpeed = Math.Max(spd, MinSpeed);
        }

        public float GetLastSpeedWithoutDebuffSkill()
        {
            return _lastSpeedWithoutDebuffSkill;
        }
        public void SetLastSpeedWithoutDebuffSkill(float spd)
        {
            _lastSpeedWithoutDebuffSkill = Math.Max(spd, MinSpeed);
        }

        private float _lastLaneMoveSpeed = 0;
        public float LastSelfLaneMoveSpeedPerSec
        {
            get { return _lastLaneMoveSpeed; }
            set { _lastLaneMoveSpeed = Math.Max(value, 0.0f); }
        }

        public int RawSpeed => _raceParam.RawSpeed;
        public float BaseSpeed => _raceParam.BaseSpeed;
        public float Speed
        {
            get
            {
                Debug.Assert(_isBaseStatusAdjusted);
                float retSpeed = _baseSpeedAdjusted;
                retSpeed = ApplyModifier(SkillDefine.SkillModifierParam.Speed, retSpeed);
                if (_raceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
                {
                    retSpeed = RaceUtilMath.Max(retSpeed, Gallop.RaceDefine.STATUS_MIN);
                }
                else
                {
                    retSpeed = RaceUtilMath.Clamp(retSpeed, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                }

                return retSpeed;
            }
        }

        public int RawStamina => _raceParam.RawStamina;
        public float BaseStamina => _raceParam.BaseStamina;
        public float Stamina
        {
            get
            {
                float retStamina = _baseStaminaAdjusted;
                retStamina = ApplyModifier(SkillDefine.SkillModifierParam.Stamina, retStamina);
                if (_raceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
                {
                    retStamina = RaceUtilMath.Max(retStamina, Gallop.RaceDefine.STATUS_MIN);
                }
                else
                {
                    retStamina = RaceUtilMath.Clamp(retStamina, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                }

                return retStamina;
            }
        }

        public int RawPow => _raceParam.RawPow;
        public float BasePow => _raceParam.BasePow;
        public float Pow
        {
            get
            {
                float retPow = _basePowAdjusted;
                retPow = ApplyModifier(SkillDefine.SkillModifierParam.Power, retPow);
                if (_raceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
                {
                    retPow = RaceUtilMath.Max(retPow, Gallop.RaceDefine.STATUS_MIN);
                }
                else
                {
                    retPow = RaceUtilMath.Clamp(retPow, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                }

                return retPow;
            }
        }

        public int RawGuts => _raceParam.RawGuts;
        public float BaseGuts => _raceParam.BaseGuts;
        public float Guts
        {
            get
            {
                Debug.Assert(_isBaseStatusAdjusted);
                float retGuts = _baseGutsAdjusted;
                retGuts = ApplyModifier(SkillDefine.SkillModifierParam.Guts, retGuts);
                if (_raceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
                {
                    retGuts = RaceUtilMath.Max(retGuts, Gallop.RaceDefine.STATUS_MIN);
                }
                else
                {
                    retGuts = RaceUtilMath.Clamp(retGuts, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                }

                return retGuts;
            }
        }

        public int RawWiz => _raceParam.RawWiz;
        public float BaseWiz => _raceParam.BaseWiz;
        public float Wiz
        {
            get
            {
                Debug.Assert(_isBaseStatusAdjusted);
                float retWiz = _baseWizAdjusted;
                retWiz = ApplyModifier(SkillDefine.SkillModifierParam.Wiz, retWiz);
                if (_raceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
                {
                    retWiz = RaceUtilMath.Max( retWiz, Gallop.RaceDefine.STATUS_MIN);
                }
                else
                {
                    retWiz = RaceUtilMath.Clamp(retWiz, Gallop.RaceDefine.STATUS_MIN, Gallop.RaceDefine.STATUS_MAX);
                }

                return retWiz;
            }
        }

        /// <summary>やる気</summary>
        public Gallop.RaceDefine.Motivation Motivation => (Gallop.RaceDefine.Motivation)_rawHorseData.motivation;
        /// <summary>やる気による基礎ステータス補正倍率</summary>
        public float MotivationCoef => _raceParam.MotivationCoef;

        public int GutsOrder { get; set; }
        public int WizOrder { get; set; }

        public bool IsStartDash { get; private set; }

        public float GetDistance()
        {
            return _distance;
        }

        public void SetDistance(float distance)
        {
            _distance = distance;
            if (_distance < 0.0f)
            {
                _distance = 0.0f;
            }
        }

        public Gallop.RaceDefine.LaneType Lane
        {
            get { return RaceUtil.CalcLane(GetLaneDistance()); }
        }

        public float GetLaneDistance()
        {
            return _laneDistance;
        }

        public void SetLaneDistance(float lanePosition)
        {
            _laneDistance = lanePosition;
        }

        public float LaneDistanceMin { get; set; }
        public float LaneDistanceMax { get; set; }
        public float LaneDistancePerInAllHorses { get; set; }
        private float _firstMoveLanePointDistance;

        public float GetCurLaneMoveSpeed() { return _laneMoveSpeed; }

        //---------------------------------------------------------------
        public bool IsCorner { get { return CurCorner != Gallop.RaceDefine.CORNER_NULL; } }
        public int CurCorner { get; set; }
        public float CornerEndDistance { get; set; }
        public bool IsCornerEventPassed { get; set; }

        //---------------------------------------------------------------
        public Gallop.RaceDefine.StraightFrontType CurStraightFrontType { get; set; }
        public int CurStraightTypeNumber { get; set; } = CourseStraight.FRONT_TYPE_NUMBER_NULL;
        public bool IsStraightFirst => IsStraight && !IsCornerEventPassed;
        
        //---------------------------------------------------------------
        public bool IsStraightLast
        {
            get
            {
                // 最終直線用のイベントパラメータが追加されたらそちらを参照する形に切り替える
                if (RaceManagerSimulate.Instance.IsExistLastStraightEvent)
                {
                    return IsLastStraightCourseEvent;
                }
                else
                {
                    return IsStraight && IsFinalCorner;
                }
            }
        }
        
        //---------------------------------------------------------------
        public bool IsLastStraightCourseEvent { get; set; }

        //---------------------------------------------------------------
        public bool IsStraight { get; set; }

        public bool IsClog { get; private set; }

        //---------------------------------------------------------------
        public void CreateDefeat()
        {
            _defeat = HorseDefeatCalculator.CalcDefeat(this, _horseAccessor, ParamDefine.Defeat);
            _defeatInitialized = true;
        }

        private Gallop.RaceDefine.DefeatType _defeat = Gallop.RaceDefine.DefeatType.Null;
        private bool _defeatInitialized = false;
        public Gallop.RaceDefine.DefeatType Defeat
        {
            get
            {
                if (!_defeatInitialized)
                {
                    Debug.LogWarning("敗因フラグが初期化されていません。");
                }
                return _defeat;
            }

            set
            {
                _defeat = value;
            }
        }

        #region Skills
        private void _InitActivateSkillCountByPhase()
        {
            _activateSkillCountByPhase.Clear();

            var phases = new [] { Gallop.RaceDefine.HorsePhase.Start, Gallop.RaceDefine.HorsePhase.MiddleRun, Gallop.RaceDefine.HorsePhase.End, Gallop.RaceDefine.HorsePhase.LastSpurt };
            foreach (var phase in phases)
            {
                _activateSkillCountByPhase.Add(phase, 0);
            }
        }

        public int GetActivateSkillCount()
        {
            int cnt = 0;
            var allPhase = RaceUtilEnum.GetEnumValues(typeof(Gallop.RaceDefine.HorsePhase));
            foreach(Gallop.RaceDefine.HorsePhase phase in allPhase)
            {
                cnt += GetActivateSkillCountByPhase(phase);
            }
            return cnt;
        }

        public int GetActivateSkillCountByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            int countByPhase = 0;
            if (!_activateSkillCountByPhase.TryGetValue(phase, out countByPhase))
            {
                return 0;
            }
            return countByPhase;
        }

        public void AddActivateSkillCountByPhase(Gallop.RaceDefine.HorsePhase phase, int add)
        {
            if (!_activateSkillCountByPhase.ContainsKey(phase))
            {
                return;
            }
            _activateSkillCountByPhase[phase] += add;
        }

        public int[] GetActivateSkillCountByRarityArray()
        {
            return _activateSkillCountByRarity;
        }
        
        public int GetActivateSkillCountByRarity(SkillDefine.SkillRarity rarity)
        {
            int index = (int)rarity - 1;
            if (index >= 0 && index < _activateSkillCountByRarity.Length)
            {
                return _activateSkillCountByRarity[index];
            }
            else
            {
                return 0;
            }
        }

        public void AddActivateSkillCountByRarity(SkillDefine.SkillRarity rarity, int add)
        {
            int index = (int)rarity - 1;
            if (index >= 0 && index < _activateSkillCountByRarity.Length)
            {
                _activateSkillCountByRarity[index] += add;
            }
        }

        public int GetActivateSpeedSkillCount()
        {
            return _activateSpeedSkillCount;
        }

        public void AddActivateSpeedSkillCount()
        {
            _activateSpeedSkillCount++;
        }

        public int GetActivateHealSkillCount()
        {
            return _activateHealSkillCount;
        }

        public void AddActivateHealSkillCount()
        {
            _activateHealSkillCount++;
        }
        
        public int GetActivateSpecificTagGroupSkillCount()
        {
            return _activateSpecificTagGroupSkillCount;
        }

        public void AddActivateSpecificTagGroupSkillCount()
        {
            _activateSpecificTagGroupSkillCount++;
        }
        
        public int GetActivateSpecificSkillAbilityTypeGroupSkillCount()
        {
            return _activateSpecificSkillAbilityTypeGroupSkillCount;
        }

        public void AddActivateSpecificSkillAbilityTypeGroupSkillCount()
        {
            _activateSpecificSkillAbilityTypeGroupSkillCount++;
        }

        public int GetActivateSkillCountByDistance(DistanceDivision distanceDivision)
        {
            _activateSkillCountByDistance.TryGetValue(distanceDivision, out int countByDistance);
            return countByDistance;
        }

        public void AddActivateSkillCountByDistance(float distance, int add)
        {
            float halfCourseDistance = _raceInfo.CourseDistance * 0.5f;
            var distanceDivision = (distance < halfCourseDistance)
                ? DistanceDivision.FirstHalf
                : DistanceDivision.SecondHalf;
            if (!_activateSkillCountByDistance.ContainsKey(distanceDivision))
            {
                _activateSkillCountByDistance.Add(distanceDivision, 0);
            }
            _activateSkillCountByDistance[distanceDivision] += add;
        }

#if CYG_DEBUG
        public Dictionary<Gallop.RaceDefine.HorsePhase, int> DbgGetActivateSkillCount()
        {
            return _activateSkillCountByPhase;
        }
#endif

        public float ApplyModifier(SkillDefine.SkillModifierParam type, float value)
        {
            return SkillModifierReceiver.ApplyModifier(type, value);
        }

        public float ApplyModifierBuffParam(SkillDefine.SkillModifierParam type, float value)
        {
            return SkillModifierReceiver.ApplyModifierBuffParam(type, value);
        }

        public float ApplyOneTimeSkillModifier(SkillDefine.SkillModifierParam type, float value)
        {
            return SkillModifierReceiver.ApplyOneTimeSkillModifier(type, value);
        }
        
        public bool ApplyModifier(SkillDefine.SkillModifierParam type, bool value)
        {
            return SkillModifierReceiver.ApplyModifier(type, value);
        }

        public void AddModifier(SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
        {
            SkillModifierReceiver.AddModifier(type, modifier);
        }

        /// <summary>
        /// SkillParamModifierAddを生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierAdd CreateSkillParamAddModifier(
            float value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        )
        {
            return SkillModifierReceiver.CreateSkillParamAddModifier(value, time, skillModifierParam, isActivateOthers);
        }

        /// <summary>
        /// SkillParamModifierSetを生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierSet CreateSkillParamSetModifier(
            float value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        )
        {
            return SkillModifierReceiver.CreateSkillParamSetModifier(value, time, skillModifierParam, isActivateOthers);
        }

        /// <summary>
        /// SkillParamModifierSetを生成
        /// </summary>
        /// <param name="value"> 効果量(true or false) </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierSet CreateSkillParamSetModifier(
            bool value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        )
        {
            return SkillModifierReceiver.CreateSkillParamSetModifier(value, time, skillModifierParam, isActivateOthers);
        }

        /// <summary>
        /// SkillParamModifierMultiplayを生成
        /// </summary>
        /// <param name="value"> 効果量(true or false) </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierMultiply CreateSkillParamMultiplyModifier(
            float value, float? time, SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        )
        {
            return SkillModifierReceiver.CreateSkillParamMultiplyModifier(value, time, skillModifierParam, isActivateOthers);
        }

        public bool HasModifier(SkillDefine.SkillModifierParam type)
        {
            return SkillModifierReceiver.HasModifier(type);
        }

        public void RemoveModifier(SkillDefine.SkillModifierParam type)
        {
            SkillModifierReceiver.RemoveModifier(type);
        }
        
        public void UpdateModifier(float deltaTime)
        {
            SkillModifierReceiver.UpdateModifiers(deltaTime);
        }

        public bool IsDebuffCancel()
        {
            const bool DEBUFF_CANCEL_DEFAULT = false;
            bool isDebuffCancel = ApplyModifier(SkillDefine.SkillModifierParam.DebuffCancel, DEBUFF_CANCEL_DEFAULT);
            return isDebuffCancel;
        }
        
        public float GetHpRateDemeritValue()
        {
            // memo: 初期値0を渡すことで、設定された効果量が取得できる
            const float VALUE_DEFAULT = 0;
            var hpRateDemeritValue = ApplyModifier(SkillDefine.SkillModifierParam.HpRateDemerit, VALUE_DEFAULT);
            return hpRateDemeritValue;
        }

        /// <summary>
        /// 特定スキル発動のスキル効果によるスキル発動予約リストの更新
        /// </summary>
        private void UpdateReservedSpecificActivateSkillList()
        {
            var reserveSpecificActivateSkillIdArray =
                SkillManager.GetReserveSpecificActivateSkillIdArray(SkillDefine.SkillAbilityType.ActivateSpecificSkill);
            if (reserveSpecificActivateSkillIdArray != null)
            {
                // とりあえず全キャラに積んでおく。所持していない場合はスキル発動の判定自体行われないので積んでおいても問題は無い
                var horseRaceInfos = _horseAccessor.GetHorseRaceInfos();
                for (int i = 0; i < horseRaceInfos.Length; i++)
                {
                    // 1グループ目処理後に更新をかけているので基本的にActivateSpecificSkillは1グループ目での更新を想定としている
                    horseRaceInfos[i].ClearReservedSpecificActivateSkillList();
                    horseRaceInfos[i].AddReservedSpecificActivateSkill(reserveSpecificActivateSkillIdArray);
                }
            }
        }

    #if CYG_DEBUG
        public List<ISkillParamModifier> DbgGetModifier(SkillDefine.SkillModifierParam type)
        {
            return SkillModifierReceiver.DbgGetModifier(type);
        }

        /// <summary>
        /// キャンセルされたスキル効果受信機を返す
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public List<ISkillParamModifier> DbgGetCancelledModifier(SkillDefine.SkillModifierParam type)
        {
            return SkillModifierReceiver.DbgGetCancelledModifier(type);
        }

        public void AddCancelledModifier(SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
        {
            SkillModifierReceiver.AddCancelledModifier(type, modifier);
        }
        
        public SkillModifierReceiver DbgGetModifierReceiver => SkillModifierReceiver;
    #endif

        public List<ISkillDetail> CurerntActiveSkills
        {
            get
            {
                return SkillManager.GetCurrentActiveSkill();
            }
        }

        private void AddSkills()
        {
            // 所持スキルの登録。
            if (_rawHorseData.skill_array != null)
            {
                foreach (var skillData in _rawHorseData.skill_array)
                {
                    AddSkillOne(skillData.skill_id, skillData.level);
                }
            }

            SkillManager.CreateSkillArray();
        }

        private void AddSkillOne(int skillId, int level)
        {
            var masterSkill = MasterManager.Instance.MasterSkillData.Get(skillId);
            if (null == masterSkill)
            {
                return;
            }

            var raceInfo = _raceInfo;
            var courseAttributeAccessor = RaceManagerSimulate.Instance;
            var timeAccessor = RaceManagerSimulate.Instance;
            var randomGenerator = RaceManagerSimulate.Instance;
            var skillEventRecorder = RaceManagerSimulate.Instance;

            var buildInfo = new SkillBuildInfo(
                masterSkill,
                level,
                this,
                _horseAccessor,
                courseAttributeAccessor,
                timeAccessor,
                randomGenerator,
                skillEventRecorder,
                raceInfo,
                ParamDefine.Skill);

            SkillManager.AddSkill(ref buildInfo);
        }

        public void ClearSkills()
        {
            SkillManager.ClearSkills();
        }

        //---------------------------------------------------------------
        public void UpdateSkillFirstGroup(float elapsedTime)
        {
            if (SkillManager == null)
            {
                return;
            }

            if (IsFinished())
            {
                return;
            }

            SkillManager.UpdateFirstGroup(elapsedTime);
        }

        //---------------------------------------------------------------
        public void PostUpdateSkillFirstGroup()
        {
            if (SkillManager == null)
            {
                return;
            }

            if (IsFinished())
            {
                return;
            }

            SkillManager.PostUpdateSkillFirstGroup();

            // 特定スキル強制発動のスキル効果による処理
            UpdateReservedSpecificActivateSkillList();
        }

        //---------------------------------------------------------------
        public void UpdateSkillSecondGroup(float elapsedTime)
        {
            if (SkillManager == null)
            {
                return;
            }

            if (IsFinished())
            {
                return;
            }

            SkillManager.UpdateSecondGroup(elapsedTime);
        }

        //---------------------------------------------------------------
        public void LotActivateSkill()
        {
            SkillManager.LotActivateSkill();
        }

        //---------------------------------------------------------------
        public void CheckSkillTriggerAndActivateFirstGroup()
        {
            SkillManager.CheckSkillTriggerAndActivateFirstGroup();
        }

        //---------------------------------------------------------------
        public void CheckSkillTriggerAndActivateSecondGroup()
        {
            SkillManager.CheckSkillTriggerAndActivateSecondGroup();
        }

        //---------------------------------------------------------------
        public SkillBase GetSkill(int skillID)
        {
            return SkillManager.GetSkill(skillID);
        }

        //---------------------------------------------------------------
        public SkillBase[] GetSkills()
        {
            return SkillManager.GetSkills();
        }

        //---------------------------------------------------------------
        public List<int> GetUsedSkillIdList()
        {
            return SkillManager.GetUsedSkillIdList();
        }

        //---------------------------------------------------------------
        public void ClearReservedSpecificActivateSkillList()
        {
            SkillManager.ClearReservedSpecificActivateSkillList();
        }

        //---------------------------------------------------------------
        public void AddReservedSpecificActivateSkill(int[] skillIdArray)
        {
            SkillManager.AddReservedSpecificActivateSkill(skillIdArray);
        }
        //---------------------------------------------------------------

#if UNITY_EDITOR && CYG_DEBUG
        public List<SkillManagerSimulate.DbgActivatedSkillInfo> GetDbgActivatedSkillList()
        {
            return SkillManager.DbgActivatedSkillInfoList;
        }
#endif
        #endregion

        //---------------------------------------------------------------
        public virtual void AddScoreSkillActivate(SkillDefine.SkillRarity rarity, int level, int groupRate)
        {
        }

        public virtual int CalcScoreTotal()
        {
            return 0;
        }
        
        public virtual List<ScoreData> ScoreList { get; private set; } = new List<ScoreData>();

        //---------------------------------------------------------------
        public virtual void AddChallengeMatchPointSkillActivate(SkillDefine.SkillRarity rarity, int level, int groupRate)
        {
        }

        public virtual int CalcChallengeMatchPointTotal()
        {
            return 0;
        }
        
        public virtual List<ChallengeMatchPointData> ChallengeMatchPointList { get; private set; } = new List<ChallengeMatchPointData>(); 

        public bool IsCompeteFight { get; set; }
        public int CompeteFightCount { get; set; }
        public int CompeteFightGroup { get; set; }
        public float CompeteFightAddTargetSpeed { get; set; }
        public float CompeteFightAddAccel { get; set; }
        public List<Gallop.AroundHorse> CompeteFightNearList { get; }
        
        
        public bool IsCompeteTop { get; set; }
        public int CompeteTopCount { get; set; }
        public float CompeteTopRemainTime { get; set; }
        public int CompeteTopGroup { get; set; }
        public float CompeteTopAddTargetSpeed { get; set; }

        #region <AI:上限突破パラメータパワー(足溜め)>
        public void UpdateActivateStatusConservePower()
        {
            _horseRaceAI.UpdateActivateStatusConservePower();
        }
        public void IncreaseConservePower(Gallop.RaceDefine.IncreaseConservePowerMode mode)
        {
            _horseRaceAI.IncreaseConservePower(mode);
        }

        public void DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode mode)
        {
            _horseRaceAI.DecreaseConservePower(mode);
        }

        public void DecreaseConservePower(List<Gallop.RaceDefine.DecreaseConservePowerMode> modeList)
        {
            _horseRaceAI.DecreaseConservePower(modeList);
        }

        public float ConservePowerAddAccel => _horseRaceAI.ConservePowerAddAccel;
        #endregion
        
        #region <AI:上限突破パラメータスタミナ>

        /// <summary> 上限突破パラメータスタミナ計算機発動状況更新 </summary>
        public void UpdateActivateStatusStaminaLimitBreakBuff()
        {
            _horseRaceAI.UpdateActivateStatusStaminaLimitBreakBuff();
        }
        /// <summary> 上限突破パラメータスタミナによる目指す速度加算値 </summary>
        public float StaminaLimitBreakBuffAddTargetSpeed => _horseRaceAI.StaminaLimitBreakBuffAddTargetSpeed;
        #endregion
        
        public void Release()
        {
        }

        //---------------------------------------------------------------
        public virtual void UpdateForceGoal(float deltaTime)
        {
        }
    }
}
#endif

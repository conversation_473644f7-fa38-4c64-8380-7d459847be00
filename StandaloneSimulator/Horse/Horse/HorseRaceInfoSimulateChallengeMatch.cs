#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中馬制御：チャレンジマッチシミュレーション用。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseRaceInfoSimulateChallengeMatch : HorseRaceInfoSimulate
    {
        //---------------------------------------------------------------
        // 定数。
        //---------------------------------------------------------------

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private HorseChallengeMatchPointCalculator _pointCalc;

        public override List<ChallengeMatchPointData> ChallengeMatchPointList => _pointCalc.PointList;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseRaceInfoSimulateChallengeMatch(HorseRaceInfoSimulate.InitDesc desc) : base(desc)
        {
            _pointCalc = new HorseChallengeMatchPointCalculator(
                this,
                _horseAccessor,
                RaceManagerSimulate.Instance,
                RaceManagerSimulate.Instance,
                _raceInfo.BorderTimeScaled,
                _raceInfo.RaceDifficulty);
        }

        //---------------------------------------------------------------
        public override void OnAllFinish()
        {
            base.OnAllFinish();

            CalcPointOnAllFinish();
        }

        //---------------------------------------------------------------
        private void CalcPointOnAllFinish()
        {
            //-----------------------------------------------------------
            // 全員ゴールした時のポイント加算。
            //-----------------------------------------------------------
            _pointCalc.OnAllFinish();

            //-----------------------------------------------------------
            // ここまでで展開ポイントの加算は終えている。
            //-----------------------------------------------------------
        #if CYG_DEBUG
            // デバッグ情報（ポイント合計値）保存用に呼び出す。
            CalcScoreTotal();
        #endif
        }

        //---------------------------------------------------------------
        public override void AddChallengeMatchPointSkillActivate(Gallop.SkillDefine.SkillRarity rarity, int level, int groupRate)
        {
            base.AddChallengeMatchPointSkillActivate(rarity, level, groupRate);
            _pointCalc.AddPointSkillActivate(rarity, level, groupRate);
        }
        
        //---------------------------------------------------------------
        public override int CalcChallengeMatchPointTotal()
        {
            int total = _pointCalc.CalcTotal();
            return total;
        }
        
        //---------------------------------------------------------------
        public override bool Update(float deltaTime)
        {
            bool ret = base.Update(deltaTime);

            // ポイント加算のために常時更新が必要なもの。
            _pointCalc.Update(deltaTime);
            
            return ret;
        }
        
        //---------------------------------------------------------------
        public override void CalcDelayTime(float max)
        {
            base.CalcDelayTime(max);
            
            // スタートダッシュ成功/失敗によるポイント加算。
            _pointCalc.AddPointGoodStart();
        } 
    }
}

#endif

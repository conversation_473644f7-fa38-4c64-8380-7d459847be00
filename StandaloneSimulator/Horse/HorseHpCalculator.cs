using System;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラのHp関連計算処理。
    /// </summary>
    /// <seealso cref="Test.TestHorseHpCalculator"/>
    //-------------------------------------------------------------------
    public static partial class HorseHpCalculator
    {
        /// <summary>
        /// １秒当たりHP減少量計算。
        /// </summary>
        /// <param name="horse">対象のキャラ。nullを渡すと状況別の通常減少レートは、常に「通常状態」を適用する。</param>
        /// <param name="groundModifierMultiHpSub">馬場補正によるHp減少倍率。</param>
        /// <param name="phase">現在のフェイズ。</param>
        /// <param name="guts">根性。</param>
        /// <param name="curSpeed">現在速度。</param>
        public static float CalcDecHpPerSec(
            IHorseRaceInfoSimulate horse,
            Gallop.RaceParamDefine.HpParam hpParam,
            float raceBaseSpeed,
            float groundModifierMultiHpSub, 
            Gallop.RaceDefine.HorsePhase phase, 
            float guts, 
            float curSpeed)
        {
            if (RaceManagerSimulate.Instance.RaceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit))
            {
                Debug.Assert(guts >= Gallop.RaceDefine.STATUS_MIN);
            }
            else
            {
                Debug.Assert(guts >= Gallop.RaceDefine.STATUS_MIN && guts <= Gallop.RaceDefine.STATUS_MAX);
            }

            float speedGap = CalcSpeedGap(hpParam, raceBaseSpeed, curSpeed);
            float decRate = CalcHpDecRate(horse, hpParam);
            float decHpPerSec = hpParam.HpDecBase * speedGap * decRate * groundModifierMultiHpSub;

            // 終盤以降、根性によって減少量が減少する。
            if (phase >= Gallop.RaceDefine.HorsePhase.End)
            {
                float gutsRate = 1 + hpParam.HpGutsCoef / (float)Math.Sqrt(guts * hpParam.HpGutsCoefSqrt);
                decHpPerSec *= gutsRate;
            }

            return decHpPerSec;
        }

        /// <summary>
        /// 指定速度で指定距離を走り続けるのに必要なHp計算。加減速は考慮しない。Hpの減少レートは「通常」固定。
        /// </summary>
        /// <param name="targetSpeed">速度。</param>
        /// <param name="distance">距離。</param>
        /// <returns>必要なHp量を返却。</returns>
        public static float CalcNeedHp(
            Gallop.RaceParamDefine.HpParam hpParam,
            float raceBaseSpeed,
            float groundModifierMultiHpSub, 
            Gallop.RaceDefine.HorsePhase phase, 
            float guts, 
            float targetSpeed, 
            float distance)
        {
            // 第５引数は、あえて狙いたい速度を現在速度として渡して計算する。
            float decHpPerSec = CalcDecHpPerSec(
                null,
                hpParam,
                raceBaseSpeed,
                groundModifierMultiHpSub, 
                phase, 
                guts, 
                targetSpeed);
            float needHp = decHpPerSec * (distance / targetSpeed);
            return needHp;
        }

        #region <Internal>
        /// <summary>
        /// HP減少量計算のための、現在速度とレース規定速度との差分から計算される変数。
        /// </summary>
        private static float CalcSpeedGap(Gallop.RaceParamDefine.HpParam hpParam, float raceBaseSpeed, float curSpeed)
        {
#if UNITY_EDITOR
            Debug.Assert(hpParam.SpeedGapParam1Pow > 0);
            Debug.Assert(RaceUtilMath.Approximately((float)Math.Pow(hpParam.SpeedGapParam1, 2), hpParam.SpeedGapParam1Pow));
#endif

            Debug.Assert(curSpeed - raceBaseSpeed > -hpParam.SpeedGapParam1);
            float retSpeedGap = (float)Math.Pow(((curSpeed - raceBaseSpeed) + hpParam.SpeedGapParam1), 2) / hpParam.SpeedGapParam1Pow;
            return retSpeedGap;
        }

        /// <summary>
        /// 状況に応じたHP減少レートの計算。
        /// </summary>
        /// <param name="horse">対象のキャラ。nullを渡すと常に通常減少レートを返却する。</param>
        /// <param name="isForceDecRateNormal">trueだと、HP減少レートは強制的にNormal値を使用する。</param>
        private static float CalcHpDecRate(IHorseRaceInfoSimulate horse, Gallop.RaceParamDefine.HpParam hpParam)
        {
            float retRate = hpParam.HpDecRateBaseNormal;

            //-----------------------------------------------------------
            // 状況参照用のキャラが渡されていないときは、通常減少レートを返却する。
            //-----------------------------------------------------------
            if (null == horse)
            {
                return retRate;
            }

            //-----------------------------------------------------------
            // 状況に応じて使用する減少レートを決定する。
            //-----------------------------------------------------------

            // スキルによる減少レート固定。
            float decRateBySkill = horse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.HpDecRate, 0);
            if (decRateBySkill > 0)
            {
                retRate = decRateBySkill;
            }
            // 掛かり＆競り合い（ハナ奪い合い）中のHP減少レート。
            else if (horse.IsTemptation && horse.IsCompeteTop)
            {
                if (horse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige)
                {
                    retRate = hpParam.HpDecRateBaseTemptationAndCompeteTopOonige;
                }
                else
                {
                    retRate = hpParam.HpDecRateBaseTemptationAndCompeteTopNige;
                }
            }
            // 競り合い（ハナ奪い合い）中のHP減少レート。
            else if (horse.IsCompeteTop)
            {
                if (horse.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige)
                {
                    retRate = hpParam.HpDecRateBaseCompeteTopOonige;
                }
                else
                {
                    retRate = hpParam.HpDecRateBaseCompeteTopNige;
                }
            }
            // 掛かり中のHP減少レート。
            else if (horse.IsTemptation)
            {
                retRate = hpParam.HpDecRateBaseTemptation;
            }
            // ポジション維持で減速中の減少レート。
            else if(horse.IsPositionKeep && horse.PositionKeepMode == Gallop.RaceDefine.PositionKeepMode.PaseDown)
            {
                retRate = hpParam.HpDecRateBasePositionKeepPaseDown;
            }

            //-----------------------------------------------------------
            // 状況別の減少レートに対して乗算するレート。
            //-----------------------------------------------------------

            // 下り坂の加速モード中はHp減少量が減少。
            if (horse.IsDownSlopeAccelMode)
            {
                retRate *= hpParam.HpDecRateMultiplyDownSlopeAccelMode;
            }

            return retRate;
        }
        #endregion
    }
}
#endif

namespace StandaloneSimulator
{
    // 再生用に切り出し（実況のHP割合判定など）
    public static partial class HorseHpCalculator
    {
        /// <summary>
        /// HP初期値計算。
        /// </summary>
        /// <param name="stamina">スタミナ。</param>
        public static float CalcInitialHp(
            Gallop.RaceParamDefine.HpParam hpParam,
            int courseDistance, 
            float stamina, 
            Gallop.RaceDefine.RunningStyle runningStyle,
            Gallop.RaceDefine.RunningStyleEx runningStyleEx)
        {
            bool isRemoveStatusLimit = false;
        #if STANDALONE_SIMULATOR || UNITY_EDITOR
            isRemoveStatusLimit =
                RaceManagerSimulate.HasInstance() &&
                RaceManagerSimulate.Instance.RaceInfo != null &&
                RaceManagerSimulate.Instance.RaceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit);
        #elif GALLOP
            isRemoveStatusLimit =
                Gallop.RaceManager.RaceInfo != null &&
                Gallop.RaceManager.RaceInfo.IsUnlock(Gallop.RaceDefine.UnlockFlag.RemoveStatusMaxLimit);
        #endif

            if (isRemoveStatusLimit)
            {
                Debug.Assert(stamina >= Gallop.RaceDefine.STATUS_MIN);
            }
            else
            {
                Debug.Assert(stamina >= Gallop.RaceDefine.STATUS_MIN && stamina <= Gallop.RaceDefine.STATUS_MAX);
            }

            // 走法ごとの補正値。
            float runningStyleCoef = 1.0f;
            if (runningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige)
            {
                runningStyleCoef = hpParam.HpMaxCoefOoNige;
            }
            else
            {
                switch (runningStyle)
                {
                    case Gallop.RaceDefine.RunningStyle.Nige: runningStyleCoef = hpParam.HpMaxCoefNige; break;
                    case Gallop.RaceDefine.RunningStyle.Senko: runningStyleCoef = hpParam.HpMaxCoefSenko; break;
                    case Gallop.RaceDefine.RunningStyle.Sashi: runningStyleCoef = hpParam.HpMaxCoefSashi; break;
                    case Gallop.RaceDefine.RunningStyle.Oikomi: runningStyleCoef = hpParam.HpMaxCoefOikomi; break;

                    default:
                        Debug.LogWarning("予期しない走法です : " + runningStyle);
                        break;
                }
            }

            float retHp = courseDistance + (stamina * hpParam.HpInitialVal1 * runningStyleCoef);

            // 初期値は0超過のはず。
            Debug.Assert(retHp > 0, "Hp初期値が0以下になっています : " + retHp);

            return retHp;
        }
    }
}

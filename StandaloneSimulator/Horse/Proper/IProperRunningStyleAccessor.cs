namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 走法適正アクセッサインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IProperRunningStyleAccessor
    {
        /// <summary>
        /// 走法適正：逃げ。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperRunningStyleNige { get; }
        /// <summary>
        /// 走法適正：先行。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperRunningStyleSenko { get; }
        /// <summary>
        /// 走法適正：差し。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperRunningStyleSashi { get; }
        /// <summary>
        /// 走法適正：追い。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperRunningStyleOikomi { get; }
    }
}

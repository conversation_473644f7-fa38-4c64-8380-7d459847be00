namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 距離適性アクセッサインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IProperDistanceAccessor
    {
        /// <summary>
        /// 距離適性：短距離。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperDistanceShort { get; }
        /// <summary>
        /// 距離適性：マイル。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperDistanceMile { get; }
        /// <summary>
        /// 距離適性：中距離。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperDistanceMiddle { get; }
        /// <summary>
        /// 距離適性：長距離。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperDistanceLong { get; }
    }
}

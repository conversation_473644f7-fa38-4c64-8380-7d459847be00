namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 馬場適正アクセッサインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IProperGroundTypeAccessor
    {
        /// <summary>
        /// 馬場適正：芝。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperGroundTurf { get; }
        /// <summary>
        /// 馬場適正：ダート。
        /// </summary>
        Gallop.RaceDefine.ProperGrade ProperGroundDirt { get; }
    }
}

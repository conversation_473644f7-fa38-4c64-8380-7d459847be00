#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using System;
using System.Diagnostics;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 競り合い（ハナ奪い合い）開始情報。
    /// </summary>
    //-------------------------------------------------------------------
    public class CompeteTopDesc
    {
        public readonly int Group;
        public readonly float AddTargetSpeed;

        public CompeteTopDesc(int group, float addTargetSpeed)
        {
            Group = group;
            AddTargetSpeed = addTargetSpeed;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// 競り合い（ハナ奪い合い）グループ番号払い出し。
    /// </summary>
    //-------------------------------------------------------------------
    public static class HorseCompeteTopRunningNumber
    {
#if STANDALONE_SIMULATOR
        [ThreadStatic]
#endif
        private static int _runningNumber = 0;

        [Conditional("STANDALONE_SIMULATOR")]
        public static void Init()
        {
            _runningNumber = 0;
        }
        
        public static int Get()
        {
            ++_runningNumber;
            return _runningNumber;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// 競り合い（ハナ奪い合い）計算機。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseCompeteTopCalculator : IHorseCompeteTopCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>出走キャラアクセッサ。</summary>
        private readonly IRaceHorseAccessor _horseAccessor;
        /// <summary>イベント記録。</summary>
        private readonly IRaceEventRecorder _eventRecorder;
        /// <summary>レース時間アクセッサ。</summary>
        private readonly IRaceTimeAccessor _timeAccessor;
        /// <summary>ParamDefine</summary>
        private readonly Gallop.RaceParamDefine.CompeteTopParam _competeParam;
        /// <summary>グループ番号とそれに属する競り合い中キャラリストの辞書</summary>
        private readonly Dictionary<int, List<IHorseRaceInfoSimulate>> _competeGroupDic = new Dictionary<int, List<IHorseRaceInfoSimulate>>();

        /// <summary>逃げ同士の競り合い発生回数</summary>
        private int _nigeCompeteCount;
        /// <summary>大逃げ同士の競り合い発生回数</summary>
        private int _oonigeCompeteCount;

        /// <summary>逃げキャラの配列</summary>
        private IHorseRaceInfoSimulate[] _nigeHorseArray;
        /// <summary>大逃げキャラの配列</summary>
        private IHorseRaceInfoSimulate[] _ooNigeHorseArray;
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public HorseCompeteTopCalculator(
            IRaceHorseAccessor horseAccessor, 
            IRaceEventRecorder eventRecorder,
            IRaceTimeAccessor timeAccessor,
            Gallop.RaceParamDefine.CompeteTopParam competeParam)
        {
            _horseAccessor = horseAccessor;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _competeParam = competeParam;
            CacheTargetRunningStyleHorse();
        }

        /// <summary>
        /// 逃げ・大逃げのキャラ配列をキャッシュ。
        /// </summary>
        private void CacheTargetRunningStyleHorse()
        {
            var nigeHorseArray = _horseAccessor.GetRunningStyleHorses(Gallop.RaceDefine.RunningStyle.Nige);
            _nigeHorseArray = nigeHorseArray.Where(x => x.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.None).ToArray();
            _ooNigeHorseArray = nigeHorseArray.Where(x => x.RunningStyleEx == Gallop.RaceDefine.RunningStyleEx.Oonige).ToArray();
        }

        /// <summary>
        /// 更新。
        /// </summary>
        public void Update(float deltaTime)
        {
            UpdateStart();
            UpdateEnd(deltaTime);
        }

        /// <summary>
        /// lhとrhが開始距離以内にいるかどうか。
        /// </summary>
        private bool CheckDistanceForStart(IHorseRaceInfoSimulate lh, IHorseRaceInfoSimulate rh)
        {
            float distanceGapAbs = Math.Abs(lh.GetDistance() - rh.GetDistance());
            float laneGapAbs = Math.Abs(lh.GetLaneDistance() - rh.GetLaneDistance());
            if (distanceGapAbs <= _competeParam.DistanceGap1 && laneGapAbs <= _competeParam.LaneGap1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// lhとrhが終了距離以内にいるかどうか。
        /// </summary>
        private bool CheckDistanceForEnd(IHorseRaceInfoSimulate lh, IHorseRaceInfoSimulate rh)
        {
            float distanceGapAbs = Math.Abs(lh.GetDistance() - rh.GetDistance());
            float laneGapAbs = Math.Abs(lh.GetLaneDistance() - rh.GetLaneDistance());
            if (distanceGapAbs <= _competeParam.DistanceGap2 && laneGapAbs <= _competeParam.LaneGap2)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        
        /// <summary>
        /// 開始チェック。
        /// </summary>
        private void UpdateStart()
        {
            var topHorse = _horseAccessor.GetFirstHorseInfo();

            // トップが逃げor大逃げでなければ競り合い始めない。※大逃げもRunningStyleはNigeなのでこれだけチェックしてればok。
            if (topHorse.RunningStyle != Gallop.RaceDefine.RunningStyle.Nige)
            {
                return;
            }
            // トップが開始距離に達していなければ競り合い始めない。
            if (topHorse.GetDistance() < _competeParam.CheckStartDistance)
            {
                return;
            }
            
            // 大逃げ同士の競り合い。
            UpdateStartOonige();

            // 逃げ同士の競り合い。
            UpdateStartNige();
        }

        /// <summary>
        /// 開始チェック：逃げ同士。
        /// </summary>
        private void UpdateStartNige()
        {
            // 既に逃げ同士の競り合い回数上限に達しているなら競り合い開始しない。
            if (_nigeCompeteCount >= _competeParam.NigeCount)
            {
                return;
            }

            // 逃げが複数人いなければ競り合い開始しない。
            if (_nigeHorseArray.Length <= 1)
            {
                return;
            }
            
            var nigeTopHorse = RaceUtil.GetTopOrderHorses(_nigeHorseArray);

            // 逃げトップが既に競り合い中なら競り合い開始しない。
            if (nigeTopHorse.IsCompeteTop)
            {
                return;
            }
            
            // 逃げトップより前のキャラが大逃げのみである。
            for (int checkOrder = nigeTopHorse.CurOrder-1; checkOrder >= 1; --checkOrder)
            {
                var horse = _horseAccessor.GetHorseInfoByOrder(checkOrder);
                if (horse.RunningStyle != Gallop.RaceDefine.RunningStyle.Nige)
                {
                    // 逃げ以外のキャラが前にいる。
                    return;
                }
            }

            bool isCompete = false;
            foreach (var horse in _nigeHorseArray)
            {
                if (nigeTopHorse == horse)
                {
                    continue;
                }

                // 既に競り合い中ならチェック不要。
                if (horse.IsCompeteTop)
                {
                    continue;
                }

                // 開始可能区間を過ぎていたら競り合わない。
                int section = horse.CalcSection();
                if (section > _competeParam.CheckEndSection)
                {
                    continue;
                }

                // 距離が近くなければ競り合わない。
                if (!CheckDistanceForStart(nigeTopHorse, horse))
                {
                    continue;
                }

                //-------------------------------------------------------
                // ここまで来たら競り合い開始は決定。
                //-------------------------------------------------------
                isCompete = true;
                
                // トップが競り合っていないなら、グループを払い出して一緒に参加。
                if (!nigeTopHorse.IsCompeteTop)
                {
                    int group = HorseCompeteTopRunningNumber.Get();
                    _competeGroupDic.Add(group, new List<IHorseRaceInfoSimulate>());
                    _competeGroupDic[group].Add(nigeTopHorse);
                    _competeGroupDic[group].Add(horse);
                
                    {
                        var desc = CreateCompeteDesc(nigeTopHorse, group);
                        StartCompete(nigeTopHorse, desc);
                    }
                    {
                        var desc = CreateCompeteDesc(horse, group);
                        StartCompete(horse, desc);
                    }
                }
                // トップが既に競り合っているなら、そのグループに参加。
                else
                {
                    int group = nigeTopHorse.CompeteTopGroup;
                    _competeGroupDic[group].Add(horse);

                    var desc = CreateCompeteDesc(horse, group);
                    StartCompete(horse, desc);
                }
            }

            // 競り合いが行われたら回数加算。
            if (isCompete)
            {
                ++_nigeCompeteCount;
            }
        }

        /// <summary>
        /// 開始チェック：大逃げ同士。
        /// </summary>
        private void UpdateStartOonige()
        {
            // 既に大逃げ同士の競り合い回数上限に達しているなら競り合い開始しない。
            if (_oonigeCompeteCount >= _competeParam.OonigeCount)
            {
                return;
            }
            
            // 大逃げが複数人いなければ競り合い開始しない。
            if (_ooNigeHorseArray.Length <= 1)
            {
                return;
            }
            
            var topHorse = _horseAccessor.GetFirstHorseInfo();
            
            // トップが大逃げじゃなかったら大逃げの競り合いは発生しない。
            if (topHorse.RunningStyleEx != Gallop.RaceDefine.RunningStyleEx.Oonige)
            {
                return;
            }
            
            // 大逃げトップが既に競り合い中なら競り合い開始しない。
            if (topHorse.IsCompeteTop)
            {
                return;
            }

            bool isCompete = false;
            foreach (var horse in _ooNigeHorseArray)
            {
                if (topHorse == horse)
                {
                    continue;
                }

                // 既に競り合い中ならチェック不要。
                if (horse.IsCompeteTop)
                {
                    continue;
                }
                
                // 開始可能区間を過ぎていたら競り合わない。
                int section = horse.CalcSection();
                if (section > _competeParam.CheckEndSection)
                {
                    continue;
                }

                // 距離が近くなければ競り合わない。
                if (!CheckDistanceForStart(topHorse, horse))
                {
                    continue;
                }

                //-------------------------------------------------------
                // ここまで来たら競り合い開始は決定。
                //-------------------------------------------------------
                isCompete = true;
                
                // トップが競り合っていないなら、グループを払い出して一緒に参加。
                if (!topHorse.IsCompeteTop)
                {
                    int group = HorseCompeteTopRunningNumber.Get();
                    _competeGroupDic.Add(group, new List<IHorseRaceInfoSimulate>());
                    _competeGroupDic[group].Add(topHorse);
                    _competeGroupDic[group].Add(horse);
                
                    {
                        var desc = CreateCompeteDesc(topHorse, group);
                        StartCompete(topHorse, desc);
                    }
                    {
                        var desc = CreateCompeteDesc(horse, group);
                        StartCompete(horse, desc);
                    }
                }
                // トップが既に競り合っているなら、そのグループに参加。
                else
                {
                    int group = topHorse.CompeteTopGroup;
                    _competeGroupDic[group].Add(horse);

                    var desc = CreateCompeteDesc(horse, group);
                    StartCompete(horse, desc);
                }
            }

            // 競り合いが行われたら回数加算。
            if (isCompete)
            {
                ++_oonigeCompeteCount;
            }
        }

        /// <summary>
        /// 競り合い開始情報生成。
        /// </summary>
        private CompeteTopDesc CreateCompeteDesc(IHorseRaceInfoSimulate horse, int group)
        {
            var retDesc = new CompeteTopDesc(group, CalcAddTargetSpeed(horse.Guts));
            return retDesc;
        }

        /// <summary>
        /// 目指す速度への加算値の計算。
        /// </summary>
        private float CalcAddTargetSpeed(float guts)
        {
            float add = (float)Math.Pow(guts * _competeParam.AddParam1Coef1, _competeParam.AddParam1Coef2) * _competeParam.AddParam1Coef3;
            return add;
        }

        /// <summary>
        /// 競り合い開始。
        /// </summary>
        private void StartCompete(IHorseRaceInfoSimulate horse, CompeteTopDesc desc)
        {
            Debug.Assert(!horse.IsCompeteTop);
            Debug.Assert(horse.CompeteTopGroup == Gallop.RaceDefine.COMPETE_GROUP_NULL);
            horse.IsCompeteTop = true;
            horse.CompeteTopRemainTime = CalcRemainTime(horse);
            horse.CompeteTopCount++;
            horse.CompeteTopGroup = desc.Group;
            horse.CompeteTopAddTargetSpeed = desc.AddTargetSpeed;
            horse.CompeteTopAddTargetSpeed = desc.AddTargetSpeed;
            // 足溜め消費
            horse.DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode.CompeteTop);
        #if CYG_DEBUG
            if (horse.DbgCompeteTopStartGroupHorseList == null)
            {
                horse.DbgCompeteTopStartGroupHorseList = RaceUtil.DbgCreateCompeteGroupHorseList(horse, horse.CompeteTopGroup, _competeGroupDic);
            }
        #endif
            
            _eventRecorder.AddCompeteTopEvent(horse.HorseIndex, _timeAccessor.AccumulateTimeSinceStart);
        }

        /// <summary>
        /// 競り合い継続時間計算。
        /// </summary>
        private float CalcRemainTime(IHorseRaceInfoSimulate horse)
        {
            float properRunningStyle = horse.ProperRunningStyleCoef;
            float remainTime = (float)Math.Pow(horse.Guts * _competeParam.TimeCoef1, _competeParam.TimeCoef2) * _competeParam.TimeCoef3 * properRunningStyle;
            return remainTime;
        }

        /// <summary>
        /// 競り合い終了。
        /// </summary>
        private void EndCompete(IHorseRaceInfoSimulate horse)
        {
        #if CYG_DEBUG
            if (horse.DbgCompeteTopEndGroupHorseList == null)
            {
                horse.DbgCompeteTopEndGroupHorseList = RaceUtil.DbgCreateCompeteGroupHorseList(horse, horse.CompeteTopGroup, _competeGroupDic);
            }
        #endif
            horse.IsCompeteTop = false;
            horse.CompeteTopGroup = Gallop.RaceDefine.COMPETE_GROUP_NULL;
            horse.CompeteTopAddTargetSpeed = 0;
            horse.CompeteTopRemainTime = 0;
            
            // ※競り合い終了時は_competeGroupDicからhorseを取り除かない。
            // ※まだ競り合い状態中のキャラが_competeGroupDicで同じグループに所属しているキャラと近くにいるかを判定し続けるため。
        }

        /// <summary>
        /// 競り合い中の全キャラの競り合いを終了。
        /// </summary>
        private void ForceEndCompeteAll()
        {
            var allHorseArray = _horseAccessor.GetHorseRaceInfos();
            foreach (var horse in allHorseArray)
            {
                // 競り合っていないなら不要。
                if (!horse.IsCompeteTop)
                {
                    continue;
                }
                EndCompete(horse);
            }
            
            // 全てのキャラの競り合いを終了するので_competeGroupDicの中身も不要。
            _competeGroupDic.Clear();
        }

        /// <summary>
        /// 終了更新。
        /// </summary>
        private void UpdateEnd(float deltaTime)
        {
            var allHorseArray = _horseAccessor.GetHorseRaceInfos();
            foreach (var horse in allHorseArray)
            {
                // 競り合っていないなら不要。
                if (!horse.IsCompeteTop)
                {
                    continue;
                }

                // 競り合い中の誰かが終了区間に達したら全員強制終了。
                if (horse.CalcSection() >= _competeParam.EndSection)
                {
                    ForceEndCompeteAll();
                    return;
                }

                // 継続時間切れたら競り合い終了。
                horse.CompeteTopRemainTime -= deltaTime;
                if (horse.CompeteTopRemainTime < 0)
                {
                    EndCompete(horse);
                    continue;
                }

                // 同グループ内のキャラが近くにいないなら競り合い終了。
                if (!HasCompeteNearHorse(horse))
                {
                    EndCompete(horse);
                    continue;
                }
            }
        }

        /// <summary>
        /// 競り合い中の同グループ内に一定距離近付いているキャラがいるかどうか。
        /// </summary>
        private bool HasCompeteNearHorse(IHorseRaceInfoSimulate horse)
        {
            if (_competeGroupDic.TryGetValue(horse.CompeteTopGroup, out var groupList))
            {
                foreach (var groupHorse in groupList)
                {
                    if (groupHorse == horse)
                    {
                        continue;
                    }

                    // 一定距離内にいればtrueを返却。
                    if (CheckDistanceForEnd(horse, groupHorse))
                    {
                        return true;
                    }
                }

                // 競り合いグループ内に一定距離内にいるキャラがいない。
                return false;
            }
            return false;
        }
    }
}

#endif

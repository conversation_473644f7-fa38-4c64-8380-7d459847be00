#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace StandaloneSimulator
{
#if STANDALONE_SIMULATOR || UNITY_EDITOR
    public class CompeteFightDesc
    {
        public readonly int Group;
        public readonly float AddTargetSpeed;
        public readonly float AddAccel;

        public CompeteFightDesc(int group, float addTargetSpeed, float addAccel)
        {
            Group = group;
            AddTargetSpeed = addTargetSpeed;
            AddAccel = addAccel;
        }
    }
    
    public static class HorseCompeteFightRunningNumber
    {
#if STANDALONE_SIMULATOR
        [ThreadStatic]
#endif
        private static int _runningNumber = 0;

        [Conditional("STANDALONE_SIMULATOR")]
        public static void Init()
        {
            _runningNumber = 0;
        }

        public static int Get()
        {
            ++_runningNumber;
            return _runningNumber;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// 競り合い（叩き合い）計算機。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseCompeteFightCalculator : IHorseCompeteFightCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>キャラアクセッサ。</summary>
        private readonly IRaceHorseAccessor _horseAccessor;
        /// <summary>イベント記録。</summary>
        private readonly IRaceEventRecorder _eventRecorder;
        /// <summary>レース時間アクセッサ。</summary>
        private readonly IRaceTimeAccessor _timeAccessor;
        /// <summary>ParamDefine</summary>
        private readonly Gallop.RaceParamDefine.CompeteFightParam _competeParam;
        /// <summary>この順位以内のキャラが競り合いできる</summary>
        private readonly int _orderThreshold;
        /// <summary>グループ番号とそれに属する競り合い中キャラリストの辞書</summary>
        private readonly Dictionary<int, List<IHorseRaceInfoSimulate>> _competeGroupDic = new Dictionary<int, List<IHorseRaceInfoSimulate>>();
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseCompeteFightCalculator(
            IRaceHorseAccessor horseAccessor, 
            IRaceEventRecorder eventRecorder,
            IRaceTimeAccessor timeAccessor,
            Gallop.RaceParamDefine.CompeteFightParam competeParam)
        {
            _horseAccessor = horseAccessor;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _competeParam = competeParam;
            int numHorse = horseAccessor.GetHorseNumber();
            
            _orderThreshold = RaceUtil.GetIntThresholdByPer(numHorse, _competeParam.TargetOrderPer);
            Debug.Assert(_orderThreshold > 0 && _orderThreshold <= numHorse);
        }

        public void Update(float deltaTime)
        {
            UpdateStart();
            UpdateEnd();
        }

        /// <summary>
        /// 開始チェック。
        /// </summary>
        private void UpdateStart()
        {
            var allHorseArray = _horseAccessor.GetHorseRaceInfos();
            foreach (var horse in allHorseArray)
            {
                // 既に競り合い中なら不要。
                if (horse.IsCompeteFight)
                {
                    continue;
                }

                // 自分が競り合いに参加したことがあるなら、２回目以降は開始しない。
                if (horse.CompeteFightCount > 0)
                {
                    continue;
                }
                
                // Hpが一定値を下回っていたら開始できない。
                if (!IsCompeteStartEnableHpPer(horse.GetHpPer()))
                {
                    continue;
                }

                foreach (var candidateHorse in horse.CompeteFightNearList)
                {
                    if (IsCompeteStartEnable(horse, candidateHorse))
                    {
                        // 相手が既に競り合っていたらそのグループに入る。
                        if (candidateHorse.infoSimulate.IsCompeteFight)
                        {
                            int group = candidateHorse.infoSimulate.CompeteFightGroup;
                            if (_competeGroupDic.TryGetValue(group, out var groupList))
                            {
                                if (!groupList.Contains(horse))
                                {
                                    groupList.Add(horse);
                                }

                                var desc = CreateCompeteDesc(horse, group);
                                StartCompete(horse, desc);
                            }
                        }
                        // 相手がまだ競り合っていないならグループを払い出して、一緒に参加。
                        else if(candidateHorse.infoSimulate.CompeteFightCount == 0)
                        {
                            int group = HorseCompeteFightRunningNumber.Get();
                            _competeGroupDic.Add(group, new List<IHorseRaceInfoSimulate>());
                            _competeGroupDic[group].Add(horse);
                            _competeGroupDic[group].Add(candidateHorse.infoSimulate);

                            {
                                var desc = CreateCompeteDesc(horse, group);
                                StartCompete(horse, desc);
                            }
                            
                            {
                                var desc = CreateCompeteDesc(candidateHorse.infoSimulate, group);
                                StartCompete(candidateHorse.infoSimulate, desc);
                            }
                        }
                        
                        break;
                    }
                }
            }
        }

        private CompeteFightDesc CreateCompeteDesc(IHorseRaceInfoSimulate horse, int group)
        {
            var retDesc = new CompeteFightDesc(group, CalcAddTargetSpeed(horse.Guts), CalcAddAccel(horse.Guts));
            return retDesc;
        }

        /// <summary>
        /// 目指す速度への加算値の計算。
        /// </summary>
        private float CalcAddTargetSpeed(float guts)
        {
            float add = (float)Math.Pow(guts * _competeParam.AddParam1Coef1, _competeParam.AddParam1Coef2) * _competeParam.AddParam1Coef3;
            return add;
        }

        /// <summary>
        /// 加速度への加算値の計算。
        /// </summary>
        private float CalcAddAccel(float guts)
        {
            float add = (float)Math.Pow(guts * _competeParam.AddParam2Coef1, _competeParam.AddParam2Coef2) * _competeParam.AddParam2Coef3;
            return add;
        }

        /// <summary>
        /// horseから見てcandidateと競り合いができるかどうか。
        /// </summary>
        private bool IsCompeteStartEnable(IHorseRaceInfoSimulate horse, Gallop.AroundHorse candidate)
        {
            bool isStraightLastSelf = horse.IsStraight && horse.IsStraightLast;
            bool isOrderSelf = horse.CurOrder + 1 <= _orderThreshold;
            
            // この候補と一定時間近くにいて、
            if (candidate.CompeteFightCandidateContinueTime >= _competeParam.TargetContinueTime)
            {
                // 速度差が一定値以内で、
                if (candidate.speedGapAbs <= _competeParam.SpeedGap)
                {
                    // 自分or相手が最終直線を走っていて、
                    bool isStarightLastTarget = candidate.infoSimulate.IsStraight && candidate.infoSimulate.IsStraightLast;
                    if (isStraightLastSelf || isStarightLastTarget)
                    {
                        // 自分or相手の順位が一定範囲内である
                        bool isOrderTarget = candidate.infoSimulate.CurOrder+1 <= _orderThreshold;
                        if (isOrderSelf || isOrderTarget)
                        {
                            // 相手のHp比率が足りている
                            if (IsCompeteStartEnableHpPer(candidate.infoSimulate.GetHpPer()))
                            {
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 競り合い"継続"可能なHpが残っているか。
        /// </summary>
        /// <param name="hpPer">残りHp比率。0 ~ 1</param>
        private bool IsCompeteContinueEnableHpPer(float hpPer)
        {
            return hpPer * 100 > _competeParam.HpPer;
        }

        /// <summary>
        /// 競り合い"開始"可能なHpが残っているか。
        /// </summary>
        /// <param name="hpPer">残りHp比率。0 ~ 1</param>
        private bool IsCompeteStartEnableHpPer(float hpPer)
        {
            return hpPer * 100 > _competeParam.HpPer2;
        }

        /// <summary>
        /// 競り合い開始。
        /// </summary>
        private void StartCompete(IHorseRaceInfoSimulate horse, CompeteFightDesc desc)
        {
            Debug.Assert(!horse.IsCompeteFight);
            Debug.Assert(horse.CompeteFightCount == 0);
            horse.IsCompeteFight = true;
            horse.CompeteFightCount++;
            horse.CompeteFightGroup = desc.Group;
            horse.CompeteFightAddTargetSpeed = desc.AddTargetSpeed;
            horse.CompeteFightAddAccel = desc.AddAccel;
        #if CYG_DEBUG
            if (horse.DbgCompeteFightStartGroupHorseList == null)
            {
                horse.DbgCompeteFightStartGroupHorseList = RaceUtil.DbgCreateCompeteGroupHorseList(horse, horse.CompeteFightGroup, _competeGroupDic);
            }
        #endif
            
            _eventRecorder.AddCompeteFightEvent(horse.HorseIndex, _timeAccessor.AccumulateTimeSinceStart);
        }
        
        /// <summary>
        /// 競り合い終了。
        /// </summary>
        private void EndCompete(IHorseRaceInfoSimulate horse)
        {
        #if CYG_DEBUG
            if (horse.DbgCompeteFightEndGroupHorseList == null)
            {
                horse.DbgCompeteFightEndGroupHorseList = RaceUtil.DbgCreateCompeteGroupHorseList(horse, horse.CompeteFightGroup, _competeGroupDic);
            }
        #endif
            horse.IsCompeteFight = false;
            horse.CompeteFightGroup = Gallop.RaceDefine.COMPETE_GROUP_NULL;
            horse.CompeteFightAddTargetSpeed = 0;
            horse.CompeteFightAddAccel = 0;
            
            // ※競り合い終了時は_competeGroupDicからhorseを取り除かない。
            // ※まだ競り合い状態中のキャラが_competeGroupDicで同じグループに所属しているキャラと近くにいるかを判定し続けるため。
        }

        /// <summary>
        /// 終了更新。
        /// </summary>
        private void UpdateEnd()
        {
            var allHorseArray = _horseAccessor.GetHorseRaceInfos();
            foreach (var horse in allHorseArray)
            {
                // 競り合っていないなら不要。
                if (!horse.IsCompeteFight)
                {
                    continue;
                }

                // Hpが一定値を下回ったら競り合いから終了。
                if (!IsCompeteContinueEnableHpPer(horse.GetHpPer()))
                {
                    EndCompete(horse);
                    continue;
                }

                // 同グループ内のキャラが近くにいないなら競り合い終了。
                if (!HasCompeteNearHorse(horse))
                {
                    EndCompete(horse);
                    continue;
                }
            }
        }

        /// <summary>
        /// 競り合い中の同グループ内に一定距離近付いているキャラがいるかどうか。
        /// </summary>
        private bool HasCompeteNearHorse(IHorseRaceInfoSimulate horse)
        {
            if (_competeGroupDic.TryGetValue(horse.CompeteFightGroup, out var groupList))
            {
                foreach (var groupHorse in groupList)
                {
                    if (groupHorse == horse)
                    {
                        continue;
                    }

                    // 一定距離内にいればtrueを返却。
                    float distanceGapAbs = Math.Abs(groupHorse.GetDistance() - horse.GetDistance());
                    if (distanceGapAbs <= _competeParam.TargetContinueDistance)
                    {
                        return true;
                    }
                }

                // 競り合いグループ内に一定距離内にいるキャラがいない。
                return false;
            }
            return false;
        }
    }
#endif
}

#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 掛かり機能計算処理Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseTemptationCalculatorNull : IHorseTemptationCalculator
    {
        public void LotTemptationStartEnable() { }
        public TemptationMode Mode { get { return TemptationMode.Null; } }
        public bool IsTemptation { get { return false; } }
        public int TemptationCount { get { return 0; } }
        public bool IsTemptationStartEnable { get { return false; } }
        public void Update( float deltaTime ) { }
        public void SetForceEndTimeBySkill(float time) { }
    }
}

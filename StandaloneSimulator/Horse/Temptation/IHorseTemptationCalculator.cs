namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 掛かり機能計算処理インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IHorseTemptationCalculator
    {
        /// <summary>
        /// 掛かりの発動抽選。レース開始時に１度だけ実行される想定。
        /// </summary>
        void LotTemptationStartEnable();
        /// <summary>
        /// 掛かり中か取得。
        /// </summary>
        bool IsTemptation { get; }
        /// <summary>
        /// 掛かりのモード取得。
        /// </summary>
        TemptationMode Mode { get; }
        /// <summary>
        /// 掛かった回数。
        /// </summary>
        int TemptationCount { get; }
        /// <summary>
        /// 掛かり発動するかどうか。
        /// </summary>
        /// <remarks>
        /// レース開始時に抽選された結果。trueならこのレース中のどこかで掛かりが発動する。
        /// </remarks>
        bool IsTemptationStartEnable { get; }
        /// <summary>
        /// 更新。
        /// </summary>
        void Update( float deltaTime );
        /// <summary>
        /// スキル効果による強制終了時間の設定。
        /// </summary>
        /// <remarks>
        /// この関数で時間を設定された場合、他の興奮終了条件は無視され、この時間のみで解除が行われる。
        /// </remarks>
        /// <param name="time">秒数</param>
        void SetForceEndTimeBySkill(float time);
    }
}

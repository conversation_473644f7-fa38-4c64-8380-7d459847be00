#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 掛かり機能計算処理。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class HorseTemptationCalculator : IHorseTemptationCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly IHorseRaceInfoSimulate _ownerHorse;
        private readonly Gallop.RaceParamDefine.TemptationParam _temptationParam;
        private readonly IRaceRandomGenerator _randomGenerator;
        private readonly System.Action _onTemptationStart;

        private int _temptationStartSection = -1;

        private float _temptationEndCheckSec;
        private float _temptationForceEndSec;

        private bool _isForceEndTimeBySkillEnable;
        private float _forceEndTimeBySkill;

        public bool IsTemptation { get; private set; }
        public TemptationMode Mode { get; private set; }
        public int TemptationCount { get; private set; }
        public bool IsTemptationStartEnable { get; private set; }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public HorseTemptationCalculator( 
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.TemptationParam temptationParam, 
            IRaceRandomGenerator randomGenerator,
            System.Action onTemptationStart)
        {
            _ownerHorse = ownerHorse;
            _temptationParam = temptationParam;
            _randomGenerator = randomGenerator;
            Mode = TemptationMode.Null;
            _onTemptationStart = onTemptationStart;
        }

        public void LotTemptationStartEnable()
        {
            float per = CalcTemptationStartEnablePer();
            float random = _randomGenerator.GetRandom(100);
            if (random < per)
            {
                // 発動。
                IsTemptationStartEnable = true;
                // 発動開始区間の抽選。
                _temptationStartSection = CalcTemptationStartSection();
            }
            
        #if CYG_DEBUG
            _ownerHorse.DbgTemptationPerRandom = random;
            _ownerHorse.DbgTemptationPerWiz = per;
            _ownerHorse.DbgTemptationStartSection = _temptationStartSection;
        #endif
        }

        private float CalcTemptationStartEnablePer()
        {
            // wizそのまま使った場合、10だった場合Log10の計算結果が0になる。
            // すると後続の計算で０割によってInfinityが発生してしまうため、+1して回避する。
            // ※プランナーにも説明し、仕様的にこの計算にした。
            const float WIZ_ADD = 1;
            float log = (float)Math.Log10(Math.Pow(_ownerHorse.Wiz * 0.1f + WIZ_ADD, 2));

            float per = (float)Math.Pow( (_temptationParam.TemptationStartPerVal1 / log), 2);
            
            // スキルによる確率上下。
            per = _ownerHorse.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.TemptationPer, per);
            
            return per;
        }

        private int CalcTemptationStartSection()
        {
            return _randomGenerator.GetRandom(
                _temptationParam.TemptationLotSectionMin,
                _temptationParam.TemptationLotSectionMax+1); // Maxも結果に含むため+1。
        }

        private TemptationMode CalcTemptationMode(Gallop.RaceDefine.RunningStyle style)
        {
            switch (style)
            {
                case Gallop.RaceDefine.RunningStyle.Nige: return CalcTemptationModeNige();
                case Gallop.RaceDefine.RunningStyle.Senko: return CalcTemptationModeSenko();
                case Gallop.RaceDefine.RunningStyle.Sashi: return CalcTemptationModeSashi();
                case Gallop.RaceDefine.RunningStyle.Oikomi: return CalcTemptationModeOikomi();
                default:
                    Debug.LogWarning("不正な走法です。style=" + style);
                    return TemptationMode.Null;
            }
        }

        private TemptationMode CalcTemptationModeNige()
        {
            return TemptationMode.Boost;
        }

        private TemptationMode CalcTemptationModeSenko()
        {
            // 先行→逃げ。
            if (_randomGenerator.GetRandom(100) < _temptationParam.SenkoToNigePer)
            {
                return TemptationMode.PositionNige;
            }

            return TemptationMode.Null;
        }

        private TemptationMode CalcTemptationModeSashi()
        {
            float perFix = _randomGenerator.GetRandom(100);
            
            // 差し→先行。
            float per = _temptationParam.SashiToSenkoPer;
            if(perFix < per)
            {
                return TemptationMode.PositionSenko;
            }

            // 差し→逃げ。
            per += _temptationParam.SashiToNigePer;
            if (perFix < per)
            {
                return TemptationMode.PositionNige;
            }

            return TemptationMode.Null;
        }

        private TemptationMode CalcTemptationModeOikomi()
        {
            float perFix = _randomGenerator.GetRandom(100);

            // 追い→差し。
            float per = _temptationParam.OikomiToSashiPer;
            if (perFix < per)
            {
                return TemptationMode.PositionSashi;
            }

            // 追い→先行。
            per += _temptationParam.OikomiToSenkoPer;
            if (perFix < per)
            {
                return TemptationMode.PositionSenko;
            }

            // 追い→逃げ。
            per += _temptationParam.OikomiToNigePer;
            if (perFix < per)
            {
                return TemptationMode.PositionNige;
            }

            return TemptationMode.Null;
        }

        /// <summary>
        /// 更新。
        /// </summary>
        public void Update( float deltaTime )
        {
            if( !IsTemptation )
            {
                CheckTemptationStart();
            }
            else
            {
                CheckTemptationEnd( deltaTime );
            }
        }

        /// <summary>
        /// 掛かり発動更新。
        /// </summary>
        private void CheckTemptationStart()
        {
            //-----------------------------------------------------------
            // 発動可能でなければ処理不要。
            //-----------------------------------------------------------
            if(!IsTemptationStartEnable)
            {
                return;
            }

            //-----------------------------------------------------------
            // 発動するモードが指定されていなければ処理不要。
            //-----------------------------------------------------------
            //if(Mode == TemptationMode.Null)
            //{
            //    DebugUtils.LogWarning("掛かり発動モードに無効値が設定されているため、発動できません");
            //    return;
            //}

            //-----------------------------------------------------------
            // 発動する区間に達していなければ処理不要。
            //-----------------------------------------------------------
            int section = _ownerHorse.CalcSection();
            if(section < _temptationStartSection)
            {
                return;
            }

            //-----------------------------------------------------------
            // 発動。
            //-----------------------------------------------------------
            StartTemptation();
        }

        /// <summary>
        /// 掛かり解除更新。
        /// </summary>
        private void CheckTemptationEnd( float deltaTime )
        {
            if( !IsTemptation )
            {
                return;
            }

            //-----------------------------------------------------------
            // スキル効果で終了時間が指定されている場合は、そのカウントダウンでのみ終了できる。
            //-----------------------------------------------------------
            if (_isForceEndTimeBySkillEnable)
            {
                if (RaceUtil.UpdateTimer(ref _forceEndTimeBySkill, deltaTime))
                {
                    EndTemptation();
                    return;
                }
            }
            //-----------------------------------------------------------
            // こっちは通常の終了チェック。
            //-----------------------------------------------------------
            else
            {
                //-------------------------------------------------------
                // 一定時間経過で強制解除。
                //-------------------------------------------------------
                if (RaceUtil.UpdateTimer(ref _temptationForceEndSec, deltaTime))
                {
                    EndTemptation();
                    return;
                }


                //-------------------------------------------------------
                // 一定時間おきに解除チェックする。
                //-------------------------------------------------------
                if (!RaceUtil.UpdateTimer(ref _temptationEndCheckSec, deltaTime))
                {
                    return;
                }
                _temptationEndCheckSec = _temptationParam.TemptationEndCheckTime;

                // 掛かり解除確率を計算、抽選して掛かり解除させる。
                float per = CalcTemptationEndPer();
                if (_randomGenerator.GetRandom(100) < per)
                {
                    EndTemptation();
                }
            }
        }

        //---------------------------------------------------------------
        private void StartTemptation()
        {
            IsTemptation = true;
            // 発動するモードの抽選。                
            Mode = CalcTemptationMode(_ownerHorse.RunningStyle);

            // 終了チェックの間隔。
            _temptationEndCheckSec = _temptationParam.TemptationEndCheckTime;
            _temptationForceEndSec = _temptationParam.TemptationForceEndTime;

            _isForceEndTimeBySkillEnable = false;
            _forceEndTimeBySkill = 0;

            // レース中に一度しか発動させないため、フラグおろす。
            IsTemptationStartEnable = false;
            // 発動回数加算。
            ++TemptationCount;
            // 足溜め消費
            _ownerHorse.DecreaseConservePower(Gallop.RaceDefine.DecreaseConservePowerMode.Temptation);

            _onTemptationStart?.Invoke();
        }

        //---------------------------------------------------------------
        private void EndTemptation()
        {
            IsTemptation = false;
            Mode = TemptationMode.Null;
        }

        /// <summary>
        /// 掛かり解除確率計算。
        /// </summary>
        /// <returns>確率を百分率で返却。</returns>
        private float CalcTemptationEndPer()
        {
            return _temptationParam.TemptationEndPer;
        }

        public void SetForceEndTimeBySkill(float time)
        {
            _forceEndTimeBySkill = time;
            _isForceEndTimeBySkillEnable = true;
        }
    }
}
#endif

// Frameデータが参照を持つため、切り出している
namespace StandaloneSimulator
{
    //---------------------------------------------------------------
    // 定義。
    //---------------------------------------------------------------
    public enum TemptationMode
    {
        Null,
        PositionSashi,  // 差しの位置取りに変わる。
        PositionSenko,  // 先行の位置取りに変わる。
        PositionNige,   // 逃げの位置取りに変わる。
        Boost,          // 目指す速度上昇。
    }
}

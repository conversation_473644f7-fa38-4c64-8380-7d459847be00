#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    //---------------------------------------------------------------
    /// <summary>
    /// 加速度計算処理。
    /// </summary>
    //---------------------------------------------------------------
    public class HorseAccelCalculator
    {
        //-----------------------------------------------------------
        // 定義。
        //-----------------------------------------------------------
        public const float ACCEL_PERSEC_OVERRUN = 1.0f;


        //-----------------------------------------------------------
        // 変数。
        //-----------------------------------------------------------
        private readonly IHorseRaceInfoSimulate _owner;
        private readonly Gallop.RaceParamDefine _paramDefine;
        private Gallop.RaceParamDefine.PhaseAccelCoef _phaseAccelCoef;

        //-----------------------------------------------------------
        // 関数。
        //-----------------------------------------------------------
        public HorseAccelCalculator(
            IHorseRaceInfoSimulate owner,
            Gallop.RaceParamDefine paramDefine)
        {
            _owner = owner;
            _paramDefine = paramDefine;
            InitPhaseAccelRate();
        }

        /// <summary>
        /// １秒当たりの加速度計算。
        /// </summary>
        public float CalcAccelPerSec()
        {
            //-----------------------------------------------------------
            // オーバーラン中は加速度固定。
            //-----------------------------------------------------------
            if (_owner.IsFinished())
            {
                return ACCEL_PERSEC_OVERRUN;
            }

            //-----------------------------------------------------------
            // 基準となる加速度。
            //-----------------------------------------------------------
            float accelPowCoef = _owner.SlopeType == Gallop.RaceDefine.SlopeType.Up 
                ? _paramDefine.accelPowCoefUpSlope 
                : _paramDefine.accelPowCoef;
            float accelPhaseCoef = GetAccelPhaseCoef();
            float retAccel = 
                (float)Math.Sqrt(_owner.Pow * _paramDefine.AccelPowCoefSqrt) 
                * accelPowCoef 
                * _owner.ProperGroundTypeCoef 
                * _owner.ProperDistanceCoefPow 
                * accelPhaseCoef;

            //-----------------------------------------------------------
            // スキルによる加算。
            //-----------------------------------------------------------
            retAccel = _owner.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.Accel, retAccel);

            //-----------------------------------------------------------
            // スタートダッシュ期間中の加算。
            //-----------------------------------------------------------
            if (_owner.IsStartDash)
            {
                retAccel += _paramDefine.StartAccelAdd;
            }

            //-----------------------------------------------------------
            // 競り合い。
            //-----------------------------------------------------------
            if (_owner.IsCompeteFight)
            {
                retAccel += _owner.CompeteFightAddAccel;
            }
            
            //-----------------------------------------------------------
            // 足溜めポイントによる加算。
            //-----------------------------------------------------------
            retAccel += _owner.ConservePowerAddAccel;
            
            // 加速度が0未満になると減速してしまうので、0で止める。
            if (retAccel < 0)
            {
                retAccel = 0;
            }

            return retAccel;
        }

        /// <summary>
        /// Phaseごとの加速度係数初期化。
        /// </summary>
        private void InitPhaseAccelRate()
        {
            if (_owner.RunningStyleEx != Gallop.RaceDefine.RunningStyleEx.None)
            {
                int runningStyleIndex = (int)_owner.RunningStyleEx-1;
                if (runningStyleIndex < 0 || runningStyleIndex >= _paramDefine.Speed.PhaseAccelCoefExArray.Length)
                {
                    Debug.LogWarning($"特殊走法{_owner.RunningStyleEx}が_paramDefine.Speed.PhaseAccelCoefExArrayに登録されていない");
                    _phaseAccelCoef = new Gallop.RaceParamDefine.PhaseAccelCoef();
                    return;
                }
                _phaseAccelCoef = _paramDefine.Speed.PhaseAccelCoefExArray[runningStyleIndex];
            }
            else
            {
                int runningStyleIndex = (int)_owner.RunningStyle-1;
                if (runningStyleIndex < 0 || runningStyleIndex >= _paramDefine.Speed.PhaseAccelCoefArray.Length)
                {
                    Debug.LogWarning($"走法{_owner.RunningStyle}が_paramDefine.Speed.PhaseAccelRateArrayに登録されていない");
                    _phaseAccelCoef = new Gallop.RaceParamDefine.PhaseAccelCoef();
                    return;
                }
                _phaseAccelCoef = _paramDefine.Speed.PhaseAccelCoefArray[runningStyleIndex];
            }
        }
        
        /// <summary>
        /// 現在のPhaseに応じた係数取得。
        /// </summary>
        private float GetAccelPhaseCoef()
        {
            switch (_owner.GetPhase())
            {
            case Gallop.RaceDefine.HorsePhase.Start: return _phaseAccelCoef.Start;
            case Gallop.RaceDefine.HorsePhase.MiddleRun: return _phaseAccelCoef.Middle;
            case Gallop.RaceDefine.HorsePhase.End: return _phaseAccelCoef.End;
            case Gallop.RaceDefine.HorsePhase.LastSpurt: return _phaseAccelCoef.Last;
            default: return 1;
            }
        }
    }
}
#endif

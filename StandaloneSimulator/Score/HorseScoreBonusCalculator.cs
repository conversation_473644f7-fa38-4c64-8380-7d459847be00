#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 競技場モードのスコアボーナス計算機。
    /// </summary>
    //-------------------------------------------------------------------
    public static class HorseScoreBonusCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>
        /// 条件判定の種類。
        /// </summary>
        /// <remarks>
        /// team_stadium_score_bonus.csvのcondition_typeカラムで参照しているので並びを変えないこと。
        /// </remarks>
        public enum ConditionType
        {
            Null = 0,
            Ace, // 1:エースが得られるボーナス。
            Item, // 2:アイテムで得られるボーナス。
            OpponentEvaluate, // 3:対戦相手の評価値によって得られるボーナス。
            Win, // 4:連勝数によって得られるボーナス。※サーバー側で計算するのでクライアント側では何もしない。
            SupportCard, // 5:サポートカードのレベルによるボーナス。
        }
        
        public const float BONUS_DEFAULT = ScoreBonusRate.BONUS_DEFAULT;
        
        /// <summary>
        /// エースによるスコアボーナス計算。
        /// </summary>
        public static void CalcAceBonus(bool isAce, ref HorseScoreCalculator.ScoreBonusDesc retBonusDesc)
        {
            var bonusMasters = MasterManager.Instance.MasterTeamStadiumScoreBonus.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.Ace);
            foreach (var bonusMaster in bonusMasters)
            {
                if (bonusMaster.ConditionValue1 == (isAce ? 1 : 0))
                {
                    float bonusRate = RaceUtilMath.MasterInt2Float(bonusMaster.ScoreRate);
                    retBonusDesc.BonusList.Add(new ScoreBonusRate(bonusMaster.Id, bonusRate));
                    return;
                }
            }
        }
        
        /// <summary>
        /// 対戦相手の評価値によるスコアボーナス計算。
        /// </summary>
        public static void CalcOpponentEvaluateBonus(int selfEvaluate, int opponentEvaluate, ref HorseScoreCalculator.ScoreBonusDesc retBonusDesc)
        {
            var bonusMasters = MasterManager.Instance.MasterTeamStadiumScoreBonus.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.OpponentEvaluate);
            
            // このボーナスデータは１件しか登録されていない想定なので件数でAssertしておく。
            Debug.Assert(bonusMasters.Count == 1, "対戦相手評価によるスコアボーナス定義が複数存在します。");

            var bonusMaster = bonusMasters.FirstOrDefault();
            if (bonusMaster == null)
            {
                return;
            }

            int divide = (bonusMaster.ScoreRate - (opponentEvaluate - selfEvaluate));
            if (divide <= 0)
            {
                return;
            }
            
            float bonusRate = (float)opponentEvaluate / divide;
            retBonusDesc.BonusList.Add(new ScoreBonusRate(bonusMaster.Id, bonusRate));
        }
        
        
        /// <summary>
        /// アイテムよるスコアボーナス計算。
        /// </summary>
        public static void CalcItemBonus(int[] itemIdArray, ref HorseScoreCalculator.ScoreBonusDesc retBonusDesc)
        {
            if (itemIdArray == null || itemIdArray.Length == 0)
            {
                return;
            }
            
            var masterItem = MasterManager.Instance.MasterItemData;
            var itemMasterArray = itemIdArray.Select(itemId => masterItem.Get(itemId));
            
            var bonusMasters = MasterManager.Instance.MasterTeamStadiumScoreBonus.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.Item);
            foreach (var bonusMaster in bonusMasters)
            {
                foreach (var itemMaster in itemMasterArray)
                {
                    // アイテムの効果種類が、ボーナスcsv側のconditionValue1と一致したらボーナス適用できる。
                    if (itemMaster.EffectType1 == bonusMaster.ConditionValue1)
                    {
                        // ボーナス値はボーナスcsv側で定義してある。
                        float bonusRate = RaceUtilMath.MasterInt2Float(bonusMaster.ScoreRate);
                        retBonusDesc.BonusList.Add(new ScoreBonusRate(bonusMaster.Id, bonusRate));
                        return;
                    }
                }
            }
        }
        
        /// <summary>
        /// サポートカードによるボーナス計算。
        /// </summary>
        public static void CalcSupportCardBonus(int supportCardScoreBonus, ref HorseScoreCalculator.ScoreBonusDesc retBonusDesc)
        {
            var bonusMaster = MasterManager.Instance.MasterTeamStadiumScoreBonus.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.SupportCard);
            if (bonusMaster == null)
            {
                return;
            }
            
            float bonusRate = RaceUtil.SupportCardBonusInt2Float(supportCardScoreBonus);
            retBonusDesc.BonusList.Add(new ScoreBonusRate(bonusMaster[0].Id, bonusRate));
        }
    }
}
#endif

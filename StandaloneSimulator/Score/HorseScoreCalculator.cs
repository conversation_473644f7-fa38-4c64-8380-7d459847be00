using System.Collections;
using System.Collections.Generic;
using System.Linq;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 競技場モードのスコア計算機：レース展開によるスコア。この計算機はキャラと1:1で紐づく。
    /// </summary>
    /// <seealso cref="Test.TestHorseScoreCalculator"/>
    //-------------------------------------------------------------------
    public sealed partial class HorseScoreCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>
        /// ボーナス情報。
        /// </summary>
        public class ScoreBonusDesc
        {
            public List<ScoreBonusRate> BonusList = new List<ScoreBonusRate>();
            
            public ScoreBonusDesc()
            {
            }

            public void AddBonusRate(int scoreBonusId, float rate)
            {
                if (BonusList.Any(b => b.ScoreBonusId == scoreBonusId))
                {
                    return;
                }
                BonusList.Add(new ScoreBonusRate(scoreBonusId, rate));
            }

            /// <summary>
            /// ボーナス適用。
            /// </summary>
            public (int ScoreWithBonus, ScoreBonusData[] ScoreDataWithBonus) ApplyBonus(ScoreData retBonus)
            {
                int retScore = retBonus.score;
                foreach (var bonus in BonusList)
                {
                    int addScore = bonus.Apply(retScore);

                    var addTo = retBonus.bonus_array.FirstOrDefault(b => b.score_bonus_id == bonus.ScoreBonusId);
                    if (addTo == null)
                    {
                        retBonus.AddArray(new ScoreBonusData(bonus.ScoreBonusId, addScore));
                    }
                    else
                    {
                        addTo.bonus_score += addScore;
                    }
                }
                
                int scoreWithBonus = retBonus.ApplyBonus(retScore);
                // #126432: scoreBonusDataも更新する必要がある
                return (scoreWithBonus, retBonus.bonus_array);
            }
        }
        
        public const float TEMPTATION_SCORE_ADD_INTERVAL = 1; // 興奮中はこの時間ごとにスコア加算する。
        private const int TOTAL_SCORE_MIN = 0;
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>
        /// 獲得したスコア。
        /// </summary>
        public List<ScoreData> ScoreList { get; private set; } = new List<ScoreData>();
        
        private readonly IHorseRaceInfoSimulate _owner;
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly IRaceEventRecorder _eventRecorder;
        private readonly IRaceTimeAccessor _timeAccessor;
        private readonly float _borderTime;

        private float _temptationScoreAddInterval;
        private Gallop.RaceDefine.HorsePhase _prevPhase;
        
    #if CYG_DEBUG
        /// <summary>
        /// スコア合計計算済みかどうか。
        /// </summary>
        /// <remarks>
        /// 合計値計算後、このクラスでスコア加算が行われないようにチェックするためのフラグ。
        /// </remarks>
        public bool DbgIsTotalScoreCalculated { get; set; }
        /// <summary>
        /// スコアにボーナス反映済みかどうか。
        /// </summary>
        private bool _dbgIsAppliedBonus;

        public void DbgSetPrevPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            _prevPhase = phase;
        }
    #endif
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseScoreCalculator(
            IHorseRaceInfoSimulate owner,
            IRaceHorseAccessor horseAccessor,
            IRaceEventRecorder eventRecorder, 
            IRaceTimeAccessor timeAccessor,
            float borderTime)
        {
            _owner = owner;
            _horseAccessor = horseAccessor;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _borderTime = borderTime;
            _temptationScoreAddInterval = TEMPTATION_SCORE_ADD_INTERVAL;
            _prevPhase = Gallop.RaceDefine.HorsePhase.Start;
        }

        /// <summary>
        /// 更新。
        /// </summary>
        public void Update(float deltaTime)
        {
            UpdateScoreTemptation(deltaTime);
            UpdateScorePositionKeep();
        }

        /// <summary>
        /// 興奮継続時間によるスコア加算更新。
        /// </summary>
        private void UpdateScoreTemptation(float deltaTime)
        {
            // 興奮状態でなければ不要。
            if (!_owner.IsTemptation)
            {
                return;
            }
            
            // 興奮一定時間継続でスコア加算。 
            _temptationScoreAddInterval -= deltaTime;
            if (_temptationScoreAddInterval > 0)
            {
                return;
            }

            _temptationScoreAddInterval = TEMPTATION_SCORE_ADD_INTERVAL;
            
            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.TemptationContinue);
            Debug.Assert(rawScoreMasters.Count == 1, "興奮継続によるスコア定義が複数存在します。");

            var rawScoreMaster = rawScoreMasters.FirstOrDefault();
            if (rawScoreMaster == null)
            {
                return;
            }
            
            AddScore(rawScoreMaster.Id);
        }

        /// <summary>
        /// Phase切り替え時のポジション維持の状態によるスコア加算。
        /// </summary>
        private void UpdateScorePositionKeep()
        {
            var phase = _owner.GetPhase();
            
            // Phaseの切り替わりが発生していなければ処理不要。
            if (phase == _prevPhase)
            {
                return;
            }

            // このキャラのPhase切り替わり時に、同走法のトップのキャラを取得。
            var runningStyleTopHorse = _horseAccessor.GetRunningStyleTopOrderHorse(_owner.RunningStyle);
            if (runningStyleTopHorse == null)
            {
                return;
            }

            // 自分が走法内トップならPhaseに応じてスコア加算。            
            bool isTop = _owner == runningStyleTopHorse;
            if (isTop)
            {
                var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.PositionKeep);
                foreach (var rawScoreMaster in rawScoreMasters)
                {
                    if (rawScoreMaster.ConditionValue1 == (int)phase)
                    {
                        AddScore(rawScoreMaster.Id);
                        break; // 条件満たす１件だけを加算。
                    }
                }
            }

            _prevPhase = phase;
        }

        /// <summary>
        /// 全員ゴール時のスコア加算。
        /// </summary>
        public void OnAllFinish(int selfEvaluate, int opponentEvaluate, int supportCardScoreBonus)
        {
            // 着タイムとボーダータイムとの差分によるスコア加算。
            AddScoreFinishTimeBorder();
            
            // 着順によるスコア加算。
            AddScoreFinishOrder(_owner.FinishOrder);
                
            // 着順・人気によるスコア加算。
            AddScoreFinishOrderAndPopularity();

            // 後着との着差によるスコア加算。
            AddScoreFinishDistanceDiff();
            
            // 全てのスコア加算が済んだ後、ボーナスを反映する。
            ApplyBonus(selfEvaluate, opponentEvaluate, supportCardScoreBonus);
        }
        
        /// <summary>
        /// 条件別スコア加算。
        /// </summary>
        private void AddScore(int rawScoreId)
        {
        #if CYG_DEBUG
            Debug.Assert(!DbgIsTotalScoreCalculated, "既に合計スコアが計算された後にAddScoreが呼ばれています。");
            Debug.Assert(!_dbgIsAppliedBonus, "ボーナスがスコアに反映された後にスコア加算が行われています。");
        #endif

            var scoreData = ScoreList.FirstOrDefault(s => s.raw_score_id == rawScoreId);
            if (scoreData == null)
            {
                scoreData = new ScoreData(rawScoreId);
                ScoreList.Add(scoreData);
            }

            // 素点を加点。ボーナス反映はレース終了後に行う。
            int rawScore = GetRawScoreByType(rawScoreId);
            scoreData.Add(rawScore);

            // スキル獲得イベントの記録。※イベントにはボーナスを含まない素点を記録する。
            _eventRecorder.AddScoreEvent(
                _owner.HorseIndex, 
                rawScoreId, 
                rawScore, 
                _timeAccessor.AccumulateTimeSinceStart);
        }

        /// <summary>
        /// スコアにボーナス反映。レース終了後に一度だけ行う。
        /// </summary>
        private void ApplyBonus(int selfEvaluate, int opponentEvaluate, int supportCardScoreBonus)
        {
        #if CYG_DEBUG
            Debug.Assert(!_dbgIsAppliedBonus, "ボーナスがスコアに反映された後に再度ボーナス反映が行われています。");
        #endif
            
            var bonusDesc = new ScoreBonusDesc();
            HorseScoreBonusCalculator.CalcAceBonus(_owner.IsTeamAce, ref bonusDesc);
            HorseScoreBonusCalculator.CalcOpponentEvaluateBonus(selfEvaluate, opponentEvaluate, ref bonusDesc);
            HorseScoreBonusCalculator.CalcItemBonus(_owner.ItemIdArray, ref bonusDesc);
            HorseScoreBonusCalculator.CalcSupportCardBonus(supportCardScoreBonus, ref bonusDesc);
        #if CYG_DEBUG
            _owner.DbgBonusRateList = bonusDesc.BonusList;
        #endif
            
            foreach (var score in ScoreList)
            {
                if (score.score <= 0)
                {
                    continue;
                }
                // #126432: scoreBonusDataも更新する必要がある
                (score.score, score.bonus_array) = bonusDesc.ApplyBonus(score);
            }
            
        #if CYG_DEBUG
            _dbgIsAppliedBonus = true;
        #endif
        }

        private static int GetRawScoreByType(int situationId)
        {
            var masterSituationScore = MasterManager.Instance.MasterTeamStadiumRawScore.Get(situationId);
            if (masterSituationScore == null)
            {
                Debug.LogWarning($"situationIdがteam_stadium_situation_score.csvに定義されていません。situationId={situationId}");
                return 0;
            }
            return masterSituationScore.Score;
        }

        /// <summary>
        /// 全条件のスコア合算値計算。
        /// </summary>
        public int CalcTotal()
        {
            int total = ScoreList.Sum(score => score.score);

            // 最小値でクリップ。
            if (total < TOTAL_SCORE_MIN)
            {
                total = TOTAL_SCORE_MIN;
            }

            return total;
        }
        
        #region 条件別の加算
        /// <summary>
        /// 条件判定及びスコア加算：着タイムがボーダータイムを上回った秒数。
        /// </summary>
        private void AddScoreFinishTimeBorder()
        {
            Debug.Assert(_owner.FinishTimeScaled >= 0, "着タイムがまだ計算されていない");
            
            // 小数第２位以下を切り捨て。
            // この10倍かつ整数にした値で比較を行っていく。10で割ってfloatに戻すと近似値による誤差が発生する可能性があるため。
            int finishTimeInt = RaceUtil.ScaleAndRoundFinishTime(_owner.FinishTimeScaled);
            int borderTimeInt = RaceUtil.ScaleAndRoundFinishTime(_borderTime);
            
            // ボーダータイムと着タイムの差分。正の値なら、ボーダータイムより早くゴールできているということ。
            int finishTimeDiff = borderTimeInt - finishTimeInt;
            // 負の値ならボーダータイムより遅いのでスコア加算はあり得ない。
            if (finishTimeDiff < 0)
            {
                return;
            }

            var situationMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishTimeBorder);
            foreach (var situationMaster in situationMasters)
            {
                // ConditionValue1秒以上、かつ、ConditionValue2秒未満で条件を満たす。
                // これらの値は秒の10倍の値で入力されているので、↑で10倍したままのfinishTimeDiffと直接比較行える。
                // 例：1.2秒なら12、2.0秒なら20と入力されている。
                int finishTimeDiffMin = situationMaster.ConditionValue1;
                int finishTimeDiffMax = situationMaster.ConditionValue2;
                
                if (finishTimeDiff >= finishTimeDiffMin &&
                    finishTimeDiff < finishTimeDiffMax)
                {
                    AddScore(situationMaster.Id);
                    break; // 条件満たす１件だけを加算。
                }
            }
        }

        /// <summary>
        /// 条件判定及びスコア加算：興奮開始時。
        /// </summary>
        public void AddScoreTemptationStart()
        {
            var situationMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.TemptationStart);
            Debug.Assert(situationMasters.Count == 1, "興奮開始によるスコア定義が複数存在します。");

            var situationMaster = situationMasters.FirstOrDefault();
            if (situationMaster == null)
            {
                return;
            }

            AddScore(situationMaster.Id);
        }

        /// <summary>
        /// 条件判定及びスコア加算：スタートダッシュ。
        /// </summary>
        public void AddScoreGoodStart()
        {
            const int CONDITION_GOODSTART = 1;
            const int CONDITION_BADSTART = 2;
            const int CONDITION_NORMAL = 3;

            bool isAdd = false;
            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.StartDash);
            foreach (var rawScoreMaster in rawScoreMasters)
            {
                // ConditionValue1が1で成功、2で失敗、3でどちらでもない。
                switch (rawScoreMaster.ConditionValue1)
                {
                    case CONDITION_GOODSTART:
                        if (_owner.IsGoodStart)
                        {
                            AddScore(rawScoreMaster.Id);
                            isAdd = true;
                        }
                        break;
                    case CONDITION_BADSTART:
                        if (_owner.IsBadStart)
                        {
                            AddScore(rawScoreMaster.Id);
                            isAdd = true;
                        }
                        break;
                    case CONDITION_NORMAL:
                        if (!_owner.IsGoodStart && !_owner.IsBadStart)
                        {
                            AddScore(rawScoreMaster.Id);
                            isAdd = true;
                        }
                        break;
                }

                // 条件満たす１件だけを加算。
                if (isAdd)
                {
                    break;
                }
            }
        }
        
        /// <summary>
        /// 条件判定及びスコア加算：後着との着差。
        /// </summary>
        public void AddScoreFinishDistanceDiff()
        {
            int finishOrder = _owner.FinishOrder;
            
            // １位のキャラ以外は加算されない。
            if (finishOrder != 0)
            {
                return;
            }
            
            // 後着のキャラが持っている、前着との着タイム差分を、着差に変換。
            var behindHorse = _horseAccessor.GetHorseInfoByFinishOrder(finishOrder+1);
            float finishTimeDiff = behindHorse.FinishTimeDiffFromPrevHorse;
            var horseLength = RaceUtil.GetHorseLengthByDiffTime(finishTimeDiff);

            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishDiff);
            foreach (var rawScoreMaster in rawScoreMasters)
            {
                // ConditionValue1が着差の種類。
                if (rawScoreMaster.ConditionValue1 == (int)horseLength)
                {
                    AddScore(rawScoreMaster.Id);
                    break; // 条件満たす１件だけを加算。
                }
            }
        }

        /// <summary>
        /// 条件判定及びスコア加算：大穴。
        /// </summary>
        public void AddScoreFinishOrderAndPopularity()
        {
            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishOrderAndPopularity);

            int finishOrder = _owner.FinishOrder;
            int popularity = _owner.Popularity;

            Debug.Assert(finishOrder >= 0, "着順がまだ計算されていない");
            
            foreach (var rawScoreMaster in rawScoreMasters)
            {
                // ConditionValue1で指定された順位以内に入っており、
                if (rawScoreMaster.ConditionValue1 >= finishOrder + 1) // csvでは着順は1~18で入力されているため+1して比較する。
                {
                    // ConditionValue2で指定された人気以下であればスコア加算。
                    if (rawScoreMaster.ConditionValue2 <= popularity + 1) // csvでは人気は1~18で入力されているため+1して比較する。
                    {
                        AddScore(rawScoreMaster.Id);
                        break; // 条件満たす１件だけを加算。
                    }
                }
            }
        }

        /// <summary>
        /// 条件判定及びスコア加算：スキル発動。
        /// </summary>
        public void AddScoreSkillActivate(Gallop.SkillDefine.SkillRarity rarity, int level, int groupRate)
        {
            // groupRateが負数なら自身へのデバフなので加算しない。
            if (groupRate < 0)
            {
                return;
            }
            
            bool isAdd = false;
            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.Skill);
            foreach (var rawScoreMaster in rawScoreMasters)
            {
                // ConditionValue1がスキルのレアリティ（SkillDefine.SkillRarity）指定。
                if (rawScoreMaster.ConditionValue1 != (int)rarity)
                {
                    continue;
                }
                // ConditionValue2がスキルのレベル指定。０だとレベル無視。
                if (rawScoreMaster.ConditionValue2 != 0 && rawScoreMaster.ConditionValue2 != (int)level)
                {
                    continue;
                }
                AddScore(rawScoreMaster.Id);
                isAdd = true;
                break; // 条件満たす１件だけを加算。
            }

            if (!isAdd)
            {
                Debug.LogWarning($"スキル発動時のスコア加算に失敗しました。team_stadium_raw_score.csvにスキルのレアリティ・レベルが定義されていません。rarity={rarity}, level={level}");
            }
        }

        /// <summary>
        /// 条件判定及びスコア加算：着順。
        /// </summary>
        /// <param name="finishOrder">着順。0~</param>
        public void AddScoreFinishOrder(int finishOrder)
        {
            Debug.Assert(finishOrder >= 0, "着順がまだ計算されていない");
            
            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishOrder);
            foreach (var rawScoreMaster in rawScoreMasters)
            {
                // csvでは着順は1~18で入力されているため+1して比較する。
                if (rawScoreMaster.ConditionValue1 == (finishOrder + 1))
                {
                    AddScore(rawScoreMaster.Id);
                    break; // 条件満たす１件だけを加算。
                }
            }
        }

        /// <summary>
        /// 条件判定及びスコア取得：５レースの最終戦績。
        /// </summary>
        /// <param name="winNum">勝ち数。</param>
        /// <param name="loseNum">負け数。</param>
        /// <remarks>
        /// レース外から呼び出す想定なので条件判定とスコアの返却のみ行う。
        /// </remarks>
        public static int CalcScoreFinalResult(int winNum, int loseNum)
        {
            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinalResult);
            foreach (var rawScoreMaster in rawScoreMasters)
            {
                // ConditionValue1=1なら勝ち数参照
                // ConditionValue1=2なら負け数参照
                int checkNum = rawScoreMaster.ConditionValue1 == 1 ? winNum : loseNum; 
                
                // 数がConditionValue2以上なら条件を満たしている。
                if (checkNum >= rawScoreMaster.ConditionValue2)
                {
                    // 条件を満たした最初のスコアのみが加算対象。
                    return GetRawScoreByType(rawScoreMaster.Id);
                }
            }

            return 0;
        }
        
        #endregion
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
// Debugデータが参照を持つため、切り出している
namespace StandaloneSimulator
{
    public class ScoreBonusRate
    {
        public const float BONUS_DEFAULT = 0.0f;
        
        public int ScoreBonusId;
        public float BonusRate = BONUS_DEFAULT;
#if GALLOP
        public string BonusName => Gallop.TextUtil.GetMasterText(Gallop.MasterString.Category.TeamStadiumScoreBonusName, ScoreBonusId);
#endif
        public ScoreBonusRate(int bonusId, float rate)
        {
            ScoreBonusId = bonusId;
            BonusRate = rate;
        }

        public int Apply(int score)
        {
            // 端数切り上げ。
            int ret = RaceUtilMath.CeilToInt(score * BonusRate - RaceUtilMath.EPSILON);
            return ret;
        }
    }
}
#endif

namespace StandaloneSimulator
{
    public sealed partial class HorseScoreCalculator
    {
        /// <summary>
        /// 条件判定の種類。
        /// </summary>
        /// <remarks>
        /// team_stadium_situation_score.csvのcondition_typeカラムで参照しているので並びを変えないこと。
        /// </remarks>
        public enum ConditionType
        {
            Null = 0,
            FinishDiff, // 1:１位のキャラが後続のキャラとの着差によって得られる。
            FinishOrderAndPopularity, // 2:着順と人気で比較。
            FinishOrder, // 3:着順を比較。
            FinishTimeBorder, // 4:着タイムがボーダータイムを上回った秒数で比較。
            TemptationStart, // 5:興奮開始時。
            TemptationContinue, // 6:興奮継続１秒ごと。
            StartDash, // 7:スタートダッシュ結果を比較。
            Skill, // 8:スキル発動時。
            PositionKeep, // 9:同走法内で１位になっている。
            FinalResult, // 10:５レースの勝ち負け数を比較。※レース外で判定される。
            TeamResult, // 11:チームメンバー複数人の着順を比較。
        }
    }
}
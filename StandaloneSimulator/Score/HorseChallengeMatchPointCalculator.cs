#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// チャレンジマッチのポイント計算機。この計算機はキャラと1:1で紐づく。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed partial class HorseChallengeMatchPointCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        private const int TOTAL_POINT_MIN = 0;
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>
        /// 獲得したポイント。
        /// </summary>
        public List<ChallengeMatchPointData> PointList { get; private set; } = new List<ChallengeMatchPointData>();
        
        private readonly IHorseRaceInfoSimulate _owner;
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly IRaceEventRecorder _eventRecorder;
        private readonly IRaceTimeAccessor _timeAccessor;
        private readonly float _borderTime;
        private readonly Gallop.RaceDefine.Difficulty _difficulty;

        private Gallop.RaceDefine.HorsePhase _prevPhase;
        
    #if CYG_DEBUG
        /// <summary>
        /// ポイント合計計算済みかどうか。
        /// </summary>
        /// <remarks>
        /// 合計値計算後、このクラスでポイント加算が行われないようにチェックするためのフラグ。
        /// </remarks>
        public bool DbgIsTotalPointCalculated { get; set; }
        /// <summary>
        /// ポイントにボーナス反映済みかどうか。
        /// </summary>
        private bool _dbgIsAppliedBonus;

        public void DbgSetPrevPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            _prevPhase = phase;
        }
    #endif
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public HorseChallengeMatchPointCalculator(
            IHorseRaceInfoSimulate owner,
            IRaceHorseAccessor horseAccessor,
            IRaceEventRecorder eventRecorder, 
            IRaceTimeAccessor timeAccessor,
            float borderTime,
            Gallop.RaceDefine.Difficulty difficulty)
        {
            _owner = owner;
            _horseAccessor = horseAccessor;
            _eventRecorder = eventRecorder;
            _timeAccessor = timeAccessor;
            _borderTime = borderTime;
            _prevPhase = Gallop.RaceDefine.HorsePhase.Start;
            _difficulty = difficulty;
        }

        /// <summary>
        /// 更新。
        /// </summary>
        public void Update(float deltaTime)
        {
            UpdatePointPositionKeep();
        }

        /// <summary>
        /// Phase切り替え時のポジション維持の状態によるポイント加算。
        /// </summary>
        private void UpdatePointPositionKeep()
        {
            var phase = _owner.GetPhase();
            
            // Phaseの切り替わりが発生していなければ処理不要。
            if (phase == _prevPhase)
            {
                return;
            }

            // このキャラのPhase切り替わり時に、同走法のトップのキャラを取得。
            var runningStyleTopHorse = _horseAccessor.GetRunningStyleTopOrderHorse(_owner.RunningStyle);
            if (runningStyleTopHorse == null)
            {
                return;
            }

            // 自分が走法内トップならPhaseに応じてポイント加算。            
            bool isTop = _owner == runningStyleTopHorse;
            if (isTop)
            {
                var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.PositionKeep);
                foreach (var rawPointMaster in rawPointMasters)
                {
                    if (rawPointMaster.ConditionValue1 == (int)phase)
                    {
                        AddPoint(rawPointMaster.Id);
                        break; // 条件満たす１件だけを加算。
                    }
                }
            }

            _prevPhase = phase;
        }

        /// <summary>
        /// 全員ゴール時のポイント加算。
        /// </summary>
        public void OnAllFinish()
        {
            // 着タイムとボーダータイムとの差分によるポイント加算。
            AddPointFinishTimeBorder();
            
            // 着順によるポイント加算。
            AddPointFinishOrder(_owner.FinishOrder);
                
            // 着順・人気によるポイント加算。
            AddPointFinishOrderAndPopularity();

            // 着順・難易度によるポイント加算。
            AddPointFinishOrderAndDifficulty(_owner.FinishOrder, _difficulty);

            // 後着との着差によるポイント加算。
            AddPointFinishDistanceDiff();

            // ボーナス計算。
            ApplyBonus();
        }
        
        /// <summary>
        /// ポイントにボーナス反映。レース終了後に一度だけ行う。
        /// </summary>
        private void ApplyBonus()
        {
        #if CYG_DEBUG
            Debug.Assert(!_dbgIsAppliedBonus, "ボーナスがポイントに反映された後に再度ボーナス反映が行われています。");
        #endif

        #if false // hibi ボーナス仕様復活時のために残しておく。
            float bonusRate = CalcBonusRate();
            foreach (var point in PointList)
            {
                if (point.Point <= 0)
                {
                    continue;
                }

                // 端数切り上げ。※チーム競技場のHorsePointCalculatorに合わせる。
                point.Bonus = Mathf.CeilToInt(point.Point * bonusRate - Math.EPSILON);
                point.Point += point.Bonus;
            }
        #endif
            
        #if CYG_DEBUG
            _dbgIsAppliedBonus = true;
        #endif
        }

        /// <summary>
        /// 条件別ポイント加算。
        /// </summary>
        private void AddPoint(int rawPointId)
        {
        #if CYG_DEBUG
            Debug.Assert(!DbgIsTotalPointCalculated, "既に合計ポイントが計算された後にAddPointが呼ばれています。");
            Debug.Assert(!_dbgIsAppliedBonus, "ボーナスがポイントに反映された後にポイント加算が行われています。");
        #endif

            var pointData = PointList.FirstOrDefault(s => s.raw_point_id == rawPointId);
            if (pointData == null)
            {
                pointData = new ChallengeMatchPointData(rawPointId);
                PointList.Add(pointData);
            }

            // 素点を加点。ボーナス反映はレース終了後に行う。
            int rawPoint = GetRawPointByType(rawPointId);
            pointData.Add(rawPoint);

            // スキル獲得イベントの記録。※イベントにはボーナスを含まない素点を記録する。
            _eventRecorder.AddChallengeMatchPointEvent(
                _owner.HorseIndex, 
                rawPointId, 
                rawPoint, 
                _timeAccessor.AccumulateTimeSinceStart);
        }

        private static int GetRawPointByType(int rawPointId)
        {
            var masterRawPoint = MasterManager.Instance.MasterChallengeMatchRawPoint.Get(rawPointId);
            if (masterRawPoint == null)
            {
                Debug.LogWarning($"rawPointIdがchallenge_match_raw_point.csvに定義されていません。rawPointId={rawPointId}");
                return 0;
            }
            return masterRawPoint.Point;
        }

        /// <summary>
        /// 全条件のポイント合算値計算。
        /// </summary>
        public int CalcTotal()
        {
            int total = PointList.Sum(x => x.point);

            // 最小値でクリップ。
            if (total < TOTAL_POINT_MIN)
            {
                total = TOTAL_POINT_MIN;
            }

            return total;
        }
        
        #region 条件別の加算
        /// <summary>
        /// 条件判定及びポイント加算：着タイムがボーダータイムを上回った秒数。
        /// </summary>
        private void AddPointFinishTimeBorder()
        {
            Debug.Assert(_owner.FinishTimeScaled >= 0, "着タイムがまだ計算されていない");
            
            // 小数第２位以下を切り捨て。
            // この10倍かつ整数にした値で比較を行っていく。10で割ってfloatに戻すと近似値による誤差が発生する可能性があるため。
            int finishTimeInt = RaceUtil.ScaleAndRoundFinishTime(_owner.FinishTimeScaled);
            int borderTimeInt = RaceUtil.ScaleAndRoundFinishTime(_borderTime);
            
            // ボーダータイムと着タイムの差分。正の値なら、ボーダータイムより早くゴールできているということ。
            int finishTimeDiff = borderTimeInt - finishTimeInt;
            // 負の値ならボーダータイムより遅いのでポイント加算はあり得ない。
            if (finishTimeDiff < 0)
            {
                return;
            }

            var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishTimeBorder);
            foreach (var masterRawPoint in rawPointMasters)
            {
                // ConditionValue1秒以上、かつ、ConditionValue2秒未満で条件を満たす。
                // これらの値は秒の10倍の値で入力されているので、↑で10倍したままのfinishTimeDiffと直接比較行える。
                // 例：1.2秒なら12、2.0秒なら20と入力されている。
                int finishTimeDiffMin = masterRawPoint.ConditionValue1;
                int finishTimeDiffMax = masterRawPoint.ConditionValue2;
                
                if (finishTimeDiff >= finishTimeDiffMin &&
                    finishTimeDiff < finishTimeDiffMax)
                {
                    AddPoint(masterRawPoint.Id);
                    break; // 条件満たす１件だけを加算。
                }
            }
        }

        /// <summary>
        /// 条件判定及びポイント加算：スタートダッシュ。
        /// </summary>
        public void AddPointGoodStart()
        {
            const int CONDITION_GOODSTART = 1;
            const int CONDITION_BADSTART = 2;
            const int CONDITION_NORMAL = 3;

            bool isAdd = false;
            var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.StartDash);
            foreach (var rawPointMaster in rawPointMasters)
            {
                // ConditionValue1が1で成功、2で失敗、3でどちらでもない。
                switch (rawPointMaster.ConditionValue1)
                {
                    case CONDITION_GOODSTART:
                        if (_owner.IsGoodStart)
                        {
                            AddPoint(rawPointMaster.Id);
                            isAdd = true;
                        }
                        break;
                    case CONDITION_BADSTART:
                        if (_owner.IsBadStart)
                        {
                            AddPoint(rawPointMaster.Id);
                            isAdd = true;
                        }
                        break;
                    case CONDITION_NORMAL:
                        if (!_owner.IsGoodStart && !_owner.IsBadStart)
                        {
                            AddPoint(rawPointMaster.Id);
                            isAdd = true;
                        }
                        break;
                }

                // 条件満たす１件だけを加算。
                if (isAdd)
                {
                    break;
                }
            }
        }
        
        /// <summary>
        /// 条件判定及びポイント加算：後着との着差。
        /// </summary>
        public void AddPointFinishDistanceDiff()
        {
            int finishOrder = _owner.FinishOrder;
            
            // １位のキャラ以外は加算されない。
            if (finishOrder != 0)
            {
                return;
            }
            
            // 後着のキャラが持っている、前着との着タイム差分を、着差に変換。
            var behindHorse = _horseAccessor.GetHorseInfoByFinishOrder(finishOrder+1);
            float finishTimeDiff = behindHorse.FinishTimeDiffFromPrevHorse;
            var horseLength = RaceUtil.GetHorseLengthByDiffTime(finishTimeDiff);

            var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishDiff);
            foreach (var rawPointMaster in rawPointMasters)
            {
                // ConditionValue1が着差の種類。
                if (rawPointMaster.ConditionValue1 == (int)horseLength)
                {
                    AddPoint(rawPointMaster.Id);
                    break; // 条件満たす１件だけを加算。
                }
            }
        }

        /// <summary>
        /// 条件判定及びポイント加算：大穴。
        /// </summary>
        public void AddPointFinishOrderAndPopularity()
        {
            var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishOrderAndPopularity);

            int finishOrder = _owner.FinishOrder;
            int popularity = _owner.Popularity;

            Debug.Assert(finishOrder >= 0, "着順がまだ計算されていない");
            
            foreach (var rawPointMaster in rawPointMasters)
            {
                // ConditionValue1で指定された順位以内に入っており、
                if (rawPointMaster.ConditionValue1 >= finishOrder + 1) // csvでは着順は1~18で入力されているため+1して比較する。
                {
                    // ConditionValue2で指定された人気以下であればポイント加算。
                    if (rawPointMaster.ConditionValue2 <= popularity + 1) // csvでは人気は1~18で入力されているため+1して比較する。
                    {
                        AddPoint(rawPointMaster.Id);
                        break; // 条件満たす１件だけを加算。
                    }
                }
            }
        }

        /// <summary>
        /// 条件判定及びポイント加算：スキル発動。
        /// </summary>
        public void AddPointSkillActivate(Gallop.SkillDefine.SkillRarity rarity, int level, int groupRate)
        {
            // groupRateが負数なら自身へのデバフなので加算しない。
            if (groupRate < 0)
            {
                return;
            }
            
            bool isAdd = false;
            var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.Skill);
            foreach (var rawPointMaster in rawPointMasters)
            {
                // ConditionValue1がスキルのレアリティ（SkillDefine.SkillRarity）指定。
                if (rawPointMaster.ConditionValue1 != (int)rarity)
                {
                    continue;
                }
                // ConditionValue2がスキルのレベル指定。０だとレベル無視。
                if (rawPointMaster.ConditionValue2 != 0 && rawPointMaster.ConditionValue2 != (int)level)
                {
                    continue;
                }
                AddPoint(rawPointMaster.Id);
                isAdd = true;
                break; // 条件満たす１件だけを加算。
            }

            if (!isAdd)
            {
                Debug.LogWarning($"スキル発動時のポイント加算に失敗しました。team_stadium_raw_point.csvにスキルのレアリティ・レベルが定義されていません。rarity={rarity}, level={level}");
            }
        }

        /// <summary>
        /// 条件判定及びポイント加算：着順。
        /// </summary>
        /// <param name="finishOrder">着順。0~</param>
        public void AddPointFinishOrder(int finishOrder)
        {
            Debug.Assert(finishOrder >= 0, "着順がまだ計算されていない");
            
            var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.FinishOrder);
            foreach (var rawPointMaster in rawPointMasters)
            {
                // csvでは着順は1~18で入力されているため+1して比較する。
                if (rawPointMaster.ConditionValue1 == (finishOrder + 1))
                {
                    AddPoint(rawPointMaster.Id);
                    break; // 条件満たす１件だけを加算。
                }
            }
        }
        
        /// <summary>
        /// 条件判定及びポイント加算：着順。
        /// </summary>
        /// <param name="finishOrder">着順。0~</param>
        /// <param name="difficulty">難易度。</param>
        public void AddPointFinishOrderAndDifficulty(int finishOrder, Gallop.RaceDefine.Difficulty difficulty)
        {
            Debug.Assert(finishOrder >= 0, "着順がまだ計算されていない");
            
            var rawPointMasters = MasterManager.Instance.MasterChallengeMatchRawPoint.GetListWithConditionTypeOrderByPriorityAsc((int)ConditionType.Difficulty);
            foreach (var rawPointMaster in rawPointMasters)
            {
                // ConditionValue1で指定された順位以内に入っており、
                if (rawPointMaster.ConditionValue1 == (int)difficulty)
                {
                    // ConditionValue2で指定された難易度であればポイント加算。
                    if (rawPointMaster.ConditionValue2 >= finishOrder + 1) // csvでは着順は1~18で入力されているため+1して比較する。
                    {
                        AddPoint(rawPointMaster.Id);
                        break; // 条件満たす１件だけを加算。
                    }
                }
            }
        }
        #endregion
    }
}

#endif

namespace StandaloneSimulator
{
    public sealed partial class HorseChallengeMatchPointCalculator
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>
        /// 条件判定の種類。
        /// </summary>
        /// <remarks>
        /// challenge_match_raw_point.csvのcondition_typeカラムで参照しているので並びを変えないこと。
        /// </remarks>
        public enum ConditionType
        {
            Null = 0,
            FinishDiff, // 1:１位のキャラが後続のキャラとの着差によって得られる。
            FinishOrderAndPopularity, // 2:着順と人気で比較。
            FinishOrder, // 3:着順を比較。
            FinishTimeBorder, // 4:着タイムがボーダータイムを上回った秒数で比較。
            NOUSE_5, // 5:未使用。旧興奮開始時。
            NOUSE_6, // 6:未使用。旧興奮継続１秒ごと。
            StartDash, // 7:スタートダッシュ結果を比較。
            Skill, // 8:スキル発動時。
            PositionKeep, // 9:同走法内で１位になっている。
            FinalResult, // 10:５レースの勝ち負け数を比較。※レース外で判定される。
            NOUSE_11, // 11:未使用。
            Difficulty, // 12:特定難易度で勝利。
        }
    }
}
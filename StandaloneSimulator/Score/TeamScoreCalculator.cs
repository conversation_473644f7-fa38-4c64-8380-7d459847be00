using System.Linq;
using System.Collections.Generic;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 競技場モードのスコア計算機：チームが獲得するスコア。この計算機はチームと1:1で紐づく。
    /// </summary>
    /// <seealso cref="Test.TestTeamScoreCalculator"/>
    //-------------------------------------------------------------------
    public sealed partial class TeamScoreCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly IHorseRaceInfoSimulate[] _teamMemberSimulate;

    #if CYG_DEBUG
        /// <summary>
        /// スコアにボーナス反映済みかどうか。
        /// </summary>
        private bool _dbgIsAppliedBonus;
    #endif
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public TeamScoreCalculator(IHorseRaceInfoSimulate[] teamMemberSimulate)
        {
            _teamMemberSimulate = teamMemberSimulate;
        }
        
        /// <summary>
        /// 全員ゴール時のスコア加算。
        /// </summary>
        public void OnAllFinish(int selfEvaluate, int opponentEvaluate, int[] itemIdArray, int supportCardScoreBonus)
        {
            // チーム成績によるスコア加算。
            AddScoreTeamResult();
            
            // 全てのスコア加算が済んだ後、ボーナスを反映する。
            ApplyBonus(selfEvaluate, opponentEvaluate, itemIdArray, supportCardScoreBonus);
        }
        
        /// <summary>
        /// 条件判定及びスコア加算：チーム成績によるスコア。
        /// </summary>
        private void AddScoreTeamResult()
        {
            var finishOrderArray = _teamMemberSimulate.Select(h => h.FinishOrder).ToArray();
            
            var rawScoreMasters = MasterManager.Instance.MasterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int)HorseScoreCalculator.ConditionType.TeamResult);
            foreach (var rawScoreMaster in rawScoreMasters)
            {
                // ConditionValue1が人数。
                if (rawScoreMaster.ConditionValue1 == finishOrderArray.Length)
                {
                    // ConditionValue2が着順。全員この着順以下であること。
                    int finishOrderThreshold = rawScoreMaster.ConditionValue2;
                    if (finishOrderArray.All(finishOrder => finishOrder + 1 <= finishOrderThreshold))
                    {
                        // 条件を満たした最初のスコアのみが加算対象。
                        AddScore(rawScoreMaster.Id);
                        break;
                    }
                }
            }
        }
        
        /// <summary>
        /// 条件別スコア加算。
        /// </summary>
        private void AddScore(int rawScoreId)
        {
            var scoreData = ScoreList.FirstOrDefault(s => s.raw_score_id == rawScoreId);
            if (scoreData == null)
            {
                scoreData = new ScoreData(rawScoreId);
                ScoreList.Add(scoreData);
            }

            // 素点を加点。ボーナス反映はレース終了後に行う。
            int rawScore = GetRawScoreByType(rawScoreId);
            scoreData.Add(rawScore);
        }

        /// <summary>
        /// スコアにボーナス反映。レース終了後に一度だけ行う。
        /// </summary>
        private void ApplyBonus(int selfEvaluate, int opponentEvaluate, int[] itemIdArray, int supportCardScoreBonus)
        {
        #if CYG_DEBUG
            Debug.Assert(!_dbgIsAppliedBonus, "ボーナスがスコアに反映された後に再度ボーナス反映が行われています。");
        #endif

            var bonusDesc = new HorseScoreCalculator.ScoreBonusDesc();
            HorseScoreBonusCalculator.CalcOpponentEvaluateBonus(selfEvaluate, opponentEvaluate, ref bonusDesc);
            HorseScoreBonusCalculator.CalcItemBonus(itemIdArray, ref bonusDesc);
            HorseScoreBonusCalculator.CalcSupportCardBonus(supportCardScoreBonus, ref bonusDesc);
            
            foreach (var score in ScoreList)
            {
                if (score.score <= 0)
                {
                    continue;
                }
                // #126432: scoreBonusDataも更新する必要がある
                (score.score, score.bonus_array) = bonusDesc.ApplyBonus(score);
            }
            
        #if CYG_DEBUG
            _dbgIsAppliedBonus = true;
        #endif
        }
        
        private static int GetRawScoreByType(int rawScoreId)
        {
            var masterRawScore = MasterManager.Instance.MasterTeamStadiumRawScore.Get(rawScoreId);
            if (masterRawScore == null)
            {
                Debug.LogWarning($"rawScoreIdがteam_stadium_raw_score.csvに定義されていません。rawScoreId={rawScoreId}");
                return 0;
            }
            return masterRawScore.Score;
        }

        /// <summary>
        /// 合計スコア計算。
        /// </summary>
        public int CalcTotal()
        {
            int sum = ScoreList.Sum(score => score.score);
            return sum;
        }
    }
}
#endif

namespace StandaloneSimulator
{
    public sealed partial class TeamScoreCalculator
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public List<ScoreData> ScoreList { get; private set; } = new List<ScoreData>();
#if GALLOP
        private readonly Gallop.IHorseRaceInfo[] _teamMember;
        
        public TeamScoreCalculator(Gallop.IHorseRaceInfo[] teamMember)
        {
            _teamMember = teamMember;
        }
#endif
    }
}
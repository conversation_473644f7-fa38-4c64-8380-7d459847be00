namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// シミュレートデータ：キャラ１体のレース結果情報。
    /// </summary>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateHorseResultData
    {
        //-------------------------------------------------------------------
        // シリアライズする変数。
        // ※増減させる場合はCalcSizeの中も変更すること。
        //-------------------------------------------------------------------
        public int FinishOrder = -1; // 着順。0が1位。
        public float FinishTime = 0.0f; // 着タイム。（RaceParamDefine.FinishTimeCoefを乗算した値）
        public float FinishTimeRaw = 0.0f; // 着タイム。（実時間）
        public float FinishDiffTime = 0.0f; // 前の着順のキャラとの着差時間。
        public float StartDelayTime = 0.0f; // スタート遅延時間。
        public byte GutsOrder = 0; // 根性順位。
        public byte WizOrder = 0; // 賢さ順位。
        public float LastSpurtStartDistance = 0.0f; // ラストスパート開始距離。
        public byte RunningStyle = 0; // 走法。
        public int Defeat = 0; // 敗因。RaceDefine.DefeatType。

    #if CYG_DEBUG // デバッグ用途。
        public RaceSimulateHorseResultDebugData DbgData = new RaceSimulateHorseResultDebugData();
    #endif

        //-------------------------------------------------------------------
        // 関数。
        //-------------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。指定キャラで記録。
        /// </summary>
        public RaceSimulateHorseResultData(float startDelayTime, int gutsOrder, int wizOrder, int finishOrder, float finishTimeScaled, float finishTimeRaw, float finishTimeDiffFromPrevHorse,
            float lastSpurtStartDistance, Gallop.RaceDefine.RunningStyle runningStyle, Gallop.RaceDefine.DefeatType defeatType)
        {
            StartDelayTime = startDelayTime;
            GutsOrder = (byte)gutsOrder;
            WizOrder = (byte)wizOrder;
            FinishOrder = finishOrder;
            FinishTime = finishTimeScaled;
            FinishTimeRaw = finishTimeRaw;
            FinishDiffTime = finishTimeDiffFromPrevHorse;
            
            SetLastSpurtStartDistance(lastSpurtStartDistance);
            SetRunningStyle(runningStyle);
            SetDefeat(defeatType);
        }
        
    #if CYG_DEBUG
        /// <summary>
        /// コンストラクタ。指定キャラで記録。
        /// </summary>
        public RaceSimulateHorseResultData(float startDelayTime, int gutsOrder, int wizOrder, int finishOrder, float finishTimeScaled, float finishTimeRaw, float finishTimeDiffFromPrevHorse,
            float lastSpurtStartDistance, Gallop.RaceDefine.RunningStyle runningStyle, Gallop.RaceDefine.DefeatType defeatType, RaceSimulateHorseResultDebugData dbgData)
        {
            StartDelayTime = startDelayTime;
            GutsOrder = (byte)gutsOrder;
            WizOrder = (byte)wizOrder;
            FinishOrder = finishOrder;
            FinishTime = finishTimeScaled;
            FinishTimeRaw = finishTimeRaw;
            FinishDiffTime = finishTimeDiffFromPrevHorse;
            
            SetLastSpurtStartDistance(lastSpurtStartDistance);
            SetRunningStyle(runningStyle);
            SetDefeat(defeatType);

            if(RaceSimulateDebugger.IsRecordDebug)
            {
                DbgData = dbgData;
            }
        }
    #endif

        /// <summary>
        /// コンストラクタ。byte配列から復元。
        /// </summary>
        public RaceSimulateHorseResultData(byte[] byteArray, int version)
        {
            Deserialize(byteArray, version);
        }

        /// <summary>
        /// ゴール時パラメータ取得。
        /// </summary>
        /// <param name="finishOrder">着順。0~。</param>
        public void GetResult(out int finishOrder, out float finishTime, out float finishTimeRaw, out float finishDiffTime)
        {
            finishOrder = FinishOrder;
            finishTime = FinishTime;
            finishTimeRaw = FinishTimeRaw;
            finishDiffTime = FinishDiffTime;
        }

        public void SetLastSpurtStartDistance(float lastSpurtStartDistance)
        {
            LastSpurtStartDistance = lastSpurtStartDistance;
        }
        public float GetLastSpurtStartDistance()
        {
            return LastSpurtStartDistance;
        }

        public void SetRunningStyle(Gallop.RaceDefine.RunningStyle runningStyle)
        {
            RunningStyle = (byte)runningStyle;
        }
        public Gallop.RaceDefine.RunningStyle GetRunningStyle()
        {
            return (Gallop.RaceDefine.RunningStyle)RunningStyle;
        }
        public void SetDefeat(Gallop.RaceDefine.DefeatType defeatType)
        {
            Defeat = (int)defeatType;
        }
        public Gallop.RaceDefine.DefeatType GetDefeat()
        {
            return (Gallop.RaceDefine.DefeatType)Defeat;
        }

        public float GetStartDelayTime()
        {
            return StartDelayTime;
        }
        public byte GetGutsOrder()
        {
            return GutsOrder;
        }
        public byte GetWizOrder()
        {
            return WizOrder;
        }
    }
}

using System.Collections.Generic;
using System.Linq;
using System;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// シミュレートデータ：キャラ１体の各フレームでのデータ。
    /// </summary>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateHorseFrameData
    {
        //-------------------------------------------------------------------
        // 定数。
        //-------------------------------------------------------------------
        public const int BLOCK_HORSEINDEX_NULL = -1;
        
        //-------------------------------------------------------------------
        // 変数。
        // ※増減させる場合はCalcSizeの中も変更すること。
        //-------------------------------------------------------------------
        public float Distance = 0; // 進行距離。
        public float LanePosition = 0; // レーン位置。
        public float Speed = 0; // 現在速度。
        public float Hp = 0; // Hp。
        public sbyte TemptationMode = (sbyte)StandaloneSimulator.TemptationMode.Null; // 掛かりのモード。
        public sbyte BlockFrontHorseIndex = BLOCK_HORSEINDEX_NULL; // 自分の前方をブロックしている対象のキャラ。ブロックされていない場合はBLOCK_HORSEINDEX_NULL。
        
        /// <summary>
        /// コンストラクタ。byte配列から復元。
        /// </summary>
        public RaceSimulateHorseFrameData(byte[] byteArray, int version)
        {
            Deserialize(byteArray, version);
        }

#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
    #if CYG_DEBUG // デバッグ用途。
        public RaceSimulateHorseFrameDebugData DbgData = new RaceSimulateHorseFrameDebugData();
    #endif
        
        public RaceSimulateHorseFrameData(float distance, float lanePosition, float speed, float hp, sbyte temptationMode, sbyte blockFrontHorseIndex)
        {
            Distance = distance;
            LanePosition = lanePosition;
            Speed = speed;
            Hp = hp;
            TemptationMode = temptationMode;
            BlockFrontHorseIndex = blockFrontHorseIndex;
        }
        
    #if CYG_DEBUG
        public RaceSimulateHorseFrameData(float distance, float lanePosition, float speed, float hp, sbyte temptationMode, sbyte blockFrontHorseIndex, RaceSimulateHorseFrameDebugData dbgData)
        {
            Distance = distance;
            LanePosition = lanePosition;
            Speed = speed;
            Hp = hp;
            TemptationMode = temptationMode;
            BlockFrontHorseIndex = blockFrontHorseIndex;
            if (RaceSimulateDebugger.IsRecordDebug)
            {
                DbgData = dbgData;
            }
        }
    #endif
#endif
    }
}
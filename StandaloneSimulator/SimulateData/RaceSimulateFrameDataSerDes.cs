using System.Linq;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateFrameDataのシリアライズ・デシリアライズ処理。
    /// </summary>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateFrameData
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        /// <summary>
        /// 自身をバイト配列に変換。
        /// </summary>
        public byte[] Serialize()
        {
            //-----------------------------------------------------------
            // 自身１件あたりのバイト数計算。
            // RaceSimulateHorseFrameDataのデータサイズは静的に求まるため、xキャラ数でok.
            //-----------------------------------------------------------
            int numBytes = 
                sizeof(float) + // Time
                RaceSimulateHorseFrameData.CalcSize() * HorseDataArray.Length;

            //-----------------------------------------------------------
            // byte配列にデータを詰める。
            //-----------------------------------------------------------
            byte[] retBytes = new byte[numBytes];
            int destOffset = 0;

            // Time
            RaceUtil.FloatToBytesAddOffset(Time, ref retBytes, ref destOffset);

            // キャラ毎データ。
            for (int i = 0; i < HorseDataArray.Length; ++i)
            {
                HorseDataArray[i].Serialize(ref retBytes, ref destOffset);
            }

            return retBytes;
        }
#endif

        /// <summary>
        /// バイト配列から自身を復元。
        /// </summary>
        public void Deserialize(byte[] bytes, int version, int charaNum, int charaSize)
        {
            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if( version >= RaceSimulateDataHeader.SIMULATE_DATA_VERSION_20200406)
            {
                Deserialize_Ver20200406_OrNewer(bytes, version, charaNum, charaSize);
            }
            //-----------------------------------------------------------
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
            /* 参考コード。
            else
            {
                Deserialize_Ver20180628_OrNewer( bytes, version );
            }
            */
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver20200406_OrNewer(byte[] bytes, int version, int charaNum, int charaSize)
        {
            int offset = 0;

            // Time。
            RaceUtil.BytesToFloatAddOffset(bytes, ref Time, ref offset);
            // キャラのデータ。
            HorseDataArray = new RaceSimulateHorseFrameData[charaNum];
            for (int i = 0; i < HorseDataArray.Length; ++i)
            {
                var charaByteArray = new byte[charaSize];
                System.Array.Copy(bytes, offset, charaByteArray, 0, charaSize);                
                
                HorseDataArray[i] = new RaceSimulateHorseFrameData(charaByteArray, version);
                offset += charaSize;
            }
        }

    #if CYG_DEBUG
        /// <summary>
        /// デバッグ情報をbyte配列に変換。
        /// </summary>
        public byte[] SerializeDebug()
        {
            //-----------------------------------------------------------
            // 全キャラのデバッグ情報をbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] bytesArrayDebug = new byte[HorseDataArray.Length][];
            for (int i = 0; i < bytesArrayDebug.Length; ++i)
            {
                bytesArrayDebug[i] = HorseDataArray[i].SerializeDebug(); // SerializeDebugはサイズ可変なのでキャラごとに配列用意。
            }

            //-----------------------------------------------------------
            // デバッグ情報バイト数計算。
            //-----------------------------------------------------------
            int numBytes = bytesArrayDebug.Sum(b => b.Length);

            //-----------------------------------------------------------
            // byte配列にデータを詰める。
            //-----------------------------------------------------------
            byte[] retBytes = new byte[numBytes];
            int destOffset = 0;

            // キャラ毎デバッグデータ。
            for (int i = 0; i < bytesArrayDebug.Length; ++i)
            {
                System.Array.Copy(bytesArrayDebug[i], 0, retBytes, destOffset, bytesArrayDebug[i].Length);
                destOffset += bytesArrayDebug[i].Length;
            }

            return retBytes;
        }

        /// <summary>
        /// byte配列からデバッグ情報を復元。
        /// </summary>
        public void DeserializeDebug(byte[] bytes, ref int offset)
        {
            for(int i = 0; i < HorseDataArray.Length; ++i)
            {
                HorseDataArray[i].DeserializeDebug(bytes, ref offset);
            }
        }
    #endif
    }
}
using System;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// １レースの記録/再生に必要なデータ。
    /// </summary>
    /// <remarks>
    /// 
    /// RaceSimulateData
    /// ┃
    /// ┣━RaceSimulateFrameData
    /// ┣━...（記録回数分 ≒ １秒ごとに記録しているのでレースにかかった秒数と"ほぼ"一緒）
    /// ┃　┣━RaceSimulateHorseFrameData（各キャラの記録フレームごとの情報（距離やレーンや速度やHpなど））
    /// ┃　┗━...（出走人数分）
    /// ┃
    /// ┣━RaceSimulateHorseResultData（各キャラのレース中不変のデータ（着順・着タイムなど））
    /// ┣━...（出走人数分）
    /// ┃
    /// ┣━RaceSimulateEventData（スキルの発動イベントなど）
    /// ┗━...（レース中に発生したイベント回数分）
    /// 
    /// </remarks>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateData
    {
        //---------------------------------------------------------------
        // 定数。
        //---------------------------------------------------------------
        private const int FRAME_START_INDEX = 1;
        private const int FRAME_NEXT_OFFSET = 1;

        //---------------------------------------------------------------
        // シリアライズする変数。
        //---------------------------------------------------------------
        public RaceSimulateDataHeader Header { get; private set; }
        private List<RaceSimulateFrameData> _frameDataList = new List<RaceSimulateFrameData>(100); // １秒ごとに記録されたデータ。
        private RaceSimulateHorseResultData[] _horseResultDataArray; // 各キャラのレース通して不変のデータ。
        private List<RaceSimulateEventData> _simEvDataList = new List<RaceSimulateEventData>(32); // レース中に発生したイベントを記録したデータ。
        private int _horseNum; // キャラ数。
        private float _distanceDiffMax = 0; // 先頭キャラと最後尾キャラのdistanceの差が最も大きかった時の値。※UI用。
    #if CYG_DEBUG
        public RaceSimulateDebugData DbgData = new RaceSimulateDebugData();
    #endif
        
        //---------------------------------------------------------------
        // シリアライズ不要な変数。
        //---------------------------------------------------------------
        private int _lastFrameIdx = FRAME_START_INDEX;
        private int _lastDistanceIndex = FRAME_START_INDEX;
        private int _lastLaneIndex = FRAME_START_INDEX;
        private float _lastCalcFrameTime = 0.0f;

        public List<RaceSimulateFrameData> FrameDataList => _frameDataList; 
        
    #if UNITY_EDITOR && CYG_DEBUG
        /// <summary> 現在のフレーム数を取得する </summary>
        public int DbgLastFrameIndex => _lastFrameIdx;
    #endif
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        /// <summary>
        /// コンストラクタ。記録用初期化。
        /// </summary>
        public RaceSimulateData(int horseNum)
        {
            _horseNum = horseNum;
            Header = new RaceSimulateDataHeader(RaceSimulateDataHeader.SIMULATE_DATA_VERSION);
        }
#endif

        /// <summary>
        /// コンストラクタ。byte配列から復元。
        /// </summary>
        public RaceSimulateData(byte[] byteArrray)
        {
            Deserialize(byteArrray);
        }
        

        #region 記録
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// レースの記録時間単位ごとの記録。
        /// </summary>
        public void Record(RaceSimulateFrameData frameData)
        {
            _frameDataList.Add(frameData);
        }
            
        /// <summary>
        /// レース結果情報記録。レース終了後に一度だけ。
        /// </summary>
        public void RecordRaceResult(RaceSimulateHorseResultData[] resultDataArray)
        {
            _horseResultDataArray = resultDataArray;
            _distanceDiffMax = RaceManagerSimulate.Instance.DistanceDiffMax;

    #if CYG_DEBUG
            var teamList = new List<RaceSimulateDebugData.TeamData>();
            if (RaceManagerSimulate.Instance != null)
            {
                foreach (var team in RaceManagerSimulate.Instance.GetTeamInfoList())
                {
                    var newTeamData = new RaceSimulateDebugData.TeamData();
                    newTeamData.TeamId = team.TeamId;
                    newTeamData.TeamMemberHorseIndexArray = team.TeamMemberArraySimulate.Select(h => h.HorseIndex).ToArray();
                    newTeamData.ScoreList = team.ScoreList;
                    teamList.Add(newTeamData);
                }
            }
            // レース全体に関するデバッグ情報は、レース終了時に一度だけ記録する。
            DbgData = new RaceSimulateDebugData(teamList);
    #endif
        }
#endif
        #endregion


        #region データ取得：レース中不変データ
        /// <summary>
        /// キャラ数取得。
        /// </summary>
        public int GetHorseDataNum()
        {
            return _horseResultDataArray.Length;
        }
        
        public RaceSimulateHorseResultData[] GetHorseResultDataArray()
        {
            return _horseResultDataArray;
        }
            
        /// <summary>
        /// キャラのレース中不変データ取得。
        /// </summary>
        public RaceSimulateHorseResultData GetHorseResultData(int horseIndex)
        {
            if ( horseIndex < 0 || _horseResultDataArray.Length <= horseIndex )
            {
                return null;
            }

            return _horseResultDataArray[horseIndex];
        }

        public float GetDistanceDiffMax()
        {
            return _distanceDiffMax;
        }
        public float GetStartDelayTime(int horseIndex)
        {
            return _horseResultDataArray[horseIndex].GetStartDelayTime();
        }
        public byte GetGutsOrder( int horseIndex )
        {
            return _horseResultDataArray[horseIndex].GetGutsOrder();
        }
        public byte GetWizOrder( int horseIndex )
        {
            return _horseResultDataArray[horseIndex].GetWizOrder();
        }
        public void GetResult(int horseIndex, out int finishOrder, out float finishTime)
        {
            _horseResultDataArray[horseIndex].GetResult(out finishOrder, out finishTime, out float _, out float _);
        }
        public void GetResult(int horseIndex, out int finishOrder, out float finishTime, out float finishTimeRaw, out float finishDiffTime)
        {
            _horseResultDataArray[horseIndex].GetResult(out finishOrder, out finishTime, out finishTimeRaw, out finishDiffTime);
        }
        public float GetLastSpurtStartDistance( int horseIndex )
        {
            return _horseResultDataArray[horseIndex].GetLastSpurtStartDistance();
        }
        public Gallop.RaceDefine.DefeatType GetDefeat( int horseIndex )
        {
            return _horseResultDataArray[horseIndex].GetDefeat();
        }

        public float GetLastFrameTime()
        {
            return _frameDataList[_frameDataList.Count - 1].Time;
        }
        public float GetLastFrameDistance(int horseIndex)
        {
            return _frameDataList[_frameDataList.Count - 1].HorseDataArray[horseIndex].Distance;
        }
        #endregion


        #region データ取得：フレームデータ
        public RaceSimulateFrameData GetFrameData(float targetTime)
        {
            int index = GetFrameDataIndex(targetTime, FRAME_START_INDEX);
            return _frameDataList[index];
        }
            
        public float GetTimeByDistance(int horseIndex, float targetDistance)
        {
            int num = _frameDataList.Count;
            for (int i = FRAME_START_INDEX; i < num; i++)
            {
                var frameDataPrev = _frameDataList[i - FRAME_NEXT_OFFSET];
                var frameDataCur = _frameDataList[i];
                var horseFrameDataPrev = frameDataPrev.HorseDataArray[horseIndex];
                var horseFrameDataCur = frameDataCur.HorseDataArray[horseIndex];

                if (targetDistance >= horseFrameDataPrev.Distance && targetDistance < horseFrameDataCur.Distance)
                {
                    float timeDiff = frameDataCur.Time - frameDataPrev.Time;
                    float distanceDiff = horseFrameDataCur.Distance - horseFrameDataPrev.Distance;

                    return frameDataPrev.Time + timeDiff * (targetDistance - horseFrameDataPrev.Distance) / distanceDiff;
                }
            }
            return 0.0f;
        }

        /// <summary>
        /// 指定のフレームで一番TopのHorseIndexを返す
        /// </summary>
        /// <param name="frame"></param>
        /// <returns></returns>
        private int GetTopHorseIndexByFrame( int frame )
        {
            var frameDataCur = _frameDataList[frame];

            // このフレームで一番進んでいるキャラを取得。
            int topHorseIndex = 0;
            {
                float curFrameMaxDistance = float.MinValue;
                for (int horseIndex = 0; horseIndex < frameDataCur.HorseDataArray.Length; ++horseIndex)
                {
                    if (frameDataCur.HorseDataArray[horseIndex].Distance > curFrameMaxDistance)
                    {
                        curFrameMaxDistance = frameDataCur.HorseDataArray[horseIndex].Distance;
                        topHorseIndex = horseIndex;
                    }
                }
            }

            return topHorseIndex;
        }

        /// <summary>
        /// 指定距離に誰かが到達した時間を取得。
        /// </summary>
        public float GetFirstReachedTimeByDistance(float targetDistance)
        {
            for (int i = _frameDataList.Count - 1; i >= FRAME_START_INDEX; i--)
            {
                var frameDataPrev = _frameDataList[i - FRAME_NEXT_OFFSET];
                var frameDataCur = _frameDataList[i];

                // このフレームで一番進んでいるキャラを取得。
                int topHorseIndex = GetTopHorseIndexByFrame(i);

                var horseFrameDataPrev = frameDataPrev.HorseDataArray[topHorseIndex];
                var horseFrameDataCur = frameDataCur.HorseDataArray[topHorseIndex];

                // この一番進んでいるキャラがtargetDistanceに達したフレームか、そのフレームなら時間を返却する。
                if (targetDistance >= horseFrameDataPrev.Distance && 
                    targetDistance < horseFrameDataCur.Distance)
                {
                    float timeDiff = frameDataCur.Time - frameDataPrev.Time;
                    float distanceDiff = horseFrameDataCur.Distance - horseFrameDataPrev.Distance;

                    return frameDataPrev.Time + timeDiff * (targetDistance - horseFrameDataPrev.Distance) / distanceDiff;
                }
            }

            return 0.0f;
        }
        
        public float GetDistance(int horseIndex, float targetTime)
        {
            int index = GetFrameDataIndex(targetTime, FRAME_START_INDEX);
            var frameDataPrev = _frameDataList[index - FRAME_NEXT_OFFSET];
            var frameDataCur = _frameDataList[index];
            var horseFrameDataPrev = frameDataPrev.HorseDataArray[horseIndex];
            var horseFrameDataCur = frameDataCur.HorseDataArray[horseIndex];

            float timeDiff = frameDataCur.Time - frameDataPrev.Time;
            float distanceDiff = horseFrameDataCur.Distance - horseFrameDataPrev.Distance;

            return horseFrameDataPrev.Distance + distanceDiff * (targetTime - frameDataPrev.Time) / timeDiff;
        }

        /// <summary>
        /// 指定の時間の時に一番先頭にいるキャラの進んでいる距離を返す
        /// </summary>
        public float GetFirstReachedDistance(float targetTime)
        {
            for (int i = _frameDataList.Count - 1; i >= FRAME_START_INDEX; i--)
            {
                var frameDataPrev = _frameDataList[i - FRAME_NEXT_OFFSET];
                var frameDataCur = _frameDataList[i];

                // このフレームで一番進んでいるキャラを取得。
                int topHorseIndex = GetTopHorseIndexByFrame(i);

                // この一番進んでいるキャラがtargetTimeに達したフレームか、そのフレームなら距離を返却する。
                if (targetTime >= frameDataPrev.Time &&
                    targetTime < frameDataCur.Time)
                {
                    return GetDistance(topHorseIndex, targetTime);
                }
            }

            return 0.0f;
        }
        #endregion


        #region データ取得：キャッシュ
        /// <summary>
        /// 指定時間で取得すべきRaceSimulateFrameDataのインデックスを確定させる
        /// </summary>
        /// <remarks>
        /// 各フレームこの関数でインデックスを確定させた後、GetCached***で値を取得できます。
        /// </remarks>
        public void CalcFrameDataCache(float targetTime)
        {
            //時間が巻き戻っているので先頭から再度調べる
            if (_lastCalcFrameTime > targetTime)
            {
                _lastFrameIdx = FRAME_START_INDEX;
                _lastLaneIndex = FRAME_START_INDEX;
                _lastDistanceIndex = FRAME_START_INDEX;
            }

            _lastFrameIdx = GetFrameDataIndex(targetTime, _lastFrameIdx);
            _lastLaneIndex = _lastFrameIdx;
            _lastDistanceIndex = _lastFrameIdx;

            _lastCalcFrameTime = targetTime;
        }
        
#if UNITY_EDITOR && CYG_DEBUG
        /// <summary>
        /// Xフレーム前のRaceSimulateFrameDataのインデックスを設定する
        /// </summary>
        public float CalcBeforeFrameDataCache(int frameNum)
        {
            const float TIME_OFFSET = 0.01f;    // UIの小数切り捨て（小数点以下第二位まで）に巻き込まれないよう、設定時間にオフセットを設ける
            
            // 最小値は下回らないように
            var beforeIdx = System.Math.Max(FRAME_START_INDEX, _lastFrameIdx - frameNum) - 1;    // なぜかIndexなのに1開始になっているから-1する
            _lastCalcFrameTime = _frameDataList[beforeIdx].Time + TIME_OFFSET;
            return _lastCalcFrameTime;
        }
        
        /// <summary>
        /// Xフレーム後のRaceSimulateFrameDataのインデックスを設定する
        /// </summary>
        public float CalcNextFrameDataCache(int frameNum)
        {
            const float TIME_OFFSET = 0.01f;    // UIの小数切り捨て（小数点以下第二位まで）に巻き込まれないよう、設定時間にオフセットを設ける
            const int NextItemOffset = 1;
            
            // 最大値は超えないように
            var nextIdx = System.Math.Min(_frameDataList.Count - NextItemOffset, _lastFrameIdx + frameNum) - 1;      // なぜかIndexなのに1開始になっているから-1する
            _lastCalcFrameTime = _frameDataList[nextIdx].Time + TIME_OFFSET;
            return _lastCalcFrameTime;
        }
#endif

        /// <summary>
        /// CalcFrameDataCacheで確定されたフレームのRaceSimulateHorseFrameDataを取得。
        /// </summary>
        public RaceSimulateHorseFrameData GetHorseFrameDataFromCache(int horseIndex)
        {
            return _frameDataList[_lastFrameIdx - FRAME_NEXT_OFFSET].HorseDataArray[horseIndex];
        }
            
        /// <summary>
        /// CalcFrameDataCacheで確定されたフレームの距離を取得。
        /// </summary>
        public float GetDistanceFormCache(int horseIndex)
        {
            var frameDataPrev = _frameDataList[_lastDistanceIndex - FRAME_NEXT_OFFSET];
            var frameDataCur = _frameDataList[_lastDistanceIndex];
            var horseFrameDataPrev = frameDataPrev.HorseDataArray[horseIndex];
            var horseFrameDataCur = frameDataCur.HorseDataArray[horseIndex];
        
            float timeDiff = frameDataCur.Time - frameDataPrev.Time;
            float distanceDiff = horseFrameDataCur.Distance - horseFrameDataPrev.Distance;

            return horseFrameDataPrev.Distance + distanceDiff * (_lastCalcFrameTime - frameDataPrev.Time) / timeDiff;
        }
            
        /// <summary>
        /// CalcFrameDataCacheで確定されたフレームのレーンを取得。
        /// </summary>
        public float GetLanePositionFromCache(int horseIndex)
        {
            // 最初からの検索を避けるためのインデックス保持
            var frameDataPrev = _frameDataList[_lastLaneIndex - FRAME_NEXT_OFFSET];
            var frameDataCur = _frameDataList[_lastLaneIndex];
            var horseFrameDataPrev = frameDataPrev.HorseDataArray[horseIndex];
            var horseFrameDataCur = frameDataCur.HorseDataArray[horseIndex];
        
            float timeDiff = frameDataCur.Time - frameDataPrev.Time;
            float lanePositionDiff = horseFrameDataCur.LanePosition - horseFrameDataPrev.LanePosition;

            return horseFrameDataPrev.LanePosition + lanePositionDiff * (_lastCalcFrameTime - frameDataPrev.Time) / timeDiff;
        }

        private int GetFrameDataIndex(float targetTime, int startIndex)
        {
            const int NextItemOffset = 1;
            int num = _frameDataList.Count;
            for (int i = startIndex; i < num;)
            {
                if (i <= 0) break;

                var frameDataPrev = _frameDataList[i - NextItemOffset];
                var frameDataCur = _frameDataList[i];

                if (targetTime >= frameDataPrev.Time && targetTime < frameDataCur.Time)
                {
                    //直前のインデックスを返す
                    return i;
                }

                if (targetTime >= frameDataPrev.Time)
                {
                    i++;
                }
                else
                {
                    i--;
                }
            }

            // 最後まで見つからなかったら末尾のフレームを返す。
            if (targetTime > 0.0f && _frameDataList.Count > 0)
            {
                return _frameDataList.Count - NextItemOffset;
            }

#if CYG_DEBUG
            Debug.LogError("存在しない時間のFrameDataを取得しようとしています");
#endif

            return 0;
        }

        public void ResetDistanceIndex()
        {
            _lastCalcFrameTime = 0.0f;

            _lastFrameIdx = FRAME_START_INDEX;
            _lastLaneIndex = FRAME_START_INDEX;
            _lastDistanceIndex = FRAME_START_INDEX;
        }
        #endregion
            
            
        #region イベント
        public void CopyEventData( ref List<RaceSimulateEventData> copyTo )
        {
            copyTo = new List<RaceSimulateEventData>( _simEvDataList );
        }

        /// <summary>
        /// イベントリスト取得。※原本なので取得先で加工しないように。
        /// </summary>
        public List<RaceSimulateEventData> GetEventOriginal()
        {
            return _simEvDataList;
        }

        /// <summary>
        /// イベント登録。
        /// </summary>
        public void AddEvent(float curTime, SimulateEventType type, params int[] _params)
        {
            RaceSimulateEventData simEvent = new RaceSimulateEventData();
            simEvent.frameTime = curTime;
            simEvent.type = type;
            simEvent.param = new int[_params.Length];
            Array.Copy(_params, simEvent.param, _params.Length);
            _simEvDataList.Add(simEvent);
        }
        
    #if CYG_DEBUG
        /// <summary>
        /// イベントリスト取得。
        /// </summary>
        public List<RaceSimulateEventData> GetEventList( SimulateEventType type, int horseIdx )
        {
            return _simEvDataList.Where( item => ( item.type == type && item.param[0] == horseIdx ) ).ToList();
        }
    #endif
        #endregion
    }
}

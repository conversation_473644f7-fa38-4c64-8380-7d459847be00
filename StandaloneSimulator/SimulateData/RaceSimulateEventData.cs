using System;
using System.Linq;

namespace StandaloneSimulator
{
    public enum SimulateEventType : byte
    {
        Score,                  // 競技場レーススコア獲得イベント。
        ChallengeMatchPoint,    // チャレンジマッチポイント獲得イベント。
        NOUSE_2,                // 未使用。
        Skill,                  // スキル発動イベント。
        CompeteTop,             // 競り合い（ハナの奪い合い）イベント。
        CompeteFight,           // 競り合い（叩き合い）イベント。
        ReleaseConservePower,   // 足溜め解放
        StaminaLimitBreakBuff,  // 上限突破パラメータスタミナ追加効果
        CompeteBeforeSpurt,     // スパート前位置取り勝負
        StaminaKeep,            // 持久力温存状態
        SecureLead,             // リード確保
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// レースシミュレーションデータ：レース中に発生したイベント（スキル発動など）。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class RaceSimulateEventData
    {
        public float frameTime;
        public SimulateEventType type;
        public int[] param;

        public byte[] IntArray2Bytes(int[] array)
        {
            int typeSize = sizeof(int);
            byte[] retBytes = new byte[typeSize * array.Length];
            for (int i = 0; i < array.Length; ++i)
            {
                Array.Copy(BitConverter.GetBytes(array[i]), 0, retBytes, typeSize * i, typeSize);
            }
            return retBytes;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// RaceSimulateEventDataを文字列に変換。
        /// </summary>
        //-------------------------------------------------------------------
        public void ToStr(System.Text.StringBuilder buf)
        {
            buf.Append(frameTime + "," + type + ",");
            for (int i = 0; i < param.Length; ++i)
            {
                buf.Append(param[i] + ",");
            }
            buf.Append("\n");
        }

    }

}

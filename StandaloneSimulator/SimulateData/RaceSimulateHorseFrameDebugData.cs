#if CYG_DEBUG
using System;
using System.Linq;
using System.Collections.Generic;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateHorseFrameDataが所持するデバッグ情報。
    /// </summary>
    //-----------------------------------------------------------------------
    public partial class RaceSimulateHorseFrameDebugData
    {
        //-------------------------------------------------------------------
        // 定義。
        //-------------------------------------------------------------------
        public class SkillStatus
        {
            public int SkillId;
            public float CoolDownTime;
            public float[] SkillDetailCoolDownTimeArray;
            public bool IsContainsUseSkillDetailCoolDownTime;    // スキル効果毎にクールダウンを持つスキルかどうか
        }
        
        public class CompeteFightNear
        {
            public readonly int HorseIndex;
            public readonly float ContinueTime;

            public CompeteFightNear(int horseIndex, float continueTime)
            {
                HorseIndex = horseIndex;
                ContinueTime = continueTime;
            }
        }

        public const int VERSION_10000000 = 10000000;
        public const int VERSION_10000001 = 10000001;
        public const int VERSION_10000002 = 10000002;
        public const int VERSION_10000003 = 10000003;
        public const int VERSION_10000004 = 10000004;
        public const int VERSION_10000005 = 10000005;
        public const int VERSION_10000006 = 10000006;
        public const int VERSION_10000007 = 10000007;
        public const int VERSION_10000008 = 10000008;
        public const int VERSION_10000009 = 10000009;
        public const int VERSION_10000010 = 10000010;
        public const int VERSION_10000011 = 10000011;
        public const int VERSION_10000012 = 10000012;
        public const int VERSION_10000013 = 10000013;
        public const int VERSION_10000014 = 10000014;
        public const int CUR_VERSION = VERSION_10000014;
        public const int BLOCK_HORSEINDEX_NULL = -1;

        //-------------------------------------------------------------------
        // 変数。
        //-------------------------------------------------------------------
        public int Version;
        
        public RaceHorseSkillData[] DbgCurrentActiveSkillArray; // 発動中のスキル。
        public ActivatedSkillFrameInfo[] DbgActivatedSkillArray; // 発動したスキル。
        public Dictionary<SkillDefine.SkillModifierParam, List<ISkillParamModifier>> DbgSkillModifierDic = new Dictionary<SkillDefine.SkillModifierParam, List<ISkillParamModifier>>();
        public Dictionary<SkillDefine.SkillModifierParam, float> DbgSkillValueParamTotal = new Dictionary<SkillDefine.SkillModifierParam, float>(); // スキルのパラメータ変動値合計。(実際にキャラに適用されている値)
        public Dictionary<Gallop.RaceDefine.HorsePhase, int> DbgActivateSkillCount; // Phaseごとのスキル発動回数。
        /// <summary> スキル効果値変動管理クラス </summary>
        public SkillParamFluctuate DbgSkillParamFluctuate = new SkillParamFluctuate();
        public float DbgSpeed; // （やる気、1200以上の圧縮、スキル効果の効果を含めた）スピード値。
        public float DbgStamina; // （やる気、1200以上の圧縮、スキル効果の効果を含めた）スタミナ値。
        public float DbgPower; // （やる気、1200以上の圧縮、スキル効果の効果を含めた）パワー値。
        public float DbgGuts; // （やる気、1200以上の圧縮、スキル効果の効果を含めた）スピード値。
        public float DbgWiz; // （やる気、1200以上の圧縮、スキル効果の効果を含めた）スピード値。
        public float DbgBlockedFrontContinueTime; // 前方ブロックされている継続時間。
        public float DbgBlockedFrontHpEmptyContinueTime; // 前方をHp0にブロックされている継続時間。
        public float DbgBlockedSideContinueTime; // サイドブロックされている継続時間。
        public byte DbgCorner; // 通過中のコーナー。
        public byte DbgCurOrder; // 現在順位。
        public byte DbgCurOrderUpCountPhaseMiddle;              // 順位上昇回数：中盤。
        public byte DbgCurOrderUpCountPhaseEndAfter;            // 順位上昇回数：終盤以降。
        public byte DbgCurOrderUpCountCorner;                   // 順位上昇回数：コーナー中。 
        public byte DbgCurOrderUpCountLastSpurt;                // 順位上昇回数：ラストスパート中。
        public byte DbgCurOrderUpCountDistance1;                // 順位上昇回数：レース開始後一定距離以降
        public byte DbgCurOrderUpCountFinalCornerAfter;         // 順位上昇回数：最終コーナー以降。
        public byte DbgCurOrderUpCountLaterHalf;                // 順位上昇回数：後半50%以降。
        public byte DbgCurOrderUpCountCornerPhaseEndAfter;
        public byte DbgCurOrderDownCountPhaseMiddle;            // 順位下降回数：中盤。
        public byte DbgCurOrderDownCountPhaseEndAfter;          // 順位下降回数：終盤以降。
        public byte DbgCurOrderDownCountCorner;                 // 順位下降回数：コーナー中。
        public byte DbgCurOrderDownCountLastSpurt;              // 順位下降回数：ラストスパート中。
        public byte DbgCurOrderDownCountDistance1;              // 順位下降回数：レース開始後一定距離以降
        public byte DbgCurOrderDownCountFinalCornerAfter;       // 順位下降回数：最終コーナー以降。
        public byte DbgCurOrderDownCountLaterHalf;              // 順位下降回数：後半50%以降。
        public byte DbgCurOrderDownCountCornerPhaseEndAfter;
        public byte[] DbgVisibleHorseIndexs; // 視野範囲に入っているキャラ。
        public byte[] DbgNearHorseIndexs; // 近くのキャラ。
        public float DbgNearHorseAroundTime; // 近くのキャラが一定数(4)以上継続して存在する時間。
        
        public byte DbgIsLastSpurt; // ラストスパートの有無。
        public byte DbgIsFinalCorner; // 最終コーナー。
        public byte DbgIsTooFast; // 速すぎる。
        public byte DbgIsTooSlow; // 遅すぎる。
        public byte DbgIsReceivedDebuff; // デバフを受けた。
        public byte DbgLaneType; // LaneType。
        public byte DbgIsBehindIsIn; // 順位１後のキャラが自分よりインにいる。
        public byte DbgIsBehindIsOut; // 順位１後のキャラが自分よりアウトにいる。
        public byte DbgNearFrontCount; // 近くのキャラで自分より前。
        public byte DbgNearBehindCount; // 近くのキャラで自分より後ろ。

        public byte DbgIsStraight;
        public byte DbgIsStraightFirst;
        public byte DbgIsStraightLast;
        public byte DbgStraightFrontType;
        public byte DbgStraightTypeNumber;

        public sbyte DbgBlockInHorseIdx;
        public sbyte DbgBlockOutHorseIdx;

        public float DbgSlopeAddTargetSpeed;
        public float DbgSlopePer;
        public float DbgSkillAddTargetSpeed;
        public float DbgForceInMoveAddTargetSpeed;
        public float DbgPositionKeepBaseTargetSpeedMultiply;

        public byte DbgIsTemptationStartEnable = 0;
        public List<ScoreData> DbgScoreList = new List<ScoreData>();
        
        public float DbgLaneMoveSpeed; // レーン移動速度。

        public float DbgTargetSpeed; // 狙いたい速度。 
        public float DbgTargetLane; // 狙いたいレーン。
        public float DbgBaseTargetSpeed;
        public int DbgHorsePhase;
        public bool DbgIsCornerLast;
        public int DbgPositionKeepMode; 
        public float DbgLastSpurtTargetSpeed;
        /// <summary> スタートダッシュ終了判定で使用する現在速度 </summary>
        public float DbgLastSpeedForStartDashFinishCheck;
        public float DbgCurDistanceDiffFromPaseMaker;  
        public float DbgDistanceDiffMin;
        public float DbgDistanceDiffMax;
        public float DbgAdjustTargetDistanceDiff;
        public float DbgVisibleDistance;
        public float DbgInfrontHorseNearContinueTime;
        public float DbgBehindHorseNearContinueTime;
        public float DbgInfrontHorseNearLaneContinueTime;
        public float DbgBehindHorseNearLaneContinueTime;
        public float DbgTemptationPerRandom;
        public float DbgTemptationPerWiz;
        public int DbgTemptationStartSection;
        public bool DbgIsInMoveEnable;
        public bool DbgIsOutMoveEnable;
        public float DbgInLaneSpace;
        public float DbgOutLaneSpace;
        public int[] DbgOverTakeHorseIndexs;
        public float DbgOverTakeLane;
        public float DbgOverTakeCoolDownTime;

        public float DbgAccelPerSec;
        public float DbgDecHpPerSec;
        public float DbgAddHp;
        
        public float DbgOvertakeTargetContinueTime;
        public float DbgOverTakeTargetHaveNoOrderUpContinueTime;
        public float DbgOverTakeTargetHaveNoOrderDownContinueTime;
        public int DbgActivateHealSkillCount;
        public int DbgActivateSpecificTagGroupSkillCount;
        public int DbgActivateSpecificSkillAbilityTypeGroupSkillCount;
        public SkillStatus[] DbgCurrentSkillStatusArray;
        public int DbgFinalCornerEndOrder;
        public float DbgLaneDistanceRateInAllHorses;
        public bool[] DbgIsOrderInContinue;

        public float DbgHpPer;
        
        public bool DbgIsCompeteTop;
        public int DbgCompeteTopCount;
        public int DbgCompeteTopGroup;
        public float DbgCompeteTopRemainTime;
        public float DbgCompeteTopAddTargetSpeed;

        public bool DbgIsCompeteFight;
        public int DbgCompeteFightCount;
        public int DbgCompeteFightGroup;
        public float DbgCompeteFightAddTargetSpeed;
        public float DbgCompeteFightAddAccel;
        public List<CompeteFightNear> DbgCompeteFightNearList = new List<CompeteFightNear>();

        public bool DbgIsSurrounded;
        public sbyte DbgPaseMakerHorseIndex;
        public int DbgTopHorseNotMostForwardRunningStyleCnt;
        public float DbgPositionKeepCoolDownTime;

        public float[] DbgDistanceDiffOnPositionKeepCalcArray;

        public int[] DbgOverTakenHorseIndexes;
        
    #region <パラメータ上限突破追加効果>
        #region <パラメータ上限突破パワー>
        /// <summary> 足溜めポイントによる加速度加算値 </summary>
        public float DbgConservePowerAddAccel;
        /// <summary> 足溜めポイントによる加速度加算の残り時間 </summary>
        public float DbgConservePowerAddAccelRemainTime;
        /// <summary> 足溜めポイント </summary>
        public float DbgConservePower;
        /// <summary> 足溜めポイント増加クールタイム </summary>
        public float DbgConservePowerIncreaseCoolTime;
        /// <summary> 足溜めポイント合計増加量リスト(key:増加モード, value:発動時間リスト) </summary>
        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDict =
            new Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>();
        /// <summary> 足溜めポイント合計減少量リスト(key:減少モード, value:発動時間リスト) </summary>
        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDict =
            new Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>();
        /// <summary> レコードごとの足溜めポイント合計増加量リスト(key:増加モード, value:発動時間リスト) </summary>
        public Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>> DbgConservePowerIncreaseTimeDictPerRecord =
            new Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>();
        /// <summary> レコードごとの足溜めポイント合計減少量リスト(key:減少モード, value:発動時間リスト) </summary>
        public Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>> DbgConservePowerDecreaseTimeDictPerRecord =
            new Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>();
        #endregion // パラメータ上限突破パワー
    #endregion // パラメータ上限突破追加効果
    
        #region <スパート前位置取り勝負>
        public float DbgCompeteBeforeSpurtRemainActiveSec;
        public float DbgCompeteBeforeSpurtRemainCoolDownSec;
        public float DbgCompeteBeforeSpurtRemainCheckIntervalSec;
        public float DbgCompeteBeforeSpurtAddSpeedValue;
        public float DbgCompeteBeforeSpurtConsumeStaminaValue;
        public int DbgCompeteBeforeSpurtActivateCount;
        public int DbgCompeteBeforeSpurtConditionNearCharaNum;
        public int DbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle;
        public bool DbgCompeteBeforeSpurtIsSpeedUpMinorBonus;
        public int DbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum;
        public int DbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum;

        public bool DbgIsStaminaKeepState;
        public float DbgStaminaKeepCheckIntervalSec;
        public float DbgStaminaKeepNeedHp;
        public float DbgStaminaKeepNeedHpForSpurt;
        public float DbgStaminaKeepNeedHpForMiddle;
        public float DbgStaminaKeepNeedHpWizRand;
        #endregion
        
        #region <リード確保>
        public float DbgSecureLeadRemainActiveSec;
        public float DbgSecureLeadRemainCoolDownSec;
        public float DbgSecureLeadRemainCheckIntervalSec;
        public float DbgSecureLeadAddSpeedValue;
        public float DbgSecureLeadConsumeStaminaValue;
        public int DbgSecureLeadActivateCount;
        public bool DbgSecureLeadIsSpeedUpMinorBonus;
        public int DbgSecureLeadSpeedUpMinorBonusNearCharaNum;
        public int DbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum;
        #endregion
        
        //-------------------------------------------------------------------
        // 関数。
        //-------------------------------------------------------------------
        public RaceSimulateHorseFrameDebugData()
        {
        }

        public RaceSimulateHorseFrameDebugData(
            Gallop.RaceDefine.HorsePhase dbgHorsePhase, 
            float dbgSpeed, float dbgStamina, float dbgPower, float dbgGuts, float dbgWiz,
            int dbgBlockInHorseIdx, int dbgBlockOutHorseIdx,
            float dbgSlopeAddTargetSpeed, float dbgSlopePer,
            float dbgBaseTargetSpeed, float dbgSkillAddTargetSpeed, float dbgForceInMoveAddTargetSpeed, float dbgPositionKeepBaseTargetSpeedMultiply,
            bool dbgIsTemptationStartEnable, int dbgTemptationStartSection, float dbgTemptationPerRandom, float dbgTemptationPerWiz,
            float dbgInfrontHorseNearContinueTime, float dbgBehindHorseNearContinueTime, float dbgInfrontHorseNearLaneContinueTime, float dbgBehindHorseNearLaneContinueTime,
            bool dbgIsInMoveEnable, bool dbgIsOutMoveEnable,
            float dbgInLaneSpace, float dbgOutLaneSpace,
            Gallop.RaceDefine.PositionKeepMode dbgPositionKeepMode,
            float dbgTargetSpeed,
            RaceHorseSkillData[] currentActiveSkills,
            ActivatedSkillFrameInfo[] dbgActivatedSkillArray,
            Dictionary<SkillDefine.SkillModifierParam, List<ISkillParamModifier>> dbgGetModifierDict,
            Dictionary<SkillDefine.SkillModifierParam, List<ISkillParamModifier>> dbgGetCancelledModifierDict,
            Dictionary<Gallop.RaceDefine.HorsePhase,int> dbgGetActivateSkillCount,
            float dbgBlockedFrontContinueTime, float dbgBlockedFrontHpEmptyContinueTime, float dbgBlockedSideContinueTime,
            float dbgLaneMoveSpeed,  float dbgTargetLane, 
            int[] dbgOverTakeHorseIndexs, float dbgOverTakeLane, float dbgOverTakeCoolDownTime, 
            int dbgCorner, bool dbgIsCornerLast, int dbgCurOrder, 
            int dbgCurOrderUpCountPhaseMiddle, int dbgCurOrderUpCountPhaseEndAfter,
            int dbgCurOrderUpCountCorner, int dbgCurOrderUpCountLastSpurt, int dbgCurOrderUpCountDistance1,
            int dbgCurOrderUpCountCornerPhaseEndAfter, int dbgCurOrderUpCountFinalCornerAfter, int dbgCurOrderUpCountLaterHalf,
            int dbgCurOrderDownCountPhaseMiddle, int dbgCurOrderDownCountPhaseEndAfter, 
            int dbgCurOrderDownCountCorner, int dbgCurOrderDownCountLastSpurt, int dbgCurOrderDownCountDistance1,
            int dbgCurOrderDownCountCornerPhaseEndAfter, int dbgCurOrderDownCountFinalCornerAfter, int dbgCurOrderDownCountLaterHalf,
            bool dbgIsStraight, bool dbgIsStraightFirst, bool dbgIsStraightLast,
            Gallop.RaceDefine.StraightFrontType dbgStraightFrontType, int dbgStraightTypeNumber,
            byte[] dbgVisibleHorseIndexs, 
            byte[] dbgNearHorseIndexs, float dbgNearHorseAroundTime,
            float dbgLastSpurtTargetSpeed, 
            float dbgCurDistanceDiffFromPaseMaker,
            float dbgDistanceDiffMin, float dbgDistanceDiffMax, float dbgAdjustTargetDistanceDiff, float dbgPositionKeepCoolDownTime,
            float[] dbgDistanceDiffOnPositionKeepCalcArray,
            float dbgVisibleDistance, 
            List<ScoreData> dbgScoreList, 
            float dbgAccelPerSec, float dbgDecHpPerSec, float dbgAddHp,
            float dbgOvertakeTargetContinueTime, float dbgOverTakeTargetHaveNoOrderUpContinueTime, float dbgOverTakeTargetHaveNoOrderDownContinueTime,
            int[] dbgOverTakenHorseIndexes, int dbgActivateHealSkillCount, int dbgActivateSpecificTagGroupSkillCount, int dbgActivateSpecificSkillAbilityTypeGroupSkillCount,
            SkillStatus[] skillArray, 
            int dbgFinalCornerEndOrder, float dbgLaneDistanceRateInAllHorses, bool[] dbgIsOrderInContinue, 
            float dbgHpPer, bool dbgIsCompeteFight, int dbgCompeteFightCount,
            int dbgCompeteFightGroup, float dbgCompeteFightAddTargetSpeed, float dbgCompeteFightAddAccel,
            List<CompeteFightNear> competeFightNearList,
            bool dbgIsCompeteTop,int dbgCompeteTopCount, int dbgCompeteTopGroup, float dbgCompeteTopRemainTime, float dbgCompeteTopAddTargetSpeed,
            bool dbgIsSurrounded, int dbgPaseMakerHorseIndex,
            int dbgTopHorseNotMostForwardRunningStyleCnt, float dbgConservePowerIncreaseCoolTime,
            float dbgConservePowerAddAccel, float dbgConservePowerAddAccelRemainTime, float dbgConservePower,  
            Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode,List<float>> dbgConservePowerIncreaseTimeDict,
            Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode,List<float>> dbgConservePowerDecreaseTimeDict,
            Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode,List<float>> dbgConservePowerIncreaseTimeDictPerRecord,
            Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode,List<float>> dbgConservePowerDecreaseTimeDictPerRecord,
            float dbgCompeteBeforeSpurtRemainActiveSec,
            float dbgCompeteBeforeSpurtRemainCoolDownSec, float dbgCompeteBeforeSpurtRemainCheckIntervalSec,
            float dbgCompeteBeforeSpurtAddSpeedValue, float dbgCompeteBeforeSpurtConsumeStaminaValue,
            int dbgCompeteBeforeSpurtActivateCount, int dbgCompeteBeforeSpurtConditionNearCharaNum,
            int dbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle,
            bool dbgCompeteBeforeSpurtIsSpeedUpMinorBonus, int dbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum,
            int dbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum, 
            bool dbgIsStaminaKeepState,
            float dbgStaminaKeepCheckIntervalSec, float dbgStaminaKeepNeedHp, float dbgStaminaKeepNeedHpForSpurt,
            float dbgStaminaKeepNeedHpForMiddle, float dbgStaminaKeepNeedHpWizRand, 
            float dbgSecureLeadRemainActiveSec,
            float dbgSecureLeadRemainCoolDownSec, float dbgSecureLeadRemainCheckIntervalSec,
            float dbgSecureLeadAddSpeedValue, float dbgSecureLeadConsumeStaminaValue, int dbgSecureLeadActivateCount,
            bool dbgSecureLeadIsSpeedUpMinorBonus, int dbgSecureLeadSpeedUpMinorBonusNearCharaNum,
            int dbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum,
            Array cachedSkillModifierParamValueArray,
            float dbgDebuffAbilityValueMultiplyRatio, float dbgDebuffAbilityValueMultiplyOtherActivateRatio,
            float dbgLastSpeedForStartDashFinishCheck
            )
            {
            Version = CUR_VERSION;
            
            DbgHorsePhase = (int)dbgHorsePhase;
            DbgSpeed = dbgSpeed;
            DbgStamina = dbgStamina;
            DbgPower = dbgPower;
            DbgGuts = dbgGuts;
            DbgWiz = dbgWiz;
            
            DbgBlockInHorseIdx = (sbyte)dbgBlockInHorseIdx;
            DbgBlockOutHorseIdx = (sbyte)dbgBlockOutHorseIdx;
            
            DbgSlopeAddTargetSpeed = dbgSlopeAddTargetSpeed;
            DbgSlopePer = dbgSlopePer;
            
            DbgBaseTargetSpeed = dbgBaseTargetSpeed;
            DbgSkillAddTargetSpeed = dbgSkillAddTargetSpeed;
            DbgForceInMoveAddTargetSpeed = dbgForceInMoveAddTargetSpeed;
            DbgPositionKeepBaseTargetSpeedMultiply = dbgPositionKeepBaseTargetSpeedMultiply;

            DbgIsTemptationStartEnable = (byte)(dbgIsTemptationStartEnable ? 1 : 0);
            DbgTemptationStartSection = dbgTemptationStartSection;
            DbgTemptationPerRandom = dbgTemptationPerRandom;
            DbgTemptationPerWiz = dbgTemptationPerWiz;
            
            DbgInfrontHorseNearContinueTime = dbgInfrontHorseNearContinueTime;
            DbgBehindHorseNearContinueTime = dbgBehindHorseNearContinueTime;
            DbgInfrontHorseNearLaneContinueTime = dbgInfrontHorseNearLaneContinueTime;
            DbgBehindHorseNearLaneContinueTime = dbgBehindHorseNearLaneContinueTime;

            DbgIsInMoveEnable = dbgIsInMoveEnable;
            DbgIsOutMoveEnable = dbgIsOutMoveEnable;
            
            DbgInLaneSpace = dbgInLaneSpace;
            DbgOutLaneSpace = dbgOutLaneSpace;
            
            DbgPositionKeepMode = (int)dbgPositionKeepMode;
            
            DbgTargetSpeed = dbgTargetSpeed;
            
            // 現在発動中のスキル。
            DbgCurrentActiveSkillArray = currentActiveSkills;
            
            // 一度でも発動したアクティブスキル。
            DbgActivatedSkillArray = dbgActivatedSkillArray;

            // デバフスキル効果値乗算割合
            DbgSkillParamFluctuate.UpdateFluctuateValues(dbgDebuffAbilityValueMultiplyRatio, dbgDebuffAbilityValueMultiplyOtherActivateRatio);
            {
                foreach (SkillDefine.SkillModifierParam type in cachedSkillModifierParamValueArray)
                {
                    var modifiers = dbgGetModifierDict[type];
                    var modifiersCount = modifiers?.Count ?? 0;

                    if (modifiersCount > 0)
                    {
                        var dbgModifiers = new List<ISkillParamModifier>(modifiersCount);
                        // DbgSkillParamFluctuate の代入で元データに影響を及ぼしてしまうのでディープコピーしておく
                        for (int i = 0; i < modifiersCount; i++)
                        {
                            dbgModifiers.Add(modifiers[i].DeepCopy());
                        }
                        // このtypeで受けているmodifierを保存。
                        DbgSkillModifierDic.Add(type, dbgModifiers);
                        // このフレームでの状態が記録されているSkillParamFluctuateを設定しなおす
                        foreach (var modifier in DbgSkillModifierDic[type])
                        {
                            modifier.SetSkillParamFluctuate(DbgSkillParamFluctuate);
                        }

                        // このtypeで受けている効果量の合計値を保存。
                        float value = 0f;
                        foreach (var modifier in modifiers)
                        {
                            // 乗算の場合は元の値が0fのままだと反映されなくなってしまうので元値を1fにする
                            if (modifier is SkillParamModifierMultiply && RaceUtilMath.Approximately(value, 0f))
                            {
                                value = 1f;
                            }
                            value = modifier.Apply(value);
                        }
                        DbgSkillValueParamTotal.Add(type, value);
                    }
                    
                    // スキル効果発動したけど無効化されたものに関してはここで別途格納(実際には効果値は反映されない)
                    var cancelledModifiers = dbgGetCancelledModifierDict[type];
                    if (cancelledModifiers != null && cancelledModifiers.Count > 0)
                    {
                        if (DbgSkillModifierDic.ContainsKey(type))
                        {
                            DbgSkillModifierDic[type].AddRange(cancelledModifiers);
                        }
                        else
                        {
                            DbgSkillModifierDic.Add(type, new List<ISkillParamModifier>(cancelledModifiers));
                        }
                    }
                }

                var activateSkillCount = dbgGetActivateSkillCount;
                activateSkillCount = new Dictionary<Gallop.RaceDefine.HorsePhase, int>(activateSkillCount.Count);
                foreach (var activateByPhase in activateSkillCount)
                {
                    activateSkillCount.Add(activateByPhase.Key, activateByPhase.Value);
                }
            }
            
            DbgBlockedFrontContinueTime = dbgBlockedFrontContinueTime;
            DbgBlockedFrontHpEmptyContinueTime = dbgBlockedFrontHpEmptyContinueTime;
            DbgBlockedSideContinueTime = dbgBlockedSideContinueTime;
            
            DbgLaneMoveSpeed = dbgLaneMoveSpeed;
            DbgTargetLane = dbgTargetLane;
            
            DbgOverTakeHorseIndexs = dbgOverTakeHorseIndexs;
            DbgOverTakeLane = dbgOverTakeLane;
            DbgOverTakeCoolDownTime = dbgOverTakeCoolDownTime;
            
            DbgCorner = (byte)dbgCorner;
            DbgIsCornerLast = dbgIsCornerLast;
            DbgCurOrder = (byte)dbgCurOrder;
            
            DbgCurOrderUpCountPhaseMiddle = (byte)dbgCurOrderUpCountPhaseMiddle;
            DbgCurOrderUpCountPhaseEndAfter = (byte)dbgCurOrderUpCountPhaseEndAfter;
            DbgCurOrderUpCountCorner = (byte)dbgCurOrderUpCountCorner;
            DbgCurOrderUpCountLastSpurt = (byte)dbgCurOrderUpCountLastSpurt;
            DbgCurOrderUpCountDistance1 = (byte)dbgCurOrderUpCountDistance1;
            DbgCurOrderUpCountCornerPhaseEndAfter = (byte)dbgCurOrderUpCountCornerPhaseEndAfter;
            DbgCurOrderUpCountFinalCornerAfter = (byte)dbgCurOrderUpCountFinalCornerAfter;
            DbgCurOrderUpCountLaterHalf = (byte)dbgCurOrderUpCountLaterHalf;
            
            DbgCurOrderDownCountPhaseMiddle = (byte)dbgCurOrderDownCountPhaseMiddle;
            DbgCurOrderDownCountPhaseEndAfter = (byte)dbgCurOrderDownCountPhaseEndAfter;
            DbgCurOrderDownCountCorner = (byte)dbgCurOrderDownCountCorner;
            DbgCurOrderDownCountLastSpurt = (byte)dbgCurOrderDownCountLastSpurt;
            DbgCurOrderDownCountDistance1 = (byte)dbgCurOrderDownCountDistance1;
            DbgCurOrderDownCountCornerPhaseEndAfter = (byte)dbgCurOrderDownCountCornerPhaseEndAfter;
            DbgCurOrderDownCountFinalCornerAfter = (byte)dbgCurOrderDownCountFinalCornerAfter;
            DbgCurOrderDownCountLaterHalf = (byte)dbgCurOrderDownCountLaterHalf;

            DbgIsStraight = (byte)(dbgIsStraight ? 1 : 0);
            DbgIsStraightFirst = (byte)(dbgIsStraightFirst ? 1 : 0);
            DbgIsStraightLast = (byte)(dbgIsStraightLast ? 1 : 0);
            DbgStraightFrontType = (byte)dbgStraightFrontType;
            DbgStraightTypeNumber = (byte)dbgStraightTypeNumber;
            
            DbgVisibleHorseIndexs = dbgVisibleHorseIndexs;
            
            DbgNearHorseIndexs = dbgNearHorseIndexs;
            DbgNearHorseAroundTime = dbgNearHorseAroundTime;
            
            DbgLastSpurtTargetSpeed = dbgLastSpurtTargetSpeed;

            DbgLastSpeedForStartDashFinishCheck = dbgLastSpeedForStartDashFinishCheck;

            DbgCurDistanceDiffFromPaseMaker = dbgCurDistanceDiffFromPaseMaker;
            DbgDistanceDiffMin = dbgDistanceDiffMin;
            DbgDistanceDiffMax = dbgDistanceDiffMax;
            DbgAdjustTargetDistanceDiff = dbgAdjustTargetDistanceDiff;
            DbgPositionKeepCoolDownTime = dbgPositionKeepCoolDownTime;

            DbgDistanceDiffOnPositionKeepCalcArray = new float[dbgDistanceDiffOnPositionKeepCalcArray.Length];
            Array.Copy(dbgDistanceDiffOnPositionKeepCalcArray, DbgDistanceDiffOnPositionKeepCalcArray, DbgDistanceDiffOnPositionKeepCalcArray.Length);
            
            DbgVisibleDistance = dbgVisibleDistance;
            
            {
                if (dbgScoreList != null)
                {
                    foreach (var scoreFrom in dbgScoreList)
                    {
                        var scoreData = new ScoreData(scoreFrom.raw_score_id, scoreFrom.num, scoreFrom.score, scoreFrom.bonus_array);
                        DbgScoreList.Add(scoreData);
                    }
                }
            }
            
            DbgAccelPerSec = dbgAccelPerSec;
            DbgDecHpPerSec = dbgDecHpPerSec;
            DbgAddHp = dbgAddHp;
            
            DbgOvertakeTargetContinueTime = dbgOvertakeTargetContinueTime;
            DbgOverTakeTargetHaveNoOrderUpContinueTime = dbgOverTakeTargetHaveNoOrderUpContinueTime;
            DbgOverTakeTargetHaveNoOrderDownContinueTime = dbgOverTakeTargetHaveNoOrderDownContinueTime;
            DbgOverTakenHorseIndexes = dbgOverTakenHorseIndexes;
            DbgActivateHealSkillCount = dbgActivateHealSkillCount;
            DbgActivateSpecificTagGroupSkillCount = dbgActivateSpecificTagGroupSkillCount;
            DbgActivateSpecificSkillAbilityTypeGroupSkillCount = dbgActivateSpecificSkillAbilityTypeGroupSkillCount;

            DbgCurrentSkillStatusArray = skillArray;
            
            DbgFinalCornerEndOrder = dbgFinalCornerEndOrder;
            DbgLaneDistanceRateInAllHorses = dbgLaneDistanceRateInAllHorses;
            DbgIsOrderInContinue = dbgIsOrderInContinue;

            DbgHpPer = dbgHpPer;
            DbgIsCompeteFight = dbgIsCompeteFight;
            DbgCompeteFightCount = dbgCompeteFightCount;
            DbgCompeteFightGroup = dbgCompeteFightGroup;
            DbgCompeteFightAddTargetSpeed = dbgCompeteFightAddTargetSpeed;
            DbgCompeteFightAddAccel = dbgCompeteFightAddAccel;
            
            DbgCompeteFightNearList = competeFightNearList;

            DbgIsCompeteTop = dbgIsCompeteTop;
            DbgCompeteTopCount = dbgCompeteTopCount;
            DbgCompeteTopGroup = dbgCompeteTopGroup;
            DbgCompeteTopRemainTime = dbgCompeteTopRemainTime;
            DbgCompeteTopAddTargetSpeed = dbgCompeteTopAddTargetSpeed;
            
            DbgIsSurrounded = dbgIsSurrounded;
            DbgPaseMakerHorseIndex = (sbyte)dbgPaseMakerHorseIndex;
            DbgTopHorseNotMostForwardRunningStyleCnt = dbgTopHorseNotMostForwardRunningStyleCnt;
            DbgConservePowerIncreaseCoolTime = dbgConservePowerIncreaseCoolTime;
            // 足溜め周り
            DbgConservePowerAddAccel = dbgConservePowerAddAccel;
            DbgConservePowerAddAccelRemainTime = dbgConservePowerAddAccelRemainTime;
            DbgConservePower = dbgConservePower;

            {
                // フレームごとの値を取りたいのでDictionaryのディープコピーをする
                DbgConservePowerIncreaseTimeDict = dbgConservePowerIncreaseTimeDict.ToDictionary(
                    data => data.Key,
                    data => new List<float>(data.Value)
                );
                DbgConservePowerDecreaseTimeDict = dbgConservePowerDecreaseTimeDict.ToDictionary(
                    data => data.Key,
                    data => new List<float>(data.Value)
                );
                DbgConservePowerIncreaseTimeDictPerRecord = dbgConservePowerIncreaseTimeDictPerRecord.ToDictionary(
                    data => data.Key,
                    data => new List<float>(data.Value)
                );
                DbgConservePowerDecreaseTimeDictPerRecord = dbgConservePowerDecreaseTimeDictPerRecord.ToDictionary(
                    data => data.Key,
                    data => new List<float>(data.Value)
                );
            }
            
            DbgCompeteBeforeSpurtRemainActiveSec = dbgCompeteBeforeSpurtRemainActiveSec;
            DbgCompeteBeforeSpurtRemainCoolDownSec = dbgCompeteBeforeSpurtRemainCoolDownSec;
            DbgCompeteBeforeSpurtRemainCheckIntervalSec = dbgCompeteBeforeSpurtRemainCheckIntervalSec;
            DbgCompeteBeforeSpurtAddSpeedValue = dbgCompeteBeforeSpurtAddSpeedValue;
            DbgCompeteBeforeSpurtConsumeStaminaValue = dbgCompeteBeforeSpurtConsumeStaminaValue;
            DbgCompeteBeforeSpurtActivateCount = dbgCompeteBeforeSpurtActivateCount;
            DbgCompeteBeforeSpurtConditionNearCharaNum = dbgCompeteBeforeSpurtConditionNearCharaNum;
            DbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle = dbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle;
            DbgCompeteBeforeSpurtIsSpeedUpMinorBonus = dbgCompeteBeforeSpurtIsSpeedUpMinorBonus;
            DbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum = dbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum;
            DbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum = dbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum;

            DbgIsStaminaKeepState = dbgIsStaminaKeepState;
            DbgStaminaKeepCheckIntervalSec = dbgStaminaKeepCheckIntervalSec;
            DbgStaminaKeepNeedHp = dbgStaminaKeepNeedHp;
            DbgStaminaKeepNeedHpForSpurt = dbgStaminaKeepNeedHpForSpurt;
            DbgStaminaKeepNeedHpForMiddle = dbgStaminaKeepNeedHpForMiddle;
            DbgStaminaKeepNeedHpWizRand = dbgStaminaKeepNeedHpWizRand;

            DbgSecureLeadRemainActiveSec = dbgSecureLeadRemainActiveSec;
            DbgSecureLeadRemainCoolDownSec = dbgSecureLeadRemainCoolDownSec;
            DbgSecureLeadRemainCheckIntervalSec = dbgSecureLeadRemainCheckIntervalSec;
            DbgSecureLeadAddSpeedValue = dbgSecureLeadAddSpeedValue;
            DbgSecureLeadConsumeStaminaValue = dbgSecureLeadConsumeStaminaValue;
            DbgSecureLeadActivateCount = dbgSecureLeadActivateCount;
            DbgSecureLeadIsSpeedUpMinorBonus = dbgSecureLeadIsSpeedUpMinorBonus;
            DbgSecureLeadSpeedUpMinorBonusNearCharaNum = dbgSecureLeadSpeedUpMinorBonusNearCharaNum;
            DbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum = dbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum;
        }

        public RaceSimulateHorseFrameDebugData(byte[] bytes, ref int offset)
        {
            Deserialize(bytes, ref offset);
        }
    }
}    

#endif

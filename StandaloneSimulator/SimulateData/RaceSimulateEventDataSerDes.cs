using System;
using System.Linq;

namespace StandaloneSimulator
{
    public partial class RaceSimulateEventData
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        //---------------------------------------------------------------
        public byte[] Serialize()
        {
            //-----------------------------------------------------------
            // 保存するデータをbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] bytesList = new byte[4][];
            bytesList[0] = BitConverter.GetBytes(frameTime);
            bytesList[1] = new byte[] { (byte)type };
            bytesList[2] = new byte[] { (byte)param.Length };
            bytesList[3] = IntArray2Bytes(param);

            //-----------------------------------------------------------
            // RaceSimulateEventData１件あたりのバイト数計算。
            //-----------------------------------------------------------
            int numBytes = bytesList.Sum(b => b.Length); // 保存するデータ。

            //-----------------------------------------------------------
            // RaceSimulateEventData１件を格納するbyte配列。
            //-----------------------------------------------------------
            byte[] retBytes = new byte[numBytes];

            //-----------------------------------------------------------
            // byte配列にデータを詰める。
            //-----------------------------------------------------------
            int destOffset = 0;

            for (int i = 0; i < bytesList.Length; ++i)
            {
                Array.Copy(bytesList[i], 0, retBytes, destOffset, bytesList[i].Length);
                destOffset += bytesList[i].Length;
            }

            return retBytes;
        }
#endif

        public void Deserialize(byte[] bytes, int version)
        {
            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if( version >= RaceSimulateDataHeader.SIMULATE_DATA_VERSION_20200406)
            {
                _Deserialize_Ver20200406_OrNewer( bytes );
            }
            //-----------------------------------------------------------
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
            /* 参考コード。
            else
            {
                _Deserialize_Ver20180619_OrNewer( bytes );
            }
            */
        }

        //---------------------------------------------------------------
        private void _Deserialize_Ver20200406_OrNewer( byte[] bytes )
        {
            int offset = 0;

            RaceUtil.BytesToFloatAddOffset( bytes, ref frameTime, ref offset );
            type = (SimulateEventType)RaceUtil.BytesToByteAddOffset( bytes, ref offset );

            byte paramLen = RaceUtil.BytesToByteAddOffset( bytes, ref offset );

            param = new int[paramLen];
            for (int i = 0; i < param.Length; ++i)
            {
                RaceUtil.BytesToInt32AddOffset( bytes, ref param[i], ref offset );
            }
        }
    }
}
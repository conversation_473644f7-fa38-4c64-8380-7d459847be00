using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// シミュレートデータ：レース全体の１フレームのデータ。
    /// </summary>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateFrameData
    {
        //-------------------------------------------------------------------
        // シリアライズする変数。
        //-------------------------------------------------------------------
        public float Time; // 記録された時間（出走開始からの経過時間：RaceManager.AccumulateTimeSinceStart）。
        public RaceSimulateHorseFrameData[] HorseDataArray; // このフレームでの各キャラの情報。
        
        //-------------------------------------------------------------------
        // 関数。
        //-------------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。渡された時間とキャラの情報を記録する。
        /// </summary>
        /// <param name="curTime">レース出走開始からの経過秒数。</param>
        /// <param name="frameDataArray">キャラアクセッサ。</param>
        public RaceSimulateFrameData(float curTime, RaceSimulateHorseFrameData[] frameDataArray)
        {
            Time = curTime;

            HorseDataArray = frameDataArray;
        }

        /// <summary>
        /// コンストラクタ。byte配列から復元。
        /// </summary>
        public RaceSimulateFrameData(byte[] byteArray, int version, int charaNum, int charaSize)
        {
            Deserialize(byteArray, version, charaNum, charaSize);
        }
    }
}
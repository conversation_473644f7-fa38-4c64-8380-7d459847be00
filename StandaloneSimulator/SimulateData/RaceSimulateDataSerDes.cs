using System;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateDataのシリアライズ・デシリアライズ処理。
    /// </summary>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateData
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        /// <summary>
        /// 自身をバイト配列→GZip圧縮→Base64文字列に変換。
        /// </summary>
        public string SerializeToCompressedBase64(bool isUseDebugInfo, bool isUseFrameDebugData)
        {
            var bytes = Serialize(isUseDebugInfo, isUseFrameDebugData);
            bytes = Gallop.CompressUtil.Compress(bytes);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// 自身をバイト配列に変換。
        /// </summary>
        /// <param name="isUseDebugInfo"> デバッグ情報を使用するか </param>
        /// <param name="isUseFrameDebugData"> Frameごとのデバッグ情報を使用するかどうか（連続シミュレートでは軽量化のためにfalse） </param>
        /// <returns></returns>
        public byte[] Serialize(bool isUseDebugInfo, bool isUseFrameDebugData)
        {
            //-----------------------------------------------------------
            // ヘッダをbyte配列で取得。
            //-----------------------------------------------------------
            Header = new RaceSimulateDataHeader(RaceSimulateDataHeader.SIMULATE_DATA_VERSION);
            byte[] bytesArrayHeader = Header.Serialize();

            //-----------------------------------------------------------
            // レース全体に関するデータをbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] bytesArrayRace = new byte[4][];
            bytesArrayRace[0] = BitConverter.GetBytes(_distanceDiffMax);
            bytesArrayRace[1] = BitConverter.GetBytes(_horseNum);
            bytesArrayRace[2] = BitConverter.GetBytes(RaceSimulateHorseFrameData.CalcSize());
            bytesArrayRace[3] = BitConverter.GetBytes(RaceSimulateHorseResultData.CalcSize());

            //-----------------------------------------------------------
            // レース全体に関するデバッグデータをbyte配列で取得。
            //-----------------------------------------------------------
            byte[] bytesArrayRaceDebug = new byte[0];
            int raceDebugSize = 0;
        #if CYG_DEBUG
            if(isUseDebugInfo)
            {
                if (DbgData != null)
                {
                    bytesArrayRaceDebug = DbgData.Serialize();
                    raceDebugSize = bytesArrayRaceDebug.Length;
                }
            }
        #endif
            
            //-----------------------------------------------------------
            // 全フレームのデータをbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] bytesArrayFrame = new byte[_frameDataList.Count][];
            for (int i = 0, cnt = _frameDataList.Count; i < cnt; ++i)
            {
                bytesArrayFrame[i] = _frameDataList[i].Serialize();
            }

            //-----------------------------------------------------------
            // 全フレームのデバッグデータをbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] bytesArrayFrameDebug = new byte[_frameDataList.Count][];
            int frameDebugSize = 0;
        #if CYG_DEBUG
            if(isUseDebugInfo && isUseFrameDebugData)
            {
                for (int i = 0, cnt = _frameDataList.Count; i < cnt; ++i)
                {
                    bytesArrayFrameDebug[i] = _frameDataList[i].SerializeDebug();
                }
                frameDebugSize = bytesArrayFrameDebug.Sum(b => b.Length);
            }
        #endif
            
            //-----------------------------------------------------------
            // 全キャラのレース中不変データをbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] horseResultByteArray = new byte[_horseResultDataArray.Length][];
            for (int i = 0; i < horseResultByteArray.Length; ++i)
            {
                horseResultByteArray[i] = _horseResultDataArray[i].Serialize();
            }
            //-----------------------------------------------------------
            // 全キャラのレース中不変データのデバッグデータをbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] horseResultDebugByteArray = new byte[_horseResultDataArray.Length][];
            int horseResultDebugSize = 0;
        #if CYG_DEBUG
            if(isUseDebugInfo)
            {
                for (int i = 0; i < _horseResultDataArray.Length; ++i)
                {
                    horseResultDebugByteArray[i] = _horseResultDataArray[i].DbgData != null 
                        ? _horseResultDataArray[i].DbgData.Serialize()
                        : new byte[0];
                }
                horseResultDebugSize = horseResultDebugByteArray.Sum(b => b.Length);
            }
        #endif

            //-----------------------------------------------------------
            // 全イベントのデータをbyte配列で取得。
            //-----------------------------------------------------------
            // イベントデータサイズ→イベントデータ本体...の順で格納していくため、出力先の配列はイベント数*2。
            byte[][] bytesArrayEvent = new byte[_simEvDataList.Count*2][];
            for (int i = 0, cnt = _simEvDataList.Count; i < cnt; ++i)
            {
                int destIndex = i * 2;
                var eventBytes = _simEvDataList[i].Serialize();
                bytesArrayEvent[destIndex  ] = BitConverter.GetBytes( (short)eventBytes.Length );
                bytesArrayEvent[destIndex+1] = eventBytes;
            }

            //-----------------------------------------------------------
            // RaceSimulateData１件あたりのバイト数計算。
            //-----------------------------------------------------------
            int numBytes = 0;
            numBytes += sizeof(int);                            // ヘッダサイズ。
            numBytes += bytesArrayHeader.Length;                // ヘッダ。
            numBytes += bytesArrayRace.Sum(b => b.Length);      // レース全体データ。
            numBytes += sizeof(int);                            // レース全体に関するデバッグデータの合計サイズ。※非CYG_DEBUGでも0を格納するため必要。
            numBytes += raceDebugSize;                          // レース全体に関するデバッグデータ。※非CYG_DEBUGでは0を格納する。
            numBytes += sizeof(int);                            // フレーム数。
            numBytes += sizeof(int);                            // フレーム毎データサイズ。
            numBytes += bytesArrayFrame.Sum(b => b.Length);     // 全フレームデータ。
            numBytes += sizeof(int);                            // 全フレームデバッグデータの合計サイズ。※非CYG_DEBUGでも0を格納するため必要。
            numBytes += frameDebugSize;                         // 全フレームデバッグデータ。※非CYG_DEBUGでは0を格納する。
            numBytes += horseResultByteArray.Sum(b => b.Length);// 全キャラレース不変データ。
            
            numBytes += sizeof(int);                            // 全キャラレース不変データのデバッグデータの合計サイズ。※非CYG_DEBUGでも0を格納するため必要。
            numBytes += horseResultDebugSize;                   // 全キャラレース不変データのデバッグデータ。※非CYG_DEBUGでは0を格納する。
            
            numBytes += sizeof(int);                            // イベント数。
            numBytes += bytesArrayEvent.Sum(b => b.Length);     // 全イベントデータ。

            //-----------------------------------------------------------
            // byte配列にデータを詰める。
            //-----------------------------------------------------------
            byte[] retBytes = new byte[numBytes];
            int destOffset = 0;

            // ヘッダデータサイズ。
            RaceUtil.Int32ToBytesAddOffset( bytesArrayHeader.Length, ref retBytes, ref destOffset );
            // ヘッダ。
            RaceUtil.BytesToBytesAddOffset( bytesArrayHeader, ref retBytes, ref destOffset );

            // レース全体データ。
            for( int i = 0; i < bytesArrayRace.Length; ++i )
            {
                RaceUtil.BytesToBytesAddOffset( bytesArrayRace[i], ref retBytes, ref destOffset );
            }
            // レース全体にかかわるデバッグデータ合計サイズ。※非CYG_DEBUGでも0を格納するため必要。
            RaceUtil.Int32ToBytesAddOffset( raceDebugSize, ref retBytes, ref destOffset );
            RaceUtil.BytesToBytesAddOffset(bytesArrayRaceDebug, ref retBytes, ref destOffset);
                

            // フレーム数。
            RaceUtil.Int32ToBytesAddOffset( _frameDataList.Count, ref retBytes, ref destOffset );
            // フレーム毎データサイズ。
            int frameDataSize = bytesArrayFrame[0].Length;
            RaceUtil.Int32ToBytesAddOffset( frameDataSize, ref retBytes, ref destOffset );
            // フレーム毎データ本体。
            for (int i = 0; i < bytesArrayFrame.Length; ++i)
            {
                Debug.Assert(frameDataSize == bytesArrayFrame[i].Length);
                RaceUtil.BytesToBytesAddOffset(bytesArrayFrame[i], ref retBytes, ref destOffset);
            }

            // フレーム毎デバッグデータ合計サイズ。※非CYG_DEBUGでも0を格納するため必要。
            RaceUtil.Int32ToBytesAddOffset( frameDebugSize, ref retBytes, ref destOffset );
            // フレーム毎デバッグデータ本体。※非CYG_DEBUGではbytesArrayFrameDebug.Lengthが0となるのでここはスキップされる。
            for (int i = 0; i < bytesArrayFrameDebug.Length; ++i)
            {
                if(null == bytesArrayFrameDebug[i])
                {
                    continue;
                }
                RaceUtil.BytesToBytesAddOffset(bytesArrayFrameDebug[i], ref retBytes, ref destOffset);
            }

            // 全キャラレース不変データ。
            for (int i = 0; i < horseResultByteArray.Length; ++i)
            {
                RaceUtil.BytesToBytesAddOffset(horseResultByteArray[i], ref retBytes, ref destOffset);
            }

            // 全キャラレース不変データのデバッグデータ合計サイズ。※非CYG_DEBUGでも0を格納するため必要。
            RaceUtil.Int32ToBytesAddOffset(horseResultDebugSize, ref retBytes, ref destOffset);
            // 全キャラレース不変データのデバッグデータ本体。※非CYG_DEBUGではhorseResultByteArray.Lengthが0となるのでここはスキップされる。
            for (int i = 0; i < horseResultDebugByteArray.Length; ++i)
            {
                if(null == horseResultDebugByteArray[i])
                {
                    continue;
                }
                RaceUtil.BytesToBytesAddOffset(horseResultDebugByteArray[i], ref retBytes, ref destOffset);
            }
            
            // イベント数。
            RaceUtil.Int32ToBytesAddOffset( _simEvDataList.Count, ref retBytes, ref destOffset );

            // イベント毎データ本体。
            for (int i = 0; i < bytesArrayEvent.Length; ++i)
            {
                RaceUtil.BytesToBytesAddOffset( bytesArrayEvent[i], ref retBytes, ref destOffset );
            }

            return retBytes;
        }
#endif

        /// <summary>
        /// バイト配列から自身を復元。
        /// </summary>
        private void Deserialize(byte[] bytes)
        {
            if (bytes == null)
            {
                Debug.Log("Data is Null!");
                return;
            }

            int offset = 0;

            //-----------------------------------------------------------
            // ヘッダ復元。
            //-----------------------------------------------------------
            Header = DeserializeHeader(bytes, ref offset);

            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if(Header.Version >= RaceSimulateDataHeader.SIMULATE_DATA_VERSION_20200406)
            {
                Deserialize_Ver20200303_OrNewer(bytes, offset, Header.Version);
            }
            //-----------------------------------------------------------
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
            /* 参考コード。
            else if(Header.Version >= RaceSimulateDataHeader.SIMULATE_DATA_VERSION_20200131)
            {
                Deserialize_Ver20190218_OrNewer(bytes, offset, Header.Version);
            }
            */
        }

        //---------------------------------------------------------------
        public static RaceSimulateDataHeader DeserializeHeader( byte[] bytes )
        {
            int offsetDummy = 0;
            return DeserializeHeader( bytes, ref offsetDummy );
        }

        //---------------------------------------------------------------
        public static RaceSimulateDataHeader DeserializeHeader( byte[] bytes, ref int offset )
        {
            // ヘッダが先頭にあるので、offsetは0で渡されるはず。
            Debug.Assert( 0 == offset );

            // ヘッダデータサイズ。
            int headerSize = RaceUtil.BytesToInt32AddOffset( bytes, ref offset );
            // ヘッダ。
            byte[] headerBytes = new byte[ headerSize ];
            Array.Copy( bytes, offset, headerBytes, 0, headerSize );
            offset += headerSize;

            var header = new RaceSimulateDataHeader(headerBytes);
            return header;
        }

        
        //---------------------------------------------------------------
        private void Deserialize_Ver20200303_OrNewer(byte[] bytes, int offset, int version)
        {
            //-----------------------------------------------------------
            // レース全体に関するデータ。
            //-----------------------------------------------------------
            int horseResultSize;
            int horseSize;
            {
                // 差分距離最大値。
                RaceUtil.BytesToFloatAddOffset(bytes, ref _distanceDiffMax, ref offset);
                // キャラ数。
                RaceUtil.BytesToInt32AddOffset(bytes, ref _horseNum, ref offset);
                // キャラ毎データサイズ。
                horseSize = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                // キャラレース不変データ毎データサイズ。
                horseResultSize = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            }

            
            //-----------------------------------------------------------
            // レース全体に関するデバッグ情報。
            //-----------------------------------------------------------
            {
                // デバッグ情報サイズ。
                int debugSize = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            #if CYG_DEBUG
                if(debugSize > 0)
                {
                    if (RaceSimulateDebugger.IsDeserializeDebug)
                    {
                        DbgData = new RaceSimulateDebugData(bytes, ref offset);
                    }
                    else
                    {
                        offset += debugSize;
                    }
                }
            #else
                // !CYG_DEBUGではデバッグ情報読み飛ばす。
                offset += debugSize;
            #endif
            }
            
            
            //-----------------------------------------------------------
            // フレームデータ。
            //-----------------------------------------------------------
            {
                // フレーム数。
                int frameNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                // フレーム毎データサイズ。
                int frameSize = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                
                // フレーム毎データ。
                _frameDataList = new List<RaceSimulateFrameData>(frameNum);
                for (int i = 0; i < frameNum; ++i)
                {
                    var frameDataBytes = new byte[frameSize];
                    Array.Copy(bytes, offset, frameDataBytes, 0, frameSize);
                    offset += frameSize;

                    var frameData = new RaceSimulateFrameData(frameDataBytes, version, _horseNum, horseSize);
                    _frameDataList.Add(frameData);
                }
            }

            //-----------------------------------------------------------
            // フレームデータのデバッグ情報。
            //-----------------------------------------------------------
            {
                // デバッグ情報サイズ。
                int debugSize = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            #if CYG_DEBUG
                if(debugSize > 0)
                {
                    if (RaceSimulateDebugger.IsDeserializeDebug)
                    {
                        for(int i = 0, cnt = _frameDataList.Count; i < cnt; ++i)
                        {
                            _frameDataList[i].DeserializeDebug(bytes, ref offset);
                        }
                    }
                    else
                    {
                        offset += debugSize;
                    }
                }
            #else
                // !CYG_DEBUGではデバッグ情報読み飛ばす。
                offset += debugSize;
            #endif
            }

            //-----------------------------------------------------------
            // キャラ毎のレース中不変データ。
            //-----------------------------------------------------------
            {
                _horseResultDataArray = new RaceSimulateHorseResultData[_horseNum];
                for (int i = 0; i < _horseResultDataArray.Length; ++i)
                {
                    var resultDataBytes = new byte[horseResultSize];
                    Array.Copy(bytes, offset, resultDataBytes, 0, horseResultSize);
                    offset += horseResultSize;

                    _horseResultDataArray[i] = new RaceSimulateHorseResultData(resultDataBytes, version);
                }
            }

            //-----------------------------------------------------------
            // キャラ毎のレース不変データのデバッグ情報。
            //-----------------------------------------------------------
            {
                // デバッグ情報サイズ。
                int debugSize = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            #if CYG_DEBUG
                if(debugSize > 0)
                {
                    if (RaceSimulateDebugger.IsDeserializeDebug)
                    {
                        for(int i = 0; i < _horseResultDataArray.Length; ++i)
                        {
                            _horseResultDataArray[i].DbgData = new RaceSimulateHorseResultDebugData(bytes, ref offset);
                        }
                    }
                    else
                    {
                        offset += debugSize;
                    }
                }
            #else
                // !CYG_DEBUGではデバッグ情報読み飛ばす。
                offset += debugSize;
            #endif
            }
            
            //-----------------------------------------------------------
            // イベントデータ。            
            //-----------------------------------------------------------
            {
                // イベント数。
                int numEvent = RaceUtil.BytesToInt32AddOffset( bytes, ref offset );
                // イベント毎データ。
                _simEvDataList = new List<RaceSimulateEventData>(numEvent);
                for (int i = 0; i < numEvent; ++i)
                {
                    short eventSize = RaceUtil.BytesToInt16AddOffset( bytes, ref offset );

                    byte[] eventDataBytes = new byte[eventSize];
                    Array.Copy(bytes, offset, eventDataBytes, 0, eventSize);
                    offset += eventSize;

                    RaceSimulateEventData eventData = new RaceSimulateEventData();
                    eventData.Deserialize( eventDataBytes, version );

                    _simEvDataList.Add(eventData);
                }
            }
        }
    }
}
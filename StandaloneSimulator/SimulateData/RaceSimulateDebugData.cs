#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
using System;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateDataが所持するデバッグ情報。
    /// </summary>
    //-----------------------------------------------------------------------
    public partial class RaceSimulateDebugData
    {
        //-------------------------------------------------------------------
        // 定義。
        //-------------------------------------------------------------------
        /// <summary>
        /// デバッグ用チーム情報シリアライズのためのクラス。
        /// </summary>
        public class TeamData
        {
            public int TeamId;
            public int[] TeamMemberHorseIndexArray;
            public List<ScoreData> ScoreList;
        }
        
        public enum SimulateEnvironment
        {
            Client,
            Preforkd,
            Console,
        }
        
        public const int VERSION_10000000 = 10000000;
        public const int CUR_VERSION = VERSION_10000000;

        private static readonly System.Text.Encoding SIMULATE_TAG_ENCODING = System.Text.Encoding.ASCII;
        
        //-------------------------------------------------------------------
        // 変数。
        //-------------------------------------------------------------------
        public int Version;
        
        public List<TeamData> DbgTeamList = new List<TeamData>();
        public string DbgSimulateTag = string.Empty;
        public SimulateEnvironment DbgSimulateEnvironment;
        
        private static int _dbgSimulateRunningNumber;

        //-------------------------------------------------------------------
        // 関数。
        //-------------------------------------------------------------------
        public RaceSimulateDebugData()
        {
            // シミュレートデータの識別子。
            DbgSimulateTag = GenerateSimulateTag();
            Version = CUR_VERSION;
    #if STANDALONE_SIMULATOR
            DbgSimulateEnvironment = SimulateEnvironment.Console;
    #else
            DbgSimulateEnvironment = SimulateEnvironment.Client;
    #endif
        }

        public RaceSimulateDebugData(List<TeamData> teamDataList)
        {
            // シミュレートデータの識別子。
            DbgSimulateTag = GenerateSimulateTag();
            
            Version = CUR_VERSION;
            DbgTeamList = teamDataList;
            // シミュレートが行われた環境。
    #if STANDALONE_SIMULATOR
            DbgSimulateEnvironment = SimulateEnvironment.Console;
    #else
            DbgSimulateEnvironment = SimulateEnvironment.Client;
    #endif
        }

        public RaceSimulateDebugData(byte[] bytes, ref int offset)
        {
            Deserialize(bytes, ref offset);
        }
        
        private string GenerateSimulateTag()
        {
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var curTime = DateTime.Now;
            var retTag = $"{curTime.Month:D2}{curTime.Day:D2}{curTime.Hour:D2}{curTime.Minute:D2}{curTime.Millisecond}-{process.Id}-{_dbgSimulateRunningNumber}";
            ++_dbgSimulateRunningNumber;
            return retTag;
        }
    }
}
#endif

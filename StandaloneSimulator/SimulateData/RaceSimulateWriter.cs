using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レースのシミュレーション結果をRaceSimulateDataに書き込む。
    /// </summary>
    //-------------------------------------------------------------------
    public class RaceSimulateWriter
    {
        //-------------------------------------------------------------------
        // 定数。
        //-------------------------------------------------------------------
        private const int BLOCK_HORSEINDEX_NULL = -1;
        private const int EventDataListReserveNum = 64;

        private RaceSimulateData _simData = null;
        private float _curTime = 0.0f;
        private static readonly Array SKILL_MODIFIER_PARAM_ENUM_ARRAY = Enum.GetValues(typeof(Gallop.SkillDefine.SkillModifierParam));   // 高速化のためにキャッシュ

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        /// <param name="exportType">RaceSimulateDataのエクスポート方法。</param>
        //---------------------------------------------------------------
        public RaceSimulateWriter()
        {
        }

        //---------------------------------------------------------------
        /// <summary>
        /// シミュレーションデータの書き込み開始。書き込み前に１度だけ呼び出す。
        /// </summary>
        //---------------------------------------------------------------
        public void StartRecord(int horseNum)
        {
            _curTime = 0.0f;

            _simData = new RaceSimulateData(horseNum);
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// シミュレーションデータ書き込み終了。書き込み終了後に１度だけ呼び出す。
        /// </summary>
        //---------------------------------------------------------------
        public void EndRecord(IRaceHorseAccessor horseAccessor)
        {
            var horseResultDataArray = new RaceSimulateHorseResultData[horseAccessor.GetHorseNumber()];
            for (int i = 0; i < horseResultDataArray.Length; ++i)
            {
                var horse = horseAccessor.GetHorseInfo(i);
#if CYG_DEBUG
                var debugData = new RaceSimulateHorseResultDebugData(
                    finalGradeHigherCount: RaceUtil.CalcFinalGradeHigherCount(horse.FinalGrade, horseAccessor),
                    finalGradeLowerCount: RaceUtil.CalcFinalGradeLowerCount(horse.FinalGrade, horseAccessor),
                    isRunningStyleCountMax: RaceUtil.CheckRunningStyleCountSameMax(horse.RunningStyle, horseAccessor),
                    popularity: horse.Popularity,
                    popularityRankLeft: horse.PopularityRankLeft,
                    popularityRankCenter: horse.PopularityRankCenter,
                    popularityRankRight: horse.PopularityRankRight,
                    isRunningStyleEqualPopularityOne: RaceManagerSimulate.Instance.GetHorseByPopularity(0).RunningStyle == horse.RunningStyle,
                    isGoodStart: horse.IsGoodStart,
                    isBadStart: horse.IsBadStart,
                    isHpEmpty: horse.IsHpEmptyOnRace,
                    temptationCount: horse.TemptationCount,
                    positionOvertakeCount: horse.PositionKeepCount,
                    lastSpurtCalcResult: horse.LastSpurtCalcResult,
                    frontBlockAccumulateTime: horse.BlockFrontAccumulateTime,
                    baseSpeed: horse.BaseSpeed,
                    baseStamina: horse.BaseStamina,
                    basePow: horse.BasePow,
                    baseGuts: horse.BaseGuts,
                    baseWiz: horse.BaseWiz,
                    baseSpeedWithMotivationRaceType: horse.DbgSpeedOnStart,
                    baseStaminaWithMotivationRaceType: horse.DbgStaminaOnStart,
                    basePowWithMotivationRaceType: horse.DbgPowOnStart,
                    baseGutsWithMotivationRaceType: horse.DbgGutsOnStart,
                    baseWizWithMotivationRaceType: horse.DbgWizOnStart,
                    speedOnStart: horse.Speed,
                    staminaOnStart: horse.Stamina,
                    powOnStart: horse.Pow,
                    gutsOnStart: horse.Guts,
                    wizOnStart: horse.Wiz,
                    bonusRateList: horse.DbgBonusRateList,
                    teamStadiumTotalScore: horse.DbgTeamStadiumTotalScore,
                    startDelayTimeBase: horse.DbgDelayTimeSavedBase,
                    scoreList: horse.ScoreList,
                    receiveHistoryList: horse.DbgGetModifierReceiver?.DbgReceiveHistoryList,
                    lastSpurtCandidateList: horse.DbgLastSpurtCandidateList,
                    lastSpurtUseCandidate: horse.DbgLastSpurtUseCandidate,
                    lastSpurtLastCalculatedSpeedQueue: horse.DbgLastSpurtLastCalculatedSpeedQueue,
                    lastSpurtCalcCount: horse.DbgLastSpurtCalcCount,
                    lastMoveOutStartDistance: horse.DbgLastMoveOutStartDistance,
                    runningStyleEx: horse.RunningStyleEx,
                    competeTopStartGroupHorseList: horse.DbgCompeteTopStartGroupHorseList,
                    competeTopEndGroupHorseList: horse.DbgCompeteFightStartGroupHorseList,
                    competeFightStartGroupHorseList: horse.DbgCompeteFightStartGroupHorseList,
                    competeFightEndGroupHorseList: horse.DbgCompeteFightEndGroupHorseList,
                    baseTargetSpeedRandomMin: horse.DbgBaseTargetSpeedRandomMin,
                    baseTargetSpeedRandomMax: horse.DbgBaseTargetSpeedRandomMax,
                    courseSetBaseSpeedCoef: horse.DbgCourseSetBaseSpeedCoef,
                    conservePowerActivityTime: horse.DbgHorseAI.DbgConservePowerActivityTime,
                    staminaLimitBreakBuffAddTargetSpeed: horse.DbgHorseAI.DbgStaminaLimitBreakBuffAddTargetSpeed,
                    staminaLimitBreakBuffLotteryRandomTableType: horse.DbgHorseAI.DbgStaminaLimitBreakBuffLotteryRandomTableType,
                    staminaLimitBreakBuffRandomTableProbabilityDict: horse.DbgHorseAI.DbgStaminaLimitBreakBuffRandomTableProbabilityDict,
                    staminaLimitBreakBuffRandomTableCoef: horse.DbgHorseAI.DbgStaminaLimitBreakBuffRandomTableCoef,
                    staminaLimitBreakBuffActivateTime: horse.DbgHorseAI.DbgStaminaLimitBreakBuffActivateTime,
                    staminaLimitBreakBuffFinishTime: horse.DbgHorseAI.DbgStaminaLimitBreakBuffFinishTime,
                    orderChangeCounterUpOrderChangeCounterUpOverTakeLogArray: horse.DbgHorseAI.DbgOrderChangeCounterUpOverTakeLogList.ToArray(),
                    orderChangeCounterDownOrderChangeCounterDownOverTakeLogArray: horse.DbgHorseAI.DbgOrderChangeCounterDownOverTakeLogList.ToArray()
                );
                horseResultDataArray[i] = new RaceSimulateHorseResultData(horse.DelayTimeSaved, horse.GutsOrder, horse.WizOrder, horse.FinishOrder, horse.FinishTimeScaled, horse.FinishTimeRaw, horse.FinishTimeDiffFromPrevHorse
                , horse.LastSpurtStartDistance, horse.RunningStyle,horse.Defeat, debugData);
#else
                horseResultDataArray[i] = new RaceSimulateHorseResultData(horse.DelayTimeSaved, horse.GutsOrder, horse.WizOrder, horse.FinishOrder, horse.FinishTimeScaled, horse.FinishTimeRaw, horse.FinishTimeDiffFromPrevHorse
                , horse.LastSpurtStartDistance, horse.RunningStyle,horse.Defeat);
#endif
            }
            _simData.RecordRaceResult(horseResultDataArray);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// レース出走前地点記録用にシミュレーションデータを書き込む。
        /// </summary>
        //---------------------------------------------------------------
        public void RecordRaceInit(IRaceHorseAccessor horseAccessor, bool isUseFrameDebugData)
        {
            Debug.Assert( RaceUtilMath.Approximately( 0, _curTime ) );

            // 0秒目地点の記録。
            Record(horseAccessor, isUseFrameDebugData);
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// シミュレーションデータの書き込み。
        /// </summary>
        //---------------------------------------------------------------
        public void Record(IRaceHorseAccessor horseAccessor, bool isUseFrameDebugData)
        {
            var horseDataArray = new RaceSimulateHorseFrameData[horseAccessor.GetHorseNumber()];
            for (int i = 0; i < horseDataArray.Length; ++i)
            {
                var horse = horseAccessor.GetHorseInfo(i);
#if CYG_DEBUG
                if (isUseFrameDebugData)
                {
                    // デバッグ用情報の生成のための下準備
                    var modifierDict = PrepareModifierDict(horse);
                    var cancelledModifierDict = PrepareCancelledModifierDict(horse);
                    var currentSkillStatusArray = PrepareCurrentSkillStatusArray(horse);
                    var competeFightNearList = PrepareCompeteFightNearList(horse);
                    var isOrderInContinue = PrepareIsOrderInContinueArray(horse);

                    // デバッグ用情報の生成
                    var dbgData = new RaceSimulateHorseFrameDebugData(
                        dbgHorsePhase: horse.GetPhase(),
                        dbgSpeed: horse.Speed,
                        dbgStamina: horse.Stamina,
                        dbgPower: horse.Pow,
                        dbgGuts: horse.Guts,
                        dbgWiz: horse.Wiz,
                        dbgBlockInHorseIdx: horse.GetBlockHorseIn() != null
                            ? horse.GetBlockHorseIn().HorseIndex
                            : BLOCK_HORSEINDEX_NULL,
                        dbgBlockOutHorseIdx: horse.GetBlockHorseOut() != null
                            ? horse.GetBlockHorseOut().HorseIndex
                            : BLOCK_HORSEINDEX_NULL,
                        dbgSlopeAddTargetSpeed: horse.DbgSlopeAddTargetSpeed,
                        dbgSlopePer: horse.SlopePer,
                        dbgBaseTargetSpeed: horse.DbgBaseTargetSpeed,
                        dbgSkillAddTargetSpeed: horse.DbgSkillAddTargetSpeed,
                        dbgForceInMoveAddTargetSpeed: horse.DbgForceInMoveAddTargetSpeed,
                        dbgPositionKeepBaseTargetSpeedMultiply: horse.DbgPositionKeepBaseTargetSpeedMultiply,
                        dbgIsTemptationStartEnable: horse.IsTemptationStartEnable,
                        dbgTemptationStartSection: horse.DbgTemptationStartSection,
                        dbgTemptationPerRandom: horse.DbgTemptationPerRandom,
                        dbgTemptationPerWiz: horse.DbgTemptationPerWiz,
                        dbgInfrontHorseNearContinueTime: horse.InfrontHorseNearContinueTime,
                        dbgBehindHorseNearContinueTime: horse.BehindHorseNearContinueTime,
                        dbgInfrontHorseNearLaneContinueTime: horse.InfrontHorseNearLaneContinueTime,
                        dbgBehindHorseNearLaneContinueTime: horse.BehindHorseNearLaneContinueTime,
                        dbgIsInMoveEnable: horse.DbgIsEnableInMove,
                        dbgIsOutMoveEnable: horse.DbgIsEnableOutMove,
                        dbgInLaneSpace: horse.DbgInLaneSpace,
                        dbgOutLaneSpace: horse.DbgOutLaneSpace,
                        dbgPositionKeepMode: horse.PositionKeepMode,
                        dbgTargetSpeed: horse.GetTargetSpeed(),
                        currentActiveSkills: horse.CurerntActiveSkills.Select(s => new RaceHorseSkillData()
                            { skill_id = s.SkillId, level = s.SkillLevel }).ToArray(),
                        dbgActivatedSkillArray: (null != horse.SkillManager.DbgGetActivatedSkills())
                            ? horse.SkillManager.DbgGetActivatedSkills().ToArray()
                            : new ActivatedSkillFrameInfo[0],
                        dbgGetModifierDict: modifierDict,
                        dbgGetCancelledModifierDict: cancelledModifierDict,
                        dbgGetActivateSkillCount: horse.DbgGetActivateSkillCount(),
                        dbgBlockedFrontContinueTime: horse.BlockFrontContinueTime,
                        dbgBlockedFrontHpEmptyContinueTime: horse.BlockFrontHpEmptyContinueTime,
                        dbgBlockedSideContinueTime: horse.BlockSideContinueTime,

                        dbgLaneMoveSpeed: horse.GetCurLaneMoveSpeed(),
                        dbgTargetLane: horse.GetTargetLane(),

                        dbgOverTakeHorseIndexs: horse.GetOverTakeHorseList() != null
                            ? horse.GetOverTakeHorseList().Select(h => h.infoSimulate.HorseIndex).ToArray()
                            : new int[0],
                        dbgOverTakeLane: horse.DbgOverTakeLane,
                        dbgOverTakeCoolDownTime: horse.DbgOverTakeCoolDownTime,

                        dbgCorner: horse.CurCorner,
                        dbgIsCornerLast: horse.IsFinalCorner,
                        dbgCurOrder: horse.CurOrder,

                        dbgCurOrderUpCountPhaseMiddle: horse.CurOrderUpCountPhaseMiddle,
                        dbgCurOrderUpCountPhaseEndAfter: horse.CurOrderUpCountPhaseEndAfter,
                        dbgCurOrderUpCountCorner: horse.CurOrderUpCountCorner,
                        dbgCurOrderUpCountLastSpurt: horse.CurOrderUpCountLastSpurt,
                        dbgCurOrderUpCountDistance1: horse.CurOrderUpCountDistance1,
                        dbgCurOrderUpCountFinalCornerAfter: horse.CurOrderUpCountFinalCornerAfter,
                        dbgCurOrderUpCountLaterHalf: horse.CurOrderUpCountLaterHalf,
                        dbgCurOrderUpCountCornerPhaseEndAfter: horse.CurOrderUpCountCornerPhaseEndAfter,

                        dbgCurOrderDownCountPhaseMiddle: horse.CurOrderDownCountPhaseMiddle,
                        dbgCurOrderDownCountPhaseEndAfter: horse.CurOrderDownCountPhaseEndAfter,
                        dbgCurOrderDownCountCorner: horse.CurOrderDownCountCorner,
                        dbgCurOrderDownCountLastSpurt: horse.CurOrderDownCountLastSpurt,
                        dbgCurOrderDownCountDistance1: horse.CurOrderDownCountDistance1,
                        dbgCurOrderDownCountFinalCornerAfter: horse.CurOrderDownCountFinalCornerAfter,
                        dbgCurOrderDownCountLaterHalf: horse.CurOrderDownCountLaterHalf,
                        dbgCurOrderDownCountCornerPhaseEndAfter: horse.CurOrderDownCountCornerPhaseEndAfter,

                        dbgIsStraight: horse.IsStraight,
                        dbgIsStraightFirst: horse.IsStraightFirst,
                        dbgIsStraightLast: horse.IsStraightLast,
                        dbgStraightFrontType: horse.CurStraightFrontType,
                        dbgStraightTypeNumber: horse.CurStraightTypeNumber,

                        dbgVisibleHorseIndexs: horse.VisibleHorses
                            .Take(horse.VisibleHorseCount)
                            .Select(h => (byte)h.infoSimulate.HorseIndex)
                            .ToArray(),

                        dbgNearHorseIndexs: horse.NearHorses
                            .Take(horse.NearHorseCount)
                            .Select(h => (byte)h.infoSimulate.HorseIndex)
                            .ToArray(),
                        dbgNearHorseAroundTime: horse.NearHorseAroundAccumulateTime,

                        dbgLastSpurtTargetSpeed: horse.DbgLastSpurtTargetSpeed,

                        dbgCurDistanceDiffFromPaseMaker: horse.DbgHorseAI.DbgCurDistanceDiffFromPaseMaker,
                        dbgDistanceDiffMin: horse.DbgHorseAI.DbgDistanceDiffMin,
                        dbgDistanceDiffMax: horse.DbgHorseAI.DbgDistanceDiffMax,
                        dbgAdjustTargetDistanceDiff: horse.DbgHorseAI.DbgAdjustTargetDistanceDiff,
                        dbgPositionKeepCoolDownTime: horse.DbgHorseAI.DbgPositionKeepCoolDownTime,

                        dbgDistanceDiffOnPositionKeepCalcArray: horse.DbgDistanceDiffOnPositionKeepCalcArray,
                        dbgVisibleDistance: horse.VisibleDistance,
                        dbgScoreList: horse.ScoreList,

                        dbgAccelPerSec: horse.DbgAccelPerSec,
                        dbgDecHpPerSec: horse.DbgDecHpPerSec,
                        dbgAddHp: horse.DbgAddHp,

                        dbgOvertakeTargetContinueTime: horse.OverTakeTargetContinueTime,
                        dbgOverTakeTargetHaveNoOrderUpContinueTime: horse.OverTakeTargetHaveNoOrderUpContinueTime,
                        dbgOverTakeTargetHaveNoOrderDownContinueTime: horse.OverTakeTargetHaveNoOrderDownContinueTime,
                        dbgOverTakenHorseIndexes: horse.DbgOverTakenHorseIndexList != null
                            ? horse.DbgOverTakenHorseIndexList.ToArray()
                            : new int[0],
                        dbgActivateHealSkillCount: horse.GetActivateHealSkillCount(),
                        dbgActivateSpecificTagGroupSkillCount: horse.GetActivateSpecificTagGroupSkillCount(),
                        dbgActivateSpecificSkillAbilityTypeGroupSkillCount: horse
                            .GetActivateSpecificSkillAbilityTypeGroupSkillCount(),
                        skillArray: currentSkillStatusArray,

                        dbgFinalCornerEndOrder: horse.FinalCornerEndOrder,
                        dbgLaneDistanceRateInAllHorses: horse.LaneDistancePerInAllHorses,
                        dbgIsOrderInContinue: isOrderInContinue,

                        dbgHpPer: horse.GetHpPer(),
                        dbgIsCompeteFight: horse.IsCompeteFight,
                        dbgCompeteFightCount: horse.CompeteFightCount,
                        dbgCompeteFightGroup: horse.CompeteFightGroup,
                        dbgCompeteFightAddTargetSpeed: horse.CompeteFightAddTargetSpeed,
                        dbgCompeteFightAddAccel: horse.CompeteFightAddAccel,
                        competeFightNearList: competeFightNearList,
                        dbgIsCompeteTop: horse.IsCompeteTop,
                        dbgCompeteTopCount: horse.CompeteTopCount,
                        dbgCompeteTopGroup: horse.CompeteTopGroup,
                        dbgCompeteTopRemainTime: horse.CompeteTopRemainTime,
                        dbgCompeteTopAddTargetSpeed: horse.CompeteTopAddTargetSpeed,

                        dbgIsSurrounded: horse.IsSurrounded,
                        dbgPaseMakerHorseIndex: horse.DbgPaseMakerHorseIndex,
                        dbgTopHorseNotMostForwardRunningStyleCnt: horse.DbgTopHorseNotMostForwardRunningStyleCnt,
                        // 足溜め周り
                        dbgConservePowerAddAccel: horse.DbgHorseAI.DbgConservePowerAddAccel,
                        dbgConservePowerAddAccelRemainTime: horse.DbgHorseAI.DbgConservePowerAddAccelRemainTime,
                        dbgConservePower: horse.DbgHorseAI.DbgConservePower,
                        dbgConservePowerIncreaseCoolTime: horse.DbgHorseAI.DbgConservePowerIncreaseCoolTime,
                        dbgConservePowerIncreaseTimeDict: horse.DbgHorseAI.DbgConservePowerIncreaseTimeDict,
                        dbgConservePowerDecreaseTimeDict: horse.DbgHorseAI.DbgConservePowerDecreaseTimeDict,
                        dbgConservePowerIncreaseTimeDictPerRecord: horse.DbgHorseAI
                            .DbgConservePowerIncreaseTimeDictPerRecord,
                        dbgConservePowerDecreaseTimeDictPerRecord: horse.DbgHorseAI
                            .DbgConservePowerDecreaseTimeDictPerRecord,

                        dbgCompeteBeforeSpurtRemainActiveSec: horse.DbgHorseAI.DbgCompeteBeforeSpurtRemainActiveSec,
                        dbgCompeteBeforeSpurtRemainCoolDownSec: horse.DbgHorseAI.DbgCompeteBeforeSpurtRemainCoolDownSec,
                        dbgCompeteBeforeSpurtRemainCheckIntervalSec: horse.DbgHorseAI
                            .DbgCompeteBeforeSpurtRemainCheckIntervalSec,
                        dbgCompeteBeforeSpurtAddSpeedValue: horse.DbgHorseAI.DbgCompeteBeforeSpurtAddSpeedValue,
                        dbgCompeteBeforeSpurtConsumeStaminaValue: horse.DbgHorseAI
                            .DbgCompeteBeforeSpurtConsumeStaminaValue,
                        dbgCompeteBeforeSpurtActivateCount: horse.DbgHorseAI.DbgCompeteBeforeSpurtActivateCount,
                        dbgCompeteBeforeSpurtConditionNearCharaNum: horse.DbgHorseAI
                            .DbgCompeteBeforeSpurtConditionNearCharaNum,
                        dbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle: horse.DbgHorseAI
                            .DbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle,
                        dbgCompeteBeforeSpurtIsSpeedUpMinorBonus: horse.DbgHorseAI
                            .DbgCompeteBeforeSpurtIsSpeedUpMinorBonus,
                        dbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum: horse.DbgHorseAI
                            .DbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum,
                        dbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum: horse.DbgHorseAI
                            .DbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum,

                        dbgIsStaminaKeepState: horse.DbgHorseAI.DbgIsStaminaKeepState,
                        dbgStaminaKeepCheckIntervalSec: horse.DbgHorseAI.DbgStaminaKeepCheckIntervalSec,
                        dbgStaminaKeepNeedHp: horse.DbgHorseAI.DbgStaminaKeepNeedHp,
                        dbgStaminaKeepNeedHpForSpurt: horse.DbgHorseAI.DbgStaminaKeepNeedHpForSpurt,
                        dbgStaminaKeepNeedHpForMiddle: horse.DbgHorseAI.DbgStaminaKeepNeedHpForMiddle,
                        dbgStaminaKeepNeedHpWizRand: horse.DbgHorseAI.DbgStaminaKeepNeedHpWizRand,

                        dbgSecureLeadRemainActiveSec: horse.DbgHorseAI.DbgSecureLeadRemainActiveSec,
                        dbgSecureLeadRemainCoolDownSec: horse.DbgHorseAI.DbgSecureLeadRemainCoolDownSec,
                        dbgSecureLeadRemainCheckIntervalSec: horse.DbgHorseAI.DbgSecureLeadRemainCheckIntervalSec,
                        dbgSecureLeadAddSpeedValue: horse.DbgHorseAI.DbgSecureLeadAddSpeedValue,
                        dbgSecureLeadConsumeStaminaValue: horse.DbgHorseAI.DbgSecureLeadConsumeStaminaValue,
                        dbgSecureLeadActivateCount: horse.DbgHorseAI.DbgSecureLeadActivateCount,
                        dbgSecureLeadIsSpeedUpMinorBonus: horse.DbgHorseAI.DbgSecureLeadIsSpeedUpMinorBonus,
                        dbgSecureLeadSpeedUpMinorBonusNearCharaNum: horse.DbgHorseAI
                            .DbgSecureLeadSpeedUpMinorBonusNearCharaNum,
                        dbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum: horse.DbgHorseAI
                            .DbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum,
                        cachedSkillModifierParamValueArray: SKILL_MODIFIER_PARAM_ENUM_ARRAY,
                        dbgDebuffAbilityValueMultiplyRatio: horse.DbgGetModifierReceiver.DbgSkillParamFluctuate
                            .DbgDebuffAbilityValueMultiplyRatio,
                        dbgDebuffAbilityValueMultiplyOtherActivateRatio: horse.DbgGetModifierReceiver
                            .DbgSkillParamFluctuate.DbgDebuffAbilityValueMultiplyOtherActivateRatio,
                        dbgLastSpeedForStartDashFinishCheck: horse.GetLastSpeedWithoutDebuffSkill()
                    );
                    horseDataArray[i] = new RaceSimulateHorseFrameData(horse.GetDistance(), horse.GetLaneDistance(),
                        horse.GetLastSpeed(), horse.GetHp(), (sbyte)horse.TemptationMode,
                        (sbyte)(null != horse.GetBlockHorseFront()
                            ? horse.GetBlockHorseFront().HorseIndex
                            : BLOCK_HORSEINDEX_NULL), dbgData);

                    // 後処理
                    horse.DbgAddHp = 0;
                    horse.DbgHorseAI.ClearDbgConservePowerIncreaseTimeDictPerRecord();
                    horse.DbgHorseAI.ClearDbgConservePowerDecreaseTimeDictPerRecord();
                }
                else
                {
                    horseDataArray[i] = new RaceSimulateHorseFrameData(horse.GetDistance(), horse.GetLaneDistance(), horse.GetLastSpeed(), horse.GetHp(), (sbyte)horse.TemptationMode,
                        (sbyte)(null != horse.GetBlockHorseFront() ? horse.GetBlockHorseFront().HorseIndex : BLOCK_HORSEINDEX_NULL));
                }
#else
                horseDataArray[i] = new RaceSimulateHorseFrameData(horse.GetDistance(), horse.GetLaneDistance(), horse.GetLastSpeed(), horse.GetHp(), (sbyte)horse.TemptationMode,
                    (sbyte)(null != horse.GetBlockHorseFront() ? horse.GetBlockHorseFront().HorseIndex : BLOCK_HORSEINDEX_NULL));
#endif
            }
            var newFrame = new RaceSimulateFrameData(_curTime, horseDataArray);
            _simData.Record(newFrame);
        }
        
#if CYG_DEBUG
        //---------------------------------------------------------------
        /// <summary>
        /// 【デバッグ用】デバッグ用情報の準備
        /// </summary>
        //---------------------------------------------------------------
        private Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>> PrepareModifierDict(IHorseRaceInfoSimulate horse)
        {
            // modifierDict準備
            var modifierDict = new Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>>();
            foreach (Gallop.SkillDefine.SkillModifierParam type in SKILL_MODIFIER_PARAM_ENUM_ARRAY)
            {
                var modifiers = horse.DbgGetModifier(type);
                modifierDict.Add(type, modifiers);
            }
            return modifierDict;
        }

        private Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>> PrepareCancelledModifierDict(IHorseRaceInfoSimulate horse)
        {
            // cancelledModifierDict準備
            var cancelledModifierDict = new Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>>();
            foreach (Gallop.SkillDefine.SkillModifierParam type in SKILL_MODIFIER_PARAM_ENUM_ARRAY)
            {
                var modifiers = horse.DbgGetCancelledModifier(type);
                cancelledModifierDict.Add(type, modifiers);
            }
            return cancelledModifierDict;
        }

        private RaceSimulateHorseFrameDebugData.SkillStatus[] PrepareCurrentSkillStatusArray(IHorseRaceInfoSimulate horse)
        {
            // currentSkillStatusArray準備
            var skillArray = horse.GetSkills();
            var currentSkillStatusArray = new RaceSimulateHorseFrameDebugData.SkillStatus[skillArray.Length];
            for (int m = 0; m < skillArray.Length; ++m)
            {
                int skillDetailCount = skillArray[m].Details.Count;
                currentSkillStatusArray[m] = new RaceSimulateHorseFrameDebugData.SkillStatus()
                {
                    SkillId = skillArray[m].SkillMasterId,
                    CoolDownTime = skillArray[m].CoolDownTime,
                    SkillDetailCoolDownTimeArray = new float[skillDetailCount],
                };
                var isContainsUseSkillDetailCoolDownTime = false; // このスキルは、以下のどちらなのかを区別したい：全てのスキル効果でIsUseSkillDetailCoolDownTimeがfalse or 1つ以上trueがある
                for (int n = 0; n < skillDetailCount; n++)
                {
                    currentSkillStatusArray[m].SkillDetailCoolDownTimeArray[n] = skillArray[m].Details[n].CoolDownTime;
                    isContainsUseSkillDetailCoolDownTime |= skillArray[m].Details[n].IsUseSkillDetailCoolDownTime;
                }

                currentSkillStatusArray[m].IsContainsUseSkillDetailCoolDownTime = isContainsUseSkillDetailCoolDownTime;
            }
            return currentSkillStatusArray;
        }

        private List<RaceSimulateHorseFrameDebugData.CompeteFightNear> PrepareCompeteFightNearList(IHorseRaceInfoSimulate horse)
        {
            // competeFightNearList準備(使われていないので消してもOK)
            var competeFightNearList = new List<RaceSimulateHorseFrameDebugData.CompeteFightNear>();
            foreach (var competeNear in horse.CompeteFightNearList)
            {
                var near = new RaceSimulateHorseFrameDebugData.CompeteFightNear(competeNear.infoSimulate.HorseIndex, competeNear.CompeteFightCandidateContinueTime);
                competeFightNearList.Add(near);
            }
            return competeFightNearList;
        }

        private bool[] PrepareIsOrderInContinueArray(IHorseRaceInfoSimulate horse)
        {
            var isOrderInContinue = new bool[RaceUtilEnum.GetEnumElementCount<Gallop.SkillDefine.OrderInType>()];
            for (int j = 0; j < isOrderInContinue.Length; j++)
            {
                isOrderInContinue[j] = horse.IsOrderInContinue((Gallop.SkillDefine.OrderInType)j);
            }
            return isOrderInContinue;
        }
#endif

        //---------------------------------------------------------------
        /// <summary>
        /// 現在の記録時間加算。
        /// </summary>
        //---------------------------------------------------------------
        public void AddCurrentTime(float elapsedTime)
        {
            _curTime += elapsedTime;
        }

        public RaceSimulateData SimulateData
        {
            get { return _simData; }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// シミュレーション中に発生したイベントを書き込み。
        /// </summary>
        //---------------------------------------------------------------
        public void AddEvent(SimulateEventType type, params int[] _params)
        {
            AddEvent(_curTime, type, _params);
        }
        public void AddEvent(float curTime, SimulateEventType type, params int[] _params)
        {
            _simData.AddEvent(curTime, type, _params);
        }

    #if false // デバッグ確認用の全イベントログ出力。
        private void OutputEv( List<RaceSimulateEventData> evList )
        {
            var strBuf = new System.Text.StringBuilder();
            foreach( var ev in evList )
            {
                strBuf.AppendLine( string.Format( "TYPE={0} TIME={1}", ev.type, ev.frameTime ) );
            }
            Debug.Log( strBuf.ToString() );
        }
    #endif
    }

}
#endif
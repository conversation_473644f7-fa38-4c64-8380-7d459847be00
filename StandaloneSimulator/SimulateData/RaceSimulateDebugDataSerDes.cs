#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
using System;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateDataが所持するデバッグ情報のシリアライズ・デシリアライズ処理。
    /// </summary>
    //-----------------------------------------------------------------------
    public partial class RaceSimulateDebugData
    {
        /// <summary>
        /// byte配列にシリアライズ。
        /// </summary>
        public byte[] Serialize()
        {
            //-----------------------------------------------------------
            // デバッグ変数をbyte配列で詰める。
            //-----------------------------------------------------------
            List<byte[]> tmpBytes = new List<byte[]>(128);

        #if !STANDALONE_SIMULATOR
            // バイナリからJson出力するときにstg環境等だとバージョンが0のままシリアライズされて
            // 出力されたJsonをデシリアライズするときにバージョンが0なのでデバッグデータがデシリアライズされずズレが発生してしまう。
            // なのでバージョンが0だったら現在のバージョンを疑似的に入れてデシリアライズ時のズレを無くす。
            if (Version == 0)
            {
                Version = CUR_VERSION;
            }
        #endif

            tmpBytes.Add(BitConverter.GetBytes(Version));

            tmpBytes.Add( BitConverter.GetBytes(DbgTeamList.Count) );

            foreach (var team in DbgTeamList)
            {
                tmpBytes.Add(BitConverter.GetBytes(team.TeamId));
                tmpBytes.Add(BitConverter.GetBytes(team.TeamMemberHorseIndexArray.Length));
                foreach (var horseIndex in team.TeamMemberHorseIndexArray)
                {
                    tmpBytes.Add(BitConverter.GetBytes(horseIndex));
                }
                tmpBytes.Add(BitConverter.GetBytes(team.ScoreList.Count));
                foreach (var teamScore in team.ScoreList)
                {
                    tmpBytes.Add(BitConverter.GetBytes(teamScore.raw_score_id));
                    tmpBytes.Add(BitConverter.GetBytes(teamScore.score));

                    tmpBytes.Add(BitConverter.GetBytes(teamScore.bonus_num));
                    foreach (var bonus in teamScore.bonus_array)
                    {
                        tmpBytes.Add(BitConverter.GetBytes(bonus.score_bonus_id));
                        tmpBytes.Add(BitConverter.GetBytes(bonus.bonus_score));
                    }
                }
            }

            var tagBytes = SIMULATE_TAG_ENCODING.GetBytes(DbgSimulateTag);
            tmpBytes.Add(BitConverter.GetBytes(tagBytes.Length));
            tmpBytes.Add(tagBytes);
            
            tmpBytes.Add(BitConverter.GetBytes((int)DbgSimulateEnvironment));

            //-----------------------------------------------------------
            // デバッグ変数全部のバイト数。
            //-----------------------------------------------------------
            int totalBytes = tmpBytes.Sum( b => b.Length );
        
            //-----------------------------------------------------------
            // 返却用のbyte配列に全部詰める。
            //-----------------------------------------------------------
            var retBytes = new byte[ totalBytes ];
            int offset = 0;
            foreach( var bytes in tmpBytes )
            {
                Array.Copy( bytes, 0, retBytes, offset, bytes.Length );   
                offset += bytes.Length;
            }
            return retBytes;
        }

        /// <summary>
        /// byte配列からデバッグ情報を復元。
        /// </summary>
        private void Deserialize(byte[] bytes, ref int offset)
        {
            Version = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);

            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if (Version >= VERSION_10000000)
            {
                Deserialize_Ver10000000_OrNewer(bytes, ref offset);
            }
            //-----------------------------------------------------------
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
        }

        private void Deserialize_Ver10000000_OrNewer(byte[] bytes, ref int offset)
        {
            int teamNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgTeamList = new List<TeamData>(teamNum);
            for (int i = 0; i < teamNum; ++i)
            {
                var newTeam = new TeamData();
                newTeam.TeamId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                int teamMemberNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                newTeam.TeamMemberHorseIndexArray = new int[teamMemberNum];
                for (int j = 0; j < newTeam.TeamMemberHorseIndexArray.Length; ++j)
                {
                    newTeam.TeamMemberHorseIndexArray[j] = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                }

                int scoreNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                newTeam.ScoreList = new List<ScoreData>(scoreNum);
                for (int j = 0; j < scoreNum; ++j)
                {
                    int rawScoreId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    var scoreData = new ScoreData(rawScoreId);
                    scoreData.num = 1;
                    scoreData.score = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);

                    int bonusNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    for (int bonusIndex = 0; bonusIndex < bonusNum; ++bonusIndex)
                    {
                        int scoreBonusId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                        int bonusScore = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                        var bonus = new ScoreBonusData(scoreBonusId, bonusScore);
                        scoreData.AddArray(bonus);    
                    }
                    newTeam.ScoreList.Add(scoreData);
                }
                DbgTeamList.Add(newTeam);
            }
            
            // シミュレート識別子。※SIMULATE_DATA_VERSION_20200330で追加。
            int simulateTagByteNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            var simulateTagByteArray = new byte[simulateTagByteNum];
            Array.Copy(bytes, offset, simulateTagByteArray, 0, simulateTagByteArray.Length);
            offset += simulateTagByteArray.Length;
            DbgSimulateTag = SIMULATE_TAG_ENCODING.GetString(simulateTagByteArray);

            // シミュレートした環境。※SIMULATE_DATA_VERSION_20200330で追加。
            DbgSimulateEnvironment = (SimulateEnvironment)RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
        }
        
    }
}
#endif
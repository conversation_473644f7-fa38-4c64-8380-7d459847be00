namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateHorseFrameDataのシリアライズ・デシリアライズ処理。
    /// </summary>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateHorseFrameData
    {
        private const float SpeedAccuracy = 100.0f;     //少数第二位までの精度を保証する
        private const float LaneAccuracy = 10000.0f;    //少数第四位までの精度を保証する

        /// <summary>
        /// このクラスのシリアライズに必要なバイト数計算。
        /// </summary>
        public static int CalcSize()
        {
            int numBytes = 0;
            numBytes += sizeof(float);      // distance。
            numBytes += sizeof(ushort);     // lanePosition(float -> ushort)。
            numBytes += sizeof(ushort);     // speed(float -> ushort)。
            numBytes += sizeof(ushort);     // hp(float -> ushort)。
            numBytes += sizeof(sbyte);      // temptationHorseIndex。
            numBytes += sizeof(sbyte);      // blockFrontHorseIndex。
            return numBytes;
        }
        
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        /// <summary>
        /// 自身をバイト配列に変換。
        /// </summary>
        public void Serialize(ref byte[] retByteArray, ref int destOffset)
        {
            // レーンは２桁いかないはず。
            Debug.Assert(LanePosition < 10.0f);
            // スピードは3桁いかないはず。リリース時最大値25。運用中の最大値30までということなので。
            Debug.Assert(Speed < 100);

            //-----------------------------------------------------------
            // byte配列にデータを詰める。
            //-----------------------------------------------------------
            RaceUtil.FloatToBytesAddOffset(Distance, ref retByteArray, ref destOffset);
            RaceUtil.UInt16ToBytesAddOffset((ushort)(LanePosition * LaneAccuracy), ref retByteArray, ref destOffset);
            RaceUtil.UInt16ToBytesAddOffset((ushort)(Speed * SpeedAccuracy), ref retByteArray, ref destOffset);
            RaceUtil.UInt16ToBytesAddOffset((ushort)Hp, ref retByteArray, ref destOffset);
            RaceUtil.ByteToBytesAddOffset((byte)TemptationMode, ref retByteArray, ref destOffset);
            RaceUtil.ByteToBytesAddOffset((byte)BlockFrontHorseIndex, ref retByteArray, ref destOffset);
        }
#endif
        
        /// <summary>
        /// バイト配列から自身を復元。
        /// </summary>
        public void Deserialize(byte[] bytes, int version)
        {
            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if (version >= RaceSimulateDataHeader.SIMULATE_DATA_VERSION_20201027)
            {
                Deserialize_Ver20201027_OrNewer(bytes);
            }
            //-----------------------------------------------------------
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
            else if (version >= RaceSimulateDataHeader.SIMULATE_DATA_VERSION_20200406)
            {
                Deserialize_Ver20200406_OrNewer(bytes);
            }
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver20201027_OrNewer(byte[] bytes)
        {
            int offset = 0;

            RaceUtil.BytesToFloatAddOffset(bytes, ref Distance, ref offset);
            LanePosition = (float)RaceUtil.BytesToUInt16AddOffset(bytes, ref offset) / LaneAccuracy;
            Speed = (float)RaceUtil.BytesToUInt16AddOffset(bytes, ref offset) / SpeedAccuracy;
            Hp = RaceUtil.BytesToUInt16AddOffset(bytes, ref offset);
            RaceUtil.BytesToSByteAddOffset(bytes, ref TemptationMode, ref offset);
            RaceUtil.BytesToSByteAddOffset(bytes, ref BlockFrontHorseIndex, ref offset);
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver20200406_OrNewer(byte[] bytes)
        {
            int offset = 0;

            RaceUtil.BytesToFloatAddOffset(bytes, ref Distance, ref offset);
            LanePosition = (float)RaceUtil.BytesToUInt16AddOffset(bytes, ref offset) / LaneAccuracy;
            Speed = (float)RaceUtil.BytesToUInt16AddOffset(bytes, ref offset) / SpeedAccuracy;
            Hp = RaceUtil.BytesToUInt16AddOffset(bytes, ref offset);
            RaceUtil.BytesToSByteAddOffset(bytes, ref TemptationMode, ref offset);
            RaceUtil.BytesToByteAddOffset(bytes, ref offset); // 旧IsPushed。仕様カットで変数消えたのでオフセット進めるだけ。
            RaceUtil.BytesToSByteAddOffset(bytes, ref BlockFrontHorseIndex, ref offset);
        }

#if CYG_DEBUG
        /// <summary>
        /// デバッグ情報をbyte配列に変換。
        /// </summary>
        public byte[] SerializeDebug()
        {
            return DbgData != null ? DbgData.Serialize() : new byte[0];
        }

        /// <summary>
        /// byte配列からデバッグ情報を復元。
        /// </summary>
        public void DeserializeDebug(byte[] bytes, ref int offset)
        {
            DbgData = new RaceSimulateHorseFrameDebugData(bytes, ref offset);
        }
#endif        
    }
}
#if CYG_DEBUG
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateHorseResultDataが所持するデバッグ情報。
    /// </summary>
    //-----------------------------------------------------------------------
    public partial class RaceSimulateHorseResultDebugData
    {
        //-------------------------------------------------------------------
        // 定義。
        //-------------------------------------------------------------------
        public const int VERSION_10000000 = 10000000;
        public const int VERSION_10000001 = 10000001;
        public const int VERSION_10000002 = 10000002;
        public const int VERSION_10000003 = 10000003;
        public const int VERSION_10000004 = 10000004;
        public const int VERSION_10000005 = 10000005;
        public const int VERSION_10000006 = 10000006;
        public const int VERSION_10000007 = 10000007;
        public const int CUR_VERSION = VERSION_10000007;

        public class CompeteGroupHorse
        {
            public readonly int HorseIndex;
            public readonly float DistanceDiff;
            public readonly float LaneDistanceDiff;
            public readonly int Group;

            public CompeteGroupHorse(int horseIndex, float distanceDiff, float laneDistanceDiff, int group)
            {
                HorseIndex = horseIndex;
                DistanceDiff = distanceDiff;
                LaneDistanceDiff = laneDistanceDiff;
                Group = group;
            }

            public CompeteGroupHorse(CompeteGroupHorse from)
            {
                HorseIndex = from.HorseIndex;
                DistanceDiff = from.DistanceDiff;
                LaneDistanceDiff = from.LaneDistanceDiff;
                Group = from.Group;
            }
        }
        
        //-------------------------------------------------------------------
        // 変数。
        //-------------------------------------------------------------------
        public int Version;

        public byte DbgFinalGradeHigherCount = 0;
        public byte DbgFinalGradeLowerCount = 0;
        public byte DbgIsRunningStyleCountMax = 0;

        public byte DbgPopularity = 0;
        public byte DbgPopularityRankLeft = 0;
        public byte DbgPopularityRankCenter = 0;
        public byte DbgPopularityRankRight = 0;

        public byte DbgIsRunningStyleEqualPopularityOne = 0;
        public byte DbgIsGoodStart = 0;
        public byte DbgIsBadStart = 0;

        public byte DbgIsHpEmpty = 0;
        public byte DbgTemptationCount = 0;
        public byte DbgPositionOvertakeCount = 0;
        public int DbgLastSpurtCalcResult = 0;
        public float DbgFrontBlockAccumulateTime = 0;

        public float DbgBaseSpeedWithMotivation;
        public float DbgBaseStaminaWithMotivation;
        public float DbgBasePowWithMotivation;
        public float DbgBaseGutsWithMotivation;
        public float DbgBaseWizWithMotivation;
        public float DbgBaseSpeedWithMotivationRaceType;
        public float DbgBaseStaminaWithMotivationRaceType;
        public float DbgBasePowWithMotivationRaceType;
        public float DbgBaseGutsWithMotivationRaceType;
        public float DbgBaseWizWithMotivationRaceType;

        public float DbgSpeedOnStart;
        public float DbgStaminaOnStart;
        public float DbgPowOnStart;
        public float DbgGutsOnStart;
        public float DbgWizOnStart;
        
        public List<ScoreBonusRate> DbgBonusRateList;
        public int DbgTeamStadiumTotalScore;
        
        public float DbgStartDelayTimeBase = 0.0f; // スタート遅延時間のスキル効果反映前の値。

        public List<ScoreData> DbgScoreList = new List<ScoreData>();
        
        public List<ReceiveHistory> DbgReceiveHistoryList = new List<ReceiveHistory>();
        
        public List<LastSpurtCandidate> DbgCandidateList { get; private set; } = new List<LastSpurtCandidate>();
        public LastSpurtCandidate DbgUseCandidate { get; private set; } = new LastSpurtCandidate();
        public float[] DbgLastSpurtLastCalculatedSpeedArray { get; private set; } = null;
        public int DbgLastSpurtCalcCount;
        public float DbgLastMoveOutStartDistance;

        public Gallop.RaceDefine.RunningStyleEx DbgRunningStyleEx = Gallop.RaceDefine.RunningStyleEx.None;
        public List<CompeteGroupHorse> DbgCompeteTopStartGroupHorseList = new List<CompeteGroupHorse>();
        public List<CompeteGroupHorse> DbgCompeteTopEndGroupHorseList = new List<CompeteGroupHorse>();
        public List<CompeteGroupHorse> DbgCompeteFightStartGroupHorseList = new List<CompeteGroupHorse>();
        public List<CompeteGroupHorse> DbgCompeteFightEndGroupHorseList = new List<CompeteGroupHorse>();

        public float DbgBaseTargetSpeedRandomMin;
        public float DbgBaseTargetSpeedRandomMax;
        
        public float DbgCourseSetBaseSpeedCoef;
        /// <summary> 足溜め効果時間 </summary>
        public float DbgConservePowerActivityTime;
        
    #region <パラメータ上限突破スタミナ>
        /// <summary> パラメータ上限突破スタミナ目指す速度加算値 </summary>
        public float DbgStaminaLimitBreakBuffAddTargetSpeed;
        /// <summary> パラメータ上限突破スタミナ抽選された揺らぎ幅テーブル </summary>
        public Gallop.RaceDefine.RandomTableType DbgStaminaLimitBreakBuffLotteryRandomTableType;
        /// <summary> パラメータ上限突破スタミナ抽選された揺らぎ幅テーブルの各タイプによる抽選確率 </summary>
        public Dictionary<Gallop.RaceDefine.RandomTableType, float> DbgStaminaLimitBreakBuffRandomTableProbabilityDict;
        /// <summary> パラメータ上限突破スタミナ揺らぎ幅テーブルで使用することになった重み </summary>
        public float DbgStaminaLimitBreakBuffRandomTableCoef;
        /// <summary> パラメータ上限突破スタミナ効果発動時間 </summary>
        public float DbgStaminaLimitBreakBuffActivateTime;
        /// <summary> パラメータ上限突破スタミナ効果終了時間 </summary>
        public float DbgStaminaLimitBreakBuffFinishTime;
    #endregion // パラメータ上限突破スタミナ

        public DbgOverTakeLog[] DbgOrderChangeCounterUpOverTakeLogArray { get; private set; } = null;
        public DbgOverTakeLog[] DbgOrderChangeCounterDownOverTakeLogArray { get; private set; } = null;
    
        //-------------------------------------------------------------------
        // 関数。
        //-------------------------------------------------------------------
        public RaceSimulateHorseResultDebugData()
        {
        }
        
        /// <summary>
        /// コンストラクタ
        /// RaceSimulateDataへの書き込みはWriterクラスに一任すること
        /// </summary>
        public RaceSimulateHorseResultDebugData(int finalGradeHigherCount, int finalGradeLowerCount, bool isRunningStyleCountMax,
            int popularity, int popularityRankLeft, int popularityRankCenter, int popularityRankRight,
            bool isRunningStyleEqualPopularityOne, bool isGoodStart, bool isBadStart,
            bool isHpEmpty, int temptationCount, int positionOvertakeCount, LastSpurtCalcResult lastSpurtCalcResult, float frontBlockAccumulateTime,
            float baseSpeed, float baseStamina, float basePow, float baseGuts, float baseWiz, 
            float baseSpeedWithMotivationRaceType, float baseStaminaWithMotivationRaceType, float basePowWithMotivationRaceType, float baseGutsWithMotivationRaceType, float baseWizWithMotivationRaceType, 
            float speedOnStart, float staminaOnStart, float powOnStart, float gutsOnStart, float wizOnStart, 
            List<ScoreBonusRate> bonusRateList, int teamStadiumTotalScore,
            float startDelayTimeBase, 
            List<ScoreData> scoreList, List<ReceiveHistory> receiveHistoryList,
            List<LastSpurtCandidate> lastSpurtCandidateList,
            LastSpurtCandidate lastSpurtUseCandidate,
            Queue<float> lastSpurtLastCalculatedSpeedQueue,
            int lastSpurtCalcCount, 
            float lastMoveOutStartDistance, Gallop.RaceDefine.RunningStyleEx runningStyleEx,
            List<CompeteGroupHorse> competeTopStartGroupHorseList,
            List<CompeteGroupHorse> competeTopEndGroupHorseList,
            List<CompeteGroupHorse> competeFightStartGroupHorseList,
            List<CompeteGroupHorse> competeFightEndGroupHorseList,
            float baseTargetSpeedRandomMin, float baseTargetSpeedRandomMax,
            float courseSetBaseSpeedCoef, float conservePowerActivityTime,
            float staminaLimitBreakBuffAddTargetSpeed,
            Gallop.RaceDefine.RandomTableType staminaLimitBreakBuffLotteryRandomTableType,
            Dictionary<Gallop.RaceDefine.RandomTableType,float> staminaLimitBreakBuffRandomTableProbabilityDict,
            float staminaLimitBreakBuffRandomTableCoef,
            float staminaLimitBreakBuffActivateTime, float staminaLimitBreakBuffFinishTime,
            DbgOverTakeLog[] orderChangeCounterUpOrderChangeCounterUpOverTakeLogArray, DbgOverTakeLog[] orderChangeCounterDownOrderChangeCounterDownOverTakeLogArray
            )
        {
            Version = CUR_VERSION;

            DbgFinalGradeHigherCount = (byte)finalGradeHigherCount;
            DbgFinalGradeLowerCount = (byte)finalGradeLowerCount;
            DbgIsRunningStyleCountMax = (byte)(isRunningStyleCountMax ? 1 : 0);

            DbgPopularity = (byte)popularity;
            DbgPopularityRankLeft = (byte)popularityRankLeft;
            DbgPopularityRankCenter = (byte)popularityRankCenter;
            DbgPopularityRankRight = (byte)popularityRankRight;

            DbgIsRunningStyleEqualPopularityOne = (byte)(isRunningStyleEqualPopularityOne ? 1 : 0);
            DbgIsGoodStart = (byte)(isGoodStart ? 1 : 0);
            DbgIsBadStart = (byte)(isBadStart ? 1 : 0);

            DbgIsHpEmpty = (byte)(isHpEmpty ? 1 : 0);
            DbgTemptationCount = (byte)temptationCount;
            DbgPositionOvertakeCount = (byte)positionOvertakeCount;
            DbgLastSpurtCalcResult = (int)lastSpurtCalcResult;
            DbgFrontBlockAccumulateTime = frontBlockAccumulateTime;

            DbgBaseSpeedWithMotivation = baseSpeed;
            DbgBaseStaminaWithMotivation = baseStamina;
            DbgBasePowWithMotivation = basePow;
            DbgBaseGutsWithMotivation = baseGuts;
            DbgBaseWizWithMotivation = baseWiz;

            DbgBaseSpeedWithMotivationRaceType = baseSpeedWithMotivationRaceType;
            DbgBaseStaminaWithMotivationRaceType = baseStaminaWithMotivationRaceType;
            DbgBasePowWithMotivationRaceType = basePowWithMotivationRaceType;
            DbgBaseGutsWithMotivationRaceType = baseGutsWithMotivationRaceType;
            DbgBaseWizWithMotivationRaceType = baseWizWithMotivationRaceType;

            DbgSpeedOnStart = speedOnStart;
            DbgStaminaOnStart = staminaOnStart;
            DbgPowOnStart = powOnStart;
            DbgGutsOnStart = gutsOnStart;
            DbgWizOnStart = wizOnStart;
            
            DbgBonusRateList = bonusRateList;
            DbgTeamStadiumTotalScore = teamStadiumTotalScore;

            DbgStartDelayTimeBase = startDelayTimeBase;
            
            DbgScoreList.Clear();
            if (scoreList != null)
            {
                foreach (var scoreFrom in scoreList)
                {
                    var scoreData = new ScoreData(scoreFrom.raw_score_id, scoreFrom.num, scoreFrom.score, scoreFrom.bonus_array);
                    DbgScoreList.Add(scoreData);
                }
            }
            
            DbgReceiveHistoryList.Clear();
            if (receiveHistoryList != null)
            {
                foreach (var historyFrom in receiveHistoryList)
                {
                    var history = new ReceiveHistory(historyFrom);
                    DbgReceiveHistoryList.Add(history);
                }
            }
            
            DbgCandidateList.Clear();
            if (lastSpurtCandidateList != null)
            {
                foreach (var candidate in lastSpurtCandidateList)
                {
                    var tmpCandidate = new LastSpurtCandidate()
                    {
                        distance = candidate.distance,
                        speed = candidate.speed,
                        needTime = candidate.needTime,
                    };
                    DbgCandidateList.Add(tmpCandidate);
                }
            }

            if (lastSpurtUseCandidate != null)
            {
                DbgUseCandidate = new LastSpurtCandidate()
                {
                    distance = lastSpurtUseCandidate.distance,
                    speed = lastSpurtUseCandidate.speed,
                    needTime = lastSpurtUseCandidate.needTime,
                };
            }

            if (lastSpurtLastCalculatedSpeedQueue != null)
            {
                DbgLastSpurtLastCalculatedSpeedArray = lastSpurtLastCalculatedSpeedQueue.ToArray();
            }

            DbgLastSpurtCalcCount = lastSpurtCalcCount;

            DbgLastMoveOutStartDistance = lastMoveOutStartDistance;
            DbgRunningStyleEx = runningStyleEx;
            
            if (competeTopStartGroupHorseList != null)
            {
                foreach (var groupHorse in competeTopStartGroupHorseList)
                {
                    DbgCompeteTopStartGroupHorseList.Add(new CompeteGroupHorse(groupHorse));
                }
            }
            if (competeTopEndGroupHorseList != null)
            {
                foreach (var groupHorse in competeTopEndGroupHorseList)
                {
                    DbgCompeteTopEndGroupHorseList.Add(new CompeteGroupHorse(groupHorse));
                }
            }
            
            if (competeFightStartGroupHorseList != null)
            {
                foreach (var groupHorse in competeFightStartGroupHorseList)
                {
                    DbgCompeteFightStartGroupHorseList.Add(new CompeteGroupHorse(groupHorse));
                }
            }
            if (competeFightEndGroupHorseList != null)
            {
                foreach (var groupHorse in competeFightEndGroupHorseList)
                {
                    DbgCompeteFightEndGroupHorseList.Add(new CompeteGroupHorse(groupHorse));
                }
            }

            DbgBaseTargetSpeedRandomMin = baseTargetSpeedRandomMin;
            DbgBaseTargetSpeedRandomMax = baseTargetSpeedRandomMax;

            DbgCourseSetBaseSpeedCoef = courseSetBaseSpeedCoef;
            DbgConservePowerActivityTime = conservePowerActivityTime;
            
            DbgStaminaLimitBreakBuffAddTargetSpeed = staminaLimitBreakBuffAddTargetSpeed;
            DbgStaminaLimitBreakBuffLotteryRandomTableType = staminaLimitBreakBuffLotteryRandomTableType;
            DbgStaminaLimitBreakBuffRandomTableProbabilityDict = staminaLimitBreakBuffRandomTableProbabilityDict;
            DbgStaminaLimitBreakBuffRandomTableCoef = staminaLimitBreakBuffRandomTableCoef;
            
            DbgStaminaLimitBreakBuffActivateTime = staminaLimitBreakBuffActivateTime;
            DbgStaminaLimitBreakBuffFinishTime = staminaLimitBreakBuffFinishTime;

            DbgOrderChangeCounterUpOverTakeLogArray = orderChangeCounterUpOrderChangeCounterUpOverTakeLogArray;
            DbgOrderChangeCounterDownOverTakeLogArray = orderChangeCounterDownOrderChangeCounterDownOverTakeLogArray;
        }
        
        public RaceSimulateHorseResultDebugData(byte[] bytes, ref int offset)
        {
            Deserialize(bytes, ref offset);
        }
    }
}
#endif

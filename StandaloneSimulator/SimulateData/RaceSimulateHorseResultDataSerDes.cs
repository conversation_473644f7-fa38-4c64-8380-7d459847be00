namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateHorseResultDataのシリアライズ・デシリアライズ処理。
    /// </summary>
    //-----------------------------------------------------------------------
    public sealed partial class RaceSimulateHorseResultData
    {
        /// <summary>
        /// このクラスのシリアライズに必要なバイト数計算。
        /// </summary>
        public static int CalcSize()
        {
            int numBytes = 0;
            numBytes += sizeof(int);                        // 着順。
            numBytes += sizeof(float);                      // 着タイム。
            numBytes += sizeof(float);                      // 前の馬との着タイム差。
            numBytes += sizeof(float);                      // スタート遅延。
            numBytes += sizeof(byte);                       // 根性相対順位。
            numBytes += sizeof(byte);                       // 賢さ相対順位。
            numBytes += sizeof(float);                      // ラストスパート開始距離。
            numBytes += sizeof(byte);                       // 走法。
            numBytes += sizeof(int);                        // 敗因。
            numBytes += sizeof(int);                        // 着タイム（スケール前）。
            return numBytes;
        }
        
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
        /// <summary>
        /// 自身をバイト配列に変換。
        /// </summary>
        public byte[] Serialize()
        {
            //-----------------------------------------------------------
            // 自身１件あたりのバイト数計算。
            //-----------------------------------------------------------
            int numBytes = CalcSize();

            //-----------------------------------------------------------
            // byte配列にデータを詰める。
            //-----------------------------------------------------------
            byte[] retBytes = new byte[numBytes];
            int destOffset = 0;

            // 着順。
            RaceUtil.Int32ToBytesAddOffset( FinishOrder, ref retBytes, ref destOffset );
            // 着タイム。
            RaceUtil.FloatToBytesAddOffset( FinishTime, ref retBytes, ref destOffset );
            // 前の馬との着タイム差。
            RaceUtil.FloatToBytesAddOffset( FinishDiffTime, ref retBytes, ref destOffset );
            // スタート遅延。
            RaceUtil.FloatToBytesAddOffset( StartDelayTime, ref retBytes, ref destOffset );
            // 根性相対順位。
            RaceUtil.ByteToBytesAddOffset( GutsOrder, ref retBytes, ref destOffset );
            // 賢さ相対順位。
            RaceUtil.ByteToBytesAddOffset( WizOrder, ref retBytes, ref destOffset );
            // ラストスパート開始距離。
            RaceUtil.FloatToBytesAddOffset( LastSpurtStartDistance, ref retBytes, ref destOffset );
            // 走法。
            RaceUtil.ByteToBytesAddOffset( RunningStyle, ref retBytes, ref destOffset );
            // 敗因。
            RaceUtil.Int32ToBytesAddOffset( Defeat, ref retBytes, ref destOffset );
            // 着タイム（スケール前）。
            RaceUtil.FloatToBytesAddOffset(FinishTimeRaw, ref retBytes, ref destOffset);

            return retBytes;
        }
#endif

        /// <summary>
        /// バイト配列から自身を復元。
        /// </summary>
        public void Deserialize(byte[] bytes, int version)
        {
            int offset = 0;
            
            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if( version >= RaceSimulateDataHeader.SIMULATE_DATA_VERSION_20200406)
            {
                Deserialize_Ver20200406_OrNewer(bytes, ref offset);
            }
            //-----------------------------------------------------------
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver20200406_OrNewer(byte[] bytes, ref int offset)
        {
            // 着順。
            RaceUtil.BytesToInt32AddOffset( bytes, ref FinishOrder, ref offset );
            // 着タイム。
            RaceUtil.BytesToFloatAddOffset( bytes, ref FinishTime, ref offset );
            // 前の馬との着タイム差。
            RaceUtil.BytesToFloatAddOffset( bytes, ref FinishDiffTime, ref offset );
            // スタート遅延。
            RaceUtil.BytesToFloatAddOffset( bytes, ref StartDelayTime, ref offset );
            // 根性相対順位。
            RaceUtil.BytesToByteAddOffset( bytes, ref GutsOrder, ref offset );
            // 賢さ相対順位。
            RaceUtil.BytesToByteAddOffset( bytes, ref WizOrder, ref offset );
            // ラストスパート開始距離。
            RaceUtil.BytesToFloatAddOffset( bytes, ref LastSpurtStartDistance, ref offset );
            // 走法。
            RaceUtil.BytesToByteAddOffset( bytes, ref RunningStyle, ref offset );
            // 敗因。 ※Ver20180910で追加。
            RaceUtil.BytesToInt32AddOffset( bytes, ref Defeat, ref offset );
            
            // スケール前着タイム。
            RaceUtil.BytesToFloatAddOffset(bytes, ref FinishTimeRaw, ref offset);
        }
    }
}
#if CYG_DEBUG
using System;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-----------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateHorseFrameDataが所持するデバッグ情報のシリアライズ・デシリアライズ処理。
    /// </summary>
    //-----------------------------------------------------------------------
    public partial class RaceSimulateHorseFrameDebugData
    {
        /// <summary>
        /// 自身をバイト配列に変換。
        /// </summary>
        public byte[] Serialize()
        {
            //-----------------------------------------------------------
            // デバッグ変数をbyte配列で詰める。
            //-----------------------------------------------------------
            List<byte[]> tmpBytes = new List<byte[]>(1024);

        #if !STANDALONE_SIMULATOR
            // バイナリからJson出力するときにstg環境等だとバージョンが0のままシリアライズされて
            // 出力されたJsonをデシリアライズするときにバージョンが0なのでデバッグデータがデシリアライズされずズレが発生してしまう。
            // なのでバージョンが0だったら現在のバージョンを疑似的に入れてデシリアライズ時のズレを無くす。
            if (Version < CUR_VERSION)
            {
                Version = CUR_VERSION;
            }
        #endif

            tmpBytes.Add(BitConverter.GetBytes(Version));

            // 狙いたい速度。
            tmpBytes.Add(BitConverter.GetBytes(DbgTargetSpeed));
            // 発動中の特性。
            tmpBytes.Add(new byte[] {(byte) (DbgCurrentActiveSkillArray?.Length ?? 0)});
            if (null != DbgCurrentActiveSkillArray)
            {
                foreach (var skillData in DbgCurrentActiveSkillArray)
                {
                    tmpBytes.Add(BitConverter.GetBytes(skillData.skill_id));
                    tmpBytes.Add(BitConverter.GetBytes(skillData.level));
                }
            }
            // 発動済みの絆スキル/特性。
            if (null != DbgActivatedSkillArray && DbgActivatedSkillArray.Length <= byte.MaxValue)
            {
                tmpBytes.Add(new byte[] {(byte) (DbgActivatedSkillArray?.Length ?? 0)});
                if (null != DbgActivatedSkillArray)
                {
                    foreach (var activatedInfo in DbgActivatedSkillArray)
                    {
                        tmpBytes.Add(BitConverter.GetBytes(activatedInfo.skillId));
                        // 発動スキル効果インデックス ※SIMULATE_DATA_VERSION_20221214で追加
                        tmpBytes.Add(BitConverter.GetBytes(activatedInfo.skillDetailIndex));
                        // ※SIMULATE_DATA_VERSION_20240930で追加
                        tmpBytes.Add(BitConverter.GetBytes(activatedInfo.frameTime));
                    }
                }
            }
            else
            {
                tmpBytes.Add(new byte[] { 0 });
            }
            
            // スキルによるパラメータ変動値。
            tmpBytes.Add(new byte[] {(byte) (DbgSkillModifierDic?.Count ?? 0)});
            if (null != DbgSkillModifierDic)
            {
                foreach (var modifierByType in DbgSkillModifierDic)
                {
                    var modifierList = modifierByType.Value;
                    tmpBytes.Add(new byte[] { (byte)modifierByType.Key });
                    tmpBytes.Add(BitConverter.GetBytes(modifierList.Count));
                    foreach (var modifier in modifierList)
                    {
                        tmpBytes.Add(new byte[] { (byte)modifier.SkillParamModifierType });
                        tmpBytes.Add(new byte[] { (byte)modifier.DbgHorseIndex });
                        tmpBytes.Add(BitConverter.GetBytes(modifier.DbgSkillId));
                        tmpBytes.Add(new byte[] { (byte)modifier.DbgSkillLevel });
                        tmpBytes.Add(BitConverter.GetBytes(modifier.BaseValue));
                        // デバフ無効化スキルが実装されたのでその情報も付随させる
                        tmpBytes.Add(new byte[] {(byte) (modifier.IsValid ? 1 : 0)});
                        // VERSION_10000010で他者が発動したスキルかどうかの情報が追加された
                        tmpBytes.Add(new byte[] {(byte) (modifier.DbgIsActivateOthers ? 1 : 0)});
                    }
                }
            }
            
            // スキルによるパラメータ変動値合計。
            tmpBytes.Add(new byte[] {(byte) (DbgSkillValueParamTotal?.Count ?? 0)});
            if (null != DbgSkillValueParamTotal)
            {
                foreach (var valueParam in DbgSkillValueParamTotal)
                {
                    tmpBytes.Add(new byte[] { (byte)valueParam.Key });
                    tmpBytes.Add(BitConverter.GetBytes(valueParam.Value));
                }
            }
            
            // Phase毎のスキル発動回数。
            tmpBytes.Add(new byte[] {(byte) (DbgActivateSkillCount?.Count ?? 0)});
            if (null != DbgActivateSkillCount)
            {
                foreach (var countByPhase in DbgActivateSkillCount)
                {
                    tmpBytes.Add(new byte[] { (byte)countByPhase.Key });
                    tmpBytes.Add(new byte[] { (byte)countByPhase.Value });
                }
            }
            
            // 前方ブロックされている継続時間。
            tmpBytes.Add(BitConverter.GetBytes(DbgBlockedFrontContinueTime));
            // 前方をHp0にブロックされている継続時間。
            tmpBytes.Add(BitConverter.GetBytes(DbgBlockedFrontHpEmptyContinueTime));
            // サイドブロックされている継続時間。
            tmpBytes.Add(BitConverter.GetBytes(DbgBlockedSideContinueTime));
            
            tmpBytes.Add(new byte[]
            {
                // hibi:使っていないのでbyteを追加したいときに使ってください
                0,
                // 通過中のコーナー。
                DbgCorner,
                // 現在順位。
                DbgCurOrder,
                // 順位上昇回数：中盤。
                DbgCurOrderUpCountPhaseMiddle,
                // 順位上昇回数：終盤以降。
                DbgCurOrderUpCountPhaseEndAfter,
                // 順位上昇回数：コーナー中。 
                DbgCurOrderUpCountCorner,
                // 順位上昇回数：ラストスパート中。
                DbgCurOrderUpCountLastSpurt,
                // 順位上昇回数：特定距離から。※SIMULATE_DATA_VERSION_20200407で追加、SIMULATE_DATA_VERSION_20241028で移動
                DbgCurOrderUpCountDistance1,
                // 順位上昇回数：最終コーナー以降。※SIMULATE_DATA_VERSION_20241028で追加。
                DbgCurOrderUpCountFinalCornerAfter,
                // 順位上昇回数：後半50%以降。※SIMULATE_DATA_VERSION_20241028で追加。
                DbgCurOrderUpCountLaterHalf,
                // 順位下降回数：中盤。
                DbgCurOrderDownCountPhaseMiddle,
                // 順位下降回数：終盤以降。
                DbgCurOrderDownCountPhaseEndAfter,
                // 順位下降回数：コーナー中。
                DbgCurOrderDownCountCorner,
                // 順位下降回数：ラストスパート中。
                DbgCurOrderDownCountLastSpurt,
                // 順位下降回数：特定距離から。※SIMULATE_DATA_VERSION_20200407で追加、SIMULATE_DATA_VERSION_20241028で移動
                DbgCurOrderDownCountDistance1,
                // 順位下降回数：最終コーナー以降。※SIMULATE_DATA_VERSION_20241028で追加。
                DbgCurOrderDownCountFinalCornerAfter,
                // 順位下降回数：後半50%以降。※SIMULATE_DATA_VERSION_20241028で追加。
                DbgCurOrderDownCountLaterHalf,
                // 視野範囲に入っているキャラ数。
                (byte)(DbgVisibleHorseIndexs?.Length ?? 0),
            });
            
            // 視野範囲に入っているキャラ。
            if (null != DbgVisibleHorseIndexs)
            {
                foreach (var horseIndex in DbgVisibleHorseIndexs)
                {
                    tmpBytes.Add(new byte[] { (byte)horseIndex });
                }
            }
            // 近くのキャラ。
            tmpBytes.Add(new byte[] {(byte) (DbgNearHorseIndexs?.Length ?? 0)});
            if (null != DbgNearHorseIndexs)
            {
                foreach (var horseIndex in DbgNearHorseIndexs)
                {
                    tmpBytes.Add(new byte[] { (byte)horseIndex });
                }
            }
            // 近くのキャラが一定数(4)以上継続して存在する時間。
            tmpBytes.Add(BitConverter.GetBytes(DbgNearHorseAroundTime));
            
            tmpBytes.Add(new byte[]
            {
                // ラストスパートの有無。
                DbgIsLastSpurt,
                // 最終コーナー。
                DbgIsFinalCorner,
                // 速すぎる。
                DbgIsTooFast,
                // 遅すぎる。
                DbgIsTooSlow,
                // デバフを受けた。
                DbgIsReceivedDebuff,
                // LaneType。
                DbgLaneType,
                // 順位１後のキャラが自分よりインにいる。
                DbgIsBehindIsIn,
                // 順位１後のキャラが自分よりアウトにいる。
                DbgIsBehindIsOut,
                // 近くのキャラで自分より前。
                DbgNearFrontCount,
                // 近くのキャラで自分より後ろ。
                DbgNearBehindCount,
                // 直線。
                DbgIsStraight,
                // 最初の直線である。
                DbgIsStraightFirst,
                // 最後の直線である。
                DbgIsStraightLast,
                // 直線の観客側/向こう正面。
                DbgStraightFrontType,
                // インをブロックしているキャラ。※SIMULATE_DATA_VERSION_20181101で追加。
                (byte)DbgBlockInHorseIdx,
                // アウトをブロックしているキャラ。※SIMULATE_DATA_VERSION_20181101で追加。
                (byte)DbgBlockOutHorseIdx,
                // 直線何本目か。※SIMULATE_DATA_VERSION_20200108で追加。
                DbgStraightTypeNumber,
            });
            
            // スコア。※SIMULATE_DATA_VERSION_20200213で追加。
            {
                tmpBytes.Add(new byte[] {(byte) (DbgScoreList?.Count ?? 0)});
                if (DbgScoreList != null)
                {
                    foreach (var score in DbgScoreList)
                    {
                        tmpBytes.Add(new byte[] {(byte) score.raw_score_id});
                        tmpBytes.Add(BitConverter.GetBytes(score.score));
                    }
                }
            }
            
            // レーン移動速度。※SIMULATE_DATA_VERSION_20200521で追加。
            tmpBytes.Add(BitConverter.GetBytes(DbgLaneMoveSpeed));
            
            tmpBytes.Add(BitConverter.GetBytes(DbgSlopeAddTargetSpeed));
            tmpBytes.Add(BitConverter.GetBytes(DbgSlopePer));
            tmpBytes.Add(BitConverter.GetBytes(DbgSkillAddTargetSpeed));
            tmpBytes.Add(BitConverter.GetBytes(DbgForceInMoveAddTargetSpeed));
            tmpBytes.Add(BitConverter.GetBytes(DbgPositionKeepBaseTargetSpeedMultiply));
            tmpBytes.Add(BitConverter.GetBytes(DbgTargetLane));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseTargetSpeed));
            tmpBytes.Add(BitConverter.GetBytes(DbgLastSpurtTargetSpeed));
            tmpBytes.Add(BitConverter.GetBytes(DbgCurDistanceDiffFromPaseMaker));
            tmpBytes.Add(BitConverter.GetBytes(DbgDistanceDiffMin));
            tmpBytes.Add(BitConverter.GetBytes(DbgDistanceDiffMax));
            tmpBytes.Add(BitConverter.GetBytes(DbgAdjustTargetDistanceDiff));
            tmpBytes.Add(BitConverter.GetBytes(DbgVisibleDistance));
            tmpBytes.Add(BitConverter.GetBytes(DbgInfrontHorseNearContinueTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgBehindHorseNearContinueTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgInfrontHorseNearLaneContinueTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgBehindHorseNearLaneContinueTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgTemptationPerRandom));
            tmpBytes.Add(BitConverter.GetBytes(DbgTemptationPerWiz));
            tmpBytes.Add(new byte[]
            {
                DbgIsTemptationStartEnable,
                (byte)DbgHorsePhase,
                (byte)(DbgIsCornerLast ? 1 : 0),
                (byte)DbgPositionKeepMode,
                (byte)DbgTemptationStartSection,
                
                (byte)(DbgIsInMoveEnable ? 1 :0),
                (byte)(DbgIsOutMoveEnable ? 1 : 0),
            });
            tmpBytes.Add(BitConverter.GetBytes(DbgInLaneSpace));
            tmpBytes.Add(BitConverter.GetBytes(DbgOutLaneSpace));
            tmpBytes.Add(new byte[] {(byte) (DbgOverTakeHorseIndexs?.Length ?? 0)});
            if (DbgOverTakeHorseIndexs != null)
            {
                foreach (var horseIndex in DbgOverTakeHorseIndexs)
                {
                    tmpBytes.Add(new byte[] { (byte)horseIndex });
                }
            }
            tmpBytes.Add(BitConverter.GetBytes(DbgOverTakeLane));
            tmpBytes.Add(BitConverter.GetBytes(DbgOverTakeCoolDownTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgAccelPerSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgDecHpPerSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgAddHp));

            tmpBytes.Add(new [] {(byte)(DbgIsCompeteTop ? 1 : 0)});
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteTopCount));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteTopGroup));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteTopRemainTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteTopAddTargetSpeed));

            tmpBytes.Add(new [] {(byte)(DbgIsCompeteFight ? 1 : 0)});
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteFightCount));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteFightGroup));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteFightAddTargetSpeed));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteFightAddAccel));
            tmpBytes.Add(new byte[] {(byte) (DbgCompeteFightNearList?.Count ?? 0)});
            if (DbgCompeteFightNearList != null)
            {
                foreach (var fightNear in DbgCompeteFightNearList)
                {
                    tmpBytes.Add(BitConverter.GetBytes(fightNear.HorseIndex));
                    tmpBytes.Add(BitConverter.GetBytes(fightNear.ContinueTime));
                }
            }

            tmpBytes.Add(new byte[] { (byte)(DbgIsSurrounded ? 1 : 0) });
            tmpBytes.Add(BitConverter.GetBytes(DbgHpPer));

            tmpBytes.Add(BitConverter.GetBytes(DbgCurrentSkillStatusArray?.Length ?? 0));
            if (DbgCurrentSkillStatusArray != null)
            {
                for (int i = 0; i < DbgCurrentSkillStatusArray.Length; i++)
                {
                    tmpBytes.Add(BitConverter.GetBytes(DbgCurrentSkillStatusArray[i].SkillId));
                    tmpBytes.Add(BitConverter.GetBytes(DbgCurrentSkillStatusArray[i].CoolDownTime));

                    // 発動スキル効果インデックス ※SIMULATE_DATA_VERSION_20221214で追加
                    tmpBytes.Add(new byte[] {(byte) (DbgCurrentSkillStatusArray[i].SkillDetailCoolDownTimeArray?.Length ?? 0)});
                    if (DbgCurrentSkillStatusArray[i].SkillDetailCoolDownTimeArray != null)
                    {
                        foreach (var coolDownTime in DbgCurrentSkillStatusArray[i].SkillDetailCoolDownTimeArray)
                        {
                            tmpBytes.Add(BitConverter.GetBytes(coolDownTime));
                        }
                    }
                    tmpBytes.Add(BitConverter.GetBytes(DbgCurrentSkillStatusArray[i].IsContainsUseSkillDetailCoolDownTime));
                }
            }

            tmpBytes.Add(new [] { (byte)DbgPaseMakerHorseIndex });
            tmpBytes.Add(BitConverter.GetBytes(DbgTopHorseNotMostForwardRunningStyleCnt));
            tmpBytes.Add(BitConverter.GetBytes(DbgPositionKeepCoolDownTime));

            tmpBytes.Add(new byte[] {(byte) (DbgDistanceDiffOnPositionKeepCalcArray?.Length ?? 0)});
            if (DbgDistanceDiffOnPositionKeepCalcArray != null)
            {
                foreach (var distanceDiff in DbgDistanceDiffOnPositionKeepCalcArray)
                {
                    tmpBytes.Add(BitConverter.GetBytes(distanceDiff));
                }
            }

            tmpBytes.Add(new byte[] {(byte) (DbgOverTakenHorseIndexes?.Length ?? 0)});
            if (DbgOverTakenHorseIndexes != null)
            {
                foreach (var horseIndex in DbgOverTakenHorseIndexes)
                {
                    tmpBytes.Add(new byte[] {(byte) horseIndex});
                }
            }
            tmpBytes.Add(BitConverter.GetBytes(DbgConservePowerAddAccel));
            tmpBytes.Add(BitConverter.GetBytes(DbgConservePowerAddAccelRemainTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgConservePower));
            tmpBytes.Add(BitConverter.GetBytes(DbgConservePowerIncreaseCoolTime));
            tmpBytes.Add(new byte[] {(byte) (DbgConservePowerIncreaseTimeDictPerRecord?.Count ?? 0)});
            if (DbgConservePowerIncreaseTimeDictPerRecord != null)
            {
                foreach (var increaseTimeData in DbgConservePowerIncreaseTimeDictPerRecord)
                {
                    tmpBytes.Add(new byte[] {(byte) increaseTimeData.Key});
                    tmpBytes.Add(BitConverter.GetBytes(increaseTimeData.Value.Count));
                    foreach (var time in increaseTimeData.Value)
                    {
                        tmpBytes.Add(BitConverter.GetBytes(time));
                    }
                }
            }

            tmpBytes.Add(new byte[] {(byte) (DbgConservePowerDecreaseTimeDictPerRecord?.Count ?? 0)});
            if (DbgConservePowerDecreaseTimeDictPerRecord != null)
            {
                foreach (var decreaseTimeData in DbgConservePowerDecreaseTimeDictPerRecord)
                {
                    tmpBytes.Add(new byte[] {(byte) decreaseTimeData.Key});
                    tmpBytes.Add(BitConverter.GetBytes(decreaseTimeData.Value.Count));
                    foreach (var time in decreaseTimeData.Value)
                    {
                        tmpBytes.Add(BitConverter.GetBytes(time));
                    }
                }
            }

            tmpBytes.Add(BitConverter.GetBytes(DbgSpeed));
            tmpBytes.Add(BitConverter.GetBytes(DbgStamina));
            tmpBytes.Add(BitConverter.GetBytes(DbgPower));
            tmpBytes.Add(BitConverter.GetBytes(DbgGuts));
            tmpBytes.Add(BitConverter.GetBytes(DbgWiz));
            
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtRemainActiveSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtRemainCoolDownSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtRemainCheckIntervalSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtAddSpeedValue));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtConsumeStaminaValue));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtActivateCount));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtConditionNearCharaNum));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtIsSpeedUpMinorBonus));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum));
            tmpBytes.Add(BitConverter.GetBytes(DbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum));

            tmpBytes.Add(BitConverter.GetBytes(DbgIsStaminaKeepState));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaKeepCheckIntervalSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaKeepNeedHp));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaKeepNeedHpForSpurt));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaKeepNeedHpForMiddle));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaKeepNeedHpWizRand));

            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadRemainActiveSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadRemainCoolDownSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadRemainCheckIntervalSec));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadAddSpeedValue));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadConsumeStaminaValue));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadActivateCount));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadIsSpeedUpMinorBonus));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadSpeedUpMinorBonusNearCharaNum));
            tmpBytes.Add(BitConverter.GetBytes(DbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum));

            tmpBytes.Add(BitConverter.GetBytes(DbgSkillParamFluctuate.DbgDebuffAbilityValueMultiplyRatio));
            tmpBytes.Add(BitConverter.GetBytes(DbgSkillParamFluctuate.DbgDebuffAbilityValueMultiplyOtherActivateRatio));

            tmpBytes.Add(BitConverter.GetBytes(DbgLastSpeedForStartDashFinishCheck));

            // VERSION_10000013で追加
            tmpBytes.Add(BitConverter.GetBytes(DbgActivateSpecificTagGroupSkillCount));
            tmpBytes.Add(BitConverter.GetBytes(DbgActivateSpecificSkillAbilityTypeGroupSkillCount));
            
            //-----------------------------------------------------------
            // デバッグ変数全部のバイト数。
            //-----------------------------------------------------------
            int totalBytes = tmpBytes.Sum(b => b.Length);

            //-----------------------------------------------------------
            // 返却用のbyte配列に全部詰める。
            //-----------------------------------------------------------
            var retBytes = new byte[totalBytes];
            int offset = 0;
            foreach (var bytes in tmpBytes)
            {
                Array.Copy(bytes, 0, retBytes, offset, bytes.Length);
                offset += bytes.Length;
            }
            return retBytes;
        }
        
        /// <summary>
        /// バイト配列から自身を復元。
        /// </summary>
        public void Deserialize(byte[] bytes, ref int offset)
        {
            Version = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);

            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if (Version >= VERSION_10000014)
            {
                Deserialize_Ver10000014_OrNewer(bytes, ref offset);
            }
            //-----------------------------------------------------------
            else if (Version >= VERSION_10000013)
            {
                Deserialize_Ver10000013_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000012)
            {
                Deserialize_Ver10000012_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000011)
            {
                Deserialize_Ver10000011_OrNewer(bytes, ref offset);
            }
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
            else if (Version >= VERSION_10000010)
            {
                Deserialize_Ver10000010_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000009)
            {
                Deserialize_Ver10000009_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000008)
            {
                Deserialize_Ver10000008_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000007)
            {
                Deserialize_Ver10000007_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000006)
            {
                Deserialize_Ver10000006_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000005)
            {
                Deserialize_Ver10000005_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000004)
            {
                Deserialize_Ver10000004_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000003)
            {
                Deserialize_Ver10000003_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000002)
            {
                Deserialize_Ver10000002_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000001)
            {
                Deserialize_Ver10000001_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000000)
            {
                Deserialize_Ver10000000_OrNewer(bytes, ref offset);
            }
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000014_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000013_OrNewer(bytes, ref offset);
            DbgActivateSpecificTagGroupSkillCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgActivateSpecificSkillAbilityTypeGroupSkillCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);

        }
        //---------------------------------------------------------------
        private void Deserialize_Ver10000013_OrNewer(byte[] bytes, ref int offset)
        {
            // 過去ロジックに手を入れているので、ここでの追記は無し
            Deserialize_Ver10000012_OrNewer(bytes, ref offset);
        }
        //---------------------------------------------------------------
        private void Deserialize_Ver10000012_OrNewer(byte[] bytes, ref int offset)
        {
            // 過去ロジックに手を入れているので、ここでの追記は無し
            Deserialize_Ver10000011_OrNewer(bytes, ref offset);
        }
        //---------------------------------------------------------------
        private void Deserialize_Ver10000011_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000010_OrNewer(bytes, ref offset);
            DbgLastSpeedForStartDashFinishCheck = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
        }
        //---------------------------------------------------------------
        private void Deserialize_Ver10000010_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000009_OrNewer(bytes, ref offset);
            var debuffAbilityValueMultiplyRatio = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            var debuffAbilityValueMultiplyOtherActivateRatio = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgSkillParamFluctuate.UpdateFluctuateValues(debuffAbilityValueMultiplyRatio, debuffAbilityValueMultiplyOtherActivateRatio);
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000009_OrNewer(byte[] bytes, ref int offset)
        {
            // 10000009ではスパート前位置取り勝負とリード確保を実装
            Deserialize_Ver10000008_OrNewer(bytes, ref offset);

            DbgCompeteBeforeSpurtRemainActiveSec = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtRemainCoolDownSec = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtRemainCheckIntervalSec = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtAddSpeedValue = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtConsumeStaminaValue = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtActivateCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtConditionNearCharaNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtConditionNearCharaNumSameRunningStyle = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtIsSpeedUpMinorBonus = RaceUtil.BytesToByteAddOffset(bytes, ref offset) != 0;
            DbgCompeteBeforeSpurtSpeedUpMinorBonusNearCharaNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteBeforeSpurtSpeedUpMinorBonusSameRunningStyleNearCharaNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);

            DbgIsStaminaKeepState = RaceUtil.BytesToByteAddOffset(bytes, ref offset) != 0;
            DbgStaminaKeepCheckIntervalSec = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStaminaKeepNeedHp = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStaminaKeepNeedHpForSpurt = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStaminaKeepNeedHpForMiddle = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStaminaKeepNeedHpWizRand = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);

            DbgSecureLeadRemainActiveSec = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgSecureLeadRemainCoolDownSec = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgSecureLeadRemainCheckIntervalSec = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgSecureLeadAddSpeedValue = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgSecureLeadConsumeStaminaValue = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgSecureLeadActivateCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgSecureLeadIsSpeedUpMinorBonus = RaceUtil.BytesToByteAddOffset(bytes, ref offset) != 0;
            DbgSecureLeadSpeedUpMinorBonusNearCharaNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgSecureLeadSpeedUpMinorBonusSameRunningStyleNearCharaNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000008_OrNewer(byte[] bytes, ref int offset)
        {
            // 10000008ではパラメーターを追加した
            Deserialize_Ver10000007_OrNewer(bytes, ref offset);
            DbgSpeed = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStamina = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgPower = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgGuts = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgWiz = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            // また、スキルのログにスキル効果毎にクールダウンを持つスキルかどうかのフラグを追加（Ver10000003追記）
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000007_OrNewer(byte[] bytes, ref int offset)
        {
            // 10000007では発動スキル効果にisValidを追加しただけなのでここでは Deserialize_Ver10000006_OrNewer をそのまま実行するだけ
            Deserialize_Ver10000006_OrNewer(bytes, ref offset);
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver10000006_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000005_OrNewer(bytes, ref offset);
            DbgConservePowerAddAccel = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgConservePowerAddAccelRemainTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgConservePower = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgConservePowerIncreaseCoolTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);

            int conservePowerIncreaseTimeDictCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgConservePowerIncreaseTimeDictPerRecord =
                new Dictionary<Gallop.RaceDefine.IncreaseConservePowerMode, List<float>>(conservePowerIncreaseTimeDictCount);

            for (int i = 0; i < conservePowerIncreaseTimeDictCount; i++)
            {
                var increaseMode =
                    (Gallop.RaceDefine.IncreaseConservePowerMode) RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                var activateTimeCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                var activateTimeList = new List<float>(activateTimeCount);
                DbgConservePowerIncreaseTimeDictPerRecord.Add(increaseMode, activateTimeList);
                for (int j = 0; j < activateTimeCount; j++)
                {
                    float activateTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                    activateTimeList.Add(activateTime);
                }
            }

            int conservePowerDecreaseTimeDictCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgConservePowerDecreaseTimeDictPerRecord =
                new Dictionary<Gallop.RaceDefine.DecreaseConservePowerMode, List<float>>(conservePowerDecreaseTimeDictCount);

            for (int i = 0; i < conservePowerDecreaseTimeDictCount; i++)
            {
                var decreaseMode =
                    (Gallop.RaceDefine.DecreaseConservePowerMode) RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                var activateTimeCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                var activateTimeList = new List<float>(activateTimeCount);
                DbgConservePowerDecreaseTimeDictPerRecord.Add(decreaseMode, activateTimeList);
                for (int j = 0; j < activateTimeCount; j++)
                {
                    float activateTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                    activateTimeList.Add(activateTime);
                }
            }
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver10000005_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000004_OrNewer(bytes, ref offset);
            byte overTakenHorseIndexCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgOverTakenHorseIndexes = new int[overTakenHorseIndexCount];
            for (int i = 0; i < DbgOverTakenHorseIndexes.Length; ++i)
            {
                DbgOverTakenHorseIndexes[i] = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            }
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000004_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000003_OrNewer(bytes, ref offset);

            DbgPaseMakerHorseIndex = (sbyte)RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgTopHorseNotMostForwardRunningStyleCnt = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgPositionKeepCoolDownTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            
            byte distanceDiffCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgDistanceDiffOnPositionKeepCalcArray = new float[distanceDiffCount];
            for (int i = 0; i < DbgDistanceDiffOnPositionKeepCalcArray.Length; i++)
            {
                DbgDistanceDiffOnPositionKeepCalcArray[i] = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            }
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000003_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000002_OrNewer(bytes, ref offset);

            int num = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCurrentSkillStatusArray = new SkillStatus[num];
            for (int i = 0; i < DbgCurrentSkillStatusArray.Length; i++)
            {
                DbgCurrentSkillStatusArray[i] = new SkillStatus();
                DbgCurrentSkillStatusArray[i].SkillId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                DbgCurrentSkillStatusArray[i].CoolDownTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                
                // スキル効果クールダウンタイム ※SIMULATE_DATA_VERSION_20221214で追加
                if (Version >= VERSION_10000006)
                {
                    byte coolDownTimeArrayCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    DbgCurrentSkillStatusArray[i].SkillDetailCoolDownTimeArray = new float[coolDownTimeArrayCount];
                    for (int j = 0; j < DbgCurrentSkillStatusArray[i].SkillDetailCoolDownTimeArray.Length; j++)
                    {
                        DbgCurrentSkillStatusArray[i].SkillDetailCoolDownTimeArray[j] = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                    }
                }
                // スキル効果毎にクールダウンを持つスキルかどうか  #113639: 2023/06/06追加
                if (Version >= VERSION_10000008)
                {
                    DbgCurrentSkillStatusArray[i].IsContainsUseSkillDetailCoolDownTime = RaceUtil.BytesToByteAddOffset(bytes, ref offset) == 1;
                }
            }
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver10000002_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000001_OrNewer(bytes, ref offset);
            DbgHpPer = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver10000001_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000000_OrNewer(bytes, ref offset);
            DbgIsSurrounded = RaceUtil.BytesToByteAddOffset(bytes, ref offset) == 1;
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000000_OrNewer(byte[] bytes, ref int offset)
        {
            // 狙いたい速度。
            RaceUtil.BytesToFloatAddOffset(bytes, ref this.DbgTargetSpeed, ref offset);
            
            // 発動中の特性。
            {
                byte numPassiveSkill = bytes[offset];
                offset += sizeof(byte);
                DbgCurrentActiveSkillArray = new RaceHorseSkillData[numPassiveSkill];
                for (int i = 0; i < DbgCurrentActiveSkillArray.Length; ++i)
                {
                    DbgCurrentActiveSkillArray[i] = new RaceHorseSkillData();
                    DbgCurrentActiveSkillArray[i].skill_id = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    DbgCurrentActiveSkillArray[i].level = RaceUtil.BytesToInt32AddOffset(bytes, ref offset); // ※SIMULATE_DATA_VERSION_20190218で追加。
                }
            }
            
            // 発動済みの特性。
            {
                byte numActivatedSkills = bytes[offset];
                offset += sizeof(byte);
                DbgActivatedSkillArray = new ActivatedSkillFrameInfo[numActivatedSkills];
                for (int i = 0; i < DbgActivatedSkillArray.Length; ++i)
                {
                    DbgActivatedSkillArray[i] = new ActivatedSkillFrameInfo();
                    RaceUtil.BytesToInt32AddOffset(bytes, ref DbgActivatedSkillArray[i].skillId, ref offset);
                    DbgActivatedSkillArray[i].frameTime = -1;
                    
                    // 発動スキル効果インデックス ※SIMULATE_DATA_VERSION_20221214で追加
                    if (Version >= VERSION_10000006)
                    {
                        RaceUtil.BytesToInt32AddOffset(bytes, ref DbgActivatedSkillArray[i].skillDetailIndex, ref offset);
                    }
                    // ※SIMULATE_DATA_VERSION_20240930で追加
                    if (Version >= VERSION_10000012)
                    {
                        RaceUtil.BytesToFloatAddOffset(bytes, ref DbgActivatedSkillArray[i].frameTime, ref offset);
                    }
                }
            }
            
            // スキルによるパラメータ変動値。
            {
                byte modifierDicCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgSkillModifierDic = new Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>>(modifierDicCount);
                for (int i = 0; i < modifierDicCount; ++i)
                {
                    var modifierParamType = (Gallop.SkillDefine.SkillModifierParam)RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    var modifierListCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    var modifierList = new List<ISkillParamModifier>(modifierListCount);
                    DbgSkillModifierDic.Add(modifierParamType, modifierList);
                    
                    for (int modifierIndex = 0; modifierIndex < modifierListCount; ++modifierIndex)
                    {
                        var modifierType = (Gallop.SkillDefine.SkillParamModifierType)RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                        int horseIndex = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                        int skillId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                        int skillLevel = (int)RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                        float skillValue = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                        bool isValid = true;
                        // デバフ無効化スキルが実装されたのでその情報も付随させる
                        if (Version >= VERSION_10000007)
                        {
                            isValid = RaceUtil.BytesToByteAddOffset(bytes, ref offset) == 1;
                        }
                        bool isActivateOthers = false;
                        // 他者が発動したスキルかどうかの情報を持たせるようになったのでその取り出し
                        if (Version >= VERSION_10000010)
                        {
                            isActivateOthers = RaceUtil.BytesToByteAddOffset(bytes, ref offset) == 1;
                        }

                        ISkillParamModifier modifier;
                        // 効果値増減インスタンスを生成。VERSION_10000010で追加されたので中身は後で入れる。
                        bool isDebuff = RaceUtil.IsDebuff(modifierParamType, skillValue);
                        switch (modifierType)
                        {
                            case Gallop.SkillDefine.SkillParamModifierType.Add:
                                modifier = new SkillParamModifierAdd(skillValue, 0, modifierParamType, DbgSkillParamFluctuate, isActivateOthers);
                                break;
                            case Gallop.SkillDefine.SkillParamModifierType.Set:
                                modifier = new SkillParamModifierSet(skillValue, 0, modifierParamType, DbgSkillParamFluctuate, isActivateOthers);
                                break;
                            case Gallop.SkillDefine.SkillParamModifierType.Multiply:
                                modifier = new SkillParamModifierMultiply(skillValue, 0, modifierParamType, DbgSkillParamFluctuate, isActivateOthers);
                                break;
                            default:
                                Debug.LogError($"SkillParamModifierType: {modifierType}用のデバッグデータデシリアライズが未対応です");
                                // 仕方ないのでSetで作っておく
                                modifier = new SkillParamModifierSet(skillValue, 0, modifierParamType, DbgSkillParamFluctuate, isActivateOthers);
                                break;
                        }

                        modifier.AppendDbgSkill(horseIndex, skillId, skillLevel, isValid: isValid);
                        
                        modifierList.Add(modifier);
                    }
                }
            }
            
            
            // スキルによるパラメータ変動値。
            {
                byte numSkillValueParam = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgSkillValueParamTotal = new Dictionary<Gallop.SkillDefine.SkillModifierParam, float>(numSkillValueParam);
                for (int i = 0; i < numSkillValueParam; ++i)
                {
                    var type = (Gallop.SkillDefine.SkillModifierParam)RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    var value = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                    DbgSkillValueParamTotal.Add(type, value);
                }
            }
            
            // Phase毎のスキル発動回数。
            {
                byte numActivateSkillCountByPhase = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgActivateSkillCount = new Dictionary<Gallop.RaceDefine.HorsePhase, int>(numActivateSkillCountByPhase);
                for (int i = 0; i < numActivateSkillCountByPhase; ++i)
                {
                    byte phase = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    byte count = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    DbgActivateSkillCount.Add((Gallop.RaceDefine.HorsePhase)phase, count);
                }
            }
            
            // 前方ブロックされている継続時間。
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgBlockedFrontContinueTime, ref offset);
            // 前方をHp0にブロックされている継続時間。
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgBlockedFrontHpEmptyContinueTime, ref offset);
            // サイドブロックされている継続時間。
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgBlockedSideContinueTime, ref offset);
            // hibi:使っていないのでbyteを追加したいときに使ってください。（Serializeの方と合わせて）
            RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 通過中のコーナー。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCorner, ref offset);
            // 現在順位。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrder, ref offset);
            
            // 順位上昇回数：中盤。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderUpCountPhaseMiddle, ref offset);
            // 順位上昇回数：終盤以降。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderUpCountPhaseEndAfter, ref offset);
            // 順位上昇回数：コーナー中。 
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderUpCountCorner, ref offset);
            // 順位上昇回数：ラストスパート中。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderUpCountLastSpurt, ref offset);
            if (Version >= VERSION_10000013)
            {
                // 順位上昇回数：特定距離から。※SIMULATE_DATA_VERSION_20200407で追加、SIMULATE_DATA_VERSION_20241028で移動
                RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderUpCountDistance1, ref offset);
                // 順位上昇回数：最終コーナー以降。※SIMULATE_DATA_VERSION_20241028で追加。
                RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderUpCountFinalCornerAfter, ref offset);
                // 順位上昇回数：後半50%以降。※SIMULATE_DATA_VERSION_20241028で追加。
                RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderUpCountLaterHalf, ref offset);
            }
            
            // 順位下降回数：中盤。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderDownCountPhaseMiddle, ref offset);
            // 順位下降回数：終盤以降。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderDownCountPhaseEndAfter, ref offset);
            // 順位下降回数：コーナー中。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderDownCountCorner, ref offset);
            // 順位下降回数：ラストスパート中。
            RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderDownCountLastSpurt, ref offset);
            if (Version >= VERSION_10000013)
            {
                // 順位下降回数：特定距離から。※SIMULATE_DATA_VERSION_20200407で追加、SIMULATE_DATA_VERSION_20241028で移動
                RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderDownCountDistance1, ref offset);
                // 順位下降回数：最終コーナー以降。※SIMULATE_DATA_VERSION_20241028で追加。
                RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderDownCountFinalCornerAfter, ref offset);
                // 順位下降回数：後半50%以降。※SIMULATE_DATA_VERSION_20241028で追加。
                RaceUtil.BytesToByteAddOffset(bytes, ref DbgCurOrderDownCountLaterHalf, ref offset);
            }

            // 視野範囲に入っているキャラ。
            {
                byte numVisible = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgVisibleHorseIndexs = new byte[numVisible];
                for (int i = 0; i < numVisible; ++i)
                {
                    DbgVisibleHorseIndexs[i] = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                }
            }
            // 近くのキャラ。
            {
                byte numNear = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgNearHorseIndexs = new byte[numNear];
                for (int i = 0; i < numNear; ++i)
                {
                    DbgNearHorseIndexs[i] = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                }
            }
            // 近くのキャラが一定数(4)以上継続して存在する時間。
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgNearHorseAroundTime, ref offset);
            
            // ラストスパートの有無。
            DbgIsLastSpurt = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 最終コーナー。
            DbgIsFinalCorner = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 速すぎる。
            DbgIsTooFast = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 遅すぎる。
            DbgIsTooSlow = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // デバフを受けた。
            DbgIsReceivedDebuff = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // LaneType。
            DbgLaneType = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 順位１後のキャラが自分よりインにいる。
            DbgIsBehindIsIn = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 順位１後のキャラが自分よりアウトにいる。
            DbgIsBehindIsOut = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 近くのキャラで自分より前。
            DbgNearFrontCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 近くのキャラで自分より後ろ。
            DbgNearBehindCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 直線。
            DbgIsStraight = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 最初の直線である。
            DbgIsStraightFirst = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 最後の直線である。
            DbgIsStraightLast = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            // 直線の観客側/向こう正面。
            DbgStraightFrontType = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            
            // インをブロックしているキャラ。※SIMULATE_DATA_VERSION_20181101で追加。
            DbgBlockInHorseIdx = RaceUtil.BytesToSByteAddOffset(bytes, ref offset);
            // アウトをブロックしているキャラ。※SIMULATE_DATA_VERSION_20181101で追加。
            DbgBlockOutHorseIdx = RaceUtil.BytesToSByteAddOffset(bytes, ref offset);
            
            // 直線何本目か。
            DbgStraightTypeNumber = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            
            // スコア。※SIMULATE_DATA_VERSION_20200213で追加。
            {
                DbgScoreList.Clear();
                byte num = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                for (int i = 0; i < num; ++i)
                {
                    var rawScoreId = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    int score = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    var scoreData = new ScoreData(rawScoreId);
                    scoreData.num = 1;
                    scoreData.score = score;
                    DbgScoreList.Add(scoreData);
                }
            }
            
            // 順位上昇回数。
            if (Version < VERSION_10000013)
            {
                // 10000013では記載場所を移した
                DbgCurOrderUpCountDistance1 = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgCurOrderDownCountDistance1 = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            }
            
            // レーン移動速度。※SIMULATE_DATA_VERSION_20200521で追加。
            DbgLaneMoveSpeed = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgSlopeAddTargetSpeed, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgSlopePer, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgSkillAddTargetSpeed, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgForceInMoveAddTargetSpeed, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgPositionKeepBaseTargetSpeedMultiply, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgTargetLane, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgBaseTargetSpeed, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgLastSpurtTargetSpeed, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgCurDistanceDiffFromPaseMaker, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgDistanceDiffMin, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgDistanceDiffMax, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgAdjustTargetDistanceDiff, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgVisibleDistance, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgInfrontHorseNearContinueTime, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgBehindHorseNearContinueTime, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgInfrontHorseNearLaneContinueTime, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgBehindHorseNearLaneContinueTime, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgTemptationPerRandom, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgTemptationPerWiz, ref offset);
            DbgIsTemptationStartEnable = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgHorsePhase = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgIsCornerLast = RaceUtil.BytesToByteAddOffset(bytes, ref offset) == 1;
            DbgPositionKeepMode = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgTemptationStartSection = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            
            DbgIsInMoveEnable = RaceUtil.BytesToByteAddOffset(bytes, ref offset) == 1;
            DbgIsOutMoveEnable = RaceUtil.BytesToByteAddOffset(bytes, ref offset) == 1;
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgInLaneSpace, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgOutLaneSpace, ref offset);
            
            byte overTakeHorseIndexCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgOverTakeHorseIndexs = new int[overTakeHorseIndexCount];
            for (int i = 0; i < DbgOverTakeHorseIndexs.Length; ++i)
            {
                DbgOverTakeHorseIndexs[i] = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            }
            
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgOverTakeLane, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgOverTakeCoolDownTime, ref offset);
            
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgAccelPerSec, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgDecHpPerSec, ref offset);
            RaceUtil.BytesToFloatAddOffset(bytes, ref DbgAddHp, ref offset);

            DbgIsCompeteTop = RaceUtil.BytesToByteAddOffset(bytes, ref offset) != 0;
            DbgCompeteTopCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteTopGroup = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteTopRemainTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgCompeteTopAddTargetSpeed = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            
            DbgIsCompeteFight = RaceUtil.BytesToByteAddOffset(bytes, ref offset) != 0;
            DbgCompeteFightCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteFightGroup = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCompeteFightAddTargetSpeed = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgCompeteFightAddAccel = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            
            int fightNearNum = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgCompeteFightNearList = new List<CompeteFightNear>(fightNearNum);
            for (int i = 0; i < fightNearNum; i++)
            {
                var near = new CompeteFightNear(
                    RaceUtil.BytesToInt32AddOffset(bytes, ref offset),
                    RaceUtil.BytesToFloatAddOffset(bytes, ref offset));
                DbgCompeteFightNearList.Add(near);
            }
        }
    }
}    

#endif
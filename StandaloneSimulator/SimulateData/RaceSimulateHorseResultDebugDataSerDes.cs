#if CYG_DEBUG
using System;
using System.Linq;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public partial class RaceSimulateHorseResultDebugData
    {
        /// <summary>
        /// デバッグ情報をbyte配列に変換。
        /// </summary>
        public byte[] Serialize()
        {
            //-----------------------------------------------------------
            // デバッグ変数をbyte配列で詰める。
            //-----------------------------------------------------------
            List<byte[]> tmpBytes = new List<byte[]>(128);

        #if !STANDALONE_SIMULATOR
            // バイナリからJson出力するときにstg環境等だとバージョンが0のままシリアライズされて
            // 出力されたJsonをデシリアライズするときにバージョンが0なのでデバッグデータがデシリアライズされずズレが発生してしまう。
            // なのでバージョンが0だったら現在のバージョンを疑似的に入れてデシリアライズ時のズレを無くす。
            if (Version == 0)
            {
                Version = CUR_VERSION;
            }
        #endif

            tmpBytes.Add(BitConverter.GetBytes(Version));

            tmpBytes.Add( new byte[]
            {
                DbgFinalGradeHigherCount,
                DbgFinalGradeLowerCount,
                DbgIsRunningStyleCountMax,
                DbgPopularity,
                DbgPopularityRankLeft,
                DbgPopularityRankCenter,
                DbgPopularityRankRight,
                DbgIsRunningStyleEqualPopularityOne,
                DbgIsGoodStart,
                DbgIsBadStart,
                DbgIsHpEmpty,
                DbgTemptationCount,
                DbgPositionOvertakeCount,
            } );
            tmpBytes.Add( BitConverter.GetBytes( DbgLastSpurtCalcResult ) );
            tmpBytes.Add( BitConverter.GetBytes( DbgFrontBlockAccumulateTime ) );
            tmpBytes.Add( BitConverter.GetBytes( DbgTeamStadiumTotalScore ) );
            tmpBytes.Add( BitConverter.GetBytes( DbgStartDelayTimeBase ) );
            
            // スコア。
            {
                tmpBytes.Add(BitConverter.GetBytes(DbgScoreList?.Count ?? 0));
                if (DbgScoreList != null)
                {
                    foreach (var score in DbgScoreList)
                    {
                        tmpBytes.Add(BitConverter.GetBytes(score.raw_score_id));
                        tmpBytes.Add(BitConverter.GetBytes(score.score));
                        tmpBytes.Add(BitConverter.GetBytes(score.bonus_num));
                        foreach (var bonus in score.bonus_array)
                        {
                            tmpBytes.Add(BitConverter.GetBytes(bonus.score_bonus_id));
                            tmpBytes.Add(BitConverter.GetBytes(bonus.bonus_score));
                        }
                    }
                }
            }
            // スコアボーナス。
            int bonusNum = DbgBonusRateList?.Count ?? 0;
            tmpBytes.Add(BitConverter.GetBytes(bonusNum));
            if (DbgBonusRateList != null)
            {
                foreach (var bonus in DbgBonusRateList)
                {
                    tmpBytes.Add(BitConverter.GetBytes(bonus.ScoreBonusId));
                    tmpBytes.Add(BitConverter.GetBytes(bonus.BonusRate));
                }
            }
            
            // スキル効果付与履歴。
            int modifierHistoryNum = DbgReceiveHistoryList?.Count ?? 0;
            tmpBytes.Add(BitConverter.GetBytes(modifierHistoryNum));
            if(DbgReceiveHistoryList != null)
            {
                foreach (var history in DbgReceiveHistoryList)
                {
                    tmpBytes.Add(BitConverter.GetBytes(history.AccumulateTime));
                    tmpBytes.Add(BitConverter.GetBytes((int)history.Type));
                    tmpBytes.Add(BitConverter.GetBytes(history.FromHorseIndex));
                    tmpBytes.Add(BitConverter.GetBytes(history.SkillId));
                    tmpBytes.Add(BitConverter.GetBytes(history.SkillLevel));
                    tmpBytes.Add(BitConverter.GetBytes(history.ItemId));
                    tmpBytes.Add(BitConverter.GetBytes(history.Value));
                }
            }
            
            // ラストスパート候補。
            int lastSpurtCandidateNum = DbgCandidateList?.Count ?? 0;
            tmpBytes.Add(BitConverter.GetBytes(lastSpurtCandidateNum));
            if (DbgCandidateList != null)
            {
                foreach (var candidate in DbgCandidateList)
                {
                    tmpBytes.Add(BitConverter.GetBytes(candidate.distance));
                    tmpBytes.Add(BitConverter.GetBytes(candidate.speed));
                    tmpBytes.Add(BitConverter.GetBytes(candidate.needTime));
                }
            }
            
            // 採用されたラストスパート結果。
            tmpBytes.Add(BitConverter.GetBytes(DbgUseCandidate.distance));
            tmpBytes.Add(BitConverter.GetBytes(DbgUseCandidate.speed));
            tmpBytes.Add(BitConverter.GetBytes(DbgUseCandidate.needTime));
            
            tmpBytes.Add(BitConverter.GetBytes(DbgLastSpurtCalcCount));

            tmpBytes.Add(BitConverter.GetBytes(DbgBaseSpeedWithMotivation));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseStaminaWithMotivation));
            tmpBytes.Add(BitConverter.GetBytes(DbgBasePowWithMotivation));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseGutsWithMotivation));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseWizWithMotivation));

            tmpBytes.Add(BitConverter.GetBytes(DbgBaseSpeedWithMotivationRaceType));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseStaminaWithMotivationRaceType));
            tmpBytes.Add(BitConverter.GetBytes(DbgBasePowWithMotivationRaceType));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseGutsWithMotivationRaceType));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseWizWithMotivationRaceType));

            SerializeCompeteGroupList(DbgCompeteTopStartGroupHorseList, tmpBytes);
            SerializeCompeteGroupList(DbgCompeteTopEndGroupHorseList, tmpBytes);
            SerializeCompeteGroupList(DbgCompeteFightStartGroupHorseList, tmpBytes);
            SerializeCompeteGroupList(DbgCompeteFightEndGroupHorseList, tmpBytes);

            int lastSpurtSpeedNum = DbgLastSpurtLastCalculatedSpeedArray?.Length ?? 0;
            tmpBytes.Add(BitConverter.GetBytes(lastSpurtSpeedNum));
            if (DbgLastSpurtLastCalculatedSpeedArray != null)
            {
                foreach (var speed in DbgLastSpurtLastCalculatedSpeedArray)
                {
                    tmpBytes.Add(BitConverter.GetBytes(speed));
                }
            }
            
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseTargetSpeedRandomMin));
            tmpBytes.Add(BitConverter.GetBytes(DbgBaseTargetSpeedRandomMax));
            tmpBytes.Add(BitConverter.GetBytes(DbgCourseSetBaseSpeedCoef));
            
            // 足溜め
            tmpBytes.Add(BitConverter.GetBytes(DbgConservePowerActivityTime));
            
            // 上限突破パラメータスタミナ
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaLimitBreakBuffAddTargetSpeed));
            tmpBytes.Add(BitConverter.GetBytes((int) DbgStaminaLimitBreakBuffLotteryRandomTableType));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaLimitBreakBuffRandomTableCoef));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaLimitBreakBuffActivateTime));
            tmpBytes.Add(BitConverter.GetBytes(DbgStaminaLimitBreakBuffFinishTime));
            tmpBytes.Add(new byte[] {(byte) (DbgStaminaLimitBreakBuffRandomTableProbabilityDict?.Count ?? 0)});
            if (DbgStaminaLimitBreakBuffRandomTableProbabilityDict != null)
            {
                foreach (var staminaLimitBreakBuffData in DbgStaminaLimitBreakBuffRandomTableProbabilityDict)
                {
                    tmpBytes.Add(new byte[] {(byte) staminaLimitBreakBuffData.Key});
                    tmpBytes.Add(BitConverter.GetBytes(staminaLimitBreakBuffData.Value));
                }
            }
            
            // 追い抜き・追い越され
            tmpBytes.Add(new byte[] { (byte)(DbgOrderChangeCounterUpOverTakeLogArray?.Length ?? 0) });
            if (DbgOrderChangeCounterUpOverTakeLogArray != null)
            {
                foreach (var overTakeLog in DbgOrderChangeCounterUpOverTakeLogArray)
                {
                    tmpBytes.Add(new byte[] { (byte)overTakeLog.HorseIndex });
                    tmpBytes.Add(BitConverter.GetBytes(overTakeLog.LogTime));
                }
            }

            tmpBytes.Add(new byte[] { (byte)(DbgOrderChangeCounterDownOverTakeLogArray?.Length ?? 0) });
            if (DbgOrderChangeCounterDownOverTakeLogArray != null)
            {
                foreach (var overTakeLog in DbgOrderChangeCounterDownOverTakeLogArray)
                {
                    tmpBytes.Add(new byte[] { (byte)overTakeLog.HorseIndex });
                    tmpBytes.Add(BitConverter.GetBytes(overTakeLog.LogTime));
                }
            }

            //-----------------------------------------------------------
            // デバッグ変数全部のバイト数。
            //-----------------------------------------------------------
            int totalBytes = tmpBytes.Sum( b => b.Length );
            
            //-----------------------------------------------------------
            // 返却用のbyte配列に全部詰める。
            //-----------------------------------------------------------
            var retBytes = new byte[ totalBytes ];
            int offset = 0;
            foreach( var bytes in tmpBytes )
            {
                Array.Copy( bytes, 0, retBytes, offset, bytes.Length );   
                offset += bytes.Length;
            }
            return retBytes;

        }

        /// <summary>
        /// byte配列からデバッグ情報を復元。
        /// </summary>
        private void Deserialize(byte[] bytes, ref int offset)
        {
            Version = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);

            //-----------------------------------------------------------
            // 現行バージョン。
            //-----------------------------------------------------------
            if(Version >= VERSION_10000007)
            {
                Deserialize_Ver10000007_OrNewer(bytes, ref offset);
            }
            //-----------------------------------------------------------
            // 以降、旧バージョン。古いバージョンほど下に書く。
            //-----------------------------------------------------------
            else if (Version >= VERSION_10000006)
            {
                Deserialize_Ver10000006_OrNewer(bytes, ref offset);
            }
            else if (Version >= VERSION_10000005)
            {
                Deserialize_Ver10000005_OrNewer(bytes, ref offset);
            }
            else if(Version >= VERSION_10000004)
            {
                Deserialize_Ver10000004_OrNewer(bytes, ref offset);
            }
            else if(Version >= VERSION_10000003)
            {
                Deserialize_Ver10000003_OrNewer(bytes, ref offset);
            }
            else if(Version >= VERSION_10000002)
            {
                Deserialize_Ver10000002_OrNewer(bytes, ref offset);
            }
            else if(Version >= VERSION_10000001)
            {
                Deserialize_Ver10000001_OrNewer(bytes, ref offset);
            }
            else if(Version >= VERSION_10000000)
            {
                Deserialize_Ver10000000_OrNewer(bytes, ref offset);
            }
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000007_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000006_OrNewer(bytes, ref offset);
            // 追い抜き・追い越され記録
            {
                // 追い抜き
                int numUp = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgOrderChangeCounterUpOverTakeLogArray = new DbgOverTakeLog[numUp];
                for (int i = 0; i < numUp; ++i)
                {
                    var horseIndex = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    var logTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                    var overTakeLog = new DbgOverTakeLog(horseIndex, logTime);
                    DbgOrderChangeCounterUpOverTakeLogArray[i] = overTakeLog;
                }
                
                //追い越され
                int numDown = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgOrderChangeCounterDownOverTakeLogArray = new DbgOverTakeLog[numDown];
                for (int i = 0; i < numDown; ++i)
                {
                    var horseIndex = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                    var logTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                    var overTakeLog = new DbgOverTakeLog(horseIndex, logTime);
                    DbgOrderChangeCounterDownOverTakeLogArray[i] = overTakeLog;
                }
            }
        }
        //---------------------------------------------------------------
        private void Deserialize_Ver10000006_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000005_OrNewer(bytes, ref offset);
            DbgStaminaLimitBreakBuffAddTargetSpeed = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStaminaLimitBreakBuffLotteryRandomTableType =
                (Gallop.RaceDefine.RandomTableType) RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgStaminaLimitBreakBuffRandomTableCoef = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStaminaLimitBreakBuffActivateTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgStaminaLimitBreakBuffFinishTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            int staminaLimitBreakBuffRandomTableProbabilityDictCount = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgStaminaLimitBreakBuffRandomTableProbabilityDict =
                new Dictionary<Gallop.RaceDefine.RandomTableType, float>(staminaLimitBreakBuffRandomTableProbabilityDictCount);

            for (int i = 0; i < staminaLimitBreakBuffRandomTableProbabilityDictCount; i++)
            {
                var randomTableType = (Gallop.RaceDefine.RandomTableType) RaceUtil.BytesToByteAddOffset(bytes, ref offset);
                DbgStaminaLimitBreakBuffRandomTableProbabilityDict[randomTableType] = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            }
        }
        //---------------------------------------------------------------
        private void Deserialize_Ver10000005_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000004_OrNewer(bytes, ref offset);
            DbgConservePowerActivityTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000004_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000003_OrNewer(bytes, ref offset);
            DbgCourseSetBaseSpeedCoef = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver10000003_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000002_OrNewer(bytes, ref offset);

            DbgBaseTargetSpeedRandomMin = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseTargetSpeedRandomMax = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
        }

        //---------------------------------------------------------------
        private void Deserialize_Ver10000002_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000001_OrNewer(bytes, ref offset);

            int lastSpurtSpeedNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgLastSpurtLastCalculatedSpeedArray = new float[lastSpurtSpeedNum];
            for (int i = 0; i < DbgLastSpurtLastCalculatedSpeedArray.Length; ++i)
            {
                DbgLastSpurtLastCalculatedSpeedArray[i] = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            }
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver10000001_OrNewer(byte[] bytes, ref int offset)
        {
            Deserialize_Ver10000000_OrNewer(bytes, ref offset);

            DeserializeCompeteGroupList(ref DbgCompeteTopStartGroupHorseList, bytes, ref offset);
            DeserializeCompeteGroupList(ref DbgCompeteTopEndGroupHorseList, bytes, ref offset);
            DeserializeCompeteGroupList(ref DbgCompeteFightStartGroupHorseList, bytes, ref offset);
            DeserializeCompeteGroupList(ref DbgCompeteFightEndGroupHorseList, bytes, ref offset);
        }
        
        //---------------------------------------------------------------
        private void Deserialize_Ver10000000_OrNewer(byte[] bytes, ref int offset)
        {
            DbgFinalGradeHigherCount = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgFinalGradeLowerCount = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgIsRunningStyleCountMax = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgPopularity = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgPopularityRankLeft = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgPopularityRankCenter = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgPopularityRankRight = RaceUtil.BytesToByteAddOffset(bytes, ref offset);
            DbgIsRunningStyleEqualPopularityOne = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgIsGoodStart = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgIsBadStart = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgIsHpEmpty = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgTemptationCount = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgPositionOvertakeCount = RaceUtil.BytesToByteAddOffset( bytes, ref offset );
            DbgLastSpurtCalcResult = RaceUtil.BytesToInt32AddOffset( bytes, ref offset );
            DbgFrontBlockAccumulateTime = RaceUtil.BytesToFloatAddOffset( bytes, ref offset );
            DbgTeamStadiumTotalScore = RaceUtil.BytesToInt32AddOffset( bytes, ref offset );
            DbgStartDelayTimeBase = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            
            // スコア。
            {
                DbgScoreList.Clear();
                int num = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                for (int i = 0; i < num; ++i)
                {
                    var rawScoreId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    var scoreData = new ScoreData(rawScoreId);
                    scoreData.num = 1;
                    scoreData.score = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    int bonusNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                    for (int bonusIndex = 0; bonusIndex < bonusNum; ++bonusIndex)
                    {
                        int scoreBonusId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                        int bonusScore = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                        var bonus = new ScoreBonusData(scoreBonusId, bonusScore);
                        scoreData.AddArray(bonus);    
                    }
                    DbgScoreList.Add(scoreData);
                }
            }

            // スコアボーナス。
            int bonusRateNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgBonusRateList = new List<ScoreBonusRate>(bonusRateNum);
            for (int i = 0; i < bonusRateNum; ++i)
            {
                int bonusId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                float rate = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                DbgBonusRateList.Add(new ScoreBonusRate(bonusId, rate));
            }
            
            // パラメータ変動受信履歴。
            int modifierHistoryNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgReceiveHistoryList = new List<ReceiveHistory>(modifierHistoryNum);
            for (int i = 0; i < modifierHistoryNum; ++i)
            {
                var history = new ReceiveHistory();
                history.AccumulateTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                history.Type = (Gallop.SkillDefine.SkillModifierParam)RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                history.FromHorseIndex = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                history.SkillId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                history.SkillLevel = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                history.ItemId = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                history.Value = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                DbgReceiveHistoryList.Add(history);
            }
            
            int lastSpurtCandidateNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgCandidateList = new List<LastSpurtCandidate>(lastSpurtCandidateNum);
            for (int i = 0; i < lastSpurtCandidateNum; ++i)
            {
                var candidate = new LastSpurtCandidate();
                candidate.distance = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                candidate.speed = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                candidate.needTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                DbgCandidateList.Add(candidate);
            }
            
            DbgUseCandidate = new LastSpurtCandidate();
            DbgUseCandidate.distance = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgUseCandidate.speed = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgUseCandidate.needTime = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgLastSpurtCalcCount = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            DbgBaseSpeedWithMotivation = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseStaminaWithMotivation = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBasePowWithMotivation = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseGutsWithMotivation = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseWizWithMotivation = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseSpeedWithMotivationRaceType = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseStaminaWithMotivationRaceType = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBasePowWithMotivationRaceType = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseGutsWithMotivationRaceType = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
            DbgBaseWizWithMotivationRaceType = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
        }
        
        /// <summary>
        /// List<CompeteGroupHorse>のシリアライズ。
        /// </summary>
        private void SerializeCompeteGroupList(List<CompeteGroupHorse> groupHorseList, List<byte[]> tmpBytes)
        {
            int groupHorseNum = groupHorseList != null ? groupHorseList.Count : 0;
            tmpBytes.Add(BitConverter.GetBytes(groupHorseNum));
            if (groupHorseList != null)
            {
                foreach (var groupHorse in groupHorseList)
                {
                    tmpBytes.Add(BitConverter.GetBytes(groupHorse.HorseIndex));
                    tmpBytes.Add(BitConverter.GetBytes(groupHorse.DistanceDiff));
                    tmpBytes.Add(BitConverter.GetBytes(groupHorse.LaneDistanceDiff));
                    tmpBytes.Add(BitConverter.GetBytes(groupHorse.Group));
                }
            }
        }
        
        /// <summary>
        /// List<CompeteGroupHorse>のデシリアライズ。
        /// </summary>
        private void DeserializeCompeteGroupList(ref List<CompeteGroupHorse> groupHorseList, byte[] bytes, ref int offset)
        {
            int groupHorseNum = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
            groupHorseList = new List<CompeteGroupHorse>();
            for (int i = 0; i < groupHorseNum; ++i)
            {
                int horseIndex = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                float distanceDiff = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                float laneDiff = RaceUtil.BytesToFloatAddOffset(bytes, ref offset);
                int group = RaceUtil.BytesToInt32AddOffset(bytes, ref offset);
                var info = new CompeteGroupHorse(horseIndex, distanceDiff, laneDiff, group);
                groupHorseList.Add(info);
            }
        }
    }
}
#endif

using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// RaceSimulateDataのヘッダ。
    /// </summary>
    //-------------------------------------------------------------------
    public class RaceSimulateDataHeader
    {
        public const int SIMULATE_DATA_VERSION_20200406 = 20200406; // 一部のストーリーレースのassetでシリアライズされている。
        public const int SIMULATE_DATA_VERSION_20200708 = 20200708;
        public const int SIMULATE_DATA_VERSION_20201027 = 20201027;
        public const int SIMULATE_DATA_VERSION_100000000 = 100000000;
        public const int SIMULATE_DATA_VERSION_100000001 = 100000001;
        public const int SIMULATE_DATA_VERSION_100000002 = 100000002;
        public const int SIMULATE_DATA_VERSION = SIMULATE_DATA_VERSION_100000002;

        public int Version = SIMULATE_DATA_VERSION;

        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public RaceSimulateDataHeader(int version)
        {
            Version = version;
        }

        /// <summary>
        /// コンストラクタ。byte配列から復元。
        /// </summary>
        public RaceSimulateDataHeader(byte[] byteArray)
        {
            Deserialize(byteArray);
        }        
        
        /// <summary>
        /// 自身をbyte配列に変換。
        /// </summary>
        public byte[] Serialize()
        {
            //-----------------------------------------------------------
            // 保存するデータをbyte配列で取得。
            //-----------------------------------------------------------
            byte[][] bytesList = new byte[1][];
            bytesList[0] = BitConverter.GetBytes(Version);

            //-----------------------------------------------------------
            // RaceSimulateDataHeader１件あたりのバイト数計算。
            //-----------------------------------------------------------
            int numBytes = bytesList.Sum(b => b.Length); // 保存するデータ。

            //-----------------------------------------------------------
            // RaceSimulateDataHeader１件を格納するbyte配列。
            //-----------------------------------------------------------
            byte[] retBytes = new byte[numBytes];

            //-----------------------------------------------------------
            // byte配列にデータを詰める。
            //-----------------------------------------------------------
            int destOffset = 0;

            for (int i = 0; i < bytesList.Length; ++i)
            {
                Array.Copy(bytesList[i], 0, retBytes, destOffset, bytesList[i].Length);
                destOffset += bytesList[i].Length;
            }

            return retBytes;
        }

        /// <summary>
        /// byte配列から自身を復元。
        /// </summary>
        /// <param name="bytes"></param>
        private void Deserialize(byte[] bytes)
        {
            int offset = 0;

            Version = BitConverter.ToInt32(bytes, offset);
            offset += sizeof(float);
        }
    };
}
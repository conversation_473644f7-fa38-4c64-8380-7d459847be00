#if STANDALONE_SIMULATOR || UNITY_EDITOR
#if CYG_DEBUG
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /// <summary>
    /// ラストスパート計算結果格納用。
    /// </summary>
    public class LastSpurtParam
    {
        public int Evaluate;
        public float StartDistance;
        public float TargetSpeed;
        public float Hp;
        public float ExceedNeedHp;
    }
}
#endif
#endif

#if CYG_DEBUG
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// １ハロン毎の情報。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseFurlongData
    {
        public float Time;
        public float Hp;
        public float LastSpeed;
        public float Lane;
    };
    
#if STANDALONE_SIMULATOR || UNITY_EDITOR
    //-------------------------------------------------------------------
    /// <summary>
    /// ハロン毎情報保持/記録。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseFurlongStat
    {
        public HorseFurlongData[] FurlongDatas { get; private set; }

        //---------------------------------------------------------------
        public HorseFurlongStat( int furlongNum )
        {
            FurlongDatas = new HorseFurlongData[ furlongNum ];
        }

        //---------------------------------------------------------------
        public void RecordIfNeeded( IHorseRaceInfoSimulate horse, float accumulateTime, float deltaTime, float currentMovedDistance )
        {
            float newDistance = horse.GetDistance() + currentMovedDistance;
            int furlongIndex = ( int )( newDistance * Gallop.RaceDefine.ONE_FURONG_DISTANCE_INV ) - 1;

            // 1ハロン到達前は無視。
            if( furlongIndex < 0 || furlongIndex >= FurlongDatas.Length )
            {
                return;
            }
            // 記録済みなら無視。
            if( FurlongDatas[ furlongIndex ] != null )
            {
                return;
            }

            FurlongDatas[ furlongIndex ] = new HorseFurlongData()
            {
                Time = _CalcFurlongTime( accumulateTime, deltaTime, horse.GetDistance(), currentMovedDistance ),
                Hp = horse.GetHp(),
                LastSpeed = horse.GetLastSpeed(),
                Lane = horse.GetLaneDistance(),
            };
        }

        //---------------------------------------------------------------
        private static float _CalcFurlongTime( float accumulateTime, float deltaTime, float distance, float currentMovedDistance )
        {
            float newDistance = distance + currentMovedDistance;
            float overDistance = newDistance % Gallop.RaceDefine.ONE_FURONG_DISTANCE;
            float rate = overDistance / currentMovedDistance;
            rate = RaceUtilMath.Clamp(rate , 0.0f , 1.0f);
            return accumulateTime + deltaTime * rate;
        }
    }
#endif
}

#endif

#if STANDALONE_SIMULATOR
#if CYG_DEBUG
#define ENABLE_LOG_OUTPUT
#endif
using System.Diagnostics;

namespace StandaloneSimulator
{
    /// <summary>
    /// STANDALONE_SIMULATORビルド時のDebug.Log系・Debug.Assertを偽装してアプリケーションでフックするためのモジュール
    /// 非STANDALONE_SIMULATOR時はGallopで定義されたグローバル空間のDebugクラスの関数群が使われることを想定している。
    /// </summary>
    public static class Debug
    {
        //
        // Summary:
        //     Assert a condition and logs an error message to the Unity console on failure.
        //
        // Parameters:
        //   condition:
        //     Condition you expect to be true.
        //
        //   context:
        //     Object to which the message applies.
        //
        //   message:
        //     String or object to be converted to string representation for display.
        [Conditional("CYG_DEBUG")]
        public static void Assert(bool condition)
        {
#if CYG_DEBUG
            System.Diagnostics.Debug.Assert(condition);
#endif
        }

        //
        // Summary:
        //     Assert a condition and logs an error message to the Unity console on failure.
        //
        // Parameters:
        //   condition:
        //     Condition you expect to be true.
        //
        //   context:
        //     Object to which the message applies.
        //
        //   message:
        //     String or object to be converted to string representation for display.
        // [Conditional("UNITY_ASSERTIONS")]
        // public static void Assert(bool condition, object message, Object context)
        // {
        //     System.Diagnostics.Debug.Assert(condition, message, context);
        // }

        [Conditional("CYG_DEBUG")]
        public static void Assert(bool condition, string message)
        {
#if CYG_DEBUG
            System.Diagnostics.Debug.Assert(condition, message);
#endif
        }

        //
        // Summary:
        //     Logs a message to the Unity Console.
        //
        // Parameters:
        //   message:
        //     String or object to be converted to string representation for display.
        //
        //   context:
        //     Object to which the message applies.
        [Conditional("CYG_DEBUG")]
        public static void Log(object message)
        {
#if CYG_DEBUG && ENABLE_LOG_OUTPUT
            RaceSimulateLogger.Output("[LOG] " + message);
#endif
        }

        //
        // Summary:
        //     A variant of Debug.Log that logs an error message to the console.
        //
        // Parameters:
        //   message:
        //     String or object to be converted to string representation for display.
        //
        //   context:
        //     Object to which the message applies.
        [Conditional("CYG_DEBUG")]
        public static void LogError(object message)
        {
#if CYG_DEBUG && ENABLE_LOG_OUTPUT
            RaceSimulateLogger.Output("[ERROR] " + message);
#endif
        }

        //
        // Summary:
        //     A variant of Debug.Log that logs a warning message to the console.
        //
        // Parameters:
        //   message:
        //     String or object to be converted to string representation for display.
        //
        //   context:
        //     Object to which the message applies.
        [Conditional("CYG_DEBUG")]
        public static void LogWarning(object message)
        {
#if CYG_DEBUG && ENABLE_LOG_OUTPUT
            RaceSimulateLogger.Output("[WARN] " + message);
#endif
        }
    }
}
#endif

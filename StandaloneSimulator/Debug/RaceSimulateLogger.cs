#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.IO;
using System.Text;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ロガー。
    /// </summary>
    //-------------------------------------------------------------------
    public static class RaceSimulateLogger
    {
#if STANDALONE_SIMULATOR
        [ThreadStatic]
#endif
        private static ILogger _impl;

        public static void Init()
        {
            _impl = CreateLoggerImpl();
            _impl.Init();
        }

        public static void SetLogDir(string logDir)
        {
            _impl.SetLogDir(logDir);
        }

        public static void Output(string log)
        {
            _impl?.Output(log);
        }

        public static void Term()
        {
            _impl.Term();
            _impl = null;
        }

        private static ILogger CreateLoggerImpl()
        {
        #if CYG_DEBUG
            return new LoggerFile();
        #else
            return new LoggerNull();
        #endif
        }

        public static string GetSimulateTag()
        {
            return _impl.GetTag();
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// ロガー：インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ILogger
    {
        void Init();
        void SetLogDir(string logDir);
        void Output(string log);
        void Term();
        string GetTag();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// ロガー：ファイル出力。
    /// </summary>
    //-------------------------------------------------------------------
    public class LoggerFile : ILogger
    {
        private const int FLUSH_SIZE = 1024;

        private string _logTag;
        private StringBuilder _strBuilder = new StringBuilder(FLUSH_SIZE);
        private string _logDir;

        private const string DEFAULT_LOG_DIR = "Log";

        public void Init()
        {
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var curTime = DateTime.Now;
            _logTag = $"{curTime.Month:D2}{curTime.Day:D2}{curTime.Hour:D2}{curTime.Minute:D2}{curTime.Millisecond}-{process.Id}";
        }

        public void SetLogDir(string logDir)
        {
            _logDir = logDir;
        }

        public void Output(string log)
        {
            _strBuilder.AppendLine($"[{_logTag}] [{DateTime.Now.ToString("MM/dd HH:mm:ss")}] {log}");

            // 複数プロセス立ち上げられてる場合でも、書き込むログファイルは同じ。
            // 書き込み時の競合を起こさないように、一定量バッファに溜まったらファイルに書き出し、閉じる。
            if (_strBuilder.Length > FLUSH_SIZE)
            {
                Flush();
            }
        }

        private void Flush()
        {
            try
            {
                var logDirName = GetLogDir();
                var logFileName = GetLogFileName();
#if !PUBLISHED
                Directory.CreateDirectory(logDirName);
#endif
                using (var sw = new StreamWriter(logDirName + "/" + logFileName, true))
                {
                    sw.Write(_strBuilder.ToString());
                }
                _strBuilder.Length = 0;
            }
            catch (Exception e)
            {
                _strBuilder.AppendLine($"ログをファイルに書き込み中に例外が発生したため書き込めませんでした。例外:{e.Message}");
            }
        }

        public void Term()
        {
            Flush();
        }

        public string GetTag() { return _logTag; }

        private string GetLogDir()
        {
            return !string.IsNullOrEmpty(_logDir) ? _logDir : DEFAULT_LOG_DIR;
        }

        private static string GetLogFileName()
        {
#if PUBLISHED
            var now = DateTime.Now;
            return $"http_app.{now.Year:D4}{now.Month:D2}{now.Day:D2}";
#else
            // １時間ごとに１ファイル。
            var now = DateTime.Now;
            return $"log_{now.Hour:D2}.log";
#endif

        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// ロガー：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class LoggerNull : ILogger
    {
        public void Init() { }
        public void SetLogDir(string logDir) { }
        public void Output(string log) { }
        public void Term() { }
        public string GetTag() { return string.Empty; }
    }
}
#endif

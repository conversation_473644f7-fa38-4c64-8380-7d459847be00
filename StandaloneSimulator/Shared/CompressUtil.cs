using System;
using System.Collections;
using System.IO;
using System.IO.Compression;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// Compress/UnCompress処理
    /// 元々はRaceUtil内にあったが、各所で使われていたので切り出した
    /// </summary>
    public static class CompressUtil
    {
        #region <バイト配列圧縮/解凍>
        /// <summary>
        /// バイト配列をGZip圧縮。
        /// </summary>
        /// <returns>圧縮したバイト配列。</returns>
        public static byte[] Compress(byte[] unCompressed)
        {
            byte[] retCompressed = null;

            using (var outSt = new MemoryStream())
            using (var compSt = new GZipStream(outSt, CompressionMode.Compress))
            {
                compSt.Write(unCompressed, 0, unCompressed.Length);
                compSt.Close();

                retCompressed = outSt.ToArray();
            }

            return retCompressed;
        }

        /// <summary>
        /// GZip圧縮済みバイト配列を解凍。
        /// </summary>
        /// <returns>解凍したバイト配列。</returns>
        public static byte[] UnCompress(byte[] compressed)
        {
            byte[] retUnCompressed = null;

            using (var inSt = new MemoryStream(compressed))
            using (var decompSt = new GZipStream(inSt, CompressionMode.Decompress))
            using (var outSt = new MemoryStream())
            {
                byte[] tmpBuf = new byte[1024];
                int readSize = 0;

                while ((readSize = decompSt.Read(tmpBuf, 0, tmpBuf.Length)) > 0)
                {
                    outSt.Write(tmpBuf, 0, readSize);
                }

                retUnCompressed = outSt.ToArray();
            }

            return retUnCompressed;
        }
        #endregion // <バイト配列圧縮/解凍>
    }
}
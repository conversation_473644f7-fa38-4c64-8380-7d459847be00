using System.Collections;

    public enum CourseParamType
    {
        Corner,             // コーナー
        GroundChange,       // 地面変化。
        Straight,           // 直線。
        LaneMaxChange,      // レーン最大値変化。
        FirstBgmLandscape,  // １曲目BGM制御（横画面）。
        SecondBgmLandscape, // ２曲目BGM制御（横画面）。
        <PERSON><PERSON><PERSON><PERSON><PERSON>rig<PERSON>,      // 実況トリガー地点。
        MoveLanePoint,      // インorアウトに寄る。
        FirstBgm,           // １曲目BGM制御。 
        SecondBgm,          // ２曲目BGM制御。 
        CrowdMode,          // 距離による歓声制御。 
        Slope,              // 坂。
    }


    [System.Serializable]
    public class CourseParam
    {
        public CourseParamType _paramType;
        public float _distance;                 // 内(LaneDistance=0)基準
        public float _distanceOffsetPerLane;    // 内/外で可変な場合に、LaneDistanceが1増加したときに変動させるdistance値

#if GALLOP
        public int[] _values;
        public int[] Values => _values;
#else
        public string _values;
        public int[] Values;
        public void LoadPostProcess()
        {
            Values = StandaloneSimulator.YamlLoadHelper.Hex2Int(_values);
            _values = null;
        }
#endif
        
        public const int STRAIGHT_VALUE_NUM = 3;
        public const int STRAIGHT_VALUE_INDEX_START_END = 0;
        public const int STRAIGHT_VALUE_INDEX_TYPE = 1;
        public const int STRAIGHT_VALUE_INDEX_IS_LAST = 2;

        public const int MOVELANEPOINT_VALUE_NUM = 1;
        public const int MOVELANEPOINT_VALUE_INDEX_IN_OR_OUT = 0;

        public const int LANEMAXCHANGE_VALUE_NUM = 1;
        public const int LANEMAXCHANGE_VALUE_INDEX_LANEMAX = 0;

        public const int CORNER_VALUE_NUM = 2;
        public const int CORNER_VALUE_INDEX_NO = 0;
        public const int CORNER_VALUE_INDEX_DISTANCE = 1;

        private const int SLOPE_VALUE_NUM = 2;
        private const int SLOPE_VALUE_INDEX_PER = 0;
        private const int SLOPE_VALUE_INDEX_LENGTH = 1;

        /// <summary>
        /// 引数を坂として解釈。
        /// </summary>
        /// <param name="valueArray">引数配列。</param>
        /// <param name="slopePer">勾配角度。</param>
        /// <param name="slopeType">坂種類。</param>
        /// <param name="slopeLength">坂継続距離。</param>
        /// <returns>引数解釈の成功有無。</returns>
        public static bool GetSlopeValue(int[] valueArray, out float slopePer, out Gallop.RaceDefine.SlopeType slopeType, out float slopeLength)
        {
            slopePer = 0;
            slopeType = Gallop.RaceDefine.SlopeType.Null;
            slopeLength = 0;
            if (valueArray.Length != SLOPE_VALUE_NUM)
            {
                return false;
            }
            
            const float SLOPE_PER_ACCURACY = 10000.0f;
            slopePer = valueArray[SLOPE_VALUE_INDEX_PER] / SLOPE_PER_ACCURACY;
            slopeType = StandaloneSimulator.HorseSlopeCalculator.SlopePer2SlopeType(slopePer);
            slopeLength = valueArray[SLOPE_VALUE_INDEX_LENGTH];
            return true;
        }

        public class CourseParamDistanceComparer : IComparer
        {
            public int Compare( object lh, object rh )
            {
                var lhParam = lh as CourseParam;
                var rhParam = rh as CourseParam;
                if( null == lhParam || null == rhParam )
                {
                    return 0;
                }

                return ( int )( ( lhParam._distance - rhParam._distance ) * 1000.0f ); // 差分が小数で出るので適当に1000倍。
            }
        }
    }

using System;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 発動条件キーワードに対応する条件クラス生成・タグ生成関数。
    /// </summary>
    public class TriggerCreateFunc
    {
        // GALLOP用: タグ検索用リスト（シミュレーターでは不要）
        public Func<StandaloneSimulator.SkillTriggerBuildInfo, List<SkillDefine.SkillTriggerTag>> FuncGetTagList;
        // シミュレート用: スキルトリガー情報（GALLOPでは不要）
        public Func<StandaloneSimulator.SkillTriggerBuildInfo, StandaloneSimulator.ISkillTrigger> FuncCreateTriggerSimulate;
        public TriggerCreateFunc(Func<StandaloneSimulator.SkillTriggerBuildInfo, StandaloneSimulator.ISkillTrigger> funcCreateTrigger) : this((_) => SkillDefine.SkillTriggerTag.Null, funcCreateTrigger) {}
        public TriggerCreateFunc(
            Func<StandaloneSimulator.SkillTriggerBuildInfo, SkillDefine.SkillTriggerTag> funcGetTag, 
            Func<StandaloneSimulator.SkillTriggerBuildInfo, StandaloneSimulator.ISkillTrigger> funcCreateTriggerSimulate)
        {
            FuncGetTagList = (b) => new List<SkillDefine.SkillTriggerTag>() { funcGetTag(b) };
            FuncCreateTriggerSimulate = funcCreateTriggerSimulate;
        }
        public TriggerCreateFunc(
            Func<StandaloneSimulator.SkillTriggerBuildInfo, List<SkillDefine.SkillTriggerTag>> funcGetTagList, 
            Func<StandaloneSimulator.SkillTriggerBuildInfo, StandaloneSimulator.ISkillTrigger> funcCreateTriggerSimulate)
        {
            FuncGetTagList = funcGetTagList;
            FuncCreateTriggerSimulate = funcCreateTriggerSimulate;
        }
        
#if GALLOP
        // タグ生成用
        public TriggerCreateFunc(Func<StandaloneSimulator.SkillTriggerBuildInfo, SkillDefine.SkillTriggerTag> funcGetTag = null)
        {
            // タグ指定なし
            if (funcGetTag == null)
            {
                FuncGetTagList = (_) => new List<SkillDefine.SkillTriggerTag>() { SkillDefine.SkillTriggerTag.Null };
                return;
            }
            
            // タグ指定あり
            FuncGetTagList = (b) => new List<SkillDefine.SkillTriggerTag>() { funcGetTag(b) };
        }
        
        public TriggerCreateFunc(
            Func<StandaloneSimulator.SkillTriggerBuildInfo, List<SkillDefine.SkillTriggerTag>> funcGetTagList)
        {
            FuncGetTagList = funcGetTagList;
        }
#endif
    }
    
#if GALLOP
    /// <summary>
    /// ゲーム内static変数定義：レース
    /// StaticVariableDefineRaceのpartialクラス（下の【TriggerTagCreateFunc】と併記したかったのでこちらに記載）
    /// </summary>
    public partial class StaticVariableDefine
    {
        public partial class Race
        {
            public class SkillTriggerBase
            {
                private static readonly TriggerCreateFunc _triggerCreateFuncNull = new Gallop.TriggerCreateFunc();
                
                // タグ専用（トリガー関数のコンストラクタは不要）
                // ※ ここにトリガーを追加した場合は 【TriggerCreateFunc】 にも追加すること
                public static readonly Dictionary<string, Gallop.TriggerCreateFunc> TriggerTagCreateFunc = new Dictionary<string, Gallop.TriggerCreateFunc>()
                {
                    #region タグ未割当の発動条件
                    {
                        "random_lot",
                        _triggerCreateFuncNull
                    },
                    {
                        "random_lot_other_activate",
                        _triggerCreateFuncNull
                    },
                    {
                        "corner_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "month",
                        _triggerCreateFuncNull
                    },
                    {
                        "grade",
                        _triggerCreateFuncNull
                    },
                    {
                        "race_id",
                        _triggerCreateFuncNull
                    },
                    {
                        "time",
                        _triggerCreateFuncNull
                    },
                    {
                        "course_distance",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_dirtgrade",
                        _triggerCreateFuncNull
                    },
                    {
                        "accumulatetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "accumulatetime_random",
                        _triggerCreateFuncNull
                    },
                    {
                        "slope",
                        _triggerCreateFuncNull
                    },
                    {
                        "up_slope_random",
                        _triggerCreateFuncNull
                    },
                    {
                        "down_slope_random",
                        _triggerCreateFuncNull
                    },
                    {
                        "up_slope_random_later_half",
                        _triggerCreateFuncNull
                    },
                    {
                        "down_slope_random_later_half",
                        _triggerCreateFuncNull
                    },
                    {
                        "corner",
                        _triggerCreateFuncNull
                    },
                    {
                        "furlong",
                        _triggerCreateFuncNull
                    },
                    {
                        "furlong_random",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in10_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in20_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in30_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in40_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in50_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in60_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in70_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in80_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_in90_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out10_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out20_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out30_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out40_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out50_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out60_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out70_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out80_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_out90_continue",
                        _triggerCreateFuncNull
                    },
                    {
                        "order",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_lastspurt",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_lastspurt",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_final_corner_end",
                        _triggerCreateFuncNull
                    },
                    {
                        "order_rate_final_corner_end",
                        _triggerCreateFuncNull
                    },
                    {
                        "gate_number",
                        _triggerCreateFuncNull
                    },
                    {
                        "gate_number_rate",
                        _triggerCreateFuncNull
                    },
                    {
                        "distance_rate",
                        _triggerCreateFuncNull
                    },
                    {
                        "remain_distance",
                        _triggerCreateFuncNull
                    },
                    {
                        "remain_distance_viewer_id",
                        _triggerCreateFuncNull
                    },
                    {
                        "distance_diff_rate",
                        _triggerCreateFuncNull
                    },
                    {
                        "all_phase_random",
                        _triggerCreateFuncNull
                    },
                    {
                        "distance_rate_after_random",
                        _triggerCreateFuncNull
                    },
                    {
                        "other_behind",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_all",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_front",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_side",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_all_continuetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_front_continuetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_side_continuetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_front_hp_empty",
                        _triggerCreateFuncNull
                    },
                    {
                        "blocked_front_hp_empty_continuetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_nige",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_senko",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_sashi",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_oikomi",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_nige_otherself",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_senko_otherself",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_sashi_otherself",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_count_oikomi_otherself",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_is_max",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_count_nige",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_count_senko",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_count_sashi",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_count_oikomi",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_opponent_count_nige",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_opponent_count_senko",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_opponent_count_sashi",
                        _triggerCreateFuncNull
                    },
                    {
                        "running_style_temptation_opponent_count_oikomi",
                        _triggerCreateFuncNull
                    },
                    {
                        "temptation_count_behind",
                        _triggerCreateFuncNull
                    },
                    {
                        "temptation_opponent_count_behind",
                        _triggerCreateFuncNull
                    },
                    {
                        "temptation_count_infront",
                        _triggerCreateFuncNull
                    },
                    {
                        "temptation_opponent_count_infront",
                        _triggerCreateFuncNull
                    },
                    {
                        "temptation_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "compete_fight_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_goodstart",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_badstart",
                        _triggerCreateFuncNull
                    },
                    {
                        "base_speed",
                        _triggerCreateFuncNull
                    },
                    {
                        "base_stamina",
                        _triggerCreateFuncNull
                    },
                    {
                        "base_power",
                        _triggerCreateFuncNull
                    },
                    {
                        "base_guts",
                        _triggerCreateFuncNull
                    },
                    {
                        "base_wiz",
                        _triggerCreateFuncNull
                    },
                    {
                        "hp",
                        _triggerCreateFuncNull
                    },
                    {
                        "hp_per",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_hp_empty",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_hp_empty_onetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "current_speed",
                        _triggerCreateFuncNull
                    },
                    {
                        "speed_order",
                        _triggerCreateFuncNull
                    },
                    {
                        "visiblehorse",
                        _triggerCreateFuncNull
                    },
                    {
                        "change_order",
                        _triggerCreateFuncNull
                    },
                    {
                        "change_order_onetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "lane_type",
                        _triggerCreateFuncNull
                    },
                    {
                        "lane_per",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_move_lane",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_overtake",
                        _triggerCreateFuncNull
                    },
                    {
                        "overtake_target_time",
                        _triggerCreateFuncNull
                    },
                    {
                        "overtake_target_no_order_up_time",
                        _triggerCreateFuncNull
                    },
                    {
                        "overtake_target_no_order_down_time",
                        _triggerCreateFuncNull
                    },
                    {
                        "final_grade_higher_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "final_grade_lower_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_surrounded",
                        _triggerCreateFuncNull
                    },
                    {
                        "near_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "near_around_accumulatetime",
                        _triggerCreateFuncNull
                    },
                    {
                        "distance_diff_top",
                        _triggerCreateFuncNull
                    },
                    {
                        "distance_diff_top_float",
                        _triggerCreateFuncNull
                    },
                    {
                        "distance_diff_infront",
                        _triggerCreateFuncNull
                    },
                    {
                        "distance_diff_behind",
                        _triggerCreateFuncNull
                    },
                    {
                        "bashin_diff_infront",
                        _triggerCreateFuncNull
                    },
                    {
                        "bashin_diff_behind",
                        _triggerCreateFuncNull
                    },
                    {
                        "near_infront_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "near_behind_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "same_skill_horse_count",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_used_skill_id",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_behind_in",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_behind_out",
                        _triggerCreateFuncNull
                    },
                    {
                        "infront_near_time",
                        _triggerCreateFuncNull
                    },
                    {
                        "behind_near_time",
                        _triggerCreateFuncNull
                    },
                    {
                        "infront_near_lane_time",
                        _triggerCreateFuncNull
                    },
                    {
                        "behind_near_lane_time",
                        _triggerCreateFuncNull
                    },
                    {
                        "behind_near_lane_time_set1",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_exist_chara_id",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_other_character_activate_advantage_skill",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_activate_other_skill_detail",
                        _triggerCreateFuncNull
                    },
                    {
                        "motivation",
                        _triggerCreateFuncNull
                    },
                    {
                        "is_exist_skill_id",
                        _triggerCreateFuncNull
                    },

                    #endregion

                    #region タグ有り発動条件

                    {
                        "activate_count_speed",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateSpeedSkillCount.GetTag())
                    },
                    {
                        "activate_count_heal",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateHealSkillCount.GetTag())
                    },
                    {
                        "activate_count_start",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateCountPhase.GetTag())
                    },
                    {
                        "activate_count_middle",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateCountPhase.GetTag())
                    },
                    {
                        "activate_count_end_after",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateCountPhase.GetTag())
                    },
                    {
                        "activate_count_all",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateCount.GetTag())
                    },
                    {
                        "activate_count_later_half",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateCount.GetTag())
                    },
                    {
                        "activate_count_all_team",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateCount.GetTag())
                    },
                    {
                        "is_activate_any_skill",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerIsActivateAnySkill.GetTag())
                    },
                    {
                        "is_activate_heal_skill",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerIsActivateHealSkill.GetTag())
                    },
                    {
                        "activate_skill_tag_group",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerActivateSkillTagGroup.GetTag())
                    },
                    {
                        "change_order_up_middle",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderUpPhaseMiddle.GetTag())
                    },
                    {
                        "change_order_up_end_after",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderUpPhaseEndAfter.GetTag())
                    },
                    {
                        "change_order_up_corner",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderUpCorner.GetTag())
                    },
                    {
                        "change_order_up_lastspurt",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderUpLastSpurt.GetTag())
                    },
                    {
                        "change_order_up_finalcorner_after",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderUpFinalCornerAfter.GetTag())
                    },
                    {
                        "change_order_up_later_half",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderUpLaterHalf.GetTag())
                    },
                    {
                        "change_order_down_middle",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderDownPhaseMiddle.GetTag())
                    },
                    {
                        "change_order_down_end_after",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderDownPhaseEndAfter.GetTag())
                    },
                    {
                        "change_order_down_corner",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderDownCorner.GetTag())
                    },
                    {
                        "change_order_down_lastspurt",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderDownLastSpurt.GetTag())
                    },
                    {
                        "change_order_down_finalcorner_after",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderDownFinalCornerAfter.GetTag())
                    },
                    {
                        "change_order_down_later_half",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerChangeOrderDownLaterHalf.GetTag())
                    },
                    {
                        "ground_type",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) => StandaloneSimulator.SkillTriggerGroundType.GetTag(buildInfo.valueRh))
                    },
                    {
                        SkillDefine.SKILL_TRIGGER_KEYWORD_ALWAYS,
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerAlways.GetTag())
                    },
                    {
                        "ground_condition",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerGroundCondition.GetTag())
                    },
                    {
                        "rotation",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerCourseRotation.GetTag())
                    },
                    {
                        SkillDefine.SKILL_TRIGGER_KEYWORD_DISTANCE_TYPE,
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerCourseDistanceType.GetTag(buildInfo.valueRh))
                    },
                    {
                        "is_basis_distance",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerCourseBasisDistance.GetTag())
                    },
                    {
                        "weather",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerWeather.GetTag())
                    },
                    {
                        "season",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerSeason.GetTag())
                    },
                    {
                        "track_id",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerRaceTrackId.GetTag())
                    },
                    {
                        "straight_random",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerStraightRandom.GetTag())
                    },
                    {
                        "while_in_straight_random",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerWhileInStraightRandom.GetTag())
                    },
                    {
                        "phase_straight_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) => 
                                StandaloneSimulator.SkillTriggerPhaseStraightRandom.GetTagList(buildInfo.valueRh))
                    },
                    {
                        "phase_latter_half_straight_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) => 
                                // SkillTriggerPhaseStraightRandomを継承している
                                StandaloneSimulator.SkillTriggerPhaseStraightRandom.GetTagList(buildInfo.valueRh))
                    },
                    {
                        "phase_first_half_straight_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                // SkillTriggerPhaseStraightRandomを継承している
                                StandaloneSimulator.SkillTriggerPhaseStraightRandom.GetTagList(buildInfo.valueRh))
                    },
                    {
                        "last_straight_random",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerLastStraightRandom.GetTag())
                    },
                    {
                        "is_last_straight_onetime",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerLastStraightOneTime.GetTag())
                    },
                    {
                        "is_last_straight",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerLastStraight.GetTag())
                    },
                    {
                        "straight_front_type",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerStraightFrontType.GetTag())
                    },
                    {
                        "corner_random",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerCornerRandom.GetTag())
                    },
                    {
                        "phase_corner_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseCornerRandom.GetTagList(buildInfo.valueRh))
                    },
                    {
                        "phase_latter_half_corner_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseCornerRandom.GetTagList(buildInfo.valueRh))
                    },
                    {
                        "all_corner_random",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerAllCornerRandom.GetTag())
                    },
                    {
                        "is_finalcorner",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerFinalCorner.GetTag())
                    },
                    {
                        "is_finalcorner_random",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerFinalCornerRandom.GetTag())
                    },
                    {
                        "is_finalcorner_laterhalf",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerFinalCornerLaterHalf.GetTag())
                    },
                    {
                        "is_finalcorner_random_middle",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerFinalCornerRandomPhaseMiddle.GetTagList())
                    },
                    {
                        "is_finalcorner_random_end",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerFinalCornerRandomPhaseEnd.GetTagList())
                    },
                    {
                        SkillDefine.SKILL_TRIGGER_KEYWORD_RUNNING_STYLE,
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) => StandaloneSimulator.SkillTriggerRunningStyle.GetTag(buildInfo.valueRh))
                    },

                    {
                        "popularity",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerPopularity.GetTag())
                    },
                    {
                        "popularity_rate",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerPopularityRate.GetTag())
                    },
                    {
                        "popularity_topicon_num",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerPopularityTopIconNum.GetTag())
                    },
                    {
                        "popularity_icon_num",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerPopularityIconNum.GetTag())
                    },
                    {
                        "post_number",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerPostNumber.GetTag())
                    },
                    {
                        "post_number_rate",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerPostNumberRate.GetTag())
                    },
                    {
                        "phase",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhase.GetTagList(buildInfo.op, buildInfo.valueRh))
                    },
                    {
                        "phase_firsthalf",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseFirstHalf.GetTag(buildInfo.valueRh))
                    },
                    {
                        "phase_firstquarter",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseFirstQuarter.GetTag(buildInfo.valueRh))
                    },
                    {
                        "phase_laterhalf",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseLaterHalf.GetTag(buildInfo.valueRh))
                    },
                    {
                        "phase_laterquarter",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseLaterQuarter.GetTag(buildInfo.valueRh))
                    },
                    {
                        "phase_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) => StandaloneSimulator.SkillTriggerPhaseRandom.GetTag(buildInfo.valueRh))
                    },
                    {
                        "phase_laterhalf_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseLaterHalfRandom.GetTag(buildInfo.valueRh))
                    },
                    {
                        "phase_firsthalf_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseFirstHalfRandom.GetTag(buildInfo.valueRh))
                    },
                    {
                        "phase_firstquarter_random",
                        new Gallop.TriggerCreateFunc(
                            (buildInfo) =>
                                StandaloneSimulator.SkillTriggerPhaseFirstQuarterRandom.GetTag(buildInfo.valueRh))
                    },
                    {
                        "running_style_count_same",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerRunningStyleCountSame.GetTag())
                    },
                    {
                        "running_style_count_same_rate",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerRunningStyleCountSameRate.GetTag())
                    },
                    {
                        "is_temptation",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerTemptation.GetTag())
                    },
                    {
                        "is_lastspurt",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerIsLastSpurt.GetTag())
                    },
                    {
                        "running_style_equal_popularity_one",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerRunningStyleEqualPopularityOne.GetTag())
                    },
                    {
                        "lastspurt",
                        new Gallop.TriggerCreateFunc(
                            (_) => StandaloneSimulator.SkillTriggerLastSpurt.GetTag())
                    },

                    #endregion
                };
            }
        }
    }
#endif
    
    public partial class SkillDefine
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        // シミュレート用トリガー関数（タグは不要）
        // ※ ここにトリガーを追加した場合は 【TriggerTagCreateFunc】 にも追加すること
        public static readonly Dictionary<string, Gallop.TriggerCreateFunc> TriggerCreateFunc = new Dictionary<string, Gallop.TriggerCreateFunc>()
        {
            #region タグ未割当の発動条件
            {
                "random_lot", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRandomLot(buildInfo))
            },
            {
                "random_lot_other_activate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRandomLotOtherActivate(buildInfo))
            },
            {
                "corner_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCourseCornerCount(buildInfo))
            },
            {
                "month", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerMonth(buildInfo))
            },
            {
                "grade", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerGrade(buildInfo))
            },
            {
                "race_id", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRaceId(buildInfo))
            },
            {
                "time",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerTime(buildInfo))
            },
            {
                "course_distance",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCourseDistance(buildInfo))
            },
            {
                "is_dirtgrade",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerIsDirtGrade(buildInfo))
            },
            {
                "accumulatetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRaceAccumulateTime(buildInfo))
            },
            {
                "accumulatetime_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRaceAccumulateTimeRandom(buildInfo))
            },
            {
                "slope", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerSlope(buildInfo))
            },
            {
                "up_slope_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerUpSlopeRandom(buildInfo))
            },
            {
                "down_slope_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerDownSlopeRandom(buildInfo))
            },
            {
                "up_slope_random_later_half",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerUpSlopeRandomLaterHalf(buildInfo))
            },
            {
                "down_slope_random_later_half",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerDownSlopeRandomLaterHalf(buildInfo))
            },
            {
                "corner", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCorner(buildInfo))
            },
            {
                "furlong", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFurlong(buildInfo))
            },
            {
                "furlong_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFurlongRandom(buildInfo))
            },
            {
                "order_rate_in10_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn10))
            },
            {
                "order_rate_in20_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn20))
            },
            {
                "order_rate_in30_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn30))
            },
            {
                "order_rate_in40_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn40))
            },
            {
                "order_rate_in50_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn50))
            },
            {
                "order_rate_in60_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn60))
            },
            {
                "order_rate_in70_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn70))
            },
            {
                "order_rate_in80_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn80))
            },
            {
                "order_rate_in90_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateInContinue(buildInfo, SkillDefine.OrderInType.OrderIn90))
            },
            {
                "order_rate_out10_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut10))
            },
            {
                "order_rate_out20_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut20))
            },
            {
                "order_rate_out30_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut30))
            },
            {
                "order_rate_out40_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut40))
            },
            {
                "order_rate_out50_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut50))
            },
            {
                "order_rate_out60_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut60))
            },
            {
                "order_rate_out70_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut70))
            },
            {
                "order_rate_out80_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut80))
            },
            {
                "order_rate_out90_continue", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateOutContinue(buildInfo, SkillDefine.OrderOutType.OrderOut90))
            },
            {
                "order", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrder(buildInfo))
            },
            {
                "order_rate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRate(buildInfo))
            },
            {
                "order_lastspurt", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderLastSpurt(buildInfo))
            },
            {
                "order_rate_lastspurt", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateLastSpurt(buildInfo))
            },
            {
                "order_final_corner_end", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderFinalCornerEnd(buildInfo))
            },
            {
                "order_rate_final_corner_end", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOrderRateFinalCornerEnd(buildInfo))
            },
            {
                "gate_number", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerGateNumber(buildInfo))
            },
            {
                "gate_number_rate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerGateNumberRate(buildInfo))
            },
            {
                "distance_rate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseDistanceRate(buildInfo))
            },
            {
                "remain_distance", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseRemainDistance(buildInfo))
            },
            {
                "remain_distance_viewer_id", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseViewerIdRemainDistance(buildInfo))
            },
            {
                "distance_diff_rate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseDistanceDiffRate(buildInfo))
            },
            {
                "all_phase_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerAllPhaseRandom(buildInfo))
            },
            {
                "distance_rate_after_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerDistanceRateAfterRandom(buildInfo))
            },
            {
                "other_behind", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOtherHorseBehind(buildInfo))
            },
            {
                "blocked_all", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlocked(buildInfo))
            },
            {
                "blocked_front", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlockedFront(buildInfo))
            },
            {
                "blocked_side", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlockedSide(buildInfo))
            },
            {
                "blocked_all_continuetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlockedAllContinueTime(buildInfo))
            },
            {
                "blocked_front_continuetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlockedFrontContinueTime(buildInfo))
            },
            {
                "blocked_side_continuetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlockedSideContinueTime(buildInfo))
            },
            {
                "blocked_front_hp_empty", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlockedFrontHpEmpty(buildInfo))
            },
            {
                "blocked_front_hp_empty_continuetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBeBlockedFrontHpEmptyContinueTime(buildInfo))
            },
            {
                "running_style_count_nige", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCount(buildInfo, Gallop.RaceDefine.RunningStyle.Nige))
            },
            {
                "running_style_count_senko", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCount(buildInfo, Gallop.RaceDefine.RunningStyle.Senko))
            },
            {
                "running_style_count_sashi", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCount(buildInfo, Gallop.RaceDefine.RunningStyle.Sashi))
            },
            {
                "running_style_count_oikomi", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCount(buildInfo, Gallop.RaceDefine.RunningStyle.Oikomi))
            },
            {
                "running_style_count_nige_otherself", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCountOtherSelf(buildInfo, Gallop.RaceDefine.RunningStyle.Nige))
            },
            {
                "running_style_count_senko_otherself", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCountOtherSelf(buildInfo, Gallop.RaceDefine.RunningStyle.Senko))
            },
            {
                "running_style_count_sashi_otherself", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCountOtherSelf(buildInfo, Gallop.RaceDefine.RunningStyle.Sashi))
            },
            {
                "running_style_count_oikomi_otherself", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCountOtherSelf(buildInfo, Gallop.RaceDefine.RunningStyle.Oikomi))
            },
            {
                "running_style_is_max", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerIsRunningStyleCountMax(buildInfo))
            },
            {
                "running_style_temptation_count_nige", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Nige, false))
            },
            {
                "running_style_temptation_count_senko", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Senko, false))
            },
            {
                "running_style_temptation_count_sashi", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Sashi, false))
            },
            {
                "running_style_temptation_count_oikomi", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Oikomi, false))
            },
            {
                "running_style_temptation_opponent_count_nige", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Nige, true))
            },
            {
                "running_style_temptation_opponent_count_senko", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Senko, true))
            },
            {
                "running_style_temptation_opponent_count_sashi", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Sashi, true))
            },
            {
                "running_style_temptation_opponent_count_oikomi", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleTemptationCount(buildInfo, Gallop.RaceDefine.RunningStyle.Oikomi, true))
            },
            {
                "temptation_count_behind", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerTemptationBehind(buildInfo, false))
            },
            {
                "temptation_opponent_count_behind", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerTemptationBehind(buildInfo, true))
            },
            {
                "temptation_count_infront", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerTemptationInfront(buildInfo, false))
            },
            {
                "temptation_opponent_count_infront", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerTemptationInfront(buildInfo, true))
            },
            {
                "temptation_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerTemptationCount(buildInfo))
            },
            {
                "compete_fight_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCompeteFightCount(buildInfo))
            },
            {
                "is_goodstart", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerGoodStart(buildInfo))
            },
            {
                "is_badstart", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBadStart(buildInfo))
            },
            {
                "base_speed", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBaseSpeed(buildInfo))
            },
            {
                "base_stamina", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBaseStamina(buildInfo))
            },
            {
                "base_power", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBasePower(buildInfo))
            },
            {
                "base_guts", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBaseGuts(buildInfo))
            },
            {
                "base_wiz", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBaseWiz(buildInfo))
            },
            {
                "hp",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHp(buildInfo))
            },
            {
                "hp_per", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHpPer(buildInfo))
            },
            {
                "is_hp_empty", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHpEmpty(buildInfo))
            },
            {
                "is_hp_empty_onetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHpEmptyOneTime(buildInfo))
            },
            {
                "current_speed", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCurrentSpeed(buildInfo))
            },
            {
                "speed_order",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerSpeedOrder(buildInfo))
            },
            {
                "visiblehorse", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerVisibleHorse(buildInfo))
            },
            {
                "change_order", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrder(buildInfo))
            },
            {
                "change_order_onetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderOneTime(buildInfo))
            },
            {
                "lane_type", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerLaneType(buildInfo))
            },
            {
                "lane_per", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerLanePer(buildInfo))
            },
            {
                "is_move_lane", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerMoveLane(buildInfo))
            },
            {
                "is_overtake", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOvertake(buildInfo))
            },
            {
                "overtake_target_time", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOvertakeTargetContinueTime(buildInfo))
            },
            {
                "overtake_target_no_order_up_time", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOvertakeTargetHaveNoOrderUpContinueTime(buildInfo))
            },
            {
                "overtake_target_no_order_down_time", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOvertakeTargetHaveNoOrderDownContinueTime(buildInfo))
            },
            {
                "final_grade_higher_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFinalGradeHigherCount(buildInfo))
            },
            {
                "final_grade_lower_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFinalGradeLowerCount(buildInfo))
            },
            {
                "is_surrounded", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerSurrounded(buildInfo))
            },
            {
                "near_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerNearHorseCount(buildInfo))
            },
            {
                "near_around_accumulatetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerNearAroundAccumulateTime(buildInfo))
            },
            {
                "distance_diff_top", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerDistanceDiffTop(buildInfo))
            },
            {
                "distance_diff_top_float",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerDistanceDiffTopFloat(buildInfo))
            },
            {
                "distance_diff_infront", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerDistanceDiffInfront(buildInfo))
            },
            {
                "distance_diff_behind", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerDistanceDiffBehind(buildInfo))
            },
            {
                "bashin_diff_infront", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBashinDiffInfront(buildInfo))
            },
            {
                "bashin_diff_behind", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerBashinDiffBehind(buildInfo))
            },
            {
                "near_infront_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerNearFrontHorseCount(buildInfo))
            },
            {
                "near_behind_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerNearBehindHorseCount(buildInfo))
            },
            {
                "same_skill_horse_count", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerSameSkillHorseCount(buildInfo))
            },
            {
                "is_used_skill_id",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerUsedParticularSkillId(buildInfo))
            },
            {
                "is_behind_in", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseBehindIn(buildInfo))
            },
            {
                "is_behind_out", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseBehindOut(buildInfo))
            },
            {
                "infront_near_time", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseInfrontNearTime(buildInfo))
            },
            {
                "behind_near_time", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseBehindNearTime(buildInfo))
            },
            {
                "infront_near_lane_time", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseInfrontNearLaneTime(buildInfo))
            },
            {
                "behind_near_lane_time", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseBehindNearLaneTime(buildInfo))
            },
            {
                "behind_near_lane_time_set1", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerHorseBehindNearLaneTimeParamSet(buildInfo, 0))
            },
            {
                "is_exist_chara_id", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerExistCharaId(buildInfo))
            },
            {
                "is_other_character_activate_advantage_skill",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerOtherCharacterActivateAdvantageSkill(buildInfo))
            },
            {
                "is_activate_other_skill_detail",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateOtherSkillDetail(buildInfo))
            },
            #endregion
            
            #region タグ有り発動条件（ただしタグ関数は不要なので削除）
            {
                "activate_count_speed", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateSpeedSkillCount(buildInfo))
            },
            {
                "activate_count_heal", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateHealSkillCount(buildInfo))
            },
            {
                "activate_count_start", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateCountPhase(buildInfo, Gallop.RaceDefine.HorsePhase.Start))
            },
            {
                "activate_count_middle", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateCountPhase(buildInfo, Gallop.RaceDefine.HorsePhase.MiddleRun))
            },
            {
                "activate_count_end_after", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateCountPhase(buildInfo, new List<Gallop.RaceDefine.HorsePhase>() { Gallop.RaceDefine.HorsePhase.End, Gallop.RaceDefine.HorsePhase.LastSpurt }))
            },
            {
                "activate_count_all", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateCount(buildInfo))
            },
            {
                "activate_count_later_half",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateCountLaterHalf(buildInfo))
            },
            {
                "activate_count_all_team",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateCountAllTeam(buildInfo))
            },
            {
                "is_activate_any_skill",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerIsActivateAnySkill(buildInfo))
            },
            {
                "is_activate_heal_skill",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerIsActivateHealSkill(buildInfo))
            },
            {
                "activate_skill_tag_group",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerActivateSkillTagGroup(buildInfo))
            },
            {
                "change_order_up_middle", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderUpPhaseMiddle(buildInfo))
            },
            {
                "change_order_up_end_after", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderUpPhaseEndAfter(buildInfo))
            },
            {
                "change_order_up_corner", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderUpCorner(buildInfo))
            },
            {
                "change_order_up_lastspurt", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderUpLastSpurt(buildInfo))
            },
            {
                "change_order_up_finalcorner_after", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderUpFinalCornerAfter(buildInfo))
            },
            {
                "change_order_up_later_half", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderUpLaterHalf(buildInfo))
            },
            {
                "change_order_down_middle", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderDownPhaseMiddle(buildInfo))
            },
            {
                "change_order_down_end_after", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderDownPhaseEndAfter(buildInfo))
            },
            {
                "change_order_down_corner", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderDownCorner(buildInfo))
            },
            {
                "change_order_down_lastspurt", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderDownLastSpurt(buildInfo))
            },
            {
                "change_order_down_finalcorner_after", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderDownFinalCornerAfter(buildInfo))
            },
            {
                "change_order_down_later_half", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerChangeOrderDownLaterHalf(buildInfo))
            },
            {
                "ground_type", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerGroundType(buildInfo))
            },
            {
                SkillDefine.SKILL_TRIGGER_KEYWORD_ALWAYS, 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerAlways(buildInfo))
            },
            {
                "ground_condition", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerGroundCondition(buildInfo))
            },
            {
                "rotation", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCourseRotation(buildInfo))
            },
            {
                SkillDefine.SKILL_TRIGGER_KEYWORD_DISTANCE_TYPE, 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCourseDistanceType(buildInfo))
            },
            {
                "is_basis_distance", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCourseBasisDistance(buildInfo))
            },
            {
                "weather", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerWeather(buildInfo))
            },
            {
                "season", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerSeason(buildInfo))
            },
            {
                "track_id", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRaceTrackId(buildInfo))
            },
            {
                "straight_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerStraightRandom(buildInfo))
            },
            {
                "while_in_straight_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerWhileInStraightRandom(buildInfo))
            },
            {
                "phase_straight_random",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseStraightRandom(buildInfo))
            },
            {
                "phase_latter_half_straight_random",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseLatterHalfStraightRandom(buildInfo))
            },
            {
                "phase_first_half_straight_random",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseFirstHalfStraightRandom(buildInfo))
            },
            {
                "last_straight_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerLastStraightRandom(buildInfo))
            },
            {
                "is_last_straight_onetime", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerLastStraightOneTime(buildInfo))
            },
            {
                "is_last_straight",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerLastStraight(buildInfo))
            },
            {
                "straight_front_type", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerStraightFrontType(buildInfo))
            },
            {
                "corner_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerCornerRandom(buildInfo))
            },
            {
                "phase_corner_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseCornerRandom(buildInfo))
            },
            {
                "phase_latter_half_corner_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseLatterHalfCornerRandom(buildInfo))
            },
            {
                "all_corner_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerAllCornerRandom(buildInfo))
            },
            {
                "is_finalcorner", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFinalCorner(buildInfo))
            },
            {
                "is_finalcorner_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFinalCornerRandom(buildInfo))
            },
            {
                "is_finalcorner_laterhalf", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFinalCornerLaterHalf(buildInfo))
            },
            {
                "is_finalcorner_random_middle",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFinalCornerRandomPhaseMiddle(buildInfo))
            },
            {
                "is_finalcorner_random_end",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerFinalCornerRandomPhaseEnd(buildInfo))
            },
            {
                SkillDefine.SKILL_TRIGGER_KEYWORD_RUNNING_STYLE, 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyle(buildInfo))
            },
            {
                "motivation",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerMotivation(buildInfo))
            },
            {
                "popularity", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPopularity(buildInfo))
            },
            {
                "popularity_rate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPopularityRate(buildInfo))
            },
            {
                "popularity_topicon_num", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPopularityTopIconNum(buildInfo))
            },
            {
                "popularity_icon_num", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPopularityIconNum(buildInfo))
            },
            {
                "post_number", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPostNumber(buildInfo))
            },
            {
                "post_number_rate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPostNumberRate(buildInfo))
            },
            {
                "phase", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhase(buildInfo))
            },
            {
                "phase_firsthalf",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseFirstHalf(buildInfo))
            },
            {
                "phase_firstquarter",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseFirstQuarter(buildInfo))
            },
            {
                "phase_laterhalf",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseLaterHalf(buildInfo))
            },
            {
                "phase_laterquarter",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseLaterQuarter(buildInfo))
            },
            {
                "phase_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseRandom(buildInfo))
            },
            {
                "phase_laterhalf_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseLaterHalfRandom(buildInfo))
            },
            {
                "phase_firsthalf_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseFirstHalfRandom(buildInfo))
            },
            {
                "phase_firstquarter_random", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerPhaseFirstQuarterRandom(buildInfo))
            },
            {
                "running_style_count_same", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCountSame(buildInfo))
            },
            {
                "running_style_count_same_rate", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleCountSameRate(buildInfo))
            },
            {
                "is_temptation", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerTemptation(buildInfo))
            },
            {
                "is_lastspurt", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerIsLastSpurt(buildInfo))
            },
            {
                "running_style_equal_popularity_one", 
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerRunningStyleEqualPopularityOne(buildInfo))
            },
            {
                "lastspurt",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerLastSpurt(buildInfo))
            },
            {
                "is_exist_skill_id",
                new Gallop.TriggerCreateFunc((buildInfo) => new StandaloneSimulator.SkillTriggerExistSkillId(buildInfo))
            },
            #endregion
        };
#endif
    }
}
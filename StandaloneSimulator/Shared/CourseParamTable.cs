#if GALLOP
using UnityEngine;
#endif
using System.Collections;
using System.Collections.Generic;

#if GALLOP
[System.Serializable]
public class CourseParamTable : MonoBehaviour
#else
    public class CourseParamTable : StandaloneSimulator.IYamlLoadable
#endif
{
    public CourseParam[] courseParams;

#if STANDALONE_SIMULATOR
        #region Dummy(YamlDotNetでasset/prefabをyamlとしてデシリアライズする時に要素が不足していると例外吐かれるので必要なもの)
        public int m_ObjectHideFlags;
        public Dictionary<string, string> m_CorrespondingSourceObject;
        public Dictionary<string, string> m_PrefabInstance;
        public Dictionary<string, string> m_PrefabAsset;
        public Dictionary<string, string> m_GameObject;
        public string m_Enabled;
        public string m_EditorHideFlags;
        public Dictionary<string, string> m_Script;
        public string m_Name;
        public string m_EditorClassIdentifier;
        #endregion

        public void LoadPostProcess()
        {
            foreach(var courseParam in courseParams)
            {
                courseParam.LoadPostProcess();
            }
        }
#endif
}

using System;
using StandaloneSimulator;
#if GALLOP
using UnityEngine;
#else
using System.Numerics;
using System.Collections.Generic;
#endif

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 競馬場レーンアニメーション管理。
    /// </summary>
    //-------------------------------------------------------------------
    [Serializable]
    [AddComponentMenu("")]
#if GALLOP
    public class CourseLaneAnim : ScriptableObject
#else
    public class CourseLaneAnim : StandaloneSimulator.IYamlLoadable
#endif
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        public const float LANE_FRAME_NUM = 1000.0f; // 走行ラインアニメーションのフレーム数(スタート~ゴールが1000フレームとして出力されている)

        //連続してアクセスされる事が多いのでメモリレイアウト的に分けておく
        [Serializable]
        public struct Key
        {
            public float[] valueX;
            public float[] valueY;
            public float[] valueZ;

        #if GALLOP
            public Quaternion[] rotation;

            public System.Numerics.Quaternion[] Rotation
            {
                get
                {
                    if (_rotationTmp == null)
                    {
                        _rotationTmp = new System.Numerics.Quaternion[rotation.Length];
                        for(int i = 0; i < rotation.Length; ++i)
                        {
                            _rotationTmp[i] = new System.Numerics.Quaternion(
                                rotation[i].x, 
                                rotation[i].y, 
                                rotation[i].z, 
                                rotation[i].w);
                        }
                    }

                    return _rotationTmp;
                }
            }

            /// <summary>
            /// アクセス回数削減用
            /// </summary>
            public int RotationLength => rotation.Length;

            /// <summary>
            /// アクセス回数削減用メソッド
            /// </summary>
            public System.Numerics.Quaternion GetRotation(int index)
            {
                if (index < 0 || index >= rotation.Length)
                {
                    Debug.LogError("不正なindex値です");
                    return new System.Numerics.Quaternion();
                }
                if (_rotationTmp == null)
                {
                    var rot = new System.Numerics.Quaternion(
                        rotation[index].x,
                        rotation[index].y,
                        rotation[index].z,
                        rotation[index].w);
                    return rot;
                }

                return _rotationTmp[index];
            }
            
            private System.Numerics.Quaternion[] _rotationTmp;
        #else
            public QuaternionDummy[] rotation; // yamlをデシリアライズするためのテンポラリ。
            public Quaternion[] Rotation;

            public int RotationLength => Rotation?.Length ?? rotation.Length;

            public Quaternion GetRotation(int index)
            {
                if(Rotation == null || index < 0 || index >= RotationLength)
                {
                    Debug.LogError("Rotation初期化前 or 不正なアクセスです");
                    return new Quaternion();
                }
                return Rotation[index];
            }

            public void LoadPostProcess()
            {
                Rotation = new Quaternion[rotation.Length];
                for(int i = 0; i < Rotation.Length; ++i)
                {
                    Rotation[i] = new Quaternion(rotation[i].x, rotation[i].y, rotation[i].z, rotation[i].w);
                }
                rotation = null;
            }
        #endif

        }

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR
        #region Dummy(YamlDotNetでasset/prefabをyamlとしてデシリアライズする時に要素が不足していると例外吐かれるので必要なもの)
        public int m_ObjectHideFlags;
        public Dictionary<string, string> m_CorrespondingSourceObject;
        public Dictionary<string, string> m_PrefabInstance;
        public Dictionary<string, string> m_PrefabAsset;
        public Dictionary<string, string> m_GameObject;
        public string m_Enabled;
        public string m_EditorHideFlags;
        public Dictionary<string, string> m_Script;
        public string m_Name;
        public string m_EditorClassIdentifier;
        #endregion
#endif
        
        public float length;
        public int Distance;
        public Key key;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
#if STANDALONE_SIMULATOR
        public CourseLaneAnim()
        {
        }
        public void LoadPostProcess()
        {
            key.LoadPostProcess();
        }
#endif
        
        private static System.Numerics.Quaternion GetRotation(Key key, float frame)
        {
            int frameN = (int)frame;
            frame = frame - frameN;
            if ((key.RotationLength - 1) <= frameN)
            {
                return key.GetRotation(key.RotationLength - 1);
            }
            
            return System.Numerics.Quaternion.Lerp(key.GetRotation(frameN), key.GetRotation(frameN + 1), frame);
        }

        private static System.Numerics.Vector3 GetPosition(Key key, float frame)
        {
            int frameN = (int)frame;
            frame = frame - frameN;

            // #20990のセーフティ用にエラーチェック。
            if (frameN < 0 ||
                frameN >= key.valueX.Length ||
                frameN >= key.valueZ.Length)
            {
                return RaceUtilMath.VECTOR3_ZERO;
            }
            if (key.valueY != null)
            {
                if (frameN >= key.valueY.Length)
                {
                    return RaceUtilMath.VECTOR3_ZERO;
                }
            }

            if ((key.valueX.Length - 1) <= frameN)
            {
                frameN = key.valueX.Length - 1;

                var x = key.valueX[frameN];
                var y = key.valueY != null ? key.valueY[frameN] : 0;
                var z = key.valueZ[frameN];
                return new System.Numerics.Vector3(x, y, z);
            }

            var pos1 = new System.Numerics.Vector3(
                key.valueX[frameN],
                key.valueY != null ? key.valueY[frameN] : 0,
                key.valueZ[frameN]);

            var pos2 = new System.Numerics.Vector3(
                key.valueX[frameN + 1],
                key.valueY != null ? key.valueY[frameN + 1] : 0,
                key.valueZ[frameN + 1]);

            return System.Numerics.Vector3.Lerp(pos1, pos2, frame);
        }

        /// <summary>
        /// 計算結果を受け取る
        /// </summary>
        /// <param name="position"></param>
        /// <param name="rotation"></param>
        /// <param name="time"></param>
        #if GALLOP
        public void Sample(out Vector3 position,out Quaternion rotation,float time)
        {
            var pos = GetPosition(this.key, time);
            var rot = GetRotation(this.key, time);
            position = new Vector3(pos.X, pos.Y, pos.Z);
            rotation = new Quaternion(rot.X, rot.Y, rot.Z, rot.W);
        }
        #endif
        public void Sample(out System.Numerics.Vector3 position, out System.Numerics.Quaternion rotation, float time)
        {
            position = GetPosition(this.key, time);
            rotation = GetRotation(this.key, time);
        }

        /// <summary>
        /// コース上の距離から、このコースパスのどのフレームを参照すべきかを計算。
        /// </summary>
        public float Distance2Time(float distance)
        {
            float time = LANE_FRAME_NUM * (distance / Distance);
            return time;
        }
    };
};

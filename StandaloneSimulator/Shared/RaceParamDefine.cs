using System.Collections;
using System.Collections.Generic;
using System;
#if GALLOP
using UnityEngine;
using UnityEngine.Serialization;
#endif

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース調整パラメータ。
    /// </summary>
    //-------------------------------------------------------------------
    [Serializable]
    [AddComponentMenu("")]
#if GALLOP
    public class RaceParamDefine : ScriptableObject
#else
    public class RaceParamDefine : StandaloneSimulator.IYamlLoadable
#endif
    {
#if STANDALONE_SIMULATOR
        [Serializable]
        public struct Vector3
        {
            public float x;
            public float y;
            public float z;
        }

        public void LoadPostProcess()
        {
            PositionKeep.PositionKeepContinueSectionExIntArray = StandaloneSimulator.YamlLoadHelper.Hex2Int(PositionKeep.PositionKeepContinueSectionExArray);
            PositionKeep.PositionKeepContinueSectionExArray = null; // 間違ってアクセスしたらすぐ分かるようにnull入れる。

            foreach(var group in Skill.SkillActivateTagGroupArray)
            {
                group.TagIntArray = StandaloneSimulator.YamlLoadHelper.Hex2Int(group.TagArray);
                group.TagArray = null; // 間違ってアクセスしたらすぐ分かるようにnull入れる。
            }

            Skill.AbilityValueUsageTagGroup1IntArray = StandaloneSimulator.YamlLoadHelper.Hex2Int(Skill.AbilityValueUsageTagGroup1Array);
            Skill.AbilityValueUsageTagGroup1Array = null; // 間違ってアクセスしたらすぐ分かるようにnull入れる。

            if (Skill.ActivateSpecificSkillArray != null)
            {
                foreach (var activateSpecificSkill in Skill.ActivateSpecificSkillArray)
                {
                    activateSpecificSkill.ActivateSkillIdIntArray = StandaloneSimulator.YamlLoadHelper.Hex2Int(activateSpecificSkill.ActivateSkillIdArray);
                    activateSpecificSkill.ActivateSkillIdArray = null; // 間違ってアクセスしたらすぐ分かるようにnull入れる。
                }
            }
        }

        #region Dummy(YamlDotNetでasset/prefabをyamlとしてデシリアライズする時に要素が不足していると例外吐かれるので必要なもの)
        public int m_ObjectHideFlags;
        public Dictionary<string, string> m_CorrespondingSourceObject;
        public Dictionary<string, string> m_PrefabInstance;
        public Dictionary<string, string> m_PrefabAsset;
        public Dictionary<string, string> m_GameObject;
        public string m_Enabled;
        public string m_EditorHideFlags;
        public Dictionary<string, string> m_Script;
        public string m_Name;
        public string m_EditorClassIdentifier;
        #endregion
#endif

        private const string HEADER_SEPARATOR = " ======================================================";

        [Tooltip("RaceParamDefineのデータバージョン　※デバッグ用途")]
        public string paramDefineVersion;

        //---------------------------------------------------------------
        [Serializable]
        public class RaceGlobalParam
        {
            [Tooltip("着タイムに乗算する係数")]
            public float FinishTimeCoef;

            [Tooltip("育成レースで基礎ステータスに加算される値")]
            public float SingleModeAddSpeed;
            public float SingleModeAddStamina;
            public float SingleModeAddPow;
            public float SingleModeAddGuts;
            public float SingleModeAddWiz;
        };
        [Space(20), Header("全体設定" + HEADER_SEPARATOR)]
        public RaceGlobalParam Global;
        
        [Tooltip("レース基礎速度の基準になる速度 ※RaceBaseSpeed=raceBaseSpeedBaseSpeed - ( ( コース距離-raceBaseSpeedBaseDistance ) / raceBaseSpeedDistanceRate )")]
        public float raceBaseSpeedBaseSpeed;
        [Tooltip("レース基礎速度の基準になる距離  ※RaceBaseSpeed=raceBaseSpeedBaseSpeed - ( ( コース距離-raceBaseSpeedBaseDistance ) / raceBaseSpeedDistanceRate )")]
        public float raceBaseSpeedBaseDistance;
        [Tooltip("レース基礎速度の(コース距離-基準になる距離)が速度に影響する度合 ※RaceBaseSpeed=raceBaseSpeedBaseSpeed - ( ( コース距離-raceBaseSpeedBaseDistance ) / raceBaseSpeedDistanceRate )")]
        public float raceBaseSpeedDistanceRate;

        //---------------------------------------------------------------
        [Space(20), Header("速度" + HEADER_SEPARATOR)]
        [Tooltip("加速度計算時のPowに乗算する係数")]
        public float accelPowCoef;
        [Tooltip("加速度計算時のPowに乗算する係数(上り坂)")]
        public float accelPowCoefUpSlope;
        [Tooltip("加速度計算時、Powに乗算する係数")]
        public float AccelPowCoefSqrt;

        [Tooltip("減速度基準値")]
        public float declBase;
        [Tooltip("減速度レート：序盤")]
        public float declRateStart;
        [Tooltip("減速度レート：中盤")]
        public float declRateMiddle;
        [Tooltip("減速度レート：終盤以降")]
        public float declRateEnd;
        [Tooltip("減速度レート：HP枯渇")]
        public float declRateHpZero;
        [Tooltip("減速度レート：ポジション維持による減速")]
        public float DeclRatePositionKeepPaseDown;

        [Tooltip("序盤、外側レーンのキャラが狙いたい速度を加減速するレーン閾値")]
        public float forceInMoveLaneThreshold;
        [Tooltip("序盤、外側レーンのキャラが狙いたい速度に加減速する固定値：逃げ")]
        public float forceInMoveTargetSpeedAddRunningOut;
        [Tooltip("序盤、外側レーンのキャラが狙いたい速度に加減速する固定値：先行")]
        public float forceInMoveTargetSpeedAddProceding;
        [Tooltip("序盤、外側レーンのキャラが狙いたい速度に加減速する固定値：差し")]
        public float forceInMoveTargetSpeedAddInsertion;
        [Tooltip("序盤、外側レーンのキャラが狙いたい速度に加減速する固定値：追い")]
        public float forceInMoveTargetSpeedAddChasing;
        [Tooltip("序盤、外側レーンのキャラが狙いたい速度に加減速するランダム値")]
        public float forceInMoveTargetSpeedAddRandomRange;

        [Tooltip("狙いたい速度計算時、Speedに乗算する係数")]
        public float addSpeedParamCoef;
        [Tooltip("狙いたい速度計算時、BaseTargetSpeedに乗算する係数")]
        public float baseTargetSpeedCoef;
        [Tooltip("ラストスパート速度計算時、Speedに乗算する係数")]
        public float LastSpurtTargetSpeedCoefSqrt;


        [Tooltip("ゴールからこの距離を引いた地点でHpが0になるようにスパート速度・開始距離計算を行う")]
        public int LastSpurtDistanceGoalBuffer;
        
        [Tooltip("ラストスパート計算時の速度減少値")]
        public float lastSpurtSpeedDelta;
        [Tooltip("ラストスパート開始距離の上限（ゴール距離からの減算値）")]
        public float lastSpurtDistanceMaxFromGoal;

        [Tooltip("ラストスパート計算Step2の速度減少値")]
        public float lastSpurtStep2SpeedDelta;
        [Tooltip("ラストスパート計算Step2の候補抽選確率変数１ ※確率 = lastSpurtStep2PerVal1 + lastSpurtStep2PerVal2 * wiz")]
        public float lastSpurtStep2PerVal1;
        [Tooltip("ラストスパート計算Step2の候補抽選確率変数２ ※確率 = lastSpurtStep2PerVal1 + lastSpurtStep2PerVal2 * wiz")]
        public float lastSpurtStep2PerVal2;

        [Tooltip("ラストスパート速度計算時のgutsに乗算する係数 ※速度に (guts * LastSpurtTargetSpeedGutsCoef1)のLastSpurtTargetSpeedGutsPow乗 * LastSpurtTargetSpeedGutsCoef2 を加算する")]
        public float LastSpurtTargetSpeedGutsCoef1;
        [Tooltip("ラストスパート速度計算時にgutsをこの値乗する ※速度に (guts * LastSpurtTargetSpeedGutsCoef1)のLastSpurtTargetSpeedGutsPow乗 * LastSpurtTargetSpeedGutsCoef2 を加算する")]
        public float LastSpurtTargetSpeedGutsPow;
        [Tooltip("ラストスパート速度計算時にguts計算結果にこの値を乗算する ※速度に (guts * LastSpurtTargetSpeedGutsCoef1)のLastSpurtTargetSpeedGutsPow乗 * LastSpurtTargetSpeedGutsCoef2 を加算する")]
        public float LastSpurtTargetSpeedGutsCoef2;

        [Tooltip("出走時の初速")]
        public float StartSpeed;
        [Tooltip("出走時の各キャラの加速度に固定で加算する値")]
        public float StartAccelAdd;

#region <スピード>
        [Serializable]
        public class PhaseAccelCoef
        {
            public float Start = 1;
            public float Middle = 1;
            public float End = 1;
            public float Last = 1;
        };
            
        [Serializable]
        public class SpeedParam
        {
            [Tooltip("最低速度のレース基礎速度に対する比率 ※最低速度 = RaceBaseSpeed * MinSpeedRate + Sqrt(Guts * MinSpeedGutsCoefSqrt) * MinSpeedGutsCoef")]
            public float MinSpeedRate;
            public float MinSpeedGutsCoefSqrt;
            public float MinSpeedGutsCoef;
            
            public float TargetSpeedMin;

            public float ExpectLastSpurtSpeed;
            
            [Tooltip("各フェイズの加速度の係数(逃げ/先行/差し/追い)")]
            public PhaseAccelCoef[] PhaseAccelCoefArray;
            [Tooltip("各フェイズの加速度の係数(大逃げ)")]
            public PhaseAccelCoef[] PhaseAccelCoefExArray;
        };
        [Space(20), Header("速度関連" + HEADER_SEPARATOR)]
        public SpeedParam Speed;
#endregion
        
        //---------------------------------------------------------------
#region <コースセットによるステータス補正>
        [Serializable]
        public class CourseSetAdjustParam
        {
            [Serializable]
            public class CoefAndThreshold
            {
                public float Coef;
                public int StatusThreshold;
            }

            public CoefAndThreshold[] CoefAndThresholdArray;
        };
        [Space(20), Header("コースセットによるステータス補正" + HEADER_SEPARATOR)]
        public CourseSetAdjustParam CourseSetAdjust;
#endregion

        //---------------------------------------------------------------
#region <坂>
        [Serializable]
        public class SlopeParam
        {
#if true // 1.15.0以降は不要だが、1.15の最低保証バージョンのリソース10004800がrelease/11500でビルドされるため、定義を残しておく。→https://xxxxxxxxxx/archives/C02S0AVN71Q/p1643021886252000?thread_ts=1642742988.170900&cid=C02S0AVN71Q
            [Tooltip("坂と見なす勾配%。勾配%が±この値以上の時に坂と見なす。")]
            public float SlopePerThreshold;
#endif

            [Tooltip("上り坂狙いたい速度加算値計算の変数1 ※加算値 = -(UpSlopeAddSpeedVal1 / pow * 勾配%")]
            public float UpSlopeAddSpeedVal1;

            [Tooltip("下り坂狙いたい速度加算値計算の変数1 ※加算値 = DownSlopeAddSpeedVal1 + 勾配% / DownSlopeAddSpeedVal2")]
            public float DownSlopeAddSpeedVal1;
            [Tooltip("下り坂狙いたい速度加算値計算の変数2 ※加算値 = DownSlopeAddSpeedVal1 + 勾配% / DownSlopeAddSpeedVal2")]
            public float DownSlopeAddSpeedVal2;

            [Tooltip("下り坂狙いたい速度加算モード開始確率の賢さに掛ける比率")]
            public float DownSlopeAddSpeedStartWizRate;
            [Tooltip("下り坂狙いたい速度加算モード終了確率")]
            public float DownSlopeAddSpeedEndPer;
        };
        [Space(20), Header("坂" + HEADER_SEPARATOR)]
        public SlopeParam Slope;
#endregion
        
        //---------------------------------------------------------------
        [Space(20), Header("仕掛け" + HEADER_SEPARATOR), Tooltip("仕掛け中のBaseTargetSpeed加算値（RaceBaseSpeedに乗算する係数）")]
        public float lastSpurtBaseTargetSpeedAddCoef;

        //---------------------------------------------------------------
        [Space(20), Header("馬場状態補正" + HEADER_SEPARATOR)]
        [Tooltip("馬場状態によるパラメータ補正：芝")]
        public RaceDefine.GroundModifierParam[] _groundModifierParamTurf;
        [Tooltip("馬場状態によるパラメータ補正：ダート")]
        public RaceDefine.GroundModifierParam[] _groundModifierParamDirt;

        //---------------------------------------------------------------
        #region <目指す速度>
        [Serializable]
        public class PhaseBaseTargetSpeedPer
        {
            public float Start;
            public float Middle;
            public float End;
            public float Last;
        };
        [Serializable]
        public class BaseTargetSpeedParam
        {
            [Tooltip("各フェイズの目指す速度基礎値計算用の比率(逃げ/先行/差し/追い)")]
            public PhaseBaseTargetSpeedPer[] PhaseBaseTargetSpeedPerArray;
            [Tooltip("各フェイズの目指す速度基礎値計算用の比率(大逃げ)")]
            public PhaseBaseTargetSpeedPer[] PhaseBaseTargetSpeedPerExArray;

            [Tooltip("マイナスランダム幅の計算変数1 ※マイナスランダム幅 = BaseTargetSpeedRandomMinusVal1 + (wiz / BaseTargetSpeedRandomMinusVal2) * Log10(wiz)")]
            public float BaseTargetSpeedRandomMinusVal1;
            [Tooltip("マイナスランダム幅の計算変数2 ※マイナスランダム幅 = BaseTargetSpeedRandomMinusVal1 + (wiz / BaseTargetSpeedRandomMinusVal2) * Log10(wiz)")]
            public float BaseTargetSpeedRandomMinusVal2;

            [Tooltip("プラスランダム幅の計算変数1 ※プラスランダム幅 = (wiz / BaseTargetSpeedRandomPlusVal1) * Log10(wiz)")]
            public float BaseTargetSpeedRandomPlusVal1;

            [Tooltip("終盤以降の目指す速度でSpeedに乗算してSqrtする値")]
            public float PhaseEndBaseTargetSpeedCoef;
        };
        [Space(20), Header("目指す速度基礎値" + HEADER_SEPARATOR)]
        public BaseTargetSpeedParam BasetTargetSpeed;
#endregion
        
        //---------------------------------------------------------------
        [Space(20), Header("レーン移動" + HEADER_SEPARATOR)]
        [Tooltip("レーン移動速度の基準値")]
        public float laneMoveSpeedBase;
        [Tooltip("基本レーン移動速度計算のための加算値")]
        public float laneMoveSpeedAdd;
        [Tooltip("基本レーン移動速度計算のためのPowに乗算する係数")]
        public float laneMoveSpeedPowCoef;

        [Tooltip("序盤、アウトにいるほどレーン移動速度に補正をかける係数")]
        public float laneMoveSpeedOutCoef;

        [Tooltip("自分より前方かつこのLaneGapAbs以内にいるキャラは、進路上のキャラと見なす")]
        public float CongestionLaneGapAbs;
        [Tooltip("進路上にキャラがこの数以上いたら混雑と見なす")]
        public int CongestionHorseCntThreshold;

        [Tooltip("レーン移動速度の加速度計算時にlaneMoveSpeedBaseに乗算する係数")]
        public float laneMoveAccelMoveSpeedBaseCoef;

        [Tooltip("終盤、外側にレーン移動する時の、レーン位置正規化係数。各キャラレーン位置を[0~この値]に正規化し、移動先レーンの算出に使用する　※移動先=(レーン位置/lastMoveOutLaneNormalizeVal) * lastMoveOutLaneCoef + Random( lastMoveOutLaneAddRandom )")]
        public float lastMoveOutLaneNormalizeVal;
        [Tooltip("終盤、外側にレーン移動する時の、正規化レーン位置に乗算する値。正規化レーン位置に応じて最大この値まで外に移動する ※移動先=(レーン位置/lastMoveOutLaneNormalizeVal) * lastMoveOutLaneCoef + Random( lastMoveOutLaneAddRandom )")]
        public float lastMoveOutLaneCoef;
        [Tooltip("終盤、外側にレーン移動する時の、移動先レーン位置に加算するランダム幅 ※移動先=(レーン位置/lastMoveOutLaneNormalizeVal) * lastMoveOutLaneCoef + Random( lastMoveOutLaneAddRandom )")]
        public float lastMoveOutLaneAddRandom;
        [Tooltip("終盤、外側にレーン移動する時の開始位置。最終コーナーのこの割合に達したら始める")]
        public float LastMoveOutStartFinalCornerRate;
        
        [Serializable]
        public class BlockParam
        {
            [Tooltip("この距離以内に近づいたら前方ブロックと見なす")]
            public float FrontBlockDistanceGap;
            [Tooltip("このレーン距離以内に近づいたら前方ブロックと見なす　※distanceへの換算はx18です。")]
            public float FrontBlockLaneGap;
            [Tooltip("この距離以内に近づいたらサイドブロックと見なす")]
            public float SideBlockDistanceGap;
            [Tooltip("このレーン距離以内に近づいたらサイドブロックと見なす　※distanceへの換算はx18です。")]
            public float SideBlockLaneGap;
            
            [Tooltip("前方ブロックされている場合、相手との距離が最も詰まっているときは相手の現在速度xFrontBlockedSpeedRateMinが自分の最大速度")]
            public float FrontBlockMaxSpeedRateMin;
            [Tooltip("前方ブロックされている場合、相手との距離が最も詰まっていないときは相手の現在速度xFrontBlockedSpeedRateMaxが自分の最大速度")]
            public float FrontBlockMaxSpeedRateMax;
        };
        [Space(20), Header("ブロック" + HEADER_SEPARATOR)]
        public BlockParam Block;
        
        //---------------------------------------------------------------
#region <近くのキャラ>
        [Serializable]
        public class NearParam
        {
            [Tooltip("近くにいると見なす距離差分")]
            public float DistanceThreshold;
            [Tooltip("近くにいると見なすレーン距離差分")]
            public float LaneDistanceThreshold;
        }
        [Space(20), Header("近くのキャラ" + HEADER_SEPARATOR)]
        public NearParam Near;
#endregion

        //---------------------------------------------------------------
#region <Hp>
        [Serializable]
        public class HpParam
        {
            [Tooltip("スタミナからHPを計算するときの変数1 ※HP=コース距離 + (スタミナ * hpInitialVal1 * 走法補正)")]
            public float HpInitialVal1;
            [Tooltip("Hp基礎減少値")]
            public float HpDecBase;

            [Tooltip("Hp初期値への係数：大逃げ")]
            public float HpMaxCoefOoNige;
            [Tooltip("Hp初期値への係数：逃げ")]
            public float HpMaxCoefNige;
            [Tooltip("Hp初期値への係数：先行")]
            public float HpMaxCoefSenko;
            [Tooltip("Hp初期値への係数：差し")]
            public float HpMaxCoefSashi;
            [Tooltip("Hp初期値への係数：追い")]
            public float HpMaxCoefOikomi;

#region <消費>
            [Tooltip("Hp消費計算過程のスピードギャップ計算変数")]
            public float SpeedGapParam1;
            [Tooltip("Hp消費計算過程のスピードギャップ計算変数の２乗値")]
            public float SpeedGapParam1Pow;


            [Tooltip("HP減少レート：通常時")]
            public float HpDecRateBaseNormal;
            [Tooltip("HP減少レート：掛かり中")]
            public float HpDecRateBaseTemptation;
            [Tooltip("HP減少レート：ポジション維持のペースダウン中")]
            public float HpDecRateBasePositionKeepPaseDown;
            [Tooltip("HP減少レート：逃げの競り合い（ハナ奪い合い）中")]
            public float HpDecRateBaseCompeteTopNige;
            [Tooltip("HP減少レート：逃げの掛かり＆競り合い（ハナ奪い合い）中")]
            public float HpDecRateBaseTemptationAndCompeteTopNige;
            [Tooltip("HP減少レート：大逃げの競り合い（ハナ奪い合い）中")]
            public float HpDecRateBaseCompeteTopOonige;
            [Tooltip("HP減少レート：大逃げの掛かり＆競り合い（ハナ奪い合い）中")]
            public float HpDecRateBaseTemptationAndCompeteTopOonige;
            
            [Tooltip("HP減少レート（にさらに乗算する値）：下り坂加速モード")]
            public float HpDecRateMultiplyDownSlopeAccelMode;

            [Tooltip("終盤以降、根性によってHp減少量に補正を掛けるための、根性で割る値 ※Hp消費量 = Hp消費量 * (1 + HpGutsCoef / Sqrt(Guts * HpGutsCoefSqrt)")]
            public float HpGutsCoef;
            [Tooltip("終盤以降、根性によってHp減少量に補正を掛けるための、根性に乗算して平方根求める値 ※Hp消費量 = Hp消費量 * (1 + HpGutsCoef / Sqrt(Guts * HpGutsCoefSqrt))")]
            public float HpGutsCoefSqrt;
#endregion
        }
        [Space(20), Header("Hp" + HEADER_SEPARATOR)]
        public HpParam Hp;
#endregion

        //---------------------------------------------------------------
#region <ポジション維持>
        [Serializable]
        public class PositionKeepParam
        {
            [Tooltip("ポジ維持：発動チェックする間隔（秒）")]
            public int PositionKeepCheckIntervalSec;
            [Tooltip("ポジ維持：発動後のクールダウン時間（秒）")]
            public int PositionKeepCoolDownSec;

            [Tooltip("ポジ維持：発動チェック開始区間")]
            public int PositionKeepStartSection;
            [Tooltip("ポジ維持：発動チェック終了区間(1区間100m、StartSectionが4、EndSectionが10の場合、300m以上1000m未満の間、発動チェックが行われる)")]
            public int PositionKeepEndSection;

            [Tooltip("ポジ維持：距離で終了する機能の場合、この区間進んだら終了")]
            public int PositionKeepContinueSection;
            [Tooltip("ポジ維持：距離で終了する機能の場合、この区間進んだら終了(特殊走法)")]
#if GALLOP
            public int[] PositionKeepContinueSectionExArray;
            public int[] PositionKeepContinueSectionExIntArray => PositionKeepContinueSectionExArray;
#else
            public string PositionKeepContinueSectionExArray;
            public int[] PositionKeepContinueSectionExIntArray;
#endif

            [Tooltip("ポジ維持：逃げ１位の発生確率 = PositionKeepSpeedUpStartPerVal1 * Log10(賢さ)")]
            public float PositionKeepSpeedUpStartPerVal1;
            [Tooltip("ポジ維持：逃げ１位の目指す速度基礎値補正係数")]
            public float PositionKeepSpeedUpBaseTargetSpeedMultiply;
            [Tooltip("ポジ維持：逃げ１位の加速は、２位との距離差がこの値以下の時にみ行う（逃げ複数人）")]
            public float PositionKeepSpeedUpEndDistanceDiff;
            [Tooltip("ポジ維持：逃げ１位の加速は、２位との距離差がこの値以下の時にみ行う（逃げ１人）")]
            public float PositionKeepSpeedUpEndDistanceDiffOne;
            [Tooltip("ポジ維持：逃げの追い抜き発生確率 = PositionKeepOvertakeStartPerVal1 * Log10(賢さ)")]
            public float PositionKeepOvertakeStartPerVal1;
            [Tooltip("ポジ維持：逃げの追い抜きで、１位になった後、２位のキャラとこの距離離れたら終了する")]
            public float PositionKeepOvertakeEndDistanceDiff;
            [Tooltip("ポジ維持：逃げの追い抜きの目指す速度基礎値補正係数")]
            public float PositionKeepOvertakeBaseTargetSpeedMultiply;

            [Tooltip("ポジ維持：大逃げ１位の発生確率 = PositionKeepSpeedUpStartPerVal1 * Log10(賢さ)")]
            public float PositionKeepSpeedUpStartPerVal1Oonige;
            [Tooltip("ポジ維持：大逃げ１位の目指す速度基礎値補正係数")]
            public float PositionKeepSpeedUpBaseTargetSpeedMultiplyOonige;
            [Tooltip("ポジ維持：大逃げ１位の加速は、２位との距離差がこの値以下の時にみ行う")]
            public float PositionKeepSpeedUpEndDistanceDiffOonige;
            [Tooltip("ポジ維持：大逃げの追い抜き発生確率 = PositionKeepOvertakeStartPerVal1 * Log10(賢さ)")]
            public float PositionKeepOvertakeStartPerVal1Oonige;
            [Tooltip("ポジ維持：大逃げの追い抜きで、１位になった後、２位のキャラとこの距離離れたら終了する")]
            public float PositionKeepOvertakeEndDistanceDiffOonige;
            [Tooltip("ポジ維持：大逃げの追い抜きの目指す速度基礎値補正係数")]
            public float PositionKeepOvertakeBaseTargetSpeedMultiplyOonige;

            [Tooltip("ポジ維持：ペース調整：先行・差し・追い共通：ペースダウン時の適正距離最大値に乗算する比率。0.0 ~ 1.0")]
            public float PositionKeepPaseUpDownDistanceDiffMaxCoef;
            
            [Tooltip("ポジ維持：ペース調整：先行：１位から最低この距離離れる")]
            public float PositionKeepPaseUpDownDistanceDiffMinSenko;
            [Tooltip("ポジ維持：ペース調整：先行：１位から最大この距離離れる")]
            public float PositionKeepPaseUpDownDistanceDiffMaxSenko;
            [Tooltip("ポジ維持：ペース調整：差し：１位から最低この距離離れる")]
            public float PositionKeepPaseUpDownDistanceDiffMinSashi;
            [Tooltip("ポジ維持：ペース調整：差し：１位から最大この距離離れる")]
            public float PositionKeepPaseUpDownDistanceDiffMaxSashi;
            [Tooltip("ポジ維持：ペース調整：追い：１位から最低この距離離れる")]
            public float PositionKeepPaseUpDownDistanceDiffMinOikomi;
            [Tooltip("ポジ維持：ペース調整：追い：１位から最大この距離離れる")]
            public float PositionKeepPaseUpDownDistanceDiffMaxOikomi;

            [Tooltip("ポジ維持：ペース調整の距離補正の基準距離")]
            public int PositionKeepPaseBaseDistance;
            [Tooltip("ポジ維持：ペース調整の距離補正の係数")]
            public float PositionKeepPaseBaseDistanceCoef;

            [Tooltip("ポジ維持：ペースダウンの目指す速度基礎値補正係数：中盤以外")]
            public float PositionKeepPaseDownBaseTargetSpeedMultiply;
            [Tooltip("ポジ維持：ペースダウンの目指す速度基礎値補正係数：中盤")]
            public float PositionKeepPaseDownBaseTargetSpeedMultiplyPhaseMiddle;
            [Tooltip("ポジ維持：ペースダウン中、目指すレーン")]
            public float PositionKeepPaseDownTargetLane;

            [Tooltip("ポジ維持：ペースアップ発生確率 = PositionKeepPaseUpStartPerVal1 * Log10(賢さ)")]
            public float PositionKeepPaseUpStartPerVal1;
            [Tooltip("ポジ維持：ペースアップの目指す速度基礎値補正係数")]
            public float PositionKeepPaseUpBaseTargetSpeedMultiply;

            [Tooltip("ポジ維持：ペースアップExの目指す速度基礎値補正係数")]
            public float PositionKeepPaseUpExBaseTargetSpeedMultiply;

            [Tooltip("差し・追いが先頭で、この距離(m)内に自分より前の走法のキャラがいる場合、それをペースメーカーにする")]
            public float PaseMakerChangeForwardRunningStyleRange;
            [Tooltip("先頭のキャラと最も前の走法の１位のキャラがこの距離以上離れていたらペースメーカー再計算する")]
            public float PaseMakerChangeMostForwardStyleRange;
            [Tooltip("先頭のキャラの走法が、この回数最も前の走法ではなかったらペースメーカー再計算する")]
            public float PaseMakerChangeTopHorseNotMostForwardStyleCount;
        }
        [Space(20), Header("ポジション維持" + HEADER_SEPARATOR)]
        public PositionKeepParam PositionKeep;
#endregion

        //---------------------------------------------------------------
        [Space(20), Header("スタート" + HEADER_SEPARATOR), Tooltip("スタート遅延時間秒最大値")]
        public float startDelayMax;

        //---------------------------------------------------------------
        #region 上限突破パラメータパワー(足溜め)

        /// <summary> 脚質別係数 </summary>
        [Serializable]
        public class ConservePowerRunningStyleCoef
        {
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("係数")]
            public float Coef;
        }

        /// <summary> 溜めポイント増加モードごとの係数 </summary>
        [Serializable]
        public class ConservePowerIncreaseCoef
        {
            [Tooltip("増加モード")]
            public RaceDefine.IncreaseConservePowerMode Mode;
            [Tooltip("係数")]
            public float Coef;
        }

        /// <summary> 足溜めによる減算モードごとの減衰係数 </summary>
        [Serializable]
        public class ConservePowerDecreaseCoef
        {
            [Tooltip("減算モード")]
            public RaceDefine.DecreaseConservePowerMode Mode;
            [Tooltip("係数")]
            public float Coef;
        }

        /// <summary> 足溜め解放時の効果時間計算距離帯別型数 </summary>
        [Serializable]
        public class ReleaseConservePowerActivityTimeDistanceTypeCoef
        {
            [Tooltip("距離帯")]
            public RaceDefine.CourseDistanceType DistanceType;
            [Tooltip("係数")]
            public float Coef;
        }

        /// <summary> 足溜め解放時の効果時間計算距離帯別型数 </summary>
        [Serializable]
        public class ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoef
        {
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("距離帯")]
            public RaceDefine.CourseDistanceType DistanceType;
            [Tooltip("係数")]
            public float Coef;
        }

        [Serializable]
        public class ConservePowerParam
        {
            [Space(10), Header("<足溜め解放加速度計算用>")]
            [Tooltip("足溜め : 解放時の加速度計算用加速係数")]
            public float ReleaseConservePowerAccelCoef;

            [Tooltip("足溜め : 解放時の加速度計算用減衰係数")]
            public float ReleaseConservePowerDeclCoef;

            [Tooltip("足溜め : 解放時の加速度計算用脚質＆距離帯別係数")]
            public ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoef[] ReleaseConservePowerAccelRunningStyleAndDistanceTypeCoefArray;

            [Tooltip("足溜め : 減少モードごとの加速度減衰係数")]
            public ConservePowerDecreaseCoef[] ConservePowerDecreaseAccelCoefArray;


            [Space(10), Header("<足溜め効果時間計算用>")]
            [Tooltip("足溜め : 解放時の効果時間計算係数")]
            public float ReleaseConservePowerActivityTimeCoef;

            [Tooltip("足溜め : 解放時の効果時間計算距離帯型数")]
            public ReleaseConservePowerActivityTimeDistanceTypeCoef[] ReleaseConservePowerActivityTimeDistanceTypeCoefArray;


            [Space(10), Header("<足溜めポイント増加計算用>")]
            [Tooltip("足溜め : 溜めで使用するクールダウン時間の初期値")]
            public float ConservePowerDefaultCoolDownTime;

            [Tooltip("足溜め : 増加モードごとの係数")]
            public ConservePowerIncreaseCoef[] ConservePowerIncreaseCoefArray;

            [Tooltip("足溜め : 脚質別係数")]
            public ConservePowerRunningStyleCoef[] ConservePowerRunningStyleCoefArray;


            [Space(10), Header("<足溜めポイント減少計算用>")]
            [Tooltip("足溜め : 減少モードごとの係数")]
            public ConservePowerDecreaseCoef[] ConservePowerDecreaseCoefArray;


            [Space(10), Header("<足溜めその他>")]
            [Tooltip("足溜め : 効果発動可能な秒数(この秒数より多かったら足溜めがラストスパートで解放される)")]
            public float ConservePowerReleasableActivityTimeThreshold;
        }

        [Space(20), Header("上限突破パラメータパワー(足溜め)" + HEADER_SEPARATOR)]
        public ConservePowerParam ConservePower;
        #endregion // 上限突破パラメータパワー(足溜め)
        
        //---------------------------------------------------------------
        #region スパート前位置取り勝負

        /// <summary> スパート前位置取り勝負：条件A「先頭が遠い」の作戦別閾値 </summary>
        [Serializable]
        public class CompeteBeforeSpurtRunningStyleFarFromTopThreshold
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("閾値")]
            public float Threshold;
        }
        
        /// <summary> スパート前位置取り勝負：スピード上昇：作戦別係数 </summary>
        [Serializable]
        public class CompeteBeforeSpurtSpeedUpRunningStyleCoef
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("係数")]
            public float Coef;
        }
        
        /// <summary> スパート前位置取り勝負：少数派ボーナス：作戦別係数 </summary>
        [Serializable]
        public class CompeteBeforeSpurtMinorBonusRunningStyleCoef
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("係数")]
            public float Coef;
        }
        
        /// <summary> スパート前位置取り勝負：持久力消費：作戦別係数 </summary>
        [Serializable]
        public class CompeteBeforeSpurtConsumeStaminaRunningStyleCoef
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("係数")]
            public float Coef;
        }
        
        /// <summary> スパート前位置取り勝負：持久力消費：距離ごとの係数 </summary>
        [Serializable]
        public class CompeteBeforeSpurtConsumeStaminaCourseDistanceCoef
        {
            [Tooltip("閾値")]
            public float Threshold;
            [Tooltip("係数")]
            public float Coef;
        }

        /// <summary> スパート前位置取り勝負 </summary>
        [Serializable]
        public class CompeteBeforeSpurtParam
        {
            [Tooltip("スパート前位置取り勝負：発動区間の開始位置")]
            public int CompeteBeforeSpurtStartSection;
            [Tooltip("スパート前位置取り勝負：発動区間の終了位置")]
            public int CompeteBeforeSpurtEndSection;
            
            [Tooltip("スパート前位置取り勝負：発動チェックする間隔（秒）")]
            public int CompeteBeforeSpurtCheckIntervalSec;
            [Tooltip("スパート前位置取り勝負：発動後のクールダウン時間（秒）")]
            public int CompeteBeforeSpurtCoolDownSec;
            
            [Tooltip("スパート前位置取り勝負：条件A「先頭が遠い」の閾値")]
            public CompeteBeforeSpurtRunningStyleFarFromTopThreshold[] CompeteBeforeSpurtDistanceFarFromTopThresholdArray;
            [Tooltip("スパート前位置取り勝負：条件A「先頭が遠い」の発動率係数")]
            public float CompeteBeforeSpurtDistanceFromTopPerCoef;
            [Tooltip("スパート前位置取り勝負：条件A「先頭が遠い」の発動率べき指数")]
            public float CompeteBeforeSpurtDistanceFromTopPerExponent;
            
            [Tooltip("スパート前位置取り勝負：条件B「近くにウマ娘が多い」の近くの定義(前方)")]
            public int CompeteBeforeSpurtNearDistanceForward;
            [Tooltip("スパート前位置取り勝負：条件B「近くにウマ娘が多い」の近くの定義(後方)")]
            public int CompeteBeforeSpurtNearDistanceBackward;
            [Tooltip("スパート前位置取り勝負：条件B「近くにウマ娘が多い」の発動率べき指数")]
            public float CompeteBeforeSpurtNearDistancePerExponent;
            
            // 「近くにいるウマ娘数による係数」 = (近くにいるウマ娘数+近くにいる同作戦のウマ娘数*2)/(出走人数)*定数
            [Tooltip("スパート前位置取り勝負：「近くにいるウマ娘数による係数」の同作戦ボーナス係数")]
            public int CompeteBeforeSpurtSameRunnnigStyleCoefOfNearCharaNumCoef;
            [Tooltip("スパート前位置取り勝負：「近くにいるウマ娘数による係数」の定数")]
            public float CompeteBeforeSpurtCoefOfNearCharaNumCoef;
            
            [Tooltip("スパート前位置取り勝負：終了条件の時間（秒）")]
            public int CompeteBeforeSpurtActiveSec;
            
            [Tooltip("スパート前位置取り勝負：持久力温存状態のチェック間隔（秒）")]
            public int CompeteBeforeSpurtStaminaKeepCheckIntervalSec;
            [Tooltip("スパート前位置取り勝負：持久力温存状態のチェックの気づく率の係数")]
            public float CompeteBeforeSpurtStaminaKeepCheckNoticePerCoef;
            [Tooltip("スパート前位置取り勝負：持久力温存状態：賢さの除数")]
            public float CompeteBeforeSpurtStaminaKeepWizDivisor;
            [Tooltip("スパート前位置取り勝負：持久力温存状態：賢さのべき指数")]
            public float CompeteBeforeSpurtStaminaKeepWizExponent;
            [Tooltip("スパート前位置取り勝負：持久力温存状態：乱数の最大値")]
            public float CompeteBeforeSpurtStaminaKeepRandomRangeMax;
            [Tooltip("スパート前位置取り勝負：持久力温存状態：乱数の最小値")]
            public float CompeteBeforeSpurtStaminaKeepRandomRangeMin;
            
            [Tooltip("スパート前位置取り勝負：速度上昇量：パワーの除数")]
            public float CompeteBeforeSpurtSpeedUpPowDivisor;
            [Tooltip("スパート前位置取り勝負：速度上昇量：パワーのべき指数")]
            public float CompeteBeforeSpurtSpeedUpPowExponent;
            [Tooltip("スパート前位置取り勝負：速度上昇量：パワーの係数")]
            public float CompeteBeforeSpurtSpeedUpPowCoef;
            
            [Tooltip("スパート前位置取り勝負：速度上昇量：根性の除数")]
            public float CompeteBeforeSpurtSpeedUpGutsDivisor;
            [Tooltip("スパート前位置取り勝負：速度上昇量：根性のべき指数")]
            public float CompeteBeforeSpurtSpeedUpGutsExponent;
            
            [Tooltip("スパート前位置取り勝負：速度上昇量：全体の係数")]
            public float CompeteBeforeSpurtSpeedUpCoef;
            
            [Tooltip("スパート前位置取り勝負：速度上昇量：作戦ごとの係数")]
            public CompeteBeforeSpurtSpeedUpRunningStyleCoef[] CompeteBeforeSpurtSpeedUpRunningStyleCoefArray;
            
            [Tooltip("スパート前位置取り勝負：少数派ボーナス：近くの定義")]
            public int CompeteBeforeSpurtMinorBonusNearDistance;
            [Tooltip("スパート前位置取り勝負：少数派ボーナス：割合の閾値")]
            public float CompeteBeforeSpurtMinorBonusPerThreshold;
            [Tooltip("スパート前位置取り勝負：少数派ボーナス：作戦ごとの係数")]
            public CompeteBeforeSpurtMinorBonusRunningStyleCoef[] CompeteBeforeSpurtMinorBonusRunningStyleCoefArray;
            
            [Tooltip("スパート前位置取り勝負：持久力消費：作戦ごとの係数")]
            public CompeteBeforeSpurtConsumeStaminaRunningStyleCoef[] CompeteBeforeSpurtConsumeStaminaRunningStyleCoefArray;
            [Tooltip("スパート前位置取り勝負：持久力消費：固定値")]
            public int CompeteBeforeSpurtConsumeStaminaValue;
            [Tooltip("スパート前位置取り勝負：持久力消費：抽選結果に応じた倍率加算値")]
            public float CompeteBeforeSpurtConsumeStaminaValueRateAddend;

            [Tooltip("スパート前位置取り勝負：持久力消費：距離ごとの係数")]
            public CompeteBeforeSpurtConsumeStaminaCourseDistanceCoef[] CompeteBeforeSpurtConsumeStaminaCourseDistanceCoefArray;
        }
        [Space(20), Header("スパート前位置取り勝負" + HEADER_SEPARATOR)]
        public CompeteBeforeSpurtParam CompeteBeforeSpurt;
        
        #endregion
        
        //---------------------------------------------------------------
        #region リード確保

        /// <summary> リード確保：セーフティリード：リードの基準値</summary>
        [Serializable]
        public class SecureLeadSafetyLeadMyRunningStyleDistance
        {
            [Header("自分")]
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Header("相手")]
            public SecureLeadSafetyLeadOpponentRunningStyleDistanceThreshold[] OpponentRunningStyleDistanceThresholdArray;
        }
        
        /// <summary> リード確保：セーフティリード：リードの基準値</summary>
        [Serializable]
        public class SecureLeadSafetyLeadOpponentRunningStyleDistanceThreshold
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Header("数値")]
            [Tooltip("閾値")]
            public float Threshold;
        }
        
        /// <summary> リード確保：スピード上昇：作戦別係数 </summary>
        [Serializable]
        public class SecureLeadSpeedUpRunningStyleCoef
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("係数")]
            public float Coef;
        }
        
        /// <summary> リード確保：少数派ボーナス：作戦別係数 </summary>
        [Serializable]
        public class SecureLeadMinorBonusRunningStyleCoef
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("係数")]
            public float Coef;
        }
        
        /// <summary> リード確保：持久力消費：作戦別係数 </summary>
        [Serializable]
        public class SecureLeadConsumeStaminaRunningStyleCoef
        {
            [Tooltip("特殊脚質")]
            public RaceDefine.RunningStyleEx RunningStyleEx;
            [Tooltip("脚質")]
            public RaceDefine.RunningStyle RunningStyle;
            [Tooltip("係数")]
            public float Coef;
        }
        
        /// <summary> リード確保：持久力消費：距離ごとの係数 </summary>
        [Serializable]
        public class SecureLeadConsumeStaminaCourseDistanceCoef
        {
            [Tooltip("閾値")]
            public float Threshold;
            [Tooltip("係数")]
            public float Coef;
        }

        /// <summary> リード確保 </summary>
        [Serializable]
        public class SecureLeadParam
        {
            [Tooltip("リード確保：発動区間の開始位置")]
            public int SecureLeadStartSection;
            [Tooltip("リード確保：発動区間の終了位置")]
            public int SecureLeadEndSection;
            
            [Tooltip("リード確保：発動チェックする間隔（秒）")]
            public int SecureLeadCheckIntervalSec;
            [Tooltip("リード確保：発動後のクールダウン時間（秒）")]
            public int SecureLeadCoolDownSec;
           
            [Tooltip("リード確保：発動率係数")]
            public float SecureLeadPerCoef;
            
            [Tooltip("リード確保：終了条件の時間（秒）")]
            public int SecureLeadActiveSec;

            [Tooltip("リード確保：セーフティリード：距離による伸縮の加算定数")]
            public int SecureLeadSafetyLeadAddend;
            [Tooltip("リード確保：セーフティリード：距離による伸縮の係数")]
            public float SecureLeadSafetyLeadCoef;
            [Tooltip("リード確保：セーフティリード：リードの基準値")]
            public SecureLeadSafetyLeadMyRunningStyleDistance[] SecureLeadSafetyLeadRunningStyleDistanceArray;
         
            [Tooltip("リード確保：速度上昇量：根性の除数")]
            public float SecureLeadSpeedUpGutsDivisor;
            [Tooltip("リード確保：速度上昇量：根性のべき指数")]
            public float SecureLeadSpeedUpGutsExponent;
            
            [Tooltip("リード確保：速度上昇量：全体の係数")]
            public float SecureLeadSpeedUpCoef;
            
            [Tooltip("リード確保：速度上昇量：作戦ごとの係数")]
            public SecureLeadSpeedUpRunningStyleCoef[] SecureLeadSpeedUpRunningStyleCoefArray;
            
            [Tooltip("リード確保：少数派ボーナス：近くの定義")]
            public int SecureLeadMinorBonusNearDistance;
            [Tooltip("リード確保：少数派ボーナス：割合の閾値")]
            public float SecureLeadMinorBonusPerThreshold;
            [Tooltip("リード確保：少数派ボーナス：作戦ごとの係数")]
            public SecureLeadMinorBonusRunningStyleCoef[] SecureLeadMinorBonusRunningStyleCoefArray;
            
            [Tooltip("リード確保：持久力消費：作戦ごとの係数")]
            public SecureLeadConsumeStaminaRunningStyleCoef[] SecureLeadConsumeStaminaRunningStyleCoefArray;
            [Tooltip("リード確保：持久力消費：固定値")]
            public int SecureLeadConsumeStaminaValue;
            [Tooltip("リード確保：持久力消費：距離ごとの係数")]
            public SecureLeadConsumeStaminaCourseDistanceCoef[] SecureLeadConsumeStaminaCourseDistanceCoefArray;
        }
        
        [Space(20), Header("リード確保" + HEADER_SEPARATOR)]
        public SecureLeadParam SecureLead;
        #endregion

        //---------------------------------------------------------------
        #region 上限突破パラメータスタミナ
        /// <summary> 上限突破パラメータスタミナ: 目指す速度上昇加算値 計算用 距離別係数 </summary>
        [Serializable]
        public class StaminaLimitBreakBuffDistanceCoef
        {
            [Tooltip("距離帯がこの値未満だったら")]
            public int DistanceThreshold;
            [Tooltip("この係数を計算式に乗算する")]
            public float Multiply;
        }
        [Serializable]
        public class StaminaLimitBreakBuffParam
        {
            [Space(10), Header("<上限突破パラメータスタミナ : 目指す速度計算用>")]
            [Tooltip("上限突破パラメータスタミナ : 目指す速度計算用加速係数(スタミナにかかる係数)")]
            public float TargetSpeedForStaminaCoef;
            [Tooltip("上限突破パラメータスタミナ : 目指す速度計算用加速係数(全体にかかる係数)")]
            public float TargetSpeedForWholeCoef;
            [Tooltip("上限突破パラメータスタミナ : 目指す速度計算用 距離別係数")]
            public StaminaLimitBreakBuffDistanceCoef[] TargetSpeedDistanceCoefArray;

            [Tooltip("上限突破パラメータスタミナ : パワーによる揺らぎ幅テーブルの抽選確率変動値(10000倍した数です)")]
            public int TargetSpeedRandomTableChangeProbabilityByPower;
            [Tooltip("上限突破パラメータスタミナ : 目指す速度計算用揺らぎ幅テーブル")]
            public StaminaLimitBreakBuffTargetSpeedRandomTable[] TargetSpeedRandomTableArray;
        }

        [Serializable]
        public class StaminaLimitBreakBuffTargetSpeedRandomTable
        {
            [Space(10), Header("<上限突破パラメータスタミナ : 目指す速度計算用揺らぎ幅テーブル>")]
            [Tooltip("上限突破パラメータスタミナ : 揺らぎ幅テーブルタイプ")]
            public RaceDefine.RandomTableType TableType;
            [Tooltip("上限突破パラメータスタミナ : 下限値を10000倍した数")]
            public int LowerLimit;
            [Tooltip("上限突破パラメータスタミナ : 上限値を10000倍した数)")]
            public int UpperLimit;
            [Tooltip("上限突破パラメータスタミナ : テーブル抽選確率を10000倍した数)")]
            public int Probability;

            public void Copy(StaminaLimitBreakBuffTargetSpeedRandomTable src)
            {
                TableType = src.TableType;
                LowerLimit = src.LowerLimit;
                UpperLimit = src.UpperLimit;
                Probability = src.Probability;
            }
        }

        [Space(20), Header("上限突破パラメータスタミナ" + HEADER_SEPARATOR)]
        public StaminaLimitBreakBuffParam StaminaLimitBreakBuff;
        #endregion // 上限突破パラメータスタミナ
        //---------------------------------------------------------------
        #region <掛かり>
        [Serializable]
        public class TemptationParam
        {
            [Tooltip("掛かり：発動抽選開始区間")]
            public int TemptationLotSectionMin;
            [Tooltip("掛かり：発動抽選終了区間(この区間も抽選対象に含まれる)")]
            public int TemptationLotSectionMax;

            [Tooltip("掛かり：発動確率 = (TemptationStartPerVal1 / Log10(賢さ * 賢さ) )の2乗")]
            public float TemptationStartPerVal1;

            [Tooltip("掛かり：強制終了する時間")]
            public float TemptationForceEndTime;
            [Tooltip("掛かり：終了チェック間隔秒")]
            public float TemptationEndCheckTime;
            [Tooltip("掛かり：終了確率")]
            public float TemptationEndPer;

            [Tooltip("掛かり：追いが差しのポジション維持になる確率")]
            public float OikomiToSashiPer;
            [Tooltip("掛かり：追いが先行のポジション維持になる確率")]
            public float OikomiToSenkoPer;
            [Tooltip("掛かり：追いが逃げのポジション維持になる確率")]
            public float OikomiToNigePer;

            [Tooltip("掛かり：差しが先行のポジション維持になる確率")]
            public float SashiToSenkoPer;
            [Tooltip("掛かり：差しが逃げのポジション維持になる確率")]
            public float SashiToNigePer;

            [Tooltip("掛かり：先行が逃げのポジション維持になる確率")]
            public float SenkoToNigePer;
        }
        [Space(20), Header("掛かり" + HEADER_SEPARATOR)]
        public TemptationParam Temptation;
#endregion


        //---------------------------------------------------------------
#region <競り合い（ハナ奪い合い）>
        [Serializable]
        public class CompeteTopParam
        {
            [Tooltip("この距離以降で競り合い開始可能")]
            public float CheckStartDistance;
            [Tooltip("この区間まで競り合い開始可能")]
            public int CheckEndSection;
            [Tooltip("競り合い中のキャラがこの区間に達したら全員強制終了")]
            public int EndSection;
            
            [Tooltip("逃げの競り合い回数上限")]
            public int NigeCount;
            [Tooltip("大逃げの競り合い回数上限")]
            public int OonigeCount;
            
            [Tooltip("この距離差以内に近付いたら競り合い開始")]
            public float DistanceGap1;
            [Tooltip("このレーン差以内に近付いたら競り合い開始")]
            public float LaneGap1;

            [Tooltip("この距離差以上遠ざかったら競り合い終了")]
            public float DistanceGap2;
            [Tooltip("このレーン差以上遠ざかったら競り合い終了")]
            public float LaneGap2;
            
            [Tooltip("継続時間計算に使う変数1")]
            public float TimeCoef1;
            [Tooltip("継続時間計算に使う変数2")]
            public float TimeCoef2;
            [Tooltip("継続時間計算に使う変数3")]
            public float TimeCoef3;
            
            [Tooltip("目指す速度加算値計算に使う変数1")]
            public float AddParam1Coef1;
            [Tooltip("目指す速度加算値計算に使う変数2")]
            public float AddParam1Coef2;
            [Tooltip("目指す速度加算値計算に使う変数3")]
            public float AddParam1Coef3;
        }
        [Space(20), Header("競り合い（ハナ奪い合い）" + HEADER_SEPARATOR)]
        public CompeteTopParam CompeteTop;
#endregion
    
    
        //---------------------------------------------------------------
#region <競り合い（叩き合い）>
        [Serializable]
        public class CompeteFightParam
        {
            [Tooltip("候補に入れるための距離差")]
            public float DistanceGap;
            [Tooltip("候補に入れるためのレーン差")]
            public float LaneGap;
            [Tooltip("候補に入れるための付近にいる継続時間")]
            public float TargetContinueTime;
            [Tooltip("候補に入れるための速度差")]
            public float SpeedGap;
            [Tooltip("候補に入れるための順位比率%")]
            public int TargetOrderPer;

            [Tooltip("継続するための付近の距離")] 
            public float TargetContinueDistance;
            [Tooltip("このHp%を切ったら終了")]
            public int HpPer;
            [Tooltip("このHp%以上なかったら開始できない")]
            public int HpPer2;
            
            [Tooltip("目指す速度加算値計算に使う変数1")]
            public float AddParam1Coef1;
            [Tooltip("目指す速度加算値計算に使う変数2")]
            public float AddParam1Coef2;
            [Tooltip("目指す速度加算値計算に使う変数3")]
            public float AddParam1Coef3;
            
            [Tooltip("加速度の加算値計算に使う変数1")]
            public float AddParam2Coef1;
            [Tooltip("加速度の加算値計算に使う変数2")]
            public float AddParam2Coef2;
            [Tooltip("加速度の加算値計算に使う変数3")]
            public float AddParam2Coef3;
        }
        [Space(20), Header("競り合い（叩き合い）" + HEADER_SEPARATOR)]
        public CompeteFightParam CompeteFight;
#endregion
            
            
        //---------------------------------------------------------------
#region <囲まれ>
        [Serializable]
        public class SurroundedParam
        {
            public float DistanceGapAbs1;
            public float LaneGap1;
            
            public float DistanceGap2;
            public float LaneGapAbs2;
        }
        [Space(20), Header("囲まれ" + HEADER_SEPARATOR)]
        public SurroundedParam Surrounded;
#endregion

            
        //---------------------------------------------------------------
        [Space(20), Header("視野範囲" + HEADER_SEPARATOR), Tooltip("視野範囲進行方向の距離(全キャラ共通)")]
        public float visibleDistance;


        //---------------------------------------------------------------
        [Space(20), Header("追い越し" + HEADER_SEPARATOR), Tooltip("追い越し実行できるとみなす【距離差分/速度差分】秒")]
        public float overTakeDistPerSpeed;
        [Tooltip("追い越し終了後のクールダウン時間")]
        public float overTakeCoolDownTime;
        [Tooltip("追い越した/追い越されたとカウントするクールダウン時間")]
        public float overTakeCountCoolDownTime;

        [Tooltip("終盤のイン側追い抜き時の係数 ※1.0が標準。大きい値になるほど、優先度が低くなる。")]
        public float overTakeInLaneCoefEnd;
        [Tooltip("終盤のアウト側追い抜き時の係数 ※1.0が標準。大きい値になるほど、優先度が低くなる。")]
        public float overTakeOutLaneCoefEnd;

        //---------------------------------------------------------------
        [Serializable]
        public class SkillParam
        {
            [Tooltip("効果継続時間が何m基準の値か")]
            public int AbilityTimeDivideDistance;
            [Tooltip("クールダウン時間が何m基準の値か")]
            public int CoolDownTimeDivideDistance;

            [Tooltip("random_lot_other_activate 抽選間隔秒")]
            public float RandomLotOtherActivateLotInterval;

            [Tooltip("レース開始時の発動確率 = LotActivatePerVal1 - (LotActivatePerVal2 / wiz)")]
            public float LotActivatePerVal1;
            [Tooltip("レース開始時の発動確率 = LotActivatePerVal1 - (LotActivatePerVal2 / wiz)")]
            public float LotActivatePerVal2;

            [Tooltip("順位一つ上のキャラと近いとみなす距離")]
            public float InfrontHorseNearDistance;
            [Tooltip("順位一つ下のキャラと近いとみなす距離")]
            public float BehindHorseNearDistance;

            [Tooltip("順位一つ上のキャラとレーンが近いとみなす距離")]
            public float InfrontHorseNearLaneDistance;
            [Tooltip("順位一つ下のキャラとレーンが近いとみなす距離")]
            public float BehindHorseNearLaneDistance;
            
            [Tooltip("レース開始時の発動確率最小値")]
            public int ActivatePerMin;

            [Serializable]
            public class TeamTotalStatusCoef
            {
                [Tooltip("チームメンバーのステータス合計値がこの値未満だったら、")]
                public float StatusThreshold;
                [Tooltip("この係数を効果値に乗算する")]
                public float Multiply;
            }

            public TeamTotalStatusCoef[] TeamTotalStatusCoefAscArray;

            public float AbilityTimeDiffDistanceTopCoefPerDistance;
            public float AbilityTimeDiffDistanceTopCoefMin;
            public float AbilityTimeDiffDistanceTopCoefMax;
            
            [Serializable]
            public class AbiiltyValueUsageRandom
            {
                public float AbilityValueCoef;
                public int Per;
            }

            [Tooltip("ability_value_usage=8で使われる効果値係数と抽選確率")]
            public AbiiltyValueUsageRandom[] AbiiltyValueUsageRandom1Array;
            [Tooltip("ability_value_usage=9で使われる効果値係数と抽選確率")]
            public AbiiltyValueUsageRandom[] AbiiltyValueUsageRandom2Array;

            [Serializable]
            public class NearParam
            {
                public float Distance;
                public float LaneDistance;
            }

            [Tooltip("一つ後ろのキャラと近いかどうかの距離・レーン")]
            public NearParam[] BehindNearParamArray;

            [Tooltip("レーン移動速度プラス時、目指す速度加算値の計算式の変数1")]
            public float LaneMoveAddParam1;
            [Tooltip("レーン移動速度プラス時、目指す速度加算値の計算式の変数2")]
            public float LaneMoveAddParam2;
            
            [Serializable]
            public class TimeRemainHpCoef
            {
                [Tooltip("残りHpがこの値未満だったら、")]
                public float HpThreshold;
                [Tooltip("この係数を効果時間に乗算する")]
                public float Multiply;
            }

            [Tooltip("SkillAbilityValueUsage.MultiplyRemainHp1(効果時間適用方法3)で使用する係数")]
            public TimeRemainHpCoef[] TimeRemainHpCoefAscArray;
            [Tooltip("SkillAbilityValueUsage.MultiplyRemainHp1(効果時間適用方法7)で使用する係数")]
            public TimeRemainHpCoef[] TimeRemainHpCoefAscArray2;

            [Serializable]
            public class RaceWinCoef
            {
                [Tooltip("育成レースの勝利数がこの値未満だったら、")]
                public int WinCountThreshold;
                [Tooltip("この係数を効果値に乗算する")]
                public float Multiply;
            }
            public RaceWinCoef[] RaceWinCountCoefAscArray;
            
            [Serializable]
            public class OrderChangeCoef
            {
                [Tooltip("順位上昇/下降回数がこの値未満だったら、")]
                public int OrderChangeCountThreshold;
                [Tooltip("この係数を効果値に乗算する")]
                public float Multiply;
            }
            public OrderChangeCoef[] OrderUpCornerPhaseEndAfterCoefAscArray;
            
            [Serializable]
            public class BaseStatusCoef
            {
                [Tooltip("基礎ステータスがこの値未満だったら、")]
                public float BaseStatusThreshold;
                [Tooltip("この係数を効果値に乗算する")]
                public float Multiply;
            }
            public BaseStatusCoef[] BaseWizCoefAscArray;

            /// <summary> 素のパラメータの中で最大のパラメータを参照した閾値リスト </summary>
            public BaseStatusCoef[] MaximumRawStatusCoefAscArray;

            [Tooltip("追い抜くたびにスキルの効果時間に加算される値")]
            public float OrderUpAddAbilityTime;
            [Tooltip("追い抜くたびにスキルの効果時間に加算される効果の上限回数")]
            public int OrderUpAddAbilityTimeMaxCount;

            [Serializable]
            public class SkillActivateTagGroup
            {
                [Tooltip("タグId")]
#if GALLOP
                public int[] TagArray;
                public int[] TagIntArray=> TagArray;
#else
                public string TagArray;
                public int[] TagIntArray;
#endif
            }
            [Tooltip("スキル発動時にスキル実行判定用評価タググループリスト")]
            public SkillActivateTagGroup[] SkillActivateTagGroupArray;

            [Tooltip("ability_value_usage=14で使われる評価タグリスト")]
#if GALLOP
            public int[] AbilityValueUsageTagGroup1Array;
            public int[] AbilityValueUsageTagGroup1IntArray=> AbilityValueUsageTagGroup1Array;
#else
            public string AbilityValueUsageTagGroup1Array;
            public int[] AbilityValueUsageTagGroup1IntArray;
#endif

            [Serializable]
            public class ActivateSpecificSkill
            {
                /// <summary> SkillDefine.SkillAbilityTypeで定義される値 </summary>
                /// <remarks> プランナーさんは数字で管理しているので数字を入力してもらう </remarks>
                public int AbilityType;
            #if GALLOP
                public int[] ActivateSkillIdArray;
                public int[] ActivateSkillIdIntArray => ActivateSkillIdArray;
            #else
                public string ActivateSkillIdArray;
                public int[] ActivateSkillIdIntArray;
            #endif
            }
            [Tooltip("特定のスキル強制発動スキルの対象スキルIDをまとめたリスト")]
            public ActivateSpecificSkill[] ActivateSpecificSkillArray;

            [Serializable]
            public class ActivateSpecificTagGroupSkillCountCoef
            {
                [Tooltip("順位上昇/下降回数がこの値未満だったら、")]
                public int ActivateSpecificTagGroupSkillCountThreshold;
                [Tooltip("この係数を効果値に乗算する")]
                public float Multiply;
            }
            public ActivateSpecificTagGroupSkillCountCoef[] ActivateSpecificTagGroupSkillCountCoefAscArray;

            [Serializable]
            public class FanCountCoef
            {
                [Tooltip("ファン数がこの値未満だったら、")]
                public int FanCountThreshold;
                [Tooltip("この係数を効果値に乗算する")]
                public float Multiply;
            }
            [Tooltip("ファン数による効果値乗算の閾値リスト")]
            public FanCountCoef[] FanCountCoefAscArray;

            [Serializable]
            public class AdditionalActivateAbilityMaxCount
            {
                [Tooltip("追加発動タイプ(additional_activate_type)")]
                public SkillDefine.SkillAbilityAdditionalActivateType AdditionalActivateType;

                [Tooltip("追加発動上限回数")] public int AdditionalActivateMaxCount;
            }

            [Tooltip("追加発動上限回数リスト")]
            public AdditionalActivateAbilityMaxCount[] AdditionalActivateAbilityMaxCountArray;

            [Serializable]
            public class DistanceDiffTopCoef
            {
                [Tooltip("先頭との距離がこの値未満だったら、")]
                public int DistanceDiffTopThreshold;
                [Tooltip("この係数を効果値に加算する(マスターデータと同じく10000倍した数を入力してください)")]
                public int AddValue;
            }
            public DistanceDiffTopCoef[] DistanceDiffTopCoefAscArray;

            [Serializable]
            public class BlockedSideMaxContinueTimeCoef
            {
                [Tooltip("サイドをブロックされた最大時間がこの値未満だったら、")]
                public int BlockedSideMaxContinueTimeThreshold;
                [Tooltip("この係数を乗算する")]
                public float Multiply;
            }

            [Tooltip("SkillAbilityValueUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1(効果値適用方法20)で使用する係数")]
            public BlockedSideMaxContinueTimeCoef[] BlockedSideMaxContinueTimePhaseMiddleRunAbilityValueCoefAscArray1;
            [Tooltip("SkillAbilityValueUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2(効果値適用方法21)で使用する係数")]
            public BlockedSideMaxContinueTimeCoef[] BlockedSideMaxContinueTimePhaseMiddleRunAbilityValueCoefAscArray2;

            [Tooltip("SkillAbilityTimeUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1(効果時間適用方法5)で使用する係数")]
            public BlockedSideMaxContinueTimeCoef[] BlockedSideMaxContinueTimePhaseMiddleRunAbilityTimeCoefAscArray1;
            [Tooltip("SkillAbilityTimeUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2(効果時間適用方法6)で使用する係数")]
            public BlockedSideMaxContinueTimeCoef[] BlockedSideMaxContinueTimePhaseMiddleRunAbilityTimeCoefAscArray2;
            
            [Serializable]
            public class SpeedCoef
            {
                [Tooltip("1200以上の圧縮、やる気、スキル効果の効果を含めたスピードの値がこの値未満だったら、")]
                public int SpeedThreshold;
                [Tooltip("この係数を乗算する")]
                public float Multiply;
            }

            [Tooltip("SkillAbilityValueUsage.MultiplySpeed1(効果値適用方法22)で使用する係数")]
            public SpeedCoef[] SpeedAbilityValueCoefAscArray1;
            [Tooltip("SkillAbilityValueUsage.MultiplySpeed2(効果値適用方法23)で使用する係数")]
            public SpeedCoef[] SpeedAbilityValueCoefAscArray2;

            // シナリオ専用パラメーターを獲得した値に応じて、効果値に係数を乗算
            [Serializable]
            public class ScenarioParameterCoef
            {
                [Tooltip("獲得したシナリオ専用パラメーターの値がこの値未満だったら、")]
                public int LevelThreshold;
                [Tooltip("この係数を乗算する")]
                public float Multiply;
            }

            [Tooltip("SkillAbilityValueUsage.MultiplyArcGlobalPotentialLevel(効果値適用方法24)で使用する係数")]
            public ScenarioParameterCoef[] ArcGlobalPotentialLevelCoefArray;
            
            // レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値に応じて効果量に倍率をかける
            [Serializable]
            public class TopLeadAmountCoef
            {
                [Tooltip("レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値がこの値未満だったら、")]
                public float Threshold;
                [Tooltip("この係数を乗算する")]
                public float Multiply;
            }
            [Tooltip("SkillAbilityValueUsage.MultiplyTopLeadAmount(効果値適用方法25)の計測開始区間（%）")]
            public float TopLeadAmountStartCourseRatePer;
            [Tooltip("SkillAbilityValueUsage.MultiplyTopLeadAmount(効果値適用方法25)の計測終了区間（%）")]
            public float TopLeadAmountEndCourseRatePer;
            [Tooltip("SkillAbilityValueUsage.MultiplyTopLeadAmount(効果値適用方法25)で使用する係数")]
            public TopLeadAmountCoef[] TopLeadAmountCoefArray;
            
            [Tooltip("SkillAbilityValueUsage.MultiplySportCompetitionWinCount(効果値適用方法26)で使用する係数")]
            public ScenarioParameterCoef[] SportFinalCompeWinCountCoefArray;
            
            [Tooltip("SkillAbilityValueUsage.MultiplyCookCookingPt(効果値適用方法27)で使用する係数")]
            public ScenarioParameterCoef[] CookCookingPtCoefArray;
            
        }
        [Space(20), Header("スキル" + HEADER_SEPARATOR)]
        public SkillParam Skill;
        
        //---------------------------------------------------------------
        [Serializable]
        public class PopularityParam
        {
            public float Z1Add;
            public float Z1Coef;
            public float Z2Add;
            public float Z2Coef;
            public float RankCenterZ1Pow;
            public float RankCenterZ2Pow;

            public int DeviationBase;
            public int DeviationCoef;
            public int DeviationThreshold;
            public int DeviationMinus;
            public int DeviationFix;
        }
        [Space(20), Header("人気" + HEADER_SEPARATOR)]
        public PopularityParam Popularity;
        
        //---------------------------------------------------------------
        [Serializable]
        public class DefeatParam
        {
            [Tooltip("「同じ走法のキャラ数が一定数以上いる」の%閾値。単位：%")]
            public int DefeatSameRunningStylePer;
            [Tooltip("「根性順位が一定以下」の%閾値。単位：%")]
            public int DefeatGutsOrderPer;
            [Tooltip("「掛かった回数」の閾値。単位：回数")]
            public int DefeatTemptaionCount;
            [Tooltip("「前方ブロックをされた累積時間」の閾値。単位：秒")]
            public float DefeatFrontBlockAccumulateTime;
            [Tooltip("惜敗と見なす着順の閾値。単位：着順")]
            public int DefeatLoseFinishOrder;
            [Tooltip("惜敗と見なす馬身差の閾値。単位：馬身")]
            public int DefeatLoseBashinDiff;
            [Tooltip("距離適性不足と見なされる閾値。この値未満なら不足。")]
            public RaceDefine.ProperGrade DefeatProperDistance;
            [Tooltip("馬場適性不足と見なされる閾値。この値未満なら不足。")]
            public RaceDefine.ProperGrade DefeatProperGround;
            [Tooltip("やる気不足と見なされるやる気の閾値。この値以下なら不足。")]
            public RaceDefine.Motivation DefeatMotivation;
        }
        [Space(20), Header("敗因" + HEADER_SEPARATOR)]
        public DefeatParam Defeat;
        
        //---------------------------------------------------------------
        [Space(20), Header("見た目：モーション" + HEADER_SEPARATOR), Tooltip("走りモーション速度加算値")]
        public float runMotionSpeedCoef;
        [Tooltip("走りモーション再生速度最小値")]
        public float runMotionSpeedMin;
        [Tooltip("走りモーション再生速度最大値")]
        public float runMotionSpeedMax;
        [Tooltip("キャラの速度がこの値の時にモーション再生速度がrunMotionSpeedMinとなる ※MotionSpped = 再生速度最小値 + ( キャラ速度 - runMotionSpeedBaseHorseSpeed ) / runMotionSpeedHorseSpeedRate")]
        public float runMotionSpeedBaseHorseSpeed;
        [Tooltip("キャラの速度がrunMotionSpeedBaseHorseSpeedを上回っているときにスピードをどれだけ上げるかの係数 ※MotionSpped = 再生速度最小値 + ( キャラ速度 - runMotionSpeedBaseHorseSpeed ) / runMotionSpeedHorseSpeedRate")]
        public float runMotionSpeedHorseSpeedRate;

        [Tooltip("モーションブレンド率：RaceBaseSpeedよりこの値速以上速度が低いとブレンド率0")]
        public float runMotionBlendRateMinSpeedOffset;
        [Tooltip("モーションブレンド率：RaceBaseSpeedよりこの値速以上速度が高いとブレンド率1")]
        public float runMotionBlendRateMaxSpeedOffset;

        [Tooltip("レーン移動Y回転 Y回転を入れるレーン移動閾値(>)")]
        public float laneMoveRotYThresholdPerSec;
        [Tooltip("レーン移動Y回転 Y回転を入れるバンク角閾値(<=)")]
        public float laneMoveRotYThresholdRotZ;
        [Tooltip("レーン移動Y回転 Y回転度合")]
        public float laneMoveRotYPerMovedLane;
        [Tooltip("レーン移動Y回転 Y回転最大値")]
        public float laneMoveRotYMax;

        [Tooltip("レーン移動Y回転 １秒当たりの回転度数")]
        public float laneMoveRotYRotatePerSec;
        [Tooltip("レーン移動Y回転 １秒当たりの回転度数(0へ変化する時)")]
        public float laneMoveRotYRotatePerSecToZero;


        [Tooltip("レーン移動Z回転 Z回転を入れるレーン移動閾値(>)")]
        public float laneMoveRotZThresholdPerSec;
        [Tooltip("レーン移動Y回転 Z回転を入れるバンク角閾値(<=)")]
        public float laneMoveRotZThresholdRotZ;
        [Tooltip("レーン移動Z回転 Z回転度合")]
        public float laneMoveRotZPerMovedLane;
        [Tooltip("レーン移動Z回転 Z回転最大値")]
        public float laneMoveRotZMax;

        [Tooltip("レーン移動Z回転 １秒当たりの回転度数")]
        public float laneMoveRotZRotatePerSec;
        [Tooltip("レーン移動Z回転 １秒当たりの回転度数(0へ変化する時)")]
        public float laneMoveRotZRotatePerSecToZero;

        [Tooltip("走法(ベース/ピッチ/ストライド)ごとの再生速度倍率")]
        public float[] RunMotionSpeedCoefByRunningType;

        //---------------------------------------------------------------
        [Space(20), Header("見た目：汚れ" + HEADER_SEPARATOR)]
        [Tooltip("足元、1m辺りの汚れエネルギー。良、鞘重、重、不良の順番")]
        public float[] footDirtBaseEnergy;
        [Tooltip("下半身、1m辺りの汚れエネルギー。良、鞘重、重、不良の順番")]
        public float[] lowerDirtBaseEnergy;
        [Tooltip("上半身、1m辺りの汚れエネルギー。良、鞘重、重、不良の順番")]
        public float[] upperDirtBaseEnergy;

        [Tooltip("汚れが適用される、前方ウマ娘との距離")]
        public float dirtDistance;
        [Tooltip("汚れが適用される、前方ウマ娘との角度")]
        public float dirtAngle;

        [Tooltip("足元、汚れが切り替わるエネルギー量")]
        public float[] footDirtStep;
        [Tooltip("下半身、汚れが切り替わるエネルギー量")]
        public float[] lowerDirtStep;
        [Tooltip("上半身、汚れが切り替わるエネルギー量")]
        public float[] upperDirtStep;

        [Space(20), Header("見た目：イベントカメラ" + HEADER_SEPARATOR)]
        [Tooltip("「イベントカメラ再生→コースカメラ戻り」から起算して次のイベントカメラ再生可能までの時間")]
        public float EventCameraIntervalSec;
        [Tooltip("ゴール手前この距離からはイベントカメラ再生不可")]
        public int EventCameraUnPlayableDistanceFromGoal;
        [Tooltip("ゴール手前この距離からはスキルカットイン後イベントカメラ再生不可（チーム競技場のみ）")]
        public int SkillCutInEventCameraUnPlayableDistanceFromGoalTeamStadium;
        [Tooltip("順位一つ後ろのキャラの左右を判定するとき、自分がトップの場合、２着とこの距離以上離れていたら近くにいないと見なす（単位はm）")]
        public float BehindPosCheckDistanceThresholdTop;
        [Tooltip("順位一つ後ろのキャラの左右を判定するとき、この距離以上離れていたら近くにいないと見なす（単位はm）")]
        public float BehindPosCheckDistanceThreshold;
        [Tooltip("順位一つ後ろのキャラの左右を判定するとき、このレーン距離以内なら左右どちらにもいないと見なす（単位はm）")]
        public float BehindPosCheckLaneDistanceMeterThreshold;

        [Serializable]
        public class CameraParam
        {
            [Tooltip("横画面ゲートインデモの座標加算値")]
            public Vector3 GateInDemoPosOffsetLandscape;
            [Tooltip("横画面ゲートインデモのFOV加算値")]
            public float GateInDemoFovOffsetLandscape;
            
            [Tooltip("横画面ゲートインデモCut2のFOV値（チームver）")]
            public float GateInDemoCut2FovLandscape;
            [Tooltip("横画面ゲートインデモCut2のFOV値（１人ver）")]
            public float GateInDemoCut2FovLandscapeSingle;
        }
        [Space(20), Header("カメラ" + HEADER_SEPARATOR)]
        public CameraParam Camera;

        //---------------------------------------------------------------
        [Space(20), Header("見た目：出走待ち" + HEADER_SEPARATOR)]
        [Tooltip("ゲートオープン後、キャラが走り出すまでの待機時間")]
        public float WaitStartDashSec;

        //---------------------------------------------------------------
        [Serializable]
        public class JikkyoParam
        {
            [Tooltip("ゴール付近と判断する残り距離")]
            public float NearGoalDistance;
            [Tooltip("ゴール付近で割り込み実況禁止にする残り距離")]
            public float NearGoalInterruptDisableDistance;
            [Tooltip("ゴール前で解説を禁止する残り距離")]
            public float CommentDisableDistance;
            [Tooltip("道中、この秒数実況が発行されなかったら、間を埋めるための順位実況を入れ")]
            public float SilentTimeMaxSec;
            [Tooltip("Immidiate時の再生禁止時間")]
            public float ImmidiateSuspendTime;
            
            [Tooltip("起動条件:「桜花賞、皐月賞、オークス、ダービー、秋華賞、菊花賞」のどれにも勝っていない の各RaceInstanceId")]
#if GALLOP
            public int[] NotWinRaceGroup1RaceInstanceIdArray;
#else
            public string NotWinRaceGroup1RaceInstanceIdArray;
#endif

            [Tooltip("１文字当たりの待機秒数")]
            public float WaitSecPerChar;
            [Tooltip("待機秒数最小値")]
            public float WaitSecMin;
            [Tooltip("待機秒数最大値")]
            public float WaitSecMax;
            
            [Tooltip("ボイスが無いためキャラ名ボイスは再生せず、モブと同じ扱いのウマ番再生を行うCharaId")]
#if GALLOP
            public int[] NoVoiceCharaIdArray;
#else
            public string NoVoiceCharaIdArray;
#endif

            [Tooltip("ボイスが無いためキャラ名ボイスは再生せず、モブと同じ扱いのウマ番再生を行うCharaId(杉本氏実況用)")]
#if GALLOP
            public int[] NoVoiceCharaIdArraySingleModeVenus;
#else
            public string NoVoiceCharaIdArraySingleModeVenus;
#endif
            [Tooltip("最後に呼び出されてからこの時間経過するまでは省略対象")]
            public float OmitTagTime;

            /// <summary>
            /// セリフ間の詰め時間。
            /// </summary>
            [Serializable]
            public class CrossTime
            {
                [Tooltip("テンションごとの実況テキスト間で詰める秒数。race_jikkyo_base.csvのtensionカラムの値と一致(Element0は無効値)")]
                public float[] SentenceCrossTimeArray = new float[0];
                [Tooltip("テンションごとのウマ娘名の前を詰める秒数。ウマ娘名の前が馬番の場合。race_jikkyo_base.csvのtensionカラムの値と一致(Element0は無効値)")]
                public float[] BeforeHorseNameBanCrossTimeArray = new float[0];
                [Tooltip("テンションごとのウマ娘名の前を詰める秒数。ウマ娘名の前が馬番以外の場合。race_jikkyo_base.csvのtensionカラムの値と一致(Element0は無効値)")]
                public float[] BeforeHorseNameCrossTimeArray = new float[0];
                [Tooltip("テンションごとのウマ娘名の後を詰める秒数。race_jikkyo_base.csvのtensionカラムの値と一致(Element0は無効値)")]
                public float[] AfterHorseNameCrossTimeArray = new float[0];
                [Tooltip("テンションごとのウマ娘名の前を詰める秒数。ウマ娘名が助詞を伴う場合。race_jikkyo_base.csvのtensionカラムの値と一致(Element0は無効値)")]
                public float[] BeforeHorseNameWithPostpositionalCrossTimeArray = new float[0];
            }

            public CrossTime[] CrossTimeArray;
#if GALLOP
            public static CrossTime[] CreateDefaultCrossTimeArray()
            {
                return new []
                {
                    new CrossTime(), new CrossTime(),
                };
            }

            private CrossTime GetCrossTime(Jikkyo.JikkyoType type)
            {
                // Jikkyo.ActorIdの有効値1から配列に登録されているため、-1。
                int index = JikkyouVoiceLoader.GetActorId(type) - 1;
                
                return index >= 0 && index < CrossTimeArray.Length
                    ? CrossTimeArray[index]
                    : CrossTimeArray[0];
            }
            
            public CrossTime[] CreateCrossTimeArray()
            {
                return new[]
                {
                    GetCrossTime(Gallop.Jikkyo.JikkyoType.Jikkyou),
                    GetCrossTime(Gallop.Jikkyo.JikkyoType.Comment),
                };
            }
#endif
        }
        [Space(20), Header("実況" + HEADER_SEPARATOR)]
        public JikkyoParam Jikkyo;
        
        //---------------------------------------------------------------
        [Serializable]
        public class SoundParam
        {
            [Tooltip("通常歓声Aisac値。0:無効値,1:ファンファーレ,2:ゲートイン,3:出走待ち引きカメラ,4:序盤開始,5:中盤開始,6:先頭最終コーナー,7:最終直線開始,8:先頭がゴール手前,9:先頭がゴール")]
            public float[] ResidentCrowdAisacValueArray;
            [Tooltip("通常歓声Aisac遷移時間。0:無効値,1:ファンファーレ,2:ゲートイン,3:出走待ち引きカメラ,4:序盤開始,5:中盤開始,6:先頭最終コーナー,7:最終直線開始,8:先頭がゴール手前,9:先頭がゴール")]
            public float[] ResidentCrowdAisacInterpSecArray;

            [Tooltip("ロンシャンレース場用通常歓声Aisac値。" +
                     "0:無効値,1:ファンファーレ,2:ゲートイン,3:出走待ち引きカメラ,4:序盤開始,5:中盤開始,6:先頭最終コーナー," +
                     "7:最終直線開始,8:先頭がゴール手前,9:先頭がゴール"
                     )
            ]
            public float[] ParisResidentCrowdAisacValueArray;
            [Tooltip("ロンシャンレース場用通常歓声Aisac遷移時間。" +
                     "0:無効値, 1:ファンファーレ,2:ゲートイン,3:出走待ち引きカメラ,4:序盤開始,5:中盤開始,6:先頭最終コーナー," +
                     "7:最終直線開始,8:先頭がゴール手前,9:先頭がゴール"
                     )
            ]
            public float[] ParisResidentCrowdAisacInterpSecArray;

            [Tooltip("ゴール手前この距離になったら、通常歓声のモードをゴール手前に変更する")]
            public int ResidentCrowdGoalBeforeDistance;

            [Tooltip("カメラの正面ベクトルと注視対象キャラの正面ベクトルのDotがこの値以上なら正面or背後から映すカメラ")]
            public float BusDotVerticalThreshold;

            [Tooltip("カメラFovがこの値以上なら【画角・中】")]
            public float BusFovMiddleThreshold;
            [Tooltip("カメラFovがこの値以上なら【画角・広い】")]
            public float BusFovWideThreshold;
            
            [Tooltip("【画角・狭い】の時カメラと対象の距離がこの値以上なら【距離・中】")]
            public float BusDistanceMiddleForFovTightThreshold;
            [Tooltip("【画角・狭い】の時カメラと対象の距離がこの値以上なら【距離・遠い】")]
            public float BusDistanceFarForFovTightThreshold;
            
            [Tooltip("【画角・中】の時カメラと対象の距離がこの値以上なら【距離・中】")]
            public float BusDistanceMiddleForFovMiddleThreshold;
            [Tooltip("【画角・中】の時カメラと対象の距離がこの値以上なら【距離・遠い】")]
            public float BusDistanceFarForFovMiddleThreshold;
            
            [Tooltip("【画角・広い】の時カメラと対象の距離がこの値以上なら【距離・中】")]
            public float BusDistanceMiddleForFovWideThreshold;
            [Tooltip("【画角・広い】の時カメラと対象の距離がこの値以上なら【距離・遠い】")]
            public float BusDistanceFarForFovWideThreshold;

            [Tooltip("BGMボリューム値。0:無効値,1:中盤,2:先頭キャラが最終コーナーに入った,4:ラストかつ最終ストレート開始")]
            public float[] BGMVolumeArray;
            [Tooltip("BGMボリュームフェード時間。0:無効値,1:中盤,2:先頭キャラが最終コーナーに入った,4:ラストかつ最終ストレート開始")]
            public float[] BGMVolumeFadeSecArray;
        }
        [Space(20), Header("サウンド" + HEADER_SEPARATOR)]
        public SoundParam Sound;
    };
}
using System.Collections.Generic;


namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース関連定数/enum定義。
    /// </summary>
    //-------------------------------------------------------------------
    public static class RaceDefine
    {
        public const int FINAL_CORNER_END_ORDER_NULL = -1;
        
        public const int HORSE_INDEX_NULL = -1;
        public const int TEAM_STADIUM_MEMBER_MAX = 3; // チーム競技場のチームメンバー最大数。
        
        public const int POPULARITY_ICON_MAX = 5;

        // URP:置き換え対応
        /// <summary>
        /// マーカーのRenderingLayerMask
        /// </summary>
        public const int MARKER_RENDERING_LAYER = 1 << 1;

        /// <summary>
        /// 縦画面/横画面/ダイナミックカメラ再生を数値化
        /// </summary>
        public enum RaceScreenMode
        {
            Portrait = 0,
            Landscape = 1,
            DynamicCamera = 2
        }

        /// <summary> 人気アイコン </summary>
        public const string POPULAR_ICON_NAME = /*"utx_ico_race_memberlist_popularity_{0:D2}"*/"utx_ico_racepopularity_{0:D2}";

        /// <summary>
        /// 人気アイコンの種類。
        /// </summary>
        public enum PopularityMark
        {
            Left = 0, // 左。
            Center, // 中央。
            Right, // 右。
        }
        
        public const int RANK_1ST = 1;
        public const int RANK_2ND = 2;
        public const int RANK_3RD = 3;
        public const int RANK_4TH = 4;
        public const int RANK_5TH = 5;
        public const int RANK_6TH = 6;

        /// <summary>コース距離を区間で区切る時の区間数。</summary>
        public const int COURSE_SECTION_NUM = 24;
        
        /// <summary> 各Phaseにおける区間数 </summary>
        public const int COURSE_SECTION_NUM_START = 4;
        public const int COURSE_SECTION_NUM_MIDDLE = 12;

        /// <summary>
        /// レースの種類。
        /// </summary>
        /// <remarks>
        /// 実況発動条件でcsvから参照しているため、並び替えや削除を行わないこと（未使用になった定義も再利用枠として残しておくこと）。
        /// </remarks>
        public enum RaceType
        {
            None = 0,       // 無効値。
            PvP,            // PVP。
            Tutorial,       // チュートリアル再生用
            Story,          // ストーリーレース（出来レース）
            StoryCondition, // ストーリーを見るための条件レース
            Champions = 5,  // チャンピオンズミーティング。 
            Single,         // シングルレース
            SingleModeScenarioTeamRace = 7, // チーム対抗戦。
            RoomMatch,      // ルームマッチ
            Practice,       // 練習
            Daily,          // デイリーレース
            TeamBuilding,   // チーム作り。
            Legend,         // レジェンドレース
            ChallengeMatch, // チャレンジマッチ
            TeamStadium,    // チーム競技場
#if !CYG_PRODUCT || UNITY_EDITOR
            Debug = 15,
#endif
            Heroes = 16,     // リーグ・オブ・ヒーローズ
            Ultimate = 17,   // マスターズチャレンジ
            RatingRace = 18, // マンスリーマッチ
        };

        /// <summary>
        /// Raceマスタにおけるレース種別
        /// </summary>
        public enum RaceGroup
        {
            None = 0,           // 無効値
            Common = 1,         // 通常レース
            Daily = 2,          // 曜日レース。
            TrainingPractice = 3,// 育成練習レース
            StoryCondition = 4, // ストーリーアンロックレース。
            TeamBuilding = 5,   // チーム作り。
            Practice = 6,       // 練習。
            Single = 7,         // シングルレース。
            Legend = 8,         // レジェンドレース
            TeamStadium = 9,    // チーム競技場。
            SingleModeScenarioTeamRace = 10, // チーム対抗戦。
            Champions = 11,     // チャンピオンズミーティング
            RoomMatch = 12,     // ルームマッチ
            ChallengeMatch = 13,// チャレンジマッチ
            Heroes = 14,       // リーグ・オブ・ヒーローズ
            Ultimate = 15,      // マスターズチャレンジ
            RatingRace = 16,    // マンスリーマッチ
            CustomG1 = 61,      // 模擬レースで表示するG1レース
        }

        /// <summary>
        /// 距離区分。
        /// </summary>
        public enum CourseDistanceType
        {
            Short = 1,  // 短距離。
            Mile,       // マイル。
            Middle,     // 中距離。
            Long,       // 長距離。
        };

        /// <summary>
        /// チームレース種別
        /// </summary>
        public enum TeamRaceNumberType
        {
            Short = 1,  // 短距離。
            Mile,       // マイル。
            Middle,     // 中距離。
            Long,       // 長距離。
            Dirt
        }

        /// <summary>
        /// 上限突破パラメータスタミナの目指す速度計算で使用するランダムな揺らぎ幅テーブルのタイプ
        /// </summary>
        public enum RandomTableType
        {
            Normal,
            Lower,
            Higher,
        }

        /// <summary>
        /// レースの遷移状態
        /// </summary>
        public enum RaceState
        {
            Null = -1,              // 無効値。
            Init,                   // レース初期化。ロード待ち。
            MemberList,             // 出走リスト画面。
            GateIn,                 // ファンファーレ&ゲートイン画面。
            WaitStartDash,          // ゲートオープン後、出走待ち。
            Race,                   // レース。
            OverRun,                // オーバーラン。
            OverRunResult,          // オーバーランしつつ電光掲示板表示。
            RaceEndCutInInit,       // レース後カットインの準備(オーバーランカットイン、リザルトカットイン前の準備)
            OverRunCutIn,    　　    // オーバーランとリザルトカットインの間に再生されるカットイン(伝説編のCMカットインで使用)のカット再生
            WinningCircle,          // ウィニングサークルで順位ポーズ。
            End,                    // レース終了。
        };

        /// <summary>
        /// レースに参加するNPCの種類。
        /// </summary>
        /// <remarks>「04_NpcEditor.xlsx」「race_npc」シート「npc_type」カラムで参照するので変更する場合はcsvも同期させること。</remarks>
        public enum RaceNpcType
        {
            Normal = 0,
            Boss = 3,

            User = 11,  // 自ユーザーのウマ馬。
            UserOther = 20, // 他ユーザーのウマ娘。 
            Ghost = 21, // シングル模擬レースの練習パートナー。
            Follow = 22, // シングル模擬レースのフォローウマ娘。
        }

        /// <summary>
        /// レースのグレード。
        /// </summary>
        public enum Grade
        {
            None = 0,
            // ※100間隔で定義していますが、追加仕様次第では150など間を刻むこともあり得る。
            Grade1 = 100, // グレード高い。
            Grade2 = 200,
            Grade3 = 300,
            Grade4 = 400,
            Grade5 = 500,
            Grade6 = 600,
            Grade7 = 700,
            Grade8 = 800,
            Grade9 = 900, // グレード低い。
            SpecialGrade = 1000,

            G1 = Grade1,            // GⅠ。
            G2 = Grade2,            // GⅡ。
            G3 = Grade3,            // GⅢ。
            Open = Grade4,          // オープン。
            U_1600 = Grade5,        // Under1600。
            U_1000 = Grade6,        // Under1000。
            PreOpen = Grade7,       // プレオープン。
            NoWin = Grade8,         // 未勝利戦。
            NewHorses = Grade9,     // 新馬戦。

            Legend = Grade1,        // レジェンド。
            Orion = Grade2,         // オリオン。
            Cassiopeia = Grade3,    // カシオペア。
            Aries = Grade4,         // アリエス。
            Varugo = Grade5,        // ヴァルゴ。
            Consolation = Grade8,   // 敗者復活。
            Beginner = Grade9,      // ビギナー。
            GUR = SpecialGrade,     // ヴィーナス編グロウアップレース
            ArcRace = SpecialGrade, // 凱旋門賞編代表交流戦

            Min = Grade9,  //一番低いグレード定義
        }

        /// <summary>
        /// 難易度
        /// </summary>
        public enum Difficulty
        {
            // レーシングカーニバル
            Easy = 1,
            Normal = 2,
            Hard = 3,
            VeryHard = 4,
            Extreme = 5,
            
            // マンスリーマッチ
            // RatingRaceDefineはこちらを参照している
            Class1      = 100,
            Class2      = 200,
            Class3      = 300,
            Class4      = 400,
            Class5      = 500,
            Class6      = 600,
            Class7      = 700,
            Class8      = 800,
            Class9      = 900,
            Class10     = 1000,
        }

        /// <summary>
        /// 競馬場の直線の正面/向こう正面種類。
        /// </summary>
        public enum StraightFrontType
        {
            Null = 0,
            Front,          // 観客側。
            AcrossFront,    // 向こう正面。
            FalseStraight,  // フォルスストレート(ロンシャンレース場などで使用)
        }

        /// <summary>
        /// 競馬場の馬場種類。
        /// </summary>
        public enum GroundType
        {
            Turf = 1,   // 芝。
            Dirt,       // ダート。

            Max = Dirt,
        }

        /// <summary>
        /// 競馬場の馬場状態。
        /// </summary>
        public enum GroundCondition
        {
            None = 0,   
            Good = 1,   // 良。
            Soft,       // 稍重。
            Hard,       // 重。
            Bad,        // 不良。
        }

        /// <summary>
        /// 天気。
        /// </summary>
        public enum Weather
        {
            None = 0,
            Sunny = 1,  // 晴れ。
            Cloudy = 2,     // 曇り。
            Rainy = 3,      // 雨。
            Snow = 4,       // 雪。
            Max,        // 最大値：追加する時は上に
            Min = None,
            Random = None,
        }

        /// <summary>
        /// 時間帯。
        /// </summary>
        public enum Time
        {
            None = 0,
            Morning = 1,    //朝
            Daytime = 2,        //昼
            Evening = 3,        //夕方
            Night = 4,          //夜
            Max,            //最大値：追加する時は上に
            Min = None,
        }

        /// <summary>
        /// 競馬場の内回り/外回り。
        /// </summary>
        public enum CourseAround
        {
            None = 1,   // 無し。※無効値ではなく、内回りも外回りも定義されないコースもある。
            Inner,      // 内回り。
            Outer,      // 外回り。
            OuterToInner,// 外回り→内回り。
        }

        /// <summary>
        /// 競馬場の右回り/左回り。
        /// </summary>
        public enum Rotation
        {
            Right = 1,  // 右回り。
            Left,       // 左回り。
            
            StraightRight, // 直線（右向き）。
            StraightLeft,  // 直線（左向き）。
        }

        /// <summary>
        /// 競馬場の観客の入り。
        /// </summary>
        /// <remarks>
        /// リソースパスに使用しているので、変更する際は要注意
        /// </remarks>
        public enum Audience
        {
            VeryFew = 90,   // ガラガラ以下
            Few = 1,        // がらがら
            Normal = 2,     // 通常
            SoldOut = 3,    // 満員
        }

        /// <summary>
        /// 競馬場のパーツカテゴリ
        /// </summary>
        public enum Category
        {
            Course = 0,     //0 競馬場
            Gate,           //1 ゲート
            TurfGoal,       //2 ターフ用ゴール
            DirtGoal,       //3 ダート用ゴール
            TurfGoalFlower, //4 ターフ用ゴール前花
            DirtGoalFlower, //5 ダート用ゴール前花
            Tree,           //6 樹木
            Audience,       //7 観客
            Flag,           //8 旗
            Sky,            //9 空
            Car,            //10 車
            ResultPanel,    //11 表彰台
        }

        /// <summary>
        /// 競馬場のゴールゲートの種類。
        /// </summary>
        public enum GoalGateType
        {
            Common,
        }

        /// <summary>
        /// キャラの走法。前の走法ほど低い値で定義する。
        /// </summary>
        public enum RunningStyle : byte
        {
            None = 0,
            Nige = 1,       // 逃げ。
            Senko,          // 先行。
            Sashi,          // 差し。
            Oikomi,         // 追い。
        }

        /// <summary>
        /// 特殊走法。
        /// </summary>
        public enum RunningStyleEx
        {
            None = 0,
            Oonige = 1,     // 大逃げ。
        }

        /// <summary>
        /// 特性経験値のカテゴリ
        /// サーバと合わせること
        /// </summary>
        public enum ProperExpCategory
        {
            None,
            Ground,
            RunningStyle,
            Distance
        }

        /// <summary>
        /// 適性のランク。
        /// </summary>
        public enum ProperGrade
        {
            // 一致している前提でキャストするため
            // 定義を変更する時は GameDefine.ParameterRank も修正すること

            Null = 0,
            G,
            F,
            E,
            D,
            C,
            B,
            A,
            S,
        }

        /// <summary>
        /// 適性の種類。
        /// </summary>
        public enum ProperType
        {
            Distance = 1, //距離適性。
            Ground, // 馬場適正。
            RunningStyle, // 走法適正。
        }
        
        /// <summary>
        /// キャラの敗因。
        /// </summary>
        public enum DefeatType
        {
            Null = 0,                   // 無効値。
            Win = 1,                    // 勝利。
            Lose,                       // 惜敗。
            RunningStyleMany,           // 自分と同じ走法のキャラ数が一定数以上いた。
            Temptaion,                  // HP0になった かつ 掛かった。
            GutsOrder,                  // HP0になった かつ 根性の順位が一定以下。
            Stamina,                    // HP0になった かつ スタミナが足りない。
            LastSpurtFalse,             // ラストスパートに失敗。
            LastSpurtTargetSpeedDec,    // ラストスパートに成功したが、狙いたい速度を落とした。
            PassiveSkillNum,             // 絆スキルの所持数が少ない。
            BlockFrontTime,             // 前方ブロックを一定時間されていた。
            Speed,                      // スピードが足りない。
            ProperDistance,             // 距離適性が一定値未満。
            ProperGround,               // 馬場適性が一定値未満。
            Motivation,                 // やる気。
        }

        /// <summary>
        /// キャラの着差。
        /// </summary>
        /// <remarks>
        /// csvで値を参照されているので並びを変えないこと。
        /// </remarks>
        public enum HorseLength
        {
            Nose,                   // ハナ差
            Head,                   // アタマ差
            Neck,                   // クビ差
            Half,                   // 1/2馬身
            ThreeQuarters,          // 3/4馬身
            One,                    // 1馬身
            OneAndOneQuarters,      // 1 1/4馬身
            OneAndHalf,             // 1 1/2馬身
            OneAndThreeQuarters,    // 1 3/4馬身
            Two,                    // 2馬身
            TwoAndHalf,             // 2 1/2馬身 ...10
            Three,                  // 3馬身
            ThreeAndHalf,           // 3 1/2馬身
            Four,                   // 4馬身
            Five,                   // 5馬身
            Six,                    // 6馬身
            Seven,                  // 7馬身
            Eight,                  // 8馬身
            Nine,                   // 9馬身
            Ten,                    // 10馬身
            OverTen,                // 10馬身以上 ...20
        }


        /// <summary>
        /// コースパスの通常レース/ストーリーレースの分類。ファイル名構築に使用する。
        /// </summary>
        public enum CoursePathType
        {
            NormalRace = 0,
            StoryRace,
        }

        // 着差判定用の距離定義。
        public const float HORSE_LENGTH_DISTANCE_ONE = 2.5f; // １馬身。これが基準。

        // 距離定義。※着差の馬身判定には使用しないこと。
        public const float HorseLength_One = 1.0f;
        public const float HorseLength_Three = 3.0f;

        // Lane定義
        public const float HorseDistance2LaneCoef = 18.0f;
        public const float HorseLane2DistanceCoef = 1f / HorseDistance2LaneCoef;
        public const float HorseLane_Quarters = 0.25f * HorseLane2DistanceCoef;
        public const float HorseLane_Harf = 0.5f * HorseLane2DistanceCoef;
        public const float HorseLane_ThreeQuarters = 0.75f * HorseLane2DistanceCoef;
        public const float HorseLane_One = 1.0f * HorseLane2DistanceCoef;
        public const float HorseLane_OneAndOneQuarters = 1.25f * HorseLane2DistanceCoef;
        public const float HorseLane_OneAndHarf = 1.5f * HorseLane2DistanceCoef;
        public const float HorseLane_OneAndThreeQuarters = 1.75f * HorseLane2DistanceCoef;
        public const float HorseLane_Two = 2.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Three = 3.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Four = 4.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Five = 5.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Six = 6.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Seven = 7.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Eight = 8.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Nine = 9.0f * HorseLane2DistanceCoef;
        public const float HorseLane_Ten = 10.0f * HorseLane2DistanceCoef;

        // 出走馬数最大値。
        public const int RACE_HORSE_MAX = 20;

        /// <summary>
        /// ストーリーレースでの最大出走人数
        /// ストーリーレースでも単純に最大出走人数を20人にするとインデックスがずれたりして大変なことになるので別途定数定義
        /// </summary>
        public const int STORY_RACE_HORSE_MAX = 18;

        //競馬場のスケール値
        public const float COURSE_SCALE = 0.625f;
        public const float COURSE_WIDTH = COURSE_SCALE * 18.0f; // [m] 全レーンの合計幅。LaneDistancを3D座標に変換するための係数。

        // １ハロンの長さ
        public const float ONE_FURONG_DISTANCE = 200.0f;
        public const float ONE_FURONG_DISTANCE_INV = 1.0f / ONE_FURONG_DISTANCE;

        // 枠番最小値最大値。
        public const int POST_NUMBER_MIN = 1;
        public const int POST_NUMBER_MAX = 8;

        /// <summary>
        /// レース中のBGM制御モード。
        /// </summary>
        public enum BGMControlMode
        {
            Null,       // 無効値。
            Normal,     // 通常（終盤から別BGM再生）。
        }

        /// <summary>
        /// レース中のBGMを決定する定義
        /// </summary>
        /// <remarks>
        /// MasterRaceBgm.ConditionType と連動しています。
        /// レースコンテンツ内でイベントごとの状態によって再生したいBGMを変えたい場合はこちらに定義を追加して制御してください
        /// </remarks>
        public enum RaceBGMConditionType
        {
            Null = -1,                // 無効値
            // リーグオブヒーローズ予約分（1~10）
            Heroes1stStage = 1,       // リーグオブヒーローズ1stステージ(メインステージ)レースBGM
            HeroesFinalStage = 10,    // リーグオブヒーローズファイナルステージ(エクストラステージ)レースBGM
            // マンスリーマッチ予約分（11~20）
            RatingRace = 11,           // マンスリーマッチ（レギュラー）レースBGM
            RatingRaceFinale = 20,    // マンスリーマッチ（フィナーレ）レースBGM
            TeamBuildingScoutRace = 21,    // 最強チーム(スカウトレース)レースBGM
            TeamBuildingSpecialRace = 30,  // 最強チーム(スペシャルレース)レースBGM
        }

        /// <summary>
        /// 馬郡SE種類
        /// </summary>
        public enum HorseGroupSE
        {
            Begin,
            MidLeft = Begin,
            MidRight,
            FarLeft,
            FarRight,
            Max,
        }


        /// <summary>
        /// 坂の種類。
        /// </summary>
        public enum SlopeType
        {
            Null,           // 坂ではない。
            Up,             // 上り坂。
            Down,           // 下り坂。
        }

        //クオリティ
        public enum QualityType
        {
            Quality3D_Light = 0,
            Quality3D,
            Quality3D_Rich,

            Quality_Unknown = -1,
        }

        /// <summary>
        /// カットイン再生モード。
        /// </summary>
        public enum CutInPlayMode
        {
            LongOnceADay, // １日１回フル再生。
            Long, // フル再生。
            Short, // 短縮版。
        }

        /// <summary>
        /// やる気
        /// </summary>
        public enum Motivation
        {
            None = 0,//無し（無効番）
            Min = 1,     // 絶不調
            Low,         // 不調
            Middle,      // 普通
            High,        // 好調
            Max,    // 絶好調
        }
        
        /// <summary>
        /// シナリオごとに保存される追加データ
        /// </summary>
        public enum ScenarioAdditionalDataType
        {
            ArcPotentialTotalLevel = 6001,      // 凱旋門賞編：合計海外適性Lv
            SportFinalCompeWinCount = 7001,     // アスリート編：決勝大会の勝利種目数
            CookCookingPt = 8001,               // お料理編：合計お料理Pt
            MechaResearchTotalLevel = 9001,     // メカ編：合計研究Lv
            LegendMasterlyBonus = 10001,        // 伝説編：獲得済みマスタリボーナス
        }

        /// <summary>
        /// 機能時限解放のビットフラグ。
        /// </summary>
        public enum UnlockFlag : uint
        {
            Status = 1 << 0,                // パラメータ上限突破後の減衰対応
            ConservePower = 1 << 1,         // 【1.27.0】 足溜め機能
            StaminaLimitBreakBuff = 1 << 2, // 【1.33.0】 スタミナ上限突破追加効果
            RemoveStatusMaxLimit = 1 << 3,  // 【2.4.0】 ステータス上限2000打ち切りの処理撤廃
            LogicUpdate_20805 = 1 << 4,     // 【2.8.5】 レースロジック改修(PDM仕様調整、スタミナ勝負仕様調整、スタミナ勝負係数調整)
        #if !CYG_PRODUCT
            // RaceDirect用に全て解放状態にする
            AllUnlock = Status |
                        ConservePower |
                        StaminaLimitBreakBuff |
                        RemoveStatusMaxLimit |
                        LogicUpdate_20805,
        #endif
        }

        /// <summary>
        /// ポジション維持機能のモード。
        /// </summary>
        public enum PositionKeepMode
        {
            Null,           // 無効値。
            SpeedUp,        // 加速。
            Overtake,       // 同じ走法内で１位を狙う。
            PaseUp,         // ペースアップ。
            PaseDown,       // ペースダウン。
            PaseUpEx,       // 自分より前にいる、後ろの走法のキャラを抜かすためのペースアップ。
        }

        /// <summary>
        /// イベントカメラの再生用途。
        /// </summary>
        public enum EventCameraCategory
        {
            Jikkyo = 1,           // 実況
            SkillCutIn,           // SSRスキルカットイン前
            Temp01,               // 空き番(3番を使う予定があったという情報を貰った為)
            ChampionsGoalCapture, // チャンミ月刊トゥインクル号外(ゴールキャプチャ)
            DynamicCamera,        // ダイナミックカメラ(ジョッキーカメラ)
            Commercial,           // 伝説編: CMレース
            PosterGoalCapture,    // 伝説編: ポスター演出(ゴールキャプチャ)
        }

        /// <summary>
        /// 自分と、一定距離内にいる他キャラとの相対位置。
        /// </summary>
        public enum HorseRelativePos
        {
            Null, // 一定距離内にいない。
            Left, // 自分から見て左にいる。
            Right, // 自分から見て右にいる。
        }

        /// <summary>
        /// ターフビジョンにレース映像以外で何を映すか。
        /// </summary>
        /// <remarks>
        /// レースタイトル中・ゲートイン中・オーバーラン以降でターフビジョンに何を映すかの定義。
        /// この値はrace_track.csvで入力しているため合わせること。
        /// </remarks>
        public enum TurfVisionType
        {
            URA = 1, // GIならGIレースロゴ。GI以外ならURAロゴ。
            NAU    , // GIならGIレースロゴ。GI以外ならNAUロゴ。
            Stand  , // GIならGIレースロゴ。GI以外なら競馬場の観客スタンド。
            FC     , // G1ならG1レースロゴ。G1以外ならFCロゴ。(ロンシャンレース場)
            SAP    , // G1ならG1レースロゴ。G1以外ならSAPロゴ。(サンタアニタパーク場)
        }

        /// <summary>
        /// 電光掲示板の馬場状態表示タイプ。
        /// </summary>
        public enum ResultBoardConditionType
        {
            Turf_None = 1,  // 左に芝、右は無し。
            Turf_Dirt,      // 左に芝、右にダート。
            Dirt_None,      // 左にダート、右は無し。
            Dirt_Turf,      // 左にダート、右に芝。
        }

        /// <summary>
        /// イベントの種類
        /// </summary>
        public enum EventType
        {
            None = 0,
            LegendRace,
        }

        
        /// <summary>
        /// race_track.csvで指定する足煙エフェクトの色指定。
        /// </summary>
        public enum RaceTrackFootSmokeColorType
        {
            LightMap = 1, // ライトマップの色。
            LightProbeOnlyNight, // 時間帯が夜の場合のみライトプローブの色（それ以外の時間帯はライトマップの色）。
        }


        /// <summary>
        /// SEの発音優先度。
        /// </summary>
        public enum SEPriority
        {
            ResidentCrowd = 1,
            GroupFoot = 2,
            HighFoot = 3,
            CourseEnvironment = 4,
            EventCrowd = 5,
            StoryRaceVoice = 6,
            CourseGateOpen = 7,
            StoryRaceTimeline = 8,
            Score = 9,
            SkillCutIn = 10,
            MessagePlate = 11,
            Skill = 13,
            Foot = 15,
            Cloth = 20,
        }
        
        /// <summary>
        /// キャラカラーの指定。
        /// </summary>
        public enum CharaColorType
        {
            Null,
            Black,
        }

        /// <summary>競り合いグループ無効値。</summary>
        public const int COMPETE_GROUP_NULL = -1;

        /// <summary>
        /// アンロックレース最終章特殊レースでmain_story_race_chara_dataからのスキルカットイン再生指定。（自キャラ以外のスキルカットインを再生できるようにするための指定）
        /// </summary>
        public enum MainStoryShowSkillType
        {
            Null,               // 再生しない。
            Show,               // 再生する。
            ShowGimmickMatch,   // ギミックの条件合致しているとき再生する。
            ShowGimmickUnmatch, // ギミックの条件非合致しているとき再生する。
        }

        /// <summary>
        /// ラストスパート計算の評価値。値が大きいほど良い計算結果。
        /// </summary>
        public enum LastSpurtEvaluateValue
        {
            False = 0,
            True,
            TrueExceedNeedMaxHp,

            Max = TrueExceedNeedMaxHp,
        }
        
        /// <summary>
        /// item_data.csvのeffect_type_*カラムの値。プログラムから参照する場合はここに定義する。
        /// </summary>
        /// <remarks>
        /// Gallop.RaceDefine.ItemEffectTypeと同じ値にする。
        /// </remarks>
        public enum ItemEffectType
        {
            StartDashSuccess = 101, // スタートダッシュ必ず成功。
            ScoreBonus = 102, // スコアボーナス。
            DebuffCancel = 103, // デバフ無効化。
        }
        
        /// <summary>
        /// 季節。Gallop.GameDefine.BgSeasonと一致させる。
        /// </summary>
        public enum Season
        {
            None = 0,
            Spring = 1,
            Summer = 2,
            Fall = 3,
            Winter = 4,
            CherryBlossom = 5,  //桜
            Max,            //最大値：追加する時は上に
            Min = None,
        }
        
        /// <summary>
        /// 溜めポイント増加モード
        /// </summary>
        public enum IncreaseConservePowerMode
        {
            // 加算モードでない
            None,

            // ポジション維持のペースダウンが適用中
            PositionKeepPaceDown,

            // ポジション維持が発動していない
            NotActivatePositionKeep,
        }

        /// <summary>
        /// 溜めポイント減少モード
        /// </summary>
        public enum DecreaseConservePowerMode
        {
            // 減算モードでない
            None,

            // スキルによる速度上昇だったがオミット
            NOUSE_1,

            // レーン移動スキルによる速度上昇だったがオミット
            NOUSE_2,

            // 位置取り争い中
            CompeteTop,

            // 掛かり中
            Temptation,
        }
        
        public enum LaneDirection
        {
            In,
            Out,
        }

        public enum LaneType
        {
            Uti,    // 内
            Naka,   // 中
            Soto,   // 外
            Oosoto, // 大外
        }
        
        /// <summary>
        /// レース中の展開。
        /// </summary>
        public enum HorsePhase
        {
            Start,      // 序盤。
            MiddleRun,  // 中盤。
            End,        // 終盤。
            LastSpurt,  // ラスト。
            Finished,   // ゴール後。
        }
        
        public static readonly RunningStyle[] HORSE_USABLE_RUNNING_STYLE_ARRAY = new RunningStyle[]
        {
            RunningStyle.Nige,
            RunningStyle.Senko,
            RunningStyle.Sashi,
            RunningStyle.Oikomi,
        };

        public const int FINISH_ORDER_NULL = -1;
        public const float FINISH_TIME_NULL = -1;
        public const float FINISH_DIFF_TIME_NULL = 0;
        public const int CUR_ORDER_DEFAULT = 0; // 出走前は全員1位扱い。
        
        /// <summary>基礎ステータスの補正後（スキルやアイテム）の値の最小最大。</summary>
        public const int STATUS_MIN = 1;
        /// <summary>基礎ステータスの補正後（スキルやアイテム）の値の最小最大。</summary>
        /// <remarks> UnlockFlag.RemoveStatusMaxLimitを削除する時にこれも消す </remarks>
        public const int STATUS_MAX = 2000;
        
        public const float MAX_SPEED = 30.0f;
        public const float LANE_MOVE_SPEED_MAX = 0.6f;
        public const float LANE_MOVE_SPEED_MIN = 0.0f;
        
        public const float OVERTAKE_END_DISTANCEGAP = RaceDefine.HorseLength_One;

        public static readonly float[] HORSE_LENGTH_DISTANCE = new float[]
        {
            HORSE_LENGTH_DISTANCE_ONE * 0.08f,    // HorseLength.Nose,                     ハナ差 ※1馬身250cm時に20cm
            HORSE_LENGTH_DISTANCE_ONE * 0.16f,    // HorseLength.Head,                     アタマ差 ※1馬身250cm時に40cm
            HORSE_LENGTH_DISTANCE_ONE * 0.32f,    // HorseLength.Neck,                     クビ差 ※1馬身250cm時に80cm
            HORSE_LENGTH_DISTANCE_ONE * 0.5f,     // HorseLength.Half,                     1/2馬身
            HORSE_LENGTH_DISTANCE_ONE * 0.75f,    // HorseLength.ThreeQuarters,            3/4馬身
            HORSE_LENGTH_DISTANCE_ONE,            // HorseLength.One,                      １馬身。これが基準
            HORSE_LENGTH_DISTANCE_ONE * 1.25f,    // HorseLength.OneAndOneQuarters,        1 1/4馬身
            HORSE_LENGTH_DISTANCE_ONE * 1.5f,     // HorseLength.OneAndHalf,               1 1/2馬身
            HORSE_LENGTH_DISTANCE_ONE * 1.75f,    // HorseLength.OneAndThreeQuarters,      1 3/4馬身
            HORSE_LENGTH_DISTANCE_ONE * 2.0f,     // HorseLength.Two,                      2馬身
            HORSE_LENGTH_DISTANCE_ONE * 2.5f,     // HorseLength.TwoAndHalf,               2 1/2馬身
            HORSE_LENGTH_DISTANCE_ONE * 3.0f,     // HorseLength.Three,                    3馬身
            HORSE_LENGTH_DISTANCE_ONE * 3.5f,     // HorseLength.ThreeAndHalf,             3 1/2馬身
            HORSE_LENGTH_DISTANCE_ONE * 4.0f,     // HorseLength.Four,                     4馬身
            HORSE_LENGTH_DISTANCE_ONE * 5.0f,     // HorseLength.Five,                     5馬身
            HORSE_LENGTH_DISTANCE_ONE * 6.0f,     // HorseLength.Six,                      6馬身
            HORSE_LENGTH_DISTANCE_ONE * 7.0f,     // HorseLength.Seven,                    7馬身
            HORSE_LENGTH_DISTANCE_ONE * 8.0f,     // HorseLength.Eight,                    8馬身
            HORSE_LENGTH_DISTANCE_ONE * 9.0f,     // HorseLength.Nine,                     9馬身
            HORSE_LENGTH_DISTANCE_ONE * 10.0f,    // HorseLength.Ten,                      10馬身
        };
        
        // バ身（10000倍した整数値）
        public static readonly int[] HORSE_LENGTH_BASHIN = new int[]
        {
            800,       // HorseLength.Nose,                     ハナ差 ※1馬身250cm時に20cm
            1600,      // HorseLength.Head,                     アタマ差 ※1馬身250cm時に40cm
            3200,      // HorseLength.Neck,                     クビ差 ※1馬身250cm時に80cm
            5000,      // HorseLength.Half,                     1/2馬身
            7500,      // HorseLength.ThreeQuarters,            3/4馬身
            10000,     // HorseLength.One,                      １馬身。これが基準
            12500,     // HorseLength.OneAndOneQuarters,        1 1/4馬身
            15000,     // HorseLength.OneAndHalf,               1 1/2馬身
            17500,     // HorseLength.OneAndThreeQuarters,      1 3/4馬身
            20000,     // HorseLength.Two,                      2馬身
            25000,     // HorseLength.TwoAndHalf,               2 1/2馬身
            30000,     // HorseLength.Three,                    3馬身
            35000,     // HorseLength.ThreeAndHalf,             3 1/2馬身
            40000,     // HorseLength.Four,                     4馬身
            50000,     // HorseLength.Five,                     5馬身
            60000,     // HorseLength.Six,                      6馬身
            70000,     // HorseLength.Seven,                    7馬身
            80000,     // HorseLength.Eight,                    8馬身
            90000,     // HorseLength.Nine,                     9馬身
            100000,    // HorseLength.Ten,                      10馬身
        };
        
        /// <summary>
        /// 馬場状態によるパラメータ補正。
        /// </summary>
        [System.Serializable]
        public class GroundModifierParam
        {
            public float addSpeed;
            public float addPower;
            public float multiHpSub;

            public GroundModifierParam(float addSpeed, float addPower, float multiHpSub)
            {
                this.addSpeed = addSpeed;
                this.addPower = addPower;
                this.multiHpSub = multiHpSub;
            }

#if STANDALONE_SIMULATOR
            public GroundModifierParam()
            {
            }
#endif
        }

        public static readonly GroundModifierParam GROUND_MODIFIER_PARAM_NULL = new GroundModifierParam(0, 0, 1);

        public const int CORNER_DISTANCE_DEFAULT = 200;
        public const int CORNER_NULL = 0; // コーナーにはいない。
        public const int LAST_CORNER_NUMBER = 4;               // 最終コーナー番号。
        public const float LAST_CORNER_THRESHOLD = 1000.0f;    // 最終コーナーと判断する距離

        /// <summary>エースのメンバーId</summary>
        public const int TEAM_ACE_MEMBER_ID = 1;
        /// <summary>無所属のチームId</summary>
        public const int TEAM_ID_NULL = 0;
        /// <summary>無所属のチームメンバーId</summary>
        public const int TEAM_MEMBER_ID_NULL = 0; 
        
        public const float OVERRUN_SPEED_MIN = 5.0f;    // オーバーラン中の最低速度
        
        /// <summary>
        /// 名前が長くて専用レイアウトが必要になるレース場ID
        /// </summary>
        public static readonly List<int> LONG_NAME_RACE_TRACK_ID_ARRAY = new List<int>
        {
            10202,  // サンタアニタパーク
        };

        /// <summary>
        /// CMレースのイベントカメラのID
        /// </summary>
        public static readonly int[] CM_CAMERA_ID_ARRAY = new int[] { 10, 20, 30 };

        /// <summary>
        /// CMレースのイベントカメラのバリエーション数
        /// </summary>
        public const int CM_CAMERA_VARIATION_COUNT = 3;

        /// <summary>
        /// CMレースのイベントカメラのバリエーション開始Index
        /// </summary>
        public const int CM_CAMERA_VARIATION_START = 0;

        /// <summary>
        /// CM演出のパターン数
        /// </summary>
        public const int CM_VARIATION_COUNT = 5;

        /// <summary>
        /// CM演出のパターンデフォルト値
        /// </summary>
        public const int CM_VARIATION_DEFAULT = 1;

        /// <summary>
        /// ナレーションのパターン数
        /// </summary>
        public const int CM_NARRATION_VARIATION_COUNT = 36;

        /// <summary>
        /// ナレーションのデフォルト値
        /// </summary>
        public const int CM_NARRATION_VARIATION_DEFAULT = 1;

        /// <summary>
        /// CMタイトル画像のデフォルト値
        /// </summary>
        public const int CM_TITLE_DEFAULT = 0;

        /// <summary>
        /// その名は画像のデフォルト値
        /// </summary>
        public const int CM_NAME_DEFAULT = 0;
    }

#region Text
#if GALLOP
    public static class RaceEnumConverter
    {
        public static string Text(this RaceDefine.CourseDistanceType distanceType)
        {
            switch (distanceType)
            {
                case RaceDefine.CourseDistanceType.Short:
                    return TextId.Race0107.Text();
                case RaceDefine.CourseDistanceType.Mile:
                    return TextId.Character0189.Text();
                case RaceDefine.CourseDistanceType.Middle:
                    return TextId.Race0106.Text();
                case RaceDefine.CourseDistanceType.Long:
                    return TextId.Race0105.Text();
            }
            return "";
        }

        public static string Text(this RaceDefine.GroundType groundType)
        {
            switch (groundType)
            {
                case RaceDefine.GroundType.Turf:
                    return TextId.Common0121.Text();
                case RaceDefine.GroundType.Dirt:
                    return TextId.Common0122.Text();
            }

            return "";
        }

        public static string ShortText(this RaceDefine.GroundType groundType)
        {
            switch (groundType)
            {
                case RaceDefine.GroundType.Turf:
                    return TextId.Common0121.Text();
                case RaceDefine.GroundType.Dirt:
                    return TextId.Common0124.Text();
            }

            return "";
        }

        public static string Text(this RaceDefine.GroundCondition condition)
        {
            switch (condition)
            {
                case RaceDefine.GroundCondition.Good:
                    return TextId.Race0186.Text();
                case RaceDefine.GroundCondition.Soft:
                    return TextId.Race0187.Text();
                case RaceDefine.GroundCondition.Hard:
                    return TextId.Race0188.Text();
                case RaceDefine.GroundCondition.Bad:
                    return TextId.Race0189.Text();
            }

            return "";
        }

        public static string Text(this RaceDefine.Weather weather)
        {
            switch (weather)
            {
                case RaceDefine.Weather.None:
                    return TextId.Common0188.Text();
                case RaceDefine.Weather.Sunny:
                    return TextId.Common0117.Text();
                case RaceDefine.Weather.Cloudy:
                    return TextId.Common0118.Text();
                case RaceDefine.Weather.Rainy:
                    return TextId.Common0119.Text();
                case RaceDefine.Weather.Snow:
                    return TextId.Common0120.Text();
            }

            return "";
        }

        public static TextId GetTextId(this RaceDefine.Grade grade)
        {
            switch (grade)
            {
                case RaceDefine.Grade.G1:
                    return TextId.Race0022;
                case RaceDefine.Grade.G2:
                    return TextId.Race0023;
                case RaceDefine.Grade.G3:
                    return TextId.Race0024;
                case RaceDefine.Grade.Open:
                    return TextId.Race0025;
                case RaceDefine.Grade.U_1600:
                    return TextId.Race0026;
                case RaceDefine.Grade.U_1000:
                    return TextId.Race0027;
                case RaceDefine.Grade.PreOpen:
                    return TextId.Race0028;
                case RaceDefine.Grade.NoWin:
                    return TextId.Race0029;
                case RaceDefine.Grade.NewHorses:
                    return TextId.Race0030;
            }
            return TextId.None;
        }

        public static string Text(this RaceDefine.Grade grade)
        {
            return grade.GetTextId().Text();
        }

        public static string Text(this RaceDefine.Audience audience)
        {
            switch (audience)
            {
                case RaceDefine.Audience.Few:
                    return TextId.Race0180.Text();
                case RaceDefine.Audience.Normal:
                    return TextId.Race0181.Text();
                case RaceDefine.Audience.SoldOut:
                    return TextId.Race0182.Text();
            }
            return "";
        }

        public static string ShortText(this RaceDefine.Audience audience)
        {
            switch (audience)
            {
                case RaceDefine.Audience.VeryFew:
                    return TextId.Race9511010.Text();
                case RaceDefine.Audience.Few:
                    return TextId.Race0183.Text();
                case RaceDefine.Audience.Normal:
                    return TextId.Race0184.Text();
                case RaceDefine.Audience.SoldOut:
                    return TextId.Race0185.Text();
            }
            return "";
        }

        public static string Text(this RaceDefine.CourseAround inout, bool isShort = false)
        {
            switch (inout)
            {
                case RaceDefine.CourseAround.Inner:
                    return isShort ? TextId.Race0241.Text() : TextId.Race0190.Text();
                case RaceDefine.CourseAround.Outer:
                    return isShort ? TextId.Race0240.Text() : TextId.Race0191.Text();
                case RaceDefine.CourseAround.OuterToInner:
                    return isShort ? TextId.Race0677.Text() : TextId.Race0676.Text();
            }

            return "";
        }

        public static string Text(this RaceDefine.Rotation rotation, bool isShort = false)
        {
            switch (rotation)
            {
                case RaceDefine.Rotation.Left:
                    return isShort ? TextId.Race0239.Text() : TextId.Race0192.Text();
                case RaceDefine.Rotation.Right:
                    return isShort ? TextId.Race0238.Text() : TextId.Race0193.Text();
                // その他は直線。直線には短縮形は無い。
                default:
                    return TextId.Race0242.Text();
            }
        }

        public static string Text(this RaceDefine.Time time)
        {
            switch (time)
            {
                case RaceDefine.Time.None:
                    return TextId.Common0188.Text();
                case RaceDefine.Time.Morning:
                    return TextId.Common0113.Text();
                case RaceDefine.Time.Daytime:
                    return TextId.Common0114.Text();
                case RaceDefine.Time.Evening:
                    return TextId.Common0115.Text();
                case RaceDefine.Time.Night:
                    return TextId.Common0116.Text();
            }
            return "";
        }

        public static string Text(this RaceDefine.RunningStyle style)
        {
            switch (style)
            {
                case RaceDefine.RunningStyle.Nige:
                    return TextId.Race0054.Text();
                case RaceDefine.RunningStyle.Senko:
                    return TextId.Race0052.Text();
                case RaceDefine.RunningStyle.Sashi:
                    return TextId.Race0051.Text();
                case RaceDefine.RunningStyle.Oikomi:
                    return TextId.Race0049.Text();
            }

            return "";
        }

        public static string Text(this RaceDefine.Motivation motivation)
        {
            switch (motivation)
            {
                case RaceDefine.Motivation.Min:
                    return TextId.Race0630.Text();
                case RaceDefine.Motivation.Low:
                    return TextId.Race0631.Text();
                case RaceDefine.Motivation.Middle:
                    return TextId.Race0632.Text();
                case RaceDefine.Motivation.High:
                    return TextId.Race0633.Text();
                case RaceDefine.Motivation.Max:
                    return TextId.Race0634.Text();
                default:
                    return "";
            }
        }

        public static string Description(this RaceDefine.Motivation motivation)
        {
            switch (motivation)
            {
                case RaceDefine.Motivation.Min:
                    return TextId.Race0602.Text();
                case RaceDefine.Motivation.Low:
                    return TextId.Race0603.Text();
                case RaceDefine.Motivation.Middle:
                    return TextId.Race0604.Text();
                case RaceDefine.Motivation.High:
                    return TextId.Race0605.Text();
                case RaceDefine.Motivation.Max:
                    return TextId.Race0606.Text();
                default:
                    return "";
            }
        }
        
        public static string Text(this RaceDefine.ProperGrade properGrade)
        {
            switch (properGrade)
            {
                case RaceDefine.ProperGrade.G:
                    return TextId.Race0425.Text();
                case RaceDefine.ProperGrade.F:
                    return TextId.Character0294.Text();
                case RaceDefine.ProperGrade.E:
                    return TextId.Race0199.Text();
                case RaceDefine.ProperGrade.D:
                    return TextId.Race0198.Text();
                case RaceDefine.ProperGrade.C:
                    return TextId.Race0197.Text();
                case RaceDefine.ProperGrade.B:
                    return TextId.Character0031.Text();
                case RaceDefine.ProperGrade.A:
                    return TextId.Race0195.Text();
                case RaceDefine.ProperGrade.S:
                    return TextId.Race0194.Text();
                default:
                    return "";
            }
        }
    }
#endif
#endregion
}

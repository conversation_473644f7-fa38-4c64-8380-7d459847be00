using System.Collections;
using System.Collections.Generic;
using System.Linq;
using SkillDefine = Gallop.SkillDefine;

//-------------------------------------------------------------------
// タグ部分のみ分離したコード
// クライアント側のタグ検索に使用する
//-------------------------------------------------------------------
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerBase : ISkillTrigger
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：左辺と右辺の比較演算。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerParamBase : SkillTriggerBase
    {
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：左辺と右辺の比較演算。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerParamBaseFloat : SkillTriggerBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：左辺と右辺の比較演算。
    /// </summary>
    /// <remarks> 入力値をfloatで受け取ります </remarks>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerParamBaseInputFloat : SkillTriggerParamBaseFloat
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：一定距離内でランダムに条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerDistanceBase : SkillTriggerBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：一定距離内でランダムに条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerDistanceSetBase : SkillTriggerBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：常に成立する。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerAlways"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerAlways : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Start;
        }
    }


    #region <レース系>
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース開催月。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerMonth"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerMonth : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レースグレード。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerGrade"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGrade : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コーナー数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCornerCount"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseCornerCount : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コースの右回り/左回り。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCourseRotation"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseRotation : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Track;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：地面素材。race_course_set.csvで指定されている値を参照する。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerGroundType"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGroundType : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            var groundType = (Gallop.RaceDefine.GroundType)valueRh;
            switch (groundType)
            {
            case Gallop.RaceDefine.GroundType.Turf: return SkillDefine.SkillTriggerTag.Turf;
            case Gallop.RaceDefine.GroundType.Dirt: return SkillDefine.SkillTriggerTag.Dirt;
            default:
                Debug.LogWarning($"発動条件のタグが取得できません。groundType={groundType}");
                return SkillDefine.SkillTriggerTag.Null;
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：馬場状態。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerGroundCondition"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGroundCondition : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Track;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：キャラのハロン数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFurlong"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFurlong : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerFurlongRandom : SkillTriggerDistanceBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内のランダムな直線内のさらにランダムな一区間で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerStraightRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Straight;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内のランダムな直線中、条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerWhileInStraightRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Straight;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phaseかつランダムな直線のさらにランダムな一区間で条件を満たす
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseStraightRandom : SkillTriggerDistanceBase
    {
        public static List<SkillDefine.SkillTriggerTag> GetTagList(int valueRh)
        {
            var phase = (Gallop.RaceDefine.HorsePhase)valueRh;

            var tagList = new List<SkillDefine.SkillTriggerTag>();
            tagList.Add(SkillDefine.SkillTriggerTag.Straight);
            
            switch (phase)
            {
                default:
                    Debug.LogWarning($"発動条件のタグが取得できません。phase={phase}");
                    break;
                case Gallop.RaceDefine.HorsePhase.Start:
                    tagList.Add(SkillDefine.SkillTriggerTag.PhaseStart);
                    break;
                case Gallop.RaceDefine.HorsePhase.MiddleRun:
                    tagList.Add(SkillDefine.SkillTriggerTag.PhaseMiddle);
                    break;
                case Gallop.RaceDefine.HorsePhase.End:
                    tagList.Add(SkillDefine.SkillTriggerTag.PhaseEnd);
                    break;
                case Gallop.RaceDefine.HorsePhase.LastSpurt:
                    tagList.Add(SkillDefine.SkillTriggerTag.LastSpurt);
                    break;
            }

            return tagList;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phase後半(残り50%以降)かつランダムな直線のさらにランダムな一区間で条件を満たす
    /// 類似処理のSkillTriggerPhaseStraightRandomを継承
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLatterHalfStraightRandom : SkillTriggerPhaseStraightRandom
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phase前半(残り50%以前)かつランダムな直線のさらにランダムな一区間で条件を満たす
    /// 類似処理のSkillTriggerPhaseStraightRandomを継承
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstHalfStraightRandom : SkillTriggerPhaseStraightRandom
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終直線内のランダムな一区間で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastStraightRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Straight;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終直線に入った次のフレームだけ条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastStraightOneTime : SkillTriggerBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Straight;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終直線にいるとき条件を満たす
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastStraight : SkillTriggerBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Straight;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：直線の正面/向こう正面。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerStraightFrontType : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Straight;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：現在の坂。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSlope : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：坂の種類指定でランダム。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerSlopeRandom : SkillTriggerDistanceBase
    {
    }
    public partial class SkillTriggerUpSlopeRandom : SkillTriggerSlopeRandom
    {
    }
    public partial class SkillTriggerDownSlopeRandom : SkillTriggerSlopeRandom
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：坂の種類指定でランダム（レース後半50%）
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerSlopeRandomLaterHalf : SkillTriggerDistanceBase
    {
    }
    public partial class SkillTriggerUpSlopeRandomLaterHalf : SkillTriggerSlopeRandomLaterHalf
    {
    }
    public partial class SkillTriggerDownSlopeRandomLaterHalf : SkillTriggerSlopeRandomLaterHalf
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：現在のコーナー番号。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCorner : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerCornerRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Corner;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phaseかつランダムなコーナーのさらにランダムな一区間で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseCornerRandom : SkillTriggerDistanceBase
    {
        public static List<SkillDefine.SkillTriggerTag> GetTagList(int valueRh)
        {
            var phase = (Gallop.RaceDefine.HorsePhase)valueRh;

            var tagList = new List<SkillDefine.SkillTriggerTag>();
            tagList.Add(SkillDefine.SkillTriggerTag.Corner);
            switch (phase)
            {
                default:
                    Debug.LogWarning($"発動条件のタグが取得できません。phase={phase}");
                    break;
                case Gallop.RaceDefine.HorsePhase.Start:
                    tagList.Add(SkillDefine.SkillTriggerTag.PhaseStart);
                    break;
                case Gallop.RaceDefine.HorsePhase.MiddleRun:
                    tagList.Add(SkillDefine.SkillTriggerTag.PhaseMiddle);
                    break;
                case Gallop.RaceDefine.HorsePhase.End:
                    tagList.Add(SkillDefine.SkillTriggerTag.PhaseEnd);
                    break;
                case Gallop.RaceDefine.HorsePhase.LastSpurt:
                    tagList.Add(SkillDefine.SkillTriggerTag.LastSpurt);
                    break;
            }

            return tagList;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phase後半(残り50%以降)かつランダムなコーナーのさらにランダムな一区間で条件を満たす。
    /// 類似処理のSkillTriggerPhaseCornerRandomを継承
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLatterHalfCornerRandom : SkillTriggerPhaseCornerRandom
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：全てのコーナーでランダム。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerAllCornerRandom : SkillTriggerDistanceSetBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Corner;
        }
    }
    

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー通過済みかどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFinalCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCorner : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Corner;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Corner;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナーの半分以降。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerLaterHalf : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Corner;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース中盤かつ最終コーナー内のランダム区間
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerRandomPhaseMiddle : SkillTriggerPhaseFinalCornerRandom
    {
        public static List<SkillDefine.SkillTriggerTag> GetTagList()
        {
            return new List<SkillDefine.SkillTriggerTag>()
            {
                SkillDefine.SkillTriggerTag.Corner,
                SkillDefine.SkillTriggerTag.PhaseMiddle
            };
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース終盤かつ最終コーナー内のランダム区間
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerRandomPhaseEnd : SkillTriggerPhaseFinalCornerRandom
    {
        public static List<SkillDefine.SkillTriggerTag> GetTagList()
        {
            return new List<SkillDefine.SkillTriggerTag>()
            {
                SkillDefine.SkillTriggerTag.Corner,
                SkillDefine.SkillTriggerTag.PhaseEnd
            };
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// 特定のフェーズかつ最終コーナー内のランダム区間に発動するスキルの抽象クラス
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerPhaseFinalCornerRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Corner;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：天気。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerWeather"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerWeather : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Weather;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：季節。
    /// </summary>
    /// <see cref="Test.TestSkillTriggerSeason"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSeason : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Season;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レースId。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerRaceId"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceId : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：出走からの経過時間。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerRaceAccumulateTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceAccumulateTime : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：出走からの経過時間が、指定範囲内でランダムに選ばれた１秒の間だけ条件を満たす。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerRaceAccumulateTimeRandom"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceAccumulateTimeRandom : SkillTriggerParamBase
    {
    }
    #endregion <レース系>


    #region <競馬場系>
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離が根幹距離かどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCourseBasisDistance"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseBasisDistance : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Track;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離種別。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCourseDistanceType"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseDistanceType : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            var distanceType = (Gallop.RaceDefine.CourseDistanceType)valueRh;
            switch (distanceType)
            {
            case Gallop.RaceDefine.CourseDistanceType.Short: return SkillDefine.SkillTriggerTag.DistanceShort;
            case Gallop.RaceDefine.CourseDistanceType.Mile: return SkillDefine.SkillTriggerTag.DistanceMile;
            case Gallop.RaceDefine.CourseDistanceType.Middle: return SkillDefine.SkillTriggerTag.DistanceMiddle;
            case Gallop.RaceDefine.CourseDistanceType.Long: return SkillDefine.SkillTriggerTag.DistanceLong;
            default:
                Debug.LogWarning($"発動条件のタグが取得できません。distanceType={distanceType}");
                return SkillDefine.SkillTriggerTag.Null;
            }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseDistance : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceTrackId : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Track;
        }
    }
    
    public partial class SkillTriggerTime : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：交流重賞かどうか
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsDirtGrade : SkillTriggerParamBase
    {
    }
    #endregion <競馬場系>


    #region <馬系>
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：特定のキャラIdが出走している。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerExistCharaId : SkillTriggerParamBase
    {
    }
        
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：速度スキルを発動した回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateSpeedSkillCount : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：回復スキルを発動した回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateHealSkillCount : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }

        
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分と先頭キャラとの距離差。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffTop : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分と先頭キャラとの距離差
    /// </summary>
    /// <remarks> SkillTriggerDistanceDiffTopのfloatバージョン </remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffTopFloat : SkillTriggerParamBaseInputFloat
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の前のキャラとの距離差。※自分が１位の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerDistanceDiffInfront"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffInfront : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の後ろのキャラとの距離差。※自分が最後尾の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerDistanceDiffBehind"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffBehind : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の前のキャラとの馬身差。※自分が１位の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerBashinDiffInfront"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBashinDiffInfront : SkillTriggerParamBaseFloat
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の後ろのキャラとの馬身差。※自分が最後尾の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerBashinDiffBehind"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBashinDiffBehind : SkillTriggerParamBaseFloat
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート中かどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerLastSpurt"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsLastSpurt : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.LastSpurt;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート評価
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastSpurt : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.LastSpurt;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より育成ランクが高いキャラの数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFinalGradeHigherCount"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalGradeHigherCount : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より育成ランクが低いキャラの数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFinalGradeLowerCount"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalGradeLowerCount : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：追い抜き中かどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerOvertake"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertake : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：誰かの追い抜き対象になっているか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertakeTargetContinueTime : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分が追い抜き対象を持っている時に順位が上がっていない継続時間。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertakeTargetHaveNoOrderUpContinueTime : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分が追い抜き対象を持っている時に順位が下がっていない継続時間。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertakeTargetHaveNoOrderDownContinueTime : SkillTriggerParamBase
    {
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レーン種類。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerLaneType"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLaneType : SkillTriggerParamBase
    {
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レーン比率。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLanePer : SkillTriggerParamBase
    {
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：一定時間同じ方向にレーン移動した。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerMoveLane : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定の順位変動があった。順位変動した次のフレームだけ条件を満たす。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderOneTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderOneTime : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定の順位変動があった。一度指定の順位変動が発生したら、条件を満たし続ける。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrder"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrder : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：中盤で他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpPhaseMiddle"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpPhaseMiddle : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：終盤以降で他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpPhaseEndAfter"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpPhaseEndAfter : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コーナーで他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpCorner : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート中に他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpLastSpurt"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpLastSpurt : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー以降に他キャラを追い抜いた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpFinalCornerAfter : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：後半50%以降に他キャラを追い抜いた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpLaterHalf : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：中盤で他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownPhaseMiddle"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownPhaseMiddle : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：終盤以降で他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownPhaseEndAfter"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownPhaseEndAfter : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コーナーで他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownCorner : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// テスト：スキル発動条件：ラストスパート中に他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownLastSpurt"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownLastSpurt : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー以降に他キャラに追い抜かれた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownFinalCornerAfter : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：後半50%以降に他キャラに追い抜かれた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownLaterHalf : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.OrderChange;
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyle : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            var runningStyle = (Gallop.RaceDefine.RunningStyle)valueRh;
            switch (runningStyle)
            {
            case Gallop.RaceDefine.RunningStyle.Nige: return SkillDefine.SkillTriggerTag.RunningStyleNige;
            case Gallop.RaceDefine.RunningStyle.Senko: return SkillDefine.SkillTriggerTag.RunningStyleSenko;
            case Gallop.RaceDefine.RunningStyle.Sashi: return SkillDefine.SkillTriggerTag.RunningStyleSashi;
            case Gallop.RaceDefine.RunningStyle.Oikomi: return SkillDefine.SkillTriggerTag.RunningStyleOikomi;
            default:
                Debug.LogWarning($"発動条件のタグが取得できません。runningStyle={runningStyle}");
                return SkillDefine.SkillTriggerTag.Null;
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の選んだ走法がレース参加者中で最も多い。※同数の場合は発動しない。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsRunningStyleCountMax : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース開始後ｎ秒後から現時点まで、順位が上位ｍ％以内をキープしているかどうか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateInContinue : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース開始後ｎ秒後から現時点まで、順位が上位ｍ％以下をキープしているかどうか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateOutContinue : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrder : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：順位がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRate : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート開始時の順位がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateLastSpurt : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート開始時の順位がｎ。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderLastSpurt : SkillTriggerParamBase
    {
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー終了時の順位がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateFinalCornerEnd : SkillTriggerParamBase
    {
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー終了時の順位がｎ。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderFinalCornerEnd : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：やる気
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerMotivation : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularity : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Popularity;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：人気がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularityRate : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillTriggerPopularity.GetTag();
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerGateNumber : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：馬番がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGateNumberRate : SkillTriggerParamBase
    {
        
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerPostNumber : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.PostNumber;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：枠番がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPostNumberRate : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillTriggerPostNumber.GetTag();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：距離が全体のｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseDistanceRate : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：残り距離。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseRemainDistance : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ViewerIdの振られているキャラの中でトップのキャラの残り距離。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseViewerIdRemainDistance : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：一位のキャラからの距離差が一位と最下位との距離差に比べて何%か。
    /// </summary>
    /// <remarks>
    /// 一位に近いほど0% .. 最下位に近いほど100%。
    /// </remarks>
    /// <seealso cref="Test.SkillTriggerHorseDistanceDiffRate"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseDistanceDiffRate : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhase : SkillTriggerParamBase
    {
        /// <summary>Phase→SkillTriggerTagの変換テーブル</summary>
        private static readonly Dictionary<Gallop.RaceDefine.HorsePhase, SkillDefine.SkillTriggerTag> PHASE_2_TAG_DIC =
            new Dictionary<Gallop.RaceDefine.HorsePhase, SkillDefine.SkillTriggerTag>()
            {
                { Gallop.RaceDefine.HorsePhase.Start, SkillDefine.SkillTriggerTag.PhaseStart },
                { Gallop.RaceDefine.HorsePhase.MiddleRun, SkillDefine.SkillTriggerTag.PhaseMiddle },
                { Gallop.RaceDefine.HorsePhase.End, SkillDefine.SkillTriggerTag.PhaseEnd },
                { Gallop.RaceDefine.HorsePhase.LastSpurt, SkillDefine.SkillTriggerTag.PhaseEnd },
            };
        
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            var phase = (Gallop.RaceDefine.HorsePhase)valueRh;
            if (PHASE_2_TAG_DIC.TryGetValue(phase, out var tag))
            {
                return tag;
            }
            else
            {
                Debug.LogWarning($"発動条件のタグが取得できません。phase={phase}");
                return SkillDefine.SkillTriggerTag.Null;
            }
        }

        //---------------------------------------------------------------
        public static List<SkillDefine.SkillTriggerTag> GetTagList(SkillDefine.SkillTriggerOperator op, int valueRh)
        {
            var retList = new List<SkillDefine.SkillTriggerTag>();
            
            if (op == SkillDefine.SkillTriggerOperator.Equal)
            {
                retList.Add(GetTag(valueRh));
            }
            else if (op == SkillDefine.SkillTriggerOperator.LesserEqual)
            {
                var phase = (Gallop.RaceDefine.HorsePhase)valueRh;
                
                // valueRhで渡されたPhase"以前"のSkillTriggerTagをリストに詰める。
                // phaseがPHASE_2_TAG_DICに見つからなかったらbreakするため無限ループのセーフティは割愛している。
                while (true)
                {
                    if (!PHASE_2_TAG_DIC.TryGetValue(phase, out var tag))
                    {
                        break;
                    }
                    if (!retList.Contains(tag))
                    {
                        retList.Add(tag);
                    }
                    phase--; // "以前"のため--
                }
            }
            else if (op == SkillDefine.SkillTriggerOperator.GreaterEqual)
            {
                var phase = (Gallop.RaceDefine.HorsePhase)valueRh;

                // valueRhで渡されたPhase"以降"のSkillTriggerTagをリストに詰める。
                // phaseがPHASE_2_TAG_DICに見つからなかったらbreakするため無限ループのセーフティは割愛している。
                while (true)
                {
                    if (!PHASE_2_TAG_DIC.TryGetValue(phase, out var tag))
                    {
                        break;
                    }
                    if (!retList.Contains(tag))
                    {
                        retList.Add(tag);
                    }
                    phase++; // "以降"のため++
                }
            }
            else
            {
                Debug.LogError($"SkillTriggerPhase.GetTagList op={op}のタグ取得は定義されていない");
            }

            return retList;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの前半の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstHalf : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            // 引数のPhaseによってタグが変わる。SkillTriggerPhaseと同じタグ取得を使う。
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの前半1/4の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstQuarter : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            // 引数のPhaseによってタグが変わる。SkillTriggerPhaseと同じタグ取得を使う。
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの後半の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLaterHalf : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            // 引数のPhaseによってタグが変わる。SkillTriggerPhaseと同じタグ取得を使う。
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの後半1/4の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLaterQuarter : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            // 引数のPhaseによってタグが変わる。SkillTriggerPhaseと同じタグ取得を使う。
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLaterHalfRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            // 引数のPhaseによってタグが変わる。SkillTriggerPhaseと同じタグ取得を使う。
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの前半で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstHalfRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            // 引数のPhaseによってタグが変わる。SkillTriggerPhaseと同じタグ取得を使う。
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの冒頭1/4で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstQuarterRandom : SkillTriggerDistanceBase
    {
        public static SkillDefine.SkillTriggerTag GetTag(int valueRh)
        {
            // 引数のPhaseによってタグが変わる。SkillTriggerPhaseと同じタグ取得を使う。
            return SkillTriggerPhase.GetTag(valueRh);
        }
    }
    

    //-------------------------------------------------------------------
    public partial class SkillTriggerAllPhaseRandom : SkillTriggerPhaseRandom
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離の指定比率以降のランダム地点で発動。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceRateAfterRandom : SkillTriggerDistanceBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１前のキャラと一定距離内に一定時間いる。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerHorseInfrontNearTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseInfrontNearTime : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１後のキャラと一定距離内に一定時間いる。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerHorseBehindNearTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindNearTime : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１前のキャラと一定距離内かつ一定レーン内に一定時間いる。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseInfrontNearLaneTime : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１後のキャラと一定距離内かつ一定レーン内に一定時間いる。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindNearLaneTime : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１後のキャラと一定距離内かつ一定レーン内に一定時間いる。パラメータセット指定版。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindNearLaneTimeParamSet : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１下のキャラが、自分よりインにいる。
    /// </summary>
    /// <remarks>自分が最後尾にいるなら条件は満たさない。同じレーンにいる場合は条件は満たさない。</remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindIn : SkillTriggerParamBase
    {
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１下のキャラが、自分よりアウトにいる。
    /// </summary>
    /// <remarks>自分が最後尾にいるなら条件は満たさない。同じレーンにいる場合は条件は満たさない。</remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindOut : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerOtherHorseBehind : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlocked : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFront : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedSide : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedAllContinueTime : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFrontContinueTime : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedSideContinueTime : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：前方をHp0で失速中のキャラにブロックされた。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFrontHpEmpty : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：前方をHp0で失速中のキャラに一定時間ブロックされた。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFrontHpEmptyContinueTime : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：走法キャラ数比較。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCount : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分以外の走法キャラ数基底。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCountOtherSelf : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCountSame : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.RunningStyleSame;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分と同じ走法がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCountSameRate : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillTriggerRunningStyleCountSame.GetTag();
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleEqualPopularityOne : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Popularity;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularityTopIconNum : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillTriggerPopularity.GetTag();
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularityIconNum : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillTriggerPopularity.GetTag();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：Phase指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCountPhase : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：レース後半(50%以降)のスキル発動数
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCountLaterHalf : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：レース開始から今までの回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCount : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：レースを通してのチームメンバーのスキル発動回数
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCountAllTeam : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：何らかのスキルを発動した次のフレーム
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsActivateAnySkill : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：回復スキルを発動した次のフレーム
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsActivateHealSkill : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：任意のタググループのスキルを発動した次のフレーム
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateSkillTagGroup : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.SkillActivateCount;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：他のウマ娘が特定の効果を持つメリットスキルを発動させたとき
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOtherCharacterActivateAdvantageSkill : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：このSkillDetailとは別のSkillDetailが発動した時
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateOtherSkillDetail : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptationCount : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より後にいる興奮状態のキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptationBehind : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より前にいる興奮状態のキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptationInfront : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptation : SkillTriggerParamBase
    {
        public static SkillDefine.SkillTriggerTag GetTag()
        {
            return SkillDefine.SkillTriggerTag.Temptation;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定走法で興奮状態のキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleTemptationCount : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：競り合い（叩き合い）発動回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCompeteFightCount : SkillTriggerParamBase
    {
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerGoodStart : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBadStart : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseSpeed : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseStamina : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBasePower : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseGuts : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseWiz : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：HP
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHp : SkillTriggerParamBaseFloat
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：Hpの比率を百分率で判定。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerHpPer"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHpPer : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerHpEmpty : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerHpEmptyOneTime : SkillTriggerBase
    {
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerCurrentSpeed : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：出走者の中での「やる気、1200以上の圧縮、スキル効果の効果を含めたスピードの値」の順位
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSpeedOrder : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：囲まれているか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSurrounded : SkillTriggerParamBase
    {
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearHorseCount : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラが一定数(4)以上継続して存在する継続時間。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearAroundAccumulateTime : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラ数で自分より前にいる数。※同じDistanceの場合はカウントに含まない。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearFrontHorseCount : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラ数で自分より後ろにいる数。※同じDistanceの場合はカウントに含まない。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearBehindHorseCount : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：視野範囲内に馬の数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerVisibleHorse : SkillTriggerParamBase
    {
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：同じスキルを持っているキャラの数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSameSkillHorseCount : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：特定スキルを発動しているか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerUsedParticularSkillId : SkillTriggerBase
    {
    }

    #endregion <馬系>


    #region <その他>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRandomLot : SkillTriggerParamBase
    {
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// 他の条件を満たしているときに一定間隔で抽選する。
    /// </summary>
    /// <remarks>
    /// 「他の条件」と見なされるのは、例えば以下のデータの場合、@で区切られたグループごととなる（→ground_type==1&grade<1の部分）。
    /// "ground_type==1&grade<1&random_lot_other_activate@ground_type==2&grade<1"
    /// </remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRandomLotOtherActivate : SkillTriggerBase
    {
    }
    #endregion <その他>
}

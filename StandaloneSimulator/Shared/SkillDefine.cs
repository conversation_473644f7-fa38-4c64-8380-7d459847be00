using System;
using System.Collections.Generic;

namespace Gallop
{
    public static partial class SkillDefine
    {
        public const string SKILL_TRIGGER_KEYWORD_ALWAYS = "always";
        public const string SKILL_TRIGGER_KEYWORD_RUNNING_STYLE = "running_style";
        public const string SKILL_TRIGGER_KEYWORD_DISTANCE_TYPE = "distance_type";
        public const float SKILL_CONTINUE_TIME_ACCURACY = 10000.0f;

        /// <summary>SkillLevelの最小値</summary>
        public const int SKILL_LEVEL_MIN = 1;

        /// <summary> 永続スキルの効果時間 </summary>
        public const int SKILL_TIME_ALWAYS_ACTIVATE = -1;

        /// <summary>
        /// スキルのレアリティ。
        /// </summary>
        public enum SkillRarity
        {
            Rarity1 = 1,    // ノーマルスキル。
            Rarity2,        // レアスキル。
            Rarity3,        // ★１スキル。
            Rarity4,        // ★３才能開花スキル。
            Rarity5,        // ★３ガチャスキル。
            Rarity6,        // 進化スキル
        }

        /// <summary>
        /// スキル効果の大分類。
        /// </summary>
        public enum SkillCategory
        {
            Speed = 0,    // 速度系。
            Heal,         // 回復系。
            Accel,        // 加速系。
            
            Null = -1,    // カテゴリ無し。
        }

        /// <summary>
        /// スキル更新の優先度
        /// </summary>
        public enum SkillPriority
        {
            Normal = 0,      // 2回目に実行される(基本的にはこれ)
            FirstGroup = 1,  // 1回目に実行される(デバフ無効化スキルなど最優先で発動したいスキルがここ)
        }

        public const int SKILLCATEGORY_NUM = 4; //Nullが-1で末尾に追加しづらいので直接定義する

        /// <summary>
        /// スキルがキャラのどのパラメータに影響を及ぼすかの種類。
        /// </summary>
        /// <remarks>
        /// ※CSV等から参照はされていないが過去にシミュレートされた情報とズレが発生することがあるので定義の途中挿入は禁止!!
        /// </remarks>
        public enum SkillModifierParam
        {
            None = -1,                               // 未定義
            Speed,                                   // スピード。
            Stamina,                                 // スタミナ。
            Power,                                   // 加速力。
            Guts,                                    // 根性。
            Wiz,                                     // 賢さ。

            CurrentSpeed,                            // 現在速度。

            TargetSpeed,                             // 目指す速度。
            LaneMoveSpeed,                           // レーン移動速度。
            TemptationPer,                           // 興奮発生確率。
            PushPer,                                 // 押しのけ発生確率。
            Accel,                                   // 加速度。

            TargetLane,                              // レーン移動先固定。0.0(LaneDistanceMin) ~ 1.0(LaneDistanceMax)

            Hp,                                      // Hp。
            HpDecRate,                               // Hp減少レート。
            TemptationEndTime,                       // 興奮終了時間の強制。
            StartDelayScale,                         // スタート遅延時間倍率。
            ForceOvertakeIn,                         // 回避行動必ずインから。
            ForceOvertakeOut,                        // 回避行動必ずアウトから。
            
            DebuffCancel,                            // デバフ無効化。

            VisibleDistance,                         // 視野範囲。

            RunningStyleExOonige,                    // 特殊走法。
            
            TargetSpeedOnMoveLane,                   // 目指す速度。※レーン移動中のみ加算。
            StartDelayFix,                           // スタート遅延時間固定。

            CurrentSpeedWithNaturalDeceleration,     // 現在速度に加算後自然減速
            ActivateRandomNormalAndRareSkill,        // 所持しているノーマルスキルとレアスキルからランダム発動
            ActivateRandomRareSkill,                 // 所持しているレアスキルからランダム発動

            DebuffAbilityValueMultiply,              // デバフスキル効果値乗算変動
            DebuffAbilityValueMultiplyOtherActivate, // 他者が発動したデバフスキルのみ効果値乗算変動

            ActivateSpecificSkill,                   // レース出走中のキャラのうち特定のスキルを所有しているおキャラがいればそれを強制発動させる
            
            HpRateDemerit,                           // 回復効果減少
        };

        /// <summary>
        /// スキル効果種類。
        /// </summary>
        public enum SkillAbilityType
        {
            None = 0,
            Speed,                                      //  1:スピード：継続。
            Stamina,                                    //  2:スタミナ：継続。
            Power,                                      //  3:加速力：継続。
            Guts,                                       //  4:根性：継続。
            Wiz,                                        //  5:賢さ：継続。
            RunningStyleExOonige,                       //  6:特殊走法：大逃げ。
            HpDecRate,                                  //  7:Hp減少レート：継続。

            VisibleDistance,                            //  8:視野範囲。
            HpRate,                                     //  9:Hpを最大Hpに対する比率で増減。
            StartDash,                                  // 10:スタートダッシュ
            ForceOvertakeIn,                            // 11:回避行動必ずインから。
            ForceOvertakeOut,                           // 12:回避行動必ずアウトから。

            TemptationEndTime,                          // 13:興奮終了時間の強制。
            StartDelayFix,                              // 14:スタート遅延時間固定。
            HpRateDemerit,                              // 15:回復効果減少。
            NOUSE_14,                                   // 16:未使用。
            NOUSE,                                      // 17:未使用。
            NOUSE_3,                                    // 18:未使用。
            NOUSE_21,                                   // 19:未使用。
            NOUSE_8,                                    // 20:未使用。
            CurrentSpeed,                               // 21:現在速度：継続。
            CurrentSpeedWithNaturalDeceleration,        // 22:現在速度に加算後自然減速
            NOUSE_2,                                    // 23:未使用。
            NOUSE_4,                                    // 24:未使用。
            NOUSE_7,                                    // 25:未使用。
            NOUSE_5,                                    // 26:未使用。
            TargetSpeed,                                // 27:狙いたい速度。
            LaneMoveSpeed,                              // 28:レーン移動速度。
            TemptationPer,                              // 29:掛かり発生確率。
            PushPer,                                    // 30:押しのけ発生確率。
            Accel,                                      // 31:加速度。
            AllStatus,                                  // 32:全ステータス
            NOUSE_10,                                   // 33:未使用。
            NOUSE_20,                                   // 34:未使用。
            TargetLane,                                 // 35:レーン移動先固定。0.0(LaneDistanceMin) ~ 1.0(LaneDistanceMax)

            ActivateRandomNormalAndRareSkill,           // 36:所持しているノーマルスキルとレアスキルからランダム発動
            ActivateRandomRareSkill,                    // 37:所持しているレアスキルからランダム発動
            DebuffCancel,                               // 38:デバフ無効
            DebuffAbilityValueMultiply,                 // 39:デバフスキル効果値乗算変動
            DebuffAbilityValueMultiplyOtherActivate,    // 40:他者が発動したデバフスキルのみ効果値乗算変動
            ActivateSpecificSkill,                      // 41:レース出走中のキャラ全員に対して特定のスキルIDのスキルを強制発動させる

            ChallengeMatchBonus_Old       = 501,        // 501:旧チャレンジマッチボーナス。（ポイントボーナス）
            ChallengeMatchBonusStatus     = 502,        // 502:チャレンジマッチボーナス。（基礎能力＋〇％）
            ChallengeMatchBonusMotivation = 503,        // 503:チャレンジマッチボーナス。（絶好調確率）
        }

        /// <summary>
        /// スキル効果時間の適用方法。
        /// </summary>
        public enum SkillAbilityTimeUsage
        {
            None = 0,
            Direct,                                            //  1:効果時間そのまま適用。
            MultiplyDistanceDiffTop,                           //  2:効果時間に【トップとの距離差に応じた係数】を乗算。
            MultiplyRemainHp1,                                 //  3:効果時間に【残りHpに応じた係数】を乗算。
            IncrementOrderUp,                                  //  4:追い抜くたびに効果時間にRaceParamDefineで設定された値を加算
            MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1, //  5:効果時間に【中盤にサイドをブロックされていた最大時間に応じた係数】を乗算
            MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2, //  6:効果時間に【中盤にサイドをブロックされていた最大時間に応じた係数】を乗算(係数違い用)
            MultiplyRemainHp2,                                 //  7:効果時間に【残りHpに応じた係数】を乗算(係数違い用)
        }

        /// <summary>
        /// スキル効果値の適用方法。
        /// </summary>
        public enum SkillAbilityValueUsage
        {
            None = 0,
            Direct,                                             //  1:効果値そのまま適用。
            MultiplySkillNum,                                   //  2:効果値に【スキル所持数】を乗算。
            MultiplyTeamTotalSpeed,                             //  3:効果値に【チームメンバーの「基礎ステータス：スピード」に応じた係数】を乗算。
            MultiplyTeamTotalStamina,                           //  4:効果値に【チームメンバーの「基礎ステータス：スタミナ」に応じた係数】を乗算。
            MultiplyTeamTotalPower,                             //  5:効果値に【チームメンバーの「基礎ステータス：パワー」に応じた係数】を乗算。
            MultiplyTeamTotalGuts,                              //  6:効果値に【チームメンバーの「基礎ステータス：根性」に応じた係数】を乗算。
            MultiplyTeamTotalWiz,                               //  7:効果値に【チームメンバーの「基礎ステータス：賢さ」に応じた係数】を乗算。
            MultiplyRandom1,                                    //  8:効果値にParamDefine登録済みの値からランダムに選んだ値を乗算。
            MultiplyRandom2,                                    //  9:効果値にParamDefine登録済みの値からランダムに選んだ値を乗算。
            MultiplySingleModeWinCount,                         // 10:効果値に【育成レースの勝利数に応じた係数】を乗算。
            MultiplyOrderUpCountCornerPhaseEndAfter,            // 11:効果値に【終盤以降のコーナーで追い抜いた数に応じた係数】を乗算。
            MultiplyFanCount,                                   // 12:効果値に【ファン数に応じた係数】を乗算。
            MultiplyMaximumRawStatus,                           // 13:効果値に「基礎ステータスの中で最大のパラメータ」に応じた係数を乗算
            MultiplyActivateSpecificTagSkillCount,              // 14:効果値に【特定の評価点タグ(ParamDefineで定義)を持つスキルを発動した回数】を乗算。
            MultiplyActivateHealSkillCount,                     // 15:効果値に【回復スキル発動回数】を乗算。
            MultiplyFinalCornerEndOrder,                        // 16:効果値に【最終コーナー通過時の順位に応じた倍率】を乗算。
            MultiplyInvTeamMemberCount,                         // 17:効果値に【チームメンバー数の逆数】を乗算。
            MultiplyBaseWiz,                                    // 18:効果値に【自分の「基礎ステータス：賢さ」に応じた係数】を乗算。
            AddDistanceDiffTop,                                 // 19:効果値に【先頭との距離に応じた係数】を加算。
            MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1,  // 20:効果値に【中盤にサイドをブロックされていた最大時間に応じた係数】を乗算
            MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2,  // 21:効果値に【中盤にサイドをブロックされていた最大時間に応じた係数】を乗算(係数違い用)
            MultiplySpeed1,                                     // 22:効果値に【1200以上の圧縮、やる気、スキル効果の効果を含めたスピードの値に応じた係数】を乗算
            MultiplySpeed2,                                     // 23:効果値に【1200以上の圧縮、やる気、スキル効果の効果を含めたスピードの値に応じた係数】を乗算(係数違い用)
            MultiplyArcGlobalPotentialLevel,                    // 24:効果値に【凱旋門賞編で獲得した海外適性レベルの値に応じた係数】を乗算。
            MultiplyTopLeadAmount,                              // 25:効果値に【レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値に応じた係数】を乗算。
            MultiplySportFinalCompeWinCount,                    // 26:効果値に【アスリート編U.A.F.決勝大会の勝利種目数の値に応じた係数】を乗算。
            MultiplyCookCookingPt,                              // 27:効果値に【お料理編で獲得したお料理Ptに応じた係数】を乗算。
            MultiplyMechaResearchTotalLevel,                    // 28:効果値に【メカ編で獲得した総研究Lvに応じた係数】を乗算。
            MultiplyActivateSpecificSkillAbilityTypeCount,      // 29:効果値に【特定のSkillAbilityTypeを持つスキルを発動した回数】を乗算。
        }

        /// <summary>
        /// スキル効果値の適用方法。
        /// </summary>
        public enum SkillAbilityAdditionalActivateType
        {
            None = 0,
            AdditionalActivateOrderUp,              // 1:追い抜くたびにスキル効果適用
            AdditionalActivateActivateAnySkill1,    // 2:スキルを発動する度スキル効果適用
            AdditionalActivateActivateAnySkill2,    // 3:スキルを発動する度スキル効果適用
        }

        /// <summary>
        /// スキル効果値へのレベル補正適用方法。
        /// </summary>
        public enum SkillAbilityValueLevelUsage
        {
            None = 0,
            Normal,     // レベル補正通常適用。
            Ignore,     // レベル補正無視。
            Inverse,    // レベル補正の逆数を適用。
        }

        /// <summary>
        /// スキル対象種類。
        /// </summary>
        /// <remarks>
        /// 注意：SkillEditor.xlsmで参照しているため、並びを変えないこと！
        /// </remarks>
        public enum SkillTargetType
        {
            Self = 1,                         //  1:スキル発動者自身。
            All,                              //  2:全員。
            AllOtherSelf,                     //  3:スキル発動者を除く全員。
            Visible,                          //  4:視野範囲内で近いキャラｎ人。
            RandomOtherSelf,                  //  5:スキル発動者を除く全員からランダムにｎ人。
            Order,                            //  6:順位がｎ位の一人。
            OrderInfront,                     //  7:順位がｎ位より前のキャラ全て。
            OrderBehind,                      //  8:順位がｎ位より後のキャラ全て。
            SelfInfront,                      //  9:スキル発動者より前のｎ人。
            SelfBehind,                       // 10:スキル発動者より後のｎ人。
            TeamMember,                       // 11:同じチームのメンバー全員。
            Near,                             // 12:近く(距離1.5以内)のキャラｎ人。
            SelfAndBlockFront,                // 13:スキル発動者と前方をブロックしているキャラ.
            BlockSide,                        // 14:スキル発動者のサイドをブロックしているキャラ。※イン/アウト両方。

            NearInfront,                      // 15:近く(距離1.5以内)で自分より前のキャラ全て。
            NearBehind,                       // 16:近く(距離1.5以内)で自分より後のキャラ全て。

            RunningStyle,                     // 17:指定走法キャラ全員。
            RunningStyleOtherSelf,            // 18:スキル発動者を除く指定走法キャラ全員。

            SelfInfrontTemptation,            // 19:スキル発動者より前かつ興奮しているｎ人。
            SelfBehindTemptation,             // 20:スキル発動者より後かつ興奮しているｎ人。

            RunningStyleTemptationOtherSelf,  // 21:自分以外で指定走法で興奮しているキャラ全員。

            CharaId,                          // 22:指定のキャラId全員。

            ActivateHealSkill,                // 23:直近で回復スキルを発動したキャラ
        }

        /// <summary>
        /// スキル発動条件のカテゴリ分け。
        /// </summary>
        public enum SkillTriggerCategory
        {
            Timing, // 特定のタイミング（「終盤で」「コーナー走行中に」など）で条件を満たす。
            Param,  // 自分や周囲のキャラの状態、レースの設定を参照して条件を満たす。
        }

        /// <summary>
        /// スキル発動条件のタグ。
        /// </summary>
        /// <remarks>
        /// サポートカードの絞り込み画面で発動条件で絞り込むときに使う。
        /// </remarks>
        public enum SkillTriggerTag
        {
            Null = 0, // 無効値。

            DistanceShort, // 短距離。
            DistanceMile, // マイル。
            DistanceMiddle, // 中距離。
            DistanceLong, // 長距離。
            RunningStyleNige, // 逃げ。
            RunningStyleSenko, // 先行。
            RunningStyleSashi, // 差し。
            RunningStyleOikomi, // 追い。
            Season, // 季節。
            Weather, // 天候。
            PostNumber, // 枠番。
            RunningStyleSame, // 同じ作戦。
            Popularity, // 人気。
            Corner, // コーナー。
            Straight, // 直線。
            Start, // スタート。
            PhaseStart, // レース序盤。
            PhaseMiddle, // レース中盤。
            PhaseEnd, // レース終盤。
            LastSpurt, // ラストスパート。
            Temptation, // 掛かり。
            Track, // レース場。
            Turf, // 芝。
            Dirt, // ダート。
            OrderChange, // 順位変動。
            SkillActivateCount, // スキル発動数。
        }

        /// <summary>
        /// スキルプレートのエフェクト種類。
        /// </summary>
        public enum SkillPlateType
        {
            Null,   // エフェクト無し。
            Buff,   // バフ。
            Debuff, // デバフ。
            NotificationOnly, // 「このレース限定のスキルです」の注意書きのみ。エフェクト無し。
            NotificationCharaMessagePlate, // 「このレース限定のスキルです」の注意書き、レース中の情報プレートはチーム競技場のキャラ立ち絵付きのもの。
            LimitedEventBuff,  // イベント限定スキル(目指せ！最強チーム等で使用)
            Heroes,            // ヒーロースキル
        }

        /// <summary>
        /// 限定スキルの種類
        /// </summary>
        public enum SkillLimitedType
        {
            Null,               // 通常スキル
            StoryUnlockSkill,   // ストーリーアンロックレース限定のスキル
            LimitedEventSkill,  // イベント限定スキル(目指せ！最強チームで使用)
        }

        /// <summary>
        /// 順位が上位〇％以内にいるかどうか、の定義。
        /// </summary>
        public enum OrderInType
        {
            OrderIn10,
            OrderIn20,
            OrderIn30,
            OrderIn40,
            OrderIn50,
            OrderIn60,
            OrderIn70,
            OrderIn80,
            OrderIn90,
        }

        /// <summary>
        /// OrderInTypeの％定義。
        /// </summary>
        public static readonly int[] ORDER_IN_PER = new[]
        {
            10,
            20,
            30,
            40,
            50,
            60,
            70,
            80,
            90,
        };

        /// <summary>
        /// 順位が上位〇％以下にいるかどうか、の定義。
        /// </summary>
        public enum OrderOutType
        {
            OrderOut10,
            OrderOut20,
            OrderOut30,
            OrderOut40,
            OrderOut50,
            OrderOut60,
            OrderOut70,
            OrderOut80,
            OrderOut90,
        }
        
        public enum SkillTriggerOperator
        {
            Equal,
            NotEqual,
            Greater,
            GreaterEqual,
            Lesser,
            LesserEqual,
        }

        public enum TriggerTextIndex
        {
            ValueLh,
            Operator,
            ValueRh,
        }

        /// <summary>
        /// 〇〇==××の１セット保持。
        /// </summary>
        public struct TriggerContext
        {
            public string Keyword;
            public SkillTriggerOperator Op;
            public int ValueRh;
        }
        
        /// <summary>
        /// スキル進化の種別(skill_upgrade_condition.upgrade_type)
        /// </summary>
        public enum SkillUpgradeType
        {
            None = 0,
            Chara = 1, // キャラ別の覚醒スキル
            Scenario = 2, // シナリオ別のスペシャリティ
        }

        #region CSV設定されないもの

        /// <summary> スキル発動の種類 </summary>
        public enum ActivateType
        {
            Null,                       // 通常発動
            OnlyShowSkillPlateAndEffect // スキルプレートとエフェクト表示だけ
        }

        /// <summary> 他スキルによる強制スキル発動で発動するスキル効果のインデックス  </summary>
        public const int FORCE_ACTIVATE_SKILL_DETAIL_INDEX = 0;

    #if CYG_DEBUG
        /// <summary> SkillParamModifierの種類 </summary>
        /// <remarks> デバッグデータのシリアライズ/デシリアライズで使用するので本番環境では使用しない </remarks>
        public enum SkillParamModifierType
        {
            Add,
            Set,
            Multiply,
        }
    #endif
        #endregion


        /// <summary>
        /// OrderOutTypeの％定義。
        /// </summary>
        public static readonly int[] ORDER_OUT_PER = new[]
        {
            10,
            20,
            30,
            40,
            50,
            60,
            70,
            80,
            90,
        };

#if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary> スキル効果のクールダウンタイムを使用するトリガータイプ </summary>
        public static readonly List<System.Type> USE_SKILL_DETAIL_COOLDOWN_TIME_TRIGGER_LIST =
            new List<System.Type>()
            {
                typeof(StandaloneSimulator.SkillTriggerActivateOtherSkillDetail),
            };

        /// <summary> スキル効果が発動してもスキル発動して扱わないトリガータイプ </summary>
        public static readonly List<System.Type> NOT_ACTIVATE_SKILL_TRIGGER_TYPE =
            new List<System.Type>()
            {
                typeof(StandaloneSimulator.SkillTriggerActivateOtherSkillDetail),
            };
#endif

        /// <summary>
        /// ランダム発動スキルなどで参照されるノーマルスキルとレアスキルのレアリティリスト
        /// </summary>
        public static readonly int[] NORMAL_AND_RARE_SKILL_RARITY_ARRAY = new[]
        {
            (int) SkillRarity.Rarity1,
            (int) SkillRarity.Rarity2,
            (int) SkillRarity.Rarity6,
        };

        /// <summary>
        /// ランダム発動スキルなどで参照されるレアスキルのレアリティリスト
        /// </summary>
        public static readonly int[] RARE_SKILL_RARITY_ARRAY = new[]
        {
            (int) SkillRarity.Rarity2,
            (int) SkillRarity.Rarity6,
        };
    }
}

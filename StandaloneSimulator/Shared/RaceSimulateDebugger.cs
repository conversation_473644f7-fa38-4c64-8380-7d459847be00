#if CYG_DEBUG
using System;
using System.Collections.Generic;
using System.Linq;
#if STANDALONE_SIMULATOR
using System.Text.Json;
#endif

namespace StandaloneSimulator
{
    public static class RaceSimulateDebugger
    {
        /// <summary>
        /// 着順上書きする対象キャラ。
        /// </summary>
        public enum FixFinishOrderTarget
        {
            Null,
            Player, // プレイヤー。
            TeamMember1, // チームレースの編成１人目。
            TeamMember2, // チームレースの編成２人目。
            TeamMember3, // チームレースの編成３人目。
            Rival1, // シングルモードのライバルキャラ1人目。
            Rival2, // シングルモードのライバルキャラ2人目。
            Rival3, // シングルモードのライバルキャラ3人目。
            Rival4, // シングルモードのライバルキャラ4人目。
            Rival5, // シングルモードのライバルキャラ5人目。
            Boss, // レジェンドレースのボス。
            HorseIndex_00,
            HorseIndex_01,
            HorseIndex_02,
            HorseIndex_03,
            HorseIndex_04,
            HorseIndex_05,
            HorseIndex_06,
            HorseIndex_07,
            HorseIndex_08,
            HorseIndex_09,
            HorseIndex_10,
            HorseIndex_11,
            HorseIndex_12,
            HorseIndex_13,
            HorseIndex_14,
            HorseIndex_15,
            HorseIndex_16,
            HorseIndex_17,
            HorseIndex_18,
            HorseIndex_19,
        }

        public const int FIX_FINISH_ORDER_NULL = -1;
        public const int FIX_FINISH_ORDER_LAST = 99999;


        public static bool IsEnableAIMoveLane { get; set; }
        public static bool IsEnableAICircumference { get; set; }
        public static bool IsEnableAIStartDelay { get; set; }
        public static bool IsEnableAISpeedAccel { get; set; }
        public static bool IsEnableAITemptation { get; set; }
        public static bool IsEnableAIPositionKeep { get; set; }
        public static bool IsEnableAICalcPhaseHpRandom { get; set; }
        public static bool IsEnableAIConsumeHp { get; set; }
        public static bool IsEnableAIForceInMove { get; set; }
        public static bool IsEnableAILastSpurt { get; set; }
        public static bool IsEnableAISlope { get; set; }
        public static bool IsEnableAICompeteTop { get; set; }       // 位置取り争い（旧名称: 競り合い（ハナ奪い合い））
        public static bool IsEnableAICompeteFight { get; set; }     // 追い比べ（旧名称: 競り合い（叩き合い））
        public static bool IsEnableAIConservePower { get; set; }
        public static bool IsEnableAIStaminaLimitBreakBuff { get; set; }
        public static bool IsEnableAICompeteBeforeSpurt { get; set; }
        public static bool IsEnableAISecureLead { get; set; }
        
        /// <summary>オーバーランもシミュレート記録するかどうか。</summary>
        public static bool IsRecordOverRun { get; set; }

        /// <summary>速度固定で走る。</summary>
        public static bool IsFixSpeed { get; set; }
        /// <summary>固定速度。</summary>
        public static float FixSpeed { get; set; }

        /// <summary>育成レース結果の後着との馬身差固定。</summary>
        public static float ForceBashinDiffBehind { get; set; }
        /// <summary>育成レース結果の１着との馬身差固定。</summary>
        public static float ForceBashinDiffFromTop { get; set; }

        /// <summary>
        /// シミュレート時、デバッグ情報を記録するかどうか。
        /// デバッグ情報は量が多いため時間が掛かる。大量にシミュレートする時はOFFにした方がよい。
        /// </summary>
        public static bool IsRecordDebug { get; set; } = true;

        /// <summary>パラメータ系スキル発動条件を無視。</summary>
        public static bool IsIgnoreSkillTriggerParam { get; set; }

        /// <summary>2Dシミュレーターでのシミュレーション実行。</summary>
        public static bool IsDebugSimulationMode { get; set; } = false;
        /// <summary>2Dシミュレーターでの連続シミュレーション回数。</summary>
        public static int SimulateTryCount { get; set; } = 1;

        /// <summary>シミュレート記録間隔をシミュレート間隔と同じにする。</summary>
        public static bool IsRecordIntervalSimulate { get; set; } = false;

        public static bool IsDebugSimulationOneTime => IsDebugSimulationMode && SimulateTryCount == 1;

        public static bool IsDebugSimulationBulk => IsDebugSimulationMode && SimulateTryCount > 1;
        /// <summary>育成レース用の基礎ステータス補正を行うかどうか。</summary>
        public static bool IsSingleModeAddStatus { get; set; }

        public static bool IsDeserializeDebug { get; set; }
        
        /// <summary> スタミナ無限機能 </summary>
        public static bool IsStaminaUnlimited { get; set; }

    #if GALLOP
        public static StandaloneSimulator.RaceSimulateData CachedSimulateData { get; set; }
        public static Gallop.RaceInitializer.LoadRaceInfo CachedLoadRaceInfo { get; set; }
    #endif

        static RaceSimulateDebugger()
        {
            SetEnableAIAll(true);

            IsRecordOverRun = false;

            IsFixSpeed = false;
            FixSpeed = 0;

            ForceBashinDiffBehind = -1;
            ForceBashinDiffFromTop = -1;

            IsIgnoreSkillTriggerParam = false;

            InitFixFinishOrder();
            InitFixBashinDiffBehind();
            InitFixBashinDiffFromTop();

            IsDeserializeDebug = true;
            IsStaminaUnlimited = false;
        }

        public static void SetEnableAIAll(bool isEnable)
        {
            IsEnableAIMoveLane = isEnable;
            IsEnableAICircumference = isEnable;
            IsEnableAIStartDelay = isEnable;
            IsEnableAISpeedAccel = isEnable;
            IsEnableAITemptation = isEnable;
            IsEnableAIPositionKeep = isEnable;
            IsEnableAICalcPhaseHpRandom = isEnable;
            IsEnableAIConsumeHp = isEnable;
            IsEnableAIForceInMove = isEnable;
            IsEnableAILastSpurt = isEnable;
            IsEnableAISlope = isEnable;
            IsEnableAICompeteTop = isEnable;
            IsEnableAICompeteFight = isEnable;
            IsEnableAIConservePower = isEnable;
            IsEnableAIStaminaLimitBreakBuff = isEnable;
            IsEnableAICompeteBeforeSpurt = isEnable;
            IsEnableAISecureLead = isEnable;
        }

        #region Override
        [Serializable]
        public class HorseOverrideInfo
        {
            public float Time;
            public int HorseIndex;

            public bool IsOverrideSpeed;
            public float Speed;

            public bool IsOverrideDistance;
            public float Distance;

            public bool IsOverrideLane;
            public float Lane;

            public bool IsAIEnable;

            public HorseOverrideInfo()
            {
                HorseIndex = -1;
                IsAIEnable = true;
            }
        }
        [Serializable]
        public class OverrideInfoJsonWrapper
        {
            public List<HorseOverrideInfo> InfoList;
        }

        public static List<HorseOverrideInfo> OverrideInfoList = new List<HorseOverrideInfo>();


        public static HorseOverrideInfo GetOverrideInfo(int horseIndex, float time)
        {
            // 高速化のための早期リターン
            if (OverrideInfoList.Count <= 0)
            {
                return null;
            }

            var infoArray = OverrideInfoList.Where(h => h.HorseIndex == horseIndex).ToArray();
            for (int i = 0; i < infoArray.Length; ++i)
            {
                if (infoArray[i].Time <= time)
                {
                    if (i < infoArray.Length - 1)
                    {
                        if (infoArray[i + 1].Time > time)
                        {
                            return infoArray[i];
                        }
                    }
                    else
                    {
                        return infoArray[i];
                    }
                }
            }
            return null;
        }


        public static void SaveOverrideInfoToJson(string fileName)
        {
            var jsonObject = new OverrideInfoJsonWrapper()
            {
                InfoList = OverrideInfoList,
            };
        #if STANDALONE_SIMULATOR
            var jsonStr = JsonSerializer.Serialize(jsonObject);
        #else
            var jsonStr = UnityEngine.JsonUtility.ToJson(jsonObject);
        #endif
            using (var sw = new System.IO.StreamWriter(fileName))
            {
                sw.Write(jsonStr);
            }
        }
        public static void LoadOverrideInfoToJson(string fileName)
        {
            var jsonStr = string.Empty;
            using (var sr = new System.IO.StreamReader(fileName))
            {
                jsonStr = sr.ReadToEnd();
            }
        #if STANDALONE_SIMULATOR
            var tmp = JsonSerializer.Deserialize<OverrideInfoJsonWrapper>(jsonStr);
        #else
            var tmp = UnityEngine.JsonUtility.FromJson(jsonStr, typeof(OverrideInfoJsonWrapper)) as OverrideInfoJsonWrapper;
        #endif
            OverrideInfoList = tmp.InfoList;
        }
        #endregion

        /// <summary>着順固定の対象キャラと着順の辞書。</summary>
    #if STANDALONE_SIMULATOR
        [ThreadStatic]
    #endif
        public static Dictionary<FixFinishOrderTarget, int> FixFinishOrderDic;

        /// <summary>後着との馬身差固定の対象キャラと馬身差の辞書。</summary>
    #if STANDALONE_SIMULATOR
        [ThreadStatic]
    #endif
        public static Dictionary<int, float> FixBashinDiffBehindDic = new Dictionary<int, float>();

        /// <summary>１着との馬身差固定の対象キャラと馬身差の辞書。</summary>
    #if STANDALONE_SIMULATOR
        [ThreadStatic]
    #endif
        public static Dictionary<int, float> FixBashinDiffFromTopDic = new Dictionary<int, float>();

        public static void InitFixFinishOrder()
        {
        #if STANDALONE_SIMULATOR // Gallop環境ではRaceUtilEnum使えないので#ifで動作分けてる。
            var targetArray = RaceUtilEnum.GetEnumValues(typeof(FixFinishOrderTarget));
        #elif GALLOP 
            var targetArray = Gallop.EnumUtil.GetEnumValues(typeof(FixFinishOrderTarget));
        #endif
            FixFinishOrderDic = new Dictionary<FixFinishOrderTarget, int>();
            FixFinishOrderDic.Clear();
            foreach (FixFinishOrderTarget target in targetArray)
            {
                FixFinishOrderDic.Add(target, FIX_FINISH_ORDER_NULL);
            }
        }
        
        /// <summary>
        /// 着順固定を登録。
        /// </summary>
        /// <param name="target">このキャラを</param>
        /// <param name="finishOrder">この着順にする。</param>
        public static void SetFixFinishOrder(FixFinishOrderTarget target, int finishOrder)
        {
            if (!FixFinishOrderDic.ContainsKey(target))
            {
                FixFinishOrderDic.Add(target, finishOrder);
            }
            else
            {
                FixFinishOrderDic[target] = finishOrder;
            }
        }

        /// <summary>
        /// 着順固定機能使われているか。
        /// </summary>
        public static bool IsNeedFixFinishOrder()
        {
            return FixFinishOrderDic.Any(v => v.Key != FixFinishOrderTarget.Null && v.Value != FIX_FINISH_ORDER_NULL);
        }

    #if STANDALONE_SIMULATOR || UNITY_EDITOR
        /// <summary>
        /// 着順固定の対象にするHorseIndex取得。見つからなければ-1。
        /// </summary>
        public static int GetFixFinishOrderHorseIndex(FixFinishOrderTarget swapTarget, IHorseRaceInfoSimulate[] horseDataArray)
        {
            switch (swapTarget)
            {
            #if GALLOP
                // クライアントシミュレートのみで有効
                case FixFinishOrderTarget.Player:
                    return GetFirstUserHorseIndex(horseDataArray);

                case FixFinishOrderTarget.TeamMember1:
                    return GetTeamMemberHorseIndex(horseDataArray, 1);
                case FixFinishOrderTarget.TeamMember2:
                    return GetTeamMemberHorseIndex(horseDataArray, 2);
                case FixFinishOrderTarget.TeamMember3:
                    return GetTeamMemberHorseIndex(horseDataArray, 3);

                case FixFinishOrderTarget.Rival1:
                    return GetRivalHorseIndex(horseDataArray, 0);
                case FixFinishOrderTarget.Rival2:
                    return GetRivalHorseIndex(horseDataArray, 1);
                case FixFinishOrderTarget.Rival3:
                    return GetRivalHorseIndex(horseDataArray, 2);
                case FixFinishOrderTarget.Rival4:
                    return GetRivalHorseIndex(horseDataArray, 3);
                case FixFinishOrderTarget.Rival5:
                    return GetRivalHorseIndex(horseDataArray, 4);
                case FixFinishOrderTarget.Boss:
                    return GetBossHorseIndex(horseDataArray);
            #endif

                case FixFinishOrderTarget.HorseIndex_00:
                case FixFinishOrderTarget.HorseIndex_01:
                case FixFinishOrderTarget.HorseIndex_02:
                case FixFinishOrderTarget.HorseIndex_03:
                case FixFinishOrderTarget.HorseIndex_04:
                case FixFinishOrderTarget.HorseIndex_05:
                case FixFinishOrderTarget.HorseIndex_06:
                case FixFinishOrderTarget.HorseIndex_07:
                case FixFinishOrderTarget.HorseIndex_08:
                case FixFinishOrderTarget.HorseIndex_09:
                case FixFinishOrderTarget.HorseIndex_10:
                case FixFinishOrderTarget.HorseIndex_11:
                case FixFinishOrderTarget.HorseIndex_12:
                case FixFinishOrderTarget.HorseIndex_13:
                case FixFinishOrderTarget.HorseIndex_14:
                case FixFinishOrderTarget.HorseIndex_15:
                case FixFinishOrderTarget.HorseIndex_16:
                case FixFinishOrderTarget.HorseIndex_17:
                case FixFinishOrderTarget.HorseIndex_18:
                case FixFinishOrderTarget.HorseIndex_19:
                    int horseIndex = swapTarget - FixFinishOrderTarget.HorseIndex_00;
                    if (horseIndex >= 0 && horseIndex < horseDataArray.Length)
                    {
                        return horseIndex;
                    }
                    break;
            }

            return -1;
        }

    #if GALLOP
        private static int GetFirstUserHorseIndex(IHorseRaceInfoSimulate[] horseDataArray)
        {
            var player = horseDataArray.FirstOrDefault(h => h.ViewerId == Gallop.Certification.ViewerId);
            return player != null ? RaceUtil.FrameOrder2HorseIndex(player.FrameOrder) : -1;
        }
        private static int GetTeamMemberHorseIndex(IHorseRaceInfoSimulate[] horseDataArray, int teamMemberId)
        {
            var horse = horseDataArray.FirstOrDefault(h => h.ViewerId == Gallop.Certification.ViewerId && h.TeamMemberId == teamMemberId);
            return horse != null ? RaceUtil.FrameOrder2HorseIndex(horse.FrameOrder) : -1;
        }
        private static int GetBossHorseIndex(IHorseRaceInfoSimulate[] horseDataArray)
        {
            var boss = horseDataArray.FirstOrDefault(h => h.NpcType == (int) Gallop.RaceDefine.RaceNpcType.Boss);
            return boss != null ? RaceUtil.FrameOrder2HorseIndex(boss.FrameOrder) : -1;
        }
        private static int GetRivalHorseIndex(IHorseRaceInfoSimulate[] horseDataArray, int rivalIndex)
        {
            if (CachedLoadRaceInfo == null)
            {
                return -1;
            }

            var masterRivalList = Gallop.MasterDataManager.Instance.masterSingleModeRival.GetListWithRaceProgramIdOrderByIdAsc(CachedLoadRaceInfo.SingleRaceProgramId);
            if (masterRivalList == null || masterRivalList.Count == 0)
            {
                return -1;
            }

            int curCharaId = Gallop.WorkDataManager.Instance.SingleMode.Character.CharaId;
            masterRivalList = masterRivalList.Where(m => m.CharaId == curCharaId).ToList();

            var rivalHorseList = horseDataArray.Where(h => masterRivalList.Any(m => m.RivalCharaId == h.CharaId)).ToList();
            if (rivalIndex >= rivalHorseList.Count)
            {
                return -1;
            }
            return RaceUtil.FrameOrder2HorseIndex(rivalHorseList[rivalIndex].FrameOrder);
        }
    #endif
    #endif
        
    #if GALLOP
        /// <summary>
        /// 【実機デバッグ用】着順固定の対象にするHorseIndex取得。見つからなければ-1。
        /// </summary>
        public static int GetFixFinishOrderHorseIndex(FixFinishOrderTarget swapTarget, Gallop.HorseData[] horseDataArray)
        {
            switch (swapTarget)
            {
                // クライアントシミュレートのみで有効
                case FixFinishOrderTarget.Player:
                    return GetFirstUserHorseIndex(horseDataArray);

                case FixFinishOrderTarget.TeamMember1:
                    return GetTeamMemberHorseIndex(horseDataArray, 1);
                case FixFinishOrderTarget.TeamMember2:
                    return GetTeamMemberHorseIndex(horseDataArray, 2);
                case FixFinishOrderTarget.TeamMember3:
                    return GetTeamMemberHorseIndex(horseDataArray, 3);

                case FixFinishOrderTarget.Rival1:
                    return GetRivalHorseIndex(horseDataArray, 0);
                case FixFinishOrderTarget.Rival2:
                    return GetRivalHorseIndex(horseDataArray, 1);
                case FixFinishOrderTarget.Rival3:
                    return GetRivalHorseIndex(horseDataArray, 2);
                case FixFinishOrderTarget.Rival4:
                    return GetRivalHorseIndex(horseDataArray, 3);
                case FixFinishOrderTarget.Rival5:
                    return GetRivalHorseIndex(horseDataArray, 4);
                case FixFinishOrderTarget.Boss:
                    return GetBossHorseIndex(horseDataArray);

                case FixFinishOrderTarget.HorseIndex_00:
                case FixFinishOrderTarget.HorseIndex_01:
                case FixFinishOrderTarget.HorseIndex_02:
                case FixFinishOrderTarget.HorseIndex_03:
                case FixFinishOrderTarget.HorseIndex_04:
                case FixFinishOrderTarget.HorseIndex_05:
                case FixFinishOrderTarget.HorseIndex_06:
                case FixFinishOrderTarget.HorseIndex_07:
                case FixFinishOrderTarget.HorseIndex_08:
                case FixFinishOrderTarget.HorseIndex_09:
                case FixFinishOrderTarget.HorseIndex_10:
                case FixFinishOrderTarget.HorseIndex_11:
                case FixFinishOrderTarget.HorseIndex_12:
                case FixFinishOrderTarget.HorseIndex_13:
                case FixFinishOrderTarget.HorseIndex_14:
                case FixFinishOrderTarget.HorseIndex_15:
                case FixFinishOrderTarget.HorseIndex_16:
                case FixFinishOrderTarget.HorseIndex_17:
                case FixFinishOrderTarget.HorseIndex_18:
                case FixFinishOrderTarget.HorseIndex_19:
                    int horseIndex = swapTarget - FixFinishOrderTarget.HorseIndex_00;
                    if (horseIndex >= 0 && horseIndex < horseDataArray.Length)
                    {
                        return horseIndex;
                    }
                    break;
            }

            return -1;
        }
        
        private static int GetFirstUserHorseIndex(Gallop.HorseData[] horseDataArray)
        {
            var player = horseDataArray.FirstOrDefault(h => h.ViewerId == Gallop.Certification.ViewerId);
            return player?.horseIndex ?? -1;
        }
        private static int GetTeamMemberHorseIndex(Gallop.HorseData[] horseDataArray, int teamMemberId)
        {
            var horse = horseDataArray.FirstOrDefault(h => h.ViewerId == Gallop.Certification.ViewerId && h.TeamMemberId == teamMemberId);
            return horse?.horseIndex ?? -1;
        }
        private static int GetBossHorseIndex(Gallop.HorseData[] horseDataArray)
        {
            var boss = horseDataArray.FirstOrDefault(h => h.NpcType == Gallop.RaceDefine.RaceNpcType.Boss);
            return boss?.horseIndex ?? -1;
        }
        private static int GetRivalHorseIndex(Gallop.HorseData[] horseDataArray, int rivalIndex)
        {
            if (CachedLoadRaceInfo == null)
            {
                return -1;
            }

            var masterRivalList = Gallop.MasterDataManager.Instance.masterSingleModeRival.GetListWithRaceProgramIdOrderByIdAsc(CachedLoadRaceInfo.SingleRaceProgramId);
            if (masterRivalList == null || masterRivalList.Count == 0)
            {
                return -1;
            }

            int curCharaId = Gallop.WorkDataManager.Instance.SingleMode.Character.CharaId;
            masterRivalList = masterRivalList.Where(m => m.CharaId == curCharaId).ToList();

            var rivalHorseList = horseDataArray.Where(h => masterRivalList.Any(m => m.RivalCharaId == h.charaId)).ToList();
            if (rivalIndex >= rivalHorseList.Count)
            {
                return -1;
            }
            return rivalHorseList[rivalIndex].horseIndex;
        }
    #endif
        
        /// <summary>
        /// 後着との馬身差固定を解除。
        /// </summary>
        public static void InitFixBashinDiffBehind()
        {
            FixBashinDiffBehindDic = new Dictionary<int, float>();
            FixBashinDiffBehindDic.Clear();
        }
        
        /// <summary>
        /// 後着との馬身差固定を登録。
        /// </summary>
        public static void SetFixBashinDiffBehind(int horseIndex, float bashinDiffBehind)
        {
            if (!FixBashinDiffBehindDic.ContainsKey(horseIndex))
            {
                FixBashinDiffBehindDic.Add(horseIndex, bashinDiffBehind);
            }
            else
            {
                FixBashinDiffBehindDic[horseIndex] = bashinDiffBehind;
            }
        }

        /// <summary>
        /// １着との馬身差固定を解除。
        /// </summary>
        public static void InitFixBashinDiffFromTop()
        {
            FixBashinDiffFromTopDic = new Dictionary<int, float>();
            FixBashinDiffFromTopDic.Clear();
        }
        
        /// <summary>
        /// １着との馬身差固定を登録。
        /// </summary>
        public static void SetFixBashinDiffFromTop(int horseIndex, float bashinDiffFromTop)
        {
            if (!FixBashinDiffFromTopDic.ContainsKey(horseIndex))
            {
                FixBashinDiffFromTopDic.Add(horseIndex, bashinDiffFromTop);
            }
            else
            {
                FixBashinDiffFromTopDic[horseIndex] = bashinDiffFromTop;
            }
        }
    }
}
#endif

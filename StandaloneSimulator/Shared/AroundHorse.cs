namespace Gallop
{
    public class AroundHorse
    { 
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public StandaloneSimulator.IHorseRaceInfoSimulate infoSimulate;
#endif
#if GALLOP
        public Gallop.IHorseRaceInfo info;
#endif
        public float distanceGap; // 距離差分。+なほど相手が前にいる
        public float distanceGapAbs; // 距離差分絶対値。
        public float laneGap; // 横位置差分。+なほど相手が外にいる
        public float laneGapAbs; // 横位置差分絶対値。
        public bool isBack; // 後方対象外か？
        public float speedGap; // スピード差分。+なほど自分が速い。※distance,laneと逆になってる。
        public float speedGapAbs; // スピード差分絶対値。

        public float CompeteFightCandidateContinueTime;

        // ※※※　ここから下は後方のキャラはチェックされない。　※※※
    };
}
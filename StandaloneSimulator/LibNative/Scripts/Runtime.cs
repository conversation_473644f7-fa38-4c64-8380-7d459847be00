#if STANDALONE_SIMULATOR
using System;
using System.Runtime.InteropServices;
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    namespace LibNative
    {
        public static class Runtime
        {
            /// <summary>
            /// DllImport用の識別子
            /// </summary>
#if PUBLISHED
            internal const string Import = "LibNative/Plugins/Linux/x86_64/libnative";
#else
            internal const string Import = "LibNative/Plugins/Windows/x86_64/libnative";
#endif
        }
    }
}
#endif
#if STANDALONE_SIMULATOR
using System;
using System.Text;
using System.Runtime.InteropServices;

namespace StandaloneSimulator
{
    namespace LibNative.Sqlite3
    {
        public class DatabaseCorruptionException : Exception
        {
            public DatabaseCorruptionException(int rc) : base(string.Format("Database is corrupted: code {0}", rc)) { }
        }
        public class DatabaseDiskFullException : Exception { }
        public class DatabaseIOErrorException : Exception { }

        public static class ResultCode
        {
            public const int SQLITE_OK = 0; /* Successful result */
            public const int SQLITE_ERROR = 1; /* Generic error */
            public const int SQLITE_INTERNAL = 2; /* Internal logic error in SQLite */
            public const int SQLITE_PERM = 3; /* Access permission denied */
            public const int SQLITE_ABORT = 4; /* Callback routine requested an abort */
            public const int SQLITE_BUSY = 5; /* The database file is locked */
            public const int SQLITE_LOCKED = 6; /* A table in the database is locked */
            public const int SQLITE_NOMEM = 7; /* <PERSON> malloc() failed */
            public const int SQLITE_READONLY = 8; /* Attempt to write a readonly database */
            public const int SQLITE_INTERRUPT = 9; /* Operation terminated by sqlite3_interrupt()*/
            public const int SQLITE_IOERR = 10; /* Some kind of disk I/O error occurred */
            public const int SQLITE_CORRUPT = 11; /* The database disk image is malformed */
            public const int SQLITE_NOTFOUND = 12; /* Unknown opcode in sqlite3_file_control() */
            public const int SQLITE_FULL = 13; /* Insertion failed because database is full */
            public const int SQLITE_CANTOPEN = 14;/* Unable to open the database file */
            public const int SQLITE_PROTOCOL = 15;/* Database lock protocol error */
            public const int SQLITE_EMPTY = 16;/* Internal use only */
            public const int SQLITE_SCHEMA = 17;/* The database schema changed */
            public const int SQLITE_TOOBIG = 18;/* String or BLOB exceeds size limit */
            public const int SQLITE_CONSTRAINT = 19;/* Abort due to constraint violation */
            public const int SQLITE_MISMATCH = 20;/* Data type mismatch */
            public const int SQLITE_MISUSE = 21;/* Library used incorrectly */
            public const int SQLITE_NOLFS = 22;/* Uses OS features not supported on host */
            public const int SQLITE_AUTH = 23;/* Authorization denied */
            public const int SQLITE_FORMAT = 24;/* Not used */
            public const int SQLITE_RANGE = 25;/* 2nd parameter to sqlite3_bind out of range */
            public const int SQLITE_NOTADB = 26;/* File opened that is not a database file */
            public const int SQLITE_NOTICE = 27;/* Notifications from sqlite3_log() */
            public const int SQLITE_WARNING = 28;/* Warnings from sqlite3_log() */
            public const int SQLITE_ROW = 100;/* sqlite3_step() has another row ready */
            public const int SQLITE_DONE = 101;/* sqlite3_step() has finished executing */

            public static void CheckCriticalError(int rc, string errMsg = null)
            {
                if (rc == ResultCode.SQLITE_CORRUPT || rc == ResultCode.SQLITE_NOTADB)
                {
                    throw new DatabaseCorruptionException(rc);
                }
                else if (rc == ResultCode.SQLITE_FULL)
                {
                    throw new DatabaseDiskFullException();
                }
                else if (rc == ResultCode.SQLITE_IOERR)
                {
                    // IOエラーは容量枯渇時にも飛んでくるので、基本的には同一視する - ykst
                    throw new DatabaseIOErrorException();
                }
                else if (!string.IsNullOrEmpty(errMsg))
                {
                    // NOTE: この判定処理は怪しいが、pragmaをexecで打ってる段階でファイルが腐ってる場合はこれしかなさそう - ykst
                    if (errMsg.IndexOf("unsupported file format", StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        throw new DatabaseCorruptionException(ResultCode.SQLITE_NOTADB);
                    }
                }
            }
        }

        public static class Flags
        {
            public const int SQLITE_OPEN_READONLY = 0x00000001; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_READWRITE = 0x00000002; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_CREATE = 0x00000004; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_DELETEONCLOSE = 0x00000008; /* VFS only */
            public const int SQLITE_OPEN_EXCLUSIVE = 0x00000010; /* VFS only */
            public const int SQLITE_OPEN_AUTOPROXY = 0x00000020; /* VFS only */
            public const int SQLITE_OPEN_URI = 0x00000040; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_MEMORY = 0x00000080; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_MAIN_DB = 0x00000100; /* VFS only */
            public const int SQLITE_OPEN_TEMP_DB = 0x00000200; /* VFS only */
            public const int SQLITE_OPEN_TRANSIENT_DB = 0x00000400; /* VFS only */
            public const int SQLITE_OPEN_MAIN_JOURNAL = 0x00000800; /* VFS only */
            public const int SQLITE_OPEN_TEMP_JOURNAL = 0x00001000; /* VFS only */
            public const int SQLITE_OPEN_SUBJOURNAL = 0x00002000; /* VFS only */
            public const int SQLITE_OPEN_SUPER_JOURNAL = 0x00004000; /* VFS only */
            public const int SQLITE_OPEN_NOMUTEX = 0x00008000; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_FULLMUTEX = 0x00010000; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_SHAREDCACHE = 0x00020000; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_PRIVATECACHE = 0x00040000; /* Ok for sqlite3_open_v2() */
            public const int SQLITE_OPEN_WAL = 0x00080000; /* VFS only */
            public const int SQLITE_OPEN_NOFOLLOW = 0x01000000; /* Ok for sqlite3_open_v2() */
        }

        public static class Config
        {
            public const int SQLITE_CONFIG_SINGLETHREAD = 1;  /* nil */
            public const int SQLITE_CONFIG_MULTITHREAD  = 2;  /* nil */
            public const int SQLITE_CONFIG_SERIALIZED   = 3;  /* nil */
            public const int SQLITE_CONFIG_MALLOC       = 4;  /* sqlite3_mem_methods* */
            public const int SQLITE_CONFIG_GETMALLOC    = 5;  /* sqlite3_mem_methods* */
            public const int SQLITE_CONFIG_SCRATCH      = 6;  /* No longer used */
            public const int SQLITE_CONFIG_PAGECACHE    = 7;  /* void*, int sz, int N */
            public const int SQLITE_CONFIG_HEAP         = 8;  /* void*, int nByte, int min */
            public const int SQLITE_CONFIG_MEMSTATUS    = 9;  /* boolean */
            public const int SQLITE_CONFIG_MUTEX       = 10;  /* sqlite3_mutex_methods* */
            public const int SQLITE_CONFIG_GETMUTEX    = 11;  /* sqlite3_mutex_methods* */
                    /* previously SQLITE_CONFIG_CHUNKALLOC= 12; which is now unused. */
            public const int SQLITE_CONFIG_LOOKASIDE   = 13;  /* int int */
            public const int SQLITE_CONFIG_PCACHE      = 14;  /* no-op */
            public const int SQLITE_CONFIG_GETPCACHE   = 15;  /* no-op */
            public const int SQLITE_CONFIG_LOG         = 16;  /* xFunc, void* */
            public const int SQLITE_CONFIG_URI         = 17;  /* int */
            public const int SQLITE_CONFIG_PCACHE2     = 18;  /* sqlite3_pcache_methods2* */
            public const int SQLITE_CONFIG_GETPCACHE2  = 19;  /* sqlite3_pcache_methods2* */
            public const int SQLITE_CONFIG_COVERING_INDEX_SCAN= 20;  /* int */
            public const int SQLITE_CONFIG_SQLLOG      = 21;  /* xSqllog, void* */
            public const int SQLITE_CONFIG_MMAP_SIZE   = 22;  /* sqlite3_int64, sqlite3_int64 */
            public const int SQLITE_CONFIG_WIN32_HEAPSIZE     = 23;  /* int nByte */
            public const int SQLITE_CONFIG_PCACHE_HDRSZ       = 24;  /* int *psz */
            public const int SQLITE_CONFIG_PMASZ              = 25;  /* unsigned int szPma */
            public const int SQLITE_CONFIG_STMTJRNL_SPILL     = 26;  /* int nByte */
            public const int SQLITE_CONFIG_SMALL_MALLOC       = 27;  /* boolean */
            public const int SQLITE_CONFIG_SORTERREF_SIZE     = 28;  /* int nByte */
            public const int SQLITE_CONFIG_MEMDB_MAXSIZE      = 29;  /* sqlite3_int64 */


            /// <summary>
            /// ライブラリの必要条件を検出するデバッグメソッド
            /// </summary>
            [System.Diagnostics.Conditional("CYG_DEBUG")]
            public static void CheckPrerequisite()
            {
                int threadSafeConfig = Plugin.sqlite3_threadsafe();

                if (threadSafeConfig == 0)
                {
                    Debug.LogError("Sqlite3 is compiled as single-threaded, which is not expected.");
                }
                else if (threadSafeConfig == 1)
                {
                    Debug.LogWarning("Sqlite3 is compiled as serialized, which is too conservative.");
                }
                else if (threadSafeConfig != 2)
                {
                    Debug.LogError("Unexpcted result (sqlite3_threadsafe): " + threadSafeConfig);
                }
            }
        }

        public static class Cipher
        {
            public enum TYPE
            {
                CODEC_TYPE_AES128 = 1,
                CODEC_TYPE_AES256,
                CODEC_TYPE_CHACHA20,
                CODEC_TYPE_SQLCIPHER,
                CODEC_TYPE_RC4
            }
        }

        public class Connection : IDisposable
        {
            public IntPtr DBHandle { get; private set; }
            public string dbPath { get; private set; }
            public bool IsOpened()
            {
                return DBHandle != IntPtr.Zero;
            }

            public Connection()
            {
                dbPath = null;
                DBHandle = IntPtr.Zero;
            }

            /// <summary>
            /// 読み取り専用でSqlite3データベースに接続する
            /// </summary>
            /// <param name="fileName">データベースファイルのパス</param>
            /// <param name="vfsName">事前にsqlite3_vfs_registerによって登録したVFS名。未指定の場合はデフォルトを使用</param>
            /// <returns>正常に接続出来たらtrue</returns>
            public bool Open(string fileName, string vfsName = null, byte[] key = null, Cipher.TYPE cipherType = Cipher.TYPE.CODEC_TYPE_CHACHA20)
            {
                Debug.Log("fileName : " + fileName);
                dbPath = fileName;

                IntPtr db = IntPtr.Zero;

                byte[] fileNameBytes = Encoding.UTF8.GetBytes(fileName + "\0");

                byte[] vfsNameBytes = null;
                if (!string.IsNullOrEmpty(vfsName))
                {
                    vfsNameBytes = Encoding.UTF8.GetBytes(vfsName + "\0");
                }

                int rc = Plugin.sqlite3_open_v2(fileNameBytes, out db, Flags.SQLITE_OPEN_READONLY, vfsNameBytes);

                DBHandle = db;

                bool isSuccess = (rc == ResultCode.SQLITE_OK);

                // pragma keyを設定
                if (isSuccess && key != null)
                {
                    rc = Plugin.sqlite3mc_config(DBHandle, Encoding.UTF8.GetBytes("cipher\0"), (int)cipherType);
                    isSuccess = rc == (int)cipherType;
                    if (isSuccess)
                    {
                        rc = Plugin.sqlite3_key(DBHandle, key, key.Length);
                        isSuccess = rc == ResultCode.SQLITE_OK;
                    }
                }

                // 以下読み込み専用チューニング
                if (isSuccess)
                {
                    // Readしかしないのでジャーナル要らない
                    Exec("pragma journal_mode=OFF");
                    // 1スレッドしかアクセスさせないので同期要らない
                    Exec("pragma synchronous=0");
    #if UNITY_EDITOR
                    // UNITY_EDITOR時はHotReloadがかかった際に掴みっぱなしになることがあるため共用モードで開く
                    Exec("pragma locking_mode=NORMAL");
    #else
                    // 1プロセスしかDBを触らないのでファイルロックして排他処理を省略
                    Exec("pragma locking_mode=EXCLUSIVE");
    #endif
                }
                else
                {
                    Debug.LogError("sqlite3_open failed: code " + rc);
                }

                return isSuccess;
            }

            /// <summary>
            /// 読み書き可でデータベースファイルに接続する。指定したパスにファイルが存在しなければ新規作成を行う。
            /// </summary>
            /// <param name="fileName">データベースファイルのパス</param>
            /// <returns>正常にOpen出来たらtrue、破損検出時はDatabaseCorruption例外が発生</returns>
            public bool OpenWritable(string fileName, string journalMode = "MEMORY", string synchronouos = "1", string lockingMode = "EXCLUSIVE", bool enableSharedCache = false, byte[] key = null, Cipher.TYPE cipherType = Cipher.TYPE.CODEC_TYPE_CHACHA20)
            {
                dbPath = fileName;
                IntPtr db = IntPtr.Zero;
                bool isSuccess = true;

                //ここのtry-catchだとシステムエラーはとらえられないはずなので過信はしないように -INADA
                try
                {
                    byte[] fileNameBytes = Encoding.UTF8.GetBytes(fileName + "\0");

                    int flags = Flags.SQLITE_OPEN_CREATE | Flags.SQLITE_OPEN_READWRITE | (enableSharedCache ? Flags.SQLITE_OPEN_SHAREDCACHE : 0);
                    int rc = Plugin.sqlite3_open_v2(fileNameBytes, out db, flags, null);

                    DBHandle = db;

                    isSuccess = (rc == 0);

                    // pragma keyを設定
                    if (isSuccess && key != null)
                    {
                        rc = Plugin.sqlite3mc_config(DBHandle, Encoding.UTF8.GetBytes("cipher\0"), (int)cipherType);
                        isSuccess = rc == (int)cipherType;
                        if (isSuccess)
                        {
                            rc = Plugin.sqlite3_key(DBHandle, key, key.Length);
                            isSuccess = rc == ResultCode.SQLITE_OK;
                        }
                    }

                    // pragmaによるランタイムチューニング。 - ykst
                    if (isSuccess)
                    {
                        // 歴史的メモ: ジャーナルファイルの最適化(WALにすると一部端末でファイルロックが消えない。DELETEだと一部端末でシステムエラー発生)
                        Exec("pragma journal_mode=" + journalMode);
                        Exec("pragma synchronous=" + synchronouos);
                        Exec("pragma locking_mode=" + lockingMode);
                    }
                    else
                    {
                        Debug.LogError("sqlite3_open failed: " + rc);
                    }
                }
                catch (Exception e)
                {
                    // ファイルを掴んでいると消去出来ないのでcloseしておく - ykst
                    if (db != IntPtr.Zero)
                    {
                        Plugin.sqlite3_close(db);
                        DBHandle = IntPtr.Zero;
                    }

                    // DB破損を上位層にハンドルさせるためにrethrow
                    throw e;
                }

                return isSuccess;
            }

            /// <summary>
            /// トランザクションの開始
            /// </summary>
            public bool Begin(bool immediate = false)
            {
                Debug.Log("Begin");
                var command = immediate ? "BEGIN IMMEDIATE;" : "BEGIN";
                return Exec(command);
            }

            /// <summary>
            /// トランザクションを終了し、コミット
            /// </summary>
            public bool Commit()
            {
                Debug.Log("COMMIT");
                return Exec("COMMIT;");
            }

            /// <summary>
            /// PassiveモードでWALを書き出してDBを更新する。
            /// サスペンド時にはFullやRestartを使いたいところだが、
            /// その瞬間にメインスレッドのコルーチンがクエリを開きっぱなしにしていた場合にデッドロックが発生してしまうので、
            /// 実際はPassiveしか安全ではない。 refs. https://www.sqlite.org/c3ref/wal_checkpoint_v2.html
            /// </summary>
            /// <returns>成功したらtrue</returns>
            public bool Checkpoint()
            {
                int rc = Plugin.sqlite3_wal_checkpoint(DBHandle, IntPtr.Zero);

                if (rc != ResultCode.SQLITE_OK)
                {
                    Debug.LogError("sqlite3_wal_checkpoint failed: " + rc);
                    return false;
                }

                return true;
            }

            /// <summary>
            /// トランザクションを終了し、コミット
            /// </summary>
            public bool Rollback()
            {
                Debug.Log("ROLLBACK");
                return Exec("ROLLBACK;");
            }

            /// <summary>
            /// バキュームを行う
            /// </summary>
            public bool Vacuum()
            {
                Debug.Log("Vacuum");
                return Exec("VACUUM;");
            }

            public bool Analyze()
            {
                Debug.Log("Analyze");
                return Exec("Analyze;");
            }

            public virtual void Dispose()
            {
                CloseDB();
                GC.SuppressFinalize(this);
            }

            ~Connection()
            {
                CloseDB();
            }

            public virtual void CloseDB()
            {
                if (DBHandle != IntPtr.Zero)
                {
                    int rc = Plugin.sqlite3_close(DBHandle);

                    if (rc != ResultCode.SQLITE_OK)
                    {
                        Debug.LogError("failed to close db at " + dbPath + ": " + rc);
                    }

                    DBHandle = IntPtr.Zero;
                }
            }

            public bool Exec(string sql)
            {
    #if UNITY_EDITOR
                if (DBHandle == IntPtr.Zero)
                {
                    Debug.LogError("database is already closed: " + dbPath);
                    return false;
                }
    #endif
                IntPtr errMsgPtr;
                byte[] sqlBytes = Encoding.UTF8.GetBytes(sql + "\0");

                int rc = Plugin.sqlite3_exec(DBHandle, sqlBytes, IntPtr.Zero, IntPtr.Zero, out errMsgPtr);

                if (rc != ResultCode.SQLITE_OK)
                {
                    string errMsg = (errMsgPtr == IntPtr.Zero) ? "" : Marshal.PtrToStringAnsi(errMsgPtr);
                    string logMsg = string.Format("sqlite3_exec failed (code {0}: {1}) with sql: {2}", rc, errMsg, sql);

                    // データーベースロックは待つことで解消される可能性があるのでエラーにはしない。ただ、基本的には発生しないようにすべき - ykst
                    if (rc == 6)
                    {
                        Debug.LogWarning(logMsg);
                    }
                    else
                    {
                        ResultCode.CheckCriticalError(rc, errMsg);
                        Debug.LogError(logMsg);
                    }
                }

                return rc == ResultCode.SQLITE_OK;
            }

            public Query Query(string sql)
            {
    #if UNITY_EDITOR
                if (DBHandle == IntPtr.Zero)
                {
                    Debug.LogError("database is already closed: " + dbPath);
                    return null;
                }
    #endif
                return new Query(this, sql);
            }

            public PreparedQuery PreparedQuery(string sql)
            {
    #if UNITY_EDITOR
                if (DBHandle == IntPtr.Zero)
                {
                    Debug.LogError("database is already closed: " + dbPath);
                    return null;
                }
    #endif
                return new PreparedQuery(this, sql);
            }
        }

        public class Query : IDisposable
        {
            protected Connection _conn = null;
            protected IntPtr _stmt = IntPtr.Zero;

            public Query(Connection conn, string sql)
            {
                _Setup(conn, sql);
            }

            protected void _Setup(Connection conn, string sql)
            {
                _conn = conn;

                IntPtr stmt;
                byte[] sqlBytes = Encoding.UTF8.GetBytes(sql);
                int rc = Plugin.sqlite3_prepare_v2(conn.DBHandle, sqlBytes, sqlBytes.Length, out stmt, IntPtr.Zero);

                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    throw new System.Exception(string.Format("sqlite3_prepare_v2 failed(code {0}) with sql: {1}", rc, sql));
                }

                _stmt = stmt;
            }

            public virtual void Dispose()
            {
                if (_stmt != IntPtr.Zero)
                {
                    int rc = Plugin.sqlite3_finalize(_stmt);
                    _stmt = IntPtr.Zero;

                    if (rc != ResultCode.SQLITE_OK)
                    {
                        ResultCode.CheckCriticalError(rc);
                        Debug.LogError("sqlite3_finalize error: " + rc);
                    }
                }
            }

            public bool Step()
            {
                int rc = Plugin.sqlite3_step(_stmt);

                bool stepped = rc == ResultCode.SQLITE_ROW;

                if (!stepped)
                {
                    ResultCode.CheckCriticalError(rc);
                }

                return stepped;
            }

            public bool Exec()
            {
                int rc = Plugin.sqlite3_step(_stmt);

                bool executed = rc == ResultCode.SQLITE_DONE;

                if (!executed)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError($"sqlite3_step failed: {rc}");
                }

                return executed;
            }

            /// <summary>
            /// idxは0から始まるので要注意！
            /// </summary>
            /// <param name="idx">検索結果の位置</param>
            /// <returns></returns>
            public int GetInt(int idx)
            {
                return Plugin.sqlite3_column_int(_stmt, idx);
            }

            /// <summary>
            /// idxは0から始まるので要注意！
            /// </summary>
            /// <param name="idx">検索結果の位置</param>
            /// <returns></returns>
            public long GetLong(int idx)
            {
                return Plugin.sqlite3_column_int64(_stmt, idx);
            }

            /// <summary>
            /// idxは0から始まるので要注意！
            /// </summary>
            /// <param name="idx">検索結果の位置</param>
            /// <returns></returns>
            public double GetDouble(int idx)
            {
                return Plugin.sqlite3_column_double(_stmt, idx);
            }

            /// <summary>
            /// idxは0から始まるので要注意！
            /// </summary>
            /// <param name="idx">検索結果の位置</param>
            /// <returns></returns>
            public string GetText(int idx)
            {
                IntPtr textPtr = Plugin.sqlite3_column_text(_stmt, idx);

                return Marshal.PtrToStringAnsi(textPtr);
            }

            /// <summary>
            /// idxは0から始まるので要注意！
            /// </summary>
            /// <param name="idx">検索結果の位置</param>
            /// <returns></returns>
            public IntPtr GetTextPtr(int idx)
            {
                // 生ポインタを返す。stmtがリセットされると無効になるので取り扱い注意
                return Plugin.sqlite3_column_text(_stmt, idx);
            }

            /// <summary>
            /// idxは0から始まるので要注意！
            /// </summary>
            /// <param name="idx">検索結果の位置</param>
            /// <returns></returns>
            public byte[] GetBlob(int idx)
            {
                int size = Plugin.sqlite3_column_bytes(_stmt, idx);
                if (size == 0)
                {
                    return null;
                }
                IntPtr blobPtr = Plugin.sqlite3_column_blob(_stmt, idx);
                byte[] managedBytes = new byte[size];
                // sqliteのblobは内部のページ上に確保されておりStepとかで消えるのでコピーする必要がある - ykst
                Marshal.Copy(blobPtr, managedBytes, 0, size);

                return managedBytes;
            }

            /// <summary>
            /// レコードの値がNULLになっているか否か
            /// </summary>
            /// <param name="idx"></param>
            /// <returns>NULLならばtrue</returns>
            public bool IsNull(int idx)
            {
                return Plugin.sqlite3_column_type(_stmt, idx) == 5;
            }
        }

        public class PreparedQuery : Query
        {
            public PreparedQuery(Connection conn, string sql) : base(conn, sql)
            {
            }

            /// <summary>
            /// Bindのidxは1から始まるので要注意！
            /// </summary>
            /// <param name="idx">bindする変数の位置</param>
            /// <param name="text">値</param>
            /// <returns></returns>
            public bool BindText(int idx, string text)
            {
                byte[] textBytes = Encoding.UTF8.GetBytes(text);

                int rc = Plugin.sqlite3_bind_text(_stmt, idx, textBytes, textBytes.Length, IntPtr.Zero);
                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError(string.Format("sqlite3_bind_text error at idx {0}: code {1}", idx, rc));
                }
                return rc == ResultCode.SQLITE_OK;
            }

            /// <summary>
            /// Bindのidxは1から始まるので要注意！
            /// </summary>
            /// <param name="idx">bindする変数の位置</param>
            /// <param name="text">値</param>
            /// <returns></returns>
            public bool BindText(int idx, byte[] textBytes)
            {
                int rc = Plugin.sqlite3_bind_text(_stmt, idx, textBytes, textBytes == null ? 0 : textBytes.Length, IntPtr.Zero);
                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError(string.Format("sqlite3_bind_text error at idx {0}: code {1}", idx, rc));
                }
                return rc == ResultCode.SQLITE_OK;
            }

            /// <summary>
            /// Bindのidxは1から始まるので要注意！
            /// </summary>
            /// <param name="idx">bindする変数の位置</param>
            /// <param name="text">値</param>
            /// <returns></returns>
            public bool BindBlob(int idx, byte[] byteArray)
            {
                int rc = Plugin.sqlite3_bind_blob(_stmt, idx, byteArray, byteArray != null ? byteArray.Length : 0, IntPtr.Zero);
                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError(string.Format("sqlite3_bind_blob error at idx {0}: {1}", idx, rc));
                }
                return rc == ResultCode.SQLITE_OK;
            }

            /// <summary>
            /// Bindのidxは1から始まるので要注意！
            /// </summary>
            /// <param name="idx">bindする変数の位置</param>
            /// <param name="iValue">値</param>
            /// <returns></returns>
            public bool BindInt(int idx, int iValue)
            {
                int rc = Plugin.sqlite3_bind_int(_stmt, idx, iValue);
                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError(string.Format("sqlite3_bind_int error at idx {0}: code {1}", idx, rc));
                }
                return rc == ResultCode.SQLITE_OK;
            }

            /// <summary>
            /// Bindのidxは1から始まるので要注意！
            /// </summary>
            /// <param name="idx">bindする変数の位置</param>
            /// <param name="rValue">値</param>
            /// <returns></returns>
            public bool BindDouble(int idx, double rValue)
            {
                int rc = Plugin.sqlite3_bind_double(_stmt, idx, rValue);
                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError(string.Format("sqlite3_bind_double error at idx {0}: code {1}", idx, rc));
                }
                return rc == ResultCode.SQLITE_OK;
            }

            /// <summary>
            /// Bindのidxは1から始まるので要注意！
            /// </summary>
            /// <param name="idx">bindする変数の位置</param>
            /// <param name="lValue">値</param>
            /// <returns></returns>
            public bool BindLong(int idx, long lValue)
            {
                int rc = Plugin.sqlite3_bind_int64(_stmt, idx, lValue);
                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError(string.Format("sqlite3_bind_int64 error at idx {0}: code {1}", idx, rc));
                }
                return rc == ResultCode.SQLITE_OK;
            }

            /// <summary>
            /// Prepared Statementのクエリが終わる度に必ず呼ぶこと。
            /// </summary>
            /// <returns></returns>
            public bool Reset()
            {
                int rc = Plugin.sqlite3_reset(_stmt);
                if (rc != ResultCode.SQLITE_OK)
                {
                    ResultCode.CheckCriticalError(rc);
                    Debug.LogError(string.Format("sqlite3_reset error: code {0}", rc));
                }
                return rc == ResultCode.SQLITE_OK;
            }
        }
    }
}
#endif

//
//  ____  ________          __     _____  _   _ ______ _____
// |  _ \|  ____\ \        / /\   |  __ \| \ | |  ____|  __ \
// | |_) | |__   \ \  /\  / /  \  | |__) |  \| | |__  | |  | |
// |  _ <|  __|   \ \/  \/ / /\ \ |  _  /| . ` |  __| | |  | |
// | |_) | |____   \  /\  / ____ \| | \ \| |\  | |____| |__| |
// |____/|______|   \/  \/_/    \_\_|  \_\_| \_|______|_____/
//
//
// This file is generated by symbolgen.rb from libnative.
// Keep it untouched or ruin the abstraction!
//
// Author: ykst <<EMAIL>>
#if STANDALONE_SIMULATOR
using System;
using System.Runtime.InteropServices;

namespace StandaloneSimulator
{
    namespace LibNative.Sqlite3
    {
        public static class Plugin
        {
    #if UNITY_EDITOR_WIN
            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_open(byte[] zFilename, out IntPtr ppDB);
            public static Delegate_sqlite3_open sqlite3_open;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_open_v2(byte[] zFilename, out IntPtr ppDB, int flags, byte[] zVfs);
            public static Delegate_sqlite3_open_v2 sqlite3_open_v2;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_close(IntPtr db);
            public static Delegate_sqlite3_close sqlite3_close;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_exec(IntPtr db, byte[] zSql, IntPtr xCallback, IntPtr pArg, out IntPtr pzErrMsg);
            public static Delegate_sqlite3_exec sqlite3_exec;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_prepare_v2(IntPtr db, byte[] zSql, int nBytes, out IntPtr ppStmt, IntPtr pzTail);
            public static Delegate_sqlite3_prepare_v2 sqlite3_prepare_v2;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_bind_text(IntPtr pStmt, int i, byte[] zData, int nData, IntPtr xDel);
            public static Delegate_sqlite3_bind_text sqlite3_bind_text;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_bind_blob(IntPtr pStmt, int i, byte[] zData, int nData, IntPtr xDel);
            public static Delegate_sqlite3_bind_blob sqlite3_bind_blob;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_bind_int(IntPtr pStmt, int i, int iValue);
            public static Delegate_sqlite3_bind_int sqlite3_bind_int;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_bind_double(IntPtr pStmt, int i, double rValue);
            public static Delegate_sqlite3_bind_double sqlite3_bind_double;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_bind_int64(IntPtr pStmt, int i, long lValue);
            public static Delegate_sqlite3_bind_int64 sqlite3_bind_int64;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_bind_null(IntPtr pStmt, int i);
            public static Delegate_sqlite3_bind_null sqlite3_bind_null;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_reset(IntPtr pStmt);
            public static Delegate_sqlite3_reset sqlite3_reset;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_step(IntPtr pStmt);
            public static Delegate_sqlite3_step sqlite3_step;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_finalize(IntPtr pStmt);
            public static Delegate_sqlite3_finalize sqlite3_finalize;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_column_int(IntPtr pStmt, int i);
            public static Delegate_sqlite3_column_int sqlite3_column_int;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate long Delegate_sqlite3_column_int64(IntPtr pStmt, int i);
            public static Delegate_sqlite3_column_int64 sqlite3_column_int64;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate double Delegate_sqlite3_column_double(IntPtr pStmt, int i);
            public static Delegate_sqlite3_column_double sqlite3_column_double;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_column_bytes(IntPtr pStmt, int i);
            public static Delegate_sqlite3_column_bytes sqlite3_column_bytes;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate IntPtr Delegate_sqlite3_column_blob(IntPtr pStmt, int i);
            public static Delegate_sqlite3_column_blob sqlite3_column_blob;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate IntPtr Delegate_sqlite3_column_text(IntPtr pStmt, int i);
            public static Delegate_sqlite3_column_text sqlite3_column_text;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_column_type(IntPtr pStmt, int i);
            public static Delegate_sqlite3_column_type sqlite3_column_type;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_vfs_register(IntPtr pStmt, int makeDflt);
            public static Delegate_sqlite3_vfs_register sqlite3_vfs_register;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_vfs_unregister(IntPtr pVfs);
            public static Delegate_sqlite3_vfs_unregister sqlite3_vfs_unregister;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate IntPtr Delegate_sqlite3_vfs_find(byte[] zVfsName);
            public static Delegate_sqlite3_vfs_find sqlite3_vfs_find;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_threadsafe();
            public static Delegate_sqlite3_threadsafe sqlite3_threadsafe;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_wal_checkpoint_v2(IntPtr db, IntPtr zDb, int eMode, out int pnLog, out int pnCkpt);
            public static Delegate_sqlite3_wal_checkpoint_v2 sqlite3_wal_checkpoint_v2;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_wal_checkpoint(IntPtr db, IntPtr zDb);
            public static Delegate_sqlite3_wal_checkpoint sqlite3_wal_checkpoint;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_key(IntPtr db, byte[] pKey, int nKey);
            public static Delegate_sqlite3_key sqlite3_key;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_key_v2(IntPtr db, byte[] zDbName, byte[] pKey, int nKey);
            public static Delegate_sqlite3_key_v2 sqlite3_key_v2;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_rekey(IntPtr db, byte[] pKey, int nKey);
            public static Delegate_sqlite3_rekey sqlite3_rekey;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3_rekey_v2(IntPtr db, byte[] zDbName, byte[] pKey, int nKey);
            public static Delegate_sqlite3_rekey_v2 sqlite3_rekey_v2;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3mc_config(IntPtr db, byte[] paramName, int newValue);
            public static Delegate_sqlite3mc_config sqlite3mc_config;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate int Delegate_sqlite3mc_config_cipher(IntPtr db, byte[] cipherName, byte[] paramName, int newValue);
            public static Delegate_sqlite3mc_config_cipher sqlite3mc_config_cipher;

            [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
            public delegate byte[] Delegate_sqlite3mc_codec_data(IntPtr db, byte[] schemaName, byte[] paramName);
            public static Delegate_sqlite3mc_codec_data sqlite3mc_codec_data;

            internal static void LoadSymbols(IntPtr dllPtr)
            {
                sqlite3_open = Runtime.GetDllProc<Delegate_sqlite3_open>(dllPtr, "sqlite3_open");
                sqlite3_open_v2 = Runtime.GetDllProc<Delegate_sqlite3_open_v2>(dllPtr, "sqlite3_open_v2");
                sqlite3_close = Runtime.GetDllProc<Delegate_sqlite3_close>(dllPtr, "sqlite3_close");
                sqlite3_exec = Runtime.GetDllProc<Delegate_sqlite3_exec>(dllPtr, "sqlite3_exec");
                sqlite3_prepare_v2 = Runtime.GetDllProc<Delegate_sqlite3_prepare_v2>(dllPtr, "sqlite3_prepare_v2");
                sqlite3_bind_text = Runtime.GetDllProc<Delegate_sqlite3_bind_text>(dllPtr, "sqlite3_bind_text");
                sqlite3_bind_blob = Runtime.GetDllProc<Delegate_sqlite3_bind_blob>(dllPtr, "sqlite3_bind_blob");
                sqlite3_bind_int = Runtime.GetDllProc<Delegate_sqlite3_bind_int>(dllPtr, "sqlite3_bind_int");
                sqlite3_bind_double = Runtime.GetDllProc<Delegate_sqlite3_bind_double>(dllPtr, "sqlite3_bind_double");
                sqlite3_bind_int64 = Runtime.GetDllProc<Delegate_sqlite3_bind_int64>(dllPtr, "sqlite3_bind_int64");
                sqlite3_bind_null = Runtime.GetDllProc<Delegate_sqlite3_bind_null>(dllPtr, "sqlite3_bind_null");
                sqlite3_reset = Runtime.GetDllProc<Delegate_sqlite3_reset>(dllPtr, "sqlite3_reset");
                sqlite3_step = Runtime.GetDllProc<Delegate_sqlite3_step>(dllPtr, "sqlite3_step");
                sqlite3_finalize = Runtime.GetDllProc<Delegate_sqlite3_finalize>(dllPtr, "sqlite3_finalize");
                sqlite3_column_int = Runtime.GetDllProc<Delegate_sqlite3_column_int>(dllPtr, "sqlite3_column_int");
                sqlite3_column_int64 = Runtime.GetDllProc<Delegate_sqlite3_column_int64>(dllPtr, "sqlite3_column_int64");
                sqlite3_column_double = Runtime.GetDllProc<Delegate_sqlite3_column_double>(dllPtr, "sqlite3_column_double");
                sqlite3_column_bytes = Runtime.GetDllProc<Delegate_sqlite3_column_bytes>(dllPtr, "sqlite3_column_bytes");
                sqlite3_column_blob = Runtime.GetDllProc<Delegate_sqlite3_column_blob>(dllPtr, "sqlite3_column_blob");
                sqlite3_column_text = Runtime.GetDllProc<Delegate_sqlite3_column_text>(dllPtr, "sqlite3_column_text");
                sqlite3_column_type = Runtime.GetDllProc<Delegate_sqlite3_column_type>(dllPtr, "sqlite3_column_type");
                sqlite3_vfs_register = Runtime.GetDllProc<Delegate_sqlite3_vfs_register>(dllPtr, "sqlite3_vfs_register");
                sqlite3_vfs_unregister = Runtime.GetDllProc<Delegate_sqlite3_vfs_unregister>(dllPtr, "sqlite3_vfs_unregister");
                sqlite3_vfs_find = Runtime.GetDllProc<Delegate_sqlite3_vfs_find>(dllPtr, "sqlite3_vfs_find");
                sqlite3_threadsafe = Runtime.GetDllProc<Delegate_sqlite3_threadsafe>(dllPtr, "sqlite3_threadsafe");
                sqlite3_wal_checkpoint_v2 = Runtime.GetDllProc<Delegate_sqlite3_wal_checkpoint_v2>(dllPtr, "sqlite3_wal_checkpoint_v2");
                sqlite3_wal_checkpoint = Runtime.GetDllProc<Delegate_sqlite3_wal_checkpoint>(dllPtr, "sqlite3_wal_checkpoint");
                sqlite3_key = Runtime.GetDllProc<Delegate_sqlite3_key>(dllPtr, "sqlite3_key");
                sqlite3_key_v2 = Runtime.GetDllProc<Delegate_sqlite3_key_v2>(dllPtr, "sqlite3_key_v2");
                sqlite3_rekey = Runtime.GetDllProc<Delegate_sqlite3_rekey>(dllPtr, "sqlite3_rekey");
                sqlite3_rekey_v2 = Runtime.GetDllProc<Delegate_sqlite3_rekey_v2>(dllPtr, "sqlite3_rekey_v2");
                sqlite3mc_config = Runtime.GetDllProc<Delegate_sqlite3mc_config>(dllPtr, "sqlite3mc_config");
                sqlite3mc_config_cipher = Runtime.GetDllProc<Delegate_sqlite3mc_config_cipher>(dllPtr, "sqlite3mc_config_cipher");
                sqlite3mc_codec_data = Runtime.GetDllProc<Delegate_sqlite3mc_codec_data>(dllPtr, "sqlite3mc_codec_data");
            }
    #else

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_open(byte[] zFilename, out IntPtr ppDB);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_open_v2(byte[] zFilename, out IntPtr ppDB, int flags, byte[] zVfs);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_close(IntPtr db);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_exec(IntPtr db, byte[] zSql, IntPtr xCallback, IntPtr pArg, out IntPtr pzErrMsg);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_prepare_v2(IntPtr db, byte[] zSql, int nBytes, out IntPtr ppStmt, IntPtr pzTail);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_bind_text(IntPtr pStmt, int i, byte[] zData, int nData, IntPtr xDel);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_bind_blob(IntPtr pStmt, int i, byte[] zData, int nData, IntPtr xDel);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_bind_int(IntPtr pStmt, int i, int iValue);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_bind_double(IntPtr pStmt, int i, double rValue);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_bind_int64(IntPtr pStmt, int i, long lValue);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_bind_null(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_reset(IntPtr pStmt);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_step(IntPtr pStmt);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_finalize(IntPtr pStmt);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_column_int(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern long sqlite3_column_int64(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern double sqlite3_column_double(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_column_bytes(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern IntPtr sqlite3_column_blob(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern IntPtr sqlite3_column_text(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_column_type(IntPtr pStmt, int i);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_vfs_register(IntPtr pStmt, int makeDflt);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_vfs_unregister(IntPtr pVfs);

            [DllImport(LibNative.Runtime.Import)]
            public static extern IntPtr sqlite3_vfs_find(byte[] zVfsName);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_threadsafe();

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_wal_checkpoint_v2(IntPtr db, IntPtr zDb, int eMode, out int pnLog, out int pnCkpt);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_wal_checkpoint(IntPtr db, IntPtr zDb);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_key(IntPtr db, byte[] pKey, int nKey);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_key_v2(IntPtr db, byte[] zDbName, byte[] pKey, int nKey);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_rekey(IntPtr db, byte[] pKey, int nKey);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3_rekey(IntPtr db, byte[] zDbName, byte[] pKey, int nKey);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3mc_config(IntPtr db, byte[] paramName, int newValue);

            [DllImport(LibNative.Runtime.Import)]
            public static extern int sqlite3mc_config_cipher(IntPtr db, byte[] cipherName, byte[] paramName, int newValue);

            [DllImport(LibNative.Runtime.Import)]
            public static extern byte[] sqlite3mc_codec_data(IntPtr db, byte[] schemaName, byte[] paramName);
    #endif
        }
    }
}
#endif

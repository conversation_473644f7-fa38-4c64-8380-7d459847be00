// VisualStudioでシミュレートテストする場合ビルド後イベントに xcopy $(ProjectDir)LibNative $(TargetDir)LibNative /E/C/H/Y/I を追加すること。
// jenkinsでビルドされる時にxcopyが使えないエラーになるため、普段はビルド後イベントから外してある。

#if STANDALONE_SIMULATOR
using System;
using System.Linq;
using System.Text.Json;
using System.Diagnostics;
using MessagePack;
using Microsoft.Extensions.Caching.Memory;

namespace StandaloneSimulator
{
    public class EntryPoint
    {
    #if CYG_DEBUG
        public static Stopwatch _sw;
    #endif

#if !PUBLISHED
        public static int Main()
        {
            var requestStr = "3gARq1VubG9ja0ZsYWdzAa5SYWNlSW5zdGFuY2VJZM4ADcv5qlJhbmRvbVNlZWTOCo4uUKZTZWFzb24Cp1dlYXRoZXIBr0dyb3VuZENvbmRpdGlvbgGsUmFjZUhvcnNlTnVtCbJSYWNlSG9yc2VEYXRhQXJyYXmZ3gAwqkZyYW1lT3JkZXIFqFZpZXdlcklkzwAAAJ9OkFd3q1RyYWluZXJOYW1lpuaWsOexs61Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZAKuVHJhaW5lZENoYXJhSWQAqk5pY2tuYW1lSWQAp0NoYXJhSWTNA+mmQ2FyZElkzgABhwWlTW9iSWQAplJhcml0eQOrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmRgqdTa2lsbElkzgABhqulTGV2ZWwBp1N0YW1pbmHMhaVTcGVlZMyWo1Bvd8yPpEd1dHNio1dpesyNrFJ1bm5pbmdTdHlsZQOrUmFjZURyZXNzSWTOAAGHBa5DaGFyYUNvbG9yVHlwZQCnTnBjVHlwZQuqRmluYWxHcmFkZQOqUG9wdWxhcml0eQG3UG9wdWxhcml0eU1hcmtSYW5rQXJyYXmTAQEBs1Byb3BlckRpc3RhbmNlU2hvcnQDslByb3BlckRpc3RhbmNlTWlsZQa0UHJvcGVyRGlzdGFuY2VNaWRkbGUHslByb3BlckRpc3RhbmNlTG9uZwe2UHJvcGVyUnVubmluZ1N0eWxlTmlnZQG3UHJvcGVyUnVubmluZ1N0eWxlU2Vua28Ht1Byb3BlclJ1bm5pbmdTdHlsZVNhc2hpB7hQcm9wZXJSdW5uaW5nU3R5bGVPaWtvbWkFsFByb3Blckdyb3VuZFR1cmYHsFByb3Blckdyb3VuZERpcnQBqk1vdGl2YXRpb24DsFdpblNhZGRsZUlkQXJyYXmQr1JhY2VSZXN1bHRBcnJheZCmVGVhbUlkAKxUZWFtTWVtYmVySWQAqFRlYW1SYW5rALJTaW5nbGVNb2RlV2luQ291bnQAqEZhbkNvdW50AadJdGVtTnVtAKtJdGVtSWRBcnJheZC0TW90aXZhdGlvbkNoYW5nZUZsYWcAtEZyYW1lT3JkZXJDaGFuZ2VGbGFnAN4AMKpGcmFtZU9yZGVyCKhWaWV3ZXJJZACrVHJhaW5lck5hbWXArU93bmVyVmlld2VySWQAsE93bmVyVHJhaW5lck5hbWWgsVNpbmdsZU1vZGVDaGFyYUlkzKuuVHJhaW5lZENoYXJhSWTMq6pOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzR/qplJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmFfpVNwZWVkQqNQb3dOpEd1dHNWo1dpekasUnVubmluZ1N0eWxlAqtSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUFqlBvcHVsYXJpdHkGt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwcCCLNQcm9wZXJEaXN0YW5jZVNob3J0ArJQcm9wZXJEaXN0YW5jZU1pbGUGtFByb3BlckRpc3RhbmNlTWlkZGxlB7JQcm9wZXJEaXN0YW5jZUxvbmcGtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UFt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBrdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQW4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBLBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uBLBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwDeADCqRnJhbWVPcmRlcgOoVmlld2VySWQAq1RyYWluZXJOYW1lwK1Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZM0BBq5UcmFpbmVkQ2hhcmFJZM0BBqpOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzSBFplJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmFXpVNwZWVkaqNQb3dTpEd1dHM/o1dpei2sUnVubmluZ1N0eWxlA6tSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUDqlBvcHVsYXJpdHkJt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwIHCbNQcm9wZXJEaXN0YW5jZVNob3J0ArJQcm9wZXJEaXN0YW5jZU1pbGUEtFByb3BlckRpc3RhbmNlTWlkZGxlBbJQcm9wZXJEaXN0YW5jZUxvbmcEtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UEt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBbdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQa4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBbBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uBLBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwDeADCqRnJhbWVPcmRlcgGoVmlld2VySWQAq1RyYWluZXJOYW1lwK1Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZM0BDq5UcmFpbmVkQ2hhcmFJZM0BDqpOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzSBNplJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmFlpVNwZWVkRqNQb3drpEd1dHM0o1dpei6sUnVubmluZ1N0eWxlA6tSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUEqlBvcHVsYXJpdHkHt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwgGA7NQcm9wZXJEaXN0YW5jZVNob3J0ArJQcm9wZXJEaXN0YW5jZU1pbGUGtFByb3BlckRpc3RhbmNlTWlkZGxlB7JQcm9wZXJEaXN0YW5jZUxvbmcGtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UEt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBbdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQa4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBbBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uBLBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwDeADCqRnJhbWVPcmRlcgmoVmlld2VySWQAq1RyYWluZXJOYW1lwK1Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZM0Bcq5UcmFpbmVkQ2hhcmFJZM0BcqpOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzSCxplJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmFlpVNwZWVkSqNQb3depEd1dHMvo1dpekisUnVubmluZ1N0eWxlA6tSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUEqlBvcHVsYXJpdHkEt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwkFBLNQcm9wZXJEaXN0YW5jZVNob3J0AbJQcm9wZXJEaXN0YW5jZU1pbGUBtFByb3BlckRpc3RhbmNlTWlkZGxlBLJQcm9wZXJEaXN0YW5jZUxvbmcFtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UEt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBbdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQa4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBbBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uBLBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwDeADCqRnJhbWVPcmRlcgKoVmlld2VySWQAq1RyYWluZXJOYW1lwK1Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZM0Bxq5UcmFpbmVkQ2hhcmFJZM0BxqpOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzSEFplJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmF8pVNwZWVkWaNQb3ddpEd1dHM1o1dpei2sUnVubmluZ1N0eWxlA6tSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUDqlBvcHVsYXJpdHkFt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwYEB7NQcm9wZXJEaXN0YW5jZVNob3J0BrJQcm9wZXJEaXN0YW5jZU1pbGUHtFByb3BlckRpc3RhbmNlTWlkZGxlBrJQcm9wZXJEaXN0YW5jZUxvbmcCtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UEt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBbdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQa4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBbBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uBLBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwDeADCqRnJhbWVPcmRlcgaoVmlld2VySWQAq1RyYWluZXJOYW1lwK1Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZM0B4K5UcmFpbmVkQ2hhcmFJZM0B4KpOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzSEfplJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmFDpVNwZWVkSqNQb3dnpEd1dHNOo1dpekOsUnVubmluZ1N0eWxlAqtSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUEqlBvcHVsYXJpdHkDt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwMIArNQcm9wZXJEaXN0YW5jZVNob3J0ArJQcm9wZXJEaXN0YW5jZU1pbGUGtFByb3BlckRpc3RhbmNlTWlkZGxlB7JQcm9wZXJEaXN0YW5jZUxvbmcGtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UFt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBrdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQW4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBLBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uBLBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwDeADCqRnJhbWVPcmRlcgeoVmlld2VySWQAq1RyYWluZXJOYW1lwK1Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZM0B9q5UcmFpbmVkQ2hhcmFJZM0B9qpOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzSE1plJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmFrpVNwZWVkZaNQb3dbpEd1dHNBo1dpelisUnVubmluZ1N0eWxlA6tSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUFqlBvcHVsYXJpdHkCt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwQDBbNQcm9wZXJEaXN0YW5jZVNob3J0ArJQcm9wZXJEaXN0YW5jZU1pbGUEtFByb3BlckRpc3RhbmNlTWlkZGxlBbJQcm9wZXJEaXN0YW5jZUxvbmcEtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UEt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBbdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQa4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBbBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uArBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwDeADCqRnJhbWVPcmRlcgSoVmlld2VySWQAq1RyYWluZXJOYW1lwK1Pd25lclZpZXdlcklkALBPd25lclRyYWluZXJOYW1loLFTaW5nbGVNb2RlQ2hhcmFJZM0B/q5UcmFpbmVkQ2hhcmFJZM0B/qpOaWNrbmFtZUlkAKdDaGFyYUlkAaZDYXJkSWQApU1vYklkzSE9plJhcml0eQGrVGFsZW50TGV2ZWwBqlNraWxsQXJyYXmQp1N0YW1pbmFdpVNwZWVkW6NQb3dVpEd1dHMpo1dpelCsUnVubmluZ1N0eWxlBKtSYWNlRHJlc3NJZAGuQ2hhcmFDb2xvclR5cGUAp05wY1R5cGUAqkZpbmFsR3JhZGUEqlBvcHVsYXJpdHkIt1BvcHVsYXJpdHlNYXJrUmFua0FycmF5kwUJBrNQcm9wZXJEaXN0YW5jZVNob3J0ArJQcm9wZXJEaXN0YW5jZU1pbGUGtFByb3BlckRpc3RhbmNlTWlkZGxlB7JQcm9wZXJEaXN0YW5jZUxvbmcGtlByb3BlclJ1bm5pbmdTdHlsZU5pZ2UDt1Byb3BlclJ1bm5pbmdTdHlsZVNlbmtvBLdQcm9wZXJSdW5uaW5nU3R5bGVTYXNoaQW4UHJvcGVyUnVubmluZ1N0eWxlT2lrb21pBrBQcm9wZXJHcm91bmRUdXJmBrBQcm9wZXJHcm91bmREaXJ0AapNb3RpdmF0aW9uArBXaW5TYWRkbGVJZEFycmF5kK9SYWNlUmVzdWx0QXJyYXmQplRlYW1JZACsVGVhbU1lbWJlcklkAKhUZWFtUmFuawCyU2luZ2xlTW9kZVdpbkNvdW50AKhGYW5Db3VudACnSXRlbU51bQCrSXRlbUlkQXJyYXmQtE1vdGl2YXRpb25DaGFuZ2VGbGFnALRGcmFtZU9yZGVyQ2hhbmdlRmxhZwCoUmFjZVR5cGUGrFNlbGZFdmFsdWF0ZQCwT3Bwb25lbnRFdmFsdWF0ZQCvU2NvcmVDYWxjVGVhbUlkALVTdXBwb3J0Q2FyZFNjb3JlQm9udXMAuENoYWxsZW5nZU1hdGNoRGlmZmljdWx0eQC4Q2hhbGxlbmdlTWF0Y2hSZXRyeUNvdW50AKtSZXNvdXJjZVZlcqgxMDAwODUwMKpEZWJ1Z1BhcmFtwA==";
            var requestBytes = Convert.FromBase64String(requestStr);
            var request = MessagePackSerializer.Deserialize<RaceSimulateRequest>(requestBytes);
            request.resource_ver = "00000000";
            Execute(request);
            return 0;
        }
#endif

        public static RaceSimulateResponse? Execute(
            RaceSimulateRequest request,
            IMemoryCache? memoryCache = null,
            string? logDir = null
        )
        {
            // 人気を1~ → 0~にするなど。
            request.DeserializePostProcess();

#if CYG_DEBUG
            _sw = new Stopwatch();
            _sw.Start();
#endif
            RaceSimulateLogger.Init();
            if (logDir != null)
            {
                RaceSimulateLogger.SetLogDir(logDir);
            }
            Debug.Log("-------------- Main enter --------------");

            var loadRaceInfo = CreateLoadRaceInfo(request);
#if CYG_DEBUG
            Debug.Log($"CreateLoadRaceInfo.:{EntryPoint._sw.ElapsedMilliseconds}ms");
#endif
            // レースシミュレーターWork更新
            WorkRaceSimulatorDataManager.CreateInstance();
            WorkRaceSimulatorDataManager.Instance.Apply(loadRaceInfo.OverwriteCourseSetInfoArray);
            
            var resoponse = LoadAndSimulate(request.resource_ver, loadRaceInfo, memoryCache);

#if CYG_DEBUG
            _sw.Stop();
            Debug.Log($"End:{_sw.ElapsedMilliseconds}ms");
#endif
            Debug.Log($"-------------- Main exit. --------------\n");
            Destroy();
            RaceSimulateLogger.Term();
            return resoponse;
        }

        private static RaceInitializer.LoadRaceInfo CreateLoadRaceInfo(RaceSimulateRequest simRequest)
        {
#if CYG_DEBUG
            SetupDebug(simRequest);
#endif

            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                simRequest.race_instance_id,
                simRequest.race_horse_data_array,
                simRequest.random_seed,
                (Gallop.RaceDefine.RaceType)simRequest.race_type,
                (Gallop.RaceDefine.Season)simRequest.season,
                (Gallop.RaceDefine.Weather)simRequest.weather,
                (Gallop.RaceDefine.GroundCondition)simRequest.ground_condition,
                opponentEvaluate: simRequest.opponent_evaluate,
                scoreCalcTeamId: simRequest.score_calc_team_id,
                supportCardScoreBonus: simRequest.support_card_bonus,
                selfEvaluate: simRequest.self_evaluate,
                raceDifficulty: (Gallop.RaceDefine.Difficulty)simRequest.challenge_match_difficulty,
                unlockFlags: simRequest.unlock_flags,
                overwriteCourseSetInfoArray: simRequest.course_set_info_array
            );

            return new RaceInitializer.LoadRaceInfo(ref buildParam);
        }

#if CYG_DEBUG
        /// <summary>
        /// デバッグ設定初期化。
        /// </summary>
        private static void SetupDebug(RaceSimulateRequest req)
        {
            RaceSimulateDebugger.InitFixFinishOrder();
            RaceSimulateDebugger.InitFixBashinDiffBehind();
            RaceSimulateDebugger.InitFixBashinDiffFromTop();

            if (req.debug_param != null)
            {
                if (req.debug_param.debug_result_rank != null)
                {
                    foreach (var fixFinishOrder in req.debug_param.debug_result_rank)
                    {
                        int horseIndex = RaceUtil.FrameOrder2HorseIndex(fixFinishOrder.frame_order);
                        int finishOrder = fixFinishOrder.finish_order - 1;
                        var target = RaceSimulateDebugger.FixFinishOrderTarget.HorseIndex_00 + horseIndex;
                        RaceSimulateDebugger.FixFinishOrderDic[target] = finishOrder;
                    }
                }

                if (req.debug_param.fix_bashin_diff_behind_array != null)
                {
                    foreach (var fixBashinDiff in req.debug_param.fix_bashin_diff_behind_array)
                    {
                        int horseIndex = RaceUtil.FrameOrder2HorseIndex(fixBashinDiff.frame_order);
                        RaceSimulateDebugger.SetFixBashinDiffBehind(horseIndex, fixBashinDiff.bashin_diff_behind);
                    }
                }

                if (req.debug_param.fix_bashin_diff_from_top_array != null)
                {
                    foreach (var fixBashinDiff in req.debug_param.fix_bashin_diff_from_top_array)
                    {
                        int horseIndex = RaceUtil.FrameOrder2HorseIndex(fixBashinDiff.frame_order);
                        RaceSimulateDebugger.SetFixBashinDiffFromTop(horseIndex, fixBashinDiff.bashin_diff_from_top);
                    }
                }

                RaceSimulateDebugger.IsRecordIntervalSimulate = req.debug_param.is_record_interval_simulate != 0;
            }
        }
#endif

        private static void Load(string resourceVersion, RaceInitializer.LoadRaceInfo loadRaceInfo, IMemoryCache? memoryCache)
        {
            // init
            HorseCompeteTopRunningNumber.Init();
            HorseCompeteFightRunningNumber.Init();

            // リソースフォルダのパス決定。
            RaceSimulateResource.CreateInstance();
            RaceSimulateResource.Instance.Init(resourceVersion, memoryCache);

            // Master初期化。
            MasterDataManager.CreateInstance();
            MasterDataManager.Instance.SetupMasterData();
#if CYG_DEBUG
            Debug.Log($"MasterDataManager.SetupMasterData:{EntryPoint._sw.ElapsedMilliseconds}ms");
#endif
            MasterManager.CreateInstance();
            MasterManager.Instance.Init();
#if CYG_DEBUG
            Debug.Log($"MasterManager.Init:{EntryPoint._sw.ElapsedMilliseconds}ms");
#endif

            var masterRaceInstance = MasterManager.Instance.MasterRaceInstance.Get(loadRaceInfo.RaceInstanceId);
            var masterRace = MasterManager.Instance.MasterRace.Get(masterRaceInstance.RaceId);
            var masterCourseSet = MasterManager.Instance.MasterRaceCourseSet.Get(masterRace.CourseSet);
            // paramdefine読み込み。
            RaceSimulateResource.Instance.LoadRaceParamDefine();

            // コースパス読み込み。
            RaceSimulateResource.Instance.LoadCousePath(
                masterCourseSet.RaceTrackId,
                masterCourseSet.Distance,
                (Gallop.RaceDefine.GroundType)masterCourseSet.Ground,
                (Gallop.RaceDefine.CourseAround)masterCourseSet.Inout,
                loadRaceInfo.RaceType);

            // オーバーランコースパス読み込み。
            RaceSimulateResource.Instance.LoadOverRunCousePath(
                masterCourseSet.RaceTrackId,
                (Gallop.RaceDefine.GroundType)masterCourseSet.Ground,
                loadRaceInfo.RaceType);

            // コースイベント読み込み。
            RaceSimulateResource.Instance.LoadCourseEvent(masterCourseSet.Id);
        }

        private static void Destroy()
        {
            RaceSimulateResource.DestroyInstance();
            MasterDataManager.DestroyInstance();
            MasterManager.DestroyInstance();
        }

        private static RaceSimulateResponse LoadAndSimulate(string resourceVersion, RaceInitializer.LoadRaceInfo loadRaceInfo, IMemoryCache? memoryCache)
        {
            Load(resourceVersion, loadRaceInfo, memoryCache);

            var raceInfo = RaceInitializer.CreateRaceInfo(loadRaceInfo);

            RaceManagerSimulate.CreateInstance();

            var simulator = RaceManagerSimulate.Instance;
            simulator.Init(
                raceInfo,
                RaceSimulateResource.Instance.CourseParamTable,
                RaceSimulateResource.Instance.ParamDefine,
                RaceSimulateResource.Instance.CoursePath,
                RaceSimulateResource.Instance.OverRunCoursePath);

            // シミュレート。
            simulator.Simulate(true);

            // シミュレート結果。
            var response = OutputSimulate(loadRaceInfo.ScoreCalcTeamId);

            RaceManagerSimulate.DestroyInstance();

            return response;
        }

        private static RaceSimulateResponse OutputSimulate(int scoreCalcTeamId)
        {
            var simulator = RaceManagerSimulate.Instance;
            var simData = simulator.GetSimulateData();

            // RaceSimulateDataをBase64文字列にシリアライズ。
            var exportBytes = Gallop.CompressUtil.Compress(simData.Serialize(true, true));

            var res = new RaceSimulateResponse
            {
                race_scenario_version = simData.Header.Version,
                race_scenario_byte = exportBytes.Length,
                race_scenario_compressed = exportBytes,
            };

            // キャラのレース結果構築。
            {
                var horseInfoArray = RaceManagerSimulate.Instance.GetHorseRaceInfos();
                res.result_horse_num = horseInfoArray.Length;
                res.result_horse_array = horseInfoArray.Select(x => new RaceResultHorseData(x, horseInfoArray)).ToArray();
            }

            // チームのレース結果構築。
            {
                var teamList = simulator.GetTeamInfoList();
                var team = teamList.FirstOrDefault(t => t.TeamId == scoreCalcTeamId && t.TeamId != Gallop.RaceDefine.TEAM_ID_NULL);
                res.result_team_num = team != null ? 1 : 0;
                if (res.result_team_num > 0)
                {
                    res.result_team = new TeamResult();
                    {
                        res.result_team.team_id= team.TeamId;

                        // チームとして獲得したスコア情報の返却値構築。（個人の獲得スコアとは別）
                        var scoreList = team.ScoreList;
                        res.result_team.team_score_num= scoreList.Count;
                        res.result_team.team_score_array = new ScoreData[scoreList.Count];
                        int scoreIndex = 0;
                        foreach (var score in scoreList)
                        {
                            res.result_team.team_score_array[scoreIndex] = new ScoreData(score.raw_score_id, score.num, score.score, score.bonus_array);
                            scoreIndex++;
                        }

                        // このチームのメンバーのスコアと、チームとして獲得したスコアの合算値の返却。
                        res.result_team.team_total_score = team.CalcMemberAndTeamScoreTotal();
                    }
                }
            }

            return res;
        }
    }
}
#endif
using System.Collections.Generic;
#if STANDALONE_SIMULATOR || UNITY_EDITOR

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動単位インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public partial interface ISkillDetail
    {
        /// <summary>
        /// このISkillDetailを所有するキャラ。
        /// </summary>
        IHorseRaceInfoSimulate OwnerHorse { get; }

        /// <summary>
        /// skill_data.csvの該当データ。
        /// .NET用
        /// </summary>
        ISkillDataAccessor SkillMasterSimulate { get; }

        /// <summary>
        /// この効果を発動した時のクールダウン時間。
        /// </summary>
        float DefaultCoolDownTime { get; }

        /// <summary> スキル効果のクールダウンタイムを使用するスキル発動条件を持つかどうか </summary>
        /// <remarks>
        /// これがTrueになるとスキル効果のクールダウンタイムを元にスキル効果が発動されるようになります
        /// </remarks>
        bool IsUseSkillDetailCoolDownTime { get; }

        /// <summary> スキル効果が発動してもスキル発動としないかどうか </summary>
        /// <remarks>
        /// これがTrueになるとこのスキル効果が発動してもスキル発動しないようになります
        /// </remarks>
        bool IsNotActivateSkill { get; }

        /// <summary>
        /// スキル効果追加。
        /// </summary>
        /// <param name="ability"></param>
        void AddAbility( ISkillAbility ability );

        /// <summary>
        /// スキル発動前提条件設定。
        /// </summary>
        void SetPreTrigger(ISkillTriggerCollection trigger);
        /// <summary>
        /// スキル発動条件設定。
        /// </summary>
        void SetTrigger( ISkillTriggerCollection trigger );

        /// <summary>
        /// スキル効果時間などの更新
        /// </summary>
        bool UpdateSkill(float deltaTime);

        /// <summary>
        /// 発動していない効果の発動条件チェックと発動更新。
        /// </summary>
        bool UpdateTriggerAndActivate(float deltaTime);

        /// <summary>
        /// 効果起動中かどうか。
        /// </summary>
        bool IsActivated { get; }

        /// <summary>
        /// 効果起動。
        /// </summary>
        void Activate();

        /// <summary>
        /// クールダウン時間更新
        /// </summary>
        bool CheckCoolDown(float deltaTime);

        /// <summary>
        /// 効果停止。
        /// </summary>
        void Stop();

        /// <summary>
        /// スキル効果残り時間。
        /// </summary>
        /// <remarks>発動していない場合は0が返る。</remarks>
        float LeftTime { get; }

        /// <summary>
        /// 任意のタイプのスキルかどうか
        /// </summary>
        /// <returns></returns>
        bool IsTargetSkill(params Gallop.SkillDefine.SkillAbilityType[] targetSkillAttributeArray);
        
        /// <summary>
        /// 発動予約されたスキル効果かどうか
        /// </summary>
        /// <returns></returns>
        public bool IsReservedActivate();
        
#if CYG_DEBUG
        /// <summary>
        /// スキル効果のクールダウンタイム
        /// </summary>
        float CoolDownTime { get; }
#endif
    }
}

#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動単位インターフェース。
    /// .NET/GALLOP兼用
    /// </summary>
    //-------------------------------------------------------------------
    public partial interface ISkillDetail
    {
#if GALLOP
        //-------------------------------------------------------------------
        // GALLOP専用（レース再生用）
        //-------------------------------------------------------------------
        /// <summary>
        /// skill_data.csvの該当データ。
        /// GALLOP用
        /// </summary>
        Gallop.MasterSkillData.SkillData SkillMaster { get; }
        
        /// <summary>
        /// スキル効果エフェクト名。
        /// </summary>
        string SkillEffectName { get; set; }
        
        /// <summary>
        /// スキル発動エフェクト名。
        /// </summary>
        string ActivateEffectName { get; set; }

        /// <summary>
        /// スキル効果SE。
        /// </summary>
        string MainCueName { get; set; }
        
        /// <summary>
        /// スキル発動SE。
        /// </summary>
        string ActivateCueName { get; set; }
#endif
        
        /// <summary>
        /// スキルId。
        /// </summary>
        int SkillId { get; }
        /// <summary>
        /// スキルレベル。
        /// </summary>
        int SkillLevel { get; }
        
        /// <summary>
        /// スキル効果取得。
        /// </summary>
        List<ISkillAbility> Abilities { get; }

        /// <summary>
        /// デバフかどうか。
        /// </summary>
        bool IsDebuff { get; set; }

        /// <summary>
        /// スキルのカテゴリ。
        /// </summary>
        Gallop.SkillDefine.SkillCategory Category { get; set; }
    }
}
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using Gallop;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動単位。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillDetail : ISkillDetail
    {
        //--------------------------------------------------------------------------------
        // GALLOP/シミュレート兼用処理
        //--------------------------------------------------------------------------------
        
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        private const int ABILITY_CAPACITY = 3;
        
        /// <summary>スキルId。</summary>
        public int SkillId
        {
            get
            {
                int id = 0;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
                id = SkillMasterSimulate?.Id ?? id;
#endif
#if GALLOP
                id = SkillMaster?.Id ?? id;
#endif
                return id;
            }
        }

        /// <summary>スキルレベル。</summary>
        public int SkillLevel { get; private set; }
    
        private bool _isDebuff;
#if CYG_DEBUG
        private bool _isDebuffInitialized;
#endif
        /// <summary>このスキルがデバフかどうか。</summary>
        public bool IsDebuff
        {
            get
            {
#if CYG_DEBUG
                Debug.Assert(_isDebuffInitialized);
#endif
                return _isDebuff;
            }
            set 
            { 
                _isDebuff = value;
#if CYG_DEBUG
                _isDebuffInitialized = true;
#endif
            }
        }
        
        /// <summary>スキルのカテゴリ分け。</summary>
        public SkillDefine.SkillCategory Category { get; set; }

        /// <summary>発動する効果。</summary>
        public List<ISkillAbility> Abilities { get; private set; }

#if GALLOP
        //--------------------------------------------------------------------------------
        // GALLOP専用処理
        //--------------------------------------------------------------------------------
        
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary> skill_data.csvの該当データ。</summary>
        public Gallop.MasterSkillData.SkillData SkillMaster { get; }
        
        /// <summary>発動時エフェクト。</summary>
        public string ActivateEffectName { get; set; }
        /// <summary>効果エフェクト。※発動時エフェクトから一定時間後に再生される。</summary>
        public string SkillEffectName { get; set; }
        /// <summary>発動時SE。</summary>
        public string ActivateCueName { get; set; } = string.Empty;
        /// <summary>効果SE。※発動時SEから一定時間後に再生される。</summary>
        public string MainCueName { get; set; } = string.Empty;
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public SkillDetail( 
            MasterSkillData.SkillData masterSkill,
            int skillLevel)
        {
            SkillMaster = masterSkill;
            SkillLevel = skillLevel;
            Abilities = new List<ISkillAbility>( ABILITY_CAPACITY );
        }
#endif
        
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        //--------------------------------------------------------------------------------
        // シミュレート専用処理
        //--------------------------------------------------------------------------------
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>SkillBaseが持つSkillDetailの識別番号。</summary>
        private readonly int _detailIndex = -1;
        /// <summary>スキル発動条件。</summary>
        private ISkillTriggerCollection _trigger;
        /// <summary>スキル発動前提条件。</summary>
        private ISkillTriggerCollection _preTrigger;

        /// <summary>スキル発動前提条件チェックが必要かどうか。</summary>
        private bool _isNeedPreTriggerCheck;

        public float DefaultCoolDownTime { get; private set;  }
        /// <summary> このスキル効果が持つクールダウンタイム </summary>
        private float _coolDownTime;

        /// <summary> スキルのクールダウンタイムではなくこのスキル効果のクールダウンタイムを使用するかどうか </summary>
        public bool IsUseSkillDetailCoolDownTime { get; private set; } = false;
        /// <summary> このスキル効果が発動してもスキル発動させないかどうか </summary>
        public bool IsNotActivateSkill { get; private set; } = false;

        /// <summary>効果時間が無限かどうか。</summary>
        private readonly bool _isTimeInfinite;
        /// <summary>効果時間の残り。</summary>
        public float LeftTime { get; private set; }
        /// <summary>効果時間計算機。</summary>
        private ISkillAbilityTimeCalculator _abilityTimeCalculator;

        /// <summary>レースの時間取得。</summary>
        private readonly IRaceTimeAccessor _timeAccessor;
        /// <summary>スキル発動イベント記録用。</summary>
        private readonly IRaceEventRecorder _skillEventRecorder;

        /// <summary> skill_data.csvの該当データ。 </summary>
        public ISkillDataAccessor SkillMasterSimulate { get; private set; }

        /// <summary>スキルのレアリティ。</summary>
        private SkillDefine.SkillRarity Rarity => (SkillDefine.SkillRarity)SkillMasterSimulate.Rarity;
        /// <summary>スコア計算に使う変数。</summary>
        public int SkillGroupRate => SkillMasterSimulate.GroupRate;
    #if CYG_DEBUG
        public float CoolDownTime => _coolDownTime;
    #endif
        /// <summary>スキル所有者。</summary>
        public IHorseRaceInfoSimulate OwnerHorse { get; private set; }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public SkillDetail( 
            IHorseRaceInfoSimulate owner,
            IRaceTimeAccessor timeAccessor,
            IRaceEventRecorder skillEventRecorder,
            IRaceHorseAccessor horseAccessor,
            ISkillDataAccessor masterSkill,
            Gallop.RaceParamDefine.SkillParam skillParam,
            int skillLevel,
            int detailIndex, 
            float leftTime,
            SkillDefine.SkillAbilityTimeUsage timeUsage, 
            float coolDownTime, 
            int courseDistance, 
            int abilityTimeDivideDistance, 
            int coolDownTimeDivideDistance)
        {
            OwnerHorse = owner;
            _timeAccessor = timeAccessor;
            _skillEventRecorder = skillEventRecorder;
            SkillMasterSimulate = masterSkill;
            SkillLevel = skillLevel;
            _detailIndex = detailIndex;
            Abilities = new List<ISkillAbility>( ABILITY_CAPACITY );
            
            // 効果時間はコース距離に比例して可変する。
            float abilityTimeByDistanceRate = CalcAbilityTimeByDistanceRate(courseDistance, abilityTimeDivideDistance);
            float abilityTimeDefault = leftTime * abilityTimeByDistanceRate;
            _abilityTimeCalculator = SkillAbilityTimeCalculatorCreator.CreateCalculator(
                owner,
                timeUsage,
                abilityTimeDefault,
                horseAccessor,
                skillParam,
                abilityTimeByDistanceRate);
            _isTimeInfinite = (abilityTimeDefault < 0.0f);

            // クールダウン時間はコース距離に比例して可変する。
            DefaultCoolDownTime = CalcCoolDownTimeByDistance(coolDownTime, courseDistance, coolDownTimeDivideDistance);
            _coolDownTime = 0f;
        }

        /// <summary>
        /// 効果継続時間計算。
        /// </summary>
        /// <remarks>  継続時間基礎値(csvに入力されている値)に乗算して距離ごとに時間を出力するのが目的 </remarks>
        /// <param name="courseDistance">コース距離。</param>
        /// <param name="abilityTimeDivideDistance">継続時間基礎値が、コース距離の何mを基準にしているか。</param>
        /// <remarks>
        /// 効果継続時間はcsv上で1000m当たりの時間として入力されているため、実際のコース距離によって倍率を掛ける。
        /// </remarks>
        private float CalcAbilityTimeByDistanceRate(int courseDistance, int abilityTimeDivideDistance)
        {
            return (float) courseDistance / abilityTimeDivideDistance;
        }

        /// <summary>
        /// クールダウン時間計算。
        /// </summary>
        /// <param name="coolDownTime">クールダウン時間基礎値。csvに入力されている値を想定。</param>
        /// <param name="courseDistance">コース距離。</param>
        /// <param name="coolDownTimeDiviceDistance">クールダウン時間基礎値が、コース距離の何mを基準にしているか。</param>
        /// <remarks>
        /// クールダウン時間はcsv上で1000m当たりの時間として入力されているため、実際のコース距離によって倍率を掛ける。
        /// </remarks>
        private float CalcCoolDownTimeByDistance(float coolDownTime, int courseDistance, int coolDownTimeDiviceDistance)
        {
            float rate = (float)courseDistance / coolDownTimeDiviceDistance;
            return coolDownTime * rate;
        }

        //---------------------------------------------------------------
        public virtual void AddAbility(ISkillAbility ability)
        {
            Abilities.Add(ability);
        }

        //---------------------------------------------------------------
        public void SetPreTrigger(ISkillTriggerCollection trigger)
        {
            _preTrigger = trigger;
            _isNeedPreTriggerCheck = !(trigger is SkillTriggerCollectionNull);
        }
        
        //---------------------------------------------------------------
        public void SetTrigger( ISkillTriggerCollection trigger )
        {
            _trigger = trigger;
            IsUseSkillDetailCoolDownTime = _preTrigger.IsContainsUseSkillDetailCoolDownTimeTrigger() ||
                                           _trigger.IsContainsUseSkillDetailCoolDownTimeTrigger();
            IsNotActivateSkill = _preTrigger.IsContainsNotActivateSkillTrigger() ||
                                 _trigger.IsContainsNotActivateSkillTrigger();
        }

        //---------------------------------------------------------------
        public bool UpdateSkill(float deltaTime)
        {
            Debug.Assert(IsActivated, "効果発動していないDetailのUpdateが呼ばれている");

            if (IsActivated)
            {
                // スキル効果追加発動
                // SkillDetail::Activateだとスキル発動として扱われてしまうのでアビリティの方だけActivateする
                for (int i = 0, cnt = Abilities.Count; i < cnt; ++i)
                {
                    int additionalActivateCount = Abilities[i].CalcAdditionalActivateCount();
                    for (int j = 0; j < additionalActivateCount; j++)
                    {
                        Abilities[i].Activate(true);
                    }
                }

                // スキル効果時間延長処理
                if (!_isTimeInfinite)
                {
                    _abilityTimeCalculator.UpdateAbilityTime();
                    float additionalAbilityTime = _abilityTimeCalculator.AdditionalAbilityTime;
                    if (additionalAbilityTime > 0f)
                    {
                        // 効果時間延長更新
                        LeftTime += additionalAbilityTime;
                        foreach (var ability in Abilities)
                        {
                            ability.AddAbilityTime(additionalAbilityTime);
                        }
                    }
                }
            }

            return UpdateLeftTime(deltaTime);
        }
        
        //---------------------------------------------------------------
        public bool UpdateLeftTime(float deltaTime)
        {
            Debug.Assert(IsActivated, "効果発動していないDetailのUpdateLeftTimeが呼ばれている");

            // 残り時間が切れたら終了。
            if(!CheckLeftTime(deltaTime))
            {
                Stop();
                return true;
            }

            return false;
        }
        public bool UpdateTriggerAndActivate(float deltaTime)
        {
            Debug.Assert(!IsActivated, "効果発動しているDetailのUpdateTriggerAndActivateが呼ばれている");

            // 前提条件を満たしているかチェック。満たしていないなら発動条件のチェックに進まない。
            if (!UpdatePreTrigger(deltaTime))
            {
                return false;
            }
            
            // 発動条件を満たしたら発動。
            _trigger.Update(deltaTime);
            if(_trigger.IsActivatedAny())
            {
                Activate();
                return true;
            }

            return false;
        }

        /// <summary>
        /// 前提条件チェック。
        /// </summary>
        /// <returns>前提条件が無いか、既に満たしているならtrue</returns>
        private bool UpdatePreTrigger(float deltaTime)
        {
            // もし他スキルによってスキル発動を指示されていてたら無条件で発動する
            // 発動対象になるのはスキル効果1のみ
            if (IsReservedActivate())
            {
                return true;
            }

            // 前提条件チェック不要。※無い、もしくは既に満たしている。
            if (!_isNeedPreTriggerCheck)
            {
                return true;
            }
            
            // 前提条件を満たしているかチェック。
            _preTrigger.Update(deltaTime);
            
            if (_preTrigger.IsActivatedAny())
            {
                // 一度条件を満たしたら以後チェックする必要は無い。
                _isNeedPreTriggerCheck = false;
                return true;
            }
            else
            {
                return false;
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 残り時間が残っているかorチェック不要かどうか。
        /// </summary>
        //---------------------------------------------------------------
        private bool CheckLeftTime( float deltaTime )
        {
            if(_isTimeInfinite)
            {
                return true;
            }

            LeftTime -= deltaTime;
            return LeftTime > 0.0f;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// クールダウン時間更新
        /// </summary>
        /// <returns> クールダウンが0になって次のスキル発動可能ならtrue </returns>
        //---------------------------------------------------------------
        public bool CheckCoolDown(float deltaTime)
        {
            return RaceUtil.UpdateTimer(ref _coolDownTime, deltaTime);
        }

        //---------------------------------------------------------------
        private int CalcTargetFlags()
        {
            var ability = Abilities.FirstOrDefault();
            if (ability == null)
            {
                return 0;
            }
            
            int retTargetFlags = 0;
            foreach( var target in ability.AbilityTargets )
            {
                retTargetFlags |= 1 << target.HorseIndex;
            }
            return retTargetFlags;
        }

        //---------------------------------------------------------------
        public bool IsActivated { get; protected set; }

        //---------------------------------------------------------------
        public void Activate()
        {
            IsActivated = true;

            // SKillAbilityTypeの22,27の配列
            // #151218の対応で速度スキルの22,27のみを対象とするスキルが登場したためそのための定義。
            SkillDefine.SkillAbilityType[] positiveEffectSpeedSkill = { SkillDefine.SkillAbilityType.CurrentSpeedWithNaturalDeceleration, SkillDefine.SkillAbilityType.TargetSpeed };

            // 効果継続時間リセット。
            LeftTime = _abilityTimeCalculator.AbilityTime;

            // 効果発動。
            for (int i = 0, cnt = Abilities.Count; i < cnt; ++i)
            {
                Abilities[i].Activate(false);
            }

            // スキル発動イベントの記録。レース再生時の演出に使用する。
            RecordSkillEvent(LeftTime);

        #if CYG_DEBUG
            OwnerHorse.SkillManager.DbgAddActivatedSkills(SkillId, _timeAccessor.AccumulateTimeSinceStart, _detailIndex);
        #endif
        #if UNITY_EDITOR && CYG_DEBUG
            // ログ出力用
            OwnerHorse.SkillManager.AddActivatedSkillInfo(SkillId, _detailIndex);
        #endif
            // スキル発動させない場合があるのでその時はここで処理を中断する
            if (IsNotActivateSkill)
            {
                return;
            }
            
            // スキル発動スコア加算。
            OwnerHorse.AddScoreSkillActivate(Rarity, SkillLevel, SkillGroupRate);
            OwnerHorse.AddChallengeMatchPointSkillActivate(Rarity, SkillLevel, SkillGroupRate);
            
            // 発動中スキル登録。
            OwnerHorse.SkillManager.AddCurrentActiveSkill(this);

            // 一度でも発動したスキル登録。
            OwnerHorse.SkillManager.AddUsedSkillId(SkillId);

            // 発動フレームとスキルの情報を登録
            OwnerHorse.SkillManager.AddActivateSkillDetail(_timeAccessor.CurrentFrame, this);

            // スキル発動回数加算。
            OwnerHorse.AddActivateSkillCountByPhase(OwnerHorse.GetPhase(), 1);
            OwnerHorse.AddActivateSkillCountByDistance(OwnerHorse.GetDistance(), 1);
            OwnerHorse.AddActivateSkillCountByRarity(Rarity, 1);

            if (IsTargetSkill(positiveEffectSpeedSkill)) // 特定SkillAbilityTypeを持つスキルだったら回数加算
            {
                // 速度スキル(22,27)の発動数が条件用のトリガーなのでAddActivateSpeedSkillCountとAddActivateSpecificSkillAbilityTypeGroupSkillCountはどちらも同じ数字の状態になるが、
                // 今後万が一加算の条件が変わる可能性がある可能性を考慮して、別々に加算するようにしておく
                OwnerHorse.AddActivateSpeedSkillCount();
                OwnerHorse.AddActivateSpecificSkillAbilityTypeGroupSkillCount();
            }
            if (IsTargetSkill(SkillDefine.SkillAbilityType.HpRate)) // 特定SkillAbilityTypeを持つスキルだったら回数加算
            {
                OwnerHorse.AddActivateHealSkillCount();
            }

            // 特定の評価タグを持つスキルだったら回数加算
            if (IsExistSpecificTag())
            {
                OwnerHorse.AddActivateSpecificTagGroupSkillCount();
            }
        }

        /// <summary>
        /// SkillDefine.SkillAbilityTypeを指定し、
        /// その対象のスキルであるかどうかを返す。
        /// </summary>
        /// <param name="targetSkillAttributeArray">確認したいスキルのSkillAbilityTypeの配列</param>
        /// <returns></returns>
        public bool IsTargetSkill(params SkillDefine.SkillAbilityType[] targetSkillAttributeArray)
        {
            for (int abilityIdx = 0, cnt = Abilities.Count; abilityIdx < cnt; ++abilityIdx)
            {
                var type = Abilities[abilityIdx].AbilityType;
                var value = Abilities[abilityIdx].AbilityValueOnActivate;

                for (int attributeIdx = 0, targetCnt = targetSkillAttributeArray.Length;
                     attributeIdx < targetCnt;
                     ++attributeIdx)
                {
                    if (type.Equals(targetSkillAttributeArray[attributeIdx]) && value > 0)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// SkillParamDefineに定義されているタググループのタグを所持しているかどうか
        /// </summary>
        /// <returns></returns>
        private bool IsExistSpecificTag()
        {
            var skillParam = OwnerHorse.ParamDefine.Skill;
            if (skillParam.AbilityValueUsageTagGroup1IntArray == null || skillParam.AbilityValueUsageTagGroup1IntArray.Length <= 0)
            {
                return false;
            }

            var tagList = SkillMasterSimulate.GetTagIds();
            for (int i = 0, cnt = tagList.Count; i < cnt; i++)
            {
                if (skillParam.AbilityValueUsageTagGroup1IntArray.Contains(tagList[i]))
                {
                    return true;
                }
            }

            return false;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// スキル発動予約されているかどうか
        /// </summary>
        /// <returns></returns>
        ///---------------------------------------------------------------
        public bool IsReservedActivate()
        {
            bool isReservedRandomActivate = false;
            bool isReservedSpecificActivate = false;
            // 現状はスキル効果1のみが対象
            if (_detailIndex == SkillDefine.FORCE_ACTIVATE_SKILL_DETAIL_INDEX)
            {
                isReservedRandomActivate = OwnerHorse.SkillManager.IsReservedRandomActivateSkill(SkillId);
                isReservedSpecificActivate = OwnerHorse.SkillManager.IsReservedSpecificActivateSkill(SkillId);
            }

            // 発動予約スキルIDリストに含まれているかどうか
            return isReservedRandomActivate || isReservedSpecificActivate;
        }

        //---------------------------------------------------------------
        public void Stop()
        {
            if(!IsActivated)
            {
                return;
            }
            IsActivated = false;

            for(int i = 0, cnt = Abilities.Count; i < cnt; ++i)
            {
                Abilities[i].Stop();
            }
            _abilityTimeCalculator.Init();
            
            // 発動中スキル登録解除。
            OwnerHorse.SkillManager.RemoveCurrentActiveSkill(this);
            // クールダウンタイムリセット
            _coolDownTime = DefaultCoolDownTime;
        }

        //---------------------------------------------------------------
        private void RecordSkillEvent(float leftTime)
        {
            var activateType = SkillDefine.ActivateType.Null;

            // スキル発動としてカウントしないスキル効果の場合は固有スキル発動演出は再生しない(今後他のパターンが出てきた場合は整理する必要がある)
            if (IsNotActivateSkill)
            {
                activateType = SkillDefine.ActivateType.OnlyShowSkillPlateAndEffect;
            }
            if (_timeAccessor.AccumulateTimeSinceStart > 0.0f)
            {
                // スキルの継続時間をfloat->intに変換してイベントに記録。
                int timeInt = (int)(leftTime * SkillDefine.SKILL_CONTINUE_TIME_ACCURACY);
                int targetFlags = CalcTargetFlags();
                
                _skillEventRecorder.AddSkillEvent(
                    OwnerHorse,
                    SkillId,
                    _detailIndex,
                    targetFlags,
                    _timeAccessor.AccumulateTimeSinceStart,
                    timeInt,
                    activateType
                );
            }
            // スタート前に発動するスキル登録。
            else
            {
                _skillEventRecorder.AddSkillEvent(
                    OwnerHorse,
                    SkillId,
                    _detailIndex,
                    0, // スキル発動エフェクト再生しないので対象フラグは0。
                    _timeAccessor.AccumulateTimeSinceStart,
                    SkillDefine.SKILL_TIME_ALWAYS_ACTIVATE,
                    activateType
                ); // 効果は永続なので-1。
            }
        }
#endif
    }
}


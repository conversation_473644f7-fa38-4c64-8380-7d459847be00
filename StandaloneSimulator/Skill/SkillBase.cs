#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Collections.Generic;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    public struct SkillBuildInfo
    {
        public readonly ISkillDataAccessor masterSkill;
        public readonly int Level;
        public readonly IHorseRaceInfoSimulate ownerInfo;
        public readonly IRaceHorseAccessor horseAccessor;
        public readonly IRaceCourseAttributeAccessor courseAttributeAccessor;
        public readonly IRaceTimeAccessor timeAccessor;
        public readonly IRaceRandomGenerator randomGenerator;
        public readonly IRaceEventRecorder SkillEventRecorder;
        public readonly RaceInfo raceInfo;
        public readonly Gallop.RaceParamDefine.SkillParam SkillParam;

        public SkillBuildInfo(
            ISkillDataAccessor masterSkill,
            int level,
            IHorseRaceInfoSimulate owner,
            IRaceHorseAccessor horseAccessor,
            IRaceCourseAttributeAccessor courseAttributeAccessor,
            IRaceTimeAccessor timeAccessor,
            IRaceRandomGenerator randomGenerator,
            IRaceEventRecorder skillEventRecorder,
            RaceInfo raceInfo,
            Gallop.RaceParamDefine.SkillParam skillParam)
        {
            this.masterSkill = masterSkill;
            this.Level = level;
            this.ownerInfo = owner;
            this.horseAccessor = horseAccessor;
            this.courseAttributeAccessor = courseAttributeAccessor;
            this.timeAccessor = timeAccessor;
            this.randomGenerator = randomGenerator;
            this.SkillEventRecorder = skillEventRecorder;
            this.raceInfo = raceInfo;
            this.SkillParam = skillParam;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル基底。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillBase
    {
        private const int SKILL_DETAIL_CAPACITY = 2;
        public const int SKILL_ACTIVATE_LOT_TRUE = 1;

        private readonly IHorseRaceInfoSimulate _ownerInfo;
        private readonly ISkillTriggerCreator _triggerCreator;
        private readonly IRaceRandomGenerator _randomGenerator;
        private readonly Gallop.RaceParamDefine.SkillParam _skillParam;

        /// <summary>
        /// このレース中、発動可能かどうか。
        /// </summary>
        /// <remarks>レース出走前に一度だけ抽選される</remarks>
        public bool IsActivateEnable { get; private set; }
#if CYG_DEBUG
        private bool _dbgIsLotActivateInitialized;
#endif

        /// <summary>
        /// スキル発動単位のリスト取得。
        /// </summary>
        public List<ISkillDetail> Details { get; private set; }

        /// <summary>
        /// スキルマスター。
        /// </summary>
        public ISkillDataAccessor SkillMaster { get; private set; }

        /// <summary>
        /// スキルマスターのId。
        /// </summary>
        public int SkillMasterId
        {
            get { return SkillMaster.Id; }
        }

        /// <summary>
        /// レベル。1~。
        /// </summary>
        public int Level { get; private set; }

        /// <summary>
        /// クールダウン時間残り。
        /// </summary>
        private float _coolDownTime;
        public float CoolDownTime => _coolDownTime;

        /// <summary>
        /// 予約されたランダム発動位置
        /// 効果1, 2がともにランダム位置の場合、効果1で抽選された位置を記録しておいて効果2でも使用する
        /// </summary>
        public float ReservedActivateDistanceMin { get; set; }
        public float ReservedActivateDistanceMax { get; set; }
        public IReadOnlyList<SkillTriggerDistanceSetBase.DistanceSet> ReservedActivateDistanceSetList { get; set; } = null; // 複数用
        // 記録した際のクラスとパラメーターを記録しておく（一致確認に使用）
        public Type ReservedActivateDistanceType { get; private set; }
        public int ReservedActivateDistanceValue { get; private set; }

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        //---------------------------------------------------------------
        public SkillBase(ref SkillBuildInfo buildInfo, ISkillTriggerCreator triggerCreator)
        {
            _ownerInfo = buildInfo.ownerInfo;
            _triggerCreator = triggerCreator;
            _randomGenerator = buildInfo.randomGenerator;
            _skillParam = buildInfo.SkillParam;
            SkillMaster = buildInfo.masterSkill;
            Level = buildInfo.Level;
            SetupDetail(
                buildInfo.Level,
                buildInfo.raceInfo,
                buildInfo.SkillParam,
                buildInfo.horseAccessor,
                buildInfo.courseAttributeAccessor,
                buildInfo.timeAccessor,
                buildInfo.randomGenerator,
                buildInfo.SkillEventRecorder);
        }

        //---------------------------------------------------------------
        private ISkillDetail CreateSkillDetail(
            int detailIndex, 
            float leftTime, 
            SkillDefine.SkillAbilityTimeUsage timeUsage, 
            float coolDownTime, 
            int courseDistance, 
            IRaceTimeAccessor timeAccessor, 
            IRaceEventRecorder skillEventRecorder,
            IRaceHorseAccessor horseAccessor)
        {
            return new SkillDetail(
                owner: _ownerInfo,
                timeAccessor: timeAccessor,
                skillEventRecorder: skillEventRecorder,
                horseAccessor: horseAccessor,
                masterSkill: SkillMaster,
                skillParam: _skillParam,
                skillLevel: Level,
                detailIndex: detailIndex,
                leftTime: leftTime,
                timeUsage: timeUsage,
                coolDownTime: coolDownTime,
                courseDistance: courseDistance,
                abilityTimeDivideDistance: _skillParam.AbilityTimeDivideDistance,
                coolDownTimeDivideDistance: _skillParam.CoolDownTimeDivideDistance);
        }

        //---------------------------------------------------------------
        private void SetupDetail(
            int level,
            RaceInfo raceInfo,
            Gallop.RaceParamDefine.SkillParam skillParam,
            IRaceHorseAccessor horseAccessor,
            IRaceCourseAttributeAccessor courseAttributeAccessor,
            IRaceTimeAccessor timeAccessor,
            IRaceRandomGenerator randomGenerator,
            IRaceEventRecorder skillEventRecorder)
        {
            Details = new List<ISkillDetail>(SKILL_DETAIL_CAPACITY);

            // 発動単位１つ目。
            {
                var leftTime = RaceUtilMath.MasterInt2Float(SkillMaster.FloatAbilityTime1);
                var coolDownTime = RaceUtilMath.MasterInt2Float(SkillMaster.FloatCooldownTime1);

                var newDetail = CreateSkillDetail(
                    detailIndex: 0, 
                    leftTime: leftTime,
                    (SkillDefine.SkillAbilityTimeUsage)SkillMaster.AbilityTimeUsage1,
                    coolDownTime: coolDownTime,
                    courseDistance: raceInfo.CourseDistance,
                    timeAccessor: timeAccessor,
                    skillEventRecorder: skillEventRecorder,
                    horseAccessor: horseAccessor);
                Details.Add(newDetail);

                var triggerBuildInfo = new SkillTriggerCreatorBuildInfo(
                    _ownerInfo,
                    horseAccessor,
                    courseAttributeAccessor,
                    timeAccessor,
                    randomGenerator,
                    raceInfo,
                    skillParam,
                    this,
                    newDetail
                );

                // 前提条件。
                var preTrigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Precondition1);
                newDetail.SetPreTrigger(preTrigger);
                
                // 発動条件。
                var trigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Condition1);
                newDetail.SetTrigger(trigger);

                // この発動単位がデバフかどうか。
                newDetail.IsDebuff = RaceUtil.IsDebuff(
                    (SkillDefine.SkillAbilityType) SkillMaster.AbilityType11,
                    RaceUtilMath.MasterInt2Float(SkillMaster.FloatAbilityValue11));
                
                // スキルカテゴリ。
                newDetail.Category = RaceUtil.GetSkillCategory((SkillDefine.SkillAbilityType)SkillMaster.AbilityType11);

                ProcessDetail1(SkillMaster, (abilityType, abilityValueUsage, abilityValueLevelUsage, additionalActivateType, abilityValue, targetType, targetValue) =>
                {
                    if (SkillDefine.SkillAbilityType.None == abilityType)
                    {
                        return;
                    }

                    var ability = CreateAbility(
                        level,
                        newDetail,
                        raceInfo,
                        horseAccessor,
                        randomGenerator,
                        targetType,
                        targetValue,
                        abilityType,
                        abilityValueUsage,
                        abilityValueLevelUsage,
                        additionalActivateType,
                        abilityValue);

                    if (null != ability)
                    {
                        newDetail.AddAbility(ability);
                    }
                });
            }

            // 発動単位２つ目。
            {
                var leftTime = RaceUtilMath.MasterInt2Float(SkillMaster.FloatAbilityTime2);
                var coolDownTime = RaceUtilMath.MasterInt2Float(SkillMaster.FloatCooldownTime2);

                var newDetail = CreateSkillDetail(
                    detailIndex: 1,
                    leftTime: leftTime,
                    (SkillDefine.SkillAbilityTimeUsage)SkillMaster.AbilityTimeUsage2,
                    coolDownTime: coolDownTime,
                    courseDistance: raceInfo.CourseDistance,
                    timeAccessor: timeAccessor,
                    skillEventRecorder: skillEventRecorder,
                    horseAccessor: horseAccessor);
                Details.Add(newDetail);

                var triggerBuildInfo = new SkillTriggerCreatorBuildInfo(
                    _ownerInfo,
                    horseAccessor,
                    courseAttributeAccessor,
                    timeAccessor,
                    randomGenerator,
                    raceInfo,
                    skillParam,
                    this,
                    newDetail
                );
                
                // 前提条件。
                var preTrigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Precondition2);
                newDetail.SetPreTrigger(preTrigger);
                
                // 発動条件。
                var trigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Condition2);
                newDetail.SetTrigger(trigger);

                // この発動単位がデバフかどうか。
                newDetail.IsDebuff = RaceUtil.IsDebuff(
                    (SkillDefine.SkillAbilityType) SkillMaster.AbilityType21,
                    RaceUtilMath.MasterInt2Float(SkillMaster.FloatAbilityValue21));

                // スキルカテゴリ。
                newDetail.Category = RaceUtil.GetSkillCategory((SkillDefine.SkillAbilityType)SkillMaster.AbilityType21);
                
                ProcessDetail2(SkillMaster, (abilityType, abilityValueUsage, abilityValueLevelUsage, additionalActivateType, abilityValue, targetType, targetValue) =>
                {
                    if (SkillDefine.SkillAbilityType.None == abilityType)
                    {
                        return;
                    }

                    var ability = CreateAbility(
                        level,
                        newDetail,
                        raceInfo,
                        horseAccessor,
                        randomGenerator,
                        targetType,
                        targetValue,
                        abilityType,
                        abilityValueUsage,
                        abilityValueLevelUsage,
                        additionalActivateType,
                        abilityValue);

                    if (null != ability)
                    {
                        newDetail.AddAbility(ability);
                    }
                });
            }
        }

        private delegate void ProcessAbility(
            SkillDefine.SkillAbilityType abilityType,
            SkillDefine.SkillAbilityValueUsage abilityValueUsage,
            SkillDefine.SkillAbilityValueLevelUsage abilityValueLevelUsage,
            SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            float abilityValue,
            SkillDefine.SkillTargetType targetType,
            int targetValue);
        
        /// <summary>
        /// 発動単位1のability1/2/3に対して任意の処理を実行。
        /// </summary>
        private void ProcessDetail1(ISkillDataAccessor masterSkill, ProcessAbility procAbility)
        {
            procAbility(
                (SkillDefine.SkillAbilityType)masterSkill.AbilityType11,
                (SkillDefine.SkillAbilityValueUsage)masterSkill.AbilityValueUsage11,
                (SkillDefine.SkillAbilityValueLevelUsage)masterSkill.AbilityValueLevelUsage11,
                (SkillDefine.SkillAbilityAdditionalActivateType)masterSkill.AdditionalActivateType11,
                RaceUtilMath.MasterInt2Float(masterSkill.FloatAbilityValue11),
                (SkillDefine.SkillTargetType)masterSkill.TargetType11,
                masterSkill.TargetValue11);
            procAbility(
                (SkillDefine.SkillAbilityType)masterSkill.AbilityType12,
                (SkillDefine.SkillAbilityValueUsage)masterSkill.AbilityValueUsage12,
                (SkillDefine.SkillAbilityValueLevelUsage)masterSkill.AbilityValueLevelUsage12,
                (SkillDefine.SkillAbilityAdditionalActivateType)masterSkill.AdditionalActivateType12,
                RaceUtilMath.MasterInt2Float(masterSkill.FloatAbilityValue12),
                (SkillDefine.SkillTargetType)masterSkill.TargetType12,
                masterSkill.TargetValue12);
            procAbility(
                (SkillDefine.SkillAbilityType)masterSkill.AbilityType13,
                (SkillDefine.SkillAbilityValueUsage)masterSkill.AbilityValueUsage13,
                (SkillDefine.SkillAbilityValueLevelUsage)masterSkill.AbilityValueLevelUsage13,
                (SkillDefine.SkillAbilityAdditionalActivateType)masterSkill.AdditionalActivateType13,
                RaceUtilMath.MasterInt2Float(masterSkill.FloatAbilityValue13),
                (SkillDefine.SkillTargetType)masterSkill.TargetType13,
                masterSkill.TargetValue13);
        }

        /// <summary>
        /// 発動単位2のability1/2/3に対して任意の処理を実行。
        /// </summary>
        private void ProcessDetail2(ISkillDataAccessor masterSkill, ProcessAbility procAbility)
        {
            procAbility(
                (SkillDefine.SkillAbilityType)masterSkill.AbilityType21,
                (SkillDefine.SkillAbilityValueUsage)masterSkill.AbilityValueUsage21,
                (SkillDefine.SkillAbilityValueLevelUsage)masterSkill.AbilityValueLevelUsage21,
                (SkillDefine.SkillAbilityAdditionalActivateType)masterSkill.AdditionalActivateType21,
                RaceUtilMath.MasterInt2Float(masterSkill.FloatAbilityValue21),
                (SkillDefine.SkillTargetType)masterSkill.TargetType21,
                masterSkill.TargetValue21);
            procAbility(
                (SkillDefine.SkillAbilityType)masterSkill.AbilityType22,
                (SkillDefine.SkillAbilityValueUsage)masterSkill.AbilityValueUsage22,
                (SkillDefine.SkillAbilityValueLevelUsage)masterSkill.AbilityValueLevelUsage22,
                (SkillDefine.SkillAbilityAdditionalActivateType)masterSkill.AdditionalActivateType22,
                RaceUtilMath.MasterInt2Float(masterSkill.FloatAbilityValue22),
                (SkillDefine.SkillTargetType)masterSkill.TargetType22,
                masterSkill.TargetValue22);
            procAbility(
                (SkillDefine.SkillAbilityType)masterSkill.AbilityType23,
                (SkillDefine.SkillAbilityValueUsage)masterSkill.AbilityValueUsage23,
                (SkillDefine.SkillAbilityValueLevelUsage)masterSkill.AbilityValueLevelUsage23,
                (SkillDefine.SkillAbilityAdditionalActivateType)masterSkill.AdditionalActivateType23,
                RaceUtilMath.MasterInt2Float(masterSkill.FloatAbilityValue23),
                (SkillDefine.SkillTargetType)masterSkill.TargetType23,
                masterSkill.TargetValue23);
        }
        
        private ISkillAbility CreateAbility(
            int level,
            ISkillDetail ownerDetail,
            RaceInfo raceInfo,
            IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator,
            SkillDefine.SkillTargetType targetType,
            int targetValue,
            SkillDefine.SkillAbilityType abilityType,
            SkillDefine.SkillAbilityValueUsage abilityValueUsage,
            SkillDefine.SkillAbilityValueLevelUsage abilityValueLevelUsage,
            SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            float abilityValue)
        {
            // #71369 味方にデバフを与えないために、ability_type_*_*ごとにデバフ判定を行う。
            bool isDebuff = RaceUtil.IsDebuff(abilityType, abilityValue);
            
            var targetBuildInfo = new SkillTargetBuildInfo(_ownerInfo, horseAccessor, randomGenerator, raceInfo, targetValue, isDebuff);
            var abilityTarget = SkillTargetCreator.CreateSkillTarget(ref targetBuildInfo, targetType, SkillMasterId);
            if (null == abilityTarget)
            {
                return null;
            }

            var newAbility = SkillAbilityCreator.CreateSkillAbility(
                level,
                _ownerInfo,
                ownerDetail,
                abilityType,
                abilityValueUsage,
                abilityValueLevelUsage,
                additionalActivateType,
                abilityValue,
                abilityTarget,
                horseAccessor,
                randomGenerator,
                _skillParam);
            return newAbility;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// ランダム位置スキルの抽選結果を予約済み状態にする
        /// 効果1, 2でランダム位置を共通化するために使用
        /// </summary>
        //---------------------------------------------------------------
        public void ReserveActivateDistanceParam(Type type, int value)
        {
            // パラメーターの保存
            ReservedActivateDistanceType = type;
            ReservedActivateDistanceValue = value;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// スキル停止。
        /// </summary>
        //---------------------------------------------------------------
        public virtual void Stop()
        {
            for (int i = 0, cnt = Details.Count; i < cnt; ++i)
            {
                Details[i].Stop();
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// スキル効果発動中か。
        /// </summary>
        /// <returns>一つでも発動中のISkillDetailがあればtrueを返却。</returns>
        //---------------------------------------------------------------
        private bool IsActivatedAnyNormalSkillDetail()
        {
            for (int i = 0, cnt = Details.Count; i < cnt; ++i)
            {
                // スキル効果のクールダウンタイムを使用する場合、スキルのクールダウンタイムには影響を与えたくないので省く
                if (Details[i].IsUseSkillDetailCoolDownTime)
                {
                    continue;
                }
                
                if (Details[i].IsActivated)
                {
                    return true;
                }
            }
            return false;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 毎フレームの更新。
        /// </summary>
        //---------------------------------------------------------------
        public virtual void Update(float deltaTime)
        {
            // レーススタート前に発動したスキルを取り除きたくないので最初の更新だけ除外する
            if (_ownerInfo.IsCompleteFirstUpdate)
            { 
                for (int i = 0, cnt = Details.Count; i < cnt; ++i)
                {
                    _ownerInfo.SkillManager.RemovePrevActivateSkillDetail(Details[i]);
                }
            }

            // 発動中の効果がある場合、残り効果時間の更新。
            if (IsActivatedAnyNormalSkillDetail())
            {
                for (int i = 0, cnt = Details.Count; i < cnt; ++i)
                {
                    // 発動していない効果は更新不要。
                    if (!Details[i].IsActivated)
                    {
                        continue;
                    }
                    
                    // スキル効果のクールダウンタイムを使用する場合は別で更新かけるのでスキップする
                    if (Details[i].IsUseSkillDetailCoolDownTime)
                    {
                        continue;
                    }

                    // 効果時間終了したら、クールダウンの開始。
                    if (Details[i].UpdateSkill(deltaTime))
                    {
                        _coolDownTime = Details[i].DefaultCoolDownTime;
                    }
                }
            }
            // 発動中の効果が無い場合、クールダウンや発動チェック。
            else
            {
                bool isActivateEnable = IsActivateEnableByWizLot();
                // クールダウン時間更新。
                if (CheckCoolDown(deltaTime))
                {
                    // 発動条件チェックと発動。
                    for (int i = 0, cnt = Details.Count; i < cnt; ++i)
                    {
                        // 賢さ抽選に外れて発動予約されていないスキル効果はスキル発動判定を行わない
                        if (!isActivateEnable && !Details[i].IsReservedActivate())
                        {
                            continue;
                        }

                        // 既に発動中の効果はチェック不要。
                        if (Details[i].IsActivated)
                        {
                            continue;
                        }

                        // スキル効果のクールダウンタイムを使用する場合は別で更新かけるのでスキップする
                        if (Details[i].IsUseSkillDetailCoolDownTime)
                        {
                            continue;
                        }
                        
                        // １フレーム内で発動するのは１つの効果のみ。indexが若い方が優先。
                        if (Details[i].UpdateTriggerAndActivate(deltaTime))
                        {
                            _ownerInfo.SkillManager.AddPrevActivateSkillDetail(Details[i]);
                            break;
                        }
                    }
                }
            }
            // スキル効果のクールダウンタイムを使用するDetailを更新
            UpdateUseSkillDetailCoolDownTime(deltaTime);
        }
        
        /// <summary>
        /// スキル効果のクールダウンタイムを使用するDetailを更新する
        /// </summary>
        /// <param name="deltaTime"></param>
        private void UpdateUseSkillDetailCoolDownTime(float deltaTime)
        {
            foreach (var detail in Details)
            {
                // スキル効果独自のクールダウンを使用するスキル効果かどうか
                if (!detail.IsUseSkillDetailCoolDownTime)
                {
                    continue;
                }

                if (detail.IsActivated)
                {
                    // スキル効果時間のクールダウンタイムリセットはSkillDetail::UpdateSkill内で行っている
                    detail.UpdateSkill(deltaTime);
                }
                else
                {
                    // クールダウンタイムが明けていない
                    if (!detail.CheckCoolDown(deltaTime))
                    {
                        continue;
                    }

                    // スキル効果の発動条件をチェック
                    if (detail.UpdateTriggerAndActivate(deltaTime))
                    {
                        // スキル発動として扱わない効果がある
                        if (!detail.IsNotActivateSkill)
                        {
                            _ownerInfo.SkillManager.AddPrevActivateSkillDetail(detail);
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// クールダウン時間更新。
        /// </summary>
        /// <returns>クールダウンが0になって次のスキル発動可能ならtrue</returns>
        private bool CheckCoolDown(float deltaTime)
        {
            return RaceUtil.UpdateTimer(ref _coolDownTime, deltaTime);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 発動条件を満たしているスキルを発動。
        /// </summary>
        //---------------------------------------------------------------
        public virtual void CheckTriggerAndActivate()
        {
            // レース開始前の抽選に外れていたら、このスキルは発動不可能。
            bool isActivateEnable = IsActivateEnableByWizLot();

            for (int i = 0, cnt = Details.Count; i < cnt; ++i)
            {
                // 賢さ抽選に外れて発動予約されていないスキル効果はスキル発動判定を行わない
                if (!isActivateEnable && !Details[i].IsReservedActivate())
                {
                    continue;
                }
                // 発動条件チェック→発動を行う。
                if (Details[i].UpdateTriggerAndActivate(0))
                {
                    _ownerInfo.SkillManager.AddPrevActivateSkillDetail(Details[i]);
                }
            }
        }

        //---------------------------------------------------------------
        public void LotActivate()
        {
            // 賢さを元にした発動抽選を行う。
            if(SkillMaster.ActivateLot == SKILL_ACTIVATE_LOT_TRUE)
            {
                float per = RaceUtil.CalcActivatePer(_ownerInfo.BaseWiz, _skillParam);
                IsActivateEnable = _randomGenerator.GetRandom(100.0f) < per;
            }
            // 発動抽選は行わない（当たった扱い）。
            else
            {
                IsActivateEnable = true;
            }

        #if CYG_DEBUG
            _dbgIsLotActivateInitialized = true;
        #endif
        }

        //---------------------------------------------------------------
        /// <summary>
        /// レース出走前の賢さによる抽選でこのスキルが発動可能となったかどうか
        /// </summary>
        /// <returns></returns>
        private bool IsActivateEnableByWizLot()
        {
#if CYG_DEBUG
            Debug.Assert(_dbgIsLotActivateInitialized, "出走前の発動抽選が行われていません");
#endif
            return IsActivateEnable;
        }
    }

}
#endif

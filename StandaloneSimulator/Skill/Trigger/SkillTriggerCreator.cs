using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using SkillDefine = Gallop.SkillDefine;
#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public struct SkillTriggerCreatorBuildInfo
    {
        public readonly IHorseRaceInfoSimulate owner;
        public readonly IRaceHorseAccessor horseAccessor;
        public readonly IRaceCourseAttributeAccessor courseAttributeAccessor;
        public readonly IRaceTimeAccessor timeAccessor;
        public readonly IRaceRandomGenerator randomGenerator;
        public readonly RaceInfo raceInfo;
        public readonly Gallop.RaceParamDefine.SkillParam SkillParam;
        public readonly SkillBase ownerSkill;
        public readonly ISkillDetail ownerSkillDetail;

        public SkillTriggerCreatorBuildInfo(
            IHorseRaceInfoSimulate owner,
            IRaceHorseAccessor horseAccessor,
            IRaceCourseAttributeAccessor courseAttributeAccessor,
            IRaceTimeAccessor timeAccessor,
            IRaceRandomGenerator randomGenerator,
            RaceInfo raceInfo,
            Gallop.RaceParamDefine.SkillParam skillParam,
            SkillBase ownerSkill,
            ISkillDetail ownerSkillDetail
            )
        {
            this.owner = owner;
            this.horseAccessor = horseAccessor;
            this.courseAttributeAccessor = courseAttributeAccessor;
            this.timeAccessor = timeAccessor;
            this.randomGenerator = randomGenerator;
            this.raceInfo = raceInfo;
            this.SkillParam = skillParam;
            this.ownerSkill = ownerSkill;
            this.ownerSkillDetail = ownerSkillDetail;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件生成インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ISkillTriggerCreator
    {
        ISkillTriggerCollection CreateTriggerCollection(ref SkillTriggerCreatorBuildInfo buildInfo, string text);
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件生成：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTriggerCreatorNull : ISkillTriggerCreator
    {
        public ISkillTriggerCollection CreateTriggerCollection(ref SkillTriggerCreatorBuildInfo buildInfo, string text)
        {
            return new SkillTriggerCollectionNull();
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件生成：シミュレート用。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCreatorSimulate : ISkillTriggerCreator
    {
        //---------------------------------------------------------------
        // "ground_type==1&grade<1@ground_type==2&grade<1"
        public ISkillTriggerCollection CreateTriggerCollection(ref SkillTriggerCreatorBuildInfo buildInfo, string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return new SkillTriggerCollectionNull();
            }

            var retCollection = new SkillTriggerCollection();

            // ORで分割。
            var textSplittedByOr = text.Split(SEPARATOR_OR);
            for (int i = 0, cnt = textSplittedByOr.Length; i < cnt; ++i)
            {
                var t = textSplittedByOr[i];
                var group = _CreateTriggers(ref buildInfo, t);
                retCollection.AddTriggerGroup(group);
            }

            return retCollection;
        }

        //---------------------------------------------------------------
        // "ground_type==1&grade<1"
        private static List<ISkillTrigger> _CreateTriggers(ref SkillTriggerCreatorBuildInfo buildInfo, string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return new List<ISkillTrigger>();
            }

            var retGroup = new List<ISkillTrigger>();

            // ANDで分割。
            var textSplittedByAnd = text.Split(SEPARATOR_AND);
            for (int i = 0, cnt = textSplittedByAnd.Length; i < cnt; ++i)
            {
                var t = textSplittedByAnd[i];
                var trigger = _CreateTrigger(ref buildInfo, t, retGroup);
                retGroup.Add(trigger);
            }

            return retGroup;
        }

        //---------------------------------------------------------------
        // "ground_type==1"
        public static ISkillTrigger _CreateTrigger(ref SkillTriggerCreatorBuildInfo buildInfo, string text, List<ISkillTrigger> group)
        {
            if(!Parse2Context(text, out var context))
            {
                return SkillTriggerNull.Instance;
            }

            if (!Gallop.SkillDefine.TriggerCreateFunc.TryGetValue(context.Keyword, out var func))
            {
                return SkillTriggerNull.Instance;
            }
            
            var triggerBuildInfo = new SkillTriggerBuildInfo(
                buildInfo.owner,
                buildInfo.horseAccessor,
                buildInfo.courseAttributeAccessor,
                buildInfo.timeAccessor,
                buildInfo.randomGenerator,
                buildInfo.raceInfo,
                buildInfo.SkillParam,
                buildInfo.ownerSkill,
                buildInfo.ownerSkillDetail,
                context.Op,
                context.ValueRh,
                group);
                
            return func.FuncCreateTriggerSimulate(triggerBuildInfo);
        }
        
        #region 発動条件コンテキスト
        //---------------------------------------------------------------
        // "ground_type==1&grade<1@ground_type==2&grade<1"
        public static List<SkillDefine.TriggerContext> CreateTriggerContextList(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return new List<SkillDefine.TriggerContext>();
            }

            var retList = new List<SkillDefine.TriggerContext>();

            // ORで分割。
            var textSplittedByOr = text.Split(SEPARATOR_OR);
            for (int i = 0, cnt = textSplittedByOr.Length; i < cnt; ++i)
            {
                var t = textSplittedByOr[i];
                var contextList = CreateTriggerContextListInternal(t);

                foreach (var context in contextList)
                {
                    if (!retList.Contains(context))
                    {
                        retList.Add(context);
                    }
                }
            }

            return retList;
        }

        //---------------------------------------------------------------
        // "ground_type==1&grade<1"
        private static List<SkillDefine.TriggerContext> CreateTriggerContextListInternal(string text)
        {
            var retContextList = new List<SkillDefine.TriggerContext>();

            if (string.IsNullOrEmpty(text))
            {
                return retContextList;
            }

            // ANDで分割。
            var textSplittedByAnd = text.Split(SEPARATOR_AND);
            for (int i = 0, cnt = textSplittedByAnd.Length; i < cnt; ++i)
            {
                var t = textSplittedByAnd[i];
                if (!Parse2Context(t, out var context))
                {
                    continue;
                }
                retContextList.Add(context);
            }

            return retContextList;
        }
        #endregion
    }
}
#endif

namespace StandaloneSimulator
{
    public partial class SkillTriggerCreatorSimulate
    {
        //-------------------------------------------------------------------
        // .NET/GALLOP兼用
        //-------------------------------------------------------------------
        private const char SEPARATOR_AND = '&';
        private const char SEPARATOR_OR = '@';
        
        private static bool Parse2Context(string text, out SkillDefine.TriggerContext retContext)
        {
            retContext = new SkillDefine.TriggerContext();
            
            // 文字列を左辺・演算子・右辺に分割。３要素に分割できない場合は不正文字列である。
            var textSplitted = Regex.Split(text, "(==|!=|<=|>=|<|>)");
            if (textSplitted.Length != System.Enum.GetNames(typeof(SkillDefine.TriggerTextIndex)).Length)
            {
                return false;
            }
            
            // 右辺の整数値。
            var valueRhTxt = textSplitted[(int)SkillDefine.TriggerTextIndex.ValueRh];
            if (!int.TryParse(valueRhTxt, out int valueRh))
            {
                return false;
            }

            // 左辺のキーワードはテキストのまま。
            retContext.Keyword = textSplitted[(int) SkillDefine.TriggerTextIndex.ValueLh];
            // 比較演算子をenumに変換。
            retContext.Op = Str2Operator(textSplitted[(int) SkillDefine.TriggerTextIndex.Operator]);
            // 右辺は整数値。
            retContext.ValueRh = valueRh;
            return true;
        }
        
        //---------------------------------------------------------------
        private static SkillDefine.SkillTriggerOperator Str2Operator(string text)
        {
            switch (text)
            {
                case "==": return SkillDefine.SkillTriggerOperator.Equal;
                case "!=": return SkillDefine.SkillTriggerOperator.NotEqual;
                case ">": return SkillDefine.SkillTriggerOperator.Greater;
                case ">=": return SkillDefine.SkillTriggerOperator.GreaterEqual;
                case "<": return SkillDefine.SkillTriggerOperator.Lesser;
                case "<=": return SkillDefine.SkillTriggerOperator.LesserEqual;

                default:
                    Debug.Assert(false, "サポートされていない演算子です。text=" + text);
                    return SkillDefine.SkillTriggerOperator.Equal;
            }
        }
        
#if GALLOP
        //-------------------------------------------------------------------
        // GALLOP専用
        //-------------------------------------------------------------------
        #region 発動条件タグ
        /// <summary>
        /// skillIdから発動条件のタグをリストで取得。
        /// </summary>
        /// <remarks>
        /// タグ未割当の発動条件のみで構成されていたりエラーケースの場合は空リストが返る。
        /// </remarks>
        public static List<SkillDefine.SkillTriggerTag> CreateTriggerTagList(int skillId)
        {
            var masterSkill = Gallop.MasterDataManager.Instance.masterSkillData.Get(skillId);
            if (masterSkill == null)
            {
                Debug.LogWarning($"不正なskillId ({skillId})");
                return new List<SkillDefine.SkillTriggerTag>();
            }

            var preCondition1 = masterSkill.Precondition1;
            var preCondition2 = masterSkill.Precondition2;
            var condition1 = masterSkill.Condition1;
            var condition2 = masterSkill.Condition2;

            var preCondition1TagList = CreateTriggerTagList(preCondition1);
            var preCondition2TagList = CreateTriggerTagList(preCondition2);
            var condition1TagList = CreateTriggerTagList(condition1);
            var condition2TagList = CreateTriggerTagList(condition2);

            var retList = preCondition1TagList
                .Union(preCondition2TagList)
                .Union(condition1TagList)
                .Union(condition2TagList).ToList();
            return retList;
        }        
        
        //---------------------------------------------------------------
        // "ground_type==1&grade<1@ground_type==2&grade<1"
        private static List<SkillDefine.SkillTriggerTag> CreateTriggerTagList(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return new List<SkillDefine.SkillTriggerTag>();
            }

            var retList = new List<SkillDefine.SkillTriggerTag>();

            // ORで分割。
            var textSplittedByOr = text.Split(SEPARATOR_OR);
            for (int i = 0, cnt = textSplittedByOr.Length; i < cnt; ++i)
            {
                var t = textSplittedByOr[i];
                var tagList = CreateTriggerTagListForKeywordSet(t);

                foreach (var tag in tagList)
                {
                    if (!retList.Contains(tag))
                    {
                        retList.Add(tag);
                    }
                }
            }

            return retList;
        }

        //---------------------------------------------------------------
        // "ground_type==1&grade<1"
        private static List<SkillDefine.SkillTriggerTag> CreateTriggerTagListForKeywordSet(string text)
        {
            var retTagList = new List<SkillDefine.SkillTriggerTag>();

            if (string.IsNullOrEmpty(text))
            {
                return retTagList;
            }

            // ANDで分割。
            var textSplittedByAnd = text.Split(SEPARATOR_AND);
            for (int i = 0, cnt = textSplittedByAnd.Length; i < cnt; ++i)
            {
                var t = textSplittedByAnd[i];
                var tagList = CreateTriggerTagListForKeyword(t);
                foreach (var tag in tagList)
                {
                    if (tag == SkillDefine.SkillTriggerTag.Null)
                    {
                        continue;
                    }
                    if (retTagList.Contains(tag))
                    {
                        continue;
                    }
                    retTagList.Add(tag);
                }
            }

            return retTagList;
        }

        //---------------------------------------------------------------
        // "ground_type==1"
        private static List<SkillDefine.SkillTriggerTag> CreateTriggerTagListForKeyword(string text)
        {
            if(!Parse2Context(text, out var context))
            {
                return new List<SkillDefine.SkillTriggerTag>();
            }

            if (!Gallop.StaticVariableDefine.Race.SkillTriggerBase.TriggerTagCreateFunc.TryGetValue(context.Keyword, out var func))
            {
                return new List<SkillDefine.SkillTriggerTag>();
            }
            
            var triggerBuildInfo = new SkillTriggerBuildInfo(context);
            return func.FuncGetTagList(triggerBuildInfo);
        }
        #endregion
#endif
    }
}

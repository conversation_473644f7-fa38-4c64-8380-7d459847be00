#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    public class SkillTriggerCollectionNull : ISkillTriggerCollection
    {
        //---------------------------------------------------------------
        public bool IsActivatedAny()
        {
            return false;
        }

        //---------------------------------------------------------------
        public void Update( float deltaTime )
        {
        }

        //---------------------------------------------------------------
        public void AddTriggerGroup( List< ISkillTrigger > group )
        {
        }
        
        //---------------------------------------------------------------
        public bool IsContainsUseSkillDetailCoolDownTimeTrigger() { return false; }

        //---------------------------------------------------------------
        public bool IsContainsNotActivateSkillTrigger() { return false; }
    }


    //-------------------------------------------------------------------
    public class SkillTriggerCollection : ISkillTriggerCollection
    {
        private const int TRIGGER_LIST_CAPACITY = 4;
        private readonly List< List< ISkillTrigger > > _triggers = null;

        //---------------------------------------------------------------
        public SkillTriggerCollection()
        {
            _triggers = new List< List< ISkillTrigger > >( TRIGGER_LIST_CAPACITY );
        }

        //---------------------------------------------------------------
        public bool IsActivatedAny()
        {
            for( int i = 0, cnt = _triggers.Count; i < cnt; ++i )
            {
                var triggers = _triggers[ i ];

                // 条件を満たしている発動条件群があれば、true返却。
                if( _IsActivatedAll( triggers ) )
                {
                    return true;
                }
            }
            return false;
        }

        //---------------------------------------------------------------
        private static bool _IsActivatedAll( List< ISkillTrigger > triggers )
        {
            for( int i = 0, cnt = triggers.Count; i < cnt; ++i )
            {
                if( !triggers[i].IsActivated )
                {
                    return false;
                }
            }
            return true;
        }

        //---------------------------------------------------------------
        public void Update( float deltaTime )
        {
            for( int i = 0, cnt_i = _triggers.Count; i < cnt_i; ++i )
            {
                var triggers = _triggers[ i ];
                for( int j = 0, cnt_j = triggers.Count; j < cnt_j; ++j )
                {
                    var trigger = triggers[ j ];
                    trigger.Update( deltaTime );
                }
            }
        }

        //---------------------------------------------------------------
        public void AddTriggerGroup( List< ISkillTrigger > group )
        {
            _triggers.Add( group );
        }
        
        //---------------------------------------------------------------
        public bool IsContainsUseSkillDetailCoolDownTimeTrigger()
        {
            foreach (var triggerList in _triggers)
            {
                foreach (var trigger in triggerList)
                {
                    if (Gallop.SkillDefine.USE_SKILL_DETAIL_COOLDOWN_TIME_TRIGGER_LIST.Contains(trigger.GetType()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        //---------------------------------------------------------------
        public bool IsContainsNotActivateSkillTrigger()
        {
            foreach (var triggerList in _triggers)
            {
                foreach (var trigger in triggerList)
                {
                    if (Gallop.SkillDefine.NOT_ACTIVATE_SKILL_TRIGGER_TYPE.Contains(trigger.GetType()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}

#endif

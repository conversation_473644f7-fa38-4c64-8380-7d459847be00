using System.Collections.Generic;
using System.Linq;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    public class SkillTriggerBuildInfo
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public readonly IHorseRaceInfoSimulate ownerHorse;
        public readonly IRaceHorseAccessor horseAccessor;
        public readonly RaceInfo raceInfo;
        public readonly SkillBase ownerSkill;
        public readonly ISkillDetail ownerSkillDetail;
        public readonly List<ISkillTrigger> selfGroup;
        public readonly IRaceCourseAttributeAccessor courseAttributeAccessor;
        public readonly IRaceTimeAccessor timeAccessor;
        public readonly IRaceRandomGenerator randomGenerator;
        public readonly Gallop.RaceParamDefine.SkillParam SkillParam;
#endif
        public readonly SkillDefine.SkillTriggerOperator op;
        public readonly int valueRh;
        
        public SkillTriggerBuildInfo(SkillDefine.TriggerContext context)
        {
            this.op = context.Op;
            this.valueRh = context.ValueRh;
        }
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        public SkillTriggerBuildInfo(
            IHorseRaceInfoSimulate horse,
            IRaceHorseAccessor horseAccessor,
            IRaceCourseAttributeAccessor courseAttributeAccessor,
            IRaceTimeAccessor timeAccessor,
            IRaceRandomGenerator randomGenerator,
            RaceInfo raceInfo,
            Gallop.RaceParamDefine.SkillParam skillParam,
            SkillBase skill,
            ISkillDetail skillDetail,
            SkillDefine.SkillTriggerOperator op,
            int valueRh,
            List<ISkillTrigger> selfGroup
            )
        {
            this.ownerHorse = horse;
            this.horseAccessor = horseAccessor;
            this.courseAttributeAccessor = courseAttributeAccessor;
            this.timeAccessor = timeAccessor;
            this.randomGenerator = randomGenerator;
            this.raceInfo = raceInfo;
            this.SkillParam = skillParam;
            this.ownerSkill = skill;
            this.ownerSkillDetail = skillDetail;
            this.op = op;
            this.valueRh = valueRh;
            this.selfGroup = selfGroup;
        }
#endif
    };

    //-------------------------------------------------------------------
    public class SkillTriggerNull : ISkillTrigger
    {
        public static SkillTriggerNull Instance = new SkillTriggerNull();

        public bool IsActivated { get { return false; } }

        public void Update(float deltaTime) { }
    }
    
#if STANDALONE_SIMULATOR || UNITY_EDITOR
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerBase : ISkillTrigger
    {
        protected readonly IHorseRaceInfoSimulate _owner;
        protected readonly IRaceHorseAccessor _horseAccessor;
        protected readonly IRaceCourseAttributeAccessor _courseAttributeAccessor;
        protected readonly IRaceTimeAccessor _timeAccessor;
        protected readonly IRaceRandomGenerator _randomGenerator;
        protected readonly RaceInfo _raceInfo;
        protected readonly Gallop.RaceParamDefine.SkillParam _skillParam;
        protected readonly SkillBase _ownerSkill;
        protected readonly ISkillDetail _ownerSkillDetail;
        protected readonly SkillDefine.SkillTriggerOperator _op = SkillDefine.SkillTriggerOperator.Equal;
        protected int _valueRh;
        protected readonly List<ISkillTrigger> _selfGroup;

        //---------------------------------------------------------------
        protected SkillTriggerBase(SkillTriggerBuildInfo buildInfo)
        {
            _owner = buildInfo.ownerHorse;
            _horseAccessor = buildInfo.horseAccessor;
            _courseAttributeAccessor = buildInfo.courseAttributeAccessor;
            _timeAccessor = buildInfo.timeAccessor;
            _randomGenerator = buildInfo.randomGenerator;
            _raceInfo = buildInfo.raceInfo;
            _skillParam = buildInfo.SkillParam;
            _ownerSkill = buildInfo.ownerSkill;
            _ownerSkillDetail = buildInfo.ownerSkillDetail;
            _op = buildInfo.op;
            _valueRh = buildInfo.valueRh;
            _selfGroup = buildInfo.selfGroup;
        }

        //---------------------------------------------------------------
        public virtual void Update(float deltaTime)
        {
            IsActivated = CheckActivate();
        #if CYG_DEBUG
            // デバッグ機能：パラメータ系の発動条件無視。
            if(RaceSimulateDebugger.IsIgnoreSkillTriggerParam && Category == SkillDefine.SkillTriggerCategory.Param)
            {
                IsActivated = true;
            }
        #endif
            // もし他スキルによってスキル発動を指示されていてたら無条件で発動する
            // 発動対象になるのはスキル効果1のみ
            if (_ownerSkillDetail.IsReservedActivate())
            {
                IsActivated = true;
            }
        }

        //---------------------------------------------------------------
        public bool IsActivated { get; protected set; }

        //---------------------------------------------------------------
        protected abstract bool CheckActivate();

        //---------------------------------------------------------------
        protected abstract SkillDefine.SkillTriggerCategory Category { get; }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：左辺と右辺の比較演算。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerParamBase : SkillTriggerBase
    {
#if CYG_DEBUG
        protected virtual bool IsEnableIgnoreSkillTrigger() { return true; }
#endif

        //---------------------------------------------------------------
        protected SkillTriggerParamBase(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            return CheckValue();
        }

        //---------------------------------------------------------------
        protected abstract int GetValueLh();

        //---------------------------------------------------------------
        protected bool CheckValue()
        {
            return CheckValueOp(GetValueLh(), _op, _valueRh);
        }

        //---------------------------------------------------------------
        public static bool CheckValueOp(int valueLh, SkillDefine.SkillTriggerOperator op, int valueRh)
        {
            switch (op)
            {
            case SkillDefine.SkillTriggerOperator.Equal: return valueLh == valueRh;
            case SkillDefine.SkillTriggerOperator.NotEqual: return valueLh != valueRh;
            case SkillDefine.SkillTriggerOperator.Greater: return valueLh > valueRh;
            case SkillDefine.SkillTriggerOperator.GreaterEqual: return valueLh >= valueRh;
            case SkillDefine.SkillTriggerOperator.Lesser: return valueLh < valueRh;
            case SkillDefine.SkillTriggerOperator.LesserEqual: return valueLh <= valueRh;
            default: return false;
            }
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category 
        { 
            get { return SkillDefine.SkillTriggerCategory.Param; }
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：左辺と右辺の比較演算。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerParamBaseFloat : SkillTriggerBase
    {
#if CYG_DEBUG
        protected virtual bool IsEnableIgnoreSkillTrigger() { return true; }
#endif

        //---------------------------------------------------------------
        protected SkillTriggerParamBaseFloat(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            return CheckValue();
        }

        //---------------------------------------------------------------
        protected abstract float GetValueLh();

        //---------------------------------------------------------------
        protected virtual bool CheckValue()
        {
            var valueLh = GetValueLh();
            switch (_op)
            {
            case SkillDefine.SkillTriggerOperator.Equal: return RaceUtilMath.Approximately(valueLh, _valueRh);
            case SkillDefine.SkillTriggerOperator.NotEqual: return !RaceUtilMath.Approximately(valueLh, _valueRh);
            case SkillDefine.SkillTriggerOperator.Greater: return valueLh > _valueRh;
            case SkillDefine.SkillTriggerOperator.GreaterEqual: return valueLh >= _valueRh;
            case SkillDefine.SkillTriggerOperator.Lesser: return valueLh < _valueRh;
            case SkillDefine.SkillTriggerOperator.LesserEqual: return valueLh <= _valueRh;
            default: return false;
            }
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category 
        { 
            get { return SkillDefine.SkillTriggerCategory.Param; }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：左辺と右辺の比較演算。
    /// </summary>
    /// <remarks> 入力値をfloatで受け取ります </remarks>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerParamBaseInputFloat : SkillTriggerParamBaseFloat
    {
        /// <summary> valueRhのfloatバージョン </summary>
        protected readonly float _valueRhFloat;
        /// <summary> 除算する数字(継承先によって小数点第何位間で取得した以下が変わると思うのでabstract) </summary>
        protected abstract float divisionNum { get; }

        //---------------------------------------------------------------
        protected SkillTriggerParamBaseInputFloat(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _valueRhFloat = (float) (_valueRh / divisionNum);
        }

        //---------------------------------------------------------------
        protected override bool CheckValue()
        {
            var valueLh = GetValueLh();
            switch (_op)
            {
                case SkillDefine.SkillTriggerOperator.Equal: return RaceUtilMath.Approximately(valueLh, _valueRhFloat);
                case SkillDefine.SkillTriggerOperator.NotEqual: return !RaceUtilMath.Approximately(valueLh, _valueRhFloat);
                case SkillDefine.SkillTriggerOperator.Greater: return valueLh > _valueRhFloat;
                case SkillDefine.SkillTriggerOperator.GreaterEqual: return valueLh >= _valueRhFloat;
                case SkillDefine.SkillTriggerOperator.Lesser: return valueLh < _valueRhFloat;
                case SkillDefine.SkillTriggerOperator.LesserEqual: return valueLh <= _valueRhFloat;
                default: return false;
            }
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Param; }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：一定距離内でランダムに条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerDistanceBase : SkillTriggerBase
    {
        protected const float DISTANCE_RANGE = 10.0f;

        private float _distanceMin = 0;   // ここから
        private float _distanceMax = 0;   // ここの間で発動可能とする。
        // 効果1, 2がともにランダム発動位置である場合に、発動位置を揃えるためにこうしている
        protected (float DistanceMin, float DistanceMax) Distance
        {
            get => (_distanceMin, _distanceMax);
            set
            {
                // 予約済みの発動位置が記録されていない or 記録したときと別クラスならば
                if (_ownerSkill.ReservedActivateDistanceType != GetType() ||
                    _ownerSkill.ReservedActivateDistanceValue != _valueRh)
                {
                    (_distanceMin, _distanceMax) = value;
                    
                    // 発動位置の予約を行う
                    (_ownerSkill.ReservedActivateDistanceMin, _ownerSkill.ReservedActivateDistanceMax) = value;
                    
                    // 記録したときのパラメーターを保持しておく（別のランダムスキルが設定された場合をケア）
                    _ownerSkill.ReserveActivateDistanceParam(GetType(), _valueRh);
                }
                else
                {
                    (_distanceMin, _distanceMax) = (_ownerSkill.ReservedActivateDistanceMin, _ownerSkill.ReservedActivateDistanceMax);
                }
            }
        }
        
        protected bool _isInitialized = false;
        protected bool _hasDistance = true;

        //---------------------------------------------------------------
        protected SkillTriggerDistanceBase(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 比較演算は==のみサポート。
            Debug.Assert(buildInfo.op == SkillDefine.SkillTriggerOperator.Equal);

            _InitDistance();
        }

        //---------------------------------------------------------------
        protected abstract void _InitDistance();
        //---------------------------------------------------------------
        protected float _GetDistance()
        {
            return _owner.GetDistance();
        }

        //---------------------------------------------------------------
        protected void LotTriggerStartDistance(float distanceMin, float distanceMax)
        {
            // ランダム最大値は発動猶予距離（DISTANCE_RANGE）分減らす。
            // 最大値に近い値が抽選された場合、そこからの発動猶予距離を考慮すると、本来期待する発動地点からずれるので。
            float randomMin = distanceMin;
            float randomMax = distanceMax - DISTANCE_RANGE;
            if (randomMax < randomMin)
            {
                randomMax = randomMin;
            }

            var tempDistance = _randomGenerator.GetRandom(randomMin, randomMax);
            Distance = (tempDistance, tempDistance + DISTANCE_RANGE);
        }
        
        //---------------------------------------------------------------
        /// <summary>
        /// 指定したPhase距離区間内の、指定した割合での開始距離と終了距離を取得
        /// </summary>
        /// <param name="phase"></param>
        /// <param name="startRate"></param>
        /// <param name="endRate"></param>
        /// <returns></returns>
        protected (float startDistance ,float endDistance) GetStartAndEndDistanceByPhaseAndRate(Gallop.RaceDefine.HorsePhase phase, float startRate, float endRate)
        {
            // 指定されたPhase距離区間を取得し、その距離区間のどの地点で発動するかをランダムで決定する。
            _raceInfo.GetPhaseDistance(phase, out float distanceMin, out float distanceMax);

            // 割合は0~1で指定する
            if (startRate < 0 || startRate > 1 || endRate < 0 || endRate > 1 || startRate > endRate)
            {
                Debug.LogError("効果の開始/終了位置が不正です");
                return (distanceMin, distanceMax);
            }

            // Phase距離区間内で、開始距離と終了位置を補正。
            float phaseLength = (distanceMax - distanceMin);
            var start = distanceMin + phaseLength * startRate;
            var end = distanceMin + phaseLength * endRate;
            return (start, end);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            if (!_isInitialized)
            {
                return false;
            }

            // 条件を満たす区間を持てなかった場合、条件を満たさない。
            if (!_hasDistance)
            {
                return false;
            }

            var dist = _GetDistance();
            if (dist >= _distanceMin &&
                dist <= _distanceMax)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }

    
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件基底：一定距離内でランダムに条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerDistanceSetBase : SkillTriggerBase
    {
        protected const float DISTANCE_RANGE = 10.0f;

        public class DistanceSet
        {
            public readonly float DistanceMin;// ここから
            public readonly float DistanceMax;// ここの間で発動可能とする。

            public DistanceSet(float min, float max)
            {
                DistanceMin = min;
                DistanceMax = max;
            }
        };

        private IReadOnlyList<DistanceSet> _distanceSetList = new List<DistanceSet>();
        protected IReadOnlyList<DistanceSet> DistanceSetList
        {
            get { return _distanceSetList;}
            set
            {
                // 予約済みの発動位置が記録されていない or 記録したときと別クラスならば
                if (_ownerSkill.ReservedActivateDistanceType != GetType() ||
                    _ownerSkill.ReservedActivateDistanceValue != _valueRh)
                {
                    _distanceSetList = value;
                    
                    // 発動位置の予約を行う
                    _ownerSkill.ReservedActivateDistanceSetList = value;
                    
                    // 予約済みフラグ更新などは後で_ownerSkill.ReserveActivateDistanceParam()で行う
                }
                else
                {
                    _distanceSetList = _ownerSkill.ReservedActivateDistanceSetList;
                }
            }
        }
        protected bool _isInitialized = false;
        private bool HasDistance => _distanceSetList.Count > 0;

        //---------------------------------------------------------------
        protected SkillTriggerDistanceSetBase(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 比較演算は==のみサポート。
            Debug.Assert(buildInfo.op == SkillDefine.SkillTriggerOperator.Equal);

            InitDistance();
        }

        //---------------------------------------------------------------
        protected abstract void InitDistance();
        
        //---------------------------------------------------------------
        protected float _GetDistance()
        {
            return _owner.GetDistance();
        }

        //---------------------------------------------------------------
        protected void LotAndAddTriggerStartDistance(float distanceMin, float distanceMax, out float retMin, out float retMax)
        {
            // ランダム最大値は発動猶予距離（DISTANCE_RANGE）分減らす。
            // 最大値に近い値が抽選された場合、そこからの発動猶予距離を考慮すると、本来期待する発動地点からずれるので。
            float randomMin = distanceMin;
            float randomMax = distanceMax - DISTANCE_RANGE;
            if (randomMax < randomMin)
            {
                randomMax = randomMin;
            }

            float min = _randomGenerator.GetRandom(randomMin, randomMax);
            float max = min + DISTANCE_RANGE;
            
            // コピーしたものを用意
            var tempDistanceSetList = new List<DistanceSet>(DistanceSetList);
            tempDistanceSetList.Add(new DistanceSet(min, max));
            tempDistanceSetList.Sort((a, b) =>
            {
                if (a.DistanceMin > b.DistanceMin)
                {
                    return 1;
                }
                if (a.DistanceMin < b.DistanceMin)
                {
                    return -1;
                }
                return 0;
            });

            retMin = min;
            retMax = max;
            
            DistanceSetList = tempDistanceSetList;
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            if (!_isInitialized)
            {
                return false;
            }

            if (!HasDistance)
            {
                return false;
            }
            
            var dist = _GetDistance();
            foreach (var set in DistanceSetList)
            {
                // _distanceSetListはDistanceMinで昇順にソートされているので、まだDistanceMinに達していないデータを見つけたら以降全て満たさないのでbreak。
                if (dist < set.DistanceMin)
                {
                    break;
                }
                
                // DistanceMinは超えているのでDistanceMax以内に収まっていればtrue。
                if (dist <= set.DistanceMax)
                {
                    return true;
                }
            }
            return false;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }
    
    

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：常に成立する。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerAlways"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerAlways : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerAlways(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            return true;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return 0;
        }
    }


    #region <レース系>
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース開催月。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerMonth"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerMonth : SkillTriggerParamBase
    {
        private readonly int _month = 0;

        //---------------------------------------------------------------
        public SkillTriggerMonth(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            var raceInstanceMaster = _raceInfo.RaceInstanceMaster;
            _month = raceInstanceMaster.Date / 100;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _month;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レースグレード。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerGrade"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGrade : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerGrade(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_raceInfo.Grade;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コーナー数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCornerCount"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseCornerCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCourseCornerCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var corners = _courseAttributeAccessor.GetCornerList();
            return corners.Count;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コースの右回り/左回り。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCourseRotation"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseRotation : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCourseRotation(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(SkillDefine.SkillTriggerOperator.Equal == buildInfo.op || SkillDefine.SkillTriggerOperator.NotEqual == buildInfo.op);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _raceInfo.RaceCourseSet.Turn;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：地面素材。race_course_set.csvで指定されている値を参照する。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerGroundType"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGroundType : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerGroundType(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(SkillDefine.SkillTriggerOperator.Equal == buildInfo.op || SkillDefine.SkillTriggerOperator.NotEqual == buildInfo.op);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_raceInfo.GroundType;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：馬場状態。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerGroundCondition"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGroundCondition : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerGroundCondition(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(SkillDefine.SkillTriggerOperator.Equal == buildInfo.op || SkillDefine.SkillTriggerOperator.NotEqual == buildInfo.op);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_raceInfo.GroundCondition;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：キャラのハロン数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFurlong"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFurlong : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerFurlong(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtil.Distance2Furlong(_owner.GetDistance());
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerFurlongRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerFurlongRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            int furlong = _valueRh;

            float distanceMin = furlong * Gallop.RaceDefine.ONE_FURONG_DISTANCE;
            float distanceMax = (furlong + 1) * Gallop.RaceDefine.ONE_FURONG_DISTANCE;

            // どの地点で発動するか、を抽選。
            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内のランダムな直線内のさらにランダムな一区間で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerStraightRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerStraightRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            var straights = _courseAttributeAccessor.GetStraightList();
            if (null == straights)
            {
                return;
            }

            // 直線が存在しない場合（は存在しないと思うが）初期化済みとして終了。一応エラーとして報告もしておく。
            if (straights.Count <= 0)
            {
                Debug.Assert(false, "直線の数が0です。");
                _isInitialized = true;
                return;
            }

            // 保存された直線区間のどこで発動するかを抽選。
            int selectedSectionIndex = _randomGenerator.GetRandom(straights.Count);
            var selectedSection = straights[selectedSectionIndex];

            // 選ばれた直線区間内のどこで発動するかを抽選。
            LotTriggerStartDistance(selectedSection.StartDistance, selectedSection.EndDistance);

            _isInitialized = true;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内のランダムな直線中、条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerWhileInStraightRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerWhileInStraightRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            var straights = _courseAttributeAccessor.GetStraightList();
            if (null == straights)
            {
                return;
            }

            // 直線が存在しない場合（は存在しないと思うが）初期化済みとして終了。一応エラーとして報告もしておく。
            if (straights.Count <= 0)
            {
                Debug.Assert(false, "直線の数が0です。");
                _isInitialized = true;
                return;
            }

            // 保存された直線区間のどこで発動するかを抽選。
            int selectedSectionIndex = _randomGenerator.GetRandom(straights.Count);
            var selectedSection = straights[selectedSectionIndex];

            // 選ばれた直線区間内にいる間は条件を満たす。
            Distance = (selectedSection.StartDistance, selectedSection.EndDistance);

            _isInitialized = true;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phaseかつランダムな直線のさらにランダムな一区間で条件を満たす
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseStraightRandom : SkillTriggerDistanceBase
    {
        // 指定Phase
        protected Gallop.RaceDefine.HorsePhase _phase;
        protected (float StartDistance, float EndDistance) _phaseDistance;
        
        //-------------------------------------------------------------------
        public SkillTriggerPhaseStraightRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }
        
        //-------------------------------------------------------------------
        protected override void _InitDistance()
        {
            // 指定Phaseの開始/終了位置を取得
            SetParameter();

            var straightList = _courseAttributeAccessor.GetStraightList();
            if (null == straightList)
            {
                return;
            }
            
            // 指定Phaseにかかっている直線のうち、指定Phaseに含まれる部分の距離を合算し、その中で抽選する
            float totalPhaseStraightDistance = 0f;
            for (int i = 0, cnt = straightList.Count; i < cnt; i++)
            {
                var startDistance = System.Math.Max(_phaseDistance.StartDistance, straightList[i].StartDistance);
                var endDistance = System.Math.Min(_phaseDistance.EndDistance, straightList[i].EndDistance);
                var deltaDistance = endDistance - startDistance;
                if (deltaDistance < 0)
                {
                    // 指定Phaseにかかっていない場合
                    continue;
                }
                totalPhaseStraightDistance += deltaDistance;
            }
            
            // 直線が見つからなかった場合
            if (RaceUtilMath.Approximately(totalPhaseStraightDistance, 0f))
            {
                return;
            }
            
            // 合算した直線距離の中から1点を選ぶ
            var selectedStraightDistance = _randomGenerator.GetRandom(totalPhaseStraightDistance);
            
            // 選ばれた点がどの直線に含まれるかを考慮し、コース全体での距離に直す
            var selectedDistance = 0f;
            for (int i = 0, cnt = straightList.Count; i < cnt; i++)
            {
                var startDistance = System.Math.Max(_phaseDistance.StartDistance, straightList[i].StartDistance);
                var endDistance = System.Math.Min(_phaseDistance.EndDistance, straightList[i].EndDistance);

                var deltaDistance = endDistance - startDistance;
                if (deltaDistance < 0)
                {
                    // 指定Phaseにかかっていない場合
                    continue;
                }

                // 選ばれた点が計算中の直線よりも後の直線に含まれる場合
                if (selectedStraightDistance > deltaDistance)
                {
                    selectedStraightDistance -= deltaDistance;
                }
                // 選ばれた点が計算中の直線に含まれる場合
                else
                {
                    // 直線開始地点 + 直線開始地点から選ばれた点までの距離
                    selectedDistance = startDistance + selectedStraightDistance;
                    break;
                }
            }
            
            // 発動地点を確定する
            LotTriggerStartDistance(selectedDistance, selectedDistance);

            _isInitialized = true;
        }

        // パラメータを設定する
        protected virtual void SetParameter()
        {
            // 指定Phaseの開始/終了位置を取得
            _phase = (Gallop.RaceDefine.HorsePhase)_valueRh;
            _raceInfo.GetPhaseDistance(_phase, out _phaseDistance.StartDistance, out _phaseDistance.EndDistance);
        }
        
        //-------------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phase後半(残り50%以降)かつランダムな直線のさらにランダムな一区間で条件を満たす
    /// 類似処理のSkillTriggerPhaseStraightRandomを継承
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLatterHalfStraightRandom : SkillTriggerPhaseStraightRandom
    {
        //-------------------------------------------------------------------
        public SkillTriggerPhaseLatterHalfStraightRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }
        
        //-------------------------------------------------------------------
        protected override void _InitDistance()
        {
            // 継承元と同じ処理
            base._InitDistance();
        }
        
        // パラメーターの設定のみオーバーライド
        protected override void SetParameter()
        {
            // 指定Phaseの開始/終了位置を取得
            _phase = (Gallop.RaceDefine.HorsePhase)_valueRh;
            // 指定Phase残り50%以降で発動
            _phaseDistance = GetStartAndEndDistanceByPhaseAndRate(_phase, 0.5f, 1f);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phase前半(残り50%以前)かつランダムな直線のさらにランダムな一区間で条件を満たす
    /// 類似処理のSkillTriggerPhaseStraightRandomを継承
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstHalfStraightRandom : SkillTriggerPhaseStraightRandom
    {
        //-------------------------------------------------------------------
        public SkillTriggerPhaseFirstHalfStraightRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        // パラメーターの設定のみオーバーライド
        protected override void SetParameter()
        {
            // 指定Phaseの開始/終了位置を取得
            _phase = (Gallop.RaceDefine.HorsePhase)_valueRh;
            // 指定Phase残り50%以降で発動
            _phaseDistance = GetStartAndEndDistanceByPhaseAndRate(_phase, 0f, 0.5f);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終直線内のランダムな一区間で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastStraightRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerLastStraightRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            _hasDistance = false;
            _isInitialized = true;
            CourseStraight targetStraight = null;
            
            var straights = _courseAttributeAccessor.GetStraightList();
            if (null == straights)
            {
                // 直線が無ければ条件を満たさない。
                return;
            }

            // 直線が存在しない場合（は存在しないと思うが）初期化済みとして終了。一応エラーとして報告もしておく。
            if (straights.Count <= 0)
            {
                Debug.Assert(false, "直線の数が0です。");
                return;
            }

            // イベントパラメータの有無によって最終直線の判定が異なる
            if (RaceManagerSimulate.Instance.IsExistLastStraightEvent)
            {
                for (int i = straights.Count - 1; i >= 0; --i)
                {
                    var straight = straights[i];
                    if (straight.IsLast)
                    {
                        targetStraight = straight;
                        break;
                    }
                }
            }
            else
            {
                // ※最終直線は、最終コーナー以降の直線のこと。HorseInfoBase.IsStraightLastと実装を合わせてある。

                if (!RaceUtil.CalcFinalCornerDistance(_raceInfo.CourseDistance, out float finalCornerStartDistance, out _))
                {
                    // 最終コーナーが無ければ最終直線も無いので条件を満たさない。
                    return;
                }

                // コースの最後の直線から調べていき、最終コーナー以降の直線を見つける。
                for (int i = straights.Count - 1; i >= 0; --i)
                {
                    var straight = straights[i];
                    if (straight.StartDistance >= finalCornerStartDistance)
                    {
                        targetStraight = straight;
                        break;
                    }
                }
            }

            // 選ばれた直線区間内のどこで発動するかを抽選。
            if (targetStraight != null)
            {
                LotTriggerStartDistance(targetStraight.StartDistance, targetStraight.EndDistance);
                _hasDistance = true;
            }
            else
            {
                Debug.Assert(false, "最終直線が見つかりませんでした");
                _hasDistance = false;
            }
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }
        
        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category => SkillDefine.SkillTriggerCategory.Timing;
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終直線に入った次のフレームだけ条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastStraightOneTime : SkillTriggerBase
    {
        private bool _isLastStraightPrev = false;

        //---------------------------------------------------------------
        public SkillTriggerLastStraightOneTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            if (_isLastStraightPrev)
            {
                return false;
            }
            
            // 最初にIsStraightLast=trueになったフレームだけ条件を満たす。
            _isLastStraightPrev = _owner.IsStraightLast;
            return _isLastStraightPrev;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category => SkillDefine.SkillTriggerCategory.Param;
        
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終直線にいるとき条件を満たす
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastStraight : SkillTriggerBase
    {
        //---------------------------------------------------------------
        public SkillTriggerLastStraight(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            return _owner.IsStraightLast;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category => SkillDefine.SkillTriggerCategory.Timing;

    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：直線の正面/向こう正面。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerStraightFrontType : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerStraightFrontType(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.CurStraightFrontType;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category => SkillDefine.SkillTriggerCategory.Timing;
        
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：現在の坂。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSlope : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerSlope(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(SkillDefine.SkillTriggerOperator.Equal == buildInfo.op || SkillDefine.SkillTriggerOperator.NotEqual == buildInfo.op);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.SlopeType;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：坂の種類指定でランダム。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerSlopeRandom : SkillTriggerDistanceBase
    {
        protected abstract Gallop.RaceDefine.SlopeType SlopeType { get; }

        //---------------------------------------------------------------
        protected SkillTriggerSlopeRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            _isInitialized = true;
                
            // ctorで指定された種類の坂を取得。
            var slopeList = _courseAttributeAccessor.GetSlopeList()
                .Where(x => x.SlopeType == SlopeType)
                .ToArray();
            
            // 指定の坂がコースに含まれているかどうか。含まれていないならこの条件は絶対に成立しない。
            _hasDistance = slopeList.Length >= 1;
            if (!_hasDistance)
            {
                return;
            }

            // ランダムな坂の開始～終了地点の中から条件を満たす距離を抽選する。
            int slopeIndex = _randomGenerator.GetRandom(slopeList.Length);
            var slope = slopeList[slopeIndex];
            LotTriggerStartDistance(slope.StartDistance, slope.EndDistance);
        }
    }
    public partial class SkillTriggerUpSlopeRandom : SkillTriggerSlopeRandom
    {
        protected override Gallop.RaceDefine.SlopeType SlopeType => Gallop.RaceDefine.SlopeType.Up;
        public SkillTriggerUpSlopeRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo) {}
    }
    public partial class SkillTriggerDownSlopeRandom : SkillTriggerSlopeRandom
    {
        protected override Gallop.RaceDefine.SlopeType SlopeType => Gallop.RaceDefine.SlopeType.Down;
        public SkillTriggerDownSlopeRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo) {}
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：坂の種類指定でランダム（レース後半50%）
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerSlopeRandomLaterHalf : SkillTriggerDistanceBase
    {
        protected abstract Gallop.RaceDefine.SlopeType SlopeType { get; }

        //---------------------------------------------------------------
        protected SkillTriggerSlopeRandomLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            _isInitialized = true;
            var halfDist = _raceInfo.CourseDistance / 2f;
                
            // ctorで指定された種類の坂を取得。
            var slopeList = _courseAttributeAccessor.GetSlopeList()
                .Where(x => x.SlopeType == SlopeType)
                .Where(x => x.EndDistance > halfDist)    // レース後半50%以降に終了地点がある坂
                .ToArray();
            
            // 指定の坂がコースに含まれているかどうか。含まれていないならこの条件は絶対に成立しない。
            _hasDistance = slopeList.Length >= 1;
            if (!_hasDistance)
            {
                return;
            }

            // ランダムな坂の開始～終了地点の中から条件を満たす距離を抽選する。
            int slopeIndex = _randomGenerator.GetRandom(slopeList.Length);
            var slope = slopeList[slopeIndex];
            var startDist = System.Math.Max(slope.StartDistance, halfDist);    // 坂がコース50%地点に跨っている場合のケア
            LotTriggerStartDistance(startDist, slope.EndDistance);
        }
    }
    public partial class SkillTriggerUpSlopeRandomLaterHalf : SkillTriggerSlopeRandomLaterHalf
    {
        protected override Gallop.RaceDefine.SlopeType SlopeType => Gallop.RaceDefine.SlopeType.Up;
        public SkillTriggerUpSlopeRandomLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo) {}
    }
    public partial class SkillTriggerDownSlopeRandomLaterHalf : SkillTriggerSlopeRandomLaterHalf
    {
        protected override Gallop.RaceDefine.SlopeType SlopeType => Gallop.RaceDefine.SlopeType.Down;
        public SkillTriggerDownSlopeRandomLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo) {}
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：現在のコーナー番号。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCorner : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCorner(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(SkillDefine.SkillTriggerOperator.Equal == buildInfo.op || SkillDefine.SkillTriggerOperator.NotEqual == buildInfo.op);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurCorner;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerCornerRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCornerRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            // 指定されたコーナーの区間を取得し、その区間のどの地点で発動するかをランダムで決定する。
            int corner = _valueRh;
            float distanceMin = 0;
            float distanceMax = 0;
            if (!RaceUtil.CalcCornerDistance(corner, out distanceMin, out distanceMax))
            {
                return;
            }
            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }

    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phaseかつランダムなコーナーのさらにランダムな一区間で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseCornerRandom : SkillTriggerDistanceBase
    {
        // 指定Phase
        protected Gallop.RaceDefine.HorsePhase _phase;
        protected (float StartDistance, float EndDistance) phaseDistance;

        //---------------------------------------------------------------
        public SkillTriggerPhaseCornerRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            // 指定Phaseの開始/終了位置を取得
            SetParameter();

            var cornerList = _courseAttributeAccessor.GetCornerList();
            if (null == cornerList)
            {
                return;
            }

            // コーナー途中でPhaseが切り替わることもあるので、該当コーナー箇所の距離を合算し、その中で抽選をする
            float totalPhaseCornerDistance = 0f;
            for (int i = 0, cnt = cornerList.Count; i < cnt; i++)
            {
                var startDistance = System.Math.Max(phaseDistance.StartDistance, cornerList[i].StartDistance);
                var endDistance = System.Math.Min(phaseDistance.EndDistance, cornerList[i].EndDistance);
                var deltaDistance = endDistance - startDistance;
                if (deltaDistance < 0) continue; // 指定Phaseにかかっていないコーナーはスルー
                totalPhaseCornerDistance += deltaDistance;
            }
            
            // コーナーが見つからなかったなら、抜ける
            if (RaceUtilMath.Approximately(totalPhaseCornerDistance, 0f))
            {
                return;
            }
            
            // 合算したコーナー距離の中の１点を選ぶ
            var selectedDistance = 0f;
            var selectedCornerDistance = _randomGenerator.GetRandom(totalPhaseCornerDistance);
            // それがどのコーナーのどこなのかを考慮し、実際の距離に戻す
            for (int i = 0, cnt = cornerList.Count; i < cnt; i++)
            {
                // そのコーナーより長いなら、丸ごと距離を引く
                var startDistance = System.Math.Max(phaseDistance.StartDistance, cornerList[i].StartDistance);
                var endDistance = System.Math.Min(phaseDistance.EndDistance, cornerList[i].EndDistance);
                var deltaDistance = endDistance - startDistance;
                if (deltaDistance < 0) continue; // 指定Phaseにかかっていないコーナーはスルー
                if (selectedCornerDistance > deltaDistance)
                {
                    selectedCornerDistance -= deltaDistance;
                    continue;
                }
                // そうでないなら、そのコーナー内に該当箇所があるので、コーナー開始地点に残り距離を足す
                else
                {
                    selectedDistance = startDistance + selectedCornerDistance;
                    break;
                }
            }

            // 同距離を入れた場合、その距離で確定する
            LotTriggerStartDistance(selectedDistance, selectedDistance);

            _isInitialized = true;
        }

        // パラメーターの設定用関数
        protected virtual void SetParameter()
        {
            // 指定Phaseの開始/終了位置を取得
            _phase = (Gallop.RaceDefine.HorsePhase)_valueRh;
            _raceInfo.GetPhaseDistance(_phase, out phaseDistance.StartDistance, out phaseDistance.EndDistance);
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース内の指定Phase後半(残り50%以降)かつランダムなコーナーのさらにランダムな一区間で条件を満たす。
    /// 類似処理のSkillTriggerPhaseCornerRandomを継承
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLatterHalfCornerRandom : SkillTriggerPhaseCornerRandom
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseLatterHalfCornerRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            // 継承元と同じ処理
            base._InitDistance();
        }

        // パラメーターの設定のみオーバーライド
        protected override void SetParameter()
        {
            // 指定Phaseの開始/終了位置を取得
            _phase = (Gallop.RaceDefine.HorsePhase)_valueRh;
            phaseDistance= GetStartAndEndDistanceByPhaseAndRate(_phase, 0.5f, 1f);
        }

    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：全てのコーナーでランダム。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerAllCornerRandom : SkillTriggerDistanceSetBase
    {
        /// <summary>前コーナーから条件を満たす区間をこの回数抽選する。</summary>
        private const int CHECK_NUM = 4;
        
        //---------------------------------------------------------------
        public SkillTriggerAllCornerRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void InitDistance()
        {
            // コース上のコーナーを作業用にCourseCornerdで複製する。
            var cornerTmpList = _courseAttributeAccessor
                .GetCornerList()
                .Select(x => new CourseCorner(x.cornerNumber, x.StartDistance, x.EndDistance, x.IsFinalCorner))
                .ToList();
            
            for (int i = 0; i < CHECK_NUM; i++)
            {
                // 抽選対象のコーナーが存在しない場合、これ以降の抽選は行わない。
                if (cornerTmpList.Count == 0)
                {
                    break;
                }
                
                // どのコーナーを使うか抽選。
                int cornerIndex = _randomGenerator.GetRandom(0, cornerTmpList.Count);

                // 選んだコーナーの中のどこで条件を満たすかを抽選。
                var cornerTmp = cornerTmpList[cornerIndex];
                LotAndAddTriggerStartDistance(
                    cornerTmp.StartDistance, 
                    cornerTmp.EndDistance, 
                    out _, 
                    out float selectMax);

                // 選んだコーナーは次回の抽選にも使えるが、条件を満たす区間は今回選んだ区間の終了（selectMax）から。
                if (cornerTmp.EndDistance - selectMax >= DISTANCE_RANGE)
                {
                    cornerTmp.StartDistance = selectMax;
                }
                else
                {
                    cornerTmpList.Remove(cornerTmp);
                }

                // 今回選んだコーナーより前のコーナーは次回以降の抽選で使わない。
                cornerTmpList.RemoveAll(x => x.StartDistance < cornerTmp.StartDistance);
            }

            _isInitialized = true;
            
            // 一度抽選が完了したら、記録したときのパラメーターを保持しておく（別のランダムスキルが設定された場合をケア）
            _ownerSkill.ReserveActivateDistanceParam(GetType(), _valueRh);
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                InitDistance();
            }
        }
    }
    

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー通過済みかどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFinalCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCorner : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerFinalCorner(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsFinalCorner ? 1 : 0;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerFinalCornerRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            // 指定されたコーナーの区間を取得し、その区間のどの地点で発動するかをランダムで決定する。
            float distanceMin = 0;
            float distanceMax = 0;
            if (!RaceUtil.CalcFinalCornerDistance(_raceInfo.CourseDistance, out distanceMin, out distanceMax))
            {
                return;
            }
            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナーの半分以降。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerLaterHalf : SkillTriggerParamBase
    {
        private bool _isInitialized;
        private bool _isExistFinalCorner;
        private float _distanceMin;
        private float _distanceMax;
        
        //---------------------------------------------------------------
        public SkillTriggerFinalCornerLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            if (!_isInitialized)
            {
                return false;
            }
            if (!_isExistFinalCorner)
            {
                return false;
            }
            return CheckValue();
        }
        
        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            float distance = _owner.GetDistance();
            return (distance >= _distanceMin && distance <= _distanceMax) ? 1 : 0;
        }
        
        //---------------------------------------------------------------
        private void InitDistance()
        {
            _isInitialized = true;
            
            // 最終コーナーの区間を取得。
            if (!RaceUtil.CalcFinalCornerDistance(_raceInfo.CourseDistance, out _distanceMin, out _distanceMax))
            {
                // 最終コーナーは無し。
                _isExistFinalCorner = false;
                return;
            }

            // 最終コーナーの半分以降で条件を満たさせるため、開始距離を補正。
            float cornerLength = _distanceMax - _distanceMin;
            _distanceMin = _distanceMin + cornerLength * 0.5f;

            _isExistFinalCorner = true;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                InitDistance();
            }
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース中盤かつ最終コーナー内のランダム区間
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerRandomPhaseMiddle : SkillTriggerPhaseFinalCornerRandom
    {
        /// <summary> 限定する対象の終盤フェーズの定義 </summary>
        protected override Gallop.RaceDefine.HorsePhase Phase => Gallop.RaceDefine.HorsePhase.MiddleRun;

        //---------------------------------------------------------------
        public SkillTriggerFinalCornerRandomPhaseMiddle(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// 特定のフェーズかつ最終コーナー内のランダム区間に発動するスキルの抽象クラス
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalCornerRandomPhaseEnd : SkillTriggerPhaseFinalCornerRandom
    {
        /// <summary> 限定する対象の終盤フェーズの定義 </summary>
        protected override Gallop.RaceDefine.HorsePhase Phase => Gallop.RaceDefine.HorsePhase.End;

        //---------------------------------------------------------------
        public SkillTriggerFinalCornerRandomPhaseEnd(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：
    /// SkillTriggerFinalCornerRandomのフェーズを限定できるようにしたもの
    /// SkillTriggerFinalCornerRandom書き換えで問題なければそっちに統合してもよいかもしれない（影響範囲要調査）
    /// </summary>
    //-------------------------------------------------------------------
    public abstract partial class SkillTriggerPhaseFinalCornerRandom : SkillTriggerDistanceBase
    {
        /// <summary> 任意のフェーズが最終コーナーの中に入っているかどうか </summary>
        protected bool _isPhaseInFinalCorner = false;
        
        /// <summary> 限定する対象のフェーズ </summary>
        protected virtual Gallop.RaceDefine.HorsePhase Phase { get; }

        //---------------------------------------------------------------
        public SkillTriggerPhaseFinalCornerRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            // 指定されたコーナーの区間を取得し、その区間のどの地点で発動するかをランダムで決定する。
            float distanceMin = 0;
            float distanceMax = 0;
            if (!RaceUtil.CalcFinalCornerDistance(_raceInfo.CourseDistance, out distanceMin, out distanceMax))
            {
                return;
            }

            //対象にするフェーズの区間で修正する
            //SkillTriggerFinalCornerRandomとの_InitDistanceの処理の差分
            var distance = ApplyPhaseDistance(distanceMin, distanceMax);

            LotTriggerStartDistance(distance.distanceMin, distance.distanceMax);

            _isInitialized = true;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            if (!_isInitialized)
            {
                _InitDistance();
            }
        }
        
        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 対象のフェーズが最終コーナーに含まれないなら絶対に発動しない
            //ApplyPhaseDistanceが区間0,0を返す時点で発動しないはずなので消しても問題ないはずではあるが明示的に無理にしてます
            if (!_isPhaseInFinalCorner)
            {
                return false;
            }

            return base.CheckActivate();
        }

        /// <summary>
        /// フェーズの区間分距離を補正する
        /// 中盤のみなら終了地点を中盤終了地点と合わせる
        /// 終盤のみなら開始地点を終盤開始地点と合わせる
        /// </summary>
        /// <param name="distanceMin">最終コーナーの開始地点</param>
        /// <param name="distanceMax">最終コーナーの終了地点</param>
        /// <returns>限定するフェーズを考慮した区間を返す</returns>
        private (float distanceMin, float distanceMax) ApplyPhaseDistance(float distanceMin,
            float distanceMax)
        {
            float phaseDistanceMin, phaseDistanceMax; //フェーズの区間
            _raceInfo.GetPhaseDistance(Phase, out phaseDistanceMin, out phaseDistanceMax);
            var resultDistance =
                GetIntersection(phaseDistanceMin, phaseDistanceMax, distanceMin, distanceMax);
            return resultDistance;
        }
        
        /// <summary>
        /// 二つの区間の積集合の区間を返す
        /// </summary>
        /// <param name="distanceMin1">区間1の最小値</param>
        /// <param name="distanceMax1">区間1の最大値</param>
        /// <param name="distanceMin2">区間2の最小値</param>
        /// <param name="distanceMax2">区間2の最大値</param>
        /// <returns>二つの区間の共通区間</returns>
        private (float distanceMin, float DistanceMax) GetIntersection(float distanceMin1, float distanceMax1,
            float distanceMin2, float distanceMax2)
        {
            if (RaceUtilMath.Approximately(distanceMin2, distanceMax1) ||
                RaceUtilMath.Approximately(distanceMin1, distanceMax2))
            {
                return (0, 0);
            }

            if (distanceMin2 > distanceMax1 || distanceMin1 > distanceMax2) //区間がかぶってなかった場合は0,0を返す
            {
                return (0, 0);
            }

            _isPhaseInFinalCorner = true;
            float resultDistanceMin = distanceMin2 > distanceMin1 ? distanceMin2 : distanceMin1;
            float resultDistanceMax = distanceMax2 < distanceMax1 ? distanceMax2 : distanceMax1;
            return (resultDistanceMin, resultDistanceMax);
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：天気。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerWeather"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerWeather : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerWeather(SkillTriggerBuildInfo buildInfo) : base( buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_raceInfo.Weather;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：季節。
    /// </summary>
    /// <see cref="Test.TestSkillTriggerSeason"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSeason : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerSeason(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_raceInfo.Season;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レースId。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerRaceId"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceId : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerRaceId(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _raceInfo.RaceId;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：出走からの経過時間。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerRaceAccumulateTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceAccumulateTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerRaceAccumulateTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_timeAccessor.AccumulateTimeSinceStart);
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：出走からの経過時間が、指定範囲内でランダムに選ばれた１秒の間だけ条件を満たす。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerRaceAccumulateTimeRandom"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceAccumulateTimeRandom : SkillTriggerParamBase
    {
        private readonly int _randomStartTime = 0;

        //---------------------------------------------------------------
        public SkillTriggerRaceAccumulateTimeRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 指定された時間までの範囲内で条件を満たす。
            // valueRhで5秒が指定された場合、まず始点となる0~4が抽選され、その始点から+1秒の範囲内で条件を満たす。
            // 例：抽選で3秒が選ばれた場合、AccumulateTimeSinceStartは3.0 ~ 3.9999…の範囲で条件を満たす。
            _randomStartTime = _randomGenerator.GetRandom(buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // レース経過時間の端数切捨てにより、発動範囲1秒を実現する。
            int accumulateTime = (int)_timeAccessor.AccumulateTimeSinceStart;
            return _randomStartTime == accumulateTime;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return 0;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }
    #endregion <レース系>


    #region <競馬場系>
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離が根幹距離かどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCourseBasisDistance"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseBasisDistance : SkillTriggerParamBase
    {
        private int _isBasisDistance = 0;

        //---------------------------------------------------------------
        public SkillTriggerCourseBasisDistance(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _isBasisDistance = RaceUtil.IsBasisDistance(_raceInfo.CourseDistance) ? 1 : 0;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _isBasisDistance;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離種別。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerCourseDistanceType"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseDistanceType : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCourseDistanceType(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_raceInfo.CourseDistanceType;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCourseDistance : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCourseDistance(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _raceInfo.CourseDistance;
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerRaceTrackId : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerRaceTrackId(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_raceInfo.RaceCourseSet.RaceTrackId;
        }
    }
    
    public partial class SkillTriggerTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(buildInfo.op == SkillDefine.SkillTriggerOperator.Equal || buildInfo.op == SkillDefine.SkillTriggerOperator.NotEqual);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int) _raceInfo.Time;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：交流重賞かどうか
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsDirtGrade : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerIsDirtGrade(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _raceInfo.RaceMaster.IsDirtgrade;
        }
    }
    #endregion <競馬場系>


    #region <馬系>
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：特定のキャラIdが出走している。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerExistCharaId : SkillTriggerParamBase
    {
        private readonly bool _isExistCharaId;
        
        //---------------------------------------------------------------
        public SkillTriggerExistCharaId(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(buildInfo.op == SkillDefine.SkillTriggerOperator.Equal || buildInfo.op == SkillDefine.SkillTriggerOperator.NotEqual);
            int charaId = buildInfo.valueRh;
            _isExistCharaId = buildInfo.horseAccessor.GetHorseRaceInfos().Any(x => x.CharaId == charaId);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // ==ならcharaIdが存在することで条件を満たす。
            if (_op == SkillDefine.SkillTriggerOperator.Equal)
            {
                return _isExistCharaId;
            }
            // !=ならcharaIdが存在しないことで条件を満たす。
            else if (_op == SkillDefine.SkillTriggerOperator.NotEqual)
            {
                return !_isExistCharaId;
            }

            return false;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return 0;
        }
    }
        
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：速度スキルを発動した回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateSpeedSkillCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerActivateSpeedSkillCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.GetActivateSpeedSkillCount();
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：回復スキルを発動した回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateHealSkillCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerActivateHealSkillCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.GetActivateHealSkillCount();
        }
    }

        
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分と先頭キャラとの距離差。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffTop : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerDistanceDiffTop(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var topHorse = _horseAccessor.GetFirstHorseInfo();
            var distanceDiff = topHorse.GetDistance() - _owner.GetDistance();
            Debug.Assert(distanceDiff >= 0.0f);

            // 端数切捨て整数で返す。
            return RaceUtilMath.FloorToInt(distanceDiff);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分と先頭キャラとの距離差
    /// </summary>
    /// <remarks> SkillTriggerDistanceDiffTopのfloatバージョン </remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffTopFloat : SkillTriggerParamBaseInputFloat
    {
        /// <summary> 除算する数字(小数点第一位までの値を取りたいので10) </summary>
        private const float DIV_NUM = 10f;

        protected override float divisionNum => DIV_NUM;

        //---------------------------------------------------------------
        public SkillTriggerDistanceDiffTopFloat(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override float GetValueLh()
        {
            var topHorse = _horseAccessor.GetFirstHorseInfo();
            var distanceDiff = topHorse.GetDistance() - _owner.GetDistance();
            Debug.Assert(distanceDiff >= 0.0f);

            // 仕様のため小数点第二位以下を切り捨て
            distanceDiff = (float)System.Math.Floor(distanceDiff * 10) / 10;
            return distanceDiff;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の前のキャラとの距離差。※自分が１位の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerDistanceDiffInfront"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffInfront : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerDistanceDiffInfront(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 自分が１位なら条件は満たせない。
            if (_owner.CurOrder == 0)
            {
                return false;
            }

            return CheckValue();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var infrontHorse = _horseAccessor.GetHorseInfoByOrder(_owner.CurOrder - 1);
            var distanceDiff = infrontHorse.GetDistance() - _owner.GetDistance();
            Debug.Assert(distanceDiff >= 0.0f);

            // 端数切捨て整数で返す。
            return RaceUtilMath.FloorToInt(distanceDiff);
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の後ろのキャラとの距離差。※自分が最後尾の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerDistanceDiffBehind"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceDiffBehind : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerDistanceDiffBehind(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 自分が最後尾なら条件は満たせない。
            if (_owner.HorseIndex == _horseAccessor.GetLastHorseIndex())
            {
                return false;
            }

            return CheckValue();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var behindHorse = _horseAccessor.GetHorseInfoByOrder(_owner.CurOrder + 1);
            var distanceDiff = _owner.GetDistance() - behindHorse.GetDistance();
            Debug.Assert(distanceDiff >= 0.0f);

            // 端数切捨て整数で返す。
            return RaceUtilMath.FloorToInt(distanceDiff);
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の前のキャラとの馬身差。※自分が１位の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerBashinDiffInfront"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBashinDiffInfront : SkillTriggerParamBaseFloat
    {
        //---------------------------------------------------------------
        public SkillTriggerBashinDiffInfront(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 自分が１位なら条件は満たせない。
            if (_owner.CurOrder == 0)
            {
                return false;
            }

            return CheckValue();
        }

        //---------------------------------------------------------------
        protected override float GetValueLh()
        {
            var infrontHorse = _horseAccessor.GetHorseInfoByOrder(_owner.CurOrder - 1);
            var distanceDiff = infrontHorse.GetDistance() - _owner.GetDistance();
            Debug.Assert(distanceDiff >= 0.0f);

            float bashinDiff = RaceUtil.Distance2BashinFloat(distanceDiff);

            return bashinDiff;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の後ろのキャラとの馬身差。※自分が最後尾の時は条件を満たさない。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerBashinDiffBehind"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBashinDiffBehind : SkillTriggerParamBaseFloat
    {
        //---------------------------------------------------------------
        public SkillTriggerBashinDiffBehind(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 自分が最後尾なら条件は満たせない。
            if (_owner.HorseIndex == _horseAccessor.GetLastHorseIndex())
            {
                return false;
            }

            return CheckValue();
        }

        //---------------------------------------------------------------
        protected override float GetValueLh()
        {
            var behindHorse = _horseAccessor.GetHorseInfoByOrder(_owner.CurOrder + 1);
            var distanceDiff = _owner.GetDistance() - behindHorse.GetDistance();
            Debug.Assert(distanceDiff >= 0.0f);

            float bashinDiff = RaceUtil.Distance2BashinFloat(distanceDiff);

            return bashinDiff;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート中かどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerLastSpurt"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsLastSpurt : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerIsLastSpurt(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsLastSpurt ? 1 : 0;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート評価
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLastSpurt : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerLastSpurt(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int) RaceUtil.LastSpurtCalcResult2EvaluateValue(_owner.LastSpurtCalcResult);
        }

        protected override bool CheckActivate()
        {
            // ラストスパート区間に入って最初の計算が行われていることが前提
            if (!_owner.IsCalcLastSpurt)
            {
                return false;
            }

            return base.CheckActivate();
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より育成ランクが高いキャラの数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFinalGradeHigherCount"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalGradeHigherCount : SkillTriggerParamBase
    {
        private readonly int _higherCount = 0;

        //---------------------------------------------------------------
        public SkillTriggerFinalGradeHigherCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _higherCount = RaceUtil.CalcFinalGradeHigherCount(buildInfo.ownerHorse.FinalGrade, buildInfo.horseAccessor);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _higherCount;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より育成ランクが低いキャラの数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerFinalGradeLowerCount"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerFinalGradeLowerCount : SkillTriggerParamBase
    {
        private readonly int _lowerCount = 0;

        //---------------------------------------------------------------
        public SkillTriggerFinalGradeLowerCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _lowerCount = RaceUtil.CalcFinalGradeLowerCount(buildInfo.ownerHorse.FinalGrade, buildInfo.horseAccessor);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _lowerCount;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：追い抜き中かどうか。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerOvertake"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertake : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOvertake(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsOvertake() ? 1 : 0;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：誰かの追い抜き対象になっているか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertakeTargetContinueTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOvertakeTargetContinueTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int timeInt = RaceUtilMath.FloorToInt(_owner.OverTakeTargetContinueTime);
            return timeInt;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分が追い抜き対象を持っている時に順位が上がっていない継続時間。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertakeTargetHaveNoOrderUpContinueTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOvertakeTargetHaveNoOrderUpContinueTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int timeInt = RaceUtilMath.FloorToInt(_owner.OverTakeTargetHaveNoOrderUpContinueTime);
            return timeInt;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分が追い抜き対象を持っている時に順位が下がっていない継続時間。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOvertakeTargetHaveNoOrderDownContinueTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOvertakeTargetHaveNoOrderDownContinueTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int timeInt = RaceUtilMath.FloorToInt(_owner.OverTakeTargetHaveNoOrderDownContinueTime);
            return timeInt;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レーン種類。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerLaneType"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLaneType : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerLaneType(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.Lane;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レーン比率。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerLanePer : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerLanePer(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_owner.LaneDistancePerInAllHorses);
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：一定時間同じ方向にレーン移動した。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerMoveLane : SkillTriggerParamBase
    {
        private const float LANE_MOVE_CONTINUE_TIME = 1.0f;
        private const int MOVE_DIRECTION_NULL = 0;
        private const int MOVE_DIRECTION_IN = 1;
        private const int MOVE_DIRECTION_OUT = 2;
        
        //---------------------------------------------------------------
        public SkillTriggerMoveLane(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            if (_owner.MoveLaneContinueTime < LANE_MOVE_CONTINUE_TIME)
            {
                return MOVE_DIRECTION_NULL;
            }

            return _owner.MoveLaneContinueDirection == Gallop.RaceDefine.LaneDirection.In
                ? MOVE_DIRECTION_IN
                : MOVE_DIRECTION_OUT;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定の順位変動があった。順位変動した次のフレームだけ条件を満たす。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderOneTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderOneTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderOneTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 順位の差分計算。
            // +:順位が落ちている。
            // =:順位が変わっていない。
            // -:順位が上がっている。
            int changeNum = _owner.CurOrder - _owner.PrevOrder;
            return changeNum;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定の順位変動があった。一度指定の順位変動が発生したら、条件を満たし続ける。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrder"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrder : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrder(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 一度条件を満たしたら、その状態を維持する。
            if (IsActivated)
            {
                return true;
            }
            else
            {
                return base.CheckActivate();
            }
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 順位の差分計算。
            // +:順位が落ちている。
            // =:順位が変わっていない。
            // -:順位が上がっている。
            int changeNum = _owner.CurOrder - _owner.PrevOrder;
            return changeNum;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：中盤で他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpPhaseMiddle"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpPhaseMiddle : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderUpPhaseMiddle(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderUpCountPhaseMiddle;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：終盤以降で他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpPhaseEndAfter"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpPhaseEndAfter : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderUpPhaseEndAfter(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderUpCountPhaseEndAfter;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コーナーで他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpCorner : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderUpCorner(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderUpCountCorner;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート中に他キャラを追い抜いた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderUpLastSpurt"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpLastSpurt : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderUpLastSpurt(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderUpCountLastSpurt;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー以降に他キャラを追い抜いた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpFinalCornerAfter : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderUpFinalCornerAfter(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderUpCountFinalCornerAfter;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：後半50%以降に他キャラを追い抜いた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderUpLaterHalf : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderUpLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderUpCountLaterHalf;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：中盤で他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownPhaseMiddle"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownPhaseMiddle : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderDownPhaseMiddle(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderDownCountPhaseMiddle;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：終盤以降で他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownPhaseEndAfter"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownPhaseEndAfter : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderDownPhaseEndAfter(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderDownCountPhaseEndAfter;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コーナーで他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownCorner"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownCorner : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderDownCorner(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderDownCountCorner;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// テスト：スキル発動条件：ラストスパート中に他キャラに追い抜かれた回数。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerChangeOrderDownLastSpurt"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownLastSpurt : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderDownLastSpurt(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderDownCountLastSpurt;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー以降に他キャラに追い抜かれた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownFinalCornerAfter : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderDownFinalCornerAfter(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderDownCountFinalCornerAfter;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：後半50%以降に他キャラに追い抜かれた回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerChangeOrderDownLaterHalf : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerChangeOrderDownLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CurOrderDownCountLaterHalf;
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyle : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerRunningStyle(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.RunningStyle;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の選んだ走法がレース参加者中で最も多い。※同数の場合は発動しない。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsRunningStyleCountMax : SkillTriggerParamBase
    {
        private readonly bool _isSelfStyleMax = false;

        //---------------------------------------------------------------
        public SkillTriggerIsRunningStyleCountMax(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _isSelfStyleMax = RaceUtil.CheckRunningStyleCountSameMax(_owner.RunningStyle, buildInfo.horseAccessor);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _isSelfStyleMax ? 1 : 0;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース開始後ｎ秒後から現時点まで、順位が上位ｍ％以内をキープしているかどうか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateInContinue : SkillTriggerParamBase
    {
        private readonly SkillDefine.OrderInType _type;
        
        //---------------------------------------------------------------
        public SkillTriggerOrderRateInContinue(SkillTriggerBuildInfo buildInfo, SkillDefine.OrderInType type) : base(buildInfo)
        {
            _type = type;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsOrderInContinue(_type) ? 1 : 0;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：レース開始後ｎ秒後から現時点まで、順位が上位ｍ％以下をキープしているかどうか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateOutContinue : SkillTriggerParamBase
    {
        private readonly SkillDefine.OrderOutType _type;
        
        //---------------------------------------------------------------
        public SkillTriggerOrderRateOutContinue(SkillTriggerBuildInfo buildInfo, SkillDefine.OrderOutType type) : base(buildInfo)
        {
            _type = type;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsOrderOutContinue(_type) ? 1 : 0;
        }
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrder : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOrder(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～18で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= _raceInfo.NumRaceHorses);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            int retOrder = _owner.CurOrder + 1;
            return retOrder;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：順位がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRate : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOrderRate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.NumRaceHorses, buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            return _owner.CurOrder + 1;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート開始時の順位がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateLastSpurt : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOrderRateLastSpurt(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.NumRaceHorses, buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // ラストスパート開始時順位計算するまでは条件満たさない。
            if (_owner.LastSpurtOrder < 0)
            {
                return false;
            }
            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            return _owner.LastSpurtOrder + 1;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ラストスパート開始時の順位がｎ。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderLastSpurt : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOrderLastSpurt(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～18で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= _raceInfo.NumRaceHorses);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // ラストスパート開始時順位計算するまでは条件満たさない。
            if (_owner.LastSpurtOrder < 0)
            {
                return false;
            }
            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            return _owner.LastSpurtOrder + 1;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー終了時の順位がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderRateFinalCornerEnd : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOrderRateFinalCornerEnd(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.NumRaceHorses, buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 最終コーナー終了時順位計算するまでは条件満たさない。
            if (!_owner.IsFinalCornerEndOrderInitialized)
            {
                return false;
            }
            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            return _owner.FinalCornerEndOrder + 1;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：最終コーナー終了時の順位がｎ。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOrderFinalCornerEnd : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOrderFinalCornerEnd(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～18で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= _raceInfo.NumRaceHorses);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 最終コーナー終了時順位計算するまでは条件満たさない。
            if (!_owner.IsFinalCornerEndOrderInitialized)
            {
                return false;
            }
            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            return _owner.FinalCornerEndOrder + 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：やる気
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerMotivation : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerMotivation(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(buildInfo.valueRh > (int)Gallop.RaceDefine.Motivation.None && buildInfo.valueRh <= (int)Gallop.RaceDefine.Motivation.Max);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.Motivation;
        }
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularity : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPopularity(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            return _owner.Popularity + 1;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：人気がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularityRate : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPopularityRate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.NumRaceHorses, buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1～18に換算するため+1。
            return _owner.Popularity + 1;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerGateNumber : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerGateNumber(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～18で指定してもらう。
            // 凱旋門賞対応から20まで指定できるようになりました
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= Gallop.RaceDefine.RACE_HORSE_MAX);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.HorseIndex + 1;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：馬番がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerGateNumberRate : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerGateNumberRate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.NumRaceHorses, buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.HorseIndex + 1;
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerPostNumber : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPostNumber(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～8で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= Gallop.RaceDefine.POST_NUMBER_MIN && buildInfo.valueRh <= Gallop.RaceDefine.POST_NUMBER_MAX);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.PostNumber;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：枠番がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPostNumberRate : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPostNumberRate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.PostNumberMax, buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.PostNumber;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：距離が全体のｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseDistanceRate : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseDistanceRate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.CourseDistance, buildInfo.valueRh);
        }

#if false // _valueRhを補正する方で他の%系と統一するため不要。
        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            float courseDistance = RaceManager.RaceInfo.CourseDistance;
            float horseDistance = _owner.GetDistance();

            // 自分の距離がコース全体の何%かを百分率で返す。
            int per = Mathf.FloorToInt( ( horseDistance / courseDistance ) * 100 );
            return per;
        }
#else
        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 端数切捨て。
            return (int)_owner.GetDistance();
        }
#endif
        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：残り距離。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseRemainDistance : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseRemainDistance(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int distance = RaceUtilMath.FloorToInt(_owner.GetDistance());
            return _raceInfo.CourseDistance - distance;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：ViewerIdの振られているキャラの中でトップのキャラの残り距離。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseViewerIdRemainDistance : SkillTriggerParamBase
    {
        private readonly IHorseRaceInfoSimulate[] _targetHorseArray;
        
        //---------------------------------------------------------------
        public SkillTriggerHorseViewerIdRemainDistance(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _targetHorseArray = buildInfo.horseAccessor.GetHorseRaceInfos().Where(x => x.ViewerId != 0).ToArray();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // トップのキャラの現在距離を取得。
            float maxDistance = _targetHorseArray.Max(x => x.GetDistance());
            
            int distance = RaceUtilMath.FloorToInt(maxDistance);
            return _raceInfo.CourseDistance - distance;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：一位のキャラからの距離差が一位と最下位との距離差に比べて何%か。
    /// </summary>
    /// <remarks>
    /// 一位に近いほど0% .. 最下位に近いほど100%。
    /// </remarks>
    /// <seealso cref="Test.SkillTriggerHorseDistanceDiffRate"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseDistanceDiffRate : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseDistanceDiffRate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 0～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 0 && buildInfo.valueRh <= 100);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var topHorse = _horseAccessor.GetFirstHorseInfo();
            var lastHorse = _horseAccessor.GetLastHorseInfo();

            float topLastDiff = topHorse.GetDistance() - lastHorse.GetDistance();
            float topSelfDiff = topHorse.GetDistance() - _owner.GetDistance();

            // 一応0割りチェック。可能性は限りなく0に近いが同じDistanceにいることはありえる。
            if (RaceUtilMath.Approximately(topLastDiff, 0))
            {
                return 0;
            }
            
            float rate = topSelfDiff / topLastDiff;
            return RaceUtilMath.FloorToInt(rate * 100);
        }
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhase : SkillTriggerParamBase
    {
#if CYG_DEBUG
        protected override bool IsEnableIgnoreSkillTrigger() { return false; }
#endif

        //---------------------------------------------------------------
        public SkillTriggerPhase(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.GetPhase();
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Timing; }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの前半の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstHalf : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseFirstHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        private void InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            var (distanceMin, distanceMax) = GetStartAndEndDistanceByPhaseAndRate(phase, 0f, 0.5f);
            Distance = (distanceMin, distanceMax);

            _isInitialized = true;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの前半1/4の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstQuarter : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseFirstQuarter(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        private void InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            var (distanceMin, distanceMax) = GetStartAndEndDistanceByPhaseAndRate(phase, 0f, 0.25f);
            Distance = (distanceMin, distanceMax);

            _isInitialized = true;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの後半の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLaterHalf : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        private void InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            var (distanceMin, distanceMax) = GetStartAndEndDistanceByPhaseAndRate(phase, 0.5f, 1.0f);
            Distance = (distanceMin, distanceMax);

            _isInitialized = true;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの後半1/4の間条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLaterQuarter : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseLaterQuarter(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        private void InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            var (distanceMin, distanceMax) = GetStartAndEndDistanceByPhaseAndRate(phase, 0.75f, 1.0f);
            Distance = (distanceMin, distanceMax);

            _isInitialized = true;
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            _InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        protected void _InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            // 指定されたPhase距離区間を取得し、その距離区間のどの地点で発動するかをランダムで決定する。
            float distanceMin = 0;
            float distanceMax = 0;
            _raceInfo.GetPhaseDistance(phase, out distanceMin, out distanceMax);
            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseLaterHalfRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseLaterHalfRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            _InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        protected void _InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            // 指定されたPhase距離区間を取得し、その距離区間のどの地点で発動するかをランダムで決定する。
            float distanceMin = 0;
            float distanceMax = 0;
            _raceInfo.GetPhaseDistance(phase, out distanceMin, out distanceMax);
            // Phase距離区間の半分以降で条件を満たさせるため、開始距離を補正。
            float phaseLength = (distanceMax - distanceMin);
            distanceMin = distanceMin + phaseLength * 0.5f;

            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの前半で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstHalfRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseFirstHalfRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        private void InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            (float distanceMin, float distanceMax) = GetStartAndEndDistanceByPhaseAndRate(phase, 0f, 0.5f);
            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定Phaseの冒頭1/4で条件を満たす。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerPhaseFirstQuarterRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerPhaseFirstQuarterRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            InitDistanceByPhase((Gallop.RaceDefine.HorsePhase)_valueRh);
        }

        //---------------------------------------------------------------
        private void InitDistanceByPhase(Gallop.RaceDefine.HorsePhase phase)
        {
            (float distanceMin, float distanceMax) = GetStartAndEndDistanceByPhaseAndRate(phase, 0f, 0.25f);

            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }
    }
    

    //-------------------------------------------------------------------
    public partial class SkillTriggerAllPhaseRandom : SkillTriggerPhaseRandom
    {
        private static readonly Gallop.RaceDefine.HorsePhase[] RANDOM_PHASES = new Gallop.RaceDefine.HorsePhase[]
        {
            Gallop.RaceDefine.HorsePhase.Start,
            Gallop.RaceDefine.HorsePhase.MiddleRun,
            Gallop.RaceDefine.HorsePhase.End,
        };

        //---------------------------------------------------------------
        public SkillTriggerAllPhaseRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            var randomIndex = _randomGenerator.GetRandom(RANDOM_PHASES.Length);
            _InitDistanceByPhase(RANDOM_PHASES[randomIndex]);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：コース距離の指定比率以降のランダム地点で発動。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerDistanceRateAfterRandom : SkillTriggerDistanceBase
    {
        //---------------------------------------------------------------
        public SkillTriggerDistanceRateAfterRandom(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override void _InitDistance()
        {
            InitDistanceByRate(_valueRh);
        }

        //---------------------------------------------------------------
        protected void InitDistanceByRate(int ratePer)
        {
            // ratePerは百分率で入力されているはず。
            Debug.Assert(ratePer >= 0 && ratePer <= 100);
            
            float distanceMin = _raceInfo.CourseDistance * ((float)ratePer / 100);
            float distanceMax = _raceInfo.CourseDistance;

            LotTriggerStartDistance(distanceMin, distanceMax);

            _isInitialized = true;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１前のキャラと一定距離内に一定時間いる。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerHorseInfrontNearTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseInfrontNearTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseInfrontNearTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_owner.InfrontHorseNearContinueTime);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１後のキャラと一定距離内に一定時間いる。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerHorseBehindNearTime"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindNearTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseBehindNearTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_owner.BehindHorseNearContinueTime);
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１前のキャラと一定距離内かつ一定レーン内に一定時間いる。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseInfrontNearLaneTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseInfrontNearLaneTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_owner.InfrontHorseNearLaneContinueTime);
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１後のキャラと一定距離内かつ一定レーン内に一定時間いる。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindNearLaneTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseBehindNearLaneTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_owner.BehindHorseNearLaneContinueTime);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１後のキャラと一定距離内かつ一定レーン内に一定時間いる。パラメータセット指定版。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindNearLaneTimeParamSet : SkillTriggerParamBase
    {
        private readonly int _paramSet;
        
        //---------------------------------------------------------------
        public SkillTriggerHorseBehindNearLaneTimeParamSet(SkillTriggerBuildInfo buildInfo, int paramSet) : base(buildInfo)
        {
            _paramSet = paramSet;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_owner.GetBehindHorseNearLaneContinueTime(_paramSet));
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１下のキャラが、自分よりインにいる。
    /// </summary>
    /// <remarks>自分が最後尾にいるなら条件は満たさない。同じレーンにいる場合は条件は満たさない。</remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindIn : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseBehindIn(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 自分が最後尾なら条件は満たさない。
            if (_owner.HorseIndex == _horseAccessor.GetLastHorseIndex())
            {
                return false;
            }
            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var behindOrder = _owner.CurOrder + 1;
            var behindHorse = _horseAccessor.GetHorseInfoByOrder(behindOrder);

            return _owner.GetLaneDistance() > behindHorse.GetLaneDistance() ? 1 : 0;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分の順位１下のキャラが、自分よりアウトにいる。
    /// </summary>
    /// <remarks>自分が最後尾にいるなら条件は満たさない。同じレーンにいる場合は条件は満たさない。</remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHorseBehindOut : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHorseBehindOut(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 自分が最後尾なら条件は満たさない。
            if (_owner.HorseIndex == _horseAccessor.GetLastHorseIndex())
            {
                return false;
            }
            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var behindOrder = _owner.CurOrder + 1;
            var behindHorse = _horseAccessor.GetHorseInfoByOrder(behindOrder);

            return _owner.GetLaneDistance() < behindHorse.GetLaneDistance() ? 1 : 0;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerOtherHorseBehind : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerOtherHorseBehind(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int ret = _raceInfo.NumRaceHorses - (_owner.CurOrder + 1);
            return ret;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlocked : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlocked(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 前・イン・アウト全てをブロックされていたら、「ブロックされている」と見なす。
            return _owner.IsBlockAll() ? 1 : 0;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFront : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlockedFront(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsBlockFront() ? 1 : 0;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedSide : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlockedSide(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsBlockSide() ? 1 : 0;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedAllContinueTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlockedAllContinueTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 前方ブロック。
            var frontContinue = _owner.BlockFrontContinueTime;
            // サイドブロック。
            var sideContinue = _owner.BlockSideContinueTime;

            var retContinue = RaceUtil.GetBlockAllContinueTime(frontContinue, sideContinue);
            // 端数切捨て。
            return (int)retContinue;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFrontContinueTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlockedFrontContinueTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var frontContinue = _owner.BlockFrontContinueTime;
            // 端数切捨て。
            return (int)frontContinue;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedSideContinueTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlockedSideContinueTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var sideContinue = _owner.BlockSideContinueTime;
            // 端数切捨て。
            return (int)sideContinue;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：前方をHp0で失速中のキャラにブロックされた。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFrontHpEmpty : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlockedFrontHpEmpty(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var blockFront = _owner.GetBlockHorseFront();
            if (null == blockFront)
            {
                return 0;
            }
            if (blockFront.GetHp() > 0)
            {
                return 0;
            }
            return 1;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：前方をHp0で失速中のキャラに一定時間ブロックされた。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerBeBlockedFrontHpEmptyContinueTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBeBlockedFrontHpEmptyContinueTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var frontContinue = _owner.BlockFrontHpEmptyContinueTime;
            // 端数切捨て。
            return (int)frontContinue;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：走法キャラ数比較。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCount : SkillTriggerParamBase
    {
        private readonly int _styleCount = 0;

        //---------------------------------------------------------------
        public SkillTriggerRunningStyleCount(SkillTriggerBuildInfo buildInfo, Gallop.RaceDefine.RunningStyle runningStyle) : base(buildInfo)
        {
            _styleCount = _horseAccessor.GetRunningStyleCount(runningStyle);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _styleCount;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分以外の走法キャラ数基底。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCountOtherSelf : SkillTriggerParamBase
    {
        private readonly int _styleCount = 0;

        //---------------------------------------------------------------
        public SkillTriggerRunningStyleCountOtherSelf(SkillTriggerBuildInfo buildInfo, Gallop.RaceDefine.RunningStyle runningStyle) : base(buildInfo)
        {
            _styleCount = _horseAccessor.GetRunningStyleCount(runningStyle);
            if (_owner.RunningStyle == runningStyle)
            {
                --_styleCount;
            }
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _styleCount;
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCountSame : SkillTriggerParamBase
    {
        private readonly int _sameStyleCount = 0;

        //---------------------------------------------------------------
        public SkillTriggerRunningStyleCountSame(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _sameStyleCount = _horseAccessor.GetRunningStyleCount(_owner.RunningStyle);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _sameStyleCount;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分と同じ走法がｎ%。%は1~100で指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleCountSameRate : SkillTriggerParamBase
    {
        private readonly int _sameStyleCount = 0;

        //---------------------------------------------------------------
        public SkillTriggerRunningStyleCountSameRate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 1～100で指定してもらう。
            Debug.Assert(buildInfo.valueRh >= 1 && buildInfo.valueRh <= 100);
            _valueRh = RaceUtil.GetIntThresholdByPer(_raceInfo.NumRaceHorses, buildInfo.valueRh);

            _sameStyleCount = _horseAccessor.GetRunningStyleCount(_owner.RunningStyle);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _sameStyleCount;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleEqualPopularityOne : SkillTriggerParamBase
    {
        private int _isEqual = 0;

        //---------------------------------------------------------------
        public SkillTriggerRunningStyleEqualPopularityOne(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            var horse = _horseAccessor.GetHorseByPopularity(0);
            _isEqual = horse.RunningStyle == buildInfo.ownerHorse.RunningStyle ? 1 : 0;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _isEqual;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularityTopIconNum : SkillTriggerParamBase
    {
        private readonly int _topIconNum = 0;

        //---------------------------------------------------------------
        public SkillTriggerPopularityTopIconNum(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _topIconNum = RaceUtil.CountPopularityTopIcon(
                buildInfo.ownerHorse.PopularityRankLeft,
                buildInfo.ownerHorse.PopularityRankCenter,
                buildInfo.ownerHorse.PopularityRankRight);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _topIconNum;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerPopularityIconNum : SkillTriggerParamBase
    {
        private readonly int _iconNum = 0;

        //---------------------------------------------------------------
        public SkillTriggerPopularityIconNum(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _iconNum = RaceUtil.CountPopularityIcon(
                buildInfo.ownerHorse.PopularityRankLeft,
                buildInfo.ownerHorse.PopularityRankCenter,
                buildInfo.ownerHorse.PopularityRankRight);
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _iconNum;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：Phase指定。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCountPhase : SkillTriggerParamBase
    {
        private readonly List<Gallop.RaceDefine.HorsePhase> _countPhases = null;

        //---------------------------------------------------------------
        public SkillTriggerActivateCountPhase(SkillTriggerBuildInfo buildInfo, Gallop.RaceDefine.HorsePhase phase) : base(buildInfo)
        {
            _countPhases = new List<Gallop.RaceDefine.HorsePhase>() { phase };
        }
        //---------------------------------------------------------------
        public SkillTriggerActivateCountPhase(SkillTriggerBuildInfo buildInfo, List<Gallop.RaceDefine.HorsePhase> phases) : base(buildInfo)
        {
            _countPhases = phases;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int cnt = 0;
            foreach (var phase in _countPhases)
            {
                cnt += _owner.GetActivateSkillCountByPhase(phase);
            }
            return cnt;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：レース後半(50%以降)のスキル発動数
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCountLaterHalf : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerActivateCountLaterHalf(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int cnt = _owner.GetActivateSkillCountByDistance(HorseRaceInfoSimulate.DistanceDivision.SecondHalf);
            return cnt;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：レース開始から今までの回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerActivateCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int cnt = _owner.GetActivateSkillCount();
            return cnt;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：スキル発動回数：レースを通してのチームメンバーのスキル発動回数
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateCountAllTeam : SkillTriggerParamBase
    {
        private readonly IEnumerable<IHorseRaceInfoSimulate> _teamHorses = null;
        //---------------------------------------------------------------
        public SkillTriggerActivateCountAllTeam(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 自チームのメンバーのみが対象
            _teamHorses = _horseAccessor.GetHorseRaceInfos()
                .Where(horse => RaceUtil.IsSameTeam(horse.TeamId, _owner.TeamId));
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int sumTeamMemberActivateSkillCount = 0;
            foreach (var horseRaceInfo in _teamHorses)
            {
                sumTeamMemberActivateSkillCount += horseRaceInfo.GetActivateSkillCount();
            }

            return sumTeamMemberActivateSkillCount;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：何らかのスキルを発動した次のフレーム
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsActivateAnySkill : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerIsActivateAnySkill(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }
        protected override int GetValueLh()
        {
           // 1フレーム前で発動したスキルリストを取得
            var prevActivateSkillDetailList = _owner.SkillManager.GetActivateSkillDetailList(_timeAccessor.CurrentFrame - 1);
            return (prevActivateSkillDetailList != null && prevActivateSkillDetailList.Count > 0) ? 1 : 0;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：回復スキルを発動した次のフレーム
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerIsActivateHealSkill : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerIsActivateHealSkill(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }
        
        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // 1フレーム前で発動したスキルリストを取得
            var prevActivateSkillDetailList = _owner.SkillManager.GetActivateSkillDetailList(_timeAccessor.CurrentFrame - 1);
            if (prevActivateSkillDetailList == null)
            {
                return 0;
            }

            var healSkillCount = prevActivateSkillDetailList.Count(detail => detail.IsTargetSkill(SkillDefine.SkillAbilityType.HpRate));
            return (healSkillCount > 0) ? 1 : 0;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：任意のタググループのスキルを発動した次のフレーム
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateSkillTagGroup : SkillTriggerParamBase
    {
        /// <summary> 発動条件となるスキルのタグIdの配列 </summary>
        private readonly int[] _tagArray = null;
        //---------------------------------------------------------------
        public SkillTriggerActivateSkillTagGroup(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(buildInfo.valueRh >= 0 && _skillParam.SkillActivateTagGroupArray != null && buildInfo.valueRh < _skillParam.SkillActivateTagGroupArray.Length);

            _tagArray = _skillParam.SkillActivateTagGroupArray[buildInfo.valueRh].TagIntArray;
        }

        protected override int GetValueLh()
        {
            return 0;
        }

        protected override bool CheckActivate()
        {
            foreach (var prevActivateSkillDetail in _owner.SkillManager.GetPrevActivateSkillDetailList())
            {
                foreach (var tagId in prevActivateSkillDetail.SkillMasterSimulate.GetTagIds())
                {
                    if (_tagArray.Contains(tagId))
                    {
                        return true;
                    }
                }
            }

            return false;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：他のウマ娘が特定の効果を持つメリットスキルを発動させたとき
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerOtherCharacterActivateAdvantageSkill : SkillTriggerParamBase
    {
        private readonly SkillDefine.SkillAbilityType _targetAbilityType = SkillDefine.SkillAbilityType.None;
        //---------------------------------------------------------------
        public SkillTriggerOtherCharacterActivateAdvantageSkill(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(buildInfo.valueRh >= 0);
            _targetAbilityType = (SkillDefine.SkillAbilityType) buildInfo.valueRh;
        }

        protected override int GetValueLh()
        {
            return 0;
        }

        protected override bool CheckActivate()
        {
            foreach (var prevActivateSkillHorseData in _horseAccessor.GetPrevActivateSkillHorseDict())
            {
                // 自分と同じチームは対象ではない
                if (prevActivateSkillHorseData.Key == _owner ||
                    RaceUtil.IsSameTeam(prevActivateSkillHorseData.Key.TeamId, _owner.TeamId)
                   )
                {
                    continue;
                }

                var prevActivateSkillDetailList = prevActivateSkillHorseData.Value;
                // スキル発動していない
                if (prevActivateSkillDetailList == null || prevActivateSkillDetailList.Count <= 0)
                {
                    continue;
                }

                foreach (var activatedSkillDetail in prevActivateSkillDetailList)
                {
                    // このISkillDetailが持つ全てのAbilityを調べる。
                    foreach (var ability in activatedSkillDetail.Abilities)
                    {
                        // csvで指定されたtypeと異なるAbilityはチェック対象外。
                        if (ability.AbilityType != _targetAbilityType)
                        {
                            continue;
                        }

                        // #95959 1.21.5では一時的にHpRateだけメリット・デメリット判定を「0超過」に変えている。
                        if (ability.AbilityType == SkillDefine.SkillAbilityType.HpRate)
                        {
                            if (ability.AbilityValueOnActivate > 0)
                            {
                                return true;
                            }
                        }
                        // typeが一致した場合、その効果値の符号を見てメリットのあるスキルであればtrue返却。
                        else if (!RaceUtil.IsSkillDemerit(ability.AbilityType, ability.AbilityValueOnActivate))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：このSkillDetailとは別のSkillDetailが発動した時
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerActivateOtherSkillDetail : SkillTriggerParamBase
    {
        /// <summary> 時間差で発動するのでスキル発動条件を満たしたときにこのフラグを上げておく </summary>
        private bool _isActivateEnable = false;
        //---------------------------------------------------------------
        public SkillTriggerActivateOtherSkillDetail(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _isActivateEnable = false;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return 0;
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 一回でも発動可能状態になったらずっと発動可能状態となる
            if (_isActivateEnable)
            {
                return true;
            }

            foreach (var activatedSkillDetail in _owner.SkillManager.GetPrevActivateSkillDetailList())
            {
                if (activatedSkillDetail.SkillId == _ownerSkill.SkillMasterId)
                {
                    _isActivateEnable = true;
                    break;
                }
            }
            return _isActivateEnable;
        }

    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptationCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerTemptationCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.TemptationCount;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より後にいる興奮状態のキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptationBehind : SkillTriggerParamBase
    {
        private readonly bool _isCheckOnlyOpponent;
        
        //---------------------------------------------------------------
        public SkillTriggerTemptationBehind(SkillTriggerBuildInfo buildInfo, bool isCheckOnlyOpponent) : base(buildInfo)
        {
            _isCheckOnlyOpponent = isCheckOnlyOpponent;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int temptationCnt = _horseAccessor
                .GetTemptationHorseList()
                .Count(horse =>
                {
                    // 他チームキャラだけをカウントする指定なら、同じチームのキャラはカウント対象から除外。
                    if (_isCheckOnlyOpponent)
                    {
                        if (RaceUtil.IsSameTeam(_owner.TeamId, horse.TeamId))
                        {
                            return false;
                        }
                    }

                    return horse.CurOrder > _owner.CurOrder;
                });
            return temptationCnt;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：自分より前にいる興奮状態のキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptationInfront : SkillTriggerParamBase
    {
        private readonly bool _isCheckOnlyOpponent;

        //---------------------------------------------------------------
        public SkillTriggerTemptationInfront(SkillTriggerBuildInfo buildInfo, bool isCheckOnlyOpponent) : base(buildInfo)
        {
            _isCheckOnlyOpponent = isCheckOnlyOpponent;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int temptationCnt = _horseAccessor
                .GetTemptationHorseList()
                .Count(horse =>
                {
                    // 他チームキャラだけをカウントする指定なら、同じチームのキャラはカウント対象から除外。
                    if (_isCheckOnlyOpponent)
                    {
                        if (RaceUtil.IsSameTeam(_owner.TeamId, horse.TeamId))
                        {
                            return false;
                        }
                    }

                    return horse.CurOrder < _owner.CurOrder;
                });
            return temptationCnt;
        }
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerTemptation : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerTemptation(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsTemptation ? 1 : 0;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：指定走法で興奮状態のキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRunningStyleTemptationCount : SkillTriggerParamBase
    {
        private readonly Gallop.RaceDefine.RunningStyle _style;
        private readonly bool _isCheckOnlyOpponent;

        //---------------------------------------------------------------
        public SkillTriggerRunningStyleTemptationCount(SkillTriggerBuildInfo buildInfo, Gallop.RaceDefine.RunningStyle style, bool isCheckOnlyOpponent) : base(buildInfo)
        {
            _style = style;
            _isCheckOnlyOpponent = isCheckOnlyOpponent;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            var horseList = _horseAccessor.GetRunningStyleTemptationHorse(_style);
            
            return _isCheckOnlyOpponent
                // 他チームキャラだけをカウントする指定なら、自分及び同じチームのキャラはカウント対象から除外。
                ? horseList.Count(h => h != _owner && !RaceUtil.IsSameTeam(_owner.TeamId, h.TeamId))
                // チームを問わずカウントする場合は自分もカウントに含まれるが問題無い。
                : horseList.Count;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：競り合い（叩き合い）発動回数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerCompeteFightCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCompeteFightCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.CompeteFightCount;
        }
    }
    
    //-------------------------------------------------------------------
    public partial class SkillTriggerGoodStart : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerGoodStart(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsGoodStart ? 1 : 0;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBadStart : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBadStart(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsBadStart ? 1 : 0;
        }
    }

    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseSpeed : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBaseSpeed(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.BaseSpeed;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseStamina : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBaseStamina(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.BaseStamina;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBasePower : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBasePower(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.BasePow;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseGuts : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBaseGuts(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.BaseGuts;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerBaseWiz : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerBaseWiz(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.BaseWiz;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：HP
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHp : SkillTriggerParamBaseFloat
    {
        //---------------------------------------------------------------
        public SkillTriggerHp(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // Hp初期化前の呼び出しは絶対に条件を満たさないため、baseの呼び出しよりも前に判定。
            if (!_owner.IsParamInitialized)
            {
                return false;
            }

            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override float GetValueLh()
        {
            Debug.Assert(_owner.IsParamInitialized);
            return _owner.GetHp();
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：Hpの比率を百分率で判定。
    /// </summary>
    /// <seealso cref="Test.TestSkillTriggerHpPer"/>
    //-------------------------------------------------------------------
    public partial class SkillTriggerHpPer : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHpPer(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // Hp初期化前の呼び出しは絶対に条件を満たさないため、baseの呼び出しよりも前に判定。
            if (!_owner.IsParamInitialized)
            {
                return false;
            }

            return base.CheckActivate();
        }
        
        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            Debug.Assert(_owner.IsParamInitialized);
            // [0.0f .. 1.0f]を百分率に変換。
            int per = (int)(_owner.GetHpPer() * 100);
            Debug.Assert(per >= 0 && per <= 100);
            return per;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerHpEmpty : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerHpEmpty(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // Hp初期化前の呼び出しは絶対に条件を満たさないため、baseの呼び出しよりも前に判定。
            if (!_owner.IsParamInitialized)
            {
                return false;
            }

            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            Debug.Assert(_owner.IsParamInitialized);
            float hp = _owner.GetHp();
            return hp <= 0.0f ? 1 : 0;
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerHpEmptyOneTime : SkillTriggerBase
    {
        private float _prevHp = 0;
        private bool _isInitialized = false;

        //---------------------------------------------------------------
        public SkillTriggerHpEmptyOneTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            if (!_CheckInitialize())
            {
                return false;
            }

            if (_prevHp <= 0.0f)
            {
                return false;
            }
            _prevHp = _owner.GetHp();
            return _prevHp <= 0.0f;
        }

        //---------------------------------------------------------------
        private bool _CheckInitialize()
        {
            if (_isInitialized)
            {
                return true;
            }

            // スキル所持者のHP初期化前(HP0の状態)に呼ばれることがあるので、その場合はこの条件も初期化しない。
            // ※つまりパラメータ初期化前は、この条件は絶対に満たされない。
            if (!_owner.IsParamInitialized)
            {
                return false;
            }

            _prevHp = _owner.GetHp();
            _isInitialized = true;
            return true;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Param; }
        }
    }


    //-------------------------------------------------------------------
    public partial class SkillTriggerCurrentSpeed : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerCurrentSpeed(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return (int)_owner.GetLastSpeed();
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：出走者の中での「やる気、1200以上の圧縮、スキル効果の効果を含めたスピードの値」の順位
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSpeedOrder : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerSpeedOrder(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            int speedOrder = 1;  //自身の速度の順位
            for (int i = 0; i < _horseAccessor.GetHorseRaceInfos().Length; i++)
            {
                if (_horseAccessor.GetHorseRaceInfos()[i].Speed > _owner.Speed)
                {
                    speedOrder++;
                }
            }
            return speedOrder;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：囲まれているか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSurrounded : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerSurrounded(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.IsSurrounded ? 1 : 0;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラ数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearHorseCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerNearHorseCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.NearHorseCount;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラが一定数(4)以上継続して存在する継続時間。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearAroundAccumulateTime : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerNearAroundAccumulateTime(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return RaceUtilMath.FloorToInt(_owner.NearHorseAroundAccumulateTime);
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラ数で自分より前にいる数。※同じDistanceの場合はカウントに含まない。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearFrontHorseCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerNearFrontHorseCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            if (_owner.NearHorseCount == 0)
            {
                return 0;
            }

            int infrontCnt = 0;
            for (int i = 0; i < _owner.NearHorseCount; ++i)
            {
                var horse = _owner.NearHorses[i];
                if (horse.distanceGap > 0)
                {
                    ++infrontCnt;
                }
            }

            return infrontCnt;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：近くのキャラ数で自分より後ろにいる数。※同じDistanceの場合はカウントに含まない。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerNearBehindHorseCount : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerNearBehindHorseCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            if (_owner.NearHorseCount == 0)
            {
                return 0;
            }

            int behindCnt = 0;
            for (int i = 0; i < _owner.NearHorseCount; ++i)
            {
                var horse = _owner.NearHorses[i];
                if (horse.distanceGap < 0)
                {
                    ++behindCnt;
                }
            }

            return behindCnt;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：視野範囲内に馬の数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerVisibleHorse : SkillTriggerParamBase
    {
        //---------------------------------------------------------------
        public SkillTriggerVisibleHorse(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _owner.VisibleHorseCount;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：同じスキルを持っているキャラの数。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerSameSkillHorseCount : SkillTriggerParamBase
    {
        private bool _isInitialized = false;
        private readonly IHorseRaceInfoSimulate[] _allHorses = null;
        private int _sameSkillHorseCount = 0;

        //---------------------------------------------------------------
        public SkillTriggerSameSkillHorseCount(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _allHorses = _horseAccessor.GetHorseRaceInfos();
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            if (!_CheckInitialize())
            {
                return false;
            }

            return base.CheckActivate();
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return _sameSkillHorseCount;
        }

        //---------------------------------------------------------------
        private bool _CheckInitialize()
        {
            if (_isInitialized)
            {
                return true;
            }

            int ownerSkillId = _ownerSkill.SkillMasterId;
            foreach (var horse in _allHorses)
            {
                // スキル初期化前のキャラが１体でもいたらこの条件は初期化不可。
                if (!horse.IsSkillInitialized)
                {
                    return false;
                }

                // 同じスキルを持っているキャラを数える。
                var skill = horse.SkillManager.GetSkill(ownerSkillId);
                if (null == skill)
                {
                    continue;
                }
                ++_sameSkillHorseCount;
            }

            _isInitialized = true;
            return true;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：特定のスキルIDのスキルを持つキャラが存在しているかどうか
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerExistSkillId : SkillTriggerParamBase
    {
        private bool _isExist = false;

        private bool _isInitialized = false;

        //---------------------------------------------------------------
        public SkillTriggerExistSkillId(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            // 等式系のみを許可
            Debug.Assert(
                buildInfo.op == SkillDefine.SkillTriggerOperator.Equal ||
                buildInfo.op == SkillDefine.SkillTriggerOperator.NotEqual
            );
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            // このメソッドは使用しないがオーバーライドしなければいけないので適当に0を返す
            return 0;
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 本当はコンストラクタ内で計算したいがコンストラクタ呼び出し時点ではSKillManager._skillArray等にアクセスするとエラーになるので
            // 条件発動チェック時に計算するようにする(既に計算済みの場合はその結果を返す)
            Initialize();

            // SkillManagerの初期化が完了していない場合は条件を満たしていないということにする
            if (!_isInitialized)
            {
                return false;
            }

            if (_op == SkillDefine.SkillTriggerOperator.Equal)
            {
                // ==ならskillIdが存在することで条件を満たす
                return _isExist;
            }
            else if (_op == SkillDefine.SkillTriggerOperator.NotEqual)
            {
                // !=ならskillIdが存在しないことで条件を満たす
                return !_isExist;
            }

            return false;
        }

        //---------------------------------------------------------------
        private void Initialize()
        {
            // 既に計算済みならキャッシュがあるので何もしない
            if (_isInitialized)
            {
                return;
            }

            // 全キャラのスキル所持状態を確認
            var allHorses = _horseAccessor.GetHorseRaceInfos();
            int targetSkillId = _valueRh;
            foreach (var horse in allHorses)
            {
                if (!horse.IsSkillInitialized)
                {
                    // 全員の準備が完了するまでは_isInitializedのフラグを上げない
                    return;
                }

                if (horse.GetSkill(targetSkillId) != null)
                {
                    _isExist = true;
                    break;
                }
            }

            _isInitialized = true;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル発動条件：特定スキルを発動しているか。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class SkillTriggerUsedParticularSkillId : SkillTriggerBase
    {
        private readonly int _skillId = 0;

        //---------------------------------------------------------------
        public SkillTriggerUsedParticularSkillId(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _skillId = buildInfo.valueRh;
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            bool isInvoked = _owner.GetUsedSkillIdList().Contains(_skillId);
            return isInvoked;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category => SkillDefine.SkillTriggerCategory.Param;
    }

    #endregion <馬系>


    #region <その他>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRandomLot : SkillTriggerParamBase
    {
        private bool _isLotOK = false;

        //---------------------------------------------------------------
        public SkillTriggerRandomLot(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            Debug.Assert(buildInfo.valueRh >= 0 && buildInfo.valueRh <= 100);
            int rand = _randomGenerator.GetRandom(100);
            _isLotOK = (rand < buildInfo.valueRh);
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            return _isLotOK;
        }

        //---------------------------------------------------------------
        protected override int GetValueLh()
        {
            return -1;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// 他の条件を満たしているときに一定間隔で抽選する。
    /// </summary>
    /// <remarks>
    /// 「他の条件」と見なされるのは、例えば以下のデータの場合、@で区切られたグループごととなる（→ground_type==1&grade<1の部分）。
    /// "ground_type==1&grade<1&random_lot_other_activate@ground_type==2&grade<1"
    /// </remarks>
    //-------------------------------------------------------------------
    public partial class SkillTriggerRandomLotOtherActivate : SkillTriggerBase
    {
        private bool _isLotOK = false;
        private float _lotInterval = 0;
        private readonly float _lotIntervalMax = 0;

        //---------------------------------------------------------------
        public SkillTriggerRandomLotOtherActivate(SkillTriggerBuildInfo buildInfo) : base(buildInfo)
        {
            _lotIntervalMax = _skillParam.RandomLotOtherActivateLotInterval;
            Debug.Assert(_lotIntervalMax > 0, "SkillTriggerRandomLotOtherActivate 抽選間隔が0秒以下です。:" + _lotIntervalMax);
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            // 抽選間隔の更新。
            if (_lotInterval > 0.0f)
            {
                _lotInterval -= deltaTime;
            }
        }

        //---------------------------------------------------------------
        protected override bool CheckActivate()
        {
            // 抽選成功した場合、他の条件を満たしている間は、trueを返し続ける。
            if (_isLotOK)
            {
                if (_IsOtherActivate())
                {
                    return true;
                }
            }

            //-----------------------------------------------------------
            // ※抽選に失敗しているか、他の発動条件を満たしていない場合は以下の処理。
            //-----------------------------------------------------------

            // 抽選OKフラグのクリア。
            _isLotOK = false;

            // 抽選間隔のチェック。
            if (_lotInterval > 0.0f)
            {
                return false;
            }

            // 他の条件を満たしているかチェック。
            if (!_IsOtherActivate())
            {
                return false;
            }

            // 抽選。
            float rand = _randomGenerator.GetRandom(100.0f);
            float per = RaceUtil.CalcActivatePer(_owner.Wiz, _skillParam);
            _isLotOK = (rand < per);
            // 次回抽選間隔の設定。
            _lotInterval = _lotIntervalMax;

            return _isLotOK;
        }

        private bool _IsOtherActivate()
        {
            foreach (var trigger in _selfGroup)
            {
                if (trigger == this)
                {
                    continue;
                }
                if (trigger.IsActivated)
                {
                    continue;
                }
                // この条件は満たしていない。
                return false;
            }
            // 自分以外の条件を満たしている。
            return true;
        }

        //---------------------------------------------------------------
        protected override SkillDefine.SkillTriggerCategory Category
        {
            get { return SkillDefine.SkillTriggerCategory.Param; }
        }
    }
    #endregion <その他>
#endif
}

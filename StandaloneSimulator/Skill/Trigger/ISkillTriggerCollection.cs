#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;

namespace StandaloneSimulator
{
    public interface ISkillTriggerCollection
    {
        //---------------------------------------------------------------
        bool IsActivatedAny();

        //---------------------------------------------------------------
        void Update( float deltaTime );

        //---------------------------------------------------------------
        void AddTriggerGroup( List< ISkillTrigger > group );
        
        //---------------------------------------------------------------
        /// <summary> スキル効果のクールダウンタイムを使用するスキル発動条件を持つかどうか </summary>
        /// <remarks>
        /// これがTrueになるとスキル効果のクールダウンタイムを元にスキル効果が発動されるようになります
        /// </remarks>
        //---------------------------------------------------------------
        bool IsContainsUseSkillDetailCoolDownTimeTrigger();

        //---------------------------------------------------------------
        /// <summary> スキル効果が発動してもスキル発動して扱わないスキル発動条件を持つかどうか </summary>
        /// <remarks>
        /// これがTrueになるとスキル効果が発動してもスキル発動として扱われなくなります。(チーム競技場のスコア加算や他のスキル発動条件に引っかかりません)
        /// </remarks>
        //---------------------------------------------------------------
        bool IsContainsNotActivateSkillTrigger();
    }
}

#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
using  System;
using System.Linq;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラのスキルによる効果値変動オブジェクト
    /// </summary>
    //-------------------------------------------------------------------
#if CYG_DEBUG && UNITY_EDITOR
    // デバッグデータ周りでディープコピーしたいのでデバッグ環境下でのみSerializable属性付与
    [Serializable]
#endif
    public class SkillParamFluctuate
    {
        /// <summary>
        /// デバフスキル効果値に乗算させる時に除外するSkillModifierParamの配列
        /// </summary>
        private static readonly Gallop.SkillDefine.SkillModifierParam[] DEBUFF_ABILITY_VALUE_MULTIPLY_EXCLUDE_SKILL_MODIFIER_PARAM_ARRAY =
        {
            Gallop.SkillDefine.SkillModifierParam.StartDelayFix,
            Gallop.SkillDefine.SkillModifierParam.StartDelayScale,
            Gallop.SkillDefine.SkillModifierParam.DebuffAbilityValueMultiply,
            Gallop.SkillDefine.SkillModifierParam.DebuffAbilityValueMultiplyOtherActivate,
            Gallop.SkillDefine.SkillModifierParam.HpRateDemerit,
        };
        /// <summary> スキル効果39でデバフに対して乗算する割合 </summary>
        /// <remarks> 自他関わらずデバフスキルのみに反映 </remarks>
        private float _debuffAbilityValueMultiplyRatio = 1f;

        /// <summary> スキル効果40でデバフに対して乗算する割合 </summary>
        /// <remarks> 他者が使用したデバフスキルのみに反映 </remarks>
        private float _debuffAbilityValueMultiplyOtherActivateRatio = 1f;

    #if CYG_DEBUG
        public float DbgDebuffAbilityValueMultiplyRatio => _debuffAbilityValueMultiplyRatio;
        public float DbgDebuffAbilityValueMultiplyOtherActivateRatio => _debuffAbilityValueMultiplyOtherActivateRatio;
    #endif
        /// <summary>
        /// スキル効果値変動値更新
        /// </summary>
        public void UpdateFluctuateValues(float debuffAbilityValueMultiplyRatio, float debuffAbilityValueMultiplyOtherActivateRatio)
        {
            _debuffAbilityValueMultiplyRatio = debuffAbilityValueMultiplyRatio;
            _debuffAbilityValueMultiplyOtherActivateRatio = debuffAbilityValueMultiplyOtherActivateRatio;
        }

        /// <summary>
        /// valueに効果値変動を反映して返す
        /// </summary>
        /// <param name="value"></param>
        /// <param name="skillModifierParam"></param>
        /// <param name="isDebuff"></param>
        /// <param name="isActivateOthers"></param>
        /// <returns></returns>
        public float ApplyValue(float value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isDebuff,
            bool isActivateOthers)
        {
            // デバフ効果なら、効果値変動をチェックする
            if (isDebuff)
            {
                value = ApplyDebuffValue(value, skillModifierParam, isActivateOthers);
            }

            return value;
        }

        /// <summary>
        /// Debuffのスキル効果値に変動を反映して返す
        /// </summary>
        /// <param name="value"></param>
        /// <param name="skillModifierParam"></param>
        /// <param name="isActivateOthers"></param>
        /// <returns></returns>
        private float ApplyDebuffValue(float value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers)
        {
            // 値によってはメリット効果になりかねないため除外する仕様
            if (DEBUFF_ABILITY_VALUE_MULTIPLY_EXCLUDE_SKILL_MODIFIER_PARAM_ARRAY.Contains(skillModifierParam))
            {
                return value;
            }

            // スキル効果39
            value *= _debuffAbilityValueMultiplyRatio;
            // スキル効果40(他者が発動したものに対してのみ反映)
            if (isActivateOthers)
            {
                value *= _debuffAbilityValueMultiplyOtherActivateRatio;
            }

            return value;
        }
    }
}
#endif
#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ISkillAbilityValueCalculator
    {
        float AbilityValue { get; }
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：ctorでAbilityValue固定：int：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyBaseStaticInt : ISkillAbilityValueCalculator
    {
        protected readonly IHorseRaceInfoSimulate _owner = null;
        protected readonly float _abilityValueBase = 0;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBaseStaticInt( IHorseRaceInfoSimulate owner, float abilityValueBase )
        {
            _owner = owner;
            _abilityValueBase = abilityValueBase;
            AbilityValue = _abilityValueBase * GetMultiply(); 
        }

        //---------------------------------------------------------------
        public float AbilityValue { get; private set; }

        //---------------------------------------------------------------
        protected abstract int GetMultiply();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：ctorでAbilityValue固定：float：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyBaseStaticFloat : ISkillAbilityValueCalculator
    {
        protected readonly IHorseRaceInfoSimulate _owner = null;
        protected readonly float _abilityValueBase = 0;
        protected readonly Gallop.RaceParamDefine.SkillParam _skillParam;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBaseStaticFloat(IHorseRaceInfoSimulate owner, float abilityValueBase, Gallop.RaceParamDefine.SkillParam skillParam)
        {
            _owner = owner;
            _abilityValueBase = abilityValueBase;
            _skillParam = skillParam;
            AbilityValue = _abilityValueBase * GetMultiply(); 
        }

        //---------------------------------------------------------------
        public float AbilityValue { get; private set; }

        //---------------------------------------------------------------
        protected abstract float GetMultiply();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：AbilityValue取得毎に値計算：float：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyBaseDynamicFloat : ISkillAbilityValueCalculator
    {
        protected readonly IHorseRaceInfoSimulate _owner = null;
        protected readonly float _abilityValueBase = 0;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBaseDynamicFloat( IHorseRaceInfoSimulate owner, float abilityValueBase )
        {
            _owner = owner;
            _abilityValueBase = abilityValueBase;
        }

        //---------------------------------------------------------------
        public float AbilityValue 
        { 
            get
            {
                return _abilityValueBase * GetMultiply();
            }
        }

        //---------------------------------------------------------------
        protected abstract float GetMultiply();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：AbilityValue取得毎に値計算：float：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueAddBaseDynamicFloat : ISkillAbilityValueCalculator
    {
        protected readonly IHorseRaceInfoSimulate _owner = null;
        /// <summary> レベル補正済みの効果値 </summary>
        private readonly float _abilityValueBase = 0;
        /// <summary> レベル補正を掛けるメソッド </summary>
        private readonly Func<float, float> _applyValueLevelUsage;
        //---------------------------------------------------------------
        protected SkillAbilityValueAddBaseDynamicFloat(IHorseRaceInfoSimulate owner, float abilityValueBase, Func<float, float> applyValueLevelUsage)
        {
            _owner = owner;
            _abilityValueBase = abilityValueBase;
            _applyValueLevelUsage = applyValueLevelUsage;
        }

        //---------------------------------------------------------------
        public float AbilityValue
        {
            get
            {
                // (効果値 + 加算値) * レベル補正という仕様で_abilityValueBaseに関しては既にレベル補正がかかって渡ってくるため
                // 加算値に別途レベル補正を掛けて足している
                return _abilityValueBase + _applyValueLevelUsage(GetAdditionalValue());
            }
        }

        //---------------------------------------------------------------
        protected abstract float GetAdditionalValue();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：直値。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueDirect : ISkillAbilityValueCalculator
    {
        //---------------------------------------------------------------
        public SkillAbilityValueDirect( float abilityValueBase )
        {
            AbilityValue = abilityValueBase;
        }

        //---------------------------------------------------------------
        public float AbilityValue { get; private set; }
    };


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：スキル所持数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplySkillNum : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary>最大倍率。</summary>
        private const float MULTIPLY_MAX = 1.2f;
        /// <summary>スキル１個あたりの増加倍率。</summary>
        private const float COEF_PER_SKILL = 0.01f;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplySkillNum(IHorseRaceInfoSimulate owner, float abilityValueBase) : base(owner, abilityValueBase)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            // このスキル自体は数から除くために-1。あり得ないはずだが一応0下回らないようにしておく。
            int skillNum = Math.Max(_owner.GetSkills().Length - 1, 0);
            // 所持スキル１個につき倍率加算。
            float multiply = Math.Min(1.0f + (skillNum * COEF_PER_SKILL), MULTIPLY_MAX);
            return multiply;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：回復スキルを使用した回数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyHealSkillActivateCount : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary>最大倍率。</summary>
        private const float MULTIPLY_MAX = 1.5f;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyHealSkillActivateCount(IHorseRaceInfoSimulate owner, float abilityValueBase) : base(owner, abilityValueBase)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int healSkillCount = _owner.GetActivateHealSkillCount();
            // 発動１回につきx0.1倍。
            float multiply = Math.Min(1.0f + (healSkillCount * 0.1f), MULTIPLY_MAX);
            return multiply;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：特定の評価タグを持つスキルを使用した回数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyActivateSpecificTagSkillCount : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly Gallop.RaceParamDefine.SkillParam _paramDefine;

        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyActivateSpecificTagSkillCount(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            Gallop.RaceParamDefine.SkillParam param)
            : base(owner, abilityValueBase)
        {
            _paramDefine = param;
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int activateSpecificTagGroupSkillCount = _owner.GetActivateSpecificTagGroupSkillCount();
            foreach (var coef in _paramDefine.ActivateSpecificTagGroupSkillCountCoefAscArray)
            {
                if (activateSpecificTagGroupSkillCount < coef.ActivateSpecificTagGroupSkillCountThreshold)
                {
                    return coef.Multiply; 
                }
            }

            Debug.LogError(
                $"特定の評価タグを持つスキル発動数({activateSpecificTagGroupSkillCount})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない"
            );
            return 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：特定のSkillAbilityTypeを持つスキルを使用した回数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyActivateSpecificSkillAbilityTypeCount : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly Gallop.RaceParamDefine.SkillParam _paramDefine;

        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyActivateSpecificSkillAbilityTypeCount(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            Gallop.RaceParamDefine.SkillParam param)
            : base(owner, abilityValueBase)
        {
            _paramDefine = param;
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int activateSpecificSkillAbilityTypeGroupSkillCount = _owner.GetActivateSpecificSkillAbilityTypeGroupSkillCount();
            foreach (var coef in _paramDefine.ActivateSpecificSkillAbilityTypeGroupSkillCountCoefAscArray)
            {
                if (activateSpecificSkillAbilityTypeGroupSkillCount < coef.ActivateSpecificSkillAbilityTypeGroupSkillCountThreshold)
                {
                    return coef.Multiply; 
                }
            }

            Debug.LogError(
                $"特定のSkillAbilityTypeを持つスキルの発動数({activateSpecificSkillAbilityTypeGroupSkillCount})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない"
            );
            return 1;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【最終コーナー通過時の順位に応じた倍率】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyFinalCornerEndOrder : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary>倍率の最大最小。</summary>
        private const float MULTIPLY_MAX = 1.2f;
        private const float MULTIPLY_MIN = 1.0f;
        /// <summary>順位が１下がるごとに倍率から引かれる値。</summary>
        private const float MULTIPLY_DOWN_RATE = 0.02f;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyFinalCornerEndOrder(IHorseRaceInfoSimulate owner, float abilityValueBase) : base(owner, abilityValueBase)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            // 最終コーナー終了時の順位が初期化される前なら倍率は1。
            if (!_owner.IsFinalCornerEndOrderInitialized)
            {
                return 1;
            }
            
            // 最終コーナー終了時の順位。0~の値だが計算上このまま使える。
            int finalCornerEndOrder = _owner.FinalCornerEndOrder;
            // １位通過（finalCornerEndOrder=0）でMULTIPLY_MAX倍。順位が１下がるごとにORDER_RATE%ずつ倍率が下がる。
            float multiply = MULTIPLY_MAX - (MULTIPLY_DOWN_RATE * finalCornerEndOrder);
            multiply = Math.Max(multiply, MULTIPLY_MIN);
            return multiply;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【チームメンバー数の逆数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyInvTeamMemberCount : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly IRaceHorseAccessor _horseAccessor;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyInvTeamMemberCount(IHorseRaceInfoSimulate owner, float abilityValueBase, IRaceHorseAccessor horseAccessor) : base(owner, abilityValueBase)
        {
            _horseAccessor = horseAccessor;
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int teamId = _owner.TeamId;
            
            // どこのチームにも属していないならx1。
            if (teamId == Gallop.RaceDefine.TEAM_ID_NULL)
            {
                return 1;
            }

            var teamHorseArray = _horseAccessor.GetTeamMemberHorseInfoArray(teamId);
            int teamMemberNum = teamHorseArray.Length;
            if (teamMemberNum <= 0)
            {
                Debug.LogError($"チームメンバー数が不正です. teamMemberNum={teamMemberNum}");
                return 1;
            }
            
            float multiply = 1.0f / teamMemberNum;
            return multiply;
        }
    }
    

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値にParamDefine登録済みの値からランダムに選んだ値を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyRandom : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly Gallop.RaceParamDefine.SkillParam.AbiiltyValueUsageRandom[] _randomArray;
        private readonly IRaceRandomGenerator _randomGenerator;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyRandom(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor, 
            IRaceRandomGenerator randomGenerator, 
            Gallop.RaceParamDefine.SkillParam.AbiiltyValueUsageRandom[] randomArray) : base(owner, abilityValueBase)
        {
            _horseAccessor = horseAccessor;
            _randomGenerator = randomGenerator;
            _randomArray = randomArray;
            Debug.Assert(randomArray.All(x => x.AbilityValueCoef >= 0), "AbiiltyValueUsageRandom[]のAbilityValueCoefに負の値が入力されている");
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            float multiply = LotValue(_randomArray);
            return multiply;
        }

        /// <summary>
        /// randomArrayに登録されている効果値から実際に使う値を抽選。
        /// </summary>
        private float LotValue(Gallop.RaceParamDefine.SkillParam.AbiiltyValueUsageRandom[] randomArray)
        {
            int perTotal = randomArray.Sum(x => x.Per);
            int rand = _randomGenerator.GetRandom(perTotal);

            for (int i = 0; i < randomArray.Length; ++i)
            {
                var random = randomArray[i];
                
                rand -= random.Per;

                if (rand <= 0)
                {
                    return random.AbilityValueCoef;
                }
            }
            
            return 1;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【チームメンバーの「基礎ステータス：***」に応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyTeamTotalStatusBase : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly Gallop.RaceParamDefine.SkillParam _paramDefine;
        private readonly IHorseRaceInfoSimulate[] _selfOnlyTeam;
        
        //---------------------------------------------------------------
        protected SkillAbilityValueMultiplyTeamTotalStatusBase(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase)
        {
            _horseAccessor = horseAccessor;
            _paramDefine = paramDefine;
            _selfOnlyTeam = new [] { _owner };
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            var statusTargetHorseArray = GetStatusTargetHorse();

            float statusTotal = 0;
            foreach (var teamHorse in statusTargetHorseArray)
            {
                statusTotal += GetStatus(teamHorse);
            }

            float multiply = GetMultiply(statusTotal);
            return multiply;
        }

        //---------------------------------------------------------------
        private IHorseRaceInfoSimulate[] GetStatusTargetHorse()
        {
            int teamId = _owner.TeamId;
            
            // どこのチームにも属していないなら自分だけ。
            if (teamId == Gallop.RaceDefine.TEAM_ID_NULL)
            {
                return _selfOnlyTeam;
            }

            // 同じチームのメンバー取得。0人以下にはならないはず。
            var teamHorseArray = _horseAccessor.GetTeamMemberHorseInfoArray(teamId);
            if (teamHorseArray.Length <= 0)
            {
                Debug.LogError($"チームメンバー数が不正です. teamMemberNum={teamHorseArray.Length}");
            }

            return teamHorseArray;
        }

        //---------------------------------------------------------------
        private float GetMultiply(float statusTotal)
        {
            foreach (var coef in _paramDefine.TeamTotalStatusCoefAscArray)
            {
                if (statusTotal < coef.StatusThreshold)
                {
                    return coef.Multiply; 
                }
            }
            
            Debug.LogWarning($"チームのステータス合計値({statusTotal})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない");
            return 1;
        }
        
        //---------------------------------------------------------------
        protected abstract float GetStatus(IHorseRaceInfoSimulate horse);
    }

    public class SkillAbilityValueMultiplyTeamTotalStatusSpeed : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusSpeed(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfoSimulate horse)
        {
            return horse.BaseSpeed;
        }
    }

    public class SkillAbilityValueMultiplyTeamTotalStatusStamina : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusStamina(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfoSimulate horse)
        {
            return horse.BaseStamina;
        }
    }
    
    public class SkillAbilityValueMultiplyTeamTotalStatusPower : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusPower(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfoSimulate horse)
        {
            return horse.BasePow;
        }
    }
    
    public class SkillAbilityValueMultiplyTeamTotalStatusGuts : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusGuts(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfoSimulate horse)
        {
            return horse.BaseGuts;
        }
    }

    public class SkillAbilityValueMultiplyTeamTotalStatusWiz : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusWiz(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfoSimulate horse)
        {
            return horse.BaseWiz;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【育成レースの勝利数に応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplySingleModeWinCount : SkillAbilityValueMultiplyBaseStaticFloat
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplySingleModeWinCount(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, paramDefine)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int winCount = _owner.SingleModeWinCount;
            
            foreach (var coef in _skillParam.RaceWinCountCoefAscArray)
            {
                if (winCount < coef.WinCountThreshold)
                {
                    return coef.Multiply; 
                }
            }
            
            return 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【ファン数に応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyFanCount : SkillAbilityValueMultiplyBaseStaticFloat
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyFanCount(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, paramDefine)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            if (_skillParam.FanCountCoefAscArray == null)
            {
                return 1;
            }

            int fanCount = _owner.FanCount;

            foreach (var coef in _skillParam.FanCountCoefAscArray)
            {
                if (fanCount < coef.FanCountThreshold)
                {
                    return coef.Multiply;
                }
            }

            Debug.LogWarning($"ファン数({fanCount})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない");
            return 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【終盤以降のコーナーで追い抜いた数に応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyOrderUpCountCornerPhaseEndAfter : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly Gallop.RaceParamDefine.SkillParam _skillParam;

        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyOrderUpCountCornerPhaseEndAfter(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase)
        {
            _skillParam = paramDefine;
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int orderUpCount = _owner.CurOrderUpCountCornerPhaseEndAfter;
            
            foreach (var coef in _skillParam.OrderUpCornerPhaseEndAfterCoefAscArray)
            {
                if (orderUpCount < coef.OrderChangeCountThreshold)
                {
                    return coef.Multiply; 
                }
            }
            
            return 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【自分の「基礎ステータス：***」に応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyStatusBase : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        protected readonly Gallop.RaceParamDefine.SkillParam _paramDefine;
        
        //---------------------------------------------------------------
        protected SkillAbilityValueMultiplyStatusBase(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase)
        {
            _paramDefine = paramDefine;
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            float multiply = GetMultiply(BaseStatus);
            return multiply;
        }

        //---------------------------------------------------------------
        private float GetMultiply(float baseStatus)
        {
            if (BaseStatusCoefArray == null)
            {
                Debug.LogWarning($"基礎ステータス({baseStatus})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない");
                return 1f;
            }

            foreach (var coef in BaseStatusCoefArray)
            {
                if (baseStatus < coef.BaseStatusThreshold)
                {
                    return coef.Multiply; 
                }
            }
            
            Debug.LogWarning($"基礎ステータス({baseStatus})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない");
            return 1;
        }

        protected abstract Gallop.RaceParamDefine.SkillParam.BaseStatusCoef[] BaseStatusCoefArray { get; }
        protected abstract float BaseStatus { get; }
    }
    
    public class SkillAbilityValueMultiplyBaseWiz : SkillAbilityValueMultiplyStatusBase
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBaseWiz(
            IHorseRaceInfoSimulate owner, 
            float abilityValueBase, 
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, paramDefine)
        {
        }

        //---------------------------------------------------------------
        protected override Gallop.RaceParamDefine.SkillParam.BaseStatusCoef[] BaseStatusCoefArray => _paramDefine.BaseWizCoefAscArray;
        protected override float BaseStatus => _owner.BaseWiz;
    }
    
    /// <summary>
    /// スキル効果値計算機：効果値に【基礎ステータスの中で最大の素パラメータに応じた係数】を乗算
    /// </summary>
    public class SkillAbilityValueMultiplyMaximumRawStatus : SkillAbilityValueMultiplyStatusBase
    {
        /// <summary> 基礎ステータスの中で最大の素パラメータを保持 </summary>
        private readonly int _maximumRawStatus = 0;

        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyMaximumRawStatus(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, paramDefine)
        {
            var rawStatusList = new int[]
            {
                owner.RawSpeed,
                owner.RawGuts,
                owner.RawPow,
                owner.RawStamina,
                owner.RawWiz
            };
            _maximumRawStatus = rawStatusList.Max();
        }

        //---------------------------------------------------------------
        protected override Gallop.RaceParamDefine.SkillParam.BaseStatusCoef[] BaseStatusCoefArray => _paramDefine.MaximumRawStatusCoefAscArray;

        protected override float BaseStatus => _maximumRawStatus;
    }
    
    //---------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【先頭との距離に応じた係数】を加算
    /// </summary>
    public class SkillAbilityValueAddDistanceDiffTop : SkillAbilityValueAddBaseDynamicFloat
    {
        private readonly Gallop.RaceParamDefine.SkillParam _paramDefine;
        private readonly IRaceHorseAccessor _horseAccessor;

        //---------------------------------------------------------------
        public SkillAbilityValueAddDistanceDiffTop(
            IHorseRaceInfoSimulate owner, float abilityValueBase, IRaceHorseAccessor horseAccessor, Gallop.RaceParamDefine.SkillParam paramDefine, Func<float, float> applyValueLevelUsagef
        ) : base(owner, abilityValueBase, applyValueLevelUsagef)
        {
            _horseAccessor = horseAccessor;
            _paramDefine = paramDefine;
        }

        //---------------------------------------------------------------
        protected override float GetAdditionalValue()
        {
            var topHorse = _horseAccessor.GetFirstHorseInfo();
            var distanceDiff = topHorse.GetDistance() - _owner.GetDistance();
            Debug.Assert(distanceDiff >= 0.0f);
            if (_paramDefine.DistanceDiffTopCoefAscArray == null)
            {
                Debug.LogWarning("先頭との距離に応じた係数加算のパラメータがRaceParamDefineに定義されていない");
                return 0f;
            }

            foreach (var coef in _paramDefine.DistanceDiffTopCoefAscArray)
            {
                if (distanceDiff < coef.DistanceDiffTopThreshold)
                {
                    // CSVと同じく10000倍した数値が入力されているので値変換する
                    return RaceUtilMath.MasterInt2Float(coef.AddValue);
                }
            }

            Debug.LogWarning($"先頭との距離{distanceDiff}に応じた係数加算がRaceParamDefineに定義されていない");
            return 0f;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【各フェーズごとのサイドをブロックされていた最大時間に応じた係数】を乗算
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyBlockedSideMaxContinueTime : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary> 判定対象のフェーズ </summary>
        private readonly Gallop.RaceDefine.HorsePhase _phase;
        /// <summary> 対象の係数テーブル </summary>
        private readonly Gallop.RaceParamDefine.SkillParam.BlockedSideMaxContinueTimeCoef[] _targetCoefAscArray = null;
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBlockedSideMaxContinueTime(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine,
            Gallop.SkillDefine.SkillAbilityValueUsage abilityValueUsage
        ) : base(owner, abilityValueBase)
        {
            switch (abilityValueUsage)
            {
                case Gallop.SkillDefine.SkillAbilityValueUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1:
                    _phase = Gallop.RaceDefine.HorsePhase.MiddleRun;
                    _targetCoefAscArray = paramDefine.BlockedSideMaxContinueTimePhaseMiddleRunAbilityValueCoefAscArray1;
                    break;
                case Gallop.SkillDefine.SkillAbilityValueUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2:
                    _phase = Gallop.RaceDefine.HorsePhase.MiddleRun;
                    _targetCoefAscArray = paramDefine.BlockedSideMaxContinueTimePhaseMiddleRunAbilityValueCoefAscArray2;
                    break;
                default:
                    Debug.LogError($"予期せぬ効果値タイプです。{abilityValueUsage}");
                    break;
            }
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            if (_targetCoefAscArray == null)
            {
                return 1;
            }

            if (!_owner.BlockSideMaxContinueTimeByPhase.TryGetValue(_phase, out var blockSideMaxContinueTime))
            {
                return 1;
            }

            foreach (var coef in _targetCoefAscArray)
            {
                if (blockSideMaxContinueTime < coef.BlockedSideMaxContinueTimeThreshold)
                {
                    return coef.Multiply;
                }
            }

            Debug.LogWarning(
                $"サイドをブロックされていた最大時間({blockSideMaxContinueTime}, フェーズ({_phase.ToString()}))に対応する係数がRaceParamDefineに定義されていないため" +
                $"倍率が取得できない"
            );
            return 1;
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【1200以上の圧縮、やる気、スキル効果の効果を含めたスピードの値に応じた係数】を乗算
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplySpeed : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary> 対象の係数テーブル </summary>
        private readonly Gallop.RaceParamDefine.SkillParam.SpeedCoef[] _targetCoefAscArray = null;
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplySpeed(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine,
            Gallop.SkillDefine.SkillAbilityValueUsage abilityValueUsage
        ) : base(owner, abilityValueBase)
        {
            switch (abilityValueUsage)
            {
                case Gallop.SkillDefine.SkillAbilityValueUsage.MultiplySpeed1:
                    _targetCoefAscArray = paramDefine.SpeedAbilityValueCoefAscArray1;
                    break;
                case Gallop.SkillDefine.SkillAbilityValueUsage.MultiplySpeed2:
                    _targetCoefAscArray = paramDefine.SpeedAbilityValueCoefAscArray2;
                    break;
                default:
                    Debug.LogError($"予期せぬ効果値タイプです。{abilityValueUsage}");
                    break;
            }
        }
        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            if (_targetCoefAscArray == null)
            {
                return 1;
            }

            foreach (var coef in _targetCoefAscArray)
            {
                if (_owner.Speed < coef.SpeedThreshold)
                {
                    return coef.Multiply;
                }
            }

            Debug.LogWarning(
                $"スピード({_owner.Speed})に対応する係数がRaceParamDefineに定義されていないため" +
                $"倍率が取得できない"
            );
            return 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【シナリオ専用パラメーターを獲得した値に応じた係数】を乗算：基底
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyScenarioData : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary> 対象の係数配列 </summary>
        protected Gallop.RaceParamDefine.SkillParam.ScenarioParameterCoef[] _scenarioParameterCoefArray;
        /// <summary> 対象の育成シナリオ </summary>
        protected Gallop.RaceDefine.ScenarioAdditionalDataType _scenarioType;
        //---------------------------------------------------------------
        protected SkillAbilityValueMultiplyScenarioData(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase
        ) : base(owner, abilityValueBase)
        {
        }
        //---------------------------------------------------------------
        // 初期設定
        protected virtual void SetupParameter(Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            // 継承先で実装する必要がある
            Debug.LogError("継承先クラスに実装を行ってください");
        }
        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            if (_scenarioParameterCoefArray　== null)
            {
                Debug.LogWarning($"RaceParamDefineに【シナリオ専用パラメーターを獲得した値に応じた係数】が定義されていない: ScenarioType = {_scenarioType}");
                return 1;
            }

            // 新規パラメーター【シナリオ専用パラメーターを獲得した値】を参照する
            var scenarioDataArray = _owner.ScenarioDataArray;
            if (_owner.ScenarioDataArray == null)
            {
                // null回避の早期リターン
                return 1;
            }

            var level = 0;
            for (int i = 0; i < scenarioDataArray.Length; i++)
            {
                // 万が一のためのnullチェック（基本的にない想定）
                if (scenarioDataArray[i] == null)
                {
                    break;
                }
                
                if (scenarioDataArray[i].type == (int)_scenarioType)
                {
                    level = scenarioDataArray[i].value;
                    break;
                }
            }
            // ParamDefineの閾値と比較する
            foreach (var coef in _scenarioParameterCoefArray)
            {
                if (level < coef.LevelThreshold)
                {
                    return coef.Multiply;
                }
            }

            Debug.LogWarning(
                $"【シナリオ専用パラメーターを獲得した値】({level})に対応する係数がRaceParamDefineに定義されていないため" +
                $"倍率が取得できない: ScenarioType = {_scenarioType}"
            );
            return 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【凱旋門賞編で獲得した海外適性レベルの値に応じた係数】を乗算
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyArcGlobalPotentialLevel : SkillAbilityValueMultiplyScenarioData
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyArcGlobalPotentialLevel(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine
        ) : base(owner, abilityValueBase)
        {
            SetupParameter(paramDefine);
        }
        //---------------------------------------------------------------
        // 育成シナリオ毎に初期設定をする
        protected sealed override void SetupParameter(Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            _scenarioType = Gallop.RaceDefine.ScenarioAdditionalDataType.ArcPotentialTotalLevel;
            _scenarioParameterCoefArray = paramDefine.ArcGlobalPotentialLevelCoefArray;
        }
        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            return base.GetMultiply();
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値に応じた係数】を乗算
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyTopLeadAmount : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary> 対象の係数テーブル </summary>
        private readonly Gallop.RaceParamDefine.SkillParam _paramDefine;
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyTopLeadAmount(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine
        ) : base(owner, abilityValueBase)
        {
            _paramDefine = paramDefine;
        }
        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            // 距離の割合は0~100%の間
            if (_paramDefine.TopLeadAmountStartCourseRatePer < 0 || _paramDefine.TopLeadAmountEndCourseRatePer > 100 ||
                _paramDefine.TopLeadAmountStartCourseRatePer > _paramDefine.TopLeadAmountEndCourseRatePer)
            {
                Debug.LogWarning("RaceParamDefineの【レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値に応じた係数】の計測範囲が不正な値です");
                return 1;
            }

            if (_paramDefine.TopLeadAmountCoefArray == null)
            {
                Debug.LogWarning("RaceParamDefineに【レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値に応じた係数】が定義されていない");
                return 1;
            }

            // 【レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値】を参照する
            var value = _owner.TopLeadMaxAmount;
            foreach (var coef in _paramDefine.TopLeadAmountCoefArray)
            {
                if (value < coef.Threshold)
                {
                    return coef.Multiply;
                }
            }

            Debug.LogWarning(
                $"【レース中特定の範囲において、自分が先頭時に2番手に対してつけた差の最大値】({value})に対応する係数がRaceParamDefineに定義されていないため" +
                $"倍率が取得できない"
            );
            return 1;
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【アスリート編U.A.F.決勝大会の勝利種目数の値に応じた係数】を乗算
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplySportFinalCompeWinCount : SkillAbilityValueMultiplyScenarioData
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplySportFinalCompeWinCount(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine
        ) : base(owner, abilityValueBase)
        {
            SetupParameter(paramDefine);
        }
        //---------------------------------------------------------------
        // 育成シナリオ毎に初期設定をする
        protected sealed override void SetupParameter(Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            _scenarioType = Gallop.RaceDefine.ScenarioAdditionalDataType.SportFinalCompeWinCount;
            _scenarioParameterCoefArray = paramDefine.SportFinalCompeWinCountCoefArray;
        }
        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            return base.GetMultiply();
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【お料理編で獲得したお料理Ptに応じた係数】を乗算
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyCookCookingPt : SkillAbilityValueMultiplyScenarioData
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyCookCookingPt(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine
        ) : base(owner, abilityValueBase)
        {
            SetupParameter(paramDefine);
        }
        //---------------------------------------------------------------
        // 育成シナリオ毎に初期設定をする
        protected sealed override void SetupParameter(Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            _scenarioType = Gallop.RaceDefine.ScenarioAdditionalDataType.CookCookingPt;
            _scenarioParameterCoefArray = paramDefine.CookCookingPtCoefArray;
        }
        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            return base.GetMultiply();
        }
    }
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【メカ編で獲得した総研究Lvに応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyMechaResearchTotalLevel : SkillAbilityValueMultiplyScenarioData
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyMechaResearchTotalLevel(
            IHorseRaceInfoSimulate owner,
            float abilityValueBase,
            Gallop.RaceParamDefine.SkillParam paramDefine
        ) : base(owner, abilityValueBase)
        {
            SetupParameter(paramDefine);
        }
        //---------------------------------------------------------------
        // 育成シナリオ毎に初期設定をする
        protected sealed override void SetupParameter(Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            _scenarioType = Gallop.RaceDefine.ScenarioAdditionalDataType.MechaResearchTotalLevel;
            _scenarioParameterCoefArray = paramDefine.MechaResearchTotalLevelCoefArray;
        }
        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            return base.GetMultiply();
        }
    }
}
#endif

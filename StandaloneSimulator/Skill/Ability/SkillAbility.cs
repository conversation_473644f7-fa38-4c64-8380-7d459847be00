#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果：Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityNull : ISkillAbility
    {
        //---------------------------------------------------------------
        public SkillDefine.SkillAbilityType AbilityType { get { return SkillDefine.SkillAbilityType.None; } }

        //---------------------------------------------------------------
        public SkillDefine.SkillAbilityValueUsage AbilityValueUsage { get { return SkillDefine.SkillAbilityValueUsage.None; } }

        //---------------------------------------------------------------
        public SkillDefine.SkillAbilityValueLevelUsage AbilityValueLevelUsage { get { return SkillDefine.SkillAbilityValueLevelUsage.None; } }

        //---------------------------------------------------------------
        public float AbilityValueOnActivate { get { return 0; } }

        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> AbilityTargets { get { return SkillTargetNull.EmptyTarget; } }
        //---------------------------------------------------------------
        public int CalcAdditionalActivateCount() { return 0; }

        //---------------------------------------------------------------
        public void AddAbilityTime(float additionalAbilityTime) { }

        //---------------------------------------------------------------
        public void Activate(bool isAdditionalActivate) { }
        //---------------------------------------------------------------
        public void Stop() { }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class SkillAbility : ISkillAbility
    {
        private readonly IHorseRaceInfoSimulate _owner = null;
        private readonly ISkillDetail _detail = null;
        private readonly ISkillTarget _target = null;
        private readonly ISkillAbilityValueCalculator _abilityValueWithUsage = null;
        /// <summary> アビリティ追加発動計算機 </summary>
        private readonly ISkillAbilityAdditionalActivate _abilityAdditionalActivate = null;
        private readonly int _level = SkillDefine.SKILL_LEVEL_MIN;
        private readonly Gallop.RaceParamDefine.SkillParam _skillParam;
        /// <summary> アビリティ追加発動タイプ </summary>
        private readonly SkillDefine.SkillAbilityAdditionalActivateType _additionalActivateType;
        /// <summary> このアビリティによるスキル効果リスト </summary>
        private readonly List<ISkillParamModifier> _skillParamModifierList;
        /// <summary> スキルが発動してからスキル追加効果発動が行われた回数 </summary>
        private int _additionalActivateCount = 0;
        
        /// <summary> HpRate: HpRateDemeritで効果量減少させた後の下限値（CSV基準） </summary>
        private const int HP_RATE_INT_VALUE_MIN = 50;
        /// <summary> HpRate: HpRateDemeritで効果量減少させた後の下限値（シミュレーター基準） </summary>
        private static readonly float HP_RATE_VALUE_MIN  = RaceUtilMath.MasterInt2Float(HP_RATE_INT_VALUE_MIN);

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        //---------------------------------------------------------------
        public SkillAbility(
            int level,
            IHorseRaceInfoSimulate owner,
            ISkillDetail detail,
            ISkillTarget target,
            SkillDefine.SkillAbilityType type,
            SkillDefine.SkillAbilityValueUsage valueUsage,
            SkillDefine.SkillAbilityValueLevelUsage valueLevelUsage,
            SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            float value,
            IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator,
            Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            AbilityType = type;
            AbilityValueUsage = valueUsage;
            AbilityValueLevelUsage = valueLevelUsage;
            _additionalActivateType = additionalActivateType;
            _owner = owner;
            _detail = detail;
            _target = target;
            _level = level;
            _skillParam = paramDefine;
            _abilityValueBase = value;

            // スキル効果値をレベルで補正。
            float valueWithLevel = SkillAbilityValueLevelCalculator.ApplyLevelToValueSimulate(level, type, value, valueLevelUsage);

            // SkillEditorに入力されている「効果値適用方法」と「効果値」から、実際に適用する効果値を算出するクラス。
            // レース再生の際も、RaceUIPlayerBuffがAbilityValueを参照するので、
            // シミュレート時だけでなくレース再生の際も正しい効果値が取得できる必要がある。
            // 加算適用の際には加算値にもレベル補正を別途掛けないといけないのでレベル補正をかけるためのメソッドを渡してあげる
            _abilityValueWithUsage = SkillAbilityValueCalculatorCreator.CreateCalculator(
                owner, valueUsage, valueWithLevel, horseAccessor, randomGenerator, paramDefine,
                (rawValue) => SkillAbilityValueLevelCalculator.ApplyLevelToValueSimulate(level, type, rawValue, valueLevelUsage)
            );
            
#if CYG_DEBUG
            // スキル効果量のテストを走らせておく
            TestSkillValue(type, value);
#endif

            _abilityAdditionalActivate = SkillAbilityAdditionalActivateCreator.CreateCalculator(owner, additionalActivateType, detail, paramDefine);
            _skillParamModifierList = new List<ISkillParamModifier>();
            _additionalActivateCount = 0;
        }

        //---------------------------------------------------------------
        public SkillDefine.SkillAbilityType AbilityType { get; private set; }

        //---------------------------------------------------------------
        public SkillDefine.SkillAbilityValueUsage AbilityValueUsage { get; private set; }

        //---------------------------------------------------------------
        public SkillDefine.SkillAbilityValueLevelUsage AbilityValueLevelUsage { get; private set; }

        //---------------------------------------------------------------
        /// <summary>
        /// ベースとなる効果値
        /// デメリット付与などで後から参照したいときのために保持している
        /// </summary>
        private float _abilityValueBase;
        
        /// <summary>
        /// 実際の適用値
        /// </summary>
        public float AbilityValueOnActivate
        {
            get
            {
                Debug.Assert(_isAbilityValueOnActivateInitialized, "Activate()での初期化前に_abilityValueOnActivateにアクセスしている");
                return _abilityValueOnActivate;
            }
            private set 
            {
                _abilityValueOnActivate = value;
                _isAbilityValueOnActivateInitialized = true;
            }
        }
        private float _abilityValueOnActivate;
        private bool _isAbilityValueOnActivateInitialized;

        //---------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> AbilityTargets => _target.GetTargets();

        //---------------------------------------------------------------
        public int CalcAdditionalActivateCount()
        {
            return _abilityAdditionalActivate.CurrentAdditionalActivateCount();
        }
        
        //---------------------------------------------------------------
        public void AddAbilityTime(float additionalAbilityTime)
        {
            foreach (var skillParamModifier in _skillParamModifierList)
            {
                skillParamModifier.AddAbilityTime(additionalAbilityTime);
            }
        }
        
        //---------------------------------------------------------------
        public void Activate(bool isAdditionalActivate)
        {
            _target.CalcTargets();

            // #75086 _abilityValueWithUsageでランダム抽選が設定されていると、SkillDetail.IsHealSkillでAbilityValueを参照するときに値が変わってしまうため、効果発動時に値を決定しておく。
            AbilityValueOnActivate = _abilityValueWithUsage.AbilityValue;
            
            // スキル効果追加発動のスキルはスキル発動時には何も効果を適用しない
            if (RaceUtil.IsAdditionalActivateAbility(_additionalActivateType) && !isAdditionalActivate)
            {
                return;
            }
            
            if (isAdditionalActivate)
            {
                _additionalActivateCount++;
            }
            
            var targets = _target.GetTargets();
            var abilityValue = AbilityValueOnActivate;
            float time = _detail.LeftTime;
            for (int i = 0, cnt = targets.Count; i < cnt; ++i)
            {
                var target = targets[i];

                switch (AbilityType)
                {
                    case SkillDefine.SkillAbilityType.Speed:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Speed, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.Stamina:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Stamina, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.Power:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Power, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.Guts:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Guts, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.Wiz:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Wiz, abilityValue, time);
                        break;

                    case SkillDefine.SkillAbilityType.AllStatus:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Speed, abilityValue, time);
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Stamina, abilityValue, time);
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Power, abilityValue, time);
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Guts, abilityValue, time);
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Wiz, abilityValue, time);
                        break;
                    
                    case SkillDefine.SkillAbilityType.RunningStyleExOonige:
                        SetSkillValueParam(target, SkillDefine.SkillModifierParam.RunningStyleExOonige,true, time);
                        break;
                        
                    case SkillDefine.SkillAbilityType.CurrentSpeed:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.CurrentSpeed, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.CurrentSpeedWithNaturalDeceleration:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.CurrentSpeedWithNaturalDeceleration, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.TargetSpeed:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.TargetSpeed, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.LaneMoveSpeed:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.LaneMoveSpeed, abilityValue, time);
                        // #73415 レーン移動速度上昇時、目指す速度も加算する。
                        if (abilityValue > 0)
                        {
                            SetSkillValueParam(target, SkillDefine.SkillModifierParam.TargetSpeedOnMoveLane, true, time);
                        }
                        break;
                    case SkillDefine.SkillAbilityType.TemptationPer:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.TemptationPer, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.PushPer:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.PushPer, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.Accel:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.Accel, abilityValue, time);
                        break;

                    case SkillDefine.SkillAbilityType.TargetLane:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.TargetLane, abilityValue, time);
                        break;

                    case SkillDefine.SkillAbilityType.HpDecRate:
                        SetSkillValueParam(target, SkillDefine.SkillModifierParam.HpDecRate, abilityValue, time);
                        break;

                    case SkillDefine.SkillAbilityType.VisibleDistance:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.VisibleDistance, abilityValue, time);
                        break;

                    case SkillDefine.SkillAbilityType.HpRate:
                    {
                        float addHp = target.GetMaxHp() * abilityValue;
                        
                        // メリット効果の場合のみ、デメリット効果がある場合は、メリット効果を適用する
                        var hpRateDemeritValue = target.GetHpRateDemeritValue();
                        if (hpRateDemeritValue > 0)
                        {
                            // abilityValueはレベル補正後の値だが、ここでは補正前の値にデメリット付与したいので、仕方がなく再計算を行う
                            // ---
                            // 補正前の効果値にデメリット付与（下限あり）
                            if (_abilityValueBase > HP_RATE_VALUE_MIN)
                            {
                                // デメリット付与後の元効果値
                                var newAbilityValueBase = Math.Max(_abilityValueBase - hpRateDemeritValue, HP_RATE_VALUE_MIN);
                                // レベル補正の再計算
                                var newAbilityValue = SkillAbilityValueLevelCalculator.ApplyLevelToValueSimulate(_level, AbilityType, newAbilityValueBase, AbilityValueLevelUsage);
                                // 回復量の上書き
                                addHp = target.GetMaxHp() * newAbilityValue;
                            }
                        }
                        
                        AddSkillValueParamOneTime(target, SkillDefine.SkillModifierParam.Hp, addHp, time);
                        break;
                    }
                        
                    
                    case SkillDefine.SkillAbilityType.TemptationEndTime:
                        SetSkillValueParamOneTime(target, SkillDefine.SkillModifierParam.TemptationEndTime, abilityValue, time);
                        break;

                    case SkillDefine.SkillAbilityType.StartDash:
                    {
                        // 倍率は0以上の値のはず。
                        if (abilityValue < 0)
                        {
                            Debug.LogWarning( $"スタートダッシュスキルの効果値が不正（0未満になっている）。SkillId={_detail.SkillId}, abilityValue={abilityValue}" );
                            abilityValue = 0;
                        }
                        SetSkillValueParamOneTime(target, SkillDefine.SkillModifierParam.StartDelayScale, abilityValue, time);
                    }
                    break;
                    
                    case SkillDefine.SkillAbilityType.StartDelayFix:
                        SetSkillValueParamOneTimeHigher(target, SkillDefine.SkillModifierParam.StartDelayFix, abilityValue, time, 0);
                        break;
                    
                    case SkillDefine.SkillAbilityType.HpRateDemerit:
                        AddSkillValueParam(target, SkillDefine.SkillModifierParam.HpRateDemerit, abilityValue, time);
                        break;
                    
                    case SkillDefine.SkillAbilityType.ForceOvertakeIn:
                        SetSkillValueParam(target, SkillDefine.SkillModifierParam.ForceOvertakeIn, true, time);
                        break;
                    
                    case SkillDefine.SkillAbilityType.ForceOvertakeOut:
                        SetSkillValueParam(target, SkillDefine.SkillModifierParam.ForceOvertakeOut, true, time);
                        break;
                    case SkillDefine.SkillAbilityType.ActivateRandomNormalAndRareSkill:
                        SetSkillValueParamOneTime(target, SkillDefine.SkillModifierParam.ActivateRandomNormalAndRareSkill, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.ActivateRandomRareSkill:
                        SetSkillValueParamOneTime(target, SkillDefine.SkillModifierParam.ActivateRandomRareSkill, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.DebuffCancel:
                        SetSkillValueParam(target, SkillDefine.SkillModifierParam.DebuffCancel, true, time);
                        break;
                    case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiply:
                        MultiplySkillValueParam(target, SkillDefine.SkillModifierParam.DebuffAbilityValueMultiply, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiplyOtherActivate:
                        MultiplySkillValueParam(target, SkillDefine.SkillModifierParam.DebuffAbilityValueMultiplyOtherActivate, abilityValue, time);
                        break;
                    case SkillDefine.SkillAbilityType.ActivateSpecificSkill:
                        // スキル強制発動なので発動時の1回のみ効果適用する
                        // (効果時間中で定義してもどのみち強制発動される側のスキルのクールダウンとかがあるので問題は起きない想定だが一応)
                        SetSkillValueParamOneTime(target, SkillDefine.SkillModifierParam.ActivateSpecificSkill, true, time);
                        break;
                }
            }
            
        #if CYG_DEBUG
            // デバフ無効化によって発動しなかったスキルもログには出したいので別途記録する
            foreach (var target in _target.GetDbgDebuffCancelTargetList())
            {
                DbgSetCancelledSkillValueParam(DbgCreateSkillValueParamList(target, AbilityType, abilityValue, time));
            }
        #endif
        }

        //---------------------------------------------------------------
        public void Stop()
        {
            // リストの解放はするがSkillParamModifierの終了は各々に巻かせる
            _skillParamModifierList.Clear();
            _additionalActivateCount = 0;
        }
        
    #if CYG_DEBUG
        private void DbgCheckLeftTimeOneTime(float time)
        {
            // OneTime系の効果は効果時間0を期待しているので違う値が入っていたらエラーログ出す。
            if (!RaceUtilMath.Approximately(time, 0))
            {
                Debug.LogWarning($"OneTime系の効果（{AbilityType}）で効果時間が０になっていません。効果時間={time}、SkillId={_detail.SkillId}");
            }
        }
        
        /// <summary>
        /// デバッグログ用にキャンセルされたスキル効果を記録する
        /// </summary>
        /// <remarks>
        /// 発動記録として残したいだけなのでSkillParamModifierSetで記録する
        /// </remarks>
        /// <param name="skillValueParamList"> 発動アビリティリスト </param>
        private void DbgSetCancelledSkillValueParam(IEnumerable<DbgSkillValueParam> skillValueParamList)
        {
            foreach (var skillValueParam in skillValueParamList)
            {
                bool isActivateOthers = skillValueParam.Target != _owner;
                var modifier = new SkillParamModifierSet(
                    skillValueParam.AbilityValue, skillValueParam.Time, skillValueParam.Type, _owner.DbgGetModifierReceiver.DbgSkillParamFluctuate,  isActivateOthers
                );
                modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, false);
                skillValueParam.Target.AddCancelledModifier(skillValueParam.Type, modifier);
            }
        }

        /// <summary>
        /// スキル効果発動時の受信機に渡すデータ
        /// </summary>
        struct DbgSkillValueParam
        {
            public readonly IHorseRaceInfoSimulate Target;
            public readonly SkillDefine.SkillModifierParam Type;
            public readonly float AbilityValue;
            public readonly float Time;

            public DbgSkillValueParam(IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, float abilityValue, float time)
            {
                Target = target;
                Type = type;
                AbilityValue = abilityValue;
                Time = time;
            }
            public DbgSkillValueParam(IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, bool abilityValue, float time)
            {
                Target = target;
                Type = type;
                // SkillModifierReceiver::Bool2Float を使うようにしても良いかも
                AbilityValue = abilityValue ? 1 : 0;
                Time = time;
            }
        }

        /// <summary>
        /// SkillModifierを作るのに必要なパラメータリストを取得する
        /// </summary>
        /// <remarks>
        /// もしデバッグに余裕があればこれをActivateで使用する形にしても良いと思う
        /// </remarks>
        /// <param name="target"></param>
        /// <param name="type"></param>
        /// <param name="abilityValue"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        private IReadOnlyList<DbgSkillValueParam> DbgCreateSkillValueParamList(
            IHorseRaceInfoSimulate target, SkillDefine.SkillAbilityType type, float abilityValue, float time
        )
        {
            var skillValueParamList = new List<DbgSkillValueParam>();
            switch (AbilityType)
            {
                case SkillDefine.SkillAbilityType.Speed:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Speed, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.Stamina:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Stamina, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.Power:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Power, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.Guts:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Guts, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.Wiz:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Wiz, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.AllStatus:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Speed, abilityValue, time));
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Stamina, abilityValue, time));
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Power, abilityValue, time));
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Guts, abilityValue, time));
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Wiz, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.RunningStyleExOonige:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.RunningStyleExOonige,true, time));
                    break;
                    
                case SkillDefine.SkillAbilityType.CurrentSpeed:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.CurrentSpeed, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.CurrentSpeedWithNaturalDeceleration:
                    skillValueParamList.Add(
                        new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.CurrentSpeedWithNaturalDeceleration, abilityValue, time)
                    );
                    break;
                case SkillDefine.SkillAbilityType.TargetSpeed:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.TargetSpeed, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.LaneMoveSpeed:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.LaneMoveSpeed, abilityValue, time));
                    // #73415 レーン移動速度上昇時、目指す速度も加算する。
                    if (abilityValue > 0)
                    {
                        skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.TargetSpeedOnMoveLane, true, time));
                    }
                    break;
                case SkillDefine.SkillAbilityType.TemptationPer:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.TemptationPer, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.PushPer:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.PushPer, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.Accel:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Accel, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.TargetLane:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.TargetLane, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.HpDecRate:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.HpDecRate, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.VisibleDistance:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.VisibleDistance, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.HpRate:
                    float addHp = target.GetMaxHp() * abilityValue;
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.Hp, addHp, time));
                    break;

                case SkillDefine.SkillAbilityType.TemptationEndTime:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.TemptationEndTime, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.StartDash:
                {
                    // 倍率は0以上の値のはず。
                    if (abilityValue < 0)
                    {
                        Debug.LogWarning( $"スタートダッシュスキルの効果値が不正（0未満になっている）。SkillId={_detail.SkillId}, abilityValue={abilityValue}" );
                        abilityValue = 0;
                    }
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.StartDelayScale, abilityValue, time));
                }
                break;
                
                case SkillDefine.SkillAbilityType.HpRateDemerit:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.HpRateDemerit, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.StartDelayFix:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.StartDelayFix, abilityValue, time));
                    break;

                case SkillDefine.SkillAbilityType.ForceOvertakeIn:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.ForceOvertakeIn, true, time));
                    break;

                case SkillDefine.SkillAbilityType.ForceOvertakeOut:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.ForceOvertakeOut, true, time));
                    break;
                case SkillDefine.SkillAbilityType.ActivateRandomNormalAndRareSkill:
                    skillValueParamList.Add(
                        new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.ActivateRandomNormalAndRareSkill, abilityValue, time)
                    );
                    break;
                case SkillDefine.SkillAbilityType.ActivateRandomRareSkill:
                    skillValueParamList.Add(
                        new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.ActivateRandomRareSkill, abilityValue, time)
                    );
                    break;
                case SkillDefine.SkillAbilityType.DebuffCancel:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.DebuffCancel, true, time));
                    break;
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiply:
                    skillValueParamList.Add(new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.DebuffAbilityValueMultiply, abilityValue, time));
                    break;
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiplyOtherActivate:
                    skillValueParamList.Add(
                        new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.DebuffAbilityValueMultiplyOtherActivate, abilityValue, time)
                    );
                    break;
                case SkillDefine.SkillAbilityType.ActivateSpecificSkill:
                    skillValueParamList.Add(
                        new DbgSkillValueParam(target, SkillDefine.SkillModifierParam.ActivateSpecificSkill, abilityValue,time)
                    );
                    break;
            }

            return skillValueParamList;
        }
        
        /// <summary>
        /// 【Test】スキル効果量のチェック
        /// </summary>
        /// <remarks>
        /// memo: DSLでは細かいテスト実装が面倒なので、シミュレーター側で実装している
        /// </remarks>
        private void TestSkillValue(SkillDefine.SkillAbilityType type, float abilityValue)
        {
            switch (type)
            {
                case SkillDefine.SkillAbilityType.CurrentSpeed:
                {
                    // これは負の値を入れてデバフスキルとして使うことを想定している
                    if (abilityValue >= 0)
                    {
                        Debug.LogError( $"SkillAbilityType21の効果値が不正（0以上になっている）。SkillId={_detail.SkillId}, abilityValue={abilityValue}" );
                    }
                    break;
                }
                case SkillDefine.SkillAbilityType.CurrentSpeedWithNaturalDeceleration:
                {
                    // これは正の値を入れてバフスキルとして使うことを想定している
                    if (abilityValue < 0)
                    {
                        Debug.LogError( $"SkillAbilityType22の効果値が不正（0未満になっている）。SkillId={_detail.SkillId}, abilityValue={abilityValue}" );
                    }
                    break;
                }
                case SkillDefine.SkillAbilityType.TargetSpeed:
                {
                    // これは正の値を入れてバフスキルとして使うことを想定している
                    if (abilityValue < 0)
                    {
                        Debug.LogError( $"SkillAbilityType27の効果値が不正（0未満になっている）。SkillId={_detail.SkillId}, abilityValue={abilityValue}" );
                    }
                    break;
                }
            }
        }
    #endif

        //---------------------------------------------------------------
        private void AddSkillValueParam(IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, float value, float time)
        {
            bool isActivateOthers = _owner != target;
            var modifier = target.CreateSkillParamAddModifier(value, time, type, isActivateOthers);
        #if CYG_DEBUG
            modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, isValid: true);
        #endif
            target.AddModifier(type, modifier);
            _skillParamModifierList.Add(modifier);
        }
        private void AddSkillValueParamOneTime(
            IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, float value, float abilityTime
        )
        {
            bool isActivateOthers = _owner != target;
            // 1回のみの発動なので時間は渡さない
            var modifier = target.CreateSkillParamAddModifier(value, null, type, isActivateOthers);
        #if CYG_DEBUG
            DbgCheckLeftTimeOneTime(abilityTime);
            modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, isValid: true);
        #endif
            target.AddModifier(type, modifier);
            _skillParamModifierList.Add(modifier);
        }
        private void SetSkillValueParamOneTimeHigher(
            IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, float value, float time, float defaultValue
        )
        {
            // 既に同じ効果が適用中なら、より大きい値の時だけ適用する。
            if (target.HasModifier(type))
            {
                float appliedValue = target.ApplyModifier(type, defaultValue);
                if (value < appliedValue)
                {
                    return;
                }
                
                // 新しい効果値の方が大きいので、既存のものは削除。
                target.RemoveModifier(type);
            }
            SetSkillValueParamOneTime(target, type, value, time);
        }

        /// <summary>
        /// valueがfloat値でスキル効果時間中ずっと効果が続くスキル効果
        /// </summary>
        /// <param name="target"></param>
        /// <param name="type"></param>
        /// <param name="value"></param>
        /// <param name="time"></param>
        private void SetSkillValueParam(IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, float value, float time)
        {
            bool isActivateOthers = _owner != target;
            var modifier = target.CreateSkillParamSetModifier(value, time, type, isActivateOthers);
        #if CYG_DEBUG
            modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, isValid: true);
        #endif
            target.AddModifier(type, modifier);
            _skillParamModifierList.Add(modifier);
        }

        /// <summary>
        /// valueがfloat値で発動時のみ効果が発動するスキル効果
        /// </summary>
        /// <param name="target"></param>
        /// <param name="type"></param>
        /// <param name="value"></param>
        /// <param name="time"></param>
        private void SetSkillValueParamOneTime(
            IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, float value, float time
        )
        {
            bool isActivateOthers = _owner != target;
            // 1回のみの発動なので時間は渡さない
            var modifier = target.CreateSkillParamSetModifier(value, null, type, isActivateOthers);
        #if CYG_DEBUG
            DbgCheckLeftTimeOneTime(time);
            modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, isValid: true);
        #endif
            target.AddModifier(type, modifier);
            _skillParamModifierList.Add(modifier);
        }

        /// <summary>
        /// valueがbool値でスキル効果時間中ずっと効果が続くスキル効果
        /// </summary>
        /// <param name="target"></param>
        /// <param name="type"></param>
        /// <param name="value"></param>
        /// <param name="time"></param>
        private void SetSkillValueParam(IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, bool value, float time)
        {
            bool isActivateOthers = _owner != target;
            var modifier = target.CreateSkillParamSetModifier(value, time, type, isActivateOthers);
        #if CYG_DEBUG
            modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, isValid: true);
        #endif
            target.AddModifier(type, modifier);
            _skillParamModifierList.Add(modifier);
        }

        /// <summary>
        /// valueがbool値で発動時のみ効果が発動するスキル効果
        /// </summary>
        /// <param name="target"></param>
        /// <param name="type"></param>
        /// <param name="value"></param>
        /// <param name="time"></param>
        private void SetSkillValueParamOneTime(
            IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, bool value, float time
        )
        {
            bool isActivateOthers = _owner != target;
            // 1回のみの発動なので時間は渡さない
            var modifier = target.CreateSkillParamSetModifier(value, null, type, isActivateOthers);
        #if CYG_DEBUG
            DbgCheckLeftTimeOneTime(time);
            modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, isValid: true);
        #endif
            target.AddModifier(type, modifier);
            _skillParamModifierList.Add(modifier);
        }

        /// <summary>
        /// 乗算用のSkillModifierParam生成
        /// </summary>
        /// <param name="target"></param>
        /// <param name="type"></param>
        /// <param name="value"></param>
        /// <param name="time"></param>
        private void MultiplySkillValueParam(IHorseRaceInfoSimulate target, SkillDefine.SkillModifierParam type, float value, float time)
        {
            bool isActivateOthers = _owner != target;
            var modifier = target.CreateSkillParamMultiplyModifier(value, time, type, isActivateOthers);
        #if CYG_DEBUG
            modifier.AppendDbgSkill(_owner.HorseIndex, _detail.SkillId, _detail.SkillLevel, isValid: true);
        #endif
            target.AddModifier(type, modifier);
            _skillParamModifierList.Add(modifier);
        }
    }
}
#endif

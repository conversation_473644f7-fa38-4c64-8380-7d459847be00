using System.Collections;
using System.Collections.Generic;
#if STANDALONE_SIMULATOR || UNITY_EDITOR

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public partial interface ISkillAbility
    {
        //---------------------------------------------------------------
        /// <summary>
        /// 効果量使用方法取得。
        /// </summary>
        //---------------------------------------------------------------
        Gallop.SkillDefine.SkillAbilityValueUsage AbilityValueUsage { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// 効果量レベル補正適用方法取得。
        /// </summary>
        //---------------------------------------------------------------
        Gallop.SkillDefine.SkillAbilityValueLevelUsage AbilityValueLevelUsage { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// 効果量取得。Activateで効果発動時に始めて値が入る。
        /// </summary>
        //---------------------------------------------------------------
        float AbilityValueOnActivate { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// 効果対象取得。
        /// </summary>
        //---------------------------------------------------------------
        List<IHorseRaceInfoSimulate> AbilityTargets { get; }

        //---------------------------------------------------------------
        /// <summary>
        /// アビリティ追加発動可能な回数を計算する
        /// </summary>
        //---------------------------------------------------------------
        int CalcAdditionalActivateCount();

        //---------------------------------------------------------------
        /// <summary>
        /// アビリティ効果時間加算
        /// </summary>
        //---------------------------------------------------------------
        void AddAbilityTime(float additionalAbilityTime);
        
        //---------------------------------------------------------------
        /// <summary>
        /// 効果発動。
        /// </summary>
        /// <param name="isAdditionalActivate"> 追加発動によるものかどうか </param>
        //---------------------------------------------------------------
        void Activate(bool isAdditionalActivate);
        //---------------------------------------------------------------
        /// <summary>
        /// スキル効果終了
        /// </summary>
        //---------------------------------------------------------------
        void Stop();
    }
}
#endif

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果インターフェース。
    /// .NET/GALLOP兼用
    /// </summary>
    //-------------------------------------------------------------------
    public partial interface ISkillAbility
    {
        //---------------------------------------------------------------
        /// <summary>
        /// 効果種類取得。
        /// </summary>
        //---------------------------------------------------------------
        Gallop.SkillDefine.SkillAbilityType AbilityType { get; }
    }
}

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    public static class SkillAbilityValueCalculatorCreator
    {
        //---------------------------------------------------------------
        /// <summary>
        /// スキル効果値計算機の生成ファクトリ。
        /// </summary>
        /// <param name="owner">スキル所有者。</param>
        /// <param name="usage">効果値適用方法。</param>
        /// <param name="abilityValueBase">効果値。</param>
        //---------------------------------------------------------------
        public static ISkillAbilityValueCalculator CreateCalculator( 
            IHorseRaceInfoSimulate owner, 
            SkillDefine.SkillAbilityValueUsage usage, 
            float abilityValueBase,
            IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator,
            Gallop.RaceParamDefine.SkillParam paramDefine,
            System.Func<float, float> applyValueLevelUsage
        )
        {
            switch( usage )
            {
                case SkillDefine.SkillAbilityValueUsage.Direct:
                    return new SkillAbilityValueDirect( abilityValueBase ); 
                case SkillDefine.SkillAbilityValueUsage.MultiplySkillNum:
                    return new SkillAbilityValueMultiplySkillNum(owner, abilityValueBase);
                case SkillDefine.SkillAbilityValueUsage.MultiplyActivateHealSkillCount:
                    return new SkillAbilityValueMultiplyHealSkillActivateCount( owner, abilityValueBase ); 
                case SkillDefine.SkillAbilityValueUsage.MultiplyActivateSpecificTagSkillCount:
                    return new SkillAbilityValueMultiplyActivateSpecificTagSkillCount( owner, abilityValueBase, paramDefine); 
                case SkillDefine.SkillAbilityValueUsage.MultiplyActivateSpecificSkillAbilityTypeCount:
                    return new SkillAbilityValueMultiplyActivateSpecificSkillAbilityTypeCount(owner, abilityValueBase,
                        paramDefine); 
                case SkillDefine.SkillAbilityValueUsage.MultiplyFinalCornerEndOrder:
                    return new SkillAbilityValueMultiplyFinalCornerEndOrder(owner, abilityValueBase);
                case SkillDefine.SkillAbilityValueUsage.MultiplyInvTeamMemberCount:
                    return new SkillAbilityValueMultiplyInvTeamMemberCount(owner, abilityValueBase, horseAccessor);
                case SkillDefine.SkillAbilityValueUsage.MultiplyRandom1:
                    return new SkillAbilityValueMultiplyRandom(
                        owner, 
                        abilityValueBase, 
                        horseAccessor, 
                        randomGenerator,
                        paramDefine.AbiiltyValueUsageRandom1Array);
                case SkillDefine.SkillAbilityValueUsage.MultiplyRandom2:
                    return new SkillAbilityValueMultiplyRandom(
                        owner, 
                        abilityValueBase, 
                        horseAccessor, 
                        randomGenerator,
                        paramDefine.AbiiltyValueUsageRandom2Array);
                
                case SkillDefine.SkillAbilityValueUsage.MultiplyTeamTotalSpeed:
                    return new SkillAbilityValueMultiplyTeamTotalStatusSpeed(owner, abilityValueBase, horseAccessor, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyTeamTotalStamina:
                    return new SkillAbilityValueMultiplyTeamTotalStatusStamina(owner, abilityValueBase, horseAccessor, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyTeamTotalPower:
                    return new SkillAbilityValueMultiplyTeamTotalStatusPower(owner, abilityValueBase, horseAccessor, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyTeamTotalGuts:
                    return new SkillAbilityValueMultiplyTeamTotalStatusGuts(owner, abilityValueBase, horseAccessor, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyTeamTotalWiz:
                    return new SkillAbilityValueMultiplyTeamTotalStatusWiz(owner, abilityValueBase, horseAccessor, paramDefine);
                
                case SkillDefine.SkillAbilityValueUsage.MultiplySingleModeWinCount:
                    return new SkillAbilityValueMultiplySingleModeWinCount(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyOrderUpCountCornerPhaseEndAfter:
                    return new SkillAbilityValueMultiplyOrderUpCountCornerPhaseEndAfter(owner, abilityValueBase, paramDefine);

                case SkillDefine.SkillAbilityValueUsage.MultiplyBaseWiz:
                    return new SkillAbilityValueMultiplyBaseWiz(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyFanCount:
                    return new SkillAbilityValueMultiplyFanCount(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyMaximumRawStatus:
                    return new SkillAbilityValueMultiplyMaximumRawStatus(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.AddDistanceDiffTop:
                    return new SkillAbilityValueAddDistanceDiffTop(owner, abilityValueBase, horseAccessor, paramDefine, applyValueLevelUsage);
                case SkillDefine.SkillAbilityValueUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1:
                case SkillDefine.SkillAbilityValueUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2:
                    // 同じクラスを使用するが中で対象の係数テーブルをabilityValueUsageのタイプによって変更している
                    return new SkillAbilityValueMultiplyBlockedSideMaxContinueTime(owner, abilityValueBase, paramDefine, usage);
                case SkillDefine.SkillAbilityValueUsage.MultiplySpeed1:
                case SkillDefine.SkillAbilityValueUsage.MultiplySpeed2:
                    // 同じクラスを使用するが中で対象の係数テーブルをabilityValueUsageのタイプによって変更している
                    return new SkillAbilityValueMultiplySpeed(owner, abilityValueBase, paramDefine, usage);
                case SkillDefine.SkillAbilityValueUsage.MultiplyArcGlobalPotentialLevel:
                    return new SkillAbilityValueMultiplyArcGlobalPotentialLevel(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyTopLeadAmount:
                    return new SkillAbilityValueMultiplyTopLeadAmount(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplySportFinalCompeWinCount:
                    return new SkillAbilityValueMultiplySportFinalCompeWinCount(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyCookCookingPt:
                    return new SkillAbilityValueMultiplyCookCookingPt(owner, abilityValueBase, paramDefine);
                case SkillDefine.SkillAbilityValueUsage.MultiplyMechaResearchTotalLevel:
                    return new SkillAbilityValueMultiplyMechaResearchTotalLevel(owner, abilityValueBase, paramDefine);
                // 不正値。
                default:
                    Debug.Assert( false, "予期しないスキル効果値適用種類です。:" + usage );
                    return new SkillAbilityValueDirect( abilityValueBase ); 
            }
        }
    }
}

#endif

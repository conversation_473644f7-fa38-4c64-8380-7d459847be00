#if STANDALONE_SIMULATOR || UNITY_EDITOR
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    public static class SkillAbilityCreator
    {
        public static ISkillAbility CreateSkillAbility(
            int level,
            IHorseRaceInfoSimulate owner,
            ISkillDetail detail,
            SkillDefine.SkillAbilityType abilityType,
            SkillDefine.SkillAbilityValueUsage valueUsage,
            SkillDefine.SkillAbilityValueLevelUsage valueLevelUsage,
            SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            float abilityValue,
            ISkillTarget target,
            IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator,
            Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            if (abilityType <= 0)
            {
                return null;
            }

            switch (abilityType)
            {
                case SkillDefine.SkillAbilityType.ForceOvertakeIn:
                case SkillDefine.SkillAbilityType.ForceOvertakeOut:
                case SkillDefine.SkillAbilityType.StartDash:
                case SkillDefine.SkillAbilityType.HpRate:
                case SkillDefine.SkillAbilityType.TemptationEndTime:
                case SkillDefine.SkillAbilityType.Speed:
                case SkillDefine.SkillAbilityType.Stamina:
                case SkillDefine.SkillAbilityType.Power:
                case SkillDefine.SkillAbilityType.Guts:
                case SkillDefine.SkillAbilityType.Wiz:
                case SkillDefine.SkillAbilityType.AllStatus:
                case SkillDefine.SkillAbilityType.HpDecRate:
                case SkillDefine.SkillAbilityType.CurrentSpeed:
                case SkillDefine.SkillAbilityType.CurrentSpeedWithNaturalDeceleration:
                case SkillDefine.SkillAbilityType.TargetSpeed:
                case SkillDefine.SkillAbilityType.LaneMoveSpeed:
                case SkillDefine.SkillAbilityType.TemptationPer:
                case SkillDefine.SkillAbilityType.PushPer:
                case SkillDefine.SkillAbilityType.Accel:
                case SkillDefine.SkillAbilityType.TargetLane:
                case SkillDefine.SkillAbilityType.VisibleDistance:
                case SkillDefine.SkillAbilityType.StartDelayFix:
                case SkillDefine.SkillAbilityType.HpRateDemerit:
                case SkillDefine.SkillAbilityType.ActivateRandomNormalAndRareSkill:
                case SkillDefine.SkillAbilityType.ActivateRandomRareSkill:
                case SkillDefine.SkillAbilityType.DebuffCancel:
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiply:
                case SkillDefine.SkillAbilityType.DebuffAbilityValueMultiplyOtherActivate:
                case SkillDefine.SkillAbilityType.ActivateSpecificSkill:
                    return new SkillAbility(level, owner, detail, target, abilityType, valueUsage, valueLevelUsage, additionalActivateType, abilityValue, horseAccessor, randomGenerator, paramDefine);

                default:
                    return new SkillAbilityNull();
            }
        }
    }
}
#endif

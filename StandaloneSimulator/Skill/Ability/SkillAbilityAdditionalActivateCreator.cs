#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    public static class SkillAbilityAdditionalActivateCreator
    {
        /// <summary>
        /// スキル追加発動生成ファクトリ
        /// </summary>
        /// <param name="owner"> スキル所有者 </param>
        /// <param name="additionalActivateType"> 追加発動タイプ </param>
        /// <param name="skillDetail"> スキルデータ </param>
        /// <param name="paramDefine"> スキルパラメータ </param>
        /// <returns></returns>
        public static ISkillAbilityAdditionalActivate CreateCalculator(
            IHorseRaceInfoSimulate owner,
            Gallop.SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            ISkillDetail skillDetail,
            Gallop.RaceParamDefine.SkillParam paramDefine)
        {
            switch (additionalActivateType)
            {
                case Gallop.SkillDefine.SkillAbilityAdditionalActivateType.None:
                    return new SkillAbilityAdditionalActivateNone();
                case Gallop.SkillDefine.SkillAbilityAdditionalActivateType.AdditionalActivateOrderUp:
                    return new SkillAbilityAdditionalActivateOrderUp(owner, additionalActivateType, paramDefine);
                case Gallop.SkillDefine.SkillAbilityAdditionalActivateType.AdditionalActivateActivateAnySkill1:
                case Gallop.SkillDefine.SkillAbilityAdditionalActivateType.AdditionalActivateActivateAnySkill2:
                    return new SkillAbilityAdditionalActivateSelfActivateAnySkill(owner, skillDetail.SkillId, additionalActivateType, paramDefine);

                default:
                    Debug.Assert(false, $"予期しない追加発動タイプです。: {additionalActivateType}");
                    return new SkillAbilityAdditionalActivateNone();
            }
        }
    }
}
#endif
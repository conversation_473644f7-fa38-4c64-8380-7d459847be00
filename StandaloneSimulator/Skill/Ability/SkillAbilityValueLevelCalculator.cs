using System.Linq;
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値をレベルで補正する計算処理。
    /// </summary>
    //-------------------------------------------------------------------
    public static class SkillAbilityValueLevelCalculator
    {
#if STANDALONE_SIMULATOR || UNITY_EDITOR
        //-------------------------------------------------------------------
        // シミュレーター用
        //-------------------------------------------------------------------
        /// <summary>
        /// スキル効果値をレベルで補正。
        /// </summary>
        /// <param name="level">スキルレベル。1~。</param>
        /// <param name="type">スキル効果種類。</param>
        /// <param name="value">スキル効果値。</param>
        /// <returns>補正後のスキル効果値。</returns>
        /// <seealso cref="Test.TestSkillAbilityValueLevelCalculator.TestApplyLevelToValue(float, int, SkillDefine.SkillAbilityType, float, SkillDefine.SkillAbilityValueLevelUsage)"/>
        public static float ApplyLevelToValueSimulate(int level, SkillDefine.SkillAbilityType type, float value, SkillDefine.SkillAbilityValueLevelUsage valueLevelUsage)
        {
            float coefFloat = GetLevelCoefSimulate(level, type);
            return ApplyLevelCoefToValue(coefFloat, value, valueLevelUsage);
        }

        /// <summary>
        /// レベルとスキル効果種類から、補正係数取得。
        /// </summary>
        private static float GetLevelCoefSimulate(int level, SkillDefine.SkillAbilityType type)
        {
            var abilityLevelList = MasterManager.Instance.MasterSkillLevelValue.GetListWithAbilityTypeOrderByLevelAsc((int)type);
            if (null == abilityLevelList || abilityLevelList.Count == 0)
            {
                Debug.LogError("SkillAbilityTypeに対応するレベル毎効果値補正値のデータが見つかりません。type=" + type);
                return 1.0f;
            }

            var masterAbilityLevel = abilityLevelList.FirstOrDefault(a => a.Level == level);
            if (null == masterAbilityLevel)
            {
                Debug.LogError(string.Format("SkillAbilityTypeのレベルに対応するレベル毎効果値補正値のデータが見つかりません。type={0}, level={1}", type, level));
                return 1.0f;
            }

            float coefFloat = RaceUtilMath.MasterInt2Float(masterAbilityLevel.FloatAbilityValueCoef);
            return coefFloat;
        }
#endif
#if GALLOP
        //-------------------------------------------------------------------
        // GALLOP用
        //-------------------------------------------------------------------
        /// <summary>
        /// スキル効果値をレベルで補正。
        /// </summary>
        /// <param name="level">スキルレベル。1~。</param>
        /// <param name="type">スキル効果種類。</param>
        /// <param name="value">スキル効果値。</param>
        /// <returns>補正後のスキル効果値。</returns>
        /// <seealso cref="Test.TestSkillAbilityValueLevelCalculator.TestApplyLevelToValue(float, int, SkillDefine.SkillAbilityType, float, SkillDefine.SkillAbilityValueLevelUsage)"/>
        public static float ApplyLevelToValue(int level, SkillDefine.SkillAbilityType type, float value, SkillDefine.SkillAbilityValueLevelUsage valueLevelUsage)
        {
            float coefFloat = GetLevelCoef(level, type);
            return ApplyLevelCoefToValue(coefFloat, value, valueLevelUsage);
        }
        
        /// <summary>
        /// レベルとスキル効果種類から、補正係数取得。
        /// GALLOP用
        /// </summary>
        private static float GetLevelCoef(int level, SkillDefine.SkillAbilityType type)
        {
            var abilityLevelList = Gallop.MasterDataManager.Instance.masterSkillLevelValue.GetListWithAbilityTypeOrderByLevelAsc((int)type);
            if (null == abilityLevelList || abilityLevelList.Count == 0)
            {
                Debug.LogError("SkillAbilityTypeに対応するレベル毎効果値補正値のデータが見つかりません。type=" + type);
                return 1.0f;
            }

            var masterAbilityLevel = abilityLevelList.FirstOrDefault(a => a.Level == level);
            if (null == masterAbilityLevel)
            {
                Debug.LogError(string.Format("SkillAbilityTypeのレベルに対応するレベル毎効果値補正値のデータが見つかりません。type={0}, level={1}", type, level));
                return 1.0f;
            }

            float coefFloat = RaceUtilMath.MasterInt2Float(masterAbilityLevel.FloatAbilityValueCoef);
            return coefFloat;
        }
#endif

        /// <summary>
        /// スキル効果値にレベル補正値を適用した値を取得。
        /// </summary>
        /// <param name="coefFloat">レベル補正値。</param>
        /// <param name="value">スキル効果値。</param>
        /// <param name="valueLevelUsage">レベル補正値適用方法。</param>
        private static float ApplyLevelCoefToValue(float coefFloat, float value, SkillDefine.SkillAbilityValueLevelUsage valueLevelUsage)
        {
            switch (valueLevelUsage)
            {
                case SkillDefine.SkillAbilityValueLevelUsage.Normal: return value * coefFloat;
                case SkillDefine.SkillAbilityValueLevelUsage.Ignore: return value;
                case SkillDefine.SkillAbilityValueLevelUsage.Inverse: return value * (1.0f / coefFloat);

                default:
                    Debug.LogError("不正なvalueLevelUsage=" + valueLevelUsage);
                    return value;
            }
        }

    }
}

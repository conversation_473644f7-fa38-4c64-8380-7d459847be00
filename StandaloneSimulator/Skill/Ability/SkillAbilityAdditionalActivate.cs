#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Linq;
using System;
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// アビリティ追加発動インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ISkillAbilityAdditionalActivate
    {
        /// <summary> 現在の追加発動可能回数 </summary>
        int CurrentAdditionalActivateCount();
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：スキル追加発動無し
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityAdditionalActivateNone : ISkillAbilityAdditionalActivate
    {
        //---------------------------------------------------------------
        public SkillAbilityAdditionalActivateNone()
        {
        }

        //---------------------------------------------------------------
        public int CurrentAdditionalActivateCount()
        {
            return 0;
        }

    };

    /// <summary>
    /// スキル追加発動基底クラス
    /// </summary>
    public abstract class SkillAbilityAdditionalActivateBase : ISkillAbilityAdditionalActivate
    {
        /// <summary> 馬データ </summary>
        protected readonly IHorseRaceInfoSimulate _owner;
        /// <summary> 追加発動の上限回数 </summary>
        protected readonly int _additionalActivateMaxCount;
        /// <summary> 追加発動回数 </summary>
        protected int _additionalActivateCount = 0;

        //---------------------------------------------------------------
        public SkillAbilityAdditionalActivateBase(
            IHorseRaceInfoSimulate owner, Gallop.SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            Gallop.RaceParamDefine.SkillParam paramDefine
        )
        {
            _owner = owner;
            var activateMaxCountDataList = paramDefine.AdditionalActivateAbilityMaxCountArray?.Where(
                value => value.AdditionalActivateType == additionalActivateType
            );
            int maxCountDataNum = activateMaxCountDataList?.Count() ?? 0;
            if (maxCountDataNum <= 0)
            {
                Debug.Assert(maxCountDataNum == 1, $"{additionalActivateType}の上限値がraceParamDefineに{maxCountDataNum}個設定されています");
                return;
            }
            _additionalActivateMaxCount = activateMaxCountDataList.FirstOrDefault()?.AdditionalActivateMaxCount ?? 0;
        }

        public abstract int CurrentAdditionalActivateCount();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル追加発動：追い抜くたびにスキル効果適用
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityAdditionalActivateOrderUp : SkillAbilityAdditionalActivateBase
    {
        //---------------------------------------------------------------
        public SkillAbilityAdditionalActivateOrderUp(
            IHorseRaceInfoSimulate owner, Gallop.SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            Gallop.RaceParamDefine.SkillParam paramDefine
        ) : base(owner, additionalActivateType, paramDefine)
        {
        }

        //---------------------------------------------------------------
        public override int CurrentAdditionalActivateCount()
        {
            // 順位が上がっていない
            if (!_owner.IsCurOrderUp)
            {
                return 0;
            }

            // 上限を超えてしまったら追加発動不可
            if (_additionalActivateCount >= _additionalActivateMaxCount)
            {
                return 0;
            }

            _additionalActivateCount++;
            return 1;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル追加発動：自分がスキル発動する度にスキル効果適用
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityAdditionalActivateSelfActivateAnySkill : SkillAbilityAdditionalActivateBase
    {
        /// <summary> このスキル追加発動条件を持つスキルのID </summary>
        private readonly int _skillId;

        //---------------------------------------------------------------
        public SkillAbilityAdditionalActivateSelfActivateAnySkill(
            IHorseRaceInfoSimulate owner, int skillId, Gallop.SkillDefine.SkillAbilityAdditionalActivateType additionalActivateType,
            Gallop.RaceParamDefine.SkillParam paramDefine
        ) : base(owner, additionalActivateType, paramDefine)
        {
            _skillId = skillId;
        }

        //---------------------------------------------------------------
        public override int CurrentAdditionalActivateCount()
        {
            int activateCount = 0;
            // 残り発動回数
            int remainingActivateCount = _additionalActivateMaxCount - _additionalActivateCount;
            // 上限を超えてしまったら追加発動不可
            if (remainingActivateCount <= 0)
            {
                return activateCount;
            }

            // スキル発動していない
            var prevActivateAdvantageSkillDetailList = _owner.SkillManager.GetPrevActivateSkillDetailList();
            if (prevActivateAdvantageSkillDetailList == null || prevActivateAdvantageSkillDetailList.Count <= 0)
            {
                return activateCount;
            }

            activateCount = prevActivateAdvantageSkillDetailList.Count;
            // 自身のスキルが発動していたらその分は考慮しないので引いておく
            if (prevActivateAdvantageSkillDetailList.Any(skillDetail => skillDetail.SkillId == _skillId))
            {
                activateCount--;
            }

            activateCount = Math.Min(activateCount, remainingActivateCount);
            _additionalActivateCount += activateCount;
            // 発動可能回数
            return activateCount;
        }
    }

}
#endif

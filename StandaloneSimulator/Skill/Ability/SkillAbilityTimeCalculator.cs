#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果時間計算機インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ISkillAbilityTimeCalculator
    {
        float AbilityTime { get; }
        float AdditionalAbilityTime { get; }
        void Init();
        void UpdateAbilityTime();
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：ctorでAbilityValue固定：float：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityTimeMultiplyBaseStaticFloat : ISkillAbilityTimeCalculator
    {
        protected readonly IHorseRaceInfoSimulate _owner = null;
        protected readonly float _abilityTimeBase = 0;
        
        //---------------------------------------------------------------
        public SkillAbilityTimeMultiplyBaseStaticFloat(IHorseRaceInfoSimulate owner, float abilityTimeBase)
        {
            _owner = owner;
            _abilityTimeBase = abilityTimeBase;
            AbilityTime = _abilityTimeBase * GetMultiply(); 
        }

        //---------------------------------------------------------------
        public float AbilityTime { get; private set; }
        //---------------------------------------------------------------
        public float AdditionalAbilityTime { get { return 0; } }
        //---------------------------------------------------------------
        public void Init() { }
        //---------------------------------------------------------------
        public void UpdateAbilityTime() { }

        //---------------------------------------------------------------
        protected abstract float GetMultiply();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果時間計算機：AbilityTime取得毎に値計算：float：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityTimeMultiplyBaseDynamicFloat : ISkillAbilityTimeCalculator
    {
        protected readonly IHorseRaceInfoSimulate _owner = null;
        protected readonly float _abilityTimeBase = 0;
        
        //---------------------------------------------------------------
        public SkillAbilityTimeMultiplyBaseDynamicFloat(IHorseRaceInfoSimulate owner, float abilityTimeBase)
        {
            _owner = owner;
            _abilityTimeBase = abilityTimeBase;
        }

        //---------------------------------------------------------------
        public float AbilityTime => _abilityTimeBase * GetMultiply(); 
        //---------------------------------------------------------------
        public float AdditionalAbilityTime { get { return 0; } }
        //---------------------------------------------------------------
        public void Init() { }
        //---------------------------------------------------------------
        public void UpdateAbilityTime() { }

        //---------------------------------------------------------------
        protected abstract float GetMultiply();
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果時間計算機：直値。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityTimeDirect : ISkillAbilityTimeCalculator
    {
        //---------------------------------------------------------------
        public SkillAbilityTimeDirect(float abilityTimeBase)
        {
            AbilityTime = abilityTimeBase;
        }

        //---------------------------------------------------------------
        public float AbilityTime { get; private set; }
        //---------------------------------------------------------------
        public float AdditionalAbilityTime { get { return 0; } }
        //---------------------------------------------------------------
        public void Init() { }
        //---------------------------------------------------------------
        public void UpdateAbilityTime() { }
    };


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果時間計算機：トップとの距離差に応じた係数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityTimeMultiplyDistanceDiffTop : SkillAbilityTimeMultiplyBaseDynamicFloat
    {
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly Gallop.RaceParamDefine.SkillParam _skillParam;
        
        //---------------------------------------------------------------
        public SkillAbilityTimeMultiplyDistanceDiffTop(IHorseRaceInfoSimulate owner, IRaceHorseAccessor horseAccessor, Gallop.RaceParamDefine.SkillParam skillParam, float abilityTimeBase) : base(owner, abilityTimeBase)
        {
            _horseAccessor = horseAccessor;
            _skillParam = skillParam;
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            var topHorse = _horseAccessor.GetFirstHorseInfo();
            float distanceDiff = topHorse.GetDistance() - _owner.GetDistance();
            float coef = distanceDiff / _skillParam.AbilityTimeDiffDistanceTopCoefPerDistance; 

            float multiply = RaceUtilMath.Clamp(
                _skillParam.AbilityTimeDiffDistanceTopCoefMin + coef, 
                _skillParam.AbilityTimeDiffDistanceTopCoefMin, 
                _skillParam.AbilityTimeDiffDistanceTopCoefMax);
            return multiply;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果時間計算機：残りHpに応じた係数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityTimeMultiplyRemainHp : SkillAbilityTimeMultiplyBaseDynamicFloat
    {
        /// <summary> 対象の係数テーブル </summary>
        private readonly Gallop.RaceParamDefine.SkillParam.TimeRemainHpCoef[] targetCoefAscArray = null;
        //---------------------------------------------------------------
        public SkillAbilityTimeMultiplyRemainHp(
            IHorseRaceInfoSimulate owner,
            Gallop.RaceParamDefine.SkillParam skillParam,
            float abilityTimeBase,
            Gallop.SkillDefine.SkillAbilityTimeUsage abilityTimeUsage
        ) : base(owner,
            abilityTimeBase)
        {
            switch (abilityTimeUsage)
            {
                case Gallop.SkillDefine.SkillAbilityTimeUsage.MultiplyRemainHp1:
                    targetCoefAscArray = skillParam.TimeRemainHpCoefAscArray;
                    break;
                case Gallop.SkillDefine.SkillAbilityTimeUsage.MultiplyRemainHp2:
                    targetCoefAscArray = skillParam.TimeRemainHpCoefAscArray2;
                    break;
                default:
                    Debug.LogError($"予期せぬ効果時間タイプです。{abilityTimeUsage}");
                    break;
            }
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            if (targetCoefAscArray == null)
            {
                return 1;
            }

            float remainHp = _owner.GetHp();
            foreach (var coef in targetCoefAscArray)
            {
                if (remainHp < coef.HpThreshold)
                {
                    return coef.Multiply; 
                }
            }
            
            Debug.LogWarning($"残りHp({remainHp})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない");
            return 1;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果時間計算機：追い抜くたびに効果時間が延長
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityTimeIncrementOrderUp : ISkillAbilityTimeCalculator
    {
        private readonly IHorseRaceInfoSimulate _owner = null;
        private readonly Gallop.RaceParamDefine.SkillParam _skillParam;
        /// <summary> アビリティ時間にかける距離に応じた重み </summary>
        private readonly float _abilityTimeByDistanceRate;
        /// <summary> アビリティ効果時間を加算した回数 </summary>
        private int _incrementCount = 0;

        //---------------------------------------------------------------
        public SkillAbilityTimeIncrementOrderUp(
            IHorseRaceInfoSimulate owner, Gallop.RaceParamDefine.SkillParam skillParam, float abilityTimeBase, float abilityTimeByDistanceRate)
        {
            _owner = owner;
            _skillParam = skillParam;
            AbilityTime = abilityTimeBase;
            _abilityTimeByDistanceRate = abilityTimeByDistanceRate;
            _incrementCount = 0;
        }

        //---------------------------------------------------------------
        public float AbilityTime { get; }
        //---------------------------------------------------------------
        public float AdditionalAbilityTime { get; private set; }

        //---------------------------------------------------------------
        public void Init()
        {
            AdditionalAbilityTime = 0f;
            _incrementCount = 0;
        }

        //---------------------------------------------------------------
        public void UpdateAbilityTime() {
            // 追い抜いていないなら加算無し
            if (!_owner.IsCurOrderUp)
            {
                AdditionalAbilityTime = 0f;
                return;
            }
            // 上限回数まで加算してしまったらそれ以降は加算無し
            if (_incrementCount >= _skillParam.OrderUpAddAbilityTimeMaxCount)
            {
                AdditionalAbilityTime = 0f;
                return;
            }

            _incrementCount++;
            // 距離に応じた重みをかける
            AdditionalAbilityTime = _skillParam.OrderUpAddAbilityTime * _abilityTimeByDistanceRate;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果時間計算機：【各フェーズごとのサイドをブロックされていた最大時間に応じた係数】を乗算
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityTimeMultiplyBlockedSideMaxContinueTime : SkillAbilityTimeMultiplyBaseDynamicFloat
    {
        /// <summary> 判定対象のフェーズ </summary>
        private readonly Gallop.RaceDefine.HorsePhase _phase;
        /// <summary> 対象の係数テーブル </summary>
        private readonly Gallop.RaceParamDefine.SkillParam.BlockedSideMaxContinueTimeCoef[] _targetCoefAscArray = null;

        //---------------------------------------------------------------
        public SkillAbilityTimeMultiplyBlockedSideMaxContinueTime(
            IHorseRaceInfoSimulate owner,
            Gallop.RaceParamDefine.SkillParam skillParam,
            float abilityTimeBase,
            Gallop.SkillDefine.SkillAbilityTimeUsage abilityTimeUsage
        ) : base(owner,
            abilityTimeBase)
        {
            switch (abilityTimeUsage)
            {
                case Gallop.SkillDefine.SkillAbilityTimeUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1:
                    _phase = Gallop.RaceDefine.HorsePhase.MiddleRun;
                    _targetCoefAscArray = skillParam.BlockedSideMaxContinueTimePhaseMiddleRunAbilityTimeCoefAscArray1;
                    break;
                case Gallop.SkillDefine.SkillAbilityTimeUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2:
                    _phase = Gallop.RaceDefine.HorsePhase.MiddleRun;
                    _targetCoefAscArray = skillParam.BlockedSideMaxContinueTimePhaseMiddleRunAbilityTimeCoefAscArray2;
                    break;
                default:
                    Debug.LogError($"予期せぬ効果時間タイプです。{abilityTimeUsage}");
                    break;
            }
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            if (_targetCoefAscArray == null)
            {
                return 1;
            }

            if (!_owner.BlockSideMaxContinueTimeByPhase.TryGetValue(_phase, out var blockSideMaxContinueTime))
            {
                return 1;
            }

            foreach (var coef in _targetCoefAscArray)
            {
                if (blockSideMaxContinueTime < coef.BlockedSideMaxContinueTimeThreshold)
                {
                    return coef.Multiply;
                }
            }

            Debug.LogWarning(
                $"サイドをブロックされていた最大時間({blockSideMaxContinueTime}, フェーズ({_phase.ToString()})に対応する係数がRaceParamDefineに定義されていないため" +
                $"倍率が取得できない"
            );
            return 1;
        }
    }
}
#endif

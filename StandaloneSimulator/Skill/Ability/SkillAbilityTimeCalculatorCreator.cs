#if STANDALONE_SIMULATOR || UNITY_EDITOR
using SkillDefine = Gallop.SkillDefine;

namespace StandaloneSimulator
{
    public static class SkillAbilityTimeCalculatorCreator
    {
        //---------------------------------------------------------------
        /// <summary>
        /// スキル効果時間計算機の生成ファクトリ。
        /// </summary>
        //---------------------------------------------------------------
        public static ISkillAbilityTimeCalculator CreateCalculator( 
            IHorseRaceInfoSimulate owner, 
            SkillDefine.SkillAbilityTimeUsage usage, 
            float abilityTimeBase,
            IRaceHorseAccessor horseAccessor,
            Gallop.RaceParamDefine.SkillParam paramDefine,
            float abilityTimeByDistanceRate)
        {
            switch( usage )
            {
                case SkillDefine.SkillAbilityTimeUsage.Direct:
                    return new SkillAbilityTimeDirect(abilityTimeBase); 
                case SkillDefine.SkillAbilityTimeUsage.MultiplyDistanceDiffTop:
                    return new SkillAbilityTimeMultiplyDistanceDiffTop(owner, horseAccessor, paramDefine, abilityTimeBase);
                case SkillDefine.SkillAbilityTimeUsage.MultiplyRemainHp1:
                case SkillDefine.SkillAbilityTimeUsage.MultiplyRemainHp2:
                    return new SkillAbilityTimeMultiplyRemainHp(owner, paramDefine, abilityTimeBase, usage);
                case SkillDefine.SkillAbilityTimeUsage.IncrementOrderUp:
                    return new SkillAbilityTimeIncrementOrderUp(owner, paramDefine, abilityTimeBase, abilityTimeByDistanceRate);
                case SkillDefine.SkillAbilityTimeUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun1:
                case SkillDefine.SkillAbilityTimeUsage.MultiplyBlockedSideMaxContinueTimePhaseMiddleRun2:
                    // 同じクラスを使用するが中で対象の係数テーブルをabilityValueUsageのタイプによって変更している
                    return new SkillAbilityTimeMultiplyBlockedSideMaxContinueTime(owner, paramDefine, abilityTimeBase, usage);
                // 不正値。
                default:
                    Debug.Assert( false, "予期しないスキル効果時間適用種類です。:" + usage );
                    return new SkillAbilityTimeDirect(abilityTimeBase); 
            }
        }
    }
}

#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 馬１体のスキル管理。
    /// </summary>
    /// <remarks>馬１体が所有するスキルを管理する。</remarks>
    //-------------------------------------------------------------------
    public sealed class SkillManagerSimulate
    {
        private readonly IHorseRaceInfoSimulate _ownerInfo = null;
        private List<SkillBase> _skills = null;

        private SkillBase[] _skillArray = null;

        private readonly List<int> _usedSkillIdList = new List<int>(10);
        /// <summary> 各フレーム毎の発動スキルリスト </summary>
        private readonly Dictionary<int, List<ISkillDetail>> _activatedSkillBaseDict = null;
        /// <summary> 発動したスキルリスト(毎フレーム各スキルのUpdateで更新されます) </summary>
        private readonly List<ISkillDetail> _prevActivateSkillDetailList = null;
        private List<ISkillDetail> _activateSkills = new List<ISkillDetail>( 5 );
        /// <summary> ランダムスキル強制発動対象のスキルIDリスト </summary>
        private List<int> _reservedRandomActivateSkillIdList = new List<int>();
        /// <summary> 特定のスキル強制発動対象のスキルIDリスト </summary>
        private List<int> _reservedSpecificActivateSkillIdList = new List<int>();

#if UNITY_EDITOR && CYG_DEBUG
        /// <summary>
        /// ログ出力用の発動スキル情報（スキル効果の情報含む）
        /// </summary>
        public struct DbgActivatedSkillInfo
        {
            public readonly int SkillId;
            public readonly int SkillDetailIndex;

            public DbgActivatedSkillInfo(int skillId, int skillDetailIndex)
            {
                SkillId = skillId;
                SkillDetailIndex = skillDetailIndex;
            }
        }

        //---------------------------------------------------------------
        public readonly List<DbgActivatedSkillInfo> DbgActivatedSkillInfoList = new List<DbgActivatedSkillInfo>(10);
        public void AddActivatedSkillInfo(int skillId, int skillDetailIndex)
        {
            var info = new DbgActivatedSkillInfo(skillId, skillDetailIndex);
            if (DbgActivatedSkillInfoList.Contains(info))
            {
                return;
            }
            DbgActivatedSkillInfoList.Add(info);
        }
#endif
        
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        //---------------------------------------------------------------
        public SkillManagerSimulate( IHorseRaceInfoSimulate owner )
        {
            _ownerInfo = owner;
            _skills = new List<SkillBase>();
            _prevActivateSkillDetailList = new List<ISkillDetail>();
            _activatedSkillBaseDict = new Dictionary<int, List<ISkillDetail>>();
        }

        //---------------------------------------------------------------
        private ISkillTriggerCreator _CreateTriggerCreate()
        {
            return new SkillTriggerCreatorSimulate();
        }

        //---------------------------------------------------------------
        public void AddSkill( ref SkillBuildInfo buildInfo )
        {
            // 同じスキルは登録されないはず。
            if( GetSkill(buildInfo.masterSkill.Id) != null )
            {
                Debug.LogError( string.Format( "同じスキルを複数所持しようとしています。horseIndex={0} skillId={1}", _ownerInfo.HorseIndex, buildInfo.masterSkill.Id ) );
                return;
            }

            var triggerCreator = _CreateTriggerCreate();
            var newSkill = new SkillBase( ref buildInfo, triggerCreator );
            _skills.Add(newSkill);
        }

        //---------------------------------------------------------------
        public SkillBase GetSkill( int skillID )
        {
            if( null == _skillArray )
            {
                return null;
            }
            for( int i = 0; i < _skillArray.Length; ++i )
            {
                if( _skillArray[ i ].SkillMasterId == skillID )
                {
                    return _skillArray[ i ];
                }
            }
            return null;
        }

        //---------------------------------------------------------------
        public SkillBase[] GetSkills() { return _skillArray; }

        //---------------------------------------------------------------
        public void CreateSkillArray()
        {
            _skillArray = _skills.ToArray();
        }

        //---------------------------------------------------------------
        public void ClearSkills()
        {
            for (int i = 0; i < _skills.Count; i++)
            {
                _skills[i].Stop();
            }
            _skills.Clear();
//            StopEffect();
        }

        //---------------------------------------------------------------
        public void AddCurrentActiveSkill(ISkillDetail detail)
        {
            if( _activateSkills.Contains( detail ) )
            {
                Debug.LogError( string.Format( "発動中のスキルを再度発動登録しようとしています。キャラ={0}番 スキルId={1}", detail.OwnerHorse.HorseIndex+1, detail.SkillId ) );
                return;
            }
            _activateSkills.Add( detail );
        }

        //---------------------------------------------------------------
        public void RemoveCurrentActiveSkill(ISkillDetail detail)
        {
            _activateSkills.Remove( detail );
        }
        
        //---------------------------------------------------------------
        public List<ISkillDetail> GetCurrentActiveSkill()
        {
            return _activateSkills; 
        }

        //---------------------------------------------------------------
        public void AddUsedSkillId(int skillId)
        {
            if(_usedSkillIdList.Contains(skillId ) )
            {
                return;
            }
            _usedSkillIdList.Add(skillId );
        }

        //---------------------------------------------------------------
        public List<int> GetUsedSkillIdList()
        {
            return _usedSkillIdList;
        }

        //---------------------------------------------------------------
        public void AddPrevActivateSkillDetail(ISkillDetail skillDetail)
        {
            if (!_prevActivateSkillDetailList.Contains(skillDetail))
            {
                _prevActivateSkillDetailList.Add(skillDetail);
            }
        }

        //---------------------------------------------------------------
        public void RemovePrevActivateSkillDetail(ISkillDetail skillDetail)
        {
            if (_prevActivateSkillDetailList.Contains(skillDetail))
            {
                _prevActivateSkillDetailList.Remove(skillDetail);
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 1つ前のフレームで発動したスキルリストを取得する
        /// </summary>
        /// <remarks>
        /// 他のキャラから参照すると馬番号の順番(スキル更新の順番)によっては結果が違ったりしてしまうので
        /// 他のキャラから参照するときはIRaceHorseAccessor::GetPrevActivateSkillHorseDictから取得するようにしてください
        /// </remarks>
        public IReadOnlyList<ISkillDetail> GetPrevActivateSkillDetailList()
        {
            return _prevActivateSkillDetailList;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 特定スキル発動として発動予約したいスキルIDリストの初期化
        /// </summary>
        public void ClearReservedSpecificActivateSkillList()
        {
            _reservedSpecificActivateSkillIdList.Clear();
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 特定スキル発動として発動予約したいスキルIDを登録する
        /// </summary>
        /// <param name="skillIdArray"> 発動予約したいスキルIDのリスト </param>
        public void AddReservedSpecificActivateSkill(int[] skillIdArray)
        {
            for (int i = 0; i < skillIdArray.Length; i++)
            {
                if (!_reservedSpecificActivateSkillIdList.Contains(skillIdArray[i]))
                {
                    _reservedSpecificActivateSkillIdList.Add(skillIdArray[i]);
                }
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 指定フレームでの発動スキルを登録
        /// </summary>
        /// <param name="frame"></param>
        /// <param name="skillDetail"></param>
        public void AddActivateSkillDetail(int frame, ISkillDetail skillDetail)
        {
            if (!_activatedSkillBaseDict.ContainsKey(frame))
            {
                _activatedSkillBaseDict.Add(frame, new List<ISkillDetail>());
            }

            _activatedSkillBaseDict[frame].Add(skillDetail);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 指定フレームでの発動スキルリストを取得
        /// </summary>
        /// <param name="frame"></param>
        /// <returns></returns>
        public IReadOnlyList<ISkillDetail> GetActivateSkillDetailList(int frame)
        {
            _activatedSkillBaseDict.TryGetValue(frame, out var activateSkillDetailList);
            return activateSkillDetailList;
        }

        public void UpdateFirstGroup(float deltaTime)
        {
            if (null != _skillArray)
            {
                for (int i = 0; i < _skillArray.Length; i++)
                {
                    if (_skillArray[i].SkillMaster.Priority != (int) Gallop.SkillDefine.SkillPriority.FirstGroup)
                    {
                        continue;
                    }

                    _skillArray[i].Update(deltaTime);
                }
            }
        }

        /// <summary>
        /// 1グループ目スキル更新の後に実行される処理
        /// </summary>
        public void PostUpdateSkillFirstGroup()
        {
        }

        //---------------------------------------------------------------
        public void UpdateSecondGroup(float deltaTime)
        {
            if (null != _skillArray)
            {
                for (int i = 0; i < _skillArray.Length; i++)
                {
                    if (_skillArray[i].SkillMaster.Priority != (int) Gallop.SkillDefine.SkillPriority.Normal)
                    {
                        continue;
                    }

                    _skillArray[i].Update(deltaTime);
                }
            }

            // 2グループ目の全てのスキル更新が終わった後に実行
            // ランダム発動スキルの更新
            UpdateReservedRandomActivateSkillIdList();
        }

        /// <summary>
        /// ランダム発動対象のスキルかどうか
        /// </summary>
        /// <remarks>
        /// ランダム発動対象のスキルかどうかは下記の通り
        /// 1. 対象のレアリティである
        /// 2. スキル効果1が永続スキルではない(ランダム発動対象がスキル効果1のため)
        /// 3. クールダウンタイムが0秒以下
        /// 4. 汎用スキル(通常の育成で獲得できるスキル)である
        /// 5. 現在発動中のスキルではない(スキルが発動したフレームだとクールダウンタイムの更新が行われないためこのチェックも入れている)
        /// </remarks>
        /// <param name="skill"> 対象のスキル </param>
        /// <param name="targetRarityList"> 発動対象のレアリティリスト </param>
        /// <returns></returns>
        private bool IsRandomActivateTargetSkill(SkillBase skill, int[] targetRarityList)
        {
            return targetRarityList.Contains(skill.SkillMaster.Rarity) &&
                   skill.SkillMaster.FloatAbilityTime1 != Gallop.SkillDefine.SKILL_TIME_ALWAYS_ACTIVATE &&
                   skill.CoolDownTime <= 0f &&
                   skill.SkillMaster.IsGeneralSkill == 1 &&
                   GetCurrentActiveSkill().All(currentActiveSkill => currentActiveSkill.SkillId != skill.SkillMasterId);
        }
        /// <summary>
        /// ランダム発動スキルの更新
        /// </summary>
        private void UpdateReservedRandomActivateSkillIdList()
        {
            _reservedRandomActivateSkillIdList.Clear();
            var playerSkills = GetSkills();
            if (playerSkills == null)
            {
                return;
            }

            // ランダム発動スキル選定(レアスキル)
            int activateRareSkillCount = (int)_ownerInfo.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.ActivateRandomRareSkill, 0);
            if (activateRareSkillCount > 0)
            {
                // ランダム発動対象のレアスキルを選定
                var playerRareSkillList = playerSkills
                    .Where(skill => IsRandomActivateTargetSkill(skill, Gallop.SkillDefine.RARE_SKILL_RARITY_ARRAY))
                    .ToList();

                int playerRareSkillListCount = playerRareSkillList.Count;
                if (playerRareSkillListCount > 0)
                {
                    // ランダム発動させたいスキルの個数と現在発動可能なスキルの個数で少ないほうを基準とする
                    for (int i = 0, cnt = System.Math.Min(playerRareSkillListCount, activateRareSkillCount); i < cnt; ++i)
                    {
                        // ここでは都度リストの個数を反映させたいので直接Count参照
                        int index = RaceManagerSimulate.Instance.GetRandom(playerRareSkillList.Count);
                        int activateSkillId = playerRareSkillList[index].SkillMasterId;
                        playerRareSkillList.RemoveAt(index);
                        _reservedRandomActivateSkillIdList.Add(activateSkillId);
                    }
                }
            }

            // ランダム発動スキル選定(全てのスキル)
            int activateNormalAndRareSkillCount = (int) _ownerInfo.ApplyModifier(Gallop.SkillDefine.SkillModifierParam.ActivateRandomNormalAndRareSkill, 0);
            if (activateNormalAndRareSkillCount > 0)
            {
                // ランダム発動対象のノーマルスキル、レアスキルを選定
                // 上で既に選定されているレアスキルは抽選から外す
                var playerNormalAndRareSkillList = playerSkills
                    .Where(skill =>
                        IsRandomActivateTargetSkill(skill, Gallop.SkillDefine.NORMAL_AND_RARE_SKILL_RARITY_ARRAY) &&
                        !_reservedRandomActivateSkillIdList.Contains(skill.SkillMasterId)
                    )
                    .ToList();
                int playerNormalAndRareSkillCount = playerNormalAndRareSkillList.Count;
                if (playerNormalAndRareSkillCount > 0)
                {
                    // ランダム発動させたいスキルの個数と現在発動可能なスキルの個数で少ないほうを基準とする
                    for (int i = 0, cnt = System.Math.Min(playerNormalAndRareSkillCount, activateNormalAndRareSkillCount); i < cnt; ++i)
                    {
                        // ここでは都度リストの個数を反映させたいので直接Count参照
                        int index = RaceManagerSimulate.Instance.GetRandom(playerNormalAndRareSkillList.Count);
                        int activateSkillId = playerNormalAndRareSkillList[index].SkillMasterId;
                        playerNormalAndRareSkillList.RemoveAt(index);
                        _reservedRandomActivateSkillIdList.Add(activateSkillId);
                    }
                }
            }
        }

        /// <summary>
        /// 特定スキル強制発動の対象スキルリスト取得
        /// </summary>
        public int[] GetReserveSpecificActivateSkillIdArray(Gallop.SkillDefine.SkillAbilityType targetAbilityType)
        {
            string errorMessage = $"スキル効果{(int)Gallop.SkillDefine.SkillAbilityType.ActivateSpecificSkill}に対する発動スキルIDリストが定義されていません";
            Gallop.SkillDefine.SkillModifierParam GetSkillModifierParamType(Gallop.SkillDefine.SkillAbilityType abilityType)
            {
                switch (abilityType)
                {
                    case Gallop.SkillDefine.SkillAbilityType.ActivateSpecificSkill:
                        return Gallop.SkillDefine.SkillModifierParam.ActivateSpecificSkill;
                    default:
                        return Gallop.SkillDefine.SkillModifierParam.None;
                }
            }

            // 特定スキル発動のスキル効果かチェック
            var targetSkillModifierParamType = GetSkillModifierParamType(targetAbilityType);
            if (targetSkillModifierParamType == Gallop.SkillDefine.SkillModifierParam.None)
            {
                return null;
            }

            // 特定スキル強制発動のスキルは発動されているかチェック
            bool isActivateSpecificSkill = _ownerInfo.ApplyModifier(targetSkillModifierParamType, false);
            if (!isActivateSpecificSkill)
            {
                return null;
            }

            // ParamDefineに特定スキル発動の対象スキルIDリストが登録されているかチェック
            var activateSpecificSkillArray = _ownerInfo.ParamDefine.Skill.ActivateSpecificSkillArray;
            if (activateSpecificSkillArray == null || activateSpecificSkillArray.Length <= 0)
            {
                Debug.LogError(errorMessage);
                return null;
            }

            int[] specificActivateSkillIdArray = null;
            // Dictionaryにキャッシュした方が良いがキャッシュを持たせるちょうど良い場所がないのと1レースに数回しか実行されない処理なので検索して処理する
            for (int i = 0; i < activateSpecificSkillArray.Length; i++)
            {
                if (activateSpecificSkillArray[i].AbilityType == (int)targetAbilityType)
                {
                    specificActivateSkillIdArray = activateSpecificSkillArray[i].ActivateSkillIdIntArray;
                    break;
                }
            }

            if (specificActivateSkillIdArray == null)
            {
                Debug.LogError(errorMessage);
                return null;
            }

            return specificActivateSkillIdArray;
        }

        //---------------------------------------------------------------
        public void LotActivateSkill()
        {
            for (int i = 0; i < _skillArray.Length; i++)
            {
                _skillArray[i].LotActivate();
            }
        }

        //---------------------------------------------------------------
        public void CheckSkillTriggerAndActivateFirstGroup()
        {
            for (int i = 0; i < _skillArray.Length; i++)
            {
                if (_skillArray[i].SkillMaster.Priority != (int) Gallop.SkillDefine.SkillPriority.FirstGroup)
                {
                    continue;
                }

                _skillArray[i].CheckTriggerAndActivate();
            }
        }

        //---------------------------------------------------------------
        public void CheckSkillTriggerAndActivateSecondGroup()
        {
            for (int i = 0; i < _skillArray.Length; i++)
            {
                if (_skillArray[i].SkillMaster.Priority != (int) Gallop.SkillDefine.SkillPriority.Normal)
                {
                    continue;
                }

                _skillArray[i].CheckTriggerAndActivate();
            }
        }

        /// <summary>
        /// 指定のスキルIDがランダム発動スキルによって発動予約されているかどうか
        /// </summary>
        /// <param name="skillId"> 対象のスキルID </param>
        /// <returns></returns>
        public bool IsReservedRandomActivateSkill(int skillId)
        {
            return _reservedRandomActivateSkillIdList != null && _reservedRandomActivateSkillIdList.Contains(skillId);
        }

        /// <summary>
        /// 対象のスキルIDが特定スキル発動によって発動予約されているかどうか
        /// </summary>
        /// <param name="skillId"> 対象のスキルID </param>
        /// <returns></returns>
        public bool IsReservedSpecificActivateSkill(int skillId)
        {
            return _reservedSpecificActivateSkillIdList != null && _reservedSpecificActivateSkillIdList.Contains(skillId);
        }

    #if CYG_DEBUG

        private List<ActivatedSkillFrameInfo> _dbgActivatedSkills = new List<ActivatedSkillFrameInfo>();

        //---------------------------------------------------------------
        public void DbgAddActivatedSkills( int skillId, float time, int skillDetailIndex)
        {
            _dbgActivatedSkills.Add( new ActivatedSkillFrameInfo() { skillId = skillId, frameTime = time, skillDetailIndex = skillDetailIndex,} );
        }
        
        //---------------------------------------------------------------
        public List<ActivatedSkillFrameInfo> DbgGetActivatedSkills()
        {
            return _dbgActivatedSkills;
        }
    #endif
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
namespace StandaloneSimulator
{
    public class ActivatedSkillFrameInfo
    {
        public int skillId = 0;
        public float frameTime = 0;
        public int skillDetailIndex = 0;
        public Gallop.SkillDefine.ActivateType activateType = Gallop.SkillDefine.ActivateType.Null;
    };
}
#endif // CYG_DEBUG


using System.Collections;
using System.Collections.Generic;
#if CYG_DEBUG
using System;
#endif
 
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラのパラメータ変動オブジェクトインターフェース。
    /// </summary>
    /// <typeparam name="T">効果量の型。</typeparam>
    //-------------------------------------------------------------------
    public interface ISkillParamModifier
    {
        /// <summary> 効果値 </summary>
        float BaseValue { get; }
        /// <summary> デバフかどうか </summary>
        /// <remarks> RaceUtil::IsDebuffで計算される結果 </remarks>
        bool IsDebuff { get; }

        /// <summary> 実際に反映される効果値を計算 </summary>
        float CalcApplyValue();
        /// <summary>
        /// 効果量をvalueに適用した結果を戻り値で返す。
        /// </summary>
        float Apply(float value);
        bool Apply(bool value);
        
        /// <summary>
        /// 更新。
        /// </summary>
        /// <returns>効果時間が終了したらtrue。</returns>
        bool Update(float deltaTime);
        
        /// <summary>
        /// 効果時間加算
        /// </summary>
        /// <param name="additionalAbilityTime"> 加算したい効果時間 </param>
        void AddAbilityTime(float additionalAbilityTime);
        
    #if CYG_DEBUG
        SkillParamModifierBase.ModifyByType ModifyBy { get; }
        int DbgHorseIndex { get; }
        int DbgSkillId { get; }
        int DbgSkillLevel { get; }
        int DbgItemId { get; }
        /// <summary> 有効かどうか </summary>
        bool IsValid { get; }
        /// <summary> 他者が発動したスキルかどうかの情報 </summary>
        bool DbgIsActivateOthers { get; }
        void AppendDbgSkill(int horseIndex, int skillId, int skillLevel, bool isValid);
        void AppendDbgItem(int horseIndex, int itemId);
        void SetSkillParamFluctuate(SkillParamFluctuate skillParamFluctuate);
        /// <summary> このSkillParamModifierがどのタイプか返す </summary>
        /// <returns></returns>
        Gallop.SkillDefine.SkillParamModifierType SkillParamModifierType { get; }
        /// <summary>
        /// ディープコピー
        /// </summary>
        /// <returns></returns>
        ISkillParamModifier DeepCopy();
    #endif
    }
    
#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラのパラメータ変動オブジェクト：基底。
    /// </summary>
    //-------------------------------------------------------------------
#if CYG_DEBUG && UNITY_EDITOR
    // デバッグデータ周りでディープコピーしたいのでデバッグ環境下でのみSerializable属性付与
    [Serializable]
#endif
    public abstract class SkillParamModifierBase : ISkillParamModifier
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        public const float TIME_INFINITE = -1;
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private float _time;
        private bool _isOneTime;
        private readonly bool _isTimeInfinite;
        protected readonly float _value;
        /// <summary> SkillModifierParamの種類 </summary>
        private readonly Gallop.SkillDefine.SkillModifierParam _skillModifierParam;
        /// <summary> デバフかどうか </summary>
        private readonly bool _isDebuff;
        /// <summary> 自身が発動したスキルかどうか </summary>
        private readonly bool _isActivateOthers;
        /// <summary> キャラのスキルによる効果値を変動させる </summary>
        private SkillParamFluctuate _skillParamFluctuate;
    #if CYG_DEBUG
        public enum ModifyByType
        {
            Skill,
            Item,
        }

        public ModifyByType ModifyBy { get; private set; }
        public int DbgHorseIndex { get; private set; }
        public int DbgSkillId { get; private set; }
        public int DbgSkillLevel { get; private set; }
        public int DbgItemId { get; private set; }
        public bool IsValid { get; private set; }
        public bool DbgIsActivateOthers => _isActivateOthers;
        public void AppendDbgSkill(int horseIndex, int skillId, int skillLevel, bool isValid)
        {
            DbgHorseIndex = horseIndex;
            DbgSkillId = skillId;
            DbgSkillLevel = skillLevel;
            ModifyBy = ModifyByType.Skill;
            IsValid = isValid;
        }

        public void AppendDbgItem(int horseIndex, int itemId)
        {
            DbgHorseIndex = horseIndex;
            DbgItemId = itemId;
            ModifyBy = ModifyByType.Item;
        }

        public void SetSkillParamFluctuate(SkillParamFluctuate skillParamFluctuate)
        {
            _skillParamFluctuate = skillParamFluctuate;
        }
        public abstract Gallop.SkillDefine.SkillParamModifierType SkillParamModifierType { get; }
        protected SkillParamModifierBase() { }

        public abstract ISkillParamModifier DeepCopy();
        /// <summary> ディープコピー </summary>
        /// <remarks>
        /// 試験的な実装になっているので現状はデバッグ周りでのみ使用を推奨。
        /// 恐らく、コピーしたいクラスが別クラスなどの参照型のメンバ変数等を持っている場合はコピーされないと思います。
        /// 使用したくなったら追加実装の必要あり。
        /// あとそもそもReflectionの処理は遅いらしいです。
        /// </remarks>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        protected T DeepCopy<T>() where T : class
        {
        #if STANDALONE_SIMULATOR
            // .NETではBinaryFormatterが使えないので別方法でディープコピー
            // 本当はJsonSerializerを使ってコピーしたかったがコンバータをそれぞれのクラスでがっつり実装したりと汎用性が低かったため下記のようにコピーしている

            // メンバ変数やプロパティを検索する時の条件
            // 派生クラス、基底クラスを含めたすべての要素が対象
            const System.Reflection.BindingFlags SEARCH_ELEMENT_FLAGS =
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Public;

            // インスタンスを作成
            var instance = Activator.CreateInstance(typeof(T), true) as T;
            var type = this.GetType();

            // 継承先のメンバ変数などもコピーしたいので基底クラスにたどり着くまで循環させる
            while (type.BaseType != null)
            {
                // プロパティのコピー
                var properties = type.GetProperties(SEARCH_ELEMENT_FLAGS);
                foreach (var property in properties)
                {
                    // Setterが定義されているプロパティのみコピーする
                    if (property.CanWrite)
                    {
                        property.SetValue(instance, property.GetValue(this));
                    }
                }

                // メンバ変数のコピー
                var fields = type.GetFields(SEARCH_ELEMENT_FLAGS);
                foreach (var field in fields)
                {
                    field.SetValue(instance, field.GetValue(this));
                }
                type = type.BaseType;
            }

            return instance;
        #else
            // クライアントシミュレートであればBinaryFormatter使えるのでこっちでコピー
            using (var memoryStream = new System.IO.MemoryStream())
            {
                var binaryFormatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                binaryFormatter.Serialize(memoryStream, this);
                memoryStream.Seek(0, System.IO.SeekOrigin.Begin);
                return (T) binaryFormatter.Deserialize(memoryStream);
            }
        #endif
        }
    #endif
        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。継続時間指定。
        /// </summary>
        /// <param name="time">継続時間。</param>
        protected SkillParamModifierBase(
            float value, float time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers)
        {
            _value = value;
            _time = time;
            _isTimeInfinite = time < 0;
            _skillModifierParam = skillModifierParam;
            _skillParamFluctuate = skillParamFluctuate;
            _isDebuff = RaceUtil.IsDebuff(skillModifierParam, _value);
            _isActivateOthers = isActivateOthers;
        }
        protected SkillParamModifierBase(bool value, float time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers)
        {
            _value = Bool2Float(value);
            _time = time;
            _isTimeInfinite = time < 0;
            _skillModifierParam = skillModifierParam;
            _isDebuff = RaceUtil.IsDebuff(skillModifierParam, _value);
            _isActivateOthers = isActivateOthers;
        }
        
        /// <summary>
        /// コンストラクタ。次のUpdateで必ず効果終了する。
        /// </summary>
        protected SkillParamModifierBase(
            float value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers
        )
        {
            _value = value;
            _isOneTime = true;
            _skillModifierParam = skillModifierParam;
            _skillParamFluctuate = skillParamFluctuate;
            _isDebuff = RaceUtil.IsDebuff(skillModifierParam, _value);
            _isActivateOthers = isActivateOthers;
        }
        protected SkillParamModifierBase(bool value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers)
        {
            _value = Bool2Float(value);
            _isOneTime = true;
            _skillModifierParam = skillModifierParam;
            _isDebuff = RaceUtil.IsDebuff(skillModifierParam, _value);
            _isActivateOthers = isActivateOthers;
        }

        public float BaseValue => _value;
        public bool IsDebuff => _isDebuff;
        public abstract float Apply(float value);
        public abstract bool Apply(bool value);

        public float CalcApplyValue()
        {
            var skillParamValue = _value;
            if (_skillParamFluctuate != null)
            {
                skillParamValue = _skillParamFluctuate.ApplyValue(skillParamValue, _skillModifierParam, _isDebuff, _isActivateOthers);
            }

            return skillParamValue;
        }

        public bool Update(float deltaTime)
        {
            // 時間で終わらせない。
            if (_isTimeInfinite)
            {
                return false;
            }
            
            // 時間ではなく更新時に必ず終了させる。
            if (_isOneTime)
            {
                return true;
            }
            
            // 指定時間経過したら終了。
            _time -= deltaTime;
            if (_time < 0)
            {
                _time = 0;
            }
            return _time <= 0;
        }

        public void AddAbilityTime(float additionalAbilityTime)
        {
            // 時間更新必要なし
            if (_isTimeInfinite || _isOneTime)
            {
                return;
            }

            _time += additionalAbilityTime;
        }

        protected bool Float2Bool(float value)
        {
            return !RaceUtilMath.Approximately(_value, 0);
        }

        protected float Bool2Float(bool value)
        {
            return value ? 1 : 0;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラのパラメータ変動オブジェクト：加算。
    /// </summary>
    //-------------------------------------------------------------------
#if CYG_DEBUG && UNITY_EDITOR
    // デバッグデータ周りでディープコピーしたいのでデバッグ環境下でのみSerializable属性付与
    [Serializable]
#endif
    public class SkillParamModifierAdd : SkillParamModifierBase
    {
    #if CYG_DEBUG
        public override Gallop.SkillDefine.SkillParamModifierType SkillParamModifierType => Gallop.SkillDefine.SkillParamModifierType.Add;
        public override ISkillParamModifier DeepCopy()
        {
            return base.DeepCopy<SkillParamModifierAdd>();
        }

        public SkillParamModifierAdd() : base()
        {
        }
    #endif
        public SkillParamModifierAdd(
            float value, float time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers
        ) : base(value, time, skillModifierParam, skillParamFluctuate, isActivateOthers)
        {
        }

        public SkillParamModifierAdd(
            float value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers
        ) : base(value, skillModifierParam, skillParamFluctuate, isActivateOthers)
        {
        }

        public override float Apply(float value)
        {
            return value + CalcApplyValue();
        }
        public override bool Apply(bool value)
        {
            Debug.LogWarning($"boolでSkillParamModifierAddは想定していない?");
            return value;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// キャラのパラメータ変動オブジェクト：上書き。
    /// </summary>
    //-------------------------------------------------------------------
#if CYG_DEBUG && UNITY_EDITOR
    // デバッグデータ周りでディープコピーしたいのでデバッグ環境下でのみSerializable属性付与
    [Serializable]
#endif
    public class SkillParamModifierSet : SkillParamModifierBase
    {
    #if CYG_DEBUG
        public override Gallop.SkillDefine.SkillParamModifierType SkillParamModifierType => Gallop.SkillDefine.SkillParamModifierType.Set;
        public override ISkillParamModifier DeepCopy()
        {
            return base.DeepCopy<SkillParamModifierSet>();
        }
        public SkillParamModifierSet() : base()
        {
        }
    #endif
        public SkillParamModifierSet(
            float value, float time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers
        ) : base(value, time, skillModifierParam, skillParamFluctuate, isActivateOthers)
        {
        }

        public SkillParamModifierSet(
            float value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers
        ) : base(value, skillModifierParam, skillParamFluctuate, isActivateOthers)
        {
        }

        public SkillParamModifierSet(
            bool value, float time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        ) : base(value, time, skillModifierParam, isActivateOthers)
        {
        }

        public SkillParamModifierSet(bool value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers)
            : base(value, skillModifierParam, isActivateOthers)
        {
        }

        public override float Apply(float value)
        {
            return CalcApplyValue();
        }
        
        public override bool Apply(bool value)
        {
            return Float2Bool(_value);
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// キャラのパラメータ変動オブジェクト：乗算。
    /// </summary>
    //-------------------------------------------------------------------
#if CYG_DEBUG && UNITY_EDITOR
    // デバッグデータ周りでディープコピーしたいのでデバッグ環境下でのみSerializable属性付与
    [Serializable]
#endif
    public class SkillParamModifierMultiply : SkillParamModifierBase
    {
    #if CYG_DEBUG
        public override Gallop.SkillDefine.SkillParamModifierType SkillParamModifierType => Gallop.SkillDefine.SkillParamModifierType.Multiply;
        public override ISkillParamModifier DeepCopy()
        {
            return base.DeepCopy<SkillParamModifierMultiply>();
        }
        public SkillParamModifierMultiply() : base()
        {
        }
    #endif
        public SkillParamModifierMultiply(
            float value, float time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers)
            : base(value, time, skillModifierParam, skillParamFluctuate, isActivateOthers)
        {
        }
        public SkillParamModifierMultiply(
            float value, Gallop.SkillDefine.SkillModifierParam skillModifierParam, SkillParamFluctuate skillParamFluctuate, bool isActivateOthers)
            :base(value, skillModifierParam, skillParamFluctuate, isActivateOthers)
        {
        }

        public override float Apply(float value)
        {
            if (RaceUtilMath.Approximately(value, 0f))
            {
                Debug.LogWarning($"SkillParamModifierMultiplyのApply時に元値が0fで設定されています。確認してください");
            }
            return value * CalcApplyValue();
        }
        public override bool Apply(bool value)
        {
            Debug.LogWarning($"boolでSkillParamModifierMultiplyは想定していない?");
            return value;
        }
    }
#endif
}

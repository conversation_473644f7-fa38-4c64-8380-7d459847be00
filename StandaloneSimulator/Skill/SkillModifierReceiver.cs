using System;
using System.Collections.Generic;
using System.Linq;

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果受信機。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillModifierReceiver
    {
        private Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>> _modifier = new Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>>();

        /// <summary> 効果時間に依らず1度だけ発動するスキルの受信機 </summary>
        /// <remarks> 現状は自然減速のようにスキル発動後一度だけ発動するスキルで使用しています </remarks>
        private Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>> _oneTimeSkillModifier = new Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>>();
        
        /// <summary> キャラのスキルによる効果値を変動させる </summary>
        private readonly SkillParamFluctuate _skillParamFluctuate;
#if CYG_DEBUG
        /// <summary> スキル効果対象になったけどデバフ無効化でキャンセルしたときに表示するようのスキル効果受信機 </summary>
        private Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>> _dbgCancelledModifier =
            new Dictionary<Gallop.SkillDefine.SkillModifierParam, List<ISkillParamModifier>>();
        public SkillParamFluctuate DbgSkillParamFluctuate => _skillParamFluctuate;
#endif

        public SkillModifierReceiver()
        {
            _skillParamFluctuate = new SkillParamFluctuate();
        }

        /// <summary>
        /// スキル効果量の継続時間更新。
        /// </summary>
        public void UpdateModifiers(float deltaTime)
        {
            foreach (var modifierByType in _modifier)
            {
                var modifierList = modifierByType.Value;
                for (int i = modifierList.Count - 1; i >= 0; --i)
                {
                    // 継続時間切れたmodifierはリストから除去。
                    if (modifierList[i].Update(deltaTime))
                    {
                        var removeModifer = modifierList[i];
                        modifierList.Remove(removeModifer);
                        // 自然減速の場合はスキル効果終了後に一度だけ加算するので保持しておく
                        if (modifierByType.Key != Gallop.SkillDefine.SkillModifierParam.CurrentSpeedWithNaturalDeceleration)
                        {
                            continue;
                        }
                        AddOneTimeSkillModifier(modifierByType.Key, removeModifer);
                    }
                }
            }
            // スキル効果値変動値を更新する
            UpdateFluctuateValues();
#if CYG_DEBUG
            // 終了時間取りたいのでちゃんと更新はかける
            foreach (var cancelledModiferByType in _dbgCancelledModifier) {
                var modifierList = cancelledModiferByType.Value;
                for (int i = modifierList.Count - 1; i >= 0; --i)
                {
                    // 継続時間切れたmodifierはリストから除去。
                    // Update内でやっているのは時間更新だけなので問題ない想定
                    if (modifierList[i].Update(deltaTime))
                    {
                        var removeModifer = modifierList[i];
                        modifierList.Remove(removeModifer);
                    }
                }
            }
#endif
        }

        /// <summary> スキル効果値変動値を更新する </summary>
        private void UpdateFluctuateValues()
        {
            const float DEFAULT_FLUCUTATE_VALUE = 1f;
            float debuffAbilityValueMultiplyRatio = DEFAULT_FLUCUTATE_VALUE;
            // デバフスキル効果値乗算変動
            if (HasModifier(Gallop.SkillDefine.SkillModifierParam.DebuffAbilityValueMultiply))
            {
                debuffAbilityValueMultiplyRatio = ApplyModifier(Gallop.SkillDefine.SkillModifierParam.DebuffAbilityValueMultiply, DEFAULT_FLUCUTATE_VALUE);
            }

            float debuffAbilityValueMultiplyOtherActivateRatio = DEFAULT_FLUCUTATE_VALUE;
            // 他者が発動したデバフスキルのみ効果値乗算変動
            if (HasModifier(Gallop.SkillDefine.SkillModifierParam.DebuffAbilityValueMultiplyOtherActivate))
            {
                debuffAbilityValueMultiplyOtherActivateRatio =
                    ApplyModifier(Gallop.SkillDefine.SkillModifierParam.DebuffAbilityValueMultiplyOtherActivate, DEFAULT_FLUCUTATE_VALUE);
            }

            _skillParamFluctuate.UpdateFluctuateValues(debuffAbilityValueMultiplyRatio, debuffAbilityValueMultiplyOtherActivateRatio);
        }

        /// <summary>
        /// SkillParamModifierAddを生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierAdd CreateSkillParamAddModifier(
            float value, float? time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        )
        {
            // timeが渡されない時は1回のみの発動
            if (time.HasValue)
            {
                return new SkillParamModifierAdd(value, time.Value, skillModifierParam, _skillParamFluctuate, isActivateOthers);
            }
            else
            {
                return new SkillParamModifierAdd(value, skillModifierParam, _skillParamFluctuate, isActivateOthers);
            }
        }

        /// <summary>
        /// SkillParamModifierSetを生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierSet CreateSkillParamSetModifier(
            float value, float? time, Gallop.SkillDefine.SkillModifierParam skillModifierParam,  bool isActivateOthers
        )
        {
            // timeが渡されない時は1回のみの発動
            if (time.HasValue)
            {
                return new SkillParamModifierSet(value, time.Value, skillModifierParam, _skillParamFluctuate, isActivateOthers);
            }
            else
            {
                return new SkillParamModifierSet(value, skillModifierParam, _skillParamFluctuate, isActivateOthers);
            }
        }

        /// <summary>
        /// SkillParamModifierSetを生成
        /// </summary>
        /// <param name="value"> 効果量(true or false) </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierSet CreateSkillParamSetModifier(
            bool value, float? time, Gallop.SkillDefine.SkillModifierParam skillModifierParam, bool isActivateOthers
        )
        {
            // timeが渡されない時は1回のみの発動
            if (time.HasValue)
            {
                return new SkillParamModifierSet(value, time.Value, skillModifierParam, isActivateOthers);
            }
            else
            {
                return new SkillParamModifierSet(value, skillModifierParam, isActivateOthers);
            }
        }

        /// <summary>
        /// SkillParamModifierMultiplyを生成
        /// </summary>
        /// <param name="value"> 効果量 </param>
        /// <param name="time"> 効果時間 </param>
        /// <param name="skillModifierParam"> SkillModifierParamの種類 </param>
        /// <param name="isActivateOthers"> 他者が発動したかどうか </param>
        /// <returns></returns>
        public SkillParamModifierMultiply CreateSkillParamMultiplyModifier(
            float value, float? time, Gallop.SkillDefine.SkillModifierParam skillModifierParam,  bool isActivateOthers
        )
        {
            // timeが渡されない時は1回のみの発動
            if (time.HasValue)
            {
                return new SkillParamModifierMultiply(value, time.Value, skillModifierParam, _skillParamFluctuate, isActivateOthers);
            }
            else
            {
                return new SkillParamModifierMultiply(value, skillModifierParam, _skillParamFluctuate, isActivateOthers);
            }
        }

        /// <summary>
        /// スキル効果量追加。
        /// </summary>
        public void AddModifier(Gallop.SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
        {
            if (!_modifier.ContainsKey(type))
            {
                _modifier.Add(type, new List<ISkillParamModifier>());
            }
            _modifier[type].Add(modifier);
        #if CYG_DEBUG
            if(RaceManagerSimulate.HasInstance())
            {
                var history = new ReceiveHistory(RaceManagerSimulate.Instance.AccumulateTimeSinceStart, type, modifier);
                DbgReceiveHistoryList.Add(history);
            }
        #endif
        }
        /// <summary>
        /// 効果時間に依らず1度だけ発動するスキルの効果量追加。
        /// </summary>
        /// <param name="type"> 効果量の種類 </param>
        /// <param name="modifier"> 追加したいModifer </param>
        private void AddOneTimeSkillModifier(Gallop.SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
        {
            if (!_oneTimeSkillModifier.ContainsKey(type))
            {
                _oneTimeSkillModifier.Add(type, new List<ISkillParamModifier>());
            }

            _oneTimeSkillModifier[type].Add(modifier);
#if CYG_DEBUG
            if (RaceManagerSimulate.HasInstance())
            {
                var history = new ReceiveHistory(RaceManagerSimulate.Instance.AccumulateTimeSinceStart, type, modifier);
                DbgOneTimeSkillReceiveHistoryList.Add(history);
            }
#endif
        }

        /// <summary>
        /// SkillModifierParamのスキル効果が現在適用中かどうか。
        /// </summary>
        public bool HasModifier(Gallop.SkillDefine.SkillModifierParam type)
        {
            // キーがまだ存在しない=まだ一度も効果発動していない。
            if (!_modifier.ContainsKey(type))
            {
                return false;
            }
            
            // modifierが存在するということは、効果時間が継続しているということ。
            return _modifier[type].Count > 0;
        }
        
        /// <summary>
        /// スキル効果量を適用。
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        public float ApplyModifier(Gallop.SkillDefine.SkillModifierParam type, float value)
        {
            if (_modifier.TryGetValue(type, out var modifierList))
            {
                foreach (var modifier in modifierList)
                {
                    value = modifier.Apply(value);
                }
            }
            return value;
        }

        /// <summary>
        /// バフのスキル効果量のみを適用。
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        public float ApplyModifierBuffParam(Gallop.SkillDefine.SkillModifierParam type, float value)
        {
            if (_modifier.TryGetValue(type, out var modifierList))
            {
                foreach (var modifier in modifierList)
                {
                    if (modifier.IsDebuff)
                    {
                        continue;
                    }
                    value = modifier.Apply(value);
                }
            }
            return value;
        }

        /// <summary>
        /// スキル効果量を適用。
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        public bool ApplyModifier(Gallop.SkillDefine.SkillModifierParam type, bool value)
        {
            if (_modifier.TryGetValue(type, out var modifierList))
            {
                foreach (var modifier in modifierList)
                {
                    value = modifier.Apply(value);
                }
            }
            return value;
        }
        
        /// <summary>
        /// 効果時間に依らず1度だけ発動するスキルの効果量を適用
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        public float ApplyOneTimeSkillModifier(Gallop.SkillDefine.SkillModifierParam type, float value)
        {
            if (!_oneTimeSkillModifier.TryGetValue(type, out var modifierList))
            {
                return value;
            }

            if (modifierList.Count <= 0)
            {
                return value;
            }

            foreach (var modifier in modifierList)
            {
                value = modifier.Apply(value);
            }

            // 1度だけしか反映しないので発動したら削除する
            modifierList.Clear();
            return value;
        }
        
        /// <summary>
        /// スキル効果値の削除。
        /// </summary>
        public void RemoveModifier(Gallop.SkillDefine.SkillModifierParam type)
        {
            if (_modifier.TryGetValue(type, out var modifierList))
            {
                modifierList.Clear();
            }
        }
    #if CYG_DEBUG

        public List<ReceiveHistory> DbgReceiveHistoryList = new List<ReceiveHistory>(); 
        /// <summary> 効果時間に依らず1度だけ発動するスキルの発動リスト </summary>
        public List<ReceiveHistory> DbgOneTimeSkillReceiveHistoryList = new List<ReceiveHistory>();
        
        public List<ISkillParamModifier> DbgGetModifier(Gallop.SkillDefine.SkillModifierParam type)
        {
            _modifier.TryGetValue(type, out var modifierList);
            return modifierList;
        }
        
        public List<ISkillParamModifier> DbgGetCancelledModifier(Gallop.SkillDefine.SkillModifierParam type)
        {
            _dbgCancelledModifier.TryGetValue(type, out var modifierList);
            return modifierList;
        }
        /// <summary>
        /// スキル効果量追加。
        /// </summary>
        public void AddCancelledModifier(Gallop.SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
        {
            if (!_dbgCancelledModifier.ContainsKey(type))
            {
                _dbgCancelledModifier.Add(type, new List<ISkillParamModifier>());
            }
            _dbgCancelledModifier[type].Add(modifier);
            if(RaceManagerSimulate.HasInstance())
            {
                var history = new ReceiveHistory(RaceManagerSimulate.Instance.AccumulateTimeSinceStart, type, modifier);
                DbgReceiveHistoryList.Add(history);
            }
        }
    #endif
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
// Debugデータが参照を持つので切り出している
namespace StandaloneSimulator
{
    public class ReceiveHistory
    {
        public float AccumulateTime;
        public Gallop.SkillDefine.SkillModifierParam Type;
        public int FromHorseIndex;
        public int SkillId;
        public int SkillLevel;
        public int ItemId;
        public float Value;
        public bool IsValid;

        public ReceiveHistory()
        {
        }

        public ReceiveHistory(ReceiveHistory from)
        {
            AccumulateTime = from.AccumulateTime;
            Type = from.Type;
            FromHorseIndex = from.FromHorseIndex;
            SkillId = from.SkillId;
            SkillLevel = from.SkillLevel;
            ItemId = from.ItemId;
            Value = from.Value;
            IsValid = from.IsValid;
        }
        
    #if CYG_DEBUG
        public ReceiveHistory(float time, Gallop.SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
        {
            AccumulateTime = time;
            Type = type;
            FromHorseIndex = modifier.DbgHorseIndex;
            SkillId = modifier.DbgSkillId;
            SkillLevel = modifier.DbgSkillLevel;
            ItemId = modifier.DbgItemId;
            Value = modifier.BaseValue;
        }
    #endif
    }
}
#endif
#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections.Generic;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ISkillTarget
    {
        //---------------------------------------------------------------
        /// <summary>
        /// 効果対象計算。
        /// </summary>
        //---------------------------------------------------------------
        void <PERSON>gets();

        //---------------------------------------------------------------
        /// <summary>
        /// 効果対象取得。
        /// </summary>
        /// <returns>対象がいない場合は空リスト。</returns>
        /// <remarks>事前にCalcTargets()で効果対象を計算済みであることが前提。</remarks>
        //---------------------------------------------------------------
        List<IHorseRaceInfoSimulate> GetTargets();

        //---------------------------------------------------------------
        /// <summary>
        /// 効果対象設定。
        /// </summary>
        /// <param name="targetFlags">HorseIndexを使用したビットフラグ。</param>
        /// <remarks>レース再生中のみの使用を想定。</remarks>
        //---------------------------------------------------------------
        void SetTargets( int targetFlags );

        //---------------------------------------------------------------
        /// <summary>
        /// 効果対象から指定キャラを取り除く。
        /// </summary>
        //---------------------------------------------------------------
        void RemoveTargets( List<IHorseRaceInfoSimulate> remove );
        
#if CYG_DEBUG
        IReadOnlyList<IHorseRaceInfoSimulate> GetDbgDebuffCancelTargetList();
#endif
    }
}
#endif

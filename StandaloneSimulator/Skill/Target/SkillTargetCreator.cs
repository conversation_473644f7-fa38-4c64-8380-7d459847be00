#if STANDALONE_SIMULATOR || UNITY_EDITOR
using SkillDefine = Gallop.SkillDefine;
namespace StandaloneSimulator
{
    public static class SkillTargetCreator
    {
        public static ISkillTarget CreateSkillTarget( 
            ref SkillTargetBuildInfo buildInfo, 
            SkillDefine.SkillTargetType type, 
            int skillId )
        {
            switch( type )
            {
                case SkillDefine.SkillTargetType.Self:                  return new SkillTargetSelf( ref buildInfo );
                case SkillDefine.SkillTargetType.All:                   return new SkillTargetAll( ref buildInfo );
                case SkillDefine.SkillTargetType.AllOtherSelf:          return new SkillTargetAllOtherSelf( ref buildInfo );
                case SkillDefine.SkillTargetType.Visible:               return new SkillTargetVisible( ref buildInfo );
                case SkillDefine.SkillTargetType.RandomOtherSelf:       return new SkillTargetRandomOtherSelf( ref buildInfo );
                case SkillDefine.SkillTargetType.Order:                 return new SkillTargetOrder( ref buildInfo );
                case SkillDefine.SkillTargetType.OrderInfront:          return new SkillTargetOrderInfront( ref buildInfo );
                case SkillDefine.SkillTargetType.OrderBehind:           return new SkillTargetOrderBehind( ref buildInfo );
                case SkillDefine.SkillTargetType.SelfInfront:           return new SkillTargetSelfInfront( ref buildInfo );
                case SkillDefine.SkillTargetType.SelfBehind:            return new SkillTargetSelfBehind( ref buildInfo );
                case SkillDefine.SkillTargetType.SelfInfrontTemptation: return new SkillTargetSelfInfrontTemptation(ref buildInfo);
                case SkillDefine.SkillTargetType.SelfBehindTemptation: return new SkillTargetSelfBehindTemptation(ref buildInfo);
                case SkillDefine.SkillTargetType.TeamMember:            return new SkillTargetTeamMember( ref buildInfo );

                case SkillDefine.SkillTargetType.Near:                  return new SkillTargetNear( ref buildInfo );
                case SkillDefine.SkillTargetType.NearInfront:           return new SkillTargetNearInfront( ref buildInfo );
                case SkillDefine.SkillTargetType.NearBehind:            return new SkillTargetNearBehind( ref buildInfo );

                case SkillDefine.SkillTargetType.SelfAndBlockFront:     return new SkillTargetSelfAndBlockFront( ref buildInfo );
                case SkillDefine.SkillTargetType.BlockSide:             return new SkillTargetBlockSide( ref buildInfo );

                case SkillDefine.SkillTargetType.RunningStyle:          return new SkillTargetRunningStyle( ref buildInfo );
                case SkillDefine.SkillTargetType.RunningStyleOtherSelf: return new SkillTargetRunningStyleOtherSelf( ref buildInfo );
                case SkillDefine.SkillTargetType.RunningStyleTemptationOtherSelf: return new SkillTargetRunningStyleTemptationOtherSelf(ref buildInfo);

                case SkillDefine.SkillTargetType.CharaId:               return new SkillTargetCharaId(ref buildInfo);
                case SkillDefine.SkillTargetType.ActivateHealSkill:     return new SkillTargetActivateHealSkill(ref buildInfo);

                default:
                    Debug.LogError( string.Format( "サポートされていないSkillTargetTypeです。skillId={0} type={1}", skillId, type ) );
                    return new SkillTargetNull();
            }
        }
    }
}
#endif

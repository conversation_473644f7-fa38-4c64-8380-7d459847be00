#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System;

namespace StandaloneSimulator
{
    public struct SkillTargetBuildInfo
    {
        public readonly IHorseRaceInfoSimulate ownerHorse;
        public readonly IRaceHorseAccessor horseAccessor;
        public readonly IRaceRandomGenerator randomGenerator;
        public readonly RaceInfo raceInfo;
        public readonly int valueRh;
        public readonly bool IsDebuff;

        public SkillTargetBuildInfo( 
            IHorseRaceInfoSimulate owner, 
            IRaceHorseAccessor horseAccessor, 
            IRaceRandomGenerator randomGenerator,
            RaceInfo raceInfo, 
            int valueRh,
            bool isDebuff)
        {
            this.ownerHorse = owner;
            this.horseAccessor = horseAccessor;
            this.randomGenerator = randomGenerator;
            this.raceInfo = raceInfo;
            this.valueRh = valueRh;
            this.IsDebuff = isDebuff;
        }
    };


    //-----------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：Nullオブジェクト。
    /// </summary>
    //-----------------------------------------------------------------------
    public class SkillTargetNull : ISkillTarget
    {
        public static List<IHorseRaceInfoSimulate> EmptyTarget { get; private set; }

        //-------------------------------------------------------------------
        static SkillTargetNull()
        {
            EmptyTarget = new List<IHorseRaceInfoSimulate>();
        }

        //-------------------------------------------------------------------
        public SkillTargetNull()
        {
        }

        //-------------------------------------------------------------------
        public void CalcTargets()
        {
        }
        
        //-------------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetTargets()
        {
            return EmptyTarget;
        }

        //-------------------------------------------------------------------
        public void SetTargets( int targetFlags )
        {
        }

        //-------------------------------------------------------------------
        public void RemoveTargets( List<IHorseRaceInfoSimulate> remove )
        {
        }
#if CYG_DEBUG
        public IReadOnlyList<IHorseRaceInfoSimulate> GetDbgDebuffCancelTargetList() { return new List<IHorseRaceInfoSimulate>(); }
#endif
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillTargetBase : ISkillTarget
    {
        protected readonly IHorseRaceInfoSimulate       _owner                  = null;
        protected readonly IRaceHorseAccessor   _horseAccessor          = null;
        protected readonly IRaceRandomGenerator _raceRandomGenerator    = null;
        protected readonly RaceInfo             _raceInfo               = null;
        protected readonly int                  _valueRh                = 0;
        protected List<IHorseRaceInfoSimulate>          _targets                = null;
        protected readonly bool _isDebuff;
#if CYG_DEBUG
        /// <summary> デバフ無効化によってターゲットから外されたキャラリスト </summary>
        private List<IHorseRaceInfoSimulate> _dbgDebuffCancelTargetList;
#endif

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        /// <param name="owner">スキル所有者。</param>
        //---------------------------------------------------------------
        protected SkillTargetBase( ref SkillTargetBuildInfo buildInfo )
        {
            _owner = buildInfo.ownerHorse;
            _horseAccessor = buildInfo.horseAccessor;
            _raceRandomGenerator = buildInfo.randomGenerator;
            _raceInfo = buildInfo.raceInfo;
            _valueRh = buildInfo.valueRh;
            _targets = new List<IHorseRaceInfoSimulate>();
            _isDebuff = buildInfo.IsDebuff;
        #if CYG_DEBUG
            _dbgDebuffCancelTargetList = new List<IHorseRaceInfoSimulate>();
        #endif
        }

        //---------------------------------------------------------------
        public void CalcTargets()
        {
            // 各派生での効果対象計算が終わった後、
            CalcTargetsDerived();
            
            // 対象除外処理を入れる。
            ExceptTarget();
            
        #if CYG_DEBUG
            // 効果対象が重複していないかエラーチェック。
            DbgCheckDuplicate();
        #endif
        }

        /// <summary>
        /// _targetsから対象に選んではいけないキャラの除外。
        /// </summary>
        private void ExceptTarget()
        {
        #if CYG_DEBUG
            _dbgDebuffCancelTargetList.Clear();
        #endif
            // デバフなら除外処理が必要。
            if (_isDebuff)
            {
                _targets = _targets
                    .Where(h =>
                    {
                        // デバフ無効化のキャラは除外。
                        if (h.IsDebuffCancel())
                        {
                        #if CYG_DEBUG
                            // ログ出力用に除外したキャラを記録する
                            _dbgDebuffCancelTargetList.Add(h);
                        #endif
                            return false;
                        }

                        // 時分以外の、自分と同じチームのキャラは除外。
                        if (h != _owner && RaceUtil.IsSameTeam(_owner.TeamId, h.TeamId))
                        {
                        #if CYG_DEBUG
                            // ログ出力用に除外したキャラを記録する
                            _dbgDebuffCancelTargetList.Add(h);
                        #endif
                            return false;
                        }
                        
                        return true;
                    })
                    .ToList();
            }
        }
        
        /// <summary>
        /// 効果対象計算の実態。派生先でこれをオーバーライドする。
        /// </summary>
        public abstract void CalcTargetsDerived();
        
        //-------------------------------------------------------------------
        public List<IHorseRaceInfoSimulate> GetTargets()
        {
            return _targets;
        }
        
#if CYG_DEBUG
        /// <summary>
        /// 効果対象リストに重複が含まれていないかをチェック。
        /// </summary>
        private void DbgCheckDuplicate()
        {
            var tmp = _targets.Distinct();
            Debug.Assert( tmp.Count() == _targets.Count, string.Format( "効果対象が重複しています。 HorseIndex={0}, this={1}", _owner.HorseIndex, this ) );
        }
        
        public IReadOnlyList<IHorseRaceInfoSimulate> GetDbgDebuffCancelTargetList()
        {
            return _dbgDebuffCancelTargetList;
        }
#endif

        //-------------------------------------------------------------------
        public void SetTargets( int targetFlags )
        {
            _targets = new List<IHorseRaceInfoSimulate>( Gallop.RaceDefine.RACE_HORSE_MAX );

            var allHorses = _horseAccessor.GetHorseRaceInfos();
            for( int i = 0, cnt = allHorses.Length; i < cnt; ++i )
            {
                if( 0 == ( targetFlags & 1 << i ) )
                {
                    continue;
                }
                _targets.Add( allHorses[ i ] );
            }
        }

        //-------------------------------------------------------------------
        public void RemoveTargets( List<IHorseRaceInfoSimulate> remove )
        {
            _targets = _targets.Except( remove ).ToList();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：全員。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetAll : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetAll( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetHorseRaceInfos().ToList();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：スキル所有者以外の全員。
    /// </summary>
    /// <seealso cref="Test.TestSkillTargetAllOtherSelf"/>
    //-------------------------------------------------------------------
    public class SkillTargetAllOtherSelf : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetAllOtherSelf( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetHorseRaceInfos().Where( h => h != _owner ).ToList();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：視野範囲の馬（スキル発動者は含まない）。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetVisible : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetVisible( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            var ownerPos = _owner.GetPosition();
            Debug.Assert( ownerPos != RaceUtilMath.VECTOR3_ZERO );

            // 視野範囲内の、最も近いｎ体を選ぶ。
            _targets = _owner.VisibleHorses
                .Take( Math.Min( _owner.VisibleHorseCount, _valueRh ) )
                .OrderBy( around => ( null != around ) ? Vector3.Distance( ownerPos, around.infoSimulate.GetPosition() ) : int.MaxValue )
                .Select( h => ( null != h ) ? h.infoSimulate : null )
                .ToList();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：スキル発動者を除く全員からランダムにｎ人。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetRandomOtherSelf : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetRandomOtherSelf( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
            Debug.Assert( buildInfo.valueRh > 0 && buildInfo.valueRh <= _raceInfo.NumRaceHorses-1 );
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            int numRaceHorses = _horseAccessor.GetHorseNumber();
            int numTarget = Math.Min( _valueRh, numRaceHorses-1 );
            int ownerHorseIndex = _owner.HorseIndex;

            _targets = new List<IHorseRaceInfoSimulate>( numTarget );
            for( int i = 0; i < numTarget; ++i )
            {
                // 検索開始地点をランダムにし、最大で全キャラ数分のループを回して効果対象に選べるキャラを抽出する。
                int startHorseIndex = _raceRandomGenerator.GetRandom( numRaceHorses );

                for( int horseIndexOffset = 0; horseIndexOffset < numRaceHorses; ++horseIndexOffset )
                {
                    int horseIndex = ( startHorseIndex + horseIndexOffset ) % numRaceHorses;

                    // スキル発動者は対象に選ばない。
                    if( horseIndex == ownerHorseIndex )
                    {
                        continue;
                    }

                    // 既に対象に選んだキャラは選ばない。
                    var tmp = _targets.FirstOrDefault( h => h.HorseIndex == horseIndex );
                    if( null != tmp )
                    {
                        continue;
                    }

                    var horseInfo = _horseAccessor.GetHorseInfo( horseIndex );
                    _targets.Add( horseInfo );
                    break;
                }
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：スキル所有者自身。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetSelf : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetSelf( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = new List<IHorseRaceInfoSimulate>() { _owner };
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：順位指定。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetOrder : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetOrder( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
            // 1～18で指定してもらう。
            Debug.Assert( buildInfo.valueRh >= 1 && buildInfo.valueRh <= _raceInfo.NumRaceHorses );
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            var target = _horseAccessor.GetHorseRaceInfos().FirstOrDefault( h => h.CurOrder+1 == _valueRh );
            _targets = new List<IHorseRaceInfoSimulate>() { target };
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：順位が指定値より前。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetOrderInfront : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetOrderInfront( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
            // 1～18で指定してもらう。
            Debug.Assert( buildInfo.valueRh >= 1 && buildInfo.valueRh <= _raceInfo.NumRaceHorses );
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetHorseRaceInfos().Where( h => h.CurOrder+1 < _valueRh ).ToList();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：順位が指定値より後ろ。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetOrderBehind : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetOrderBehind( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
            // 1～18で指定してもらう。
            Debug.Assert( buildInfo.valueRh >= 1 && buildInfo.valueRh <= _raceInfo.NumRaceHorses );
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetHorseRaceInfos().Where( h => h.CurOrder+1 > _valueRh ).ToList();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：順位がスキル発動者より前のｎ頭。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetSelfInfront : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetSelfInfront( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            // 発動者が１位の場合、前には誰もいない。
            if( 0 == _owner.CurOrder )
            {
                _targets = SkillTargetNull.EmptyTarget;
                return;
            }

            _targets = new List<IHorseRaceInfoSimulate>( _valueRh );
            
            int getOrder = _owner.CurOrder;
            for( int i = 0; i < _valueRh; ++i )
            {
                // 取得対象の順位が１位より前に行ったら、もう対象が存在しない。
                if( --getOrder < 0 )
                {
                    break;
                }
                var info = _horseAccessor.GetHorseInfoByOrder( getOrder );
                if( null == info )
                {
                    Debug.Assert( false, "スキル対象の指定順位の馬の取得に失敗。getOrder=" + getOrder );
                    continue;
                }
                _targets.Add( info );
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：順位がスキル発動者より後のｎ頭。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetSelfBehind : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetSelfBehind( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            // 発動者が最下位の場合、後ろには誰もいない。
            if( _horseAccessor.LastOrder == _owner.CurOrder )
            {
                _targets = SkillTargetNull.EmptyTarget;
                return;
            }

            _targets = new List<IHorseRaceInfoSimulate>( _valueRh );

            int getOrder = _owner.CurOrder;
            for( int i = 0; i < _valueRh; ++i )
            {
                // 取得対象の順位が最下位より後に行ったら、もう対象が存在しない。
                if( ++getOrder > _horseAccessor.LastOrder )
                {
                    break;
                }
                var info = _horseAccessor.GetHorseInfoByOrder( getOrder );
                if( null == info )
                {
                    Debug.Assert( false, "スキル対象の指定順位の馬の取得に失敗。getOrder=" + getOrder );
                    continue;
                }
                _targets.Add( info );
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：自分以外で指定走法で興奮しているキャラ全員。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetRunningStyleTemptationOtherSelf : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetRunningStyleTemptationOtherSelf(ref SkillTargetBuildInfo buildInfo) : base(ref buildInfo)
        {
            Debug.Assert( buildInfo.valueRh >= (int)Gallop.RaceDefine.RunningStyle.Nige && buildInfo.valueRh <= (int)Gallop.RaceDefine.RunningStyle.Oikomi );
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetRunningStyleHorses( (Gallop.RaceDefine.RunningStyle)_valueRh )
                .Where( h => h.IsTemptation )
                .ToList();
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：順位がスキル発動者より前で興奮しているｎ頭。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetSelfInfrontTemptation : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetSelfInfrontTemptation(ref SkillTargetBuildInfo buildInfo) : base(ref buildInfo)
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            // 発動者が１位の場合、前には誰もいない。
            if (0 == _owner.CurOrder)
            {
                _targets = SkillTargetNull.EmptyTarget;
            }
            else
            {
                // 興奮している自分より後ろのキャラ。
                var temptationHorseList = _horseAccessor.GetTemptationHorseList().Where(h => h.CurOrder < _owner.CurOrder).ToList();

                // temptationHorseListの中から最大_valueRh体までランダムに選ぶ。
                _targets = new List<IHorseRaceInfoSimulate>(_valueRh);
                for(int i = 0, cnt = Math.Min(temptationHorseList.Count, _valueRh); i < cnt; ++i)
                {
                    int index = _raceRandomGenerator.GetRandom(temptationHorseList.Count);
                    var target = temptationHorseList[index];
                    temptationHorseList.RemoveAt(index);
                    _targets.Add(target);
                }
            }
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：順位がスキル発動者より後で興奮しているｎ頭。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetSelfBehindTemptation : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetSelfBehindTemptation(ref SkillTargetBuildInfo buildInfo) : base(ref buildInfo)
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            // 発動者が最下位の場合、後ろには誰もいない。
            if (_horseAccessor.LastOrder == _owner.CurOrder)
            {
                _targets = SkillTargetNull.EmptyTarget;
            }
            else
            {
                // 興奮している自分より後ろのキャラ。
                var temptationHorseList = _horseAccessor.GetTemptationHorseList().Where(h => h.CurOrder > _owner.CurOrder).ToList();

                // temptationHorseListの中から最大_valueRh体までランダムに選ぶ。
                _targets = new List<IHorseRaceInfoSimulate>(_valueRh);
                for(int i = 0, cnt = Math.Min(temptationHorseList.Count, _valueRh); i < cnt; ++i)
                {
                    int index = _raceRandomGenerator.GetRandom(temptationHorseList.Count);
                    var target = temptationHorseList[index];
                    temptationHorseList.RemoveAt(index);
                    _targets.Add(target);
                }
            }
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：同じチームメンバー。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetTeamMember : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetTeamMember(ref SkillTargetBuildInfo buildInfo) : base(ref buildInfo)
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            int teamId = _owner.TeamId;
            
            // どこのチームにも属していないなら自分だけ。
            if (teamId == Gallop.RaceDefine.TEAM_ID_NULL)
            {
                _targets = new List<IHorseRaceInfoSimulate>() { _owner };
                return;
            }

            //  自分と同じチームメンバーが対象。
            var teamMember = _horseAccessor.GetTeamMemberHorseInfoArray(teamId);
            _targets = teamMember.ToList();
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：距離ｎ以内の最も近い一頭。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetMostNear : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetMostNear( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            // 距離とレーン距離の閾値。この値以内だけが対象。
            float distanceGapAbsThreshold = _valueRh;
            float laneDistanceGapAbsThreshold = _valueRh * Gallop.RaceDefine.HorseLane2DistanceCoef;

            // スキル所持者の距離とレーン距離。
            float ownerDistance = _owner.GetDistance();
            float ownerLaneDistance = _owner.GetLaneDistance();

            float realDistanceMin = float.MaxValue;
            IHorseRaceInfoSimulate targetMostNear = null;

            var horses = _horseAccessor.GetHorseRaceInfos();
            foreach( var horse in horses )
            {
                if( horse == _owner )
                {
                    continue;
                }

                // 距離差が一定範囲内。
                float distanceGapAbs = Math.Abs( ownerDistance - horse.GetDistance() );
                if( distanceGapAbs > distanceGapAbsThreshold )
                {
                    continue;
                }

                // レーン距離差が一定範囲内。
                float laneDistanceGapAbs = Math.Abs( ownerLaneDistance - horse.GetLaneDistance() );
                if( laneDistanceGapAbs > laneDistanceGapAbsThreshold )
                {
                    continue;
                }

                // 最も近い距離とキャラを保存。※LaneDistanceはDistanceに換算。
                float realDistance = distanceGapAbs + ( laneDistanceGapAbs * Gallop.RaceDefine.HorseDistance2LaneCoef );
                if( realDistance > realDistanceMin )
                {
                    continue;
                }
                realDistanceMin = realDistance;
                targetMostNear = horse;
            }

            // 最も近かった１キャラが効果対象。範囲内にキャラがいなかった場合は空リストにしておく。
            _targets = new List<IHorseRaceInfoSimulate>( 1 );
            if( null != targetMostNear )
            {
                _targets.Add( targetMostNear );
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：近くのキャラｎ頭。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetNear : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetNear( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            int numHorse = Math.Min( _valueRh, _owner.NearHorseCount );
            int addCnt = 0;

            _targets = new List<IHorseRaceInfoSimulate>();
            var horses = _owner.NearHorses.Take( _owner.NearHorseCount ).ToArray();

            // スキル所持者からの距離が近い順にソート。
            System.Array.Sort( horses, new AroundNearComparer() );

            foreach( var horse in horses )
            {
                // これ以上のキャラは対象に取らない。
                if( addCnt >= numHorse )
                {
                    break;
                }
                if( horse == _owner )
                {
                    continue;
                }
                if( horse == null )
                {
                    Debug.Assert( false );
                    continue;
                }
                // 範囲内にいるので効果対象。
                _targets.Add( horse.infoSimulate );
                ++addCnt;
            }
        }
    }

    class AroundNearComparer : IComparer
    {
        public int Compare( object lh, object rh )
        {
            if( lh == null || rh == null )
            {
                return 0;
            }

            var lhAround = lh as Gallop.AroundHorse;
            var rhAround = rh as Gallop.AroundHorse;
            float lhDist = lhAround.distanceGapAbs + ( lhAround.laneGapAbs * Gallop.RaceDefine.HorseDistance2LaneCoef );
            float rhDist = rhAround.distanceGapAbs + ( rhAround.laneGapAbs * Gallop.RaceDefine.HorseDistance2LaneCoef );
            return lhDist.CompareTo( rhDist );
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：近く(距離1.5以内)で自分より前のキャラ全て。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetNearInfront : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetNearInfront( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _owner.NearHorses
                .Take( _owner.NearHorseCount )
                .Where( h => h.distanceGap > 0 )
                .Select( h => h.infoSimulate )
                .ToList();
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：近く(距離1.5以内)で自分より後のキャラ全て。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetNearBehind : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetNearBehind( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _owner.NearHorses
                .Take( _owner.NearHorseCount )
                .Where( h => h.distanceGap < 0 )
                .Select( h => h.infoSimulate )
                .ToList();
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：スキル発動者と前方ブロック相手。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetSelfAndBlockFront : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetSelfAndBlockFront( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = new List<IHorseRaceInfoSimulate>( 2 );
            _targets.Add( _owner );

            var blockFront = _owner.GetBlockHorseFront();
            if( null != blockFront )
            {
                _targets.Add( blockFront );
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：スキル発動者のサイドをブロックしているキャラ。※イン/アウト両方。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetBlockSide : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetBlockSide( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = new List<IHorseRaceInfoSimulate>( 2 );

            var blockIn = _owner.GetBlockHorseIn();
            if( null != blockIn )
            {
                _targets.Add( blockIn );
            }

            var blockOut = _owner.GetBlockHorseOut();
            if( null != blockOut )
            {
                _targets.Add( blockOut );
            }
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：指定走法のキャラ全員。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetRunningStyle : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetRunningStyle( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
            Debug.Assert( buildInfo.valueRh >= (int)Gallop.RaceDefine.RunningStyle.Nige && buildInfo.valueRh <= (int)Gallop.RaceDefine.RunningStyle.Oikomi );
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetRunningStyleHorses( (Gallop.RaceDefine.RunningStyle)_valueRh ).ToList();
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：自分以外の指定走法のキャラ全員。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetRunningStyleOtherSelf : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetRunningStyleOtherSelf( ref SkillTargetBuildInfo buildInfo ) : base( ref buildInfo )
        {
            Debug.Assert( buildInfo.valueRh >= (int)Gallop.RaceDefine.RunningStyle.Nige && buildInfo.valueRh <= (int)Gallop.RaceDefine.RunningStyle.Oikomi );
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetRunningStyleHorses( (Gallop.RaceDefine.RunningStyle)_valueRh ).Where( h => h != _owner ).ToList();
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：指定キャラIdのキャラ全員。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetCharaId : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetCharaId(ref SkillTargetBuildInfo buildInfo) : base(ref buildInfo)
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets = _horseAccessor.GetHorseRaceInfos().Where(x => x.CharaId == _valueRh).ToList();
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果対象計算機：直近で回復スキルを発動したキャラ
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillTargetActivateHealSkill : SkillTargetBase
    {
        //---------------------------------------------------------------
        public SkillTargetActivateHealSkill(ref SkillTargetBuildInfo buildInfo) : base(ref buildInfo)
        {
        }

        //---------------------------------------------------------------
        public override void CalcTargetsDerived()
        {
            _targets.Clear();
            var activateHealSkillHorseHistoryList = _horseAccessor.GetActivateHealSkillHorseHistoryList();
            for (int i = activateHealSkillHorseHistoryList.Count - 1; i >= 0; i--)
            {
                if (_targets.Contains(activateHealSkillHorseHistoryList[i]))
                {
                    continue;
                }

                _targets.Add(activateHealSkillHorseHistoryList[i]);
                // CSVで設定された人数分見つかったら終了
                if (_targets.Count == _valueRh)
                {
                    break;
                }
            }
        }
    }
}

#endif

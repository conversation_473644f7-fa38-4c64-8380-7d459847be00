# Race Simulate API Server
GallopClientにあるレースシミュレート処理を用いたレースシミュレートAPIサーバー

## setup on local
### GallopClientのプログラムをリンクする
1. コマンドプロンプトを起動する (Windows キー + R → ｀cmd｀ と打ってEnter)
2. コマンドプロンプトに次のコマンドを打つ
    ```
    cd <RaceSimulateAPI直下>
    mklink /J StandaloneSimulator <GallopClient の StandaloneSimulator/Src のパス>
    mklink /J LibNative <GallopClient の StandaloneSimulator/LibNative のパス>
    ```
3. 以下の表示が出て、エクスプローラにジャンクションを示すアイコンが表示されたら成功です
    ```
    StandaloneSimulator <<===>> <GallopClient の StandaloneSimulator/Src のパス> のジャンクションが作成されました
    LibNative <<===>> <GallopClient の StandaloneSimulator/LibNative のパス> のジャンクションが作成されました
    ```

StandaloneSimulatorへのパスは次の通り
```
Assets/_Gallop/Scripts/Game/Scene/Race/RaceMain/StandaloneSimulator/Core/StandaloneSimulator
```

コピペ用（Src）
```
Assets\_Gallop\Scripts\Game\Scene\Race\RaceMain\StandaloneSimulator\Core\StandaloneSimulator\Src
```

コピペ用（LibNative）
```
Assets\_Gallop\Scripts\Game\Scene\Race\RaceMain\StandaloneSimulator\Core\StandaloneSimulator\LibNative
```

## Usage
on virtual box
### start up
```shell
# build and reload
sh Tools/local_build_and_reload.sh
```

### test request
```shell
# /ping
curl -s http://localhost:15000/ping
# => OK

# /simulate
sh Tools/test_simulate.sh
# => {"simulate_result": "XXXXX...."}
```

#!/bin/sh

script_dir=$(cd $(dirname $0);pwd)
cd $script_dir

port=15000
message_pack_file="sample.mpac"

php -r '$file = json_decode(file_get_contents("sample_request.json"), true);echo \msgpack_pack($file);' > ${message_pack_file}

curl -s \
  -X POST \
  -H "Content-Type: application/x-msgpack" \
  --data-binary "@${message_pack_file}" \
  http://localhost:$port/simulate \
  > result.mpac

php -r '$result = \msgpack_unpack(file_get_contents("result.mpac"));unset($result["simulate_result"]["race_scenario_compressed"]);echo json_encode($result, JSON_PRETTY_PRINT);'

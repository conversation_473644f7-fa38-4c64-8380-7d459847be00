# CodeGeneratorについて

## ここは何をするところ？
umamusumeではマスターデータにSQLiteを使用しています。

[_GallopCsv](https://xxx.xxx.xxx/gallop/_GallopCsv)内のCSVをSQLiteに変換しています。
SQLiteの変換ルール(DSL)は[こちら](https://xxx.xxx.xxx/gallop/GallopClient/tree/develop/CodeGenerator)です。

ここではレースシミュレータ用のSQLiteファイルの作成を行います。

## 準備
レースシミュレータ用SQLiteを作成するのに手順が必要になります。
方法が2通りありますので以下に記載します。

### (1) windowsPCを使用している方向け

1. RaceSimulateAPI リポジトリの Tools/CodeGenerator に ｀Base｀ というフォルダを新しく作る
2. コマンドプロンプトを起動する (Windows キー + R → ｀cmd｀ と打ってEnter)
3. コマンドプロンプトに次のコマンドを打つ
    ```
    cd <1で作成した Base のパス>
    mklink /J CodeGenerator <GallopClient の CodeGenerator のパス>
    ```
4. 以下の表示が出て、エクスプローラにジャンクションを示すアイコンが表示されたら成功です
    ```
    CodeGenerator <<===>> <GallopClient の CodeGenerator のパス> のジャンクションが作成されました
    ```

### (2) mac, linux環境向け
現時点ではサポートしていません
`gen_race_simulate_api.bat`を参考にmdbファイルを所定の場所に生成してください

## ファイル説明
### gen_race_simulate_api.bat
レースシミュレータ用SQLiteを作成するWindowsバッチです。
ローカル環境でSQLiteファイルを作成する場合には、このバッチを実行してください。

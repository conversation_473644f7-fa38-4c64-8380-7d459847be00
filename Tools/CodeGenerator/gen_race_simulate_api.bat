@echo off
chcp 65001

set RESOURCE_VERSION=00000000
set BASE_DIR=Base/CodeGenerator

@rem rubyのバージョンを取得
for /f "usebackq delims=" %%a in (`call %~dp0set_rubyversion.bat`) do set RUBY_VERSION=%%a
if not exist %USERPROFILE%\rbenv_root\%RUBY_VERSION%-1\bin\ruby.exe (
  echo "rbenv is not installed."
  echo "please run GallopClient/tools/setup.bat."
  @pause
  exit
)

if exist error_report.html del error_report.html
if not exist ../../../resource/%RESOURCE_VERSION%/RaceSimulator mkdir ..\..\..\resource\%RESOURCE_VERSION%\RaceSimulator

%USERPROFILE%\rbenv_root\%RUBY_VERSION%-1\bin\ruby.exe %BASE_DIR%/Scripts/main.rb -s %BASE_DIR%/DSL/master.dsl.rb -c import_csv -i ../../../resource/%RESOURCE_VERSION%/csv -o ../../../resource/%RESOURCE_VERSION%/RaceSimulator -e ./error_report.html -a client %*

if errorlevel 1 (
  start error_report.html
) else if errorlevel 0 (
  del error_report.html
  echo ALL OK
  timeout 3
)

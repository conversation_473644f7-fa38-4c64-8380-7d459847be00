using System.ComponentModel.DataAnnotations;

namespace RaceSimulateAPI;

public class RaceSimulateAPIOptions
{
    /// <summary>
    /// オプションのセクション名
    /// </summary>
    public const string SectionName = "RaceSimulateAPI";

    /// <summary>
    /// UNIXドメインソケットでListenする際のパス。空文字なら設定しないためポートで受ける
    /// <remarks> 環境変数で設定されることを想定 (RACESIMULATEAPI__UNIXSOCKET) </remarks> 
    /// </summary>
    public string UnixSocket { get; set; } = string.Empty;

    /// <summary>
    /// メモリキャッシュを使用するか。デフォルトはfalse
    /// </summary>
    public bool UseMemoryCache { get; set; } = false;

    /// <summary>
    /// ログを保存するディレクトリ
    /// </summary>
    [Required(ErrorMessage = "{0} is required. this must not be empty.")]
    public string LogDir { get; set; } = string.Empty;
}
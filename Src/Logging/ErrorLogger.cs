namespace RaceSimulateAPI.Logging;
using System.Text.Json;
using System.Text.Encodings.Web;
using System.Net;

public static class ErrorLogger
{
    public static void Write(string saveLogDir, ErrorLogInfo errorLogInfo, Func<ErrorLogInfo, string> formatter)
    {
        var message = formatter(errorLogInfo);
        var filename = GetFileName(errorLogInfo.DateTime);
        var filePath = $"{saveLogDir}/{filename}";

        WriteToFile(filePath, message, errorLogInfo.DateTime);
    }

    private static void WriteToFile(string filePath, string message, DateTime dateTime)
    {
        using var sw = new StreamWriter(filePath, true);
        
        var serverName = Dns.GetHostName();
        // 格式化成 "2024-09-29T15:04:55+08:00" 的格式
        string formattedTime = dateTime.ToString("yyyy-MM-ddTHH:mm:sszzz");

        var log = new
        {
            time = formattedTime,
            level = "ERROR",
            app_id = "uma-tw",
            instance_id = serverName,
            game_name = "uma-tx-tw",
            app_name = "uma-tw",
            log_type = "aspnet",
            log = message
        };
        string logString = JsonSerializer.Serialize(log, new JsonSerializerOptions { Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping, WriteIndented = false });

        sw.WriteLine(logString);
    }

    private static string GetFileName(DateTime dateTime)
    {
        return $"http_error.{dateTime.Year:D4}{dateTime.Month:D2}{dateTime.Day:D2}.log";
    }
}
namespace RaceSimulateAPI.Logging;

public class ErrorLogInfo
{
    public DateTime DateTime { get; }
    public string ErrorMessage { get; }
    public string ErrorDetail { get; }

    public ErrorLogInfo(DateTime dateTime, string errorMessage, string errorDetail)
    {
        DateTime = dateTime;
        ErrorMessage = errorMessage;
        ErrorDetail = errorDetail;
    }

    public string FormatTsv()
    {
        return $"{DateTime.ToString("yyyy-MM-ddTHH:mm:ss")}\t{ErrorMessage}\t{ErrorDetail.ReplaceLineEndings(@"\n")}";
    }
}
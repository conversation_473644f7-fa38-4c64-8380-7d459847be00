namespace RaceSimulateAPI.Extensions;

public static class ServiceExtensions
{
    public static IWebHostBuilder UseUnixSocket(this IWebHostBuilder hostBuilder, string socketPath)
    {
        if (socketPath == "")
        {
            return hostBuilder;
        }

        // ファイルが存在したら削除する
        if (File.Exists(socketPath))
        {
            File.Delete(socketPath);
        }

        return hostBuilder.ConfigureKestrel(serverOptions => serverOptions.ListenUnixSocket(socketPath));
    }
}